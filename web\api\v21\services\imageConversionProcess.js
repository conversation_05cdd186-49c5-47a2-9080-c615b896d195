const { parentPort } = require('worker_threads');
const { fromBuffer } = require('pdf2pic');
const { PDFDocument } = require('pdf-lib');
const axios = require('axios');
const fs = require('fs');
const puppeteerService = require('./puppeteerService');


parentPort.on('message', async (message) => {
    console.log("======================Image Conversion process started==========================");
    const { pdfFileLink, fileName } = JSON.parse(message);
    console.log('pdfFileLink', pdfFileLink);
    console.log('fileName', fileName);
    const options = {
        density: 400,
        saveFilename: `${fileName}`,
        savePath: `./uploads/${fileName}`,
        format: "png",
        width: 1600,
        height: 900,
        quality: 100,
    };
    const folderPath = `./uploads/${fileName}`;
    if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath);
    }
    const response = await axios.get(pdfFileLink, { responseType: "arraybuffer" });
    const pdfBuffer = response.data;
    const pdfDoc = await PDFDocument.load(pdfBuffer);
    const totalNumberOfPages = pdfDoc.getPageCount();
    const converter = fromBuffer(pdfBuffer, options);
    try {

        const promises = [];
        for (let i = 1; i <= totalNumberOfPages; i++) {
            promises.push(converter(i, { responseType: "image" })
                .then((resolve) => {
                    console.log(`Page ${i} is now converted as image`);
                    return resolve;
                })
                .catch((error) => {
                    console.error(`Error converting page ${i}:`, error);
                    parentPort.postMessage({ success: false });
                    throw new Error(error);
                })
            );
        }
        Promise.all(promises)
            .then(() => {
                console.log("All pages converted!");
                const uploadPromises = [];
                for (let i = 1; i <= totalNumberOfPages; i++) {
                    const imagePath = `./uploads/${fileName}/${fileName}.${i}.png`;
                    uploadPromises.push(puppeteerService.saveImageToS3Bucket(imagePath, fileName));
                }
                return Promise.all(uploadPromises);
            })
            .then((results) => {
                const imageLinkArray = results.map(result => result.data[0].fileLink);
                console.log(imageLinkArray);
                fs.rm(folderPath, { recursive: true }, (err) => {
                    if (err) {
                        console.error('Error removing directory:', err);
                    } else {
                        console.log('Directory removed successfully.');
                        parentPort.postMessage({ success: true, result: imageLinkArray });
                    }
                });

            })
            .catch((error) => {
                console.error("An error occurred during conversion or uploading:", error)
                parentPort.postMessage({ success: false });
            });
    } catch (error) {
        console.error('Error converting PDF to images:', error);
        fs.rm(folderPath, { recursive: true }, (err) => {
            if (err) {
                console.error('Error removing directory:', err);
            } else {
                console.log('Directory removed successfully.');
            }
        });
        parentPort.postMessage({ success: false });
        //throw error;
    }
});