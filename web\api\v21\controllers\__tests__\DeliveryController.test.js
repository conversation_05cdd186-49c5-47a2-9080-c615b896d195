const DeliveryController = require('../DeliveryController');
const { deliveryService, projectService } = require('../../services');
const { Member, ProjectSettings } = require('../../models');
const exportService = require('../../services/exportService');

// Mock dependencies
jest.mock('../../services', () => ({
  deliveryService: {
    newRequest: jest.fn(),
    editRequest: jest.fn(),
    listNDR: jest.fn(),
    getNDRData: jest.fn(),
    getMemberData: jest.fn(),
    updateNDRStatus: jest.fn(),
    bulkdeliveryRequestUpload: jest.fn(),
    deleteQueuedNdr: jest.fn(),
    editMultipleDeliveryRequest: jest.fn(),
    lastDelivery: jest.fn(),
    getSingleDeliveryRequest: jest.fn(),
    upcomingRequestListForMobile: jest.fn(),
    upcomingRequestList: jest.fn(),
  },
  projectService: {
    getProjectDetails: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  Member: {},
  ProjectSettings: {
    getCalendarStatusColor: jest.fn(),
    getCalendarCard: jest.fn(),
  },
}));

jest.mock('../../services/exportService', () => ({
  sampleDeliveryRequestTemplate: jest.fn(),
}));

describe('DeliveryController', () => {
  let mockReq, mockRes, mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
      end: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('newRequest', () => {
    it('should create new delivery request successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      deliveryService.newRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.newRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delivery request creation', async () => {
      const mockError = new Error('Service error');
      deliveryService.newRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.newRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delivery request creation', async () => {
      const mockError = new Error('Exception error');
      deliveryService.newRequest.mockRejectedValue(mockError);

      await DeliveryController.newRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editRequest', () => {
    it('should edit delivery request successfully', async () => {
      const mockResponse = { id: 1, data: 'updated' };
      deliveryService.editRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.editRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delivery request edit', async () => {
      const mockError = new Error('Service error');
      deliveryService.editRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.editRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delivery request edit', async () => {
      const mockError = new Error('Exception error');
      deliveryService.editRequest.mockRejectedValue(mockError);

      await DeliveryController.editRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('listNDR', () => {
    it('should list NDR successfully with project data', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };
      const mockStatusData = { status: 'active' };
      const mockCardData = { card: 'data' };
      
      mockReq.params.ProjectId = '123';
      
      deliveryService.listNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });
      
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardData);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await DeliveryController.listNDR(mockReq, mockRes, mockNext);

      expect(deliveryService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('123');
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
        statusData: mockStatusData,
        cardData: mockCardData,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should list NDR successfully without project data', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };
      
      mockReq.params.ProjectId = '';
      
      deliveryService.listNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });
      
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await DeliveryController.listNDR(mockReq, mockRes, mockNext);

      expect(deliveryService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(ProjectSettings.getCalendarStatusColor).not.toHaveBeenCalled();
      expect(ProjectSettings.getCalendarCard).not.toHaveBeenCalled();
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
        statusData: undefined,
        cardData: undefined,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from NDR list', async () => {
      const mockError = new Error('Service error');
      deliveryService.listNDR.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.listNDR(mockReq, mockRes, mockNext);

      expect(deliveryService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from last delivery', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockError = new Error('Last delivery error');
      
      deliveryService.listNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });
      
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.listNDR(mockReq, mockRes, mockNext);

      expect(deliveryService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in NDR list', async () => {
      const mockError = new Error('Exception error');
      deliveryService.listNDR.mockRejectedValue(mockError);

      await DeliveryController.listNDR(mockReq, mockRes, mockNext);

      expect(deliveryService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getNDRData', () => {
    it('should get NDR data successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      deliveryService.getNDRData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.getNDRData(mockReq, mockRes, mockNext);

      expect(deliveryService.getNDRData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from NDR data', async () => {
      const mockError = new Error('Service error');
      deliveryService.getNDRData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.getNDRData(mockReq, mockRes, mockNext);

      expect(deliveryService.getNDRData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in NDR data', async () => {
      const mockError = new Error('Exception error');
      deliveryService.getNDRData.mockRejectedValue(mockError);

      await DeliveryController.getNDRData(mockReq, mockRes, mockNext);

      expect(deliveryService.getNDRData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getMemberData', () => {
    it('should get member data successfully', async () => {
      const mockResponse = { id: 1, data: 'member' };
      deliveryService.getMemberData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.getMemberData(mockReq, mockRes, mockNext);

      expect(deliveryService.getMemberData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Member listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from member data', async () => {
      const mockError = new Error('Service error');
      deliveryService.getMemberData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.getMemberData(mockReq, mockRes, mockNext);

      expect(deliveryService.getMemberData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in member data', async () => {
      const mockError = new Error('Exception error');
      deliveryService.getMemberData.mockRejectedValue(mockError);

      await DeliveryController.getMemberData(mockReq, mockRes, mockNext);

      expect(deliveryService.getMemberData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateNDRStatus', () => {
    it('should update NDR status successfully', async () => {
      const mockResponse = { id: 1, status: 'completed' };
      mockReq.body.status = 'completed';
      
      deliveryService.updateNDRStatus.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.updateNDRStatus(mockReq, mockRes, mockNext);

      expect(deliveryService.updateNDRStatus).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'completed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from NDR status update', async () => {
      const mockError = new Error('Service error');
      deliveryService.updateNDRStatus.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.updateNDRStatus(mockReq, mockRes, mockNext);

      expect(deliveryService.updateNDRStatus).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in NDR status update', async () => {
      const mockError = new Error('Exception error');
      deliveryService.updateNDRStatus.mockRejectedValue(mockError);

      await DeliveryController.updateNDRStatus(mockReq, mockRes, mockNext);

      expect(deliveryService.updateNDRStatus).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('sampleBulkDeliveryRequestTemplate', () => {
    it('should download sample bulk delivery request template successfully', async () => {
      const mockWorkbook = {
        xlsx: {
          write: jest.fn().mockResolvedValue(),
        },
      };
      const mockProjectDetail = { projectName: 'Test Project', id: 123 };
      
      mockReq.params.ProjectId = '123';
      mockReq.params.ParentCompanyId = '456';
      
      exportService.sampleDeliveryRequestTemplate.mockResolvedValue(mockWorkbook);
      projectService.getProjectDetails.mockResolvedValue(mockProjectDetail);

      await DeliveryController.sampleBulkDeliveryRequestTemplate(mockReq, mockRes, mockNext);

      expect(mockReq.data).toEqual({
        ProjectId: '123',
        ParentCompanyId: '456',
      });
      expect(exportService.sampleDeliveryRequestTemplate).toHaveBeenCalledWith(mockReq);
      expect(projectService.getProjectDetails).toHaveBeenCalledWith(mockReq);
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringContaining('Test Project_123_'),
      );
      expect(mockWorkbook.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle null workbook from export service', async () => {
      exportService.sampleDeliveryRequestTemplate.mockResolvedValue(null);

      await DeliveryController.sampleBulkDeliveryRequestTemplate(mockReq, mockRes, mockNext);

      expect(exportService.sampleDeliveryRequestTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'cannot export document', status: 422 });
      expect(mockRes.setHeader).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in sample template download', async () => {
      const mockError = new Error('Exception error');
      exportService.sampleDeliveryRequestTemplate.mockRejectedValue(mockError);

      await DeliveryController.sampleBulkDeliveryRequestTemplate(mockReq, mockRes, mockNext);

      expect(exportService.sampleDeliveryRequestTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('bulkUploadDeliveryRequest', () => {
    it('should upload bulk delivery request successfully', async () => {
      const mockResponse = { data: 'uploaded' };
      deliveryService.bulkdeliveryRequestUpload.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.bulkUploadDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.bulkdeliveryRequestUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Bookings uploaded successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from bulk upload', async () => {
      const mockError = new Error('Service error');
      deliveryService.bulkdeliveryRequestUpload.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.bulkUploadDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.bulkdeliveryRequestUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in bulk upload', async () => {
      const mockError = new Error('Exception error');
      deliveryService.bulkdeliveryRequestUpload.mockRejectedValue(mockError);

      await DeliveryController.bulkUploadDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.bulkdeliveryRequestUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('deleteQueuedNdr', () => {
    it('should delete queued NDR successfully', async () => {
      const mockResponse = { id: 1, deleted: true };
      deliveryService.deleteQueuedNdr.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeliveryController.deleteQueuedNdr(mockReq, mockRes, mockNext);

      expect(deliveryService.deleteQueuedNdr).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Queued Delivery Booking Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from queued NDR deletion', async () => {
      const mockError = new Error('Service error');
      deliveryService.deleteQueuedNdr.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeliveryController.deleteQueuedNdr(mockReq, mockRes, mockNext);

      expect(deliveryService.deleteQueuedNdr).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in queued NDR deletion', async () => {
      const mockError = new Error('Exception error');
      deliveryService.deleteQueuedNdr.mockRejectedValue(mockError);

      await DeliveryController.deleteQueuedNdr(mockReq, mockRes, mockNext);

      expect(deliveryService.deleteQueuedNdr).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editMultipleDeliveryRequest', () => {
    it('should edit multiple delivery request successfully', async () => {
      const mockResponse = { success: true, data: [{ id: 1, data: 'updated' }] };
      deliveryService.editMultipleDeliveryRequest.mockResolvedValue(mockResponse);

      await DeliveryController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editMultipleDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Updated Successfully.',
        data: mockResponse.data,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle unsuccessful edit multiple delivery request', async () => {
      const mockResponse = { success: false, message: 'Update failed' };
      deliveryService.editMultipleDeliveryRequest.mockResolvedValue(mockResponse);

      await DeliveryController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editMultipleDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({ message: mockResponse.message });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle server error in edit multiple delivery request', async () => {
      const mockResponse = { message: 'Server error' };
      deliveryService.editMultipleDeliveryRequest.mockResolvedValue(mockResponse);

      await DeliveryController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editMultipleDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: mockResponse.message });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in edit multiple delivery request', async () => {
      const mockError = new Error('Exception error');
      deliveryService.editMultipleDeliveryRequest.mockRejectedValue(mockError);

      await DeliveryController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryService.editMultipleDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
}); 