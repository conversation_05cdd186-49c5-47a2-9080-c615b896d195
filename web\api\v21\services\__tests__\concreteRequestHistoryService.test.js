const historyService = require('../concreteRequestHistoryService');
const helper = require('../../helpers/domainHelper');

// Mock all dependencies
jest.mock('../../models', () => ({
    Enterprise: {
        findOne: jest.fn(),
    },
    Sequelize: {
        Op: {
            ne: 'ne',
        },
    },
}));

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn(),
}));

describe('ConcreteRequestHistoryService', () => {
    let mockConcreteRequest;
    let mockConcreteRequestHistory;
    let mockUser;
    let mockMember;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup mock data
        mockConcreteRequest = {
            findOne: jest.fn(),
        };

        mockConcreteRequestHistory = {
            findAll: jest.fn(),
        };

        mockUser = {
            findOne: jest.fn(),
        };

        mockMember = {
            findOne: jest.fn(),
        };

        mockEnterprise = {
            findOne: jest.fn(),
        };

        // Setup default mock implementations
        helper.returnProjectModel.mockResolvedValue({
            Member: mockMember,
            User: mockUser,
        });

        helper.getDynamicModel.mockResolvedValue({
            ConcreteRequestHistory: mockConcreteRequestHistory,
            ConcreteRequest: mockConcreteRequest,
            User: mockUser,
        });
    });

    describe('getConcreteRequestHistories', () => {
        const mockInputData = {
            params: {
                ConcreteRequestId: 123,
                ProjectId: 456,
            },
            body: {
                ParentCompanyId: 1,
            },
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
        };

        const mockHistoryList = [
            {
                id: 1,
                ConcreteRequestId: 123,
                ProjectId: 456,
                Member: {
                    User: {
                        firstName: 'John',
                        lastName: 'Doe',
                        profilePic: 'profile.jpg',
                    },
                },
            },
        ];

        const mockExistRequest = {
            id: 123,
            ConcreteRequestId: 123,
            ProjectId: 456,
            isDeleted: false,
        };

        it('should successfully get concrete request histories', async () => {
            // Setup mocks for getDynamicModel
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });

            mockConcreteRequest.findOne.mockResolvedValue(mockExistRequest);
            mockConcreteRequestHistory.findAll.mockResolvedValue(mockHistoryList);
            mockUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });

            // Execute
            const done = jest.fn();
            await historyService.getConcreteRequestHistories(mockInputData, done);

            // Verify
            expect(mockConcreteRequest.findOne).toHaveBeenCalledWith({
                where: {
                    ConcreteRequestId: mockInputData.params.ConcreteRequestId,
                    ProjectId: +mockInputData.params.ProjectId,
                    isDeleted: false,
                },
            });

            expect(mockConcreteRequestHistory.findAll).toHaveBeenCalledWith({
                include: [
                    {
                        association: 'Member',
                        include: [
                            { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
                        ],
                    },
                ],
                where: {
                    ConcreteRequestId: mockExistRequest.id,
                    ProjectId: +mockInputData.params.ProjectId,
                },
                order: [['id', 'DESC']],
            });

            expect(done).toHaveBeenCalledWith({ historyList: mockHistoryList, exist: mockExistRequest }, false);
        });

        it('should return error when concrete request does not exist', async () => {
            // Setup mocks for getDynamicModel
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });
            mockUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });

            mockConcreteRequest.findOne.mockResolvedValue(null);

            // Execute
            const done = jest.fn();
            await historyService.getConcreteRequestHistories(mockInputData, done);

            // Verify
            expect(done).toHaveBeenCalledWith(null, { message: 'Concrete Booking id does not exist' });
        });

        it('should handle errors during execution', async () => {
            // Setup mocks to cause error in getDynamicModel
            const mockError = new Error('Database error');
            helper.returnProjectModel.mockRejectedValue(mockError);

            // Execute
            const done = jest.fn();
            await historyService.getConcreteRequestHistories(mockInputData, done);

            // Verify
            expect(done).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('getDomainFromEnterprise', () => {
        it('should return empty string when domainName is not provided', async () => {
            const result = await historyService.getDomainFromEnterprise(null);
            expect(result).toBe('');
        });

        it('should return domain name when enterprise exists', async () => {
            const mockDomain = 'testdomain';
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: mockDomain.toLowerCase() });

            const result = await historyService.getDomainFromEnterprise(mockDomain);
            expect(result).toBe(mockDomain);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: mockDomain.toLowerCase() },
            });
        });

        it('should return empty string when enterprise does not exist', async () => {
            const mockDomain = 'testdomain';
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);

            const result = await historyService.getDomainFromEnterprise(mockDomain);
            expect(result).toBe('');
        });
    });

    describe('getEnterpriseFromParentCompany', () => {
        it('should return null when ParentCompanyId is not provided', async () => {
            const result = await historyService.getEnterpriseFromParentCompany(null);
            expect(result).toBeNull();
        });

        it('should return null when ParentCompanyId is undefined', async () => {
            const result = await historyService.getEnterpriseFromParentCompany('undefined');
            expect(result).toBeNull();
        });

        it('should return enterprise when found', async () => {
            const mockEnterprise = { id: 1, status: 'completed' };
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await historyService.getEnterpriseFromParentCompany(1);
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' },
            });
        });
    });

    describe('getUserEnterprise', () => {
        const mockUser = {
            email: '<EMAIL>',
        };

        it('should return null when user email is not provided', async () => {
            const result = await historyService.getUserEnterprise({}, 1);
            expect(result).toBeNull();
        });

        it('should return null when user is not found', async () => {
            const mockUserObj = { email: '<EMAIL>' };

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockResolvedValue(null);
            const mockMemberFindOne = jest.fn();

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            const result = await historyService.getUserEnterprise(mockUserObj, 1);
            expect(result).toBeNull();
        });

        it('should return enterprise from parent company when member is not found', async () => {
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockEnterprise = { id: 1, status: 'completed' };
            const mockUserObj = { email: '<EMAIL>' };

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockResolvedValue(mockUserData);
            const mockMemberFindOne = jest.fn().mockResolvedValue(null);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await historyService.getUserEnterprise(mockUserObj, 1);
            expect(result).toEqual(mockEnterprise);
        });

        it('should return enterprise when member is account', async () => {
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockMemberData = {
                id: 1,
                UserId: 1,
                RoleId: 2,
                isDeleted: false,
                isAccount: true,
                EnterpriseId: 1
            };
            const mockEnterprise = { id: 1, status: 'completed' };
            const mockUserObj = { email: '<EMAIL>' };

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockResolvedValue(mockUserData);
            const mockMemberFindOne = jest.fn().mockResolvedValue(mockMemberData);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await historyService.getUserEnterprise(mockUserObj, 1);
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: mockMemberData.EnterpriseId, status: 'completed' },
            });
        });
    });

    describe('getDynamicModel', () => {
        const mockInputData = {
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
            body: {
                ParentCompanyId: 1,
            },
        };

        it('should successfully get dynamic model with domain name', async () => {
            const mockEnterprise = { name: 'testdomain' };
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);
            mockUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });

            const result = await historyService.getDynamicModel(mockInputData);

            expect(helper.returnProjectModel).toHaveBeenCalled();
            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
            expect(result).toBe(true);
        });

        it('should get dynamic model using parent company when domain is not available', async () => {
            const mockEnterprise = { name: 'testdomain' };
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockMemberData = { id: 1, UserId: 1, RoleId: 2, isDeleted: false, isAccount: true, EnterpriseId: 1 };
            const { Enterprise } = require('../../models');

            // Create fresh mock functions for getUserEnterprise
            const mockUserFindOne = jest.fn()
                .mockResolvedValueOnce(mockUserData) // For getUserEnterprise
                .mockResolvedValueOnce({ id: 1, email: '<EMAIL>' }); // For final user lookup
            const mockMemberFindOne = jest.fn().mockResolvedValue(mockMemberData);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            // First call returns null (no domain)
            Enterprise.findOne.mockResolvedValueOnce(null);
            // Second call returns enterprise
            Enterprise.findOne.mockResolvedValueOnce(mockEnterprise);

            const result = await historyService.getDynamicModel(mockInputData);

            expect(helper.returnProjectModel).toHaveBeenCalled();
            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
            expect(result).toBe(true);
        });

        it('should update user data when domain is available', async () => {
            const mockEnterprise = { name: 'testdomain' };
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const { Enterprise } = require('../../models');

            Enterprise.findOne.mockResolvedValue(mockEnterprise);
            mockUser.findOne.mockResolvedValue(mockUserData);

            const inputData = { ...mockInputData };
            await historyService.getDynamicModel(inputData);

            expect(inputData.user).toEqual(mockUserData);
        });
    });

    // Additional comprehensive test cases for 90% coverage
    describe('getConcreteRequestHistories - Additional Cases', () => {
        const mockInputData = {
            params: {
                ConcreteRequestId: 123,
                ProjectId: '456', // Test string ProjectId
            },
            body: {
                ParentCompanyId: 1,
            },
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
        };

        it('should handle string ProjectId correctly', async () => {
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });
            mockUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });

            const mockExistRequest = { id: 123, ConcreteRequestId: 123, ProjectId: 456, isDeleted: false };
            const mockHistoryList = [];

            mockConcreteRequest.findOne.mockResolvedValue(mockExistRequest);
            mockConcreteRequestHistory.findAll.mockResolvedValue(mockHistoryList);

            const done = jest.fn();
            await historyService.getConcreteRequestHistories(mockInputData, done);

            expect(mockConcreteRequest.findOne).toHaveBeenCalledWith({
                where: {
                    ConcreteRequestId: 123,
                    ProjectId: 456, // Should be converted to number
                    isDeleted: false,
                },
            });
            expect(done).toHaveBeenCalledWith({ historyList: mockHistoryList, exist: mockExistRequest }, false);
        });

        it('should handle error in ConcreteRequestHistory.findAll', async () => {
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });
            mockUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });

            const mockExistRequest = { id: 123, ConcreteRequestId: 123, ProjectId: 456, isDeleted: false };
            const mockError = new Error('History fetch error');

            mockConcreteRequest.findOne.mockResolvedValue(mockExistRequest);
            mockConcreteRequestHistory.findAll.mockRejectedValue(mockError);

            const done = jest.fn();
            await historyService.getConcreteRequestHistories(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle error in getDynamicModel', async () => {
            const mockError = new Error('getDynamicModel error');
            helper.returnProjectModel.mockRejectedValue(mockError);

            const done = jest.fn();
            await historyService.getConcreteRequestHistories(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('returnProjectModel', () => {
        it('should successfully call helper.returnProjectModel', async () => {
            const mockModelData = {
                Member: mockMember,
                User: mockUser,
            };
            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await historyService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle error in helper.returnProjectModel', async () => {
            const mockError = new Error('returnProjectModel error');
            helper.returnProjectModel.mockRejectedValue(mockError);

            await expect(historyService.returnProjectModel()).rejects.toThrow('returnProjectModel error');
        });
    });

    describe('getDomainFromEnterprise - Additional Cases', () => {
        it('should handle error in Enterprise.findOne', async () => {
            const { Enterprise } = require('../../models');
            const mockError = new Error('Database error');
            Enterprise.findOne.mockRejectedValue(mockError);

            await expect(historyService.getDomainFromEnterprise('testdomain')).rejects.toThrow('Database error');
        });

        it('should handle case-insensitive domain lookup', async () => {
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });

            const result = await historyService.getDomainFromEnterprise('TESTDOMAIN');

            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'testdomain' }
            });
            expect(result).toBe('TESTDOMAIN');
        });
    });

    describe('getEnterpriseFromParentCompany - Additional Cases', () => {
        it('should handle error in Enterprise.findOne', async () => {
            const { Enterprise } = require('../../models');
            const mockError = new Error('Database error');
            Enterprise.findOne.mockRejectedValue(mockError);

            await expect(historyService.getEnterpriseFromParentCompany(1)).rejects.toThrow('Database error');
        });

        it('should return null when enterprise is not found', async () => {
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);

            const result = await historyService.getEnterpriseFromParentCompany(1);
            expect(result).toBeNull();
        });
    });

    describe('getUserEnterprise - Additional Cases', () => {
        const mockUserObj = { email: '<EMAIL>' };

        it('should return enterprise from parent company when member is not account', async () => {
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockMemberData = {
                id: 1,
                UserId: 1,
                RoleId: 2,
                isDeleted: false,
                isAccount: false, // Not an account
                EnterpriseId: 1
            };
            const mockEnterprise = { id: 1, status: 'completed' };

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockResolvedValue(mockUserData);
            const mockMemberFindOne = jest.fn().mockResolvedValue(mockMemberData);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await historyService.getUserEnterprise(mockUserObj, 1);
            expect(result).toEqual(mockEnterprise);
        });

        it('should filter out members with RoleId = 4', async () => {
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockEnterprise = { id: 1, status: 'completed' };

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockResolvedValue(mockUserData);
            const mockMemberFindOne = jest.fn().mockResolvedValue(null);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await historyService.getUserEnterprise(mockUserObj, 1);

            expect(mockMemberFindOne).toHaveBeenCalledWith({
                where: { UserId: 1, RoleId: { ne: 4 }, isDeleted: false }
            });
            expect(result).toEqual(mockEnterprise);
        });

        it('should handle error in user lookup', async () => {
            const mockError = new Error('User lookup error');

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockRejectedValue(mockError);
            const mockMemberFindOne = jest.fn();

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            await expect(historyService.getUserEnterprise(mockUserObj, 1)).rejects.toThrow('User lookup error');
        });

        it('should handle error in member lookup', async () => {
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockError = new Error('Member lookup error');

            // Create fresh mock functions for this test
            const mockUserFindOne = jest.fn().mockResolvedValue(mockUserData);
            const mockMemberFindOne = jest.fn().mockRejectedValue(mockError);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            await expect(historyService.getUserEnterprise(mockUserObj, 1)).rejects.toThrow('Member lookup error');
        });
    });

    describe('getDynamicModel - Additional Cases', () => {
        it('should handle ParentCompanyId from params instead of body', async () => {
            const mockInputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: null, // No domain name
                },
                body: {},
                params: {
                    ParentCompanyId: 2, // ParentCompanyId in params
                },
            };

            const mockEnterprise = { name: 'testdomain' };
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockMemberData = { id: 1, UserId: 1, RoleId: 2, isDeleted: false, isAccount: true, EnterpriseId: 1 };
            const { Enterprise } = require('../../models');

            // Create fresh mock functions for getUserEnterprise
            const mockUserFindOne = jest.fn()
                .mockResolvedValueOnce(mockUserData) // For getUserEnterprise
                .mockResolvedValueOnce({ id: 1, email: '<EMAIL>' }); // For final user lookup
            const mockMemberFindOne = jest.fn().mockResolvedValue(mockMemberData);

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: mockMemberFindOne },
                User: { findOne: mockUserFindOne },
            });

            // No domain found
            Enterprise.findOne.mockResolvedValueOnce(null);
            // Enterprise found
            Enterprise.findOne.mockResolvedValueOnce(mockEnterprise);

            const result = await historyService.getDynamicModel(mockInputData);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
            expect(result).toBe(true);
        });

        it('should handle case when no domain and no enterprise found', async () => {
            const mockInputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: null,
                },
                body: {
                    ParentCompanyId: 1,
                },
            };

            const { Enterprise } = require('../../models');
            // No domain found
            Enterprise.findOne.mockResolvedValueOnce(null);
            // No enterprise found from parent company
            mockUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
            mockMember.findOne.mockResolvedValue(null);
            Enterprise.findOne.mockResolvedValueOnce(null);

            const result = await historyService.getDynamicModel(mockInputData);

            expect(helper.getDynamicModel).toHaveBeenCalledWith(undefined);
            expect(result).toBe(true);
        });

        it('should handle case when User.findOne returns null after domain is found', async () => {
            const mockInputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain',
                },
                body: {
                    ParentCompanyId: 1,
                },
            };

            const mockEnterprise = { name: 'testdomain' };
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);
            mockUser.findOne.mockResolvedValue(null); // User not found

            const result = await historyService.getDynamicModel(mockInputData);

            expect(result).toBe(true);
            // inputData.user should remain unchanged when User.findOne returns null
            expect(mockInputData.user.email).toBe('<EMAIL>');
        });

        it('should handle error in helper.getDynamicModel', async () => {
            const mockInputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain',
                },
                body: {
                    ParentCompanyId: 1,
                },
            };

            const mockEnterprise = { name: 'testdomain' };
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const mockError = new Error('getDynamicModel error');
            helper.getDynamicModel.mockRejectedValue(mockError);

            await expect(historyService.getDynamicModel(mockInputData)).rejects.toThrow('getDynamicModel error');
        });
    });
});