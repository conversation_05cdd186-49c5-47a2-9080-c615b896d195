const equipmentService = require('../equipmentService');
const helper = require('../../helpers/domainHelper');
const ApiError = require('../../helpers/apiError');
const status = require('http-status');

// Mock all dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      in: Symbol('in'),
      ne: Symbol('ne'),
      and: Symbol('and'),
      or: Symbol('or'),
      iLike: Symbol('iLike'),
      gte: Symbol('gte'),
    },
    and: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  DeliverEquipment: {
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    createInstance: jest.fn(),
  },
  CraneRequestEquipment: {
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    createInstance: jest.fn(),
  },
  DeliveryRequest: {
    findAll: jest.fn(),
  },
  CraneRequest: {
    findAll: jest.fn(),
  },
  DeliverHistory: {
    findAll: jest.fn(),
    createInstance: jest.fn(),
  },
  CraneRequestHistory: {
    findAll: jest.fn(),
    createInstance: jest.fn(),
  },
  Equipments: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    getEquipment: jest.fn(),
    getAll: jest.fn(),
    createInstance: jest.fn(),
    createEquipment: jest.fn(),
    updateInstance: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
    getProject: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
    getBy: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
  },
  PresetEquipmentType: {
    findAll: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

jest.mock('../../helpers/apiError');

describe('EquipmentService', () => {
  let mockInputData;
  let mockDone;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get mocked models
    mockModels = require('../../models');

    mockInputData = {
      user: {
        id: 1,
        email: '<EMAIL>',
        domainName: 'testdomain',
      },
      body: {
        ProjectId: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
        controlledBy: 1,
        ParentCompanyId: 1,
      },
      params: {
        ProjectId: 1,
        pageNo: '1',
        pageSize: '10',
        ParentCompanyId: 1,
      },
    };

    mockDone = jest.fn();

    // Setup default mock implementations
    helper.getDynamicModel.mockResolvedValue({
      Equipments: mockModels.Equipments,
      Project: mockModels.Project,
      Member: mockModels.Member,
      User: mockModels.User,
    });

    helper.returnProjectModel.mockResolvedValue(true);

    mockModels.Enterprise.findOne.mockResolvedValue({
      name: 'testdomain',
      id: 1,
    });

    mockModels.Member.getBy.mockResolvedValue({
      id: 1,
      UserId: 1,
      ProjectId: 1,
    });

    mockModels.Project.getProject.mockResolvedValue({
      id: 1,
      projectName: 'Test Project',
    });

    mockModels.Equipments.getEquipment.mockResolvedValue(null);
    mockModels.Equipments.findOne.mockResolvedValue(null);
    mockModels.Equipments.createInstance.mockResolvedValue({
      id: 1,
      equipmentName: 'Test Equipment',
    });
    mockModels.Equipments.createEquipment.mockResolvedValue({
      id: 1,
      equipmentName: 'Test Equipment',
    });
  });

  describe('addEquipment', () => {
    it('should successfully add equipment', async () => {
      const mockEquipment = {
        id: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
      };

      mockModels.Equipments.createEquipment.mockResolvedValue(mockEquipment);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(helper.getDynamicModel).toHaveBeenCalled();
      expect(mockModels.Member.getBy).toHaveBeenCalledWith({
        UserId: mockInputData.user.id,
        ProjectId: mockInputData.body.ProjectId,
      });
      expect(mockModels.Project.getProject).toHaveBeenCalledWith({
        id: mockInputData.body.ProjectId,
      });
      expect(mockModels.Member.getBy).toHaveBeenCalledWith({
        id: mockInputData.body.controlledBy,
        ProjectId: mockInputData.body.ProjectId,
      });
      expect(mockDone).toHaveBeenCalledWith(mockEquipment, false);
    });

    it('should return error when project does not exist', async () => {
      mockModels.Project.getProject.mockResolvedValue(null);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should return error when controlled member not in project', async () => {
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // For createdBy
        .mockResolvedValueOnce(null); // For controlledBy

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should return error when equipment name already exists', async () => {
      mockModels.Equipments.getEquipment.mockResolvedValue({
        id: 1,
        equipmentName: 'Test Equipment',
      });

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.getEquipment).toHaveBeenCalledWith({
        equipmentName: mockInputData.body.equipmentName,
        ProjectId: mockInputData.body.ProjectId,
        isDeleted: false,
      });
    });

    it('should handle errors during equipment creation', async () => {
      const error = new Error('Database error');
      mockModels.Equipments.createInstance.mockRejectedValue(error);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should generate auto ID when no previous equipment exists', async () => {
      mockModels.Equipments.findOne.mockResolvedValue(null);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalledWith({
        where: { ProjectId: mockInputData.body.ProjectId, isDeleted: false },
        order: [['equipmentAutoId', 'DESC']],
      });
    });

    it('should increment auto ID based on last equipment', async () => {
      mockModels.Equipments.findOne.mockResolvedValue({
        equipmentAutoId: 5,
      });

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalled();
    });

    it('should call createEquipment instead of createInstance', async () => {
      const mockEquipment = {
        id: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
      };

      mockModels.Equipments.createEquipment = jest.fn().mockResolvedValue(mockEquipment);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: mockInputData.body.ProjectId,
          equipmentName: mockInputData.body.equipmentName,
          equipmentType: mockInputData.body.equipmentType,
          controlledBy: mockInputData.body.controlledBy,
          equipmentAutoId: 1,
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipment, false);
    });

    it('should handle member not found for createdBy', async () => {
      mockModels.Member.getBy.mockResolvedValueOnce(null);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle JSON parsing of lastIdValue', async () => {
      const mockLastEquipment = { equipmentAutoId: 10 };
      const mockEquipment = {
        id: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
      };

      mockModels.Equipments.findOne.mockResolvedValue(mockLastEquipment);
      mockModels.Equipments.createEquipment.mockResolvedValue(mockEquipment);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentAutoId: 11,
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipment, false);
    });

    it('should handle equipment creation with proper auto ID assignment', async () => {
      const mockEquipment = {
        id: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
      };

      // Mock the sequence: getBy for createdBy, getProject, getBy for controlledBy, getEquipment, findOne for lastId
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue({ id: 1 });
      mockModels.Equipments.getEquipment.mockResolvedValue(null);
      mockModels.Equipments.findOne.mockResolvedValue(null); // No previous equipment
      mockModels.Equipments.createEquipment.mockResolvedValue(mockEquipment);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: mockInputData.body.ProjectId,
          equipmentName: mockInputData.body.equipmentName,
          equipmentType: mockInputData.body.equipmentType,
          controlledBy: mockInputData.body.controlledBy,
          createdBy: 1,
          equipmentAutoId: 1,
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipment, false);
    });

    it('should handle createEquipment returning null', async () => {
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue({ id: 1 });
      mockModels.Equipments.getEquipment.mockResolvedValue(null);
      mockModels.Equipments.findOne.mockResolvedValue(null);
      mockModels.Equipments.createEquipment.mockResolvedValue(null);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, false);
    });

    it('should handle equipment with very high auto ID', async () => {
      const mockLastEquipment = { equipmentAutoId: 999999 };
      const mockEquipment = {
        id: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
      };

      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue({ id: 1 });
      mockModels.Equipments.getEquipment.mockResolvedValue(null);
      mockModels.Equipments.findOne.mockResolvedValue(mockLastEquipment);
      mockModels.Equipments.createEquipment.mockResolvedValue(mockEquipment);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentAutoId: 1000000,
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipment, false);
    });

    it('should handle equipment with zero auto ID', async () => {
      const mockLastEquipment = { equipmentAutoId: 0 };
      const mockEquipment = {
        id: 1,
        equipmentName: 'Test Equipment',
        equipmentType: 'Crane',
      };

      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue({ id: 1 });
      mockModels.Equipments.getEquipment.mockResolvedValue(null);
      mockModels.Equipments.findOne.mockResolvedValue(mockLastEquipment);
      mockModels.Equipments.createEquipment.mockResolvedValue(mockEquipment);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentAutoId: 1,
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipment, false);
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully with domain name', async () => {
      await equipmentService.getDynamicModel(mockInputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: mockInputData.user.domainName.toLowerCase() },
      });
      expect(helper.getDynamicModel).toHaveBeenCalledWith(mockInputData.user.domainName);
    });

    it('should handle missing domain name with ParentCompanyId', async () => {
      const inputDataWithoutDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
      };

      mockModels.Enterprise.findOne.mockResolvedValue(null);

      await equipmentService.getDynamicModel(inputDataWithoutDomain);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should update user when domain enterprise value exists', async () => {
      const newUser = { id: 2, email: '<EMAIL>' };
      mockModels.User.findOne.mockResolvedValue(newUser);

      await equipmentService.getDynamicModel(mockInputData);

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: mockInputData.user.email },
      });
    });

    it('should handle enterprise not found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      await equipmentService.getDynamicModel(mockInputData);

      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should handle undefined ParentCompanyId', async () => {
      const inputDataWithUndefinedParent = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
        body: {
          ...mockInputData.body,
          ParentCompanyId: 'undefined',
        },
      };

      await equipmentService.getDynamicModel(inputDataWithUndefinedParent);

      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should use params ParentCompanyId when body ParentCompanyId is not available', async () => {
      const inputDataWithParamsParent = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
        body: {},
        params: {
          ...mockInputData.params,
          ParentCompanyId: 2,
        },
      };

      await equipmentService.getDynamicModel(inputDataWithParamsParent);

      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should return domain name when found', async () => {
      const result = await equipmentService.getDynamicModel(mockInputData);

      expect(result).toBe(mockInputData.user.domainName);
    });

    it('should handle getUserEnterprise when domainName is null and ParentCompanyId exists', async () => {
      const inputDataWithoutDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
        body: {
          ...mockInputData.body,
          ParentCompanyId: 1,
        },
      };

      // Mock getUserEnterprise to return a domain
      equipmentService.getUserEnterprise = jest.fn().mockResolvedValue('testenterprise');

      await equipmentService.getDynamicModel(inputDataWithoutDomain);

      expect(equipmentService.getUserEnterprise).toHaveBeenCalledWith(
        inputDataWithoutDomain.user.email,
        1
      );
    });

    it('should handle when domainNameResult is null', async () => {
      const inputDataWithoutDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: 'nonexistent',
        },
      };

      // Mock getDomainEnterprise to return null
      equipmentService.getDomainEnterprise = jest.fn().mockResolvedValue(null);

      await equipmentService.getDynamicModel(inputDataWithoutDomain);

      expect(helper.getDynamicModel).toHaveBeenCalledWith(null);
    });

    it('should not update user when domainNameResult is falsy', async () => {
      const inputDataWithoutDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
        body: {
          ParentCompanyId: 'undefined',
        },
      };

      equipmentService.getDomainEnterprise = jest.fn().mockResolvedValue(null);
      equipmentService.getUserEnterprise = jest.fn().mockResolvedValue(null);

      await equipmentService.getDynamicModel(inputDataWithoutDomain);

      expect(mockModels.User.findOne).not.toHaveBeenCalled();
    });
  });

  describe('listEquipment', () => {
    it('should list equipment successfully', async () => {
      const mockEquipmentList = [
        { id: 1, equipmentName: 'Equipment 1' },
        { id: 2, equipmentName: 'Equipment 2' },
      ];

      mockModels.Equipments.getAll.mockResolvedValue(mockEquipmentList);

      await equipmentService.listEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentList, false);
    });

    it('should handle errors during equipment listing', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.listEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle basic filtering with rows array', async () => {
      const inputDataWithFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          isFilter: true,
        },
      };

      const mockEquipmentData = {
        rows: [
          { id: 1, equipmentName: 'Zebra Equipment' },
          { id: 2, equipmentName: 'Alpha Equipment' },
        ],
      };

      mockModels.Equipments.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentService.listEquipment(inputDataWithFilter, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should handle basic filtering with direct array', async () => {
      const inputDataWithFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          isFilter: true,
        },
      };

      const mockEquipmentData = [
        { id: 1, equipmentName: 'Zebra Equipment' },
        { id: 2, equipmentName: 'Alpha Equipment' },
      ];

      mockModels.Equipments.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentService.listEquipment(inputDataWithFilter, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should handle showActivatedAlone filter', async () => {
      const inputDataWithActiveFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          showActivatedAlone: true,
        },
      };

      await equipmentService.listEquipment(inputDataWithActiveFilter, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          isActive: true,
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle nameFilter', async () => {
      const inputDataWithNameFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          nameFilter: 'Crane',
        },
      };

      await equipmentService.listEquipment(inputDataWithNameFilter, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentName: {
            [mockModels.Sequelize.Op.iLike]: '%Crane%',
          },
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle companyNameFilter', async () => {
      const inputDataWithCompanyFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          companyNameFilter: 'Test Company',
        },
      };

      await equipmentService.listEquipment(inputDataWithCompanyFilter, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          '$controllUserDetails.Company.companyName$': {
            [mockModels.Sequelize.Op.iLike]: '%Test Company%',
          },
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle idFilter', async () => {
      const inputDataWithIdFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          idFilter: 123,
        },
      };

      await equipmentService.listEquipment(inputDataWithIdFilter, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentAutoId: 123,
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle memberFilter', async () => {
      const inputDataWithMemberFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          memberFilter: 456,
        },
      };

      await equipmentService.listEquipment(inputDataWithMemberFilter, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          '$controllUserDetails.id$': 456,
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle typeFilter', async () => {
      const inputDataWithTypeFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          typeFilter: 'Crane',
        },
      };

      await equipmentService.listEquipment(inputDataWithTypeFilter, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          '$PresetEquipmentType.equipmentType$': 'Crane',
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle search with numeric value', async () => {
      const inputDataWithSearch = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          search: '123',
        },
      };

      await equipmentService.listEquipment(inputDataWithSearch, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Number),
        expect.any(Number),
        expect.objectContaining({
          [mockModels.Sequelize.Op.and]: expect.any(Array),
        }),
        undefined,
        undefined
      );
    });

    it('should handle search with text value', async () => {
      const inputDataWithSearch = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          search: 'crane',
        },
      };

      await equipmentService.listEquipment(inputDataWithSearch, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Number),
        expect.any(Number),
        expect.objectContaining({
          [mockModels.Sequelize.Op.and]: expect.any(Array),
        }),
        undefined,
        undefined
      );
    });

    it('should handle sort and sortByField parameters', async () => {
      const inputDataWithSort = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          sort: 'ASC',
          sortByField: 'equipmentName',
        },
      };

      await equipmentService.listEquipment(inputDataWithSort, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        'ASC',
        'equipmentName'
      );
    });

    it('should handle pagination parameters', async () => {
      const inputDataWithPagination = {
        ...mockInputData,
        params: {
          ...mockInputData.params,
          pageNo: '2',
          pageSize: '20',
        },
      };

      await equipmentService.listEquipment(inputDataWithPagination, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        20, // pageSize
        20, // offset (pageNo - 1) * pageSize = (2 - 1) * 20
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle empty filter results with rows', async () => {
      const inputDataWithFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          isFilter: true,
        },
      };

      const mockEquipmentData = {
        rows: [],
      };

      mockModels.Equipments.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentService.listEquipment(inputDataWithFilter, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should handle empty filter results with direct array', async () => {
      const inputDataWithFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          isFilter: true,
        },
      };

      const mockEquipmentData = [];

      mockModels.Equipments.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentService.listEquipment(inputDataWithFilter, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });
  });

  describe('lastEquipment', () => {
    it('should return next equipment ID when equipment exists', async () => {
      const mockLastEquipment = { equipmentAutoId: 5 };
      mockModels.Equipments.findOne.mockResolvedValue(mockLastEquipment);

      await equipmentService.lastEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalledWith({
        where: { ProjectId: mockInputData.params.ProjectId, isDeleted: false },
        order: [['equipmentAutoId', 'DESC']],
      });
      expect(mockDone).toHaveBeenCalledWith({ id: 6 }, false);
    });

    it('should return 1 when no equipment exists', async () => {
      mockModels.Equipments.findOne.mockResolvedValue(null);

      await equipmentService.lastEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
    });

    it('should handle errors during lastEquipment', async () => {
      const error = new Error('Database error');
      mockModels.Equipments.findOne.mockRejectedValue(error);

      await equipmentService.lastEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('craneListEquipment', () => {
    it('should list crane equipment successfully with rows', async () => {
      const mockCraneEquipment = {
        rows: [
          { id: 1, equipmentName: 'Crane B', PresetEquipmentType: { isCraneType: true } },
          { id: 2, equipmentName: 'Crane A', PresetEquipmentType: { isCraneType: true } },
        ],
        count: 2,
      };

      mockModels.Equipments.findAndCountAll.mockResolvedValue(mockCraneEquipment);

      await equipmentService.craneListEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findAndCountAll).toHaveBeenCalledWith({
        where: {
          ProjectId: mockInputData.params.ProjectId,
          isDeleted: false,
        },
        include: [
          {
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      expect(mockDone).toHaveBeenCalledWith(mockCraneEquipment, false);
    });

    it('should list crane equipment successfully with direct array', async () => {
      const mockCraneEquipment = [
        { id: 1, equipmentName: 'Crane B', PresetEquipmentType: { isCraneType: true } },
        { id: 2, equipmentName: 'Crane A', PresetEquipmentType: { isCraneType: true } },
      ];

      mockModels.Equipments.findAndCountAll.mockResolvedValue(mockCraneEquipment);

      await equipmentService.craneListEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockCraneEquipment, false);
    });

    it('should handle showActivatedAlone filter in crane list', async () => {
      const inputDataWithActiveFilter = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          showActivatedAlone: true,
        },
      };

      const mockCraneEquipment = {
        rows: [
          { id: 1, equipmentName: 'Crane A', PresetEquipmentType: { isCraneType: true } },
        ],
        count: 1,
      };

      mockModels.Equipments.findAndCountAll.mockResolvedValue(mockCraneEquipment);

      await equipmentService.craneListEquipment(inputDataWithActiveFilter, mockDone);

      expect(mockModels.Equipments.findAndCountAll).toHaveBeenCalledWith({
        where: {
          ProjectId: mockInputData.params.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      expect(mockDone).toHaveBeenCalledWith(mockCraneEquipment, false);
    });

    it('should handle empty crane equipment list with rows', async () => {
      const mockCraneEquipment = {
        rows: [],
        count: 0,
      };

      mockModels.Equipments.findAndCountAll.mockResolvedValue(mockCraneEquipment);

      await equipmentService.craneListEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockCraneEquipment, false);
    });

    it('should handle empty crane equipment list with direct array', async () => {
      const mockCraneEquipment = [];

      mockModels.Equipments.findAndCountAll.mockResolvedValue(mockCraneEquipment);

      await equipmentService.craneListEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockCraneEquipment, false);
    });

    it('should handle errors during crane equipment listing', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.craneListEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('getAllEquipmentType', () => {
    it('should get all equipment types successfully', async () => {
      const mockEquipmentTypes = [
        {
          id: 1,
          equipmentName: 'Equipment 1',
          PresetEquipmentType: { id: 1, equipmentType: 'Crane', isCraneType: true }
        },
        {
          id: 2,
          equipmentName: 'Equipment 2',
          PresetEquipmentType: { id: 2, equipmentType: 'Truck', isCraneType: false }
        },
      ];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipmentTypes);

      await equipmentService.getAllEquipmentType(mockInputData, mockDone);

      expect(mockModels.Equipments.findAll).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentTypes, false);
    });

    it('should handle errors during getAllEquipmentType', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.getAllEquipmentType(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('updateEquipment', () => {
    beforeEach(() => {
      mockInputData.body = {
        ...mockInputData.body,
        id: 1,
        equipmentName: 'Updated Equipment',
      };
    });

    it('should update equipment successfully', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };
      const mockUpdatedEquipment = { id: 1, equipmentName: 'Updated Equipment' };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockExistingEquipment) // For equipment existence check
        .mockResolvedValueOnce(null); // For name uniqueness check

      mockModels.Project.getProject.mockResolvedValue({ id: 1, projectName: 'Test Project' });
      mockModels.Equipments.updateInstance.mockResolvedValue(mockUpdatedEquipment);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalledWith({
        where: expect.objectContaining({
          id: mockInputData.body.id,
          ProjectId: mockInputData.body.ProjectId,
        }),
      });
      expect(mockModels.Project.getProject).toHaveBeenCalledWith({ id: mockInputData.body.ProjectId });
      expect(mockModels.Equipments.updateInstance).toHaveBeenCalledWith(
        mockInputData.body.id,
        expect.objectContaining({
          equipmentName: 'Updated Equipment',
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockUpdatedEquipment, false);
    });

    it('should return error when equipment does not exist', async () => {
      mockModels.Equipments.findOne.mockResolvedValue(null);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'Equipment id does not exist.' });
    });

    it('should return error when project does not exist', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };
      mockModels.Equipments.findOne.mockResolvedValue(mockExistingEquipment);
      mockModels.Project.getProject.mockResolvedValue(null);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project does not exist.' });
    });

    it('should return error when equipment name already exists for different equipment', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };
      const mockNameConflict = { id: 2, equipmentName: 'Updated Equipment' };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockExistingEquipment) // For equipment existence check
        .mockResolvedValueOnce(mockNameConflict); // For name uniqueness check

      mockModels.Project.getProject.mockResolvedValue({ id: 1, projectName: 'Test Project' });

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should allow updating equipment with same name', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Updated Equipment' };
      const mockNameCheck = { id: 1, equipmentName: 'Updated Equipment' };
      const mockUpdatedEquipment = { id: 1, equipmentName: 'Updated Equipment' };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockExistingEquipment) // For equipment existence check
        .mockResolvedValueOnce(mockNameCheck); // For name uniqueness check

      mockModels.Project.getProject.mockResolvedValue({ id: 1, projectName: 'Test Project' });
      mockModels.Equipments.updateInstance.mockResolvedValue(mockUpdatedEquipment);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.updateInstance).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(mockUpdatedEquipment, false);
    });

    it('should handle errors during equipment update', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should properly delete ProjectId from input data before update', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };
      const mockUpdatedEquipment = { id: 1, equipmentName: 'Updated Equipment' };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockExistingEquipment) // For equipment existence check
        .mockResolvedValueOnce(null); // For name uniqueness check

      mockModels.Project.getProject.mockResolvedValue({ id: 1, projectName: 'Test Project' });
      mockModels.Equipments.updateInstance.mockResolvedValue(mockUpdatedEquipment);

      const inputDataCopy = JSON.parse(JSON.stringify(mockInputData));
      await equipmentService.updateEquipment(inputDataCopy, mockDone);

      expect(mockModels.Equipments.updateInstance).toHaveBeenCalledWith(
        inputDataCopy.body.id,
        expect.not.objectContaining({
          ProjectId: expect.anything(),
        })
      );
      expect(mockDone).toHaveBeenCalledWith(mockUpdatedEquipment, false);
    });

    it('should handle updateInstance returning null', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockExistingEquipment) // For equipment existence check
        .mockResolvedValueOnce(null); // For name uniqueness check

      mockModels.Project.getProject.mockResolvedValue({ id: 1, projectName: 'Test Project' });
      mockModels.Equipments.updateInstance.mockResolvedValue(null);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, false);
    });

    it('should handle Sequelize.and condition properly', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };

      mockModels.Equipments.findOne.mockResolvedValueOnce(mockExistingEquipment);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalledWith({
        where: mockModels.Sequelize.and(
          {
            id: mockInputData.body.id,
          },
          { ProjectId: mockInputData.body.ProjectId }
        ),
      });
    });

    it('should handle name existence check with Sequelize.and', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockExistingEquipment) // For equipment existence check
        .mockResolvedValueOnce(null); // For name uniqueness check

      mockModels.Project.getProject.mockResolvedValue({ id: 1, projectName: 'Test Project' });

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenNthCalledWith(2, {
        where: mockModels.Sequelize.and(
          {
            equipmentName: mockInputData.body.equipmentName,
          },
          { ProjectId: mockInputData.body.ProjectId }
        ),
      });
    });
  });

  describe('deleteEquipment', () => {
    beforeEach(() => {
      mockInputData.body = {
        ...mockInputData.body,
        id: [1, 2],
        isSelectAll: false,
      };
    });

    it('should delete specific equipment successfully', async () => {
      const mockEquipments = [
        { id: 1, equipmentName: 'Equipment 1' },
      ];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);
      mockModels.DeliverEquipment.findOne.mockResolvedValue(null);
      mockModels.CraneRequestEquipment.findOne.mockResolvedValue(null);
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findAll).toHaveBeenCalledWith({
        where: {
          ProjectId: mockInputData.body.ProjectId,
          isDeleted: false,
          id: { [mockModels.Sequelize.Op.in]: mockInputData.body.id },
        },
      });
      expect(mockDone).toHaveBeenCalledWith('success', false);
    });

    it('should delete all equipment when isSelectAll is true', async () => {
      const inputDataSelectAll = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          isSelectAll: true,
        },
      };

      const mockEquipments = [
        { id: 1, equipmentName: 'Equipment 1' },
        { id: 2, equipmentName: 'Equipment 2' },
      ];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);
      mockModels.DeliverEquipment.findOne.mockResolvedValue(null);
      mockModels.CraneRequestEquipment.findOne.mockResolvedValue(null);
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deleteEquipment(inputDataSelectAll, mockDone);

      expect(mockModels.Equipments.findAll).toHaveBeenCalledWith({
        where: { ProjectId: mockInputData.body.ProjectId, isDeleted: false },
      });
    });

    it('should prevent deletion when equipment is mapped to delivery request', async () => {
      const mockEquipments = [{ id: 1, equipmentName: 'Equipment 1' }];
      const mockDeliveryMapping = { id: 1, EquipmentId: 1 };

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);
      mockModels.DeliverEquipment.findOne.mockResolvedValue(mockDeliveryMapping);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, {
        message: 'Equipment 1 cannot be deleted. Equipment 1 is mapped to submitted requests',
      });
    });

    it('should prevent deletion when equipment is mapped to crane request', async () => {
      const mockEquipments = [{ id: 1, equipmentName: 'Equipment 1' }];
      const mockCraneMapping = { id: 1, EquipmentId: 1 };

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);
      mockModels.DeliverEquipment.findOne.mockResolvedValue(null);
      mockModels.CraneRequestEquipment.findOne.mockResolvedValue(mockCraneMapping);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, {
        message: 'Equipment 1 cannot be deleted. Equipment 1 is mapped to submitted requests',
      });
    });

    it('should handle errors during equipment deletion', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle successful deletion of last equipment in list', async () => {
      const mockEquipments = [{ id: 1, equipmentName: 'Equipment 1' }];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);
      mockModels.DeliverEquipment.findOne.mockResolvedValue(null);
      mockModels.CraneRequestEquipment.findOne.mockResolvedValue(null);
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.update).toHaveBeenCalledWith(
        { isDeleted: true },
        {
          where: { id: 1, ProjectId: mockInputData.body.ProjectId, isDeleted: false },
        }
      );
      expect(mockDone).toHaveBeenCalledWith('success', false);
    });

    it('should handle multiple equipment deletion with mixed results', async () => {
      const mockEquipments = [
        { id: 1, equipmentName: 'Equipment 1' },
        { id: 2, equipmentName: 'Equipment 2' },
      ];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);
      mockModels.DeliverEquipment.findOne
        .mockResolvedValueOnce(null) // First equipment not mapped
        .mockResolvedValueOnce(null); // Second equipment not mapped
      mockModels.CraneRequestEquipment.findOne
        .mockResolvedValueOnce(null) // First equipment not mapped
        .mockResolvedValueOnce(null); // Second equipment not mapped
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.update).toHaveBeenCalledTimes(2);
      expect(mockDone).toHaveBeenCalledWith('success', false);
    });
  });

  describe('equipmentsForBulkUploadDeliveryRequest', () => {
    it('should return equipments for bulk upload', async () => {
      const mockEquipments = [
        { id: 1, equipmentName: 'Equipment 1' },
        { id: 2, equipmentName: 'Equipment 2' },
      ];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);

      const result = await equipmentService.equipmentsForBulkUploadDeliveryRequest(mockInputData);

      expect(helper.getDynamicModel).toHaveBeenCalled();
      expect(mockModels.Equipments.findAll).toHaveBeenCalledWith({
        where: {
          ProjectId: +mockInputData.params.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        attributes: ['id', 'equipmentName'],
      });
      expect(result).toEqual(mockEquipments);
    });

    it('should handle errors during bulk upload equipment fetch', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      const result = await equipmentService.equipmentsForBulkUploadDeliveryRequest(mockInputData);

      expect(result).toBeUndefined();
    });

    it('should handle successful fetch with empty results', async () => {
      const mockEquipments = [];

      mockModels.Equipments.findAll.mockResolvedValue(mockEquipments);

      const result = await equipmentService.equipmentsForBulkUploadDeliveryRequest(mockInputData);

      expect(result).toEqual(mockEquipments);
    });

    it('should handle null input data', async () => {
      const result = await equipmentService.equipmentsForBulkUploadDeliveryRequest(null);

      expect(result).toBeUndefined();
    });
  });

  describe('getPresetEquipmentTypeList', () => {
    it('should get preset equipment types successfully', async () => {
      const mockEquipmentTypes = [
        { id: 3, equipmentType: 'Excavator' },
        { id: 1, equipmentType: 'Crane' },
        { id: 2, equipmentType: 'Truck' },
      ];

      mockModels.PresetEquipmentType.findAll.mockResolvedValue(mockEquipmentTypes);

      await equipmentService.getPresetEquipmentTypeList(mockInputData, mockDone);

      expect(helper.getDynamicModel).toHaveBeenCalled();
      expect(mockModels.PresetEquipmentType.findAll).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          isActive: true,
        },
        attributes: ['id', 'equipmentType'],
      });
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentTypes, false);
    });

    it('should handle errors during preset equipment type fetch', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.getPresetEquipmentTypeList(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should sort equipment types alphabetically', async () => {
      const mockEquipmentTypes = [
        { id: 3, equipmentType: 'Zebra' },
        { id: 1, equipmentType: 'Alpha' },
        { id: 2, equipmentType: 'Beta' },
      ];

      mockModels.PresetEquipmentType.findAll.mockResolvedValue(mockEquipmentTypes);

      await equipmentService.getPresetEquipmentTypeList(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentTypes, false);
    });

    it('should handle empty equipment types list', async () => {
      const mockEquipmentTypes = [];

      mockModels.PresetEquipmentType.findAll.mockResolvedValue(mockEquipmentTypes);

      await equipmentService.getPresetEquipmentTypeList(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentTypes, false);
    });
  });

  describe('getDomainEnterprise', () => {
    it('should return domain name when enterprise exists', async () => {
      const domainName = 'testdomain';
      const mockEnterprise = { id: 1, name: 'testdomain' };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await equipmentService.getDomainEnterprise(domainName);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: domainName.toLowerCase() },
      });
      expect(result).toBe(domainName);
    });

    it('should return empty string when enterprise does not exist', async () => {
      const domainName = 'nonexistent';

      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await equipmentService.getDomainEnterprise(domainName);

      expect(result).toBe('');
    });

    it('should return null when domain name is not provided', async () => {
      const result = await equipmentService.getDomainEnterprise(null);

      expect(result).toBeNull();
      expect(mockModels.Enterprise.findOne).not.toHaveBeenCalled();
    });
  });

  describe('getUserEnterprise', () => {
    beforeEach(() => {
      // Setup publicUser and publicMember mocks
      const mockPublicUser = { findOne: jest.fn() };
      const mockPublicMember = { findOne: jest.fn() };

      helper.returnProjectModel.mockResolvedValue({
        User: mockPublicUser,
        Member: mockPublicMember,
      });
    });

    it('should return null when email or ParentCompanyId is missing', async () => {
      const result1 = await equipmentService.getUserEnterprise(null, 1);
      const result2 = await equipmentService.getUserEnterprise('<EMAIL>', null);

      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });

    it('should return null when user is not found', async () => {
      await equipmentService.returnProjectModel();
      const mockPublicUser = (await helper.returnProjectModel()).User;
      mockPublicUser.findOne.mockResolvedValue(null);

      const result = await equipmentService.getUserEnterprise('<EMAIL>', 1);

      expect(result).toBeNull();
    });

    it('should return enterprise name for account member', async () => {
      await equipmentService.returnProjectModel();
      const mockPublicUser = (await helper.returnProjectModel()).User;
      const mockPublicMember = (await helper.returnProjectModel()).Member;

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: true, EnterpriseId: 1 };
      const mockEnterprise = { id: 1, name: 'TestEnterprise', status: 'completed' };

      mockPublicUser.findOne.mockResolvedValue(mockUser);
      mockPublicMember.findOne.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await equipmentService.getUserEnterprise('<EMAIL>', 1);

      expect(result).toBe('testenterprise');
    });

    it('should return enterprise name by ParentCompanyId when not account member', async () => {
      await equipmentService.returnProjectModel();
      const mockPublicUser = (await helper.returnProjectModel()).User;
      const mockPublicMember = (await helper.returnProjectModel()).Member;

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: false };
      const mockEnterprise = { id: 1, name: 'ParentEnterprise', status: 'completed' };

      mockPublicUser.findOne.mockResolvedValue(mockUser);
      mockPublicMember.findOne.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await equipmentService.getUserEnterprise('<EMAIL>', 1);

      expect(result).toBe('parententerprise');
    });

    it('should return null when member is not found', async () => {
      await equipmentService.returnProjectModel();
      const mockPublicUser = (await helper.returnProjectModel()).User;
      const mockPublicMember = (await helper.returnProjectModel()).Member;

      const mockUser = { id: 1, email: '<EMAIL>' };

      mockPublicUser.findOne.mockResolvedValue(mockUser);
      mockPublicMember.findOne.mockResolvedValue(null);

      const result = await equipmentService.getUserEnterprise('<EMAIL>', 1);

      expect(result).toBeUndefined();
    });

    it('should return null when account member enterprise is not found', async () => {
      await equipmentService.returnProjectModel();
      const mockPublicUser = (await helper.returnProjectModel()).User;
      const mockPublicMember = (await helper.returnProjectModel()).Member;

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: true, EnterpriseId: 1 };

      mockPublicUser.findOne.mockResolvedValue(mockUser);
      mockPublicMember.findOne.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await equipmentService.getUserEnterprise('<EMAIL>', 1);

      expect(result).toBeUndefined();
    });

    it('should return null when ParentCompany enterprise is not found', async () => {
      await equipmentService.returnProjectModel();
      const mockPublicUser = (await helper.returnProjectModel()).User;
      const mockPublicMember = (await helper.returnProjectModel()).Member;

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: false };

      mockPublicUser.findOne.mockResolvedValue(mockUser);
      mockPublicMember.findOne.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await equipmentService.getUserEnterprise('<EMAIL>', 1);

      expect(result).toBeUndefined();
    });
  });

  describe('returnProjectModel', () => {
    it('should set public user and member variables', async () => {
      const mockModelData = {
        User: { findOne: jest.fn() },
        Member: { findOne: jest.fn() },
      };

      helper.returnProjectModel.mockResolvedValue(mockModelData);

      await equipmentService.returnProjectModel();

      expect(helper.returnProjectModel).toHaveBeenCalled();
    });
  });

  describe('getMappedRequests', () => {
    beforeEach(() => {
      mockInputData.body = {
        ...mockInputData.body,
        id: 1,
      };
    });

    it('should return mapped requests when equipment exists', async () => {
      const mockEquipment = { id: 1, equipmentAutoId: 1, equipmentName: 'Test Equipment' };
      const mockDeliveryRequests = [
        { id: 1, equipmentDetails: [{ id: 1, Equipment: { id: 1, equipmentName: 'Test Equipment' } }] },
      ];
      const mockCraneRequests = [
        { id: 2, equipmentDetails: [{ id: 2, Equipment: { id: 1, equipmentName: 'Test Equipment' } }] },
      ];
      const mockCraneEquipments = [
        { id: 1, equipmentName: 'Crane Equipment', PresetEquipmentTypeId: 1 },
      ];
      const mockNonCraneEquipments = [
        { id: 2, equipmentName: 'Non-Crane Equipment', PresetEquipmentTypeId: 2 },
      ];

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue(mockCraneRequests);
      mockModels.Equipments.findAll
        .mockResolvedValueOnce(mockCraneEquipments)
        .mockResolvedValueOnce(mockNonCraneEquipments);

      await equipmentService.getMappedRequests(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalledWith({
        where: {
          equipmentAutoId: mockInputData.body.id,
          ProjectId: mockInputData.body.ProjectId,
          isDeleted: false,
        },
      });

      expect(mockDone).toHaveBeenCalledWith(
        {
          mappedRequest: expect.arrayContaining(mockDeliveryRequests.concat(mockCraneRequests)),
          equipments: {
            craneEquipments: mockCraneEquipments,
            nonCraneEquipments: mockNonCraneEquipments,
          },
        },
        false
      );
    });

    it('should return error when equipment not found', async () => {
      mockModels.Equipments.findOne.mockResolvedValue(null);

      await equipmentService.getMappedRequests(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should handle errors during getMappedRequests', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.getMappedRequests(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle when crane requests are null', async () => {
      const mockEquipment = { id: 1, equipmentAutoId: 1, equipmentName: 'Test Equipment' };
      const mockDeliveryRequests = [
        { id: 1, equipmentDetails: [{ id: 1, Equipment: { id: 1, equipmentName: 'Test Equipment' } }] },
      ];
      const mockCraneEquipments = [
        { id: 1, equipmentName: 'Crane Equipment', PresetEquipmentTypeId: 1 },
      ];
      const mockNonCraneEquipments = [
        { id: 2, equipmentName: 'Non-Crane Equipment', PresetEquipmentTypeId: 2 },
      ];

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue(null);
      mockModels.Equipments.findAll
        .mockResolvedValueOnce(mockCraneEquipments)
        .mockResolvedValueOnce(mockNonCraneEquipments);

      await equipmentService.getMappedRequests(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        {
          mappedRequest: mockDeliveryRequests,
          equipments: {
            craneEquipments: mockCraneEquipments,
            nonCraneEquipments: mockNonCraneEquipments,
          },
        },
        false
      );
    });

    it('should handle when crane requests are empty array', async () => {
      const mockEquipment = { id: 1, equipmentAutoId: 1, equipmentName: 'Test Equipment' };
      const mockDeliveryRequests = [
        { id: 1, equipmentDetails: [{ id: 1, Equipment: { id: 1, equipmentName: 'Test Equipment' } }] },
      ];
      const mockCraneEquipments = [
        { id: 1, equipmentName: 'Crane Equipment', PresetEquipmentTypeId: 1 },
      ];
      const mockNonCraneEquipments = [
        { id: 2, equipmentName: 'Non-Crane Equipment', PresetEquipmentTypeId: 2 },
      ];

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue([]);
      mockModels.Equipments.findAll
        .mockResolvedValueOnce(mockCraneEquipments)
        .mockResolvedValueOnce(mockNonCraneEquipments);

      await equipmentService.getMappedRequests(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        {
          mappedRequest: mockDeliveryRequests,
          equipments: {
            craneEquipments: mockCraneEquipments,
            nonCraneEquipments: mockNonCraneEquipments,
          },
        },
        false
      );
    });
  });

  describe('deactivateEquipment', () => {
    beforeEach(() => {
      mockInputData.body = {
        ...mockInputData.body,
        id: 1,
        equipmentSwitchedRequests: [],
      };
      mockInputData.user = {
        ...mockInputData.user,
        firstName: 'John',
        lastName: 'Doe',
      };
    });

    it('should deactivate equipment successfully', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockDeliveryRequests = [];
      const mockCraneRequests = [];

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue(mockCraneRequests);
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.findOne).toHaveBeenCalledWith({
        where: {
          id: mockInputData.body.id,
          ProjectId: mockInputData.body.ProjectId,
          isDeleted: false,
        },
      });
      expect(mockModels.Member.getBy).toHaveBeenCalledWith({
        UserId: mockInputData.user.id,
        ProjectId: mockInputData.body.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      expect(mockDone).toHaveBeenCalledWith([1], false);
    });

    it('should return error when equipment not found', async () => {
      mockModels.Equipments.findOne.mockResolvedValue(null);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should handle delivery request equipment deactivation', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockDeliveryRequests = [
        { id: 1, equipmentDetails: [{ id: 1 }] },
      ];

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue([]);
      mockModels.DeliverHistory.createInstance.mockResolvedValue({});
      mockModels.DeliverEquipment.update.mockResolvedValue([1]);
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockModels.DeliverHistory.createInstance).toHaveBeenCalled();
      expect(mockModels.DeliverEquipment.update).toHaveBeenCalled();
    });

    it('should handle crane request equipment deactivation', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockCraneRequests = [
        { id: 1, equipmentDetails: [{ id: 1 }] },
      ];

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue([]);
      mockModels.CraneRequest.findAll.mockResolvedValue(mockCraneRequests);
      mockModels.CraneRequestHistory.createInstance.mockResolvedValue({});
      mockModels.CraneRequestEquipment.update.mockResolvedValue([1]);
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockModels.CraneRequestHistory.createInstance).toHaveBeenCalled();
      expect(mockModels.CraneRequestEquipment.update).toHaveBeenCalled();
    });

    it('should handle equipment switching for delivery requests', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockSwitchedRequests = [
        {
          id: 1,
          requestType: 'deliveryRequest',
          changedEquipmentId: 2,
          DeliveryId: 'DEL001',
        },
      ];

      mockInputData.body.equipmentSwitchedRequests = mockSwitchedRequests;

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockEquipment)
        .mockResolvedValueOnce({ id: 2, equipmentName: 'New Equipment' });
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue([{ id: 1 }]);
      mockModels.CraneRequest.findAll.mockResolvedValue([]);
      mockModels.DeliverEquipment.findAll.mockResolvedValue([]);
      mockModels.DeliverEquipment.createInstance.mockResolvedValue({});
      mockModels.DeliverHistory.createInstance.mockResolvedValue({});
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockModels.DeliverEquipment.createInstance).toHaveBeenCalled();
    });

    it('should handle equipment switching for crane requests', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockSwitchedRequests = [
        {
          id: 1,
          requestType: 'craneRequest',
          changedEquipmentId: 2,
          CraneRequestId: 'CR001',
        },
      ];

      mockInputData.body.equipmentSwitchedRequests = mockSwitchedRequests;

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue([]);
      mockModels.CraneRequest.findAll.mockResolvedValue([{ id: 1 }]);
      mockModels.CraneRequestEquipment.findAll.mockResolvedValue([]);
      mockModels.CraneRequestEquipment.createInstance.mockResolvedValue({});
      mockModels.CraneRequestHistory.createInstance.mockResolvedValue({});
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockModels.CraneRequestEquipment.createInstance).toHaveBeenCalled();
    });

    it('should handle errors during equipment deactivation', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null input data', async () => {
      const nullInputData = null;

      await expect(equipmentService.getDynamicModel(nullInputData)).rejects.toThrow();
    });

    it('should handle missing user in input data', async () => {
      const inputDataWithoutUser = {
        ...mockInputData,
        user: null,
      };

      await expect(equipmentService.getDynamicModel(inputDataWithoutUser)).rejects.toThrow();
    });

    it('should handle missing body in input data', async () => {
      const inputDataWithoutBody = {
        ...mockInputData,
        body: null,
      };

      await equipmentService.addEquipment(inputDataWithoutBody, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle database connection errors', async () => {
      const dbError = new Error('Database connection failed');
      helper.getDynamicModel.mockRejectedValue(dbError);

      await equipmentService.listEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, dbError);
    });

    it('should handle equipment with null equipmentAutoId', async () => {
      const mockLastEquipment = { equipmentAutoId: null };
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue({ id: 1 });
      mockModels.Equipments.getEquipment.mockResolvedValue(null);
      mockModels.Equipments.findOne.mockResolvedValue(mockLastEquipment);
      mockModels.Equipments.createEquipment.mockResolvedValue({ id: 1 });

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentAutoId: 1,
        })
      );
    });

    it('should handle equipment with undefined equipmentAutoId', async () => {
      const mockLastEquipment = { equipmentAutoId: undefined };
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue({ id: 1 });
      mockModels.Equipments.getEquipment.mockResolvedValue(null);
      mockModels.Equipments.findOne.mockResolvedValue(mockLastEquipment);
      mockModels.Equipments.createEquipment.mockResolvedValue({ id: 1 });

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockModels.Equipments.createEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          equipmentAutoId: 1,
        })
      );
    });

    it('should handle empty equipment list in deleteEquipment', async () => {
      mockModels.Equipments.findAll.mockResolvedValue([]);

      await equipmentService.deleteEquipment(mockInputData, mockDone);

      expect(mockModels.DeliverEquipment.findOne).not.toHaveBeenCalled();
      expect(mockModels.CraneRequestEquipment.findOne).not.toHaveBeenCalled();
    });

    it('should handle missing params in input data', async () => {
      const inputDataWithoutParams = {
        ...mockInputData,
        params: null,
      };

      await equipmentService.lastEquipment(inputDataWithoutParams, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle getDomainEnterprise with null domain name', async () => {
      const result = await equipmentService.getDomainEnterprise(null);
      expect(result).toBeNull();
    });

    it('should handle getDomainEnterprise with undefined domain name', async () => {
      const result = await equipmentService.getDomainEnterprise(undefined);
      expect(result).toBeNull();
    });

    it('should handle getDomainEnterprise with empty string', async () => {
      const result = await equipmentService.getDomainEnterprise('');
      expect(result).toBeNull();
    });

    it('should handle getDomainEnterprise with valid domain but no enterprise found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);
      const result = await equipmentService.getDomainEnterprise('nonexistent');
      expect(result).toBeNull();
    });

    it('should handle getDomainEnterprise with valid domain and enterprise found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue({ id: 1, name: 'testdomain' });
      const result = await equipmentService.getDomainEnterprise('testdomain');
      expect(result).toBe('testdomain');
    });

    it('should handle getUserEnterprise with null email', async () => {
      const result = await equipmentService.getUserEnterprise(null, 1);
      expect(result).toBeNull();
    });

    it('should handle getUserEnterprise with null ParentCompanyId', async () => {
      const result = await equipmentService.getUserEnterprise('<EMAIL>', null);
      expect(result).toBeNull();
    });

    it('should handle getUserEnterprise with undefined email', async () => {
      const result = await equipmentService.getUserEnterprise(undefined, 1);
      expect(result).toBeNull();
    });

    it('should handle getUserEnterprise with undefined ParentCompanyId', async () => {
      const result = await equipmentService.getUserEnterprise('<EMAIL>', undefined);
      expect(result).toBeNull();
    });

    it('should handle lastEquipment with undefined params', async () => {
      const inputDataWithUndefinedParams = {
        ...mockInputData,
        params: undefined,
      };

      await equipmentService.lastEquipment(inputDataWithUndefinedParams, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle getAllEquipmentType with missing params', async () => {
      const inputDataWithoutParams = {
        ...mockInputData,
        params: null,
      };

      await equipmentService.getAllEquipmentType(inputDataWithoutParams, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle craneListEquipment with missing params', async () => {
      const inputDataWithoutParams = {
        ...mockInputData,
        params: null,
      };

      await equipmentService.craneListEquipment(inputDataWithoutParams, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle updateEquipment with missing body', async () => {
      const inputDataWithoutBody = {
        ...mockInputData,
        body: null,
      };

      await equipmentService.updateEquipment(inputDataWithoutBody, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle deleteEquipment with missing body', async () => {
      const inputDataWithoutBody = {
        ...mockInputData,
        body: null,
      };

      await equipmentService.deleteEquipment(inputDataWithoutBody, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle getMappedRequests with missing body', async () => {
      const inputDataWithoutBody = {
        ...mockInputData,
        body: null,
      };

      await equipmentService.getMappedRequests(inputDataWithoutBody, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle deactivateEquipment with missing body', async () => {
      const inputDataWithoutBody = {
        ...mockInputData,
        body: null,
      };

      await equipmentService.deactivateEquipment(inputDataWithoutBody, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle deactivateEquipment with missing user', async () => {
      const inputDataWithoutUser = {
        ...mockInputData,
        user: null,
      };

      await equipmentService.deactivateEquipment(inputDataWithoutUser, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle listEquipment with all filters combined', async () => {
      const inputDataWithAllFilters = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          showActivatedAlone: true,
          nameFilter: 'Crane',
          companyNameFilter: 'Test Company',
          idFilter: 123,
          memberFilter: 456,
          typeFilter: 'Crane',
          search: 'test',
          sort: 'DESC',
          sortByField: 'equipmentName',
        },
        params: {
          ...mockInputData.params,
          pageNo: '3',
          pageSize: '15',
        },
      };

      const mockEquipmentList = [
        { id: 1, equipmentName: 'Test Crane' },
      ];

      mockModels.Equipments.getAll.mockResolvedValue(mockEquipmentList);

      await equipmentService.listEquipment(inputDataWithAllFilters, mockDone);

      expect(mockModels.Equipments.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          isActive: true,
          equipmentName: {
            [mockModels.Sequelize.Op.iLike]: '%Crane%',
          },
          '$controllUserDetails.Company.companyName$': {
            [mockModels.Sequelize.Op.iLike]: '%Test Company%',
          },
          equipmentAutoId: 123,
          '$controllUserDetails.id$': 456,
          '$PresetEquipmentType.equipmentType$': 'Crane',
        }),
        15, // pageSize
        30, // offset (pageNo - 1) * pageSize = (3 - 1) * 15
        expect.objectContaining({
          [mockModels.Sequelize.Op.and]: expect.any(Array),
        }),
        'DESC',
        'equipmentName'
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentList, false);
    });

    it('should handle addEquipment with missing member for createdBy', async () => {
      mockModels.Member.getBy.mockResolvedValueOnce(null); // createdBy not found

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle addEquipment with missing member for controlledBy', async () => {
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy found
        .mockResolvedValueOnce(null); // controlledBy not found

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle addEquipment with missing project', async () => {
      mockModels.Member.getBy
        .mockResolvedValueOnce({ id: 1 }) // createdBy
        .mockResolvedValueOnce({ id: 1 }); // controlledBy
      mockModels.Project.getProject.mockResolvedValue(null);

      await equipmentService.addEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle updateEquipment with missing project', async () => {
      const mockExistingEquipment = { id: 1, equipmentName: 'Old Equipment' };

      mockModels.Equipments.findOne.mockResolvedValueOnce(mockExistingEquipment);
      mockModels.Project.getProject.mockResolvedValue(null);

      await equipmentService.updateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle deactivateEquipment with missing member', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };

      mockModels.Equipments.findOne.mockResolvedValue(mockEquipment);
      mockModels.Member.getBy.mockResolvedValue(null);

      await equipmentService.deactivateEquipment(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle deactivateEquipment with equipment switched requests', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockDeliveryRequests = [
        { id: 1, equipmentDetails: [{ id: 1, Equipment: { id: 1, equipmentName: 'Test Equipment' } }] },
      ];
      const mockCraneRequests = [];

      const inputDataWithSwitchedRequests = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          id: 1,
          equipmentSwitchedRequests: [
            {
              id: 1,
              requestType: 'deliveryRequest',
              DeliveryId: 'DEL001',
              changedEquipmentId: 2,
            },
          ],
        },
      };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockEquipment) // Main equipment
        .mockResolvedValueOnce({ id: 2, equipmentName: 'New Equipment' }); // Changed equipment
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue(mockCraneRequests);
      mockModels.DeliverEquipment.findAll.mockResolvedValue([]);
      mockModels.DeliverEquipment.createInstance.mockResolvedValue({ id: 1 });
      mockModels.DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(inputDataWithSwitchedRequests, mockDone);

      expect(mockModels.DeliverEquipment.createInstance).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith([1], false);
    });

    it('should handle deactivateEquipment with crane request switched', async () => {
      const mockEquipment = { id: 1, equipmentName: 'Test Equipment' };
      const mockMember = { id: 1, UserId: 1 };
      const mockDeliveryRequests = [];
      const mockCraneRequests = [
        { id: 1, equipmentDetails: [{ id: 1, Equipment: { id: 1, equipmentName: 'Test Equipment' } }] },
      ];

      const inputDataWithCraneSwitchedRequests = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          id: 1,
          equipmentSwitchedRequests: [
            {
              id: 1,
              requestType: 'craneRequest',
              CraneRequestId: 'CR001',
              changedEquipmentId: 2,
            },
          ],
        },
      };

      mockModels.Equipments.findOne
        .mockResolvedValueOnce(mockEquipment) // Main equipment
        .mockResolvedValueOnce({ id: 2, equipmentName: 'New Crane Equipment' }); // Changed equipment
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.DeliveryRequest.findAll.mockResolvedValue(mockDeliveryRequests);
      mockModels.CraneRequest.findAll.mockResolvedValue(mockCraneRequests);
      mockModels.CraneRequestEquipment.findAll.mockResolvedValue([]);
      mockModels.CraneRequestEquipment.createInstance.mockResolvedValue({ id: 1 });
      mockModels.CraneRequestHistory.createInstance.mockResolvedValue({ id: 1 });
      mockModels.Equipments.update.mockResolvedValue([1]);

      await equipmentService.deactivateEquipment(inputDataWithCraneSwitchedRequests, mockDone);

      expect(mockModels.CraneRequestEquipment.createInstance).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith([1], false);
    });
  });
});
