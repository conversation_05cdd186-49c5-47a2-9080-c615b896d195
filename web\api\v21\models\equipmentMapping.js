module.exports = (sequelize, DataTypes) => {
    const EquipmentMapping = sequelize.define(
        'EquipmentMapping',
        {
            EquipmentId: DataTypes.STRING,
            GateId: DataTypes.INTEGER,
            LocationId: DataTypes.INTEGER,
            Date: DataTypes.DATE,
            startTime: DataTypes.DATE,
            endTime: DataTypes.DATE,
            isDeleted: DataTypes.BOOLEAN,
            DeliveryId: DataTypes.INTEGER,
            CraneId: DataTypes.INTEGER,
            ConcreteId: DataTypes.INTEGER,
            InspectionId: DataTypes.INTEGER
        },
        {},
    );
    return EquipmentMapping;
};