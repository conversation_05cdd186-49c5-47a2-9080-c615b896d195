const Joi = require('joi');

const companyValidation = {
  addCompany: {
    body: Joi.object({
      companyName: Joi.string().min(3).required(),
      scope: Joi.optional().allow(''),
      address: Joi.optional().allow(''),
      secondAddress: Joi.optional().allow(''),
      definableWorkId: Joi.array(),
      ProjectId: Joi.number().required(),
      country: Joi.optional().allow(''),
      logo: Joi.optional().allow(''),
      city: Joi.optional().allow(''),
      state: Joi.optional().allow(''),
      zipCode: Joi.optional().allow(''),
      website: Joi.string().allow('', null),
      isParent: Joi.boolean().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  editCompany: {
    body: Joi.object({
      id: Joi.number().required(),
      companyName: Joi.string().min(3).required(),
      scope: Joi.optional().allow(''),
      logo: Joi.optional().allow(''),
      address: Joi.optional().allow(''),
      secondAddress: Joi.optional().allow(''),
      definableWorkId: Joi.array(),
      ProjectId: Joi.number().required(),
      country: Joi.optional().allow(''),
      city: Joi.optional().allow(''),
      state: Joi.optional().allow(''),
      zipCode: Joi.optional().allow(''),
      website: Joi.string().allow('', null),
      ParentCompanyId: Joi.any(),
    }),
  },
  getDefinableWork: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  sampleCompanyTemplate: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  listCompany: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      companyFilter: Joi.optional().allow(''),
      dfowFilter: Joi.number(),
      search: Joi.optional().allow(''),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      inviteMember: Joi.boolean().required(),
    }),
  },
  deleteCompany: {
    body: Joi.object({
      id: Joi.array(),
      ProjectId: Joi.number().required(),
      isSelectAll: Joi.boolean().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  createCompany: {
    body: Joi.object({
      company: Joi.optional().allow(''),
    }),
  },
};
module.exports = companyValidation;
