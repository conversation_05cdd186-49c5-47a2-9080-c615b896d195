const { parentPort } = require('worker_threads');
const { parse } = require('flatted');
const { Company, CompanyDefine, Member, DeliverDefineWork } = require('../models');
const companyservice = require('../companyService');
const { companyService } = require('..');

// Mock all dependencies
jest.mock('worker_threads', () => ({
    parentPort: {
        on: jest.fn(),
        postMessage: jest.fn()
    }
}));

jest.mock('../models', () => ({
    Company: {
        findOne: jest.fn(),
        createInstance: jest.fn()
    },
    CompanyDefine: {
        createInstance: jest.fn()
    },
    Member: {
        getBy: jest.fn()
    },
    DeliverDefineWork: {
        findOne: jest.fn()
    }
}));

jest.mock('../companyService', () => ({
    getDynamicModel: jest.fn(),
    createPublicCompany: jest.fn()
}));

jest.mock('..', () => ({
    companyService: {
        createPublicCompany: jest.fn()
    }
}));

// Import the module under test
const bulkCompanyProcess = require('../bulkCompanyProcess');

describe('bulkCompanyProcess', () => {
    let mockLoginUser;
    let mockProjectId;
    let mockProjectDetails;
    let mockInputData;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();

        // Setup common test data
        mockLoginUser = {
            id: 1,
            name: 'Test User'
        };

        mockProjectId = 123;
        mockProjectDetails = {
            id: mockProjectId,
            name: 'Test Project'
        };

        mockInputData = {
            user: {
                domainName: 'test.com'
            }
        };

        // Reset companyService mock to resolved state
        companyService.createPublicCompany.mockResolvedValue({ success: true });
    });

    describe('validateMemberAccess', () => {
        it('should return member details when valid access exists', async () => {
            const mockMember = { id: 1, UserId: 1, ProjectId: mockProjectId };
            Member.getBy.mockResolvedValue(mockMember);

            const result = await bulkCompanyProcess.validateMemberAccess(mockLoginUser, mockProjectId);
            expect(result).toEqual(mockMember);
            expect(Member.getBy).toHaveBeenCalledWith({
                UserId: mockLoginUser.id,
                ProjectId: mockProjectId,
                isActive: true,
                isDeleted: false
            });
        });

        it('should return null when no member access exists', async () => {
            Member.getBy.mockResolvedValue(null);

            const result = await bulkCompanyProcess.validateMemberAccess(mockLoginUser, mockProjectId);
            expect(result).toBeNull();
        });

        it('should handle database errors gracefully', async () => {
            const dbError = new Error('Database connection failed');
            Member.getBy.mockRejectedValue(dbError);

            await expect(bulkCompanyProcess.validateMemberAccess(mockLoginUser, mockProjectId))
                .rejects.toThrow('Database connection failed');
        });

        it('should handle invalid user ID', async () => {
            const invalidUser = { id: null };
            Member.getBy.mockResolvedValue(null);

            const result = await bulkCompanyProcess.validateMemberAccess(invalidUser, mockProjectId);
            expect(result).toBeNull();
            expect(Member.getBy).toHaveBeenCalledWith({
                UserId: null,
                ProjectId: mockProjectId,
                isActive: true,
                isDeleted: false
            });
        });

        it('should handle invalid project ID', async () => {
            Member.getBy.mockResolvedValue(null);

            const result = await bulkCompanyProcess.validateMemberAccess(mockLoginUser, null);
            expect(result).toBeNull();
            expect(Member.getBy).toHaveBeenCalledWith({
                UserId: mockLoginUser.id,
                ProjectId: null,
                isActive: true,
                isDeleted: false
            });
        });
    });

    describe('getNextCompanyAutoId', () => {
        it('should return incremented auto ID when previous company exists', async () => {
            const mockLastCompany = { companyAutoId: 5 };
            Company.findOne.mockResolvedValue(mockLastCompany);

            const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
            expect(result).toBe(6);
            expect(Company.findOne).toHaveBeenCalledWith({
                where: { ProjectId: mockProjectId, isDeleted: false },
                order: [['companyAutoId', 'DESC']]
            });
        });

        it('should return 1 when no previous company exists', async () => {
            Company.findOne.mockResolvedValue(null);

            const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
            expect(result).toBe(1);
        });

        it('should return 1 when companyAutoId is null', async () => {
            const mockLastCompany = { companyAutoId: null };
            Company.findOne.mockResolvedValue(mockLastCompany);

            const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
            expect(result).toBe(1);
        });

        it('should return 1 when companyAutoId is undefined', async () => {
            const mockLastCompany = { companyAutoId: undefined };
            Company.findOne.mockResolvedValue(mockLastCompany);

            const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
            expect(result).toBe(1);
        });

        it('should return 1 when companyAutoId is 0', async () => {
            const mockLastCompany = { companyAutoId: 0 };
            Company.findOne.mockResolvedValue(mockLastCompany);

            const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
            expect(result).toBe(1);
        });

        it('should handle database errors', async () => {
            const dbError = new Error('Database query failed');
            Company.findOne.mockRejectedValue(dbError);

            await expect(bulkCompanyProcess.getNextCompanyAutoId(mockProjectId))
                .rejects.toThrow('Database query failed');
        });

        it('should handle large auto ID values', async () => {
            const mockLastCompany = { companyAutoId: 999999 };
            Company.findOne.mockResolvedValue(mockLastCompany);

            const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
            expect(result).toBe(1000000);
        });
    });

    describe('getWebsiteValue', () => {
        it('should return null for empty website', () => {
            expect(bulkCompanyProcess.getWebsiteValue(null)).toBeNull();
            expect(bulkCompanyProcess.getWebsiteValue(undefined)).toBeNull();
            expect(bulkCompanyProcess.getWebsiteValue('')).toBeNull();
            expect(bulkCompanyProcess.getWebsiteValue(false)).toBeNull();
            expect(bulkCompanyProcess.getWebsiteValue(0)).toBeNull();
        });

        it('should return text property for object website', () => {
            const websiteObj = { text: 'https://example.com' };
            expect(bulkCompanyProcess.getWebsiteValue(websiteObj)).toBe('https://example.com');
        });

        it('should return string value for string website', () => {
            expect(bulkCompanyProcess.getWebsiteValue('https://example.com')).toBe('https://example.com');
        });

        it('should handle object without text property', () => {
            const websiteObj = { url: 'https://example.com' };
            expect(bulkCompanyProcess.getWebsiteValue(websiteObj)).toBeUndefined();
        });

        it('should handle object with null text property', () => {
            const websiteObj = { text: null };
            expect(bulkCompanyProcess.getWebsiteValue(websiteObj)).toBeNull();
        });

        it('should handle object with empty text property', () => {
            const websiteObj = { text: '' };
            expect(bulkCompanyProcess.getWebsiteValue(websiteObj)).toBe('');
        });

        it('should handle numeric string website', () => {
            expect(bulkCompanyProcess.getWebsiteValue('123')).toBe('123');
        });

        it('should handle array input as object type', () => {
            const websiteArray = ['https://example.com'];
            // Arrays are objects in JavaScript, so this will try to access .text property
            expect(bulkCompanyProcess.getWebsiteValue(websiteArray)).toBeUndefined();
        });
    });

    describe('prepareCompanyParams', () => {
        it('should prepare company parameters correctly', () => {
            const mockRow = {
                companyName: 'Test Company',
                address_line_1: '123 Main St',
                address_line_2: 'Suite 100',
                country: 'USA',
                state: 'CA',
                city: 'San Francisco',
                website: 'https://example.com',
                zipcode: '94105',
                additional_notes: 'Test notes'
            };

            const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);

            expect(result).toEqual({
                companyName: 'Test Company',
                address: '123 Main St',
                secondAddress: 'Suite 100',
                country: 'USA',
                state: 'CA',
                city: 'San Francisco',
                website: 'https://example.com',
                zipCode: '94105',
                additional_notes: 'Test notes',
                ProjectId: mockProjectId,
                isParent: false
            });
        });

        it('should handle null values in row data', () => {
            const mockRow = {
                companyName: 'Test Company'
            };

            const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);

            expect(result).toEqual({
                companyName: 'Test Company',
                address: null,
                secondAddress: null,
                country: null,
                state: null,
                city: null,
                website: null,
                zipCode: null,
                additional_notes: null,
                ProjectId: mockProjectId,
                isParent: false
            });
        });

        it('should handle empty string values', () => {
            const mockRow = {
                companyName: 'Test Company',
                address_line_1: '',
                address_line_2: '',
                country: '',
                state: '',
                city: '',
                website: '',
                zipcode: '',
                additional_notes: ''
            };

            const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);

            expect(result).toEqual({
                companyName: 'Test Company',
                address: null,
                secondAddress: null,
                country: null,
                state: null,
                city: null,
                website: null,
                zipCode: null,
                additional_notes: null,
                ProjectId: mockProjectId,
                isParent: false
            });
        });

        it('should handle website object', () => {
            const mockRow = {
                companyName: 'Test Company',
                website: { text: 'https://example.com' }
            };

            const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);

            expect(result.website).toBe('https://example.com');
        });

        it('should handle mixed data types', () => {
            const mockRow = {
                companyName: 'Test Company',
                address_line_1: 123,
                zipcode: 94105,
                website: true
            };

            const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);

            expect(result.address).toBe(123);
            expect(result.zipCode).toBe(94105);
            expect(result.website).toBe(true);
        });
    });

    describe('prepareRowData', () => {
        it('should prepare row data correctly from array', () => {
            const mockElement = ['header', 'Company1', 'DFOW1', 'Address1', 'Address2', 'USA', 'CA', 'SF', '94105', 'https://example.com', 'Notes'];

            const result = bulkCompanyProcess.prepareRowData(mockElement);

            expect(result).toEqual({
                companyName: 'Company1',
                dfow: 'DFOW1',
                address_line_1: 'Address1',
                address_line_2: 'Address2',
                country: 'USA',
                state: 'CA',
                city: 'SF',
                zipcode: '94105',
                website: 'https://example.com',
                additional_notes: 'Notes'
            });
        });

        it('should handle array with missing elements', () => {
            const mockElement = ['header', 'Company1'];

            const result = bulkCompanyProcess.prepareRowData(mockElement);

            expect(result).toEqual({
                companyName: 'Company1',
                dfow: undefined,
                address_line_1: undefined,
                address_line_2: undefined,
                country: undefined,
                state: undefined,
                city: undefined,
                zipcode: undefined,
                website: undefined,
                additional_notes: undefined
            });
        });

        it('should handle empty array', () => {
            const mockElement = ['header'];

            const result = bulkCompanyProcess.prepareRowData(mockElement);

            expect(result).toEqual({
                companyName: undefined,
                dfow: undefined,
                address_line_1: undefined,
                address_line_2: undefined,
                country: undefined,
                state: undefined,
                city: undefined,
                zipcode: undefined,
                website: undefined,
                additional_notes: undefined
            });
        });

        it('should not modify original array', () => {
            const mockElement = ['header', 'Company1', 'DFOW1'];
            const originalLength = mockElement.length;

            bulkCompanyProcess.prepareRowData(mockElement);

            expect(mockElement.length).toBe(originalLength);
            expect(mockElement[0]).toBe('header');
        });
    });

    describe('getDFOWData', () => {
        it('should return DFOW data when found', async () => {
            const mockDFOW = { id: 1, DFOW: 'TEST-DFOW' };
            DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);

            const result = await bulkCompanyProcess.getDFOWData('TEST-DFOW', mockProjectId);
            expect(result).toEqual(mockDFOW);
            expect(DeliverDefineWork.findOne).toHaveBeenCalledWith({
                where: { DFOW: 'TEST-DFOW', ProjectId: mockProjectId }
            });
        });

        it('should return null when DFOW not found', async () => {
            DeliverDefineWork.findOne.mockResolvedValue(null);

            const result = await bulkCompanyProcess.getDFOWData('NONEXISTENT-DFOW', mockProjectId);
            expect(result).toBeNull();
        });

        it('should return null when DFOW is empty', async () => {
            const result = await bulkCompanyProcess.getDFOWData('', mockProjectId);
            expect(result).toBeNull();
            expect(DeliverDefineWork.findOne).not.toHaveBeenCalled();
        });

        it('should return null when DFOW is null', async () => {
            const result = await bulkCompanyProcess.getDFOWData(null, mockProjectId);
            expect(result).toBeNull();
            expect(DeliverDefineWork.findOne).not.toHaveBeenCalled();
        });

        it('should return null when DFOW is undefined', async () => {
            const result = await bulkCompanyProcess.getDFOWData(undefined, mockProjectId);
            expect(result).toBeNull();
            expect(DeliverDefineWork.findOne).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            const dbError = new Error('Database connection failed');
            DeliverDefineWork.findOne.mockRejectedValue(dbError);

            await expect(bulkCompanyProcess.getDFOWData('TEST-DFOW', mockProjectId))
                .rejects.toThrow('Database connection failed');
        });
    });

    describe('createCompanyDefinition', () => {
        it('should create company definition when DFOW exists', async () => {
            const mockDFOW = { id: 1, DFOW: 'TEST-DFOW' };
            const mockCompany = { id: 1 };
            DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);
            CompanyDefine.createInstance.mockResolvedValue({ id: 1 });

            await bulkCompanyProcess.createCompanyDefinition(
                { dfow: 'TEST-DFOW' },
                mockCompany,
                mockProjectId
            );

            expect(CompanyDefine.createInstance).toHaveBeenCalledWith({
                DeliverDefineWorkId: mockDFOW.id,
                CompanyId: mockCompany.id,
                ProjectId: mockProjectId
            });
        });

        it('should not create company definition when DFOW does not exist', async () => {
            DeliverDefineWork.findOne.mockResolvedValue(null);

            await bulkCompanyProcess.createCompanyDefinition(
                { dfow: 'NONEXISTENT-DFOW' },
                { id: 1 },
                mockProjectId
            );

            expect(CompanyDefine.createInstance).not.toHaveBeenCalled();
        });

        it('should not create company definition when DFOW is empty', async () => {
            await bulkCompanyProcess.createCompanyDefinition(
                { dfow: '' },
                { id: 1 },
                mockProjectId
            );

            expect(DeliverDefineWork.findOne).not.toHaveBeenCalled();
            expect(CompanyDefine.createInstance).not.toHaveBeenCalled();
        });

        it('should not create company definition when DFOW is null', async () => {
            await bulkCompanyProcess.createCompanyDefinition(
                { dfow: null },
                { id: 1 },
                mockProjectId
            );

            expect(DeliverDefineWork.findOne).not.toHaveBeenCalled();
            expect(CompanyDefine.createInstance).not.toHaveBeenCalled();
        });

        it('should handle database errors during DFOW lookup', async () => {
            const dbError = new Error('DFOW lookup failed');
            DeliverDefineWork.findOne.mockRejectedValue(dbError);

            await expect(bulkCompanyProcess.createCompanyDefinition(
                { dfow: 'TEST-DFOW' },
                { id: 1 },
                mockProjectId
            )).rejects.toThrow('DFOW lookup failed');
        });

        it('should handle database errors during company definition creation', async () => {
            const mockDFOW = { id: 1, DFOW: 'TEST-DFOW' };
            const dbError = new Error('Company definition creation failed');
            DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);
            CompanyDefine.createInstance.mockRejectedValue(dbError);

            await expect(bulkCompanyProcess.createCompanyDefinition(
                { dfow: 'TEST-DFOW' },
                { id: 1 },
                mockProjectId
            )).rejects.toThrow('Company definition creation failed');
        });
    });

    describe('createPublicCompanyIfNeeded', () => {
        it('should create public company when domain name exists', async () => {
            const companyParam = { companyName: 'Test Company' };
            const inputData = { user: { domainName: 'test.com' } };

            await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

            expect(companyService.createPublicCompany).toHaveBeenCalledWith(companyParam);
        });

        it('should not create public company when domain name does not exist', async () => {
            const companyParam = { companyName: 'Test Company' };
            const inputData = { user: {} };

            await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

            expect(companyService.createPublicCompany).not.toHaveBeenCalled();
        });

        it('should not create public company when domain name is null', async () => {
            const companyParam = { companyName: 'Test Company' };
            const inputData = { user: { domainName: null } };

            await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

            expect(companyService.createPublicCompany).not.toHaveBeenCalled();
        });

        it('should not create public company when domain name is empty string', async () => {
            const companyParam = { companyName: 'Test Company' };
            const inputData = { user: { domainName: '' } };

            await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

            expect(companyService.createPublicCompany).not.toHaveBeenCalled();
        });

        it('should not create public company when user is missing', async () => {
            const companyParam = { companyName: 'Test Company' };
            const inputData = {};

            await expect(bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData))
                .rejects.toThrow();
        });

        it('should handle errors during public company creation', async () => {
            const companyParam = { companyName: 'Test Company' };
            const inputData = { user: { domainName: 'test.com' } };
            const serviceError = new Error('Public company creation failed');
            companyService.createPublicCompany.mockRejectedValue(serviceError);

            await expect(bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData))
                .rejects.toThrow('Public company creation failed');
        });

        it('should handle undefined input data', async () => {
            const companyParam = { companyName: 'Test Company' };

            await expect(bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, undefined))
                .rejects.toThrow();
        });
    });

    describe('processCompanyRow', () => {
        it('should process company row successfully', async () => {
            const mockRow = {
                companyName: 'Test Company',
                dfow: 'TEST-DFOW'
            };
            const mockMember = { id: 1 };
            const mockCompany = { id: 1 };
            const mockDFOW = { id: 1 };

            Member.getBy.mockResolvedValue(mockMember);
            Company.findOne.mockResolvedValue({ companyAutoId: 5 });
            Company.createInstance.mockResolvedValue(mockCompany);
            DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).toHaveBeenCalled();
            expect(companyService.createPublicCompany).toHaveBeenCalled();
            expect(CompanyDefine.createInstance).toHaveBeenCalled();
        });

        it('should not process when company name is missing', async () => {
            const mockRow = { dfow: 'TEST-DFOW' };

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should not process when company name is empty string', async () => {
            const mockRow = { companyName: '', dfow: 'TEST-DFOW' };

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should not process when project details are missing', async () => {
            const mockRow = { companyName: 'Test Company' };

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                null,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should not process when member access is invalid', async () => {
            const mockRow = { companyName: 'Test Company' };
            Member.getBy.mockResolvedValue(null);

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should process company without DFOW', async () => {
            const mockRow = {
                companyName: 'Test Company'
                // no dfow property
            };
            const mockMember = { id: 1 };
            const mockCompany = { id: 1 };

            Member.getBy.mockResolvedValue(mockMember);
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue(mockCompany);

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).toHaveBeenCalled();
            expect(companyService.createPublicCompany).toHaveBeenCalled();
            expect(CompanyDefine.createInstance).not.toHaveBeenCalled();
        });

        it('should handle errors during company creation', async () => {
            const mockRow = { companyName: 'Test Company' };
            const mockMember = { id: 1 };
            const createError = new Error('Company creation failed');

            Member.getBy.mockResolvedValue(mockMember);
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockRejectedValue(createError);

            await expect(bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            )).rejects.toThrow('Company creation failed');
        });

        it('should handle errors during member validation', async () => {
            const mockRow = { companyName: 'Test Company' };
            const memberError = new Error('Member validation failed');

            Member.getBy.mockRejectedValue(memberError);

            await expect(bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            )).rejects.toThrow('Member validation failed');
        });

        it('should process company with all optional fields', async () => {
            const mockRow = {
                companyName: 'Test Company',
                dfow: 'TEST-DFOW',
                address_line_1: '123 Main St',
                address_line_2: 'Suite 100',
                country: 'USA',
                state: 'CA',
                city: 'San Francisco',
                website: 'https://example.com',
                zipcode: '94105',
                additional_notes: 'Test notes'
            };
            const mockMember = { id: 1 };
            const mockCompany = { id: 1 };
            const mockDFOW = { id: 1 };

            Member.getBy.mockResolvedValue(mockMember);
            Company.findOne.mockResolvedValue({ companyAutoId: 10 });
            Company.createInstance.mockResolvedValue(mockCompany);
            DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);

            await bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    companyName: 'Test Company',
                    address: '123 Main St',
                    secondAddress: 'Suite 100',
                    country: 'USA',
                    state: 'CA',
                    city: 'San Francisco',
                    website: 'https://example.com',
                    zipCode: '94105',
                    additional_notes: 'Test notes',
                    ProjectId: mockProjectId,
                    isParent: false,
                    companyAutoId: 11
                })
            );
        });
    });

    describe('Message Handler', () => {
        // Test the message handler functionality by testing the main processing logic
        it('should process all company records successfully', async () => {
            // Reset mocks for this specific test
            Company.createInstance.mockClear();

            const mockCompanyRecords = [
                ['header1', 'header2', 'header3'],
                ['Company1', 'DFOW1', 'Address1'],
                ['Company2', 'DFOW2', 'Address2']
            ];

            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue({ companyAutoId: 5 });
            Company.createInstance.mockResolvedValue({ id: 1 });
            DeliverDefineWork.findOne.mockResolvedValue({ id: 1 });

            // Test the processing logic for each record
            for (let i = 0; i < mockCompanyRecords.length; i++) {
                const element = mockCompanyRecords[i];
                const row = bulkCompanyProcess.prepareRowData(element);

                if (row.companyName) {
                    await bulkCompanyProcess.processCompanyRow(
                        row,
                        mockProjectDetails,
                        mockLoginUser,
                        mockProjectId,
                        mockInputData
                    );
                }
            }

            expect(Company.createInstance).toHaveBeenCalled();
        });

        it('should handle empty company records', async () => {
            const mockCompanyRecords = [];

            // Test processing empty records
            for (let i = 0; i < mockCompanyRecords.length; i++) {
                // No processing should occur
            }

            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should handle single company record', async () => {
            // Reset mocks for this specific test
            Company.createInstance.mockClear();

            const mockCompanyRecords = [
                ['Company1', 'DFOW1', 'Address1']
            ];

            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue({ id: 1 });

            const row = bulkCompanyProcess.prepareRowData(mockCompanyRecords[0]);
            await bulkCompanyProcess.processCompanyRow(
                row,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            );

            expect(Company.createInstance).toHaveBeenCalledTimes(1);
        });

        it('should process records with mixed success and failures', async () => {
            // Reset mocks for this specific test
            Company.createInstance.mockClear();

            const mockCompanyRecords = [
                ['header', 'Company1', 'DFOW1', 'Address1'],
                ['header', '', 'DFOW2', 'Address2'], // This should be skipped due to empty company name
                ['header', 'Company3', 'DFOW3', 'Address3']
            ];

            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue({ id: 1 });

            let processedCount = 0;
            for (let i = 0; i < mockCompanyRecords.length; i++) {
                const row = bulkCompanyProcess.prepareRowData(mockCompanyRecords[i]);
                if (row.companyName) {
                    await bulkCompanyProcess.processCompanyRow(
                        row,
                        mockProjectDetails,
                        mockLoginUser,
                        mockProjectId,
                        mockInputData
                    );
                    processedCount++;
                }
            }

            // Should only create 2 companies (skipping the one with empty name)
            expect(Company.createInstance).toHaveBeenCalled();
            expect(processedCount).toBe(2);
        });

        it('should handle errors during processing', async () => {
            const mockRow = { companyName: 'Test Company' };
            const createError = new Error('Database error');

            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockRejectedValue(createError);

            await expect(bulkCompanyProcess.processCompanyRow(
                mockRow,
                mockProjectDetails,
                mockLoginUser,
                mockProjectId,
                mockInputData
            )).rejects.toThrow('Database error');
        });

        it('should test console.log for last record simulation', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            // Simulate the last record condition
            const mockCompanyRecords = [
                ['Company1', 'DFOW1', 'Address1'],
                ['Company2', 'DFOW2', 'Address2']
            ];

            // Test the logic that would be in the message handler
            for (const [i] of mockCompanyRecords.entries()) {
                if (i === mockCompanyRecords.length - 1) {
                    console.log('success');
                }
            }

            expect(consoleSpy).toHaveBeenCalledWith('success');
            consoleSpy.mockRestore();
        });

        it('should test message handler registration simulation', () => {
            // Since we're testing the exported functions, we simulate the message handler behavior
            expect(typeof bulkCompanyProcess.processCompanyRow).toBe('function');
            expect(typeof bulkCompanyProcess.prepareRowData).toBe('function');
        });

        it('should test postMessage call simulation', () => {
            // Simulate the postMessage call that would happen at the end of processing
            parentPort.postMessage('done');
            expect(parentPort.postMessage).toHaveBeenCalledWith('done');
        });

        it('should test getDynamicModel call simulation', async () => {
            // Test that getDynamicModel would be called with inputData
            companyservice.getDynamicModel.mockResolvedValue(true);

            await companyservice.getDynamicModel(mockInputData);

            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
        });

        it('should test message handler logic simulation', async () => {
            // Test the message handler logic by simulating the exact flow
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [
                    ['header', 'Company1', 'DFOW1', 'Address1'],
                    ['header', 'Company2', 'DFOW2', 'Address2']
                ],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Setup mocks for successful processing
            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue({ id: 1 });
            companyservice.getDynamicModel.mockResolvedValue(true);

            // Simulate the exact message handler logic without using parse
            await companyservice.getDynamicModel(mockMessageData.inputData);

            for (const [i, element] of mockMessageData.companyRecords.entries()) {
                const row = bulkCompanyProcess.prepareRowData(element);
                await bulkCompanyProcess.processCompanyRow(
                    row,
                    mockMessageData.projectDetails,
                    mockMessageData.loginUser,
                    mockMessageData.ProjectId,
                    mockMessageData.inputData
                );

                if (i === mockMessageData.companyRecords.length - 1) {
                    console.log('success');
                }
            }

            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
            expect(Company.createInstance).toHaveBeenCalled();
        });

        it('should test message handler with empty company records', async () => {
            // Test the message handler logic with empty records
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Mock parse function
            jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

            companyservice.getDynamicModel.mockResolvedValue(true);

            // Simulate the message handler logic
            const parsedMessage = require('flatted').parse('mock-empty-message-string');
            await companyservice.getDynamicModel(parsedMessage.inputData);

            for (const [i, element] of parsedMessage.companyRecords.entries()) {
                const row = bulkCompanyProcess.prepareRowData(element);
                await bulkCompanyProcess.processCompanyRow(
                    row,
                    parsedMessage.projectDetails,
                    parsedMessage.loginUser,
                    parsedMessage.ProjectId,
                    parsedMessage.inputData
                );

                if (i === parsedMessage.companyRecords.length - 1) {
                    console.log('success');
                }
            }

            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
            // No companies should be created for empty records
            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should test message handler event registration', () => {
            // Test that the message handler was registered
            expect(parentPort.on).toHaveBeenCalledWith('message', expect.any(Function));
        });

        it('should test messageHandler function with single record', async () => {
            // Test the messageHandler function directly
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [
                    ['header', 'Company1', 'DFOW1', 'Address1']
                ],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Mock parse function
            const { parse } = require('flatted');
            jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

            // Setup mocks for successful processing
            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue({ id: 1 });
            companyservice.getDynamicModel.mockResolvedValue(true);

            // Call the messageHandler function directly
            await bulkCompanyProcess.messageHandler('mock-message-string');

            expect(parse).toHaveBeenCalledWith('mock-message-string');
            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
            expect(Company.createInstance).toHaveBeenCalled();
            expect(parentPort.postMessage).toHaveBeenCalledWith('done');
        });

        it('should test messageHandler function with multiple records and log success', async () => {
            // Test the messageHandler function with multiple records to cover the success log
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [
                    ['header', 'Company1', 'DFOW1', 'Address1'],
                    ['header', 'Company2', 'DFOW2', 'Address2']
                ],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Mock parse function
            const { parse } = require('flatted');
            jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

            // Setup mocks for successful processing
            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue({ id: 1 });
            companyservice.getDynamicModel.mockResolvedValue(true);

            // Mock console.log to verify it's called
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            // Call the messageHandler function directly
            await bulkCompanyProcess.messageHandler('mock-message-string');

            expect(parse).toHaveBeenCalledWith('mock-message-string');
            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
            expect(Company.createInstance).toHaveBeenCalled();
            expect(consoleSpy).toHaveBeenCalledWith('success');
            expect(parentPort.postMessage).toHaveBeenCalledWith('done');

            consoleSpy.mockRestore();
        });

        it('should test messageHandler function with empty records array', async () => {
            // Test the messageHandler function with empty records
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Mock parse function
            const { parse } = require('flatted');
            jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

            companyservice.getDynamicModel.mockResolvedValue(true);

            // Call the messageHandler function directly
            await bulkCompanyProcess.messageHandler('mock-empty-message-string');

            expect(parse).toHaveBeenCalledWith('mock-empty-message-string');
            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
            expect(parentPort.postMessage).toHaveBeenCalledWith('done');
            // No companies should be created for empty records
            expect(Company.createInstance).not.toHaveBeenCalled();
        });

        it('should handle errors in messageHandler function', async () => {
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [
                    ['header', 'Company1', 'DFOW1', 'Address1']
                ],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Mock parse function
            const { parse } = require('flatted');
            jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

            // Mock getDynamicModel to throw an error
            const dynamicModelError = new Error('Dynamic model failed');
            companyservice.getDynamicModel.mockRejectedValue(dynamicModelError);

            // Call the messageHandler function and expect it to throw
            await expect(bulkCompanyProcess.messageHandler('mock-message-string'))
                .rejects.toThrow('Dynamic model failed');

            expect(parse).toHaveBeenCalledWith('mock-message-string');
            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
        });

        it('should handle parse errors in messageHandler function', async () => {
            // Mock parse function to throw an error
            const { parse } = require('flatted');
            const parseError = new Error('Parse failed');
            jest.spyOn(require('flatted'), 'parse').mockImplementation(() => {
                throw parseError;
            });

            // Call the messageHandler function and expect it to throw
            await expect(bulkCompanyProcess.messageHandler('invalid-message-string'))
                .rejects.toThrow('Parse failed');

            expect(parse).toHaveBeenCalledWith('invalid-message-string');
        });

        it('should test actual parentPort message event', async () => {
            // Test that the message event handler is properly registered
            const mockMessageData = {
                projectDetails: mockProjectDetails,
                loginUser: mockLoginUser,
                companyRecords: [
                    ['header', 'Company1', 'DFOW1', 'Address1']
                ],
                ProjectId: mockProjectId,
                inputData: mockInputData
            };

            // Mock parse function
            const { parse } = require('flatted');
            jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

            // Setup mocks for successful processing
            Member.getBy.mockResolvedValue({ id: 1 });
            Company.findOne.mockResolvedValue(null);
            Company.createInstance.mockResolvedValue({ id: 1 });
            companyservice.getDynamicModel.mockResolvedValue(true);

            // Get the registered message handler
            const messageHandlerCallback = parentPort.on.mock.calls.find(call => call[0] === 'message')[1];

            // Call the actual message handler
            await messageHandlerCallback('mock-message-string');

            expect(parse).toHaveBeenCalledWith('mock-message-string');
            expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
            expect(Company.createInstance).toHaveBeenCalled();
            expect(parentPort.postMessage).toHaveBeenCalledWith('done');
        });
    });

    describe('Additional Edge Cases and Error Scenarios', () => {
        describe('getNextCompanyAutoId edge cases', () => {
            it('should handle JSON.parse/JSON.stringify with complex object', async () => {
                const complexCompany = {
                    companyAutoId: 5,
                    nested: { data: 'test' },
                    array: [1, 2, 3],
                    date: new Date(),
                    nullValue: null
                };
                Company.findOne.mockResolvedValue(complexCompany);

                const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
                expect(result).toBe(6);
            });

            it('should handle negative companyAutoId', async () => {
                const mockLastCompany = { companyAutoId: -5 };
                Company.findOne.mockResolvedValue(mockLastCompany);

                const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
                expect(result).toBe(-4);
            });

            it('should handle very large companyAutoId', async () => {
                const mockLastCompany = { companyAutoId: Number.MAX_SAFE_INTEGER - 1 };
                Company.findOne.mockResolvedValue(mockLastCompany);

                const result = await bulkCompanyProcess.getNextCompanyAutoId(mockProjectId);
                expect(result).toBe(Number.MAX_SAFE_INTEGER);
            });
        });

        describe('prepareCompanyParams edge cases', () => {
            it('should handle boolean values in row data', () => {
                const mockRow = {
                    companyName: 'Test Company',
                    address_line_1: true,
                    address_line_2: false,
                    country: null,
                    state: undefined,
                    city: '',
                    website: 0,
                    zipcode: NaN,
                    additional_notes: []
                };

                const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);

                expect(result).toEqual({
                    companyName: 'Test Company',
                    address: null, // true becomes null due to || null
                    secondAddress: null, // false becomes null due to || null
                    country: null,
                    state: null,
                    city: null,
                    website: null, // 0 is falsy, so getWebsiteValue returns null
                    zipCode: null, // NaN becomes null due to || null
                    additional_notes: null, // [] is truthy but becomes null due to || null logic
                    ProjectId: mockProjectId,
                    isParent: false
                });
            });

            it('should handle website as array', () => {
                const mockRow = {
                    companyName: 'Test Company',
                    website: ['https://example.com']
                };

                const result = bulkCompanyProcess.prepareCompanyParams(mockRow, mockProjectId);
                expect(result.website).toBeUndefined(); // Arrays don't have .text property
            });
        });

        describe('createPublicCompanyIfNeeded edge cases', () => {
            it('should handle inputData with nested user object but no domainName', async () => {
                const companyParam = { companyName: 'Test Company' };
                const inputData = {
                    user: {
                        id: 1,
                        name: 'Test User'
                        // no domainName property
                    }
                };

                await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

                expect(companyService.createPublicCompany).not.toHaveBeenCalled();
            });

            it('should handle inputData with user as null', async () => {
                const companyParam = { companyName: 'Test Company' };
                const inputData = { user: null };

                await expect(bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData))
                    .rejects.toThrow();
            });

            it('should handle boolean domainName', async () => {
                const companyParam = { companyName: 'Test Company' };
                const inputData = { user: { domainName: false } };

                await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

                expect(companyService.createPublicCompany).not.toHaveBeenCalled();
            });

            it('should handle numeric domainName', async () => {
                const companyParam = { companyName: 'Test Company' };
                const inputData = { user: { domainName: 0 } };

                await bulkCompanyProcess.createPublicCompanyIfNeeded(companyParam, inputData);

                expect(companyService.createPublicCompany).not.toHaveBeenCalled();
            });
        });

        describe('processCompanyRow comprehensive edge cases', () => {
            it('should handle row with whitespace-only company name', async () => {
                const mockRow = { companyName: '   ', dfow: 'TEST-DFOW' };

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    mockProjectDetails,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                // Should not process because whitespace-only name is truthy but invalid
                expect(Company.createInstance).not.toHaveBeenCalled();
            });

            it('should handle row with zero as company name', async () => {
                const mockRow = { companyName: 0, dfow: 'TEST-DFOW' };

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    mockProjectDetails,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                // Should not process because 0 is falsy
                expect(Company.createInstance).not.toHaveBeenCalled();
            });

            it('should handle row with false as company name', async () => {
                const mockRow = { companyName: false, dfow: 'TEST-DFOW' };

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    mockProjectDetails,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                // Should not process because false is falsy
                expect(Company.createInstance).not.toHaveBeenCalled();
            });

            it('should process row with numeric company name', async () => {
                const mockRow = { companyName: 123, dfow: 'TEST-DFOW' };
                const mockMember = { id: 1 };
                const mockCompany = { id: 1 };
                const mockDFOW = { id: 1 };

                Member.getBy.mockResolvedValue(mockMember);
                Company.findOne.mockResolvedValue({ companyAutoId: 5 });
                Company.createInstance.mockResolvedValue(mockCompany);
                DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    mockProjectDetails,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                expect(Company.createInstance).toHaveBeenCalled();
                expect(companyService.createPublicCompany).toHaveBeenCalled();
                expect(CompanyDefine.createInstance).toHaveBeenCalled();
            });

            it('should handle undefined projectDetails', async () => {
                const mockRow = { companyName: 'Test Company' };

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    undefined,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                expect(Company.createInstance).not.toHaveBeenCalled();
            });

            it('should handle empty object as projectDetails', async () => {
                const mockRow = { companyName: 'Test Company' };

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    {},
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                // Empty object is truthy, so it should proceed
                const mockMember = { id: 1 };
                Member.getBy.mockResolvedValue(mockMember);
                Company.findOne.mockResolvedValue(null);
                Company.createInstance.mockResolvedValue({ id: 1 });

                await bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    {},
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                );

                expect(Member.getBy).toHaveBeenCalled();
            });

            it('should handle errors during createPublicCompanyIfNeeded', async () => {
                const mockRow = { companyName: 'Test Company' };
                const mockMember = { id: 1 };
                const mockCompany = { id: 1 };
                const publicCompanyError = new Error('Public company creation failed');

                Member.getBy.mockResolvedValue(mockMember);
                Company.findOne.mockResolvedValue(null);
                Company.createInstance.mockResolvedValue(mockCompany);
                companyService.createPublicCompany.mockRejectedValue(publicCompanyError);

                await expect(bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    mockProjectDetails,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                )).rejects.toThrow('Public company creation failed');
            });

            it('should handle errors during createCompanyDefinition', async () => {
                const mockRow = { companyName: 'Test Company', dfow: 'TEST-DFOW' };
                const mockMember = { id: 1 };
                const mockCompany = { id: 1 };
                const mockDFOW = { id: 1 };
                const definitionError = new Error('Company definition creation failed');

                Member.getBy.mockResolvedValue(mockMember);
                Company.findOne.mockResolvedValue(null);
                Company.createInstance.mockResolvedValue(mockCompany);
                DeliverDefineWork.findOne.mockResolvedValue(mockDFOW);
                CompanyDefine.createInstance.mockRejectedValue(definitionError);

                await expect(bulkCompanyProcess.processCompanyRow(
                    mockRow,
                    mockProjectDetails,
                    mockLoginUser,
                    mockProjectId,
                    mockInputData
                )).rejects.toThrow('Company definition creation failed');
            });
        });

        describe('prepareRowData comprehensive edge cases', () => {
            it('should handle array with null and undefined values', () => {
                const mockElement = ['header', 'Company1', null, undefined, '', 0, false, NaN, [], {}];

                const result = bulkCompanyProcess.prepareRowData(mockElement);

                expect(result).toEqual({
                    companyName: 'Company1',
                    dfow: null,
                    address_line_1: undefined,
                    address_line_2: '',
                    country: 0,
                    state: false,
                    city: NaN,
                    zipcode: [],
                    website: {},
                    additional_notes: undefined
                });
            });

            it('should handle very long array', () => {
                const mockElement = ['header', 'Company1', 'DFOW1', 'Addr1', 'Addr2', 'USA', 'CA', 'SF', '94105', 'website', 'notes', 'extra1', 'extra2', 'extra3'];

                const result = bulkCompanyProcess.prepareRowData(mockElement);

                expect(result).toEqual({
                    companyName: 'Company1',
                    dfow: 'DFOW1',
                    address_line_1: 'Addr1',
                    address_line_2: 'Addr2',
                    country: 'USA',
                    state: 'CA',
                    city: 'SF',
                    zipcode: '94105',
                    website: 'website',
                    additional_notes: 'notes'
                });
            });

            it('should not mutate the original array', () => {
                const mockElement = ['header', 'Company1', 'DFOW1'];
                const originalElement = [...mockElement];

                bulkCompanyProcess.prepareRowData(mockElement);

                expect(mockElement).toEqual(originalElement);
            });
        });

        describe('Integration test scenarios', () => {
            it('should handle complete workflow with all components', async () => {
                const mockMessageData = {
                    projectDetails: mockProjectDetails,
                    loginUser: mockLoginUser,
                    companyRecords: [
                        ['header', 'Company1', 'DFOW1', '123 Main St', 'Suite 100', 'USA', 'CA', 'SF', '94105', 'https://example.com', 'Notes'],
                        ['header', 'Company2', '', '456 Oak Ave', '', 'Canada', 'ON', 'Toronto', 'M5V 1A1', { text: 'https://example2.com' }, '']
                    ],
                    ProjectId: mockProjectId,
                    inputData: mockInputData
                };

                // Mock parse function
                const { parse } = require('flatted');
                jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

                // Setup mocks for successful processing
                Member.getBy.mockResolvedValue({ id: 1 });
                Company.findOne
                    .mockResolvedValueOnce({ companyAutoId: 10 })
                    .mockResolvedValueOnce({ companyAutoId: 11 });
                Company.createInstance.mockResolvedValue({ id: 1 });
                DeliverDefineWork.findOne.mockResolvedValue({ id: 1 });
                companyservice.getDynamicModel.mockResolvedValue(true);

                // Mock console.log to verify it's called
                const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

                // Call the messageHandler function
                await bulkCompanyProcess.messageHandler('mock-message-string');

                expect(parse).toHaveBeenCalledWith('mock-message-string');
                expect(companyservice.getDynamicModel).toHaveBeenCalledWith(mockInputData);
                expect(Company.createInstance).toHaveBeenCalledTimes(2);
                expect(companyService.createPublicCompany).toHaveBeenCalledTimes(2);
                expect(CompanyDefine.createInstance).toHaveBeenCalledTimes(1); // Only first company has DFOW
                expect(consoleSpy).toHaveBeenCalledWith('success');
                expect(parentPort.postMessage).toHaveBeenCalledWith('done');

                consoleSpy.mockRestore();
            });

            it('should handle mixed success and failure scenarios', async () => {
                const mockMessageData = {
                    projectDetails: mockProjectDetails,
                    loginUser: mockLoginUser,
                    companyRecords: [
                        ['header', 'Company1', 'DFOW1', 'Address1'], // Should succeed
                        ['header', '', 'DFOW2', 'Address2'], // Should be skipped (empty name)
                        ['header', 'Company3', 'DFOW3', 'Address3'] // Should succeed
                    ],
                    ProjectId: mockProjectId,
                    inputData: mockInputData
                };

                // Mock parse function
                jest.spyOn(require('flatted'), 'parse').mockReturnValue(mockMessageData);

                // Setup mocks
                Member.getBy.mockResolvedValue({ id: 1 });
                Company.findOne.mockResolvedValue({ companyAutoId: 5 });
                Company.createInstance.mockResolvedValue({ id: 1 });
                DeliverDefineWork.findOne.mockResolvedValue({ id: 1 });
                companyservice.getDynamicModel.mockResolvedValue(true);

                // Mock console.log to verify it's called
                const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

                await bulkCompanyProcess.messageHandler('mock-message-string');

                // Should only create 2 companies (skipping the one with empty name)
                expect(Company.createInstance).toHaveBeenCalledTimes(2);
                expect(consoleSpy).toHaveBeenCalledWith('success');
                expect(parentPort.postMessage).toHaveBeenCalledWith('done');

                consoleSpy.mockRestore();
            });
        });
    });
});