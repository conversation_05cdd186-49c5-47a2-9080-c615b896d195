const { Address } = require('../models');
const addressService = require('../addressService');

// Mock the models
jest.mock('../models', () => ({
    Address: {
        create: jest.fn(),
        findOne: jest.fn(),
        findAll: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
    },
}));

describe('Address Service', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('validateAddressData', () => {
        it('should return empty array for valid address data', () => {
            const validData = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
                ParentCompanyId: 1,
            };

            const errors = addressService.validateAddressData(validData);
            expect(errors).toEqual([]);
        });

        it('should return error for missing street', () => {
            const invalidData = {
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('Street address is required');
        });

        it('should return error for empty street', () => {
            const invalidData = {
                street: '   ',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('Street address is required');
        });

        it('should return error for missing city', () => {
            const invalidData = {
                street: '123 Main St',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('City is required');
        });

        it('should return error for missing state', () => {
            const invalidData = {
                street: '123 Main St',
                city: 'Test City',
                zipCode: '12345',
                country: 'Test Country',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('State is required');
        });

        it('should return error for missing zipCode', () => {
            const invalidData = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                country: 'Test Country',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('Zip code is required');
        });

        it('should return error for missing country', () => {
            const invalidData = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('Country is required');
        });

        it('should return error for invalid ParentCompanyId', () => {
            const invalidData = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
                ParentCompanyId: -1,
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('Valid Parent Company ID is required');
        });

        it('should return error for invalid zip code format', () => {
            const invalidData = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: 'invalid',
                country: 'Test Country',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors).toContain('Invalid zip code format');
        });

        it('should accept valid zip code formats', () => {
            const validData1 = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
            };

            const validData2 = {
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345-6789',
                country: 'Test Country',
            };

            expect(addressService.validateAddressData(validData1)).toEqual([]);
            expect(addressService.validateAddressData(validData2)).toEqual([]);
        });

        it('should return multiple errors for multiple invalid fields', () => {
            const invalidData = {
                street: '',
                city: '',
                state: '',
                zipCode: '',
                country: '',
            };

            const errors = addressService.validateAddressData(invalidData);
            expect(errors.length).toBeGreaterThan(1);
            expect(errors).toContain('Street address is required');
            expect(errors).toContain('City is required');
            expect(errors).toContain('State is required');
            expect(errors).toContain('Zip code is required');
            expect(errors).toContain('Country is required');
        });
    });

    describe('createAddress', () => {
        const mockAddressData = {
            street: '123 Main St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country',
            isDefault: true,
            ParentCompanyId: 1,
        };

        it('should successfully create a new address', async () => {
            const mockCreatedAddress = { id: 1, ...mockAddressData };
            Address.create.mockResolvedValue(mockCreatedAddress);

            const result = await addressService.createAddress(mockAddressData);

            expect(Address.create).toHaveBeenCalledWith(mockAddressData);
            expect(result).toEqual(mockCreatedAddress);
        });

        it('should successfully create address with extended zip code', async () => {
            const dataWithExtendedZip = { ...mockAddressData, zipCode: '12345-6789' };
            const mockCreatedAddress = { id: 1, ...dataWithExtendedZip };
            Address.create.mockResolvedValue(mockCreatedAddress);

            const result = await addressService.createAddress(dataWithExtendedZip);

            expect(Address.create).toHaveBeenCalledWith(dataWithExtendedZip);
            expect(result).toEqual(mockCreatedAddress);
        });

        it('should handle validation errors for missing street', async () => {
            const invalidData = { ...mockAddressData, street: '' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('Street address is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing city', async () => {
            const invalidData = { ...mockAddressData, city: '' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('City is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing state', async () => {
            const invalidData = { ...mockAddressData, state: '' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('State is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing zipCode', async () => {
            const invalidData = { ...mockAddressData, zipCode: '' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('Zip code is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing country', async () => {
            const invalidData = { ...mockAddressData, country: '' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('Country is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for invalid zip code format', async () => {
            const invalidData = { ...mockAddressData, zipCode: 'invalid-zip' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('Invalid zip code format');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for invalid ParentCompanyId', async () => {
            const invalidData = { ...mockAddressData, ParentCompanyId: -1 };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('Valid Parent Company ID is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle validation errors for whitespace-only fields', async () => {
            const invalidData = { ...mockAddressData, street: '   ' };

            await expect(addressService.createAddress(invalidData))
                .rejects
                .toThrow('Street address is required');

            expect(Address.create).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            Address.create.mockRejectedValue(new Error('Database error'));

            await expect(addressService.createAddress(mockAddressData))
                .rejects
                .toThrow('Failed to create address');
        });

        it('should handle database constraint errors', async () => {
            Address.create.mockRejectedValue(new Error('Unique constraint violation'));

            await expect(addressService.createAddress(mockAddressData))
                .rejects
                .toThrow('Failed to create address');
        });

        it('should create address without ParentCompanyId', async () => {
            const dataWithoutCompany = { ...mockAddressData };
            delete dataWithoutCompany.ParentCompanyId;
            const mockCreatedAddress = { id: 1, ...dataWithoutCompany };
            Address.create.mockResolvedValue(mockCreatedAddress);

            const result = await addressService.createAddress(dataWithoutCompany);

            expect(Address.create).toHaveBeenCalledWith(dataWithoutCompany);
            expect(result).toEqual(mockCreatedAddress);
        });
    });

    describe('getAddress', () => {
        const mockAddress = {
            id: 1,
            street: '123 Main St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country',
            isDefault: true,
            ParentCompanyId: 1,
        };

        it('should successfully retrieve an address by ID', async () => {
            Address.findOne.mockResolvedValue(mockAddress);

            const result = await addressService.getAddress(1);

            expect(Address.findOne).toHaveBeenCalledWith({
                where: { id: 1, isDeleted: false }
            });
            expect(result).toEqual(mockAddress);
        });

        it('should return null for non-existent address', async () => {
            Address.findOne.mockResolvedValue(null);

            const result = await addressService.getAddress(999);

            expect(result).toBeNull();
        });

        it('should handle invalid address ID - null', async () => {
            await expect(addressService.getAddress(null))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.findOne).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - undefined', async () => {
            await expect(addressService.getAddress(undefined))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.findOne).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - zero', async () => {
            await expect(addressService.getAddress(0))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.findOne).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - negative number', async () => {
            await expect(addressService.getAddress(-1))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.findOne).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - non-integer', async () => {
            await expect(addressService.getAddress(1.5))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.findOne).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - string', async () => {
            await expect(addressService.getAddress('invalid'))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.findOne).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            Address.findOne.mockRejectedValue(new Error('Database error'));

            await expect(addressService.getAddress(1))
                .rejects
                .toThrow('Failed to retrieve address');
        });

        it('should handle database connection errors', async () => {
            Address.findOne.mockRejectedValue(new Error('Connection timeout'));

            await expect(addressService.getAddress(1))
                .rejects
                .toThrow('Failed to retrieve address');
        });
    });

    describe('updateAddress', () => {
        const mockAddressData = {
            street: '456 New St',
            city: 'New City',
            state: 'New State',
            zipCode: '54321',
            country: 'New Country',
        };

        it('should successfully update an address', async () => {
            const mockUpdatedAddress = { id: 1, ...mockAddressData };
            Address.update.mockResolvedValue([1]);
            Address.findOne.mockResolvedValue(mockUpdatedAddress);

            const result = await addressService.updateAddress(1, mockAddressData);

            expect(Address.update).toHaveBeenCalledWith(
                mockAddressData,
                { where: { id: 1, isDeleted: false } }
            );
            expect(result).toEqual(mockUpdatedAddress);
        });

        it('should successfully update address with extended zip code', async () => {
            const dataWithExtendedZip = { ...mockAddressData, zipCode: '54321-9876' };
            const mockUpdatedAddress = { id: 1, ...dataWithExtendedZip };
            Address.update.mockResolvedValue([1]);
            Address.findOne.mockResolvedValue(mockUpdatedAddress);

            const result = await addressService.updateAddress(1, dataWithExtendedZip);

            expect(Address.update).toHaveBeenCalledWith(
                dataWithExtendedZip,
                { where: { id: 1, isDeleted: false } }
            );
            expect(result).toEqual(mockUpdatedAddress);
        });

        it('should handle non-existent address', async () => {
            Address.update.mockResolvedValue([0]);

            await expect(addressService.updateAddress(999, mockAddressData))
                .rejects
                .toThrow('Address not found');
        });

        it('should handle invalid address ID - null', async () => {
            await expect(addressService.updateAddress(null, mockAddressData))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - zero', async () => {
            await expect(addressService.updateAddress(0, mockAddressData))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - negative', async () => {
            await expect(addressService.updateAddress(-1, mockAddressData))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - non-integer', async () => {
            await expect(addressService.updateAddress(1.5, mockAddressData))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing street', async () => {
            const invalidData = { ...mockAddressData, street: '' };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('Street address is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing city', async () => {
            const invalidData = { ...mockAddressData, city: '' };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('City is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing state', async () => {
            const invalidData = { ...mockAddressData, state: '' };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('State is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing zipCode', async () => {
            const invalidData = { ...mockAddressData, zipCode: '' };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('Zip code is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for missing country', async () => {
            const invalidData = { ...mockAddressData, country: '' };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('Country is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for invalid zip code format', async () => {
            const invalidData = { ...mockAddressData, zipCode: 'invalid-zip' };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('Invalid zip code format');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle validation errors for invalid ParentCompanyId', async () => {
            const invalidData = { ...mockAddressData, ParentCompanyId: -1 };

            await expect(addressService.updateAddress(1, invalidData))
                .rejects
                .toThrow('Valid Parent Company ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle database errors during update', async () => {
            Address.update.mockRejectedValue(new Error('Database error'));

            await expect(addressService.updateAddress(1, mockAddressData))
                .rejects
                .toThrow('Failed to update address');
        });

        it('should handle database errors during findOne after update', async () => {
            Address.update.mockResolvedValue([1]);
            Address.findOne.mockRejectedValue(new Error('Database error'));

            await expect(addressService.updateAddress(1, mockAddressData))
                .rejects
                .toThrow('Failed to update address');
        });
    });

    describe('deleteAddress', () => {
        it('should successfully soft delete an address', async () => {
            Address.update.mockResolvedValue([1]);

            const result = await addressService.deleteAddress(1);

            expect(Address.update).toHaveBeenCalledWith(
                { isDeleted: true },
                { where: { id: 1, isDeleted: false } }
            );
            expect(result).toBe(true);
        });

        it('should handle non-existent address', async () => {
            Address.update.mockResolvedValue([0]);

            await expect(addressService.deleteAddress(999))
                .rejects
                .toThrow('Address not found');
        });

        it('should handle invalid address ID - null', async () => {
            await expect(addressService.deleteAddress(null))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - undefined', async () => {
            await expect(addressService.deleteAddress(undefined))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - zero', async () => {
            await expect(addressService.deleteAddress(0))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - negative number', async () => {
            await expect(addressService.deleteAddress(-1))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - non-integer', async () => {
            await expect(addressService.deleteAddress(1.5))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - string', async () => {
            await expect(addressService.deleteAddress('invalid'))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            Address.update.mockRejectedValue(new Error('Database error'));

            await expect(addressService.deleteAddress(1))
                .rejects
                .toThrow('Failed to delete address');
        });

        it('should handle database constraint errors', async () => {
            Address.update.mockRejectedValue(new Error('Foreign key constraint'));

            await expect(addressService.deleteAddress(1))
                .rejects
                .toThrow('Failed to delete address');
        });

        it('should handle database connection timeout', async () => {
            Address.update.mockRejectedValue(new Error('Connection timeout'));

            await expect(addressService.deleteAddress(1))
                .rejects
                .toThrow('Failed to delete address');
        });
    });

    describe('getCompanyAddresses', () => {
        const mockAddresses = [
            {
                id: 1,
                street: '123 Main St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
                isDefault: true,
                ParentCompanyId: 1,
            },
            {
                id: 2,
                street: '456 Second St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'Test Country',
                isDefault: false,
                ParentCompanyId: 1,
            },
        ];

        it('should successfully retrieve all addresses for a company', async () => {
            Address.findAll.mockResolvedValue(mockAddresses);

            const result = await addressService.getCompanyAddresses(1);

            expect(Address.findAll).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, isDeleted: false },
                order: [['isDefault', 'DESC'], ['createdAt', 'DESC']]
            });
            expect(result).toEqual(mockAddresses);
        });

        it('should return empty array for company with no addresses', async () => {
            Address.findAll.mockResolvedValue([]);

            const result = await addressService.getCompanyAddresses(999);

            expect(result).toEqual([]);
        });

        it('should handle invalid company ID - null', async () => {
            await expect(addressService.getCompanyAddresses(null))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.findAll).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - undefined', async () => {
            await expect(addressService.getCompanyAddresses(undefined))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.findAll).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - zero', async () => {
            await expect(addressService.getCompanyAddresses(0))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.findAll).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - negative number', async () => {
            await expect(addressService.getCompanyAddresses(-1))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.findAll).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - non-integer', async () => {
            await expect(addressService.getCompanyAddresses(1.5))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.findAll).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - string', async () => {
            await expect(addressService.getCompanyAddresses('invalid'))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.findAll).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            Address.findAll.mockRejectedValue(new Error('Database error'));

            await expect(addressService.getCompanyAddresses(1))
                .rejects
                .toThrow('Failed to retrieve company addresses');
        });

        it('should handle database connection timeout', async () => {
            Address.findAll.mockRejectedValue(new Error('Connection timeout'));

            await expect(addressService.getCompanyAddresses(1))
                .rejects
                .toThrow('Failed to retrieve company addresses');
        });

        it('should handle database query timeout', async () => {
            Address.findAll.mockRejectedValue(new Error('Query timeout'));

            await expect(addressService.getCompanyAddresses(1))
                .rejects
                .toThrow('Failed to retrieve company addresses');
        });

        it('should return single address for company', async () => {
            const singleAddress = [mockAddresses[0]];
            Address.findAll.mockResolvedValue(singleAddress);

            const result = await addressService.getCompanyAddresses(1);

            expect(result).toEqual(singleAddress);
            expect(result).toHaveLength(1);
        });

        it('should return multiple addresses ordered correctly', async () => {
            Address.findAll.mockResolvedValue(mockAddresses);

            const result = await addressService.getCompanyAddresses(1);

            expect(Address.findAll).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, isDeleted: false },
                order: [['isDefault', 'DESC'], ['createdAt', 'DESC']]
            });
            expect(result).toEqual(mockAddresses);
        });
    });

    describe('setDefaultAddress', () => {
        it('should successfully set an address as default', async () => {
            Address.update.mockResolvedValueOnce([2]); // First call to unset all defaults
            Address.update.mockResolvedValueOnce([1]); // Second call to set new default

            const result = await addressService.setDefaultAddress(1, 1);

            expect(Address.update).toHaveBeenCalledTimes(2);
            expect(Address.update).toHaveBeenNthCalledWith(1,
                { isDefault: false },
                { where: { ParentCompanyId: 1, isDeleted: false } }
            );
            expect(Address.update).toHaveBeenNthCalledWith(2,
                { isDefault: true },
                { where: { id: 1, ParentCompanyId: 1, isDeleted: false } }
            );
            expect(result).toBe(true);
        });

        it('should handle non-existent address', async () => {
            Address.update.mockResolvedValueOnce([2]); // First call to unset all defaults
            Address.update.mockResolvedValueOnce([0]); // Second call fails - address not found

            await expect(addressService.setDefaultAddress(999, 1))
                .rejects
                .toThrow('Address not found or does not belong to the company');
        });

        it('should handle address that does not belong to company', async () => {
            Address.update.mockResolvedValueOnce([2]); // First call to unset all defaults
            Address.update.mockResolvedValueOnce([0]); // Second call fails - wrong company

            await expect(addressService.setDefaultAddress(1, 999))
                .rejects
                .toThrow('Address not found or does not belong to the company');
        });

        it('should handle invalid address ID - null', async () => {
            await expect(addressService.setDefaultAddress(null, 1))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - zero', async () => {
            await expect(addressService.setDefaultAddress(0, 1))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - negative', async () => {
            await expect(addressService.setDefaultAddress(-1, 1))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid address ID - non-integer', async () => {
            await expect(addressService.setDefaultAddress(1.5, 1))
                .rejects
                .toThrow('Valid address ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - null', async () => {
            await expect(addressService.setDefaultAddress(1, null))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - zero', async () => {
            await expect(addressService.setDefaultAddress(1, 0))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - negative', async () => {
            await expect(addressService.setDefaultAddress(1, -1))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle invalid company ID - non-integer', async () => {
            await expect(addressService.setDefaultAddress(1, 1.5))
                .rejects
                .toThrow('Valid company ID is required');

            expect(Address.update).not.toHaveBeenCalled();
        });

        it('should handle database errors during first update', async () => {
            Address.update.mockRejectedValueOnce(new Error('Database error'));

            await expect(addressService.setDefaultAddress(1, 1))
                .rejects
                .toThrow('Failed to set default address');
        });

        it('should handle database errors during second update', async () => {
            Address.update.mockResolvedValueOnce([2]); // First call succeeds
            Address.update.mockRejectedValueOnce(new Error('Database error')); // Second call fails

            await expect(addressService.setDefaultAddress(1, 1))
                .rejects
                .toThrow('Failed to set default address');
        });

        it('should handle case where no addresses exist for company', async () => {
            Address.update.mockResolvedValueOnce([0]); // No addresses to unset
            Address.update.mockResolvedValueOnce([0]); // Address not found

            await expect(addressService.setDefaultAddress(1, 1))
                .rejects
                .toThrow('Address not found or does not belong to the company');
        });

        it('should handle database connection timeout', async () => {
            Address.update.mockRejectedValueOnce(new Error('Connection timeout'));

            await expect(addressService.setDefaultAddress(1, 1))
                .rejects
                .toThrow('Failed to set default address');
        });
    });
});