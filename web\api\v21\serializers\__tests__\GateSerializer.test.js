const GateSerializer = require('../GateSerializer');

describe('GateSerializer', () => {
  it('should serialize all fields correctly', () => {
    const equipment = {
      id: 1,
      equipmentName: 'Gate 1',
      createdBy: 2,
      controlledBy: 3,
      projectId: 4,
      createdAt: '2024-01-01T00:00:00Z',
    };
    const result = GateSerializer.serialize(equipment);
    expect(result).toEqual(equipment);
  });

  it('should serialize with missing fields as undefined', () => {
    const equipment = {
      id: 1,
      equipmentName: 'Gate 1',
    };
    const result = GateSerializer.serialize(equipment);
    expect(result).toEqual({
      id: 1,
      equipmentName: 'Gate 1',
      createdBy: undefined,
      controlledBy: undefined,
      projectId: undefined,
      createdAt: undefined,
    });
  });
});
