const notificationService = require('../notificationService');
const helper = require('../helpers/domainHelper');

// Mock all required dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      and: Symbol('and'),
      or: Symbol('or'),
      not: Symbol('not'),
      in: Symbol('in'),
      between: Symbol('between'),
      iLike: Symbol('iLike'),
      ne: Symbol('ne'),
    },
    col: jest.fn(),
    and: jest.fn((conditions) => ({ [Symbol('and')]: conditions })),
  },
  Enterprise: {
    findOne: jest.fn(),
    findAll: jest.fn(),
  },
  VersionUpdates: {
    findOne: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock('../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
  extractDomainFromEmail: jest.fn(),
}));

jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return jest.fn((date) => {
    if (date === 'invalid-date') {
      return actualMoment('2023-01-01');
    }
    return actualMoment(date);
  });
});

describe('NotificationService', () => {
  let mockModels, mockNotification, mockMember, mockDeliveryPersonNotification, mockUser;

  beforeEach(() => {
    jest.clearAllMocks();

    mockNotification = {
      findByPk: jest.fn(),
      getAll: jest.fn(),
      getUnSeenCount: jest.fn(),
    };

    mockMember = {
      findOne: jest.fn(),
      findAll: jest.fn(),
    };

    mockDeliveryPersonNotification = {
      findOne: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      getUnSeenCount: jest.fn(),
    };

    mockUser = {
      findOne: jest.fn(),
    };

    mockModels = {
      Notification: mockNotification,
      Member: mockMember,
      DeliveryPersonNotification: mockDeliveryPersonNotification,
      User: mockUser,
    };

    helper.getDynamicModel.mockResolvedValue(mockModels);
    helper.returnProjectModel.mockResolvedValue(mockModels);
    helper.extractDomainFromEmail.mockReturnValue('test.com');
  });

  describe('buildBaseCondition', () => {
    it('should build condition with dateFilter', async () => {
      const incomeData = { dateFilter: '2023-01-01T00:00:00Z' };
      const result = await notificationService.buildBaseCondition(incomeData);
      expect(result.createdAt).toBeDefined();
    });

    it('should build condition without dateFilter', async () => {
      const incomeData = {};
      const result = await notificationService.buildBaseCondition(incomeData);
      expect(result.createdAt).toBeUndefined();
    });

    it('should handle invalid dateFilter', async () => {
      const incomeData = { dateFilter: 'invalid-date' };
      const result = await notificationService.buildBaseCondition(incomeData);
      expect(result.createdAt).toBeDefined();
    });
  });

  describe('listNotification', () => {
    it('should return error when member does not exist', async () => {
      const mockInputData = {
        body: {},
        params: { pageNo: 1, pageSize: 10 },
        user: { id: 1, email: '<EMAIL>' },
      };

      mockMember.findAll.mockResolvedValue([]);
      const done = jest.fn();
      await notificationService.listNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, { message: 'Member Does not exist.' });
    });

    it('should successfully list notifications', async () => {
      const mockInputData = {
        body: {},
        params: { pageNo: 1, pageSize: 10 },
        user: { id: 1, email: '<EMAIL>' },
      };

      const mockMemberData = [{ id: 1, RoleId: 3 }];
      const mockNotifications = { rows: [{ id: 1 }] };

      mockMember.findAll.mockResolvedValue(mockMemberData);
      mockNotification.getAll.mockResolvedValue(mockNotifications);
      mockNotification.getUnSeenCount.mockResolvedValue(5);

      const done = jest.fn();
      await notificationService.listNotification(mockInputData, done);

      expect(done).toHaveBeenCalledWith(
        expect.objectContaining({
          count: 1,
          rows: expect.any(Array),
          unSeenCount: 5,
        }),
        false,
      );
    });

    it('should handle database errors', async () => {
      const mockInputData = {
        body: {},
        params: { pageNo: 1, pageSize: 10 },
        user: { id: 1, email: '<EMAIL>' },
      };

      mockMember.findAll.mockRejectedValue(new Error('Database error'));
      const done = jest.fn();
      await notificationService.listNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle getLimitedData error', async () => {
      const mockInputData = {
        body: {},
        params: { pageNo: 1, pageSize: 10 },
        user: { id: 1, email: '<EMAIL>' },
      };

      const mockMemberData = [{ id: 1, RoleId: 3 }];
      const mockNotifications = { rows: [{ id: 1 }] };

      mockMember.findAll.mockResolvedValue(mockMemberData);
      mockNotification.getAll.mockResolvedValue(mockNotifications);
      mockNotification.getUnSeenCount.mockResolvedValue(0);

      // Mock getLimitedData to call done with error
      const originalGetLimitedData = notificationService.getLimitedData;
      notificationService.getLimitedData = jest.fn(
        (_result, _offset, _limit, _count, _finalResult, done) => {
          done(null, new Error('Pagination error'));
        },
      );

      const done = jest.fn();
      await notificationService.listNotification(mockInputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });

      // Restore original function
      notificationService.getLimitedData = originalGetLimitedData;
    });
  });

  describe('getNotificationCount', () => {
    it('should return error when member does not exist', async () => {
      const mockInputData = {
        query: { ProjectId: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      mockMember.findAll.mockResolvedValue([]);
      const done = jest.fn();
      await notificationService.getNotificationCount(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, { message: 'Member Does not exist.' });
    });

    it('should successfully get notification count', async () => {
      const mockInputData = {
        query: { ProjectId: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      const mockMemberData = [{ id: 1, RoleId: 3 }];
      mockMember.findAll.mockResolvedValue(mockMemberData);
      mockDeliveryPersonNotification.getUnSeenCount.mockResolvedValue([1, 2, 3]);

      const done = jest.fn();
      await notificationService.getNotificationCount(mockInputData, done);
      expect(done).toHaveBeenCalledWith(3, false);
    });

    it('should handle null memberDetails', async () => {
      const mockInputData = {
        query: { ProjectId: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      mockMember.findAll.mockResolvedValue(null);
      const done = jest.fn();
      await notificationService.getNotificationCount(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, { message: 'Member Does not exist.' });
    });

    it('should handle database errors', async () => {
      const mockInputData = {
        query: { ProjectId: 1 },
        user: { id: 1, email: '<EMAIL>' },
      };

      mockMember.findAll.mockRejectedValue(new Error('Database error'));
      const done = jest.fn();
      await notificationService.getNotificationCount(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('setReadNotification', () => {
    it('should successfully update notification as read', async () => {
      const mockInputData = {
        query: { id: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      mockDeliveryPersonNotification.update.mockResolvedValue([1]);
      const done = jest.fn();
      await notificationService.setReadNotification(mockInputData, done);

      expect(mockDeliveryPersonNotification.update).toHaveBeenCalledWith(
        { seen: true },
        { where: { id: 1 } }
      );
      expect(done).toHaveBeenCalledWith([1], false);
    });

    it('should handle database errors', async () => {
      const mockInputData = {
        query: { id: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      mockDeliveryPersonNotification.update.mockRejectedValue(new Error('Update error'));
      const done = jest.fn();
      await notificationService.setReadNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('setReadAllNotification', () => {
    it('should successfully update all notifications as read', async () => {
      const mockInputData = {
        ProjectId: 1,
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      const mockNotifications = [{ id: 1 }, { id: 2 }];
      mockDeliveryPersonNotification.findAll.mockResolvedValue(mockNotifications);

      const done = jest.fn();
      await notificationService.setReadAllNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(mockNotifications, false);
    });

    it('should handle missing ProjectId', async () => {
      const mockInputData = {
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      const done = jest.fn();
      await notificationService.setReadAllNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(undefined, false);
    });

    it('should handle database errors', async () => {
      const mockInputData = {
        ProjectId: 1,
        user: { id: 1, email: '<EMAIL>' },
      };

      mockDeliveryPersonNotification.findAll.mockRejectedValue(new Error('Database error'));
      const done = jest.fn();
      await notificationService.setReadAllNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('deleteNotification', () => {
    it('should return error when notification does not exist', async () => {
      const mockInputData = {
        query: { id: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      mockNotification.findByPk.mockResolvedValue(null);
      const done = jest.fn();
      await notificationService.deleteNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, { message: 'Notification Does not Exist.' });
    });

    it('should successfully delete notification', async () => {
      const mockInputData = {
        query: { id: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      const mockNotificationData = { id: 1, ProjectId: 1 };
      const mockMemberData = { id: 1 };
      const mockDeliveryNotification = { destroy: jest.fn().mockResolvedValue(true) };

      mockNotification.findByPk.mockResolvedValue(mockNotificationData);
      mockMember.findOne.mockResolvedValue(mockMemberData);
      mockDeliveryPersonNotification.findOne.mockResolvedValue(mockDeliveryNotification);

      const done = jest.fn();
      await notificationService.deleteNotification(mockInputData, done);

      expect(mockNotification.findByPk).toHaveBeenCalledWith(1);
      expect(mockMember.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, ProjectId: 1, isDeleted: false },
      });
      expect(mockDeliveryPersonNotification.findOne).toHaveBeenCalledWith({
        where: { NotificationId: 1, MemberId: 1 },
      });
      expect(mockDeliveryNotification.destroy).toHaveBeenCalled();
      expect(done).toHaveBeenCalledWith(true, false);
    });

    it('should handle database errors', async () => {
      const mockInputData = {
        query: { id: 1 },
        user: { id: 1, email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
      };

      mockNotification.findByPk.mockRejectedValue(new Error('Database error'));
      const done = jest.fn();
      await notificationService.deleteNotification(mockInputData, done);
      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('getLimitedData', () => {
    it('should return limited data within limit', async () => {
      const result = [
        { id: 1, title: 'Test 1' },
        { id: 2, title: 'Test 2' },
        { id: 3, title: 'Test 3' },
      ];
      const done = jest.fn();

      await notificationService.getLimitedData(result, 0, 2, 0, [], done);

      expect(done).toHaveBeenCalledWith(
        [
          { id: 1, title: 'Test 1' },
          { id: 2, title: 'Test 2' },
        ],
        false,
      );
    });

    it('should return all data when limit exceeds array length', async () => {
      const result = [{ id: 1, title: 'Test 1' }];
      const done = jest.fn();

      await notificationService.getLimitedData(result, 0, 5, 0, [], done);

      expect(done).toHaveBeenCalledWith([{ id: 1, title: 'Test 1' }], false);
    });

    it('should return empty array when starting index exceeds array length', async () => {
      const result = [{ id: 1, title: 'Test 1' }];
      const done = jest.fn();

      await notificationService.getLimitedData(result, 5, 2, 0, [], done);

      expect(done).toHaveBeenCalledWith([], false);
    });

    it('should handle when count equals limit', async () => {
      const result = [{ id: 1, title: 'Test 1' }];
      const done = jest.fn();

      await notificationService.getLimitedData(result, 0, 1, 1, [{ id: 0, title: 'Existing' }], done);

      expect(done).toHaveBeenCalledWith([{ id: 0, title: 'Existing' }], false);
    });
  });

  describe('Utility Functions', () => {
    it('should test buildSubCondition with different RoleIds', () => {
      const memberDetails1 = [{ RoleId: 3 }];
      const memberDetails2 = [{ RoleId: 4 }];
      const memberDetails3 = [{ RoleId: 2 }];
      const memberDelivery = [1, 2, 3];

      const result1 = notificationService.buildSubCondition(memberDetails1, memberDelivery);
      const result2 = notificationService.buildSubCondition(memberDetails2, memberDelivery);
      const result3 = notificationService.buildSubCondition(memberDetails3, memberDelivery);

      expect(result1.MemberId).toBeDefined();
      expect(result2.MemberId).toBeDefined();
      expect(result3.MemberId).toBeUndefined();
    });

    it('should test populateMemberDelivery', () => {
      const members = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const delivery = [];
      notificationService.populateMemberDelivery(members, delivery);
      expect(delivery).toEqual([1, 2, 3]);
    });

    it('should test buildCondition', () => {
      const inputData1 = { query: { ProjectId: 1 } };
      const inputData2 = { query: { ProjectId: 'undefined' } };
      const inputData3 = { query: {} };

      const condition1 = notificationService.buildCondition(inputData1);
      const condition2 = notificationService.buildCondition(inputData2);
      const condition3 = notificationService.buildCondition(inputData3);

      expect(condition1.ProjectId).toBe(1);
      expect(condition2.ProjectId).toBeUndefined();
      expect(condition3.ProjectId).toBeUndefined();
    });

    it('should test findEnterpriseByDomain', async () => {
      const { Enterprise } = require('../../models');
      const mockEnterprise = { id: 1, name: 'test.com' };
      Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await notificationService.findEnterpriseByDomain('test.com');
      expect(result).toEqual(mockEnterprise);

      const emptyResult = await notificationService.findEnterpriseByDomain('');
      expect(emptyResult).toBeNull();
    });

    it('should test findEnterpriseByParentCompanyId', async () => {
      const { Enterprise } = require('../../models');
      const mockEnterprise = { id: 1, name: 'test.com' };
      Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await notificationService.findEnterpriseByParentCompanyId(1);
      expect(result).toEqual(mockEnterprise);

      const undefinedResult = await notificationService.findEnterpriseByParentCompanyId('undefined');
      expect(undefinedResult).toBeNull();
    });

    it('should test getUnseenCountForMembers', async () => {
      const memberDetails = { RoleId: 3 };
      const subCondition = { isDeleted: false };
      const memberDelivery = [1, 2, 3];
      const condition = { seen: false };
      const loginUser = { id: 1 };

      mockDeliveryPersonNotification.getUnSeenCount.mockResolvedValue([1, 2, 3]);
      const result = await notificationService.getUnseenCountForMembers(
        memberDetails, subCondition, memberDelivery, condition, loginUser
      );
      expect(result).toBe(3);
    });

    it('should test getDynamicModel', async () => {
      const mockInputData = {
        user: { email: '<EMAIL>', domainName: 'test.com' },
        body: { ParentCompanyId: 1 }
      };

      const result = await notificationService.getDynamicModel(mockInputData);
      expect(result).toBe(1);
      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should test getVersion function', async () => {
      const { VersionUpdates } = require('../../models');
      const mockVersionData = { version: '1.0.0', releasenote: '{"notes": "test"}', iosversion: '1.0.0' };
      VersionUpdates.findOne.mockResolvedValue(mockVersionData);

      const result = await notificationService.getVersion();
      expect(result.status).toBe(200);
      expect(result.data.version).toBe('1.0.0');
    });

    it('should handle getVersion with null version data', async () => {
      const { VersionUpdates } = require('../../models');
      VersionUpdates.findOne.mockResolvedValue(null);

      const result = await notificationService.getVersion();
      expect(result).toBeUndefined();
    });

    it('should handle getVersion with invalid JSON', async () => {
      const { VersionUpdates } = require('../../models');
      const mockVersionData = { version: '1.0.0', releasenote: 'invalid-json', iosversion: '1.0.0' };
      VersionUpdates.findOne.mockResolvedValue(mockVersionData);

      const result = await notificationService.getVersion();
      expect(result).toBeUndefined();
    });

    it('should handle getVersion database errors', async () => {
      const { VersionUpdates } = require('../../models');
      VersionUpdates.findOne.mockRejectedValue(new Error('Database error'));

      const result = await notificationService.getVersion();
      expect(result).toBeUndefined();
    });

    it('should test versionUpdation function', async () => {
      const { VersionUpdates } = require('../../models');
      const mockInputData = {
        body: {
          version: '2.0.0',
          iosversion: '2.0.0',
          releasenote: { notes: 'New version' }
        }
      };

      VersionUpdates.update.mockResolvedValue([1]);

      const result = await notificationService.versionUpdation(mockInputData);
      expect(result.status).toBe(200);
      expect(result.message).toBe('version updated successfully.');
    });

    it('should handle versionUpdation failure', async () => {
      const { VersionUpdates } = require('../../models');
      const mockInputData = {
        body: {
          version: '2.0.0',
          iosversion: '2.0.0',
          releasenote: { notes: 'New version' }
        }
      };

      VersionUpdates.update.mockResolvedValue(null);

      const result = await notificationService.versionUpdation(mockInputData);
      expect(result.status).toBe(500);
      expect(result.message).toBe('cannot update the version');
    });

    it('should handle versionUpdation database errors', async () => {
      const { VersionUpdates } = require('../../models');
      const mockInputData = {
        body: {
          version: '2.0.0',
          iosversion: '2.0.0',
          releasenote: { notes: 'New version' }
        }
      };

      VersionUpdates.update.mockRejectedValue(new Error('Database error'));

      const result = await notificationService.versionUpdation(mockInputData);
      expect(result).toBeUndefined();
    });

    it('should test buildSearchCondition with search term', async () => {
      const searchTerm = 'test';
      const result = await notificationService.buildSearchCondition(searchTerm);
      expect(result[require('../../models').Sequelize.Op.and]).toBeDefined();
    });

    it('should test buildSearchCondition with empty search term', async () => {
      const result = await notificationService.buildSearchCondition('');
      expect(result).toEqual({});
    });

    it('should test buildSearchCondition with null search term', async () => {
      const result = await notificationService.buildSearchCondition(null);
      expect(result).toEqual({});
    });

    it('should test buildBaseCondition with all filters', async () => {
      const incomeData = {
        ProjectId: 1,
        descriptionFilter: 'test description',
        dateFilter: '2023-01-01T00:00:00Z',
        statusFilter: 'active',
        projectNameFilter: 2
      };
      const result = await notificationService.buildBaseCondition(incomeData);
      expect(result.ProjectId).toBe(2); // projectNameFilter overrides ProjectId
      expect(result.description).toBeDefined();
      expect(result.createdAt).toBeDefined();
      expect(result.type).toBe('active');
    });

    it('should test findUserByEmail', async () => {
      const mockUser = { id: 1, email: '<EMAIL>' };
      mockUser.findOne = jest.fn().mockResolvedValue(mockUser);

      // Set the publicUser
      await notificationService.returnProjectModel();

      const result = await notificationService.findUserByEmail('<EMAIL>');
      expect(result).toEqual(mockUser);
    });

    it('should test findUserByEmail with empty email', async () => {
      const result = await notificationService.findUserByEmail('');
      expect(result).toBeNull();
    });

    it('should test findMemberByUserId', async () => {
      const mockMember = { id: 1, UserId: 1 };
      mockMember.findOne = jest.fn().mockResolvedValue(mockMember);

      // Set the publicMember
      await notificationService.returnProjectModel();

      const result = await notificationService.findMemberByUserId(1);
      expect(result).toEqual(mockMember);
    });

    it('should test findMemberByUserId with empty userId', async () => {
      const result = await notificationService.findMemberByUserId(null);
      expect(result).toBeNull();
    });

    it('should test getDomainFromUserData', async () => {
      const mockInputData = {
        user: { domainName: 'test.com', email: '<EMAIL>' },
        body: { ParentCompanyId: 1 }
      };

      const result = await notificationService.getDomainFromUserData(mockInputData);
      expect(result).toBe('test.com');
    });

    it('should test extractParentCompanyId from different sources', () => {
      const inputData1 = { body: { ParentCompanyId: 1 } };
      const inputData2 = { params: { ParentCompanyId: 2 } };
      const inputData3 = { query: { ParentCompanyId: 3 } };

      expect(notificationService.extractParentCompanyId(inputData1)).toBe(1);
      expect(notificationService.extractParentCompanyId(inputData2)).toBe(2);
      expect(notificationService.extractParentCompanyId(inputData3)).toBe(3);
    });

    it('should test updateUserWithDomain', async () => {
      const mockInputData = {
        user: { email: '<EMAIL>' }
      };
      const mockNewUser = { id: 1, email: '<EMAIL>', domain: 'test.com' };

      mockUser.findOne.mockResolvedValue(mockNewUser);

      const result = await notificationService.updateUserWithDomain(mockInputData, 'test.com');
      expect(result).toEqual(mockNewUser);
    });

    it('should test updateUserWithDomain when user not found', async () => {
      const mockInputData = {
        user: { email: '<EMAIL>' }
      };

      mockUser.findOne.mockResolvedValue(null);

      const result = await notificationService.updateUserWithDomain(mockInputData, 'test.com');
      expect(result).toEqual(mockInputData.user);
    });
  });
});
