const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  multer.memoryStorage = jest.fn(() => 'mocked-memory-storage');
  return multer;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  UserController: {
    show: jest.fn(),
    userLists: jest.fn(),
    update: jest.fn(),
    isAuthenticatedUser: jest.fn(),
    superAdminDetails: jest.fn(),
    updateAdminProfile: jest.fn(),
    changePassword: jest.fn(),
    uploadProfile: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  userValidation: {
    changePassword: { body: {}, params: {} },
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
}));

describe('userRoute', () => {
  let router;
  let userRoute;
  let UserController;
  let passportConfig;
  let userValidation;
  let checkAdmin;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    userRoute = require('../userRoute');
    const controllers = require('../../controllers');
    UserController = controllers.UserController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    userValidation = validations.userValidation;
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = userRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify GET routes
      expect(router.get).toHaveBeenCalledWith('/', UserController.show);
      expect(router.get).toHaveBeenCalledWith('/user_lists', UserController.userLists);
      expect(router.get).toHaveBeenCalledWith(
        '/authenticated_user',
        passportConfig.isAuthenticated,
        UserController.isAuthenticatedUser
      );
      expect(router.get).toHaveBeenCalledWith(
        '/user_details',
        passportConfig.isAuthenticated,
        UserController.superAdminDetails
      );

      // Verify PUT routes
      expect(router.put).toHaveBeenCalledWith(
        '/',
        passportConfig.isAuthenticated,
        UserController.update
      );
      expect(router.put).toHaveBeenCalledWith(
        '/update_admin_profile',
        passportConfig.isAuthenticated,
        UserController.updateAdminProfile
      );

      // Verify POST routes
      expect(router.post).toHaveBeenCalledWith(
        '/change_password',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        UserController.changePassword
      );
      expect(router.post).toHaveBeenCalledWith(
        '/upload_profile',
        'mocked-multer-middleware',
        passportConfig.isAuthenticated,
        UserController.uploadProfile
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = userRoute.router;
      const result2 = userRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure validation middleware correctly', () => {
      userRoute.router;

      expect(validate).toHaveBeenCalledWith(
        userValidation.changePassword,
        { keyByField: true },
        { abortEarly: false }
      );
    });

    it('should test multer middleware is properly configured', () => {
      userRoute.router;

      // Find the upload_profile route call
      const uploadProfileCall = router.post.mock.calls.find(call =>
        call[0] === '/upload_profile'
      );

      expect(uploadProfileCall).toBeDefined();
      expect(uploadProfileCall[1]).toBe('mocked-multer-middleware');
      expect(uploadProfileCall[2]).toBe(passportConfig.isAuthenticated);
      expect(uploadProfileCall[3]).toBe(UserController.uploadProfile);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof userRoute).toBe('object');
      expect(userRoute).toHaveProperty('router');
      
      const descriptor = Object.getOwnPropertyDescriptor(userRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(userRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});