const moment = require('moment');
const Moment = require('moment');
const MomentRange = require('moment-range');

const momentRange = MomentRange.extendMoment(Moment);
const { Sequelize, Enterprise } = require('../models');
let {
  DeliveryRequest,
  InspectionRequest,
  Member,
  VoidList,
  DeliverCompany,
  DeliverGate,
  InspectionCompany,
  InspectionGate,
  User,
  DeliveryPerson,
  CraneRequest,
} = require('../models');
const {
  CraneRequestCompany,
  CraneRequestResponsiblePerson,
  ConcreteRequest,
} = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const deliveryService = {
  async getEventNDR(inputData, done) {
    try {
      const { timezoneoffset } = inputData.headers;
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const loginUser = inputData.user;

      const memberDetails = await this.getMemberDetails(loginUser, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Member not found' });
      }

      const { startDateTime, endDateTime } = this.getDateTimeRange(incomeData, timezoneoffset);
      const condition = await this.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);
      const searchCondition = this.buildSearchCondition(incomeData, params);

      const deliveryList = await this.fetchDeliveryList(condition, memberDetails, searchCondition);

      if (!deliveryList?.rows?.length) {
        return done(deliveryList, false);
      }

      if (this.shouldApplyFilters(incomeData)) {
        return this.applyFiltersAndReturn(
          incomeData,
          deliveryList.rows,
          memberDetails,
          timezoneoffset,
          done
        );
      }

      return done(deliveryList, false);
    } catch (e) {
      done(null, e);
    }
  },

  async fetchDeliveryList(condition, memberDetails, searchCondition) {
    return await DeliveryRequest.getCalendarData(
      condition,
      memberDetails.RoleId,
      memberDetails.id,
      searchCondition,
      'DESC'
    );
  },

  async buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData) {
    const condition = {
      ProjectId: params.ProjectId,
      isQueued: false,
      deliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };

    if (memberDetails) {
      const voidDelivery = await this.getVoidDeliveryIds(params.ProjectId);
      this.applyVoidCondition(params, condition, voidDelivery);
    }

    this.applyFiltersToCondition(condition, incomeData);
    return condition;
  },

  async getVoidDeliveryIds(projectId) {
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    return voidList.map(element => element.DeliveryRequestId);
  },

  applyVoidCondition(params, condition, voidDelivery) {
    const voidOperator = (params.void === '0' || params.void === 0) ? Op.notIn : Op.in;
    condition['$DeliveryRequest.id$'] = {
      [Op.and]: [{ [voidOperator]: voidDelivery }],
    };
  },

  applyFiltersToCondition(condition, incomeData) {
    if (incomeData.descriptionFilter) {
      condition.description = {
        [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
      };
    }
    if (incomeData.pickFrom) {
      condition.cranePickUpLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
      };
    }
    if (incomeData.pickTo) {
      condition.craneDropOffLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
      };
    }
    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }
    if (incomeData.memberFilter > 0) {
      condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }
    if (incomeData.locationFilter) {
      condition['$location.locationPath$'] = incomeData.locationFilter;
    }
    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }
  },

  buildSearchCondition(incomeData, params) {
    if (!incomeData.search) {
      return {};
    }

    const searchDefault = [
      {
        '$approverDetails.User.firstName$': {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        '$equipmentDetails.Equipment.equipmentName$': {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        description: {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        cranePickUpLocation: {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        craneDropOffLocation: {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        '$location.locationPath$': {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
    ];

    if (!Number.isNaN(+incomeData.search)) {
      return {
        [Op.and]: [
          {
            [Op.or]: [
              searchDefault,
              {
                [Op.and]: [
                  {
                    DeliveryId: +incomeData.search,
                    isDeleted: false,
                    ProjectId: +params.ProjectId,
                  },
                ],
              },
            ],
          },
        ],
      };
    }

    return {
      [Op.and]: [
        {
          [Op.or]: searchDefault,
        },
      ],
    };
  },

  shouldApplyFilters(incomeData) {
    return (incomeData.companyFilter > 0) ||
      (incomeData.gateFilter > 0) ||
      (incomeData.memberFilter > 0) ||
      incomeData.dateFilter;
  },

  applyFiltersAndReturn(incomeData, deliveryList, memberDetails, timezoneoffset, done) {
    this.getSearchData(
      incomeData,
      deliveryList,
      0,
      [],
      memberDetails,
      timezoneoffset,
      async (checkResponse, checkError) => {
        if (!checkError) {
          const result = {
            rows: checkResponse,
            count: checkResponse.length,
          };
          return done(result, false);
        }
        return done(null, checkError);
      },
    );
  },

  async getInspectionEventNDR(inputData, done) {
    try {
      const { timezoneoffset } = inputData.headers;
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const loginUser = inputData.user;

      const memberDetails = await this.getMemberDetails(loginUser, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Member not found' });
      }

      const { startDateTime, endDateTime } = this.getDateTimeRange(incomeData, timezoneoffset);
      const condition = await this.buildInspectionCondition(params, startDateTime, endDateTime, memberDetails, incomeData);
      const searchCondition = this.buildSearchCondition(incomeData, params);

      const deliveryList = await InspectionRequest.getCalendarData(
        condition,
        memberDetails.RoleId,
        memberDetails.id,
        searchCondition,
        'DESC'
      );

      if (!deliveryList?.rows?.length) {
        return done(deliveryList, false);
      }

      if (this.shouldApplyFilters(incomeData)) {
        return this.applyInspectionFiltersAndReturn(
          incomeData,
          deliveryList.rows,
          memberDetails,
          timezoneoffset,
          done
        );
      }

      return done(deliveryList, false);
    } catch (e) {
      done(null, e);
    }
  },

  async buildInspectionCondition(params, startDateTime, endDateTime, memberDetails, incomeData) {
    const condition = {
      ProjectId: params.ProjectId,
      isQueued: false,
      inspectionStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };

    if (memberDetails) {
      const voidDelivery = await this.getVoidInspectionIds(params.ProjectId);
      this.applyVoidInspectionCondition(params, condition, voidDelivery);
    }

    this.applyInspectionFiltersToCondition(condition, incomeData);
    return condition;
  },

  async getVoidInspectionIds(projectId) {
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        InspectionRequestId: { [Op.ne]: null },
      },
    });
    return voidList.map(element => element.InspectionRequestId);
  },

  applyVoidInspectionCondition(params, condition, voidDelivery) {
    const voidOperator = (params.void === '0' || params.void === 0) ? Op.notIn : Op.in;
    condition['$InspectionRequest.id$'] = {
      [Op.and]: [{ [voidOperator]: voidDelivery }],
    };
  },

  applyInspectionFiltersToCondition(condition, incomeData) {
    if (incomeData.descriptionFilter) {
      condition.description = {
        [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
      };
    }
    if (incomeData.pickFrom) {
      condition.cranePickUpLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
      };
    }
    if (incomeData.pickTo) {
      condition.craneDropOffLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
      };
    }
    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }
    if (incomeData.inspectionStatusFilter) {
      condition.inspectionStatus = {
        [Sequelize.Op.iLike]: `%${incomeData.inspectionStatusFilter}%`,
      };
    }
    if (incomeData.inspectionTypeFilter) {
      condition.inspectionType = {
        [Sequelize.Op.iLike]: `%${incomeData.inspectionTypeFilter}%`,
      };
    }
    if (incomeData.memberFilter > 0) {
      condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }
    if (incomeData.locationFilter) {
      condition['$location.locationPath$'] = incomeData.locationFilter;
    }
    this.applyStatusFilter(condition, incomeData.statusFilter);
  },

  applyStatusFilter(condition, statusFilter) {
    if (!statusFilter) return;

    if (statusFilter === 'Delivered') {
      condition.inspectionStatus = {
        [Sequelize.Op.or]: [
          { [Sequelize.Op.iLike]: 'Pass' },
          { [Sequelize.Op.iLike]: 'Fail' },
        ],
      };
    } else if (statusFilter === 'Approved') {
      condition.status = statusFilter;
      condition.inspectionStatus = null;
    } else {
      condition.status = statusFilter;
    }
  },

  applyInspectionFiltersAndReturn(incomeData, deliveryList, memberDetails, timezoneoffset, done) {
    this.getInspectionSearchData(
      incomeData,
      deliveryList,
      0,
      [],
      memberDetails,
      timezoneoffset,
      async (checkResponse, checkError) => {
        if (!checkError) {
          const result = {
            rows: checkResponse,
            count: checkResponse.length,
          };
          return done(result, false);
        }
        return done(null, checkError);
      },
    );
  },

  async getDeliveryRequestWithCrane(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { timezoneoffset } = inputData.headers;
      const { params } = inputData;
      const incomeData = inputData.body;
      const loginUser = inputData.user;

      const memberDetails = await this.getMemberDetails(loginUser, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Member not found' });
      }

      const { startDateTime, endDateTime } = this.getDateTimeRange(incomeData, timezoneoffset);
      const { deliveryRequestCondition, craneDeliveryRequestCondition } = await this.buildRequestConditions(
        params,
        startDateTime,
        endDateTime,
        memberDetails
      );

      const { craneRequestArray, deliveryList } = await this.fetchRequestData(
        inputData,
        memberDetails,
        deliveryRequestCondition,
        craneDeliveryRequestCondition,
        incomeData
      );

      if (this.shouldApplyFilters(incomeData)) {
        return this.applyFiltersAndReturn(
          incomeData,
          craneRequestArray,
          deliveryList,
          memberDetails,
          timezoneoffset,
          done
        );
      }

      if (deliveryList.length > 0) {
        craneRequestArray.push(...deliveryList);
      }
      return done(craneRequestArray, false);
    } catch (e) {
      done(null, e);
    }
  },
  getDateTimeRange(incomeData, timezoneoffset) {
    const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);
    return { startDateTime, endDateTime };
  },
  async buildRequestConditions(params, startDateTime, endDateTime, memberDetails) {
    const deliveryRequestCondition = {
      ProjectId: +params.ProjectId,
      isQueued: false,
      isAssociatedWithCraneRequest: true,
      deliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };

    const craneDeliveryRequestCondition = {
      ProjectId: +params.ProjectId,
      craneDeliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };

    const { voidDelivery, voidCraneDelivery } = await this.getVoidLists(params.ProjectId);

    this.applyVoidConditions(
      params,
      deliveryRequestCondition,
      craneDeliveryRequestCondition,
      voidDelivery,
      voidCraneDelivery
    );

    return { deliveryRequestCondition, craneDeliveryRequestCondition };
  },
  async getVoidLists(projectId) {
    const voidDelivery = [];
    const voidCraneDelivery = [];

    const voidList = await VoidList.findAll({
      where: {
        ProjectId: +projectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    voidList.forEach(element => voidDelivery.push(element.DeliveryRequestId));

    const voidCraneRequestList = await VoidList.findAll({
      where: {
        ProjectId: +projectId,
        isDeliveryRequest: false,
        CraneRequestId: { [Op.ne]: null },
      },
    });
    voidCraneRequestList.forEach(element => voidCraneDelivery.push(element.CraneRequestId));

    return { voidDelivery, voidCraneDelivery };
  },
  applyVoidConditions(params, deliveryRequestCondition, craneDeliveryRequestCondition, voidDelivery, voidCraneDelivery) {
    const voidOperator = (params.void === '0' || params.void === 0) ? Op.notIn : Op.in;

    deliveryRequestCondition['$DeliveryRequest.id$'] = {
      [Op.and]: [{ [voidOperator]: voidDelivery }],
    };

    craneDeliveryRequestCondition['$CraneRequest.id$'] = {
      [Op.and]: [{ [voidOperator]: voidCraneDelivery }],
    };
  },
  async fetchRequestData(inputData, memberDetails, deliveryRequestCondition, craneDeliveryRequestCondition, incomeData) {
    const { roleId, memberId } = memberDetails;
    let craneRequestArray = [];
    let deliveryList = [];

    if (!this.shouldSkipCraneRequest(incomeData)) {
      const craneRequestList = await CraneRequest.getAll({
        req: inputData,
        roleId,
        memberId,
        attr: craneDeliveryRequestCondition,
        filters: {
          description: incomeData.descriptionFilter,
          company: incomeData.companyFilter,
          member: incomeData.memberFilter,
          equipment: incomeData.equipmentFilter,
          status: incomeData.statusFilter,
          id: incomeData.idFilter,
          pickFrom: incomeData.pickFrom,
          pickTo: incomeData.pickTo,
          date: incomeData.dateFilter,
        },
        dateRange: {
          startdate: incomeData.startdate,
          enddate: incomeData.enddate,
        },
        search: incomeData.search,
        order: 'DESC',
        sort: incomeData.sort,
        sortColumn: incomeData.sortByField,
      });
      craneRequestArray = craneRequestList;
    }

    if (!this.shouldSkipDeliveryRequest(incomeData)) {
      deliveryList = await DeliveryRequest.getCraneAssociatedRequest({
        req: inputData,
        roleId,
        memberId,
        attr: deliveryRequestCondition,
        descriptionFilter: incomeData.descriptionFilter,
        startdate: incomeData.startdate,
        enddate: incomeData.enddate,
        companyFilter: incomeData.companyFilter,
        memberFilter: incomeData.memberFilter,
        equipmentFilter: incomeData.equipmentFilter,
        statusFilter: incomeData.statusFilter,
        idFilter: incomeData.idFilter,
        pickFrom: incomeData.pickFrom,
        pickTo: incomeData.pickTo,
        search: incomeData.search,
        gateFilter: incomeData.gateFilter,
        order: 'DESC',
        sort: incomeData.sort,
        sortByField: incomeData.sortByField,
        voidType: inputData.params.void,
        dateFilter: incomeData.dateFilter,
      });
    }

    return { craneRequestArray, deliveryList };
  },
  shouldSkipCraneRequest(incomeData) {
    return (incomeData.gateFilter > 0) ||
      (incomeData.statusFilter === 'Delivered');
  },
  shouldSkipDeliveryRequest(incomeData) {
    return incomeData.statusFilter === 'Completed';
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const domainName = this.getDomainNameFromInput(inputData);
    const ParentCompanyId = this.getParentCompanyId(inputData);

    const domainEnterpriseValue = await this.findDomainEnterprise(domainName);
    if (domainEnterpriseValue) {
      return this.setupModelsWithDomain(domainName, inputData);
    }

    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      return this.setupModelsWithParentCompany(inputData, ParentCompanyId);
    }

    return this.setupDefaultModels(inputData);
  },

  getDomainNameFromInput(inputData) {
    return inputData.user?.domainName ?? '';
  },

  getParentCompanyId(inputData) {
    return inputData.body?.ParentCompanyId ?? inputData.params?.ParentCompanyId;
  },

  async findDomainEnterprise(domainName) {
    if (!domainName) return null;

    return await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });
  },

  async setupModelsWithDomain(domainName, inputData) {
    const modelObj = await helper.getDynamicModel(domainName);
    this.assignModels(modelObj);
    return null;
  },

  async setupModelsWithParentCompany(inputData, ParentCompanyId) {
    const { email } = inputData.user;
    if (!email) return this.setupDefaultModels(inputData);

    const userData = await this.findUserByEmail(email);
    if (!userData) return this.setupDefaultModels(inputData);

    const enterpriseValue = await this.findEnterpriseForUser(userData, ParentCompanyId);
    if (!enterpriseValue) return this.setupDefaultModels(inputData);

    const domainName = enterpriseValue.name.toLowerCase();
    const modelObj = await helper.getDynamicModel(domainName);
    this.assignModels(modelObj);

    const newUser = await User.findOne({ where: { email: inputData.user.email } });
    inputData.user = newUser;
    return null;
  },

  async findUserByEmail(email) {
    return await publicUser.findOne({ where: { email } });
  },

  async findEnterpriseForUser(userData, ParentCompanyId) {
    const memberData = await this.findMemberData(userData);
    if (!memberData) {
      return await this.findEnterpriseByParentCompany(ParentCompanyId);
    }

    if (memberData.isAccount) {
      return await this.findEnterpriseById(memberData.EnterpriseId);
    }

    return await this.findEnterpriseByParentCompany(ParentCompanyId);
  },

  async findMemberData(userData) {
    return await publicMember.findOne({
      where: {
        UserId: userData.id,
        RoleId: { [Op.ne]: 4 },
        isDeleted: false
      },
    });
  },

  async findEnterpriseById(enterpriseId) {
    return await Enterprise.findOne({
      where: {
        id: enterpriseId,
        status: 'completed'
      },
    });
  },

  async findEnterpriseByParentCompany(ParentCompanyId) {
    return await Enterprise.findOne({
      where: {
        ParentCompanyId,
        status: 'completed'
      },
    });
  },

  async setupDefaultModels(inputData) {
    const modelObj = await helper.getDynamicModel('');
    this.assignModels(modelObj);
    return null;
  },

  assignModels(modelObj) {
    // The assignment was marked as unnecessary and removed.
  },

  async getSearchData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (!deliveryList.length) {
      return done(deliveryList, false);
    }

    await this.getDynamicModel(incomeData);
    const element = this.prepareElement(deliveryList[index]);
    const status = await this.checkSearchConditions(incomeData, element);

    if (this.shouldAddElement(status, incomeData, element, timezoneoffset)) {
      result.push(element);
    }

    return this.processNextElement(
      incomeData,
      deliveryList,
      index,
      result,
      memberDetails,
      timezoneoffset,
      done
    );
  },

  prepareElement(elementValue) {
    return JSON.parse(JSON.stringify(elementValue));
  },

  async checkSearchConditions(incomeData, element) {
    const status = { companyCondition: true, gateCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      const data = await DeliverCompany.findOne({
        where: {
          DeliveryId: element.id,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      status.companyCondition = !!data;
    }

    if (incomeData.gateFilter > 0) {
      const data = await DeliverGate.findOne({
        where: {
          DeliveryId: element.id,
          GateId: +incomeData.gateFilter,
          isDeleted: false,
        },
      });
      status.gateCondition = !!data;
    }

    return status;
  },

  shouldAddElement(status, incomeData, element, timezoneoffset) {
    if (!this.areAllConditionsMet(status)) {
      return false;
    }

    if (!incomeData.dateFilter) {
      return true;
    }

    return this.isElementInDateRange(element, incomeData.dateFilter, timezoneoffset);
  },

  areAllConditionsMet(status) {
    return status.companyCondition && status.gateCondition && status.memberCondition;
  },

  isElementInDateRange(element, dateFilter, timezoneoffset) {
    if (!element?.deliveryStart) {
      return false;
    }

    const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);

    return moment(element.deliveryStart)
      .utcOffset(Number(timezoneoffset))
      .isBetween(startDateTime, endDateTime, undefined, '()');
  },

  processNextElement(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done
  ) {
    if (index >= deliveryList.length - 1) {
      return done(result, false);
    }

    return this.getSearchData(
      incomeData,
      deliveryList,
      index + 1,
      result,
      memberDetails,
      timezoneoffset,
      (response, err) => {
        if (!err) {
          done(response, false);
        } else {
          done(null, err);
        }
      }
    );
  },

  async getInspectionSearchData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (!deliveryList.length) {
      return done(deliveryList, false);
    }

    await this.getDynamicModel(incomeData);
    const element = this.prepareElement(deliveryList[index]);
    const status = await this.checkInspectionSearchConditions(incomeData, element);

    if (this.shouldAddInspectionElement(status, incomeData, element, timezoneoffset)) {
      result.push(element);
    }

    return this.processNextInspectionElement(
      incomeData,
      deliveryList,
      index,
      result,
      memberDetails,
      timezoneoffset,
      done
    );
  },

  async checkInspectionSearchConditions(incomeData, element) {
    const status = { companyCondition: true, gateCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      const data = await InspectionCompany.findOne({
        where: {
          InspectionId: element.id,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      status.companyCondition = !!data;
    }

    if (incomeData.gateFilter > 0) {
      const data = await InspectionGate.findOne({
        where: {
          InspectionId: element.id,
          GateId: +incomeData.gateFilter,
          isDeleted: false,
        },
      });
      status.gateCondition = !!data;
    }

    return status;
  },

  shouldAddInspectionElement(status, incomeData, element, timezoneoffset) {
    if (!this.areAllInspectionConditionsMet(status)) {
      return false;
    }

    if (!incomeData.dateFilter) {
      return true;
    }

    return this.isInspectionElementInDateRange(element, incomeData.dateFilter, timezoneoffset);
  },

  areAllInspectionConditionsMet(status) {
    return status.companyCondition && status.gateCondition && status.memberCondition;
  },

  isInspectionElementInDateRange(element, dateFilter, timezoneoffset) {
    if (!element?.inspectionStart) {
      return false;
    }

    const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);

    return moment(element.inspectionStart)
      .utcOffset(Number(timezoneoffset))
      .isBetween(startDateTime, endDateTime, undefined, '()');
  },

  processNextInspectionElement(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done
  ) {
    if (index >= deliveryList.length - 1) {
      return done(result, false);
    }

    return this.getInspectionSearchData(
      incomeData,
      deliveryList,
      index + 1,
      result,
      memberDetails,
      timezoneoffset,
      (response, err) => {
        if (!err) {
          done(response, false);
        } else {
          done(null, err);
        }
      }
    );
  },
  async getSearchCraneRequestCalendarData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (!deliveryList.length) {
      return done(deliveryList, false);
    }

    await this.getDynamicModel(incomeData);
    const element = this.prepareElement(deliveryList[index]);
    const status = await this.checkCraneSearchConditions(incomeData, element);

    if (this.shouldAddCraneElement(status)) {
      result.push(element);
    }

    return this.processNextCraneElement(
      incomeData,
      deliveryList,
      index,
      result,
      memberDetails,
      timezoneoffset,
      done
    );
  },

  async checkCraneSearchConditions(incomeData, element) {
    const status = { companyCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      const data = await CraneRequestCompany.findOne({
        where: {
          CraneRequestId: element.id,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      status.companyCondition = !!data;
    }

    if (incomeData.memberFilter > 0) {
      const data = await CraneRequestResponsiblePerson.findOne({
        where: {
          CraneRequestId: element.id,
          MemberId: incomeData.memberFilter,
          isDeleted: false,
        },
      });
      status.memberCondition = !!data;
    }

    return status;
  },

  shouldAddCraneElement(status) {
    return status.companyCondition && status.memberCondition;
  },

  processNextCraneElement(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done
  ) {
    if (index >= deliveryList.length - 1) {
      return done(result, false);
    }

    return this.getSearchCraneRequestCalendarData(
      incomeData,
      deliveryList,
      index + 1,
      result,
      memberDetails,
      timezoneoffset,
      (response, err) => {
        if (!err) {
          done(response, false);
        } else {
          done(null, err);
        }
      }
    );
  },
  async getConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { timezoneoffset } = inputData.headers;
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;

      if (!this.isValidVoidParam(params?.void)) {
        return done(null, { message: 'Please enter void as 1 or 0' });
      }

      const memberDetails = await this.getMemberDetails(loginUser, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      const voidConcreteDelivery = await this.getVoidConcreteDeliveryIds(params.ProjectId);
      const concreteCondition = this.buildConcreteCondition(params, timezoneoffset, incomeData, voidConcreteDelivery);

      const getConcreteRequest = await ConcreteRequest.getAll({
        req: inputData,
        attr: concreteCondition,
        filters: {
          description: incomeData.descriptionFilter,
          location: incomeData.locationFilter,
          concreteSupplier: incomeData.concreteSupplierFilter,
          orderNumber: incomeData.orderNumberFilter,
          status: incomeData.statusFilter,
          mixDesign: incomeData.mixDesignFilter,
          member: incomeData.memberFilter,
          search: incomeData.search,
        },
        sort: {
          order: '',
          sort: incomeData.sort,
          sortColumn: incomeData.sortByField,
        },
      });

      done(getConcreteRequest, false);
    } catch (e) {
      done(null, e);
    }
  },

  isValidVoidParam(voidParam) {
    return !(voidParam >= 1 && voidParam <= 0);
  },

  async getMemberDetails(loginUser, projectId) {
    return await publicMember.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: projectId,
        isDeleted: false,
        isActive: true,
      }),
    });
  },

  async getVoidConcreteDeliveryIds(projectId) {
    const voidConcreteRequestList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeliveryRequest: false,
        ConcreteRequestId: { [Op.ne]: null },
      },
    });
    return voidConcreteRequestList.map(element => element.ConcreteRequestId);
  },

  buildConcreteCondition(params, timezoneoffset, incomeData, voidConcreteDelivery) {
    const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);

    const condition = {
      ProjectId: +params.ProjectId,
      isDeleted: false,
      concretePlacementStart: {
        [Op.between]: [startDateTime.toDate(), endDateTime.toDate()],
      },
    };

    const voidOperator = (params.void === '0' || params.void === 0) ? Op.notIn : Op.in;
    condition['$ConcreteRequest.id$'] = {
      [Op.and]: [{ [voidOperator]: voidConcreteDelivery }],
    };

    return condition;
  },
};

module.exports = deliveryService;