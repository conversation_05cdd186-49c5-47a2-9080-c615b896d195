module.exports = (sequelize, DataTypes) => {
  const ConcretePumpSize = sequelize.define(
    'ConcretePumpSize',
    {
      pumpSize: DataTypes.STRING,
      pumpSizeAutoId: DataTypes.INTEGER,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  ConcretePumpSize.associate = (models) => {
    ConcretePumpSize.belongsTo(models.User, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });

    ConcretePumpSize.belongsTo(models.Project);

    return ConcretePumpSize;
  };
  ConcretePumpSize.getConcretePumpSizes = async (attr) => {
    const pumpSizes = await ConcretePumpSize.findOne({
      include: [
        { association: 'userDetails', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        'Project',
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return pumpSizes;
  };
  ConcretePumpSize.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
    let pumpSize;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    if (limit === 0) {
      pumpSize = await ConcretePumpSize.findAll({
        where: { ...attr },
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    } else {
      pumpSize = await ConcretePumpSize.findAndCountAll({
        where: { ...attr, ...searchCondition },
        attributes: ['pumpSize', 'id', 'pumpSizeAutoId'],
        limit,
        offset,
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    }
    return pumpSize;
  };
  ConcretePumpSize.updateInstance = async (id, args) => {
    const pumpSize = await ConcretePumpSize.update(args, { where: { id } });
    return pumpSize;
  };

  ConcretePumpSize.createConcretePumpSize = async (pumpSizeData) => {
    const newConcretePumpSizeData = await ConcretePumpSize.create(pumpSizeData);
    return newConcretePumpSizeData;
  };
  return ConcretePumpSize;
};
