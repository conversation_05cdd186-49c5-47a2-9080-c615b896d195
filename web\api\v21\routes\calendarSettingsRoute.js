const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { calendarsettingsController } = require('../controllers');
const { calendarSettingsValidation } = require('../middlewares/validations');
const cacheMiddleware = require('../middlewares/cacheMiddleware');

const calendarSettingsRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_event',
      validate(
        calendarSettingsValidation.addCalendarEvent,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAddEvent(),
      calendarsettingsController.addCalendarEvent,
    );
    router
      .get(
        '/get_event/:id',
        validate(
          calendarSettingsValidation.getCalendarEvent,
          { keyByField: true },
          { abortEarly: false },
        ),
        passportConfig.isAuthenticated,
        cacheMiddleware.cacheCalendarEvent(),
        calendarsettingsController.getCalendarEvent,
      )
      .put(
        '/edit_event/:id',
        validate(
          calendarSettingsValidation.editCalendarEvent,
          { keyByField: true },
          { abortEarly: false },
        ),
        passportConfig.isAuthenticated,
        cacheMiddleware.invalidateAfterUpdateEvent(),
        calendarsettingsController.editCalendarEvent,
      )
      .put(
        '/delete_event/:id',
        validate(
          calendarSettingsValidation.deleteCalendarEvent,
          { keyByField: true },
          { abortEarly: false },
        ),
        passportConfig.isAuthenticated,
        cacheMiddleware.invalidateAfterDeleteEvent(),
        calendarsettingsController.deleteCalendarEvent,
      );
    router.get(
      '/get_calendar_events',
      validate(
        calendarSettingsValidation.getCalendarEvents,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheCalendarEvents(),
      calendarsettingsController.getCalendarEvents,
    );
    router.get(
      '/get_calendar_month_events',
      validate(
        calendarSettingsValidation.getCalendarMonthEvents,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheCalendarEvents(),
      calendarsettingsController.getCalendarMonthEvents,
    );
    return router;
  },
};

module.exports = calendarSettingsRoute;
