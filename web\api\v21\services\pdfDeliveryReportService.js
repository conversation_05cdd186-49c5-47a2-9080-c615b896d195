const moment = require('moment');
const fs = require('fs');
const awsConfig = require('../middlewares/awsConfig');
const { Project, Company } = require('../models');
const puppeteerService = require('./puppeteerService');

const pdfDeliveryReportService = {
  extractDeliveryHeaderSelection(req) {
    const { selectedHeaders } = req.body;
    const header = [];
    const selectedFlags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isEquipmentSelected: false,
      isDfowSelected: false,
      isGateSelected: false,
      isCompanySelected: false,
      isPersonSelected: false,
      isLocationSelected: false
    };

    selectedHeaders.forEach(item => {
      if (item.isActive) {
        const key = item.key === 'name' ? 'Person' : item.key.charAt(0).toUpperCase() + item.key.slice(1);
        selectedFlags[`is${key}Selected`] = true;
        header.push(`<th style="text-align:center">${item.title}</th>`);
      }
    });

    return { selectedFlags, header };
  },
  buildDeliveryRows(data, flags, timezoneoffset) {
    const rows = [];

    const td = (val) => `<td style="color: rgb(10,10,10);font-weight:600;text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${val}</td>`;
    const wrapList = (list) => Array.isArray(list) ? list.map(val => `<p>${val}</p>`).join('') : '-';

    data.forEach(item => {
      const deliveryStart = moment(item.deliveryStart).add(Number(timezoneoffset), 'minutes');
      const deliveryEnd = moment(item.deliveryEnd).add(Number(timezoneoffset), 'minutes');

      // Extract approver name logic
      const approverName = item.approverDetails ? `${item.approverDetails.User.firstName} ${item.approverDetails.User.lastName}` : '-';

      const row = [
        flags.isIdSelected ? td(item.DeliveryId) : '',
        flags.isDescriptionSelected ? td(item.description) : '',
        flags.isDateSelected ? td(`${deliveryStart.format('MM/DD/YYYY hh:mm a')} - ${deliveryEnd.format('hh:mm a')}`) : '',
        flags.isStatusSelected ? td(item.status || '-') : '',
        flags.isApprovedBySelected ? td(approverName) : '',
        flags.isEquipmentSelected ? td(wrapList(item.equipmentDetails?.map(e => e.Equipment.equipmentName))) : '',
        flags.isDfowSelected ? td(wrapList(item.defineWorkDetails?.map(d => d.DeliverDefineWork.DFOW))) : '',
        flags.isGateSelected ? td(item.gateDetails?.[0]?.Gate?.gateName || '-') : '',
        flags.isCompanySelected ? td(wrapList(item.companyDetails?.map(c => c.Company.companyName))) : '',
        flags.isPersonSelected ? td(wrapList(item.memberDetails?.map(m => `${m.Member.User.firstName} ${m.Member.User.lastName}`))) : '',
        flags.isLocationSelected ? td(item.location?.locationPath || '-') : ''
      ].join('');

      rows.push(`<tr style="border-bottom:1px solid #e0e0e0;font-size:12px">${row}</tr>`);
    });

    return rows;
  },

  generateDeliveryPdfTemplate(templatePath, projectData, companyData, loginUser, req, header, content) {
    let template = fs.readFileSync(templatePath, 'utf-8');

    return template
      .replace('$projectName', projectData.projectName)
      .replace('$companyName', companyData.companyName)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName}`)
      .replace('$reportType', 'Delivery')
      .replace('$header', header.join(''))
      .replace('$data', content.join(''))
      .replace(/,/g, '');
  },
  async pdfFormatOfDeliveryRequest(params, loginUser, data, req, done) {
    try {
      const { timezoneoffset } = req.headers;
      const { selectedFlags, header } = this.extractDeliveryHeaderSelection(req);

      const projectData = await Project.findOne({
        where: { isDeleted: false, id: +params.ProjectId },
        attributes: ['projectName']
      });

      const companyData = await Company.findOne({
        where: { isDeleted: false, ParentCompanyId: +req.body.ParentCompanyId, isParent: true },
        attributes: ['companyName']
      });

      const rows = this.buildDeliveryRows(data, selectedFlags, timezoneoffset);
      const templatePath = '/usr/src/web/api/v21/views/mail-templates/deliveryReport.html';
      const finalHtml = this.generateDeliveryPdfTemplate(templatePath, projectData, companyData, loginUser, req, header, rows);

      const pdfBuffer = await puppeteerService.generatePdfBuffer(finalHtml);
      if (pdfBuffer) {
        awsConfig.reportUpload(pdfBuffer, req.body.reportName, req.body.exportType, (result, error) => {
          if (!error) done(result, false);
          else done(false, { message: 'Upload failed' });
        });
      } else {
        done(false, { message: 'Cannot export the document' });
      }
    } catch (err) {
      done(false, { message: 'Unexpected error during PDF generation', error: err.message });
    }
  }

};
module.exports = pdfDeliveryReportService;
