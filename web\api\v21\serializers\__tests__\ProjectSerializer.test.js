const ProjectSerializer = require('../ProjectSerializer');

describe('ProjectSerializer', () => {
  it('should serialize all fields correctly', () => {
    const equipment = {
      id: 1,
      equipmentName: 'Project 1',
      createdBy: 2,
      controlledBy: 3,
      projectId: 4,
      createdAt: '2024-01-01T00:00:00Z',
    };
    const result = ProjectSerializer.serialize(equipment);
    expect(result).toEqual(equipment);
  });

  it('should serialize with missing fields as undefined', () => {
    const equipment = {
      id: 1,
      equipmentName: 'Project 1',
    };
    const result = ProjectSerializer.serialize(equipment);
    expect(result).toEqual({
      id: 1,
      equipmentName: 'Project 1',
      createdBy: undefined,
      controlledBy: undefined,
      projectId: undefined,
      createdAt: undefined,
    });
  });
});
