const status = require('http-status');
const haulingLogService = require('../services/haulingLogService')

const HaulingLogController = {
    async addHaulingLog(req, res, next) {
        haulingLogService.addHaulingLog(req, async (haulingLog, error) => {
            if (error) {
                next(error);
            } else {
                res.status(status.CREATED).json({
                    message: 'HaulingLog added successfully.',
                    data: haulingLog,
                });
            }
        });
    },
    async listHaulingLog(req, res, next) {
        haulingLogService.listHaulingLog(req, async (haulingLogDetail, error) => {
            if (error) {
                next(error);
            } else {
                res.status(status.OK).json({
                    message: 'Hauling Log Listed successfully.',
                    data: haulingLogDetail,
                });
            }
        });
    }
};
module.exports = HaulingLogController;
