module.exports = (sequelize, DataTypes) => {
  const CraneRequestHistory = sequelize.define(
    'CraneRequestHistory',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      CraneRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      type: {
        type: DataTypes.STRING,
      }, // values: ['edit', 'comment', 'approved', 'declined', 'expired', 'attachement', 'completed'],
      description: DataTypes.STRING,
    },
    {},
  );
  CraneRequestHistory.associate = (models) => {
    CraneRequestHistory.belongsTo(models.Member);
    CraneRequestHistory.belongsTo(models.CraneRequest);
  };
  CraneRequestHistory.getAll = async (attr) => {
    const newCraneRequestHistory = await CraneRequestHistory.findAll({
      where: { ...attr },
    });
    return newCraneRequestHistory;
  };
  CraneRequestHistory.createInstance = async (paramData) => {
    const newCraneRequestHistory = await CraneRequestHistory.create(paramData);
    return newCraneRequestHistory;
  };
  return CraneRequestHistory;
};
