const gateService = require('../gateService');
const helper = require('../../helpers/domainHelper');
const ApiError = require('../../helpers/apiError');
const status = require('http-status');

// Mock all dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      in: 'in',
      ne: 'ne',
      and: 'and',
      or: 'or',
      iLike: 'iLike',
      gte: 'gte',
    },
    and: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  DeliverGate: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    update: jest.fn(),
    createInstance: jest.fn(),
  },
  DeliveryRequest: {
    findAll: jest.fn(),
  },
  Member: {
    getBy: jest.fn(),
    findOne: jest.fn(),
  },
  DeliverHistory: {
    createInstance: jest.fn(),
  },
  Gates: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    getGates: jest.fn(),
    createGate: jest.fn(),
    getAll: jest.fn(),
    updateInstance: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
    getProject: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

jest.mock('../../helpers/apiError');

describe('GateService', () => {
  let mockInputData;
  let mockDone;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get mocked models
    mockModels = require('../../models');

    mockInputData = {
      user: {
        id: 1,
        email: '<EMAIL>',
        domainName: 'testdomain',
        firstName: 'John',
        lastName: 'Doe',
      },
      body: {
        ProjectId: 1,
        gateName: 'Test Gate',
        gateType: 'Main',
        ParentCompanyId: 1,
        id: 1,
        isSelectAll: false,
        gateSwitchedRequests: [],
      },
      params: {
        ProjectId: 1,
        pageNo: '1',
        pageSize: '10',
        ParentCompanyId: 1,
      },
    };

    mockDone = jest.fn();

    // Setup default mock implementations
    helper.getDynamicModel.mockResolvedValue({
      Gates: mockModels.Gates,
      Project: mockModels.Project,
      User: mockModels.User,
    });

    helper.returnProjectModel.mockResolvedValue({
      User: mockModels.User,
      Member: mockModels.Member,
    });

    mockModels.Enterprise.findOne.mockResolvedValue({
      name: 'testdomain',
      id: 1,
    });

    mockModels.Project.getProject.mockResolvedValue({
      id: 1,
      projectName: 'Test Project',
    });

    mockModels.Gates.getGates.mockResolvedValue(null);
    mockModels.Gates.findOne.mockResolvedValue(null);
    mockModels.Gates.createGate.mockResolvedValue({
      id: 1,
      gateName: 'Test Gate',
    });

    mockModels.Member.getBy.mockResolvedValue({
      id: 1,
      UserId: 1,
      ProjectId: 1,
    });
  });

  describe('addGates', () => {
    it('should successfully add a gate', async () => {
      const mockGate = {
        id: 1,
        gateName: 'Test Gate',
        gateType: 'Main',
      };

      mockModels.Gates.createGate.mockResolvedValue(mockGate);

      await gateService.addGates(mockInputData, mockDone);

      expect(helper.getDynamicModel).toHaveBeenCalled();
      expect(mockModels.Project.getProject).toHaveBeenCalledWith({
        id: mockInputData.body.ProjectId,
      });
      expect(mockModels.Gates.getGates).toHaveBeenCalledWith({
        gateName: mockInputData.body.gateName,
        isDeleted: false,
        ProjectId: mockInputData.body.ProjectId,
      });
      expect(mockDone).toHaveBeenCalledWith(mockGate, false);
    });

    it('should return error when project does not exist', async () => {
      mockModels.Project.getProject.mockResolvedValue(null);

      await gateService.addGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should return error when gate name already exists', async () => {
      mockModels.Gates.getGates.mockResolvedValue({
        id: 1,
        gateName: 'Test Gate',
      });

      await gateService.addGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should generate auto ID when no previous gate exists', async () => {
      mockModels.Gates.findOne.mockResolvedValue(null);

      await gateService.addGates(mockInputData, mockDone);

      expect(mockModels.Gates.findOne).toHaveBeenCalledWith({
        where: { ProjectId: mockInputData.body.ProjectId, isDeleted: false },
        order: [['gateAutoId', 'DESC']],
      });
    });

    it('should increment auto ID based on last gate', async () => {
      mockModels.Gates.findOne.mockResolvedValue({
        gateAutoId: 5,
      });

      await gateService.addGates(mockInputData, mockDone);

      expect(mockModels.Gates.findOne).toHaveBeenCalled();
    });

    it('should handle errors during gate creation', async () => {
      const error = new Error('Database error');
      mockModels.Gates.createGate.mockRejectedValue(error);

      await gateService.addGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully with domain name', async () => {
      await gateService.getDynamicModel(mockInputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: mockInputData.user.domainName.toLowerCase() },
      });
      expect(helper.getDynamicModel).toHaveBeenCalledWith(mockInputData.user.domainName);
    });

    it('should handle missing domain name with ParentCompanyId', async () => {
      const inputDataWithoutDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
      };

      mockModels.Enterprise.findOne.mockResolvedValue(null);

      await gateService.getDynamicModel(inputDataWithoutDomain);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should update user when domain enterprise value exists', async () => {
      const newUser = { id: 2, email: '<EMAIL>' };
      mockModels.User.findOne.mockResolvedValue(newUser);

      await gateService.getDynamicModel(mockInputData);

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: mockInputData.user.email },
      });
    });
  });

  describe('deactivateGate', () => {
    beforeEach(() => {
      mockModels.Gates.findOne.mockResolvedValue({
        id: 1,
        gateName: 'Test Gate',
        ProjectId: 1,
      });

      mockModels.DeliverGate.findAll.mockResolvedValue([]);
      mockModels.Gates.update.mockResolvedValue([1]);
    });

    it('should successfully deactivate a gate', async () => {
      await gateService.deactivateGate(mockInputData, mockDone);

      expect(mockModels.Gates.findOne).toHaveBeenCalledWith({
        where: {
          id: mockInputData.body.id,
          ProjectId: mockInputData.body.ProjectId,
          isDeleted: false,
        },
      });
      expect(mockModels.Member.getBy).toHaveBeenCalledWith({
        UserId: mockInputData.user.id,
        ProjectId: mockInputData.body.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      expect(mockModels.Gates.update).toHaveBeenCalledWith(
        { isActive: false },
        {
          where: {
            id: 1,
            isActive: true,
          },
        }
      );
      expect(mockDone).toHaveBeenCalledWith([1], false);
    });

    it('should return error when gate not found', async () => {
      mockModels.Gates.findOne.mockResolvedValue(null);

      await gateService.deactivateGate(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should handle gate switched requests', async () => {
      const inputDataWithSwitchedRequests = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          gateSwitchedRequests: [
            { id: 1, gateId: 2 },
            { id: 2, gateId: 3 },
          ],
        },
      };

      await gateService.deactivateGate(inputDataWithSwitchedRequests, mockDone);

      expect(mockModels.Gates.update).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith([1], false);
    });

    it('should handle errors during deactivation', async () => {
      const error = new Error('Database error');
      mockModels.Gates.findOne.mockRejectedValue(error);

      await gateService.deactivateGate(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('returnProjectModel', () => {
    it('should return project model successfully', async () => {
      await gateService.returnProjectModel();

      expect(helper.returnProjectModel).toHaveBeenCalled();
    });
  });

  describe('getDynamicModel edge cases', () => {
    it('should handle when no new user is found', async () => {
      const inputDataWithDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: 'testdomain',
        },
      };

      mockModels.User.findOne.mockResolvedValue(null);

      const result = await gateService.getDynamicModel(inputDataWithDomain);

      expect(result).toBe(inputDataWithDomain.body.ProjectId);
      expect(inputDataWithDomain.user).toEqual(mockInputData.user);
    });

    it('should return ProjectId from body', async () => {
      const result = await gateService.getDynamicModel(mockInputData);

      expect(result).toBe(mockInputData.body.ProjectId);
    });
  });

  describe('updateGates', () => {
    beforeEach(() => {
      mockModels.Gates.findOne.mockResolvedValue({
        id: 1,
        gateName: 'Test Gate',
        ProjectId: 1,
      });
      mockModels.Gates.updateInstance = jest.fn().mockResolvedValue({
        id: 1,
        gateName: 'Updated Gate',
      });
    });

    it('should successfully update a gate', async () => {
      const updateInputData = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          id: 1,
          gateName: 'Updated Gate',
        },
      };

      await gateService.updateGates(updateInputData, mockDone);

      expect(mockModels.Gates.findOne).toHaveBeenCalledWith({
        where: expect.objectContaining({
          id: updateInputData.body.id,
          ProjectId: updateInputData.body.ProjectId,
        }),
      });
      expect(mockModels.Project.getProject).toHaveBeenCalledWith({
        id: updateInputData.body.ProjectId,
      });
      expect(mockModels.Gates.updateInstance).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should return error when gate not found', async () => {
      mockModels.Gates.findOne.mockResolvedValue(null);

      await gateService.updateGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'Gate id does not exist.' });
    });

    it('should return error when project does not exist', async () => {
      mockModels.Project.getProject.mockResolvedValue(null);

      await gateService.updateGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project does not exist.' });
    });

    it('should return error when gate name already exists for different gate', async () => {
      mockModels.Gates.getGates.mockResolvedValue({
        id: 2, // Different ID
        gateName: 'Test Gate',
      });

      await gateService.updateGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should allow updating gate with same name for same gate', async () => {
      mockModels.Gates.getGates.mockResolvedValue({
        id: 1, // Same ID
        gateName: 'Test Gate',
      });

      await gateService.updateGates(mockInputData, mockDone);

      expect(mockModels.Gates.updateInstance).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should handle errors during gate update', async () => {
      const error = new Error('Database error');
      mockModels.Gates.updateInstance.mockRejectedValue(error);

      await gateService.updateGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('resolveDomainName', () => {
    it('should return domain name when enterprise exists', async () => {
      const inputData = {
        user: { domainName: 'testdomain' },
        body: {},
      };

      mockModels.Enterprise.findOne.mockResolvedValue({
        name: 'testdomain',
        id: 1,
      });

      const result = await gateService.resolveDomainName(inputData);

      expect(result).toBe('testdomain');
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' },
      });
    });

    it('should fallback to parent company when domain enterprise not found', async () => {
      const inputData = {
        user: { domainName: 'nonexistent' },
        body: { ParentCompanyId: 1 },
      };

      mockModels.Enterprise.findOne.mockResolvedValue(null);
      gateService.resolveDomainFromParentCompany = jest.fn().mockResolvedValue('parentdomain');

      const result = await gateService.resolveDomainName(inputData);

      expect(result).toBe('parentdomain');
      expect(gateService.resolveDomainFromParentCompany).toHaveBeenCalledWith(inputData);
    });

    it('should fallback to parent company when no domain name provided', async () => {
      const inputData = {
        user: { domainName: null },
        body: { ParentCompanyId: 1 },
      };

      gateService.resolveDomainFromParentCompany = jest.fn().mockResolvedValue('parentdomain');

      const result = await gateService.resolveDomainName(inputData);

      expect(result).toBe('parentdomain');
      expect(gateService.resolveDomainFromParentCompany).toHaveBeenCalledWith(inputData);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const dbError = new Error('Database connection failed');
      helper.getDynamicModel.mockRejectedValue(dbError);

      await gateService.addGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, dbError);
    });

    it('should handle invalid input data', async () => {
      const invalidInputData = { ...mockInputData, body: null };

      await gateService.addGates(invalidInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle missing required fields in addGates', async () => {
      const incompleteInputData = {
        ...mockInputData,
        body: {
          ProjectId: 1,
          // Missing gateName
        }
      };

      await gateService.addGates(incompleteInputData, mockDone);

      expect(helper.getDynamicModel).toHaveBeenCalledWith(incompleteInputData);
    });
  });

  describe('resolveDomainFromParentCompany', () => {
    beforeEach(() => {
      helper.returnProjectModel.mockResolvedValue({
        User: mockModels.User,
        Member: mockModels.Member,
      });
    });

    it('should return empty string when ParentCompanyId is undefined', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: {},
        params: {},
      };

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('');
    });

    it('should return empty string when ParentCompanyId is "undefined"', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: { ParentCompanyId: 'undefined' },
        params: {},
      };

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('');
    });

    it('should get domain from parent company when user not found', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: { ParentCompanyId: 1 },
        params: {},
      };

      await gateService.returnProjectModel();
      mockModels.User.findOne.mockResolvedValue(null);
      gateService.getDomainFromParentCompany = jest.fn().mockResolvedValue('parentdomain');

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('parentdomain');
      expect(gateService.getDomainFromParentCompany).toHaveBeenCalledWith(1);
    });

    it('should get domain from parent company when member not found', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: { ParentCompanyId: 1 },
        params: {},
      };

      await gateService.returnProjectModel();
      mockModels.User.findOne.mockResolvedValue({ id: 1 });
      mockModels.Member.findOne.mockResolvedValue(null);
      gateService.getDomainFromParentCompany = jest.fn().mockResolvedValue('parentdomain');

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('parentdomain');
      expect(gateService.getDomainFromParentCompany).toHaveBeenCalledWith(1);
    });

    it('should return enterprise name when member is account', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: { ParentCompanyId: 1 },
        params: {},
      };

      await gateService.returnProjectModel();
      mockModels.User.findOne.mockResolvedValue({ id: 1 });
      mockModels.Member.findOne.mockResolvedValue({
        isAccount: true,
        EnterpriseId: 1,
      });
      mockModels.Enterprise.findOne.mockResolvedValue({
        name: 'AccountDomain',
        status: 'completed',
      });

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('accountdomain');
    });

    it('should return empty string when enterprise not found for account member', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: { ParentCompanyId: 1 },
        params: {},
      };

      await gateService.returnProjectModel();
      mockModels.User.findOne.mockResolvedValue({ id: 1 });
      mockModels.Member.findOne.mockResolvedValue({
        isAccount: true,
        EnterpriseId: 1,
      });
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('');
    });

    it('should get domain from parent company when member is not account', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
        body: { ParentCompanyId: 1 },
        params: {},
      };

      await gateService.returnProjectModel();
      mockModels.User.findOne.mockResolvedValue({ id: 1 });
      mockModels.Member.findOne.mockResolvedValue({
        isAccount: false,
        EnterpriseId: 1,
      });
      gateService.getDomainFromParentCompany = jest.fn().mockResolvedValue('parentdomain');

      const result = await gateService.resolveDomainFromParentCompany(inputData);

      expect(result).toBe('parentdomain');
      expect(gateService.getDomainFromParentCompany).toHaveBeenCalledWith(1);
    });
  });

  describe('getDomainFromParentCompany', () => {
    it('should return enterprise name when found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue({
        name: 'ParentDomain',
        status: 'completed',
      });

      const result = await gateService.getDomainFromParentCompany(1);

      expect(result).toBe('parentdomain');
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' },
      });
    });

    it('should return empty string when enterprise not found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await gateService.getDomainFromParentCompany(1);

      expect(result).toBe('');
    });
  });

  describe('updateModelReferences', () => {
    it('should update model references correctly', async () => {
      const mockModelObj = {
        Gates: { test: 'gates' },
        Project: { test: 'project' },
        User: { test: 'user' },
      };

      await gateService.updateModelReferences(mockModelObj);

      // Since we can't directly test the module-level variables,
      // we verify the method executes without error
      expect(true).toBe(true);
    });
  });

  describe('updateUserIfNeeded', () => {
    it('should update user when domain name exists and user found', async () => {
      const inputData = {
        user: { email: '<EMAIL>', id: 1 },
      };
      const newUser = { email: '<EMAIL>', id: 2 };

      mockModels.User.findOne.mockResolvedValue(newUser);

      await gateService.updateUserIfNeeded(inputData, 'testdomain');

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(inputData.user).toEqual(newUser);
    });

    it('should not update user when domain name is null', async () => {
      const inputData = {
        user: { email: '<EMAIL>', id: 1 },
      };
      const originalUser = { ...inputData.user };

      await gateService.updateUserIfNeeded(inputData, null);

      expect(mockModels.User.findOne).not.toHaveBeenCalled();
      expect(inputData.user).toEqual(originalUser);
    });

    it('should not update user when user not found', async () => {
      const inputData = {
        user: { email: '<EMAIL>', id: 1 },
      };
      const originalUser = { ...inputData.user };

      mockModels.User.findOne.mockResolvedValue(null);

      await gateService.updateUserIfNeeded(inputData, 'testdomain');

      expect(inputData.user).toEqual(originalUser);
    });
  });

  describe('listGates', () => {
    beforeEach(() => {
      mockModels.Gates.getAll = jest.fn().mockResolvedValue({
        rows: [
          { id: 1, gateName: 'Gate A', gateAutoId: 1 },
          { id: 2, gateName: 'Gate B', gateAutoId: 2 },
        ],
        count: 2,
      });
    });

    it('should list gates with basic pagination', async () => {
      const listInputData = {
        ...mockInputData,
        params: {
          ...mockInputData.params,
          pageNo: '1',
          pageSize: '10',
        },
        body: {},
      };

      await gateService.listGates(listInputData, mockDone);

      expect(mockModels.Gates.getAll).toHaveBeenCalledWith(
        {
          ProjectId: listInputData.params.ProjectId,
          isDeleted: false,
        },
        10,
        0,
        {},
        undefined,
        undefined
      );
      expect(mockDone).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should list gates with showActivatedAlone filter', async () => {
      const listInputData = {
        ...mockInputData,
        body: { showActivatedAlone: true },
      };

      await gateService.listGates(listInputData, mockDone);

      expect(mockModels.Gates.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          isActive: true,
        }),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should search gates by text', async () => {
      const listInputData = {
        ...mockInputData,
        body: { search: 'Gate A' },
      };

      await gateService.listGates(listInputData, mockDone);

      expect(mockModels.Gates.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Number),
        expect.any(Number),
        expect.objectContaining({
          [mockModels.Sequelize.Op.and]: expect.any(Array),
        }),
        undefined,
        undefined
      );
    });

    it('should search gates by numeric value', async () => {
      const listInputData = {
        ...mockInputData,
        body: { search: '123' },
      };

      await gateService.listGates(listInputData, mockDone);

      expect(mockModels.Gates.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Number),
        expect.any(Number),
        expect.objectContaining({
          [mockModels.Sequelize.Op.and]: expect.any(Array),
        }),
        undefined,
        undefined
      );
    });

    it('should apply sorting when provided', async () => {
      const listInputData = {
        ...mockInputData,
        body: {
          sort: 'ASC',
          sortByField: 'gateName',
        },
      };

      await gateService.listGates(listInputData, mockDone);

      expect(mockModels.Gates.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Number),
        expect.any(Number),
        expect.any(Object),
        'ASC',
        'gateName'
      );
    });

    it('should sort results when isFilter is true with rows', async () => {
      const listInputData = {
        ...mockInputData,
        body: { isFilter: true },
      };

      mockModels.Gates.getAll.mockResolvedValue({
        rows: [
          { gateName: 'Gate Z' },
          { gateName: 'Gate A' },
        ],
      });

      await gateService.listGates(listInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        expect.objectContaining({
          rows: expect.arrayContaining([
            expect.objectContaining({ gateName: 'Gate A' }),
            expect.objectContaining({ gateName: 'Gate Z' }),
          ]),
        }),
        false
      );
    });

    it('should sort results when isFilter is true with array', async () => {
      const listInputData = {
        ...mockInputData,
        body: { isFilter: true },
      };

      mockModels.Gates.getAll.mockResolvedValue([
        { gateName: 'Gate Z' },
        { gateName: 'Gate A' },
      ]);

      await gateService.listGates(listInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ gateName: 'Gate A' }),
          expect.objectContaining({ gateName: 'Gate Z' }),
        ]),
        false
      );
    });

    it('should handle errors in listGates', async () => {
      const error = new Error('Database error');
      mockModels.Gates.getAll.mockRejectedValue(error);

      await gateService.listGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('deleteGates', () => {
    beforeEach(() => {
      mockModels.Gates.findAll.mockResolvedValue([
        { id: 1, gateName: 'Gate 1' },
        { id: 2, gateName: 'Gate 2' },
      ]);
      mockModels.DeliverGate.findOne.mockResolvedValue(null);
      mockModels.Gates.update.mockResolvedValue([1]);
    });

    it('should delete specific gates when not selectAll', async () => {
      const deleteInputData = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          id: [1, 2],
          isSelectAll: false,
        },
      };

      await gateService.deleteGates(deleteInputData, mockDone);

      expect(mockModels.Gates.findAll).toHaveBeenCalledWith({
        where: {
          ProjectId: deleteInputData.body.ProjectId,
          isDeleted: false,
          id: { [mockModels.Sequelize.Op.in]: [1, 2] },
        },
      });
    });

    it('should delete all gates when isSelectAll is true', async () => {
      const deleteInputData = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          isSelectAll: true,
        },
      };

      await gateService.deleteGates(deleteInputData, mockDone);

      expect(mockModels.Gates.findAll).toHaveBeenCalledWith({
        where: {
          ProjectId: deleteInputData.body.ProjectId,
          isDeleted: false,
        },
      });
    });

    it('should return error when gate is mapped to bookings', async () => {
      mockModels.DeliverGate.findOne.mockResolvedValue({
        id: 1,
        GateId: 1,
      });

      await gateService.deleteGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        null,
        expect.objectContaining({
          message: expect.stringContaining('cannot be deleted'),
        })
      );
    });

    it('should handle errors in deleteGates', async () => {
      const error = new Error('Database error');
      mockModels.Gates.findAll.mockRejectedValue(error);

      await gateService.deleteGates(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('lastGate', () => {
    it('should return next gate ID when last gate exists', async () => {
      const inputData = {
        params: { ProjectId: 1 },
      };

      mockModels.Gates.findOne.mockResolvedValue({
        gateAutoId: 5,
      });

      await gateService.lastGate(inputData, mockDone);

      expect(mockModels.Gates.findOne).toHaveBeenCalledWith({
        where: { ProjectId: 1, isDeleted: false },
        order: [['gateAutoId', 'DESC']],
      });
      expect(mockDone).toHaveBeenCalledWith({ id: 6 }, false);
    });

    it('should return 1 when no gates exist', async () => {
      const inputData = {
        params: { ProjectId: 1 },
      };

      mockModels.Gates.findOne.mockResolvedValue(null);

      await gateService.lastGate(inputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
    });

    it('should handle errors in lastGate', async () => {
      const error = new Error('Database error');
      mockModels.Gates.findOne.mockRejectedValue(error);

      await gateService.lastGate(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('gatesForBulkUploadDeliveryRequest', () => {
    it('should return active gates for bulk upload', async () => {
      const inputData = {
        params: { ProjectId: '1' },
        user: mockInputData.user,
        body: mockInputData.body,
      };

      const mockGates = [
        { id: 1, gateName: 'Gate 1' },
        { id: 2, gateName: 'Gate 2' },
      ];

      mockModels.Gates.findAll.mockResolvedValue(mockGates);

      const result = await gateService.gatesForBulkUploadDeliveryRequest(inputData);

      expect(mockModels.Gates.findAll).toHaveBeenCalledWith({
        where: {
          ProjectId: 1,
          isDeleted: false,
          isActive: true,
        },
        attributes: ['id', 'gateName'],
      });
      expect(result).toEqual(mockGates);
    });

    it('should handle errors in gatesForBulkUploadDeliveryRequest', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      const result = await gateService.gatesForBulkUploadDeliveryRequest(mockInputData);

      expect(result).toBeUndefined();
    });
  });

  describe('getMappedRequests', () => {
    beforeEach(() => {
      mockModels.Gates.findOne.mockResolvedValue({
        id: 1,
        gateName: 'Test Gate',
        gateAutoId: 1,
      });
      mockModels.DeliveryRequest.findAll.mockResolvedValue([
        { id: 1, description: 'Request 1' },
      ]);
      mockModels.Gates.findAll.mockResolvedValue([
        { id: 1, gateName: 'Gate 1' },
        { id: 2, gateName: 'Gate 2' },
      ]);
    });

    it('should get mapped requests successfully', async () => {
      const inputData = {
        ...mockInputData,
        body: {
          id: 1,
          ProjectId: 1,
        },
      };

      await gateService.getMappedRequests(inputData, mockDone);

      expect(mockModels.Gates.findOne).toHaveBeenCalledWith({
        where: {
          gateAutoId: 1,
          ProjectId: 1,
          isDeleted: false,
        },
      });
      expect(mockDone).toHaveBeenCalledWith(
        expect.objectContaining({
          mappedRequest: expect.any(Array),
          gates: expect.any(Array),
        }),
        false
      );
    });

    it('should return error when gate not found', async () => {
      mockModels.Gates.findOne.mockResolvedValue(null);

      await gateService.getMappedRequests(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
    });

    it('should handle errors in getMappedRequests', async () => {
      const error = new Error('Database error');
      mockModels.Gates.findOne.mockRejectedValue(error);

      await gateService.getMappedRequests(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('Enhanced deactivateGate scenarios', () => {
    beforeEach(() => {
      mockModels.Gates.findOne.mockResolvedValue({
        id: 1,
        gateName: 'Test Gate',
        ProjectId: 1,
      });
      mockModels.DeliveryRequest.findAll.mockResolvedValue([
        {
          id: 1,
          description: 'Request 1',
          gateDetails: [{ id: 1, Gate: { gateName: 'Test Gate', id: 1 } }],
        },
      ]);
      mockModels.DeliverHistory.createInstance = jest.fn().mockResolvedValue({});
      mockModels.DeliverGate.update.mockResolvedValue([1]);
      mockModels.DeliverGate.findAll.mockResolvedValue([]);
      mockModels.DeliverGate.createInstance = jest.fn().mockResolvedValue({});
    });

    it('should handle complex gate switching with mapped requests', async () => {
      const complexInputData = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          gateSwitchedRequests: [
            {
              id: 1,
              changedGateId: 2,
              DeliveryId: 'DEL001',
            },
          ],
        },
      };

      mockModels.Gates.findOne
        .mockResolvedValueOnce({
          id: 1,
          gateName: 'Test Gate',
          ProjectId: 1,
        })
        .mockResolvedValueOnce({
          id: 2,
          gateName: 'New Gate',
        });

      await gateService.deactivateGate(complexInputData, mockDone);

      expect(mockModels.DeliverHistory.createInstance).toHaveBeenCalled();
      expect(mockModels.DeliverGate.update).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(expect.any(Array), false);
    });

    it('should update existing gate mapping when switching gates', async () => {
      const complexInputData = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          gateSwitchedRequests: [
            {
              id: 1,
              changedGateId: 2,
              DeliveryId: 'DEL001',
            },
          ],
        },
      };

      mockModels.DeliverGate.findAll.mockResolvedValue([
        { id: 1, GateId: 2, DeliveryId: 1 },
      ]);

      mockModels.Gates.findOne
        .mockResolvedValueOnce({
          id: 1,
          gateName: 'Test Gate',
          ProjectId: 1,
        })
        .mockResolvedValueOnce({
          id: 2,
          gateName: 'New Gate',
        });

      await gateService.deactivateGate(complexInputData, mockDone);

      expect(mockModels.DeliverGate.update).toHaveBeenCalledWith(
        expect.objectContaining({
          GateId: 2,
          isActive: true,
        }),
        expect.any(Object)
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle null user in input data', async () => {
      const nullUserInputData = {
        ...mockInputData,
        user: null
      };

      await expect(gateService.getDynamicModel(nullUserInputData)).rejects.toThrow();
    });

    it('should handle empty gate switched requests array', async () => {
      const inputDataWithEmptyArray = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          gateSwitchedRequests: [],
        },
      };

      mockModels.Gates.findOne.mockResolvedValue({
        id: 1,
        gateName: 'Test Gate',
        ProjectId: 1,
      });
      mockModels.DeliveryRequest.findAll.mockResolvedValue([]);

      await gateService.deactivateGate(inputDataWithEmptyArray, mockDone);

      expect(mockModels.Gates.update).toHaveBeenCalled();
    });

    it('should handle pagination edge cases in listGates', async () => {
      const edgeCaseInputData = {
        ...mockInputData,
        params: {
          ...mockInputData.params,
          pageNo: '0',
          pageSize: '0',
        },
        body: {},
      };

      mockModels.Gates.getAll.mockResolvedValue({ rows: [], count: 0 });

      await gateService.listGates(edgeCaseInputData, mockDone);

      expect(mockModels.Gates.getAll).toHaveBeenCalledWith(
        expect.any(Object),
        0,
        0,
        expect.any(Object),
        undefined,
        undefined
      );
    });

    it('should handle empty search results in listGates', async () => {
      const searchInputData = {
        ...mockInputData,
        body: { search: 'nonexistent' },
      };

      mockModels.Gates.getAll.mockResolvedValue({ rows: [], count: 0 });

      await gateService.listGates(searchInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith({ rows: [], count: 0 }, false);
    });
  });
});
