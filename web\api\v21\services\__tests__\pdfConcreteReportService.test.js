// Mock dependencies first before requiring modules
jest.mock('fs');
jest.mock('../../middlewares/awsConfig');
jest.mock('puppeteer', () => ({
    launch: jest.fn(),
}));
jest.mock('../puppeteerService');
jest.mock('../../models', () => ({
    Project: {
        findOne: jest.fn(),
    },
    Company: {
        findOne: jest.fn(),
    },
}));
jest.mock('moment', () => {
    const mockMomentInstance = {
        format: jest.fn().mockReturnValue('03/15/2024 12:30 PM'),
        add: jest.fn().mockReturnThis(),
    };
    return jest.fn(() => mockMomentInstance);
});

const fs = require('fs');
const moment = require('moment');
const pdfConcreteReportService = require('../pdfConcreteReportService');
const puppeteerService = require('../puppeteerService');
const awsConfig = require('../../middlewares/awsConfig');
const { Project, Company } = require('../../models');

describe('pdfConcreteReportService', () => {
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock fs.readFileSync
        fs.readFileSync.mockReturnValue(
            '$projectName $companyName $generatedDate $generatedBy $reportType $header $data'
        );

        // Mock puppeteerService
        puppeteerService.generatePdfBuffer.mockResolvedValue(Buffer.from('mock-pdf'));

        // Mock awsConfig
        awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
            callback('success-url', null);
        });
    });

    describe('extractConcreteHeaderSelection', () => {
        it('should extract headers and flags correctly for all active headers', () => {
            const mockReq = {
                body: {
                    selectedHeaders: [
                        { key: 'id', title: 'ID', isActive: true },
                        { key: 'description', title: 'Description', isActive: true },
                        { key: 'date', title: 'Date', isActive: true },
                        { key: 'status', title: 'Status', isActive: true },
                        { key: 'approvedBy', title: 'Approved By', isActive: true },
                        { key: 'company', title: 'Company', isActive: true },
                        { key: 'orderNumber', title: 'Order Number', isActive: true },
                        { key: 'slump', title: 'Slump', isActive: true },
                        { key: 'truckSpacing', title: 'Truck Spacing', isActive: true },
                        { key: 'primer', title: 'Primer', isActive: true },
                        { key: 'name', title: 'Person', isActive: true },
                        { key: 'quantity', title: 'Quantity', isActive: true },
                        { key: 'mixDesign', title: 'Mix Design', isActive: true },
                        { key: 'location', title: 'Location', isActive: true }
                    ]
                }
            };

            const result = pdfConcreteReportService.extractConcreteHeaderSelection(mockReq);

            expect(result.flags.isIdSelected).toBe(true);
            expect(result.flags.isDescriptionSelected).toBe(true);
            expect(result.flags.isDateSelected).toBe(true);
            expect(result.flags.isStatusSelected).toBe(true);
            expect(result.flags.isApprovedBySelected).toBe(true);
            expect(result.flags.isCompanySelected).toBe(true);
            expect(result.flags.isOrderNumberSelected).toBe(true);
            expect(result.flags.isSlumpSelected).toBe(true);
            expect(result.flags.isTruckSpacingSelected).toBe(true);
            expect(result.flags.isPrimerOrderedSelected).toBe(true);
            expect(result.flags.isPersonSelected).toBe(true);
            expect(result.flags.isQuantityOrderedSelected).toBe(true);
            expect(result.flags.isMixDesignSelected).toBe(true);
            expect(result.flags.isLocationSelected).toBe(true);

            expect(result.header).toHaveLength(14);
            expect(result.header[0]).toBe('<th style="text-align:center">ID</th>');
        });

        it('should handle inactive headers correctly', () => {
            const mockReq = {
                body: {
                    selectedHeaders: [
                        { key: 'id', title: 'ID', isActive: true },
                        { key: 'description', title: 'Description', isActive: false },
                        { key: 'date', title: 'Date', isActive: true }
                    ]
                }
            };

            const result = pdfConcreteReportService.extractConcreteHeaderSelection(mockReq);

            expect(result.flags.isIdSelected).toBe(true);
            expect(result.flags.isDescriptionSelected).toBe(false);
            expect(result.flags.isDateSelected).toBe(true);
            expect(result.header).toHaveLength(2);
        });

        it('should handle empty selectedHeaders array', () => {
            const mockReq = {
                body: {
                    selectedHeaders: []
                }
            };

            const result = pdfConcreteReportService.extractConcreteHeaderSelection(mockReq);

            expect(Object.values(result.flags).every(flag => flag === false)).toBe(true);
            expect(result.header).toHaveLength(0);
        });

        it('should handle special key mappings correctly', () => {
            const mockReq = {
                body: {
                    selectedHeaders: [
                        { key: 'name', title: 'Person Name', isActive: true },
                        { key: 'mixDesign', title: 'Mix Design', isActive: true },
                        { key: 'orderNumber', title: 'Order #', isActive: true },
                        { key: 'truckSpacing', title: 'Truck Spacing', isActive: true },
                        { key: 'primer', title: 'Primer Ordered', isActive: true },
                        { key: 'quantity', title: 'Quantity Ordered', isActive: true }
                    ]
                }
            };

            const result = pdfConcreteReportService.extractConcreteHeaderSelection(mockReq);

            expect(result.flags.isPersonSelected).toBe(true);
            expect(result.flags.isMixDesignSelected).toBe(true);
            expect(result.flags.isOrderNumberSelected).toBe(true);
            expect(result.flags.isTruckSpacingSelected).toBe(true);
            expect(result.flags.isPrimerOrderedSelected).toBe(true);
            expect(result.flags.isQuantityOrderedSelected).toBe(true);
        });
    });

    describe('buildConcreteRows', () => {
        const mockFlags = {
            isIdSelected: true,
            isDescriptionSelected: true,
            isDateSelected: false,
            isStatusSelected: false,
            isApprovedBySelected: false,
            isCompanySelected: false,
            isOrderNumberSelected: false,
            isSlumpSelected: false,
            isTruckSpacingSelected: false,
            isPrimerOrderedSelected: false,
            isPersonSelected: false,
            isQuantityOrderedSelected: false,
            isMixDesignSelected: false,
            isLocationSelected: false
        };

        const mockData = [
            {
                ConcreteRequestId: 1,
                description: 'Test Description 1',
                concretePlacementStart: '2024-03-15T10:00:00Z',
                concretePlacementEnd: '2024-03-15T12:00:00Z'
            },
            {
                ConcreteRequestId: 2,
                description: 'Test Description 2',
                concretePlacementStart: '2024-03-15T14:00:00Z',
                concretePlacementEnd: '2024-03-15T16:00:00Z'
            }
        ];

        it('should build rows correctly with selected flags', () => {
            const result = pdfConcreteReportService.buildConcreteRows(mockData, mockFlags, 0);

            expect(result).toHaveLength(2);
            expect(result[0]).toContain('<tr style="border-bottom: 1px solid #e0e0e0; font-size: 12px">');
            expect(result[0]).toContain('Test Description 1');
            expect(result[1]).toContain('Test Description 2');
        });

        it('should handle empty data array', () => {
            const result = pdfConcreteReportService.buildConcreteRows([], mockFlags, 0);

            expect(result).toHaveLength(0);
        });

        it('should handle timezone offset correctly', () => {
            const result = pdfConcreteReportService.buildConcreteRows(mockData, mockFlags, -240);

            expect(result).toHaveLength(2);
            expect(moment().add).toHaveBeenCalledWith(-240, 'minutes');
        });
    });

    describe('buildConcreteRow', () => {
        const mockItem = {
            ConcreteRequestId: 1,
            description: 'Test Description',
            concretePlacementStart: '2024-03-15T10:00:00Z',
            concretePlacementEnd: '2024-03-15T12:00:00Z',
            status: 'Approved',
            approverDetails: {
                User: { firstName: 'John', lastName: 'Doe' }
            },
            concreteSupplierDetails: [
                { Company: { companyName: 'Supplier 1' } },
                { Company: { companyName: 'Supplier 2' } }
            ],
            concreteOrderNumber: 'ORD-001',
            slump: '4-6',
            truckSpacingHours: '2',
            primerForPump: 'Yes',
            memberDetails: [
                { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
            ],
            concreteQuantityOrdered: '10',
            mixDesignDetails: [
                { ConcreteMixDesign: { mixDesign: 'Mix A' } }
            ],
            location: { locationPath: 'Building A' }
        };

        const mockFlags = {
            isIdSelected: true,
            isDescriptionSelected: true,
            isDateSelected: true,
            isStatusSelected: true,
            isApprovedBySelected: true,
            isCompanySelected: true,
            isOrderNumberSelected: true,
            isSlumpSelected: true,
            isTruckSpacingSelected: true,
            isPrimerOrderedSelected: true,
            isPersonSelected: true,
            isQuantityOrderedSelected: true,
            isMixDesignSelected: true,
            isLocationSelected: true
        };

        const mockTd = (val) => `<td>${val}</td>`;
        const mockWrapList = (list) => list.map(val => `<p>${val}</p>`).join('') || '-';

        it('should build complete row with all fields selected', () => {
            const result = pdfConcreteReportService.buildConcreteRow(
                mockItem, mockFlags, 0, mockTd, mockWrapList
            );

            expect(result).toContain('<td>1</td>'); // ID
            expect(result).toContain('<td>Test Description</td>'); // Description
            expect(result).toContain('<td>Approved</td>'); // Status
            expect(result).toContain('<td>John Doe</td>'); // Approver
            expect(result).toContain('<td>ORD-001</td>'); // Order Number
            expect(result).toContain('<td>4-6</td>'); // Slump
            expect(result).toContain('<td>2</td>'); // Truck Spacing
            expect(result).toContain('<td>Yes</td>'); // Primer
            expect(result).toContain('<td>10</td>'); // Quantity
            expect(result).toContain('<td>Building A</td>'); // Location
        });

        it('should handle missing optional fields', () => {
            const itemWithMissingFields = {
                ConcreteRequestId: 1,
                description: 'Test Description',
                concretePlacementStart: '2024-03-15T10:00:00Z',
                concretePlacementEnd: '2024-03-15T12:00:00Z',
                status: 'Pending'
            };

            const result = pdfConcreteReportService.buildConcreteRow(
                itemWithMissingFields, mockFlags, 0, mockTd, mockWrapList
            );

            expect(result).toContain('<td>1</td>');
            expect(result).toContain('<td>Test Description</td>');
            expect(result).toContain('<td>Pending</td>');
            expect(result).toContain('<td>-</td>'); // Missing approver
        });
    });

    describe('Individual cell building methods', () => {
        const mockTd = (val) => `<td>${val}</td>`;
        const mockWrapList = (list) => list.map(val => `<p>${val}</p>`).join('') || '-';

        describe('buildIdCell', () => {
            it('should return ID cell when flag is selected', () => {
                const item = { ConcreteRequestId: 123 };
                const flags = { isIdSelected: true };

                const result = pdfConcreteReportService.buildIdCell(item, flags, mockTd);

                expect(result).toBe('<td>123</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { ConcreteRequestId: 123 };
                const flags = { isIdSelected: false };

                const result = pdfConcreteReportService.buildIdCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildDescriptionCell', () => {
            it('should return description cell when flag is selected', () => {
                const item = { description: 'Test Description' };
                const flags = { isDescriptionSelected: true };

                const result = pdfConcreteReportService.buildDescriptionCell(item, flags, mockTd);

                expect(result).toBe('<td>Test Description</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { description: 'Test Description' };
                const flags = { isDescriptionSelected: false };

                const result = pdfConcreteReportService.buildDescriptionCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildDateCell', () => {
            it('should return formatted date cell when flag is selected', () => {
                const mockStart = { format: jest.fn().mockReturnValue('03/15/2024 10:00 AM') };
                const mockEnd = { format: jest.fn().mockReturnValue('12:00 PM') };
                const flags = { isDateSelected: true };

                const result = pdfConcreteReportService.buildDateCell(mockStart, mockEnd, flags, mockTd);

                expect(result).toBe('<td>03/15/2024 10:00 AM - 12:00 PM</td>');
                expect(mockStart.format).toHaveBeenCalledWith('MM/DD/YYYY hh:mm a');
                expect(mockEnd.format).toHaveBeenCalledWith('hh:mm a');
            });

            it('should return empty string when flag is not selected', () => {
                const mockStart = { format: jest.fn() };
                const mockEnd = { format: jest.fn() };
                const flags = { isDateSelected: false };

                const result = pdfConcreteReportService.buildDateCell(mockStart, mockEnd, flags, mockTd);

                expect(result).toBe('');
                expect(mockStart.format).not.toHaveBeenCalled();
                expect(mockEnd.format).not.toHaveBeenCalled();
            });
        });

        describe('buildStatusCell', () => {
            it('should return status cell when flag is selected', () => {
                const item = { status: 'Approved' };
                const flags = { isStatusSelected: true };

                const result = pdfConcreteReportService.buildStatusCell(item, flags, mockTd);

                expect(result).toBe('<td>Approved</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { status: 'Approved' };
                const flags = { isStatusSelected: false };

                const result = pdfConcreteReportService.buildStatusCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildApprovedByCell', () => {
            it('should return approver name when flag is selected and approver exists', () => {
                const item = {
                    approverDetails: {
                        User: { firstName: 'John', lastName: 'Doe' }
                    }
                };
                const flags = { isApprovedBySelected: true };

                const result = pdfConcreteReportService.buildApprovedByCell(item, flags, mockTd);

                expect(result).toBe('<td>John Doe</td>');
            });

            it('should return dash when flag is selected but no approver', () => {
                const item = { approverDetails: null };
                const flags = { isApprovedBySelected: true };

                const result = pdfConcreteReportService.buildApprovedByCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = {
                    approverDetails: {
                        User: { firstName: 'John', lastName: 'Doe' }
                    }
                };
                const flags = { isApprovedBySelected: false };

                const result = pdfConcreteReportService.buildApprovedByCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildCompanyCell', () => {
            it('should return company names when flag is selected and companies exist', () => {
                const item = {
                    concreteSupplierDetails: [
                        { Company: { companyName: 'Company A' } },
                        { Company: { companyName: 'Company B' } }
                    ]
                };
                const flags = { isCompanySelected: true };

                const result = pdfConcreteReportService.buildCompanyCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('<td><p>Company A</p><p>Company B</p></td>');
            });

            it('should return dash when flag is selected but no companies', () => {
                const item = { concreteSupplierDetails: [] };
                const flags = { isCompanySelected: true };

                const result = pdfConcreteReportService.buildCompanyCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = {
                    concreteSupplierDetails: [
                        { Company: { companyName: 'Company A' } }
                    ]
                };
                const flags = { isCompanySelected: false };

                const result = pdfConcreteReportService.buildCompanyCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('');
            });
        });

        describe('buildOrderNumberCell', () => {
            it('should return order number when flag is selected and order exists', () => {
                const item = { concreteOrderNumber: 'ORD-123' };
                const flags = { isOrderNumberSelected: true };

                const result = pdfConcreteReportService.buildOrderNumberCell(item, flags, mockTd);

                expect(result).toBe('<td>ORD-123</td>');
            });

            it('should return dash when flag is selected but no order number', () => {
                const item = { concreteOrderNumber: null };
                const flags = { isOrderNumberSelected: true };

                const result = pdfConcreteReportService.buildOrderNumberCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { concreteOrderNumber: 'ORD-123' };
                const flags = { isOrderNumberSelected: false };

                const result = pdfConcreteReportService.buildOrderNumberCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildSlumpCell', () => {
            it('should return slump when flag is selected and slump exists', () => {
                const item = { slump: '4-6' };
                const flags = { isSlumpSelected: true };

                const result = pdfConcreteReportService.buildSlumpCell(item, flags, mockTd);

                expect(result).toBe('<td>4-6</td>');
            });

            it('should return dash when flag is selected but no slump', () => {
                const item = { slump: null };
                const flags = { isSlumpSelected: true };

                const result = pdfConcreteReportService.buildSlumpCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { slump: '4-6' };
                const flags = { isSlumpSelected: false };

                const result = pdfConcreteReportService.buildSlumpCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildTruckSpacingCell', () => {
            it('should return truck spacing when flag is selected and spacing exists', () => {
                const item = { truckSpacingHours: '2' };
                const flags = { isTruckSpacingSelected: true };

                const result = pdfConcreteReportService.buildTruckSpacingCell(item, flags, mockTd);

                expect(result).toBe('<td>2</td>');
            });

            it('should return dash when flag is selected but no spacing', () => {
                const item = { truckSpacingHours: null };
                const flags = { isTruckSpacingSelected: true };

                const result = pdfConcreteReportService.buildTruckSpacingCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { truckSpacingHours: '2' };
                const flags = { isTruckSpacingSelected: false };

                const result = pdfConcreteReportService.buildTruckSpacingCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildPrimerCell', () => {
            it('should return primer info when flag is selected and primer exists', () => {
                const item = { primerForPump: 'Yes' };
                const flags = { isPrimerOrderedSelected: true };

                const result = pdfConcreteReportService.buildPrimerCell(item, flags, mockTd);

                expect(result).toBe('<td>Yes</td>');
            });

            it('should return dash when flag is selected but no primer info', () => {
                const item = { primerForPump: null };
                const flags = { isPrimerOrderedSelected: true };

                const result = pdfConcreteReportService.buildPrimerCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { primerForPump: 'Yes' };
                const flags = { isPrimerOrderedSelected: false };

                const result = pdfConcreteReportService.buildPrimerCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildPersonCell', () => {
            it('should return person names when flag is selected and members exist', () => {
                const item = {
                    memberDetails: [
                        { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
                        { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
                    ]
                };
                const flags = { isPersonSelected: true };

                const result = pdfConcreteReportService.buildPersonCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('<td><p>John Doe</p><p>Jane Smith</p></td>');
            });

            it('should return dash when flag is selected but no members', () => {
                const item = { memberDetails: [] };
                const flags = { isPersonSelected: true };

                const result = pdfConcreteReportService.buildPersonCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = {
                    memberDetails: [
                        { Member: { User: { firstName: 'John', lastName: 'Doe' } } }
                    ]
                };
                const flags = { isPersonSelected: false };

                const result = pdfConcreteReportService.buildPersonCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('');
            });
        });

        describe('buildQuantityCell', () => {
            it('should return quantity when flag is selected and quantity exists', () => {
                const item = { concreteQuantityOrdered: '10' };
                const flags = { isQuantityOrderedSelected: true };

                const result = pdfConcreteReportService.buildQuantityCell(item, flags, mockTd);

                expect(result).toBe('<td>10</td>');
            });

            it('should return dash when flag is selected but no quantity', () => {
                const item = { concreteQuantityOrdered: null };
                const flags = { isQuantityOrderedSelected: true };

                const result = pdfConcreteReportService.buildQuantityCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { concreteQuantityOrdered: '10' };
                const flags = { isQuantityOrderedSelected: false };

                const result = pdfConcreteReportService.buildQuantityCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });

        describe('buildMixDesignCell', () => {
            it('should return mix designs when flag is selected and designs exist', () => {
                const item = {
                    mixDesignDetails: [
                        { ConcreteMixDesign: { mixDesign: 'Mix A' } },
                        { ConcreteMixDesign: { mixDesign: 'Mix B' } }
                    ]
                };
                const flags = { isMixDesignSelected: true };

                const result = pdfConcreteReportService.buildMixDesignCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('<td><p>Mix A</p><p>Mix B</p></td>');
            });

            it('should return dash when flag is selected but no designs', () => {
                const item = { mixDesignDetails: [] };
                const flags = { isMixDesignSelected: true };

                const result = pdfConcreteReportService.buildMixDesignCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = {
                    mixDesignDetails: [
                        { ConcreteMixDesign: { mixDesign: 'Mix A' } }
                    ]
                };
                const flags = { isMixDesignSelected: false };

                const result = pdfConcreteReportService.buildMixDesignCell(item, flags, mockTd, mockWrapList);

                expect(result).toBe('');
            });
        });

        describe('buildLocationCell', () => {
            it('should return location when flag is selected and location exists', () => {
                const item = { location: { locationPath: 'Building A' } };
                const flags = { isLocationSelected: true };

                const result = pdfConcreteReportService.buildLocationCell(item, flags, mockTd);

                expect(result).toBe('<td>Building A</td>');
            });

            it('should return dash when flag is selected but no location', () => {
                const item = { location: null };
                const flags = { isLocationSelected: true };

                const result = pdfConcreteReportService.buildLocationCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return dash when flag is selected but location has no path', () => {
                const item = { location: {} };
                const flags = { isLocationSelected: true };

                const result = pdfConcreteReportService.buildLocationCell(item, flags, mockTd);

                expect(result).toBe('<td>-</td>');
            });

            it('should return empty string when flag is not selected', () => {
                const item = { location: { locationPath: 'Building A' } };
                const flags = { isLocationSelected: false };

                const result = pdfConcreteReportService.buildLocationCell(item, flags, mockTd);

                expect(result).toBe('');
            });
        });
    });

    describe('generateConcretePdfTemplate', () => {
        const mockProjectData = { projectName: 'Test Project' };
        const mockCompanyData = { companyName: 'Test Company' };
        const mockLoginUser = { firstName: 'John', lastName: 'Doe' };
        const mockReq = { body: { generatedDate: '2024-03-15' } };
        const mockHeader = ['<th>Header 1</th>', '<th>Header 2</th>'];
        const mockContent = ['<tr>Row 1</tr>', '<tr>Row 2</tr>'];

        it('should replace all template placeholders correctly', () => {
            const templatePath = '/test/template.html';

            const result = pdfConcreteReportService.generateConcretePdfTemplate(
                templatePath, mockProjectData, mockCompanyData, mockLoginUser, mockReq, mockHeader, mockContent
            );

            expect(fs.readFileSync).toHaveBeenCalledWith(templatePath, 'utf-8');
            expect(result).toBe('Test Project Test Company 2024-03-15 John Doe Concrete <th>Header 1</th><th>Header 2</th> <tr>Row 1</tr><tr>Row 2</tr>');
        });

        it('should handle empty header and content arrays', () => {
            const templatePath = '/test/template.html';

            const result = pdfConcreteReportService.generateConcretePdfTemplate(
                templatePath, mockProjectData, mockCompanyData, mockLoginUser, mockReq, [], []
            );

            expect(result).toBe('Test Project Test Company 2024-03-15 John Doe Concrete  ');
        });
    });

    describe('pdfFormatOfConcreteRequest', () => {
        const mockParams = { ProjectId: 1 };
        const mockLoginUser = { firstName: 'John', lastName: 'Doe' };
        const mockData = [
            {
                ConcreteRequestId: 1,
                description: 'Test Description',
                concretePlacementStart: '2024-03-15T10:00:00Z',
                concretePlacementEnd: '2024-03-15T12:00:00Z',
                status: 'Approved'
            }
        ];
        const mockReq = {
            headers: { timezoneoffset: 0 },
            body: {
                ParentCompanyId: 1,
                selectedHeaders: [
                    { key: 'id', title: 'ID', isActive: true },
                    { key: 'description', title: 'Description', isActive: true }
                ],
                generatedDate: '2024-03-15',
                reportName: 'test-report',
                exportType: 'pdf'
            }
        };

        beforeEach(() => {
            Project.findOne.mockResolvedValue({ projectName: 'Test Project' });
            Company.findOne.mockResolvedValue({ companyName: 'Test Company' });
        });

        it('should generate PDF successfully', (done) => {
            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBe(false);
                    expect(result).toBe('success-url');
                    expect(Project.findOne).toHaveBeenCalledWith({
                        where: { isDeleted: false, id: 1 },
                        attributes: ['projectName']
                    });
                    expect(Company.findOne).toHaveBeenCalledWith({
                        where: { isDeleted: false, ParentCompanyId: 1, isParent: true },
                        attributes: ['companyName']
                    });
                    expect(puppeteerService.generatePdfBuffer).toHaveBeenCalled();
                    expect(awsConfig.reportUpload).toHaveBeenCalledWith(
                        Buffer.from('mock-pdf'),
                        'test-report',
                        'pdf',
                        expect.any(Function)
                    );
                    done();
                }
            );
        });

        it('should handle project not found', (done) => {
            Project.findOne.mockResolvedValue(null);

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBeTruthy();
                    expect(error.message).toBe('Unexpected error during PDF generation');
                    expect(result).toBe(false);
                    done();
                }
            );
        });

        it('should handle company not found', (done) => {
            Company.findOne.mockResolvedValue(null);

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBeTruthy();
                    expect(error.message).toBe('Unexpected error during PDF generation');
                    expect(result).toBe(false);
                    done();
                }
            );
        });

        it('should handle PDF generation failure', (done) => {
            puppeteerService.generatePdfBuffer.mockResolvedValue(null);

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBeTruthy();
                    expect(error.message).toBe('Cannot export the document');
                    expect(result).toBe(false);
                    done();
                }
            );
        });

        it('should handle AWS upload failure', (done) => {
            awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
                callback(null, 'Upload error');
            });

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBeTruthy();
                    expect(error.message).toBe('Upload failed');
                    expect(result).toBe(false);
                    done();
                }
            );
        });

        it('should handle database errors', (done) => {
            Project.findOne.mockRejectedValue(new Error('Database connection failed'));

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBeTruthy();
                    expect(error.message).toBe('Unexpected error during PDF generation');
                    expect(error.error).toBe('Database connection failed');
                    expect(result).toBe(false);
                    done();
                }
            );
        });

        it('should handle puppeteer service errors', (done) => {
            puppeteerService.generatePdfBuffer.mockRejectedValue(new Error('Puppeteer failed'));

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                (result, error) => {
                    expect(error).toBeTruthy();
                    expect(error.message).toBe('Unexpected error during PDF generation');
                    expect(error.error).toBe('Puppeteer failed');
                    expect(result).toBe(false);
                    done();
                }
            );
        });

        it('should handle empty data array', (done) => {
            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                [],
                mockReq,
                (result, error) => {
                    expect(error).toBe(false);
                    expect(result).toBe('success-url');
                    done();
                }
            );
        });

        it('should handle different timezone offsets', (done) => {
            const reqWithTimezone = {
                ...mockReq,
                headers: { timezoneoffset: -240 }
            };

            pdfConcreteReportService.pdfFormatOfConcreteRequest(
                mockParams,
                mockLoginUser,
                mockData,
                reqWithTimezone,
                (result, error) => {
                    expect(error).toBe(false);
                    expect(result).toBe('success-url');
                    expect(moment().add).toHaveBeenCalledWith(-240, 'minutes');
                    done();
                }
            );
        });
    });
});