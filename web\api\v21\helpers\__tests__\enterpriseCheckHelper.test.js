const enterpriseCheck = require('../enterpriseCheckHelper');
const models = require('../../models');

jest.mock('../../models', () => ({
  User: { findOne: jest.fn() },
  Member: { findOne: jest.fn() },
  Enterprise: { findOne: jest.fn() },
  Sequelize: {
    Op: { and: 'and', ne: 'ne' },
    and: jest.fn(),
    fn: jest.fn(),
    col: jest.fn(),
    where: jest.fn(),
  },
}));

describe('enterpriseCheckHelper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('findDomainEnterprise returns domain if found', async () => {
    models.Enterprise.findOne.mockResolvedValue({ name: 'foo' });
    const result = await enterpriseCheck.findDomainEnterprise('foo');
    expect(result).toBe('foo');
  });
  it('findDomainEnterprise returns empty if not found', async () => {
    models.Enterprise.findOne.mockResolvedValue(null);
    const result = await enterpriseCheck.findDomainEnterprise('foo');
    expect(result).toBe('');
  });
  it('findUserByEmail returns user if found', async () => {
    models.User.findOne.mockResolvedValue({ id: 1 });
    const result = await enterpriseCheck.findUserByEmail('<EMAIL>');
    expect(result).toEqual({ id: 1 });
  });
  it('findMemberByUserId returns member if found', async () => {
    models.Member.findOne.mockResolvedValue({ id: 2 });
    const result = await enterpriseCheck.findMemberByUserId(2);
    expect(result).toEqual({ id: 2 });
  });
  it('findEnterpriseByParentCompanyId returns enterprise if found', async () => {
    models.Enterprise.findOne.mockResolvedValue({ id: 3 });
    const result = await enterpriseCheck.findEnterpriseByParentCompanyId(1);
    expect(result).toEqual({ id: 3 });
  });
  it('findEnterpriseByMember returns enterprise if isAccount', async () => {
    models.Enterprise.findOne.mockResolvedValue({ id: 4 });
    const result = await enterpriseCheck.findEnterpriseByMember(
      { isAccount: true, EnterpriseId: 4 },
      1,
    );
    expect(result).toEqual({ id: 4 });
  });
  it('findEnterpriseByMember returns by parent if not isAccount', async () => {
    models.Enterprise.findOne.mockResolvedValue({ id: 5 });
    const result = await enterpriseCheck.findEnterpriseByMember({ isAccount: false }, 2);
    expect(result).toEqual({ id: 5 });
  });
  it('checkEnterPrise returns domainName if found', async () => {
    enterpriseCheck.findDomainEnterprise = jest.fn().mockResolvedValue('foo');
    const inputData = { user: { domainName: 'foo', email: '<EMAIL>' }, body: {}, params: {} };
    const result = await enterpriseCheck.checkEnterPrise(inputData);
    expect(result).toBe('foo');
  });
  it('checkEnterPrise returns domainName from enterprise if not found initially', async () => {
    enterpriseCheck.findDomainEnterprise = jest
      .fn()
      .mockResolvedValueOnce('')
      .mockResolvedValueOnce('bar');
    enterpriseCheck.findUserByEmail = jest.fn().mockResolvedValue({ id: 1 });
    enterpriseCheck.findMemberByUserId = jest.fn().mockResolvedValue({ isAccount: false });
    enterpriseCheck.findEnterpriseByMember = jest.fn().mockResolvedValue({ name: 'bar' });
    const inputData = {
      user: { domainName: 'foo', email: '<EMAIL>' },
      body: { ParentCompanyId: 2 },
      params: {},
    };
    const result = await enterpriseCheck.checkEnterPrise(inputData);
    expect(result).toBe('bar');
  });
});
