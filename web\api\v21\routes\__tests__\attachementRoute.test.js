const { Router } = require('express');
const multer = require('multer');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const mockMulter = jest.fn(() => ({
    array: jest.fn(() => 'mocked-multer-middleware'),
  }));
  mockMulter.memoryStorage = jest.fn(() => 'mocked-storage');
  return mockMulter;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  AttachementController: {
    createAttachement: jest.fn(),
    createInspectionAttachement: jest.fn(),
    deleteAttachement: jest.fn(),
    deleteInspectionAttachement: jest.fn(),
    getAttachement: jest.fn(),
    getInspectionAttachement: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  attachementValidation: {
    createAttachement: jest.fn(),
    createInspectionAttachement: jest.fn(),
    deleteAttachement: jest.fn(),
    getAttachement: jest.fn(),
  },
}));

describe('attachementRoute', () => {
  let router;
  let attachementRoute;
  let AttachementController;
  let passportConfig;
  let attachementValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    attachementRoute = require('../attachementRoute');
    const controllers = require('../../controllers');
    AttachementController = controllers.AttachementController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    attachementValidation = validations.attachementValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = attachementRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer.memoryStorage).toHaveBeenCalled();
      expect(multer).toHaveBeenCalledWith({ storage: 'mocked-storage' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(2);
      expect(router.get).toHaveBeenCalledTimes(4);

      // Verify POST routes
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/add_attachement/:DeliveryRequestId/?:ParentCompanyId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        AttachementController.createAttachement,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/add_inspection_attachement/:InspectionRequestId/?:ParentCompanyId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        AttachementController.createInspectionAttachement,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/remove_attachement/:id/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        AttachementController.deleteAttachement,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/remove_inspection_attachement/:id/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        AttachementController.deleteInspectionAttachement,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/get_attachement/:DeliveryRequestId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        AttachementController.getAttachement,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        4,
        '/get_inspection_attachement/:InspectionRequestId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        AttachementController.getInspectionAttachement,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(5);
      expect(validate).toHaveBeenCalledWith(
        attachementValidation.createAttachement,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        attachementValidation.createInspectionAttachement,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        attachementValidation.deleteAttachement,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        attachementValidation.getAttachement,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = attachementRoute.router;
      const result2 = attachementRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes with proper middleware chains', () => {
      attachementRoute.router;

      // Verify POST routes have multer, validation, and authentication
      const postCalls = router.post.mock.calls;
      postCalls.forEach(call => {
        expect(call).toHaveLength(5); // path + multer + validation + auth + controller
      });

      // Verify most GET routes have validation and authentication
      const getCalls = router.get.mock.calls;
      expect(getCalls[0]).toHaveLength(4); // path + validation + auth + controller
      expect(getCalls[1]).toHaveLength(4); // path + validation + auth + controller
      expect(getCalls[2]).toHaveLength(4); // path + validation + auth + controller
      expect(getCalls[3]).toHaveLength(3); // path + auth + controller (no validation)
    });

    it('should use authentication middleware for all routes', () => {
      attachementRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof attachementRoute).toBe('object');
      expect(attachementRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(attachementRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(attachementRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
