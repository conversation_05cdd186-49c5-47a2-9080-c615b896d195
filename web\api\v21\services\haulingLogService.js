const status = require('http-status');
const {
    Sequelize,
    HaulingLog
} = require('../models');

const haulingLogService = {
    async addHaulingLog(equData, done) {
        try {
            const inputData = equData.body;
            const newEquipment = await HaulingLog.createHaulingLog(inputData);
            done(newEquipment, false);
        } catch (e) {
            done(null, e);
        }
    },
    async listHaulingLog(inputData, done) {
        try {
            const { params } = inputData;
            let searchCondition = {};
            const pageNumber = +params.pageNo;
            const pageSize = +params.pageSize;
            const { sort } = inputData.body;
            const { sortByField } = inputData.body;
            const offset = (pageNumber - 1) * pageSize;
            const condition = {
                isDeleted: false,
                projectId: inputData.query.projectId,
            };

            const equipmentData = await HaulingLog.getAll(
                condition,
                pageSize,
                offset,
                searchCondition,
                sort,
                sortByField,
            );

            done(equipmentData, false);
        } catch (e) {
            done(null, e);
        }
    },
};
module.exports = haulingLogService;