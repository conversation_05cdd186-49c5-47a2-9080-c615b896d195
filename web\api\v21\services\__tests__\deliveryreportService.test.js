const deliveryreportService = require('../deliveryreportService');
const helper = require('../../helpers/domainHelper');

// Mock all dependencies
jest.mock('moment', () => {
  const moment = jest.requireActual('moment');
  return moment;
});

jest.mock('lodash', () => ({
  ...jest.requireActual('lodash'),
}));

jest.mock('http-status', () => ({
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
}));

jest.mock('../exportService', () => ({
  exportData: jest.fn(),
}));

jest.mock('../pdfDeliveryReportService', () => ({
  generatePdf: jest.fn(),
}));

jest.mock('../csvDeliveryReportService', () => ({
  generateCsv: jest.fn(),
}));

jest.mock('../../helpers/apiError', () => {
  return jest.fn().mockImplementation((message, statusCode) => {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
  });
});

jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      in: jest.fn(),
      ne: jest.fn(),
      and: jest.fn(),
      or: jest.fn(),
      between: jest.fn(),
      notIn: jest.fn(),
      iLike: jest.fn(),
    },
    and: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
  },
  DeliveryRequest: {
    getAll: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

describe('DeliveryreportService', () => {
  let mockInputData;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    mockModels = require('../../models');

    mockInputData = {
      user: {
        id: 1,
        email: '<EMAIL>',
        domainName: 'testdomain',
        firstName: 'John',
        lastName: 'Doe',
      },
      body: {
        ProjectId: 1,
        ParentCompanyId: 1,
        search: 'test search',
      },
      params: {
        ProjectId: 1,
        pageNo: '1',
        pageSize: '10',
        void: '0',
      },
      headers: {
        timezoneoffset: '-300',
      },
    };

    // Setup default mock implementations
    helper.getDynamicModel.mockResolvedValue({
      DeliveryRequest: mockModels.DeliveryRequest,
      User: mockModels.User,
      Member: mockModels.Member,
      DeliverGate: {},
      DeliverCompany: {},
    });

    helper.returnProjectModel.mockResolvedValue({
      Project: mockModels.Project,
      User: mockModels.User,
      Member: mockModels.Member,
    });

    // Default Enterprise mock - will be overridden in specific tests
    mockModels.Enterprise.findOne.mockResolvedValue({
      name: 'testdomain',
      id: 1,
      status: 'completed',
    });

    // Default Member mock - will be overridden in specific tests
    mockModels.Member.findOne.mockResolvedValue({
      id: 1,
      UserId: 1,
      ProjectId: 1,
      RoleId: 2,
      isDeleted: false,
      isActive: true,
      isAccount: true,
      EnterpriseId: 1,
    });

    // Default User mock - will be overridden in specific tests
    mockModels.User.findOne.mockResolvedValue({
      id: 1,
      email: '<EMAIL>',
    });

    mockModels.DeliveryRequest.getAll.mockResolvedValue([
      { id: 1, description: 'Test delivery' },
      { id: 2, description: 'Another delivery' },
    ]);
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(deliveryreportService).toBeDefined();
    });

    it('should have all required methods', () => {
      expect(typeof deliveryreportService.getDynamicModel).toBe('function');
      expect(typeof deliveryreportService.getDomainName).toBe('function');
      expect(typeof deliveryreportService.listDeliveryRequest).toBe('function');
      expect(typeof deliveryreportService.isInvalidVoidParam).toBe('function');
      expect(typeof deliveryreportService.getMemberDetails).toBe('function');
      expect(typeof deliveryreportService.buildSearchCondition).toBe('function');
      expect(typeof deliveryreportService.calculateOffset).toBe('function');
      expect(typeof deliveryreportService.buildCondition).toBe('function');
      expect(typeof deliveryreportService.returnProjectModel).toBe('function');
      expect(typeof deliveryreportService.getLimitData).toBe('function');
    });
  });

  describe('getDynamicModel', () => {
    it('should successfully get dynamic model with domain name', async () => {
      const result = await deliveryreportService.getDynamicModel(mockInputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
      expect(result).toBeNull();
    });

    it('should handle ParentCompanyId from params', async () => {
      const inputData = {
        ...mockInputData,
        body: {},
        params: { ...mockInputData.params, ParentCompanyId: 2 },
      };

      await deliveryreportService.getDynamicModel(inputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(helper.getDynamicModel).toHaveBeenCalled();
    });
  });

  describe('getDomainName', () => {
    it('should return domain name when valid domain exists', async () => {
      const result = await deliveryreportService.getDomainName('testdomain', null, '<EMAIL>');

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' },
      });
      expect(result).toBe('testdomain');
    });

    it('should return empty string when domain does not exist', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.getDomainName('nonexistent', null, '<EMAIL>');

      expect(result).toBe('');
    });

    it('should find domain by ParentCompanyId when no domain name provided', async () => {
      const result = await deliveryreportService.getDomainName(null, 1, '<EMAIL>');

      expect(result).toBeDefined();
    });

    it('should handle case insensitive domain names', async () => {
      const result = await deliveryreportService.getDomainName('TESTDOMAIN', null, '<EMAIL>');

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' },
      });
      expect(result).toBe('TESTDOMAIN');
    });

    it('should return empty string when both domain and ParentCompanyId are null', async () => {
      const result = await deliveryreportService.getDomainName(null, null, '<EMAIL>');

      expect(result).toBeNull();
    });
  });

  describe('findDomainNameByCompanyIdOrEmail', () => {
    it('should find domain name by email', async () => {
      const result = await deliveryreportService.findDomainNameByCompanyIdOrEmail(1, '<EMAIL>');

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(result).toBeDefined();
    });

    it('should handle null email', async () => {
      // Mock the Enterprise.findOne to return null to test the empty string path
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByCompanyIdOrEmail(1, null);

      expect(result).toBe('');
    });

    it('should handle empty email string', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByCompanyIdOrEmail(1, '');

      expect(result).toBe('');
    });

    it('should handle user not found by email', async () => {
      mockModels.User.findOne.mockResolvedValue(null);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByCompanyIdOrEmail(1, '<EMAIL>');

      expect(result).toBe('');
    });

    it('should handle member data not found for user', async () => {
      mockModels.User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      mockModels.Member.findOne.mockResolvedValue(null);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByCompanyIdOrEmail(1, '<EMAIL>');

      expect(result).toBe('');
    });
  });

  describe('getPublicMemberData', () => {
    it('should get member data by user ID', async () => {
      const result = await deliveryreportService.getPublicMemberData(1);

      expect(result).toBeDefined();
    });

    it('should call publicMember.findOne with correct parameters', async () => {
      // Setup publicMember mock through returnProjectModel
      await deliveryreportService.returnProjectModel();

      await deliveryreportService.getPublicMemberData(123);

      expect(mockModels.Member.findOne).toHaveBeenCalledWith({
        where: {
          UserId: 123,
          RoleId: { [mockModels.Sequelize.Op.ne]: 4 },
          isDeleted: false
        },
      });
    });

    it('should return null when no member found', async () => {
      mockModels.Member.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.getPublicMemberData(999);

      expect(result).toBeNull();
    });
  });

  describe('findDomainNameByMemberData', () => {
    it('should find domain name when member is account', async () => {
      const memberData = { isAccount: true, EnterpriseId: 1 };

      const result = await deliveryreportService.findDomainNameByMemberData(memberData, 1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { id: 1, status: 'completed' },
      });
      expect(result).toBe('testdomain');
    });

    it('should find domain name when member is not account', async () => {
      const memberData = { isAccount: false };
      // Mock to return null to test the empty string path
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByMemberData(memberData, 1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' },
      });
      expect(result).toBe('');
    });

    it('should handle null member data', async () => {
      // Mock to return null to test the empty string path
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByMemberData(null, 1);

      expect(result).toBe('');
    });

    it('should return empty string when no enterprise found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);
      const memberData = { isAccount: true, EnterpriseId: 1 };

      const result = await deliveryreportService.findDomainNameByMemberData(memberData, 1);

      expect(result).toBe('');
    });

    it('should handle undefined member data', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.findDomainNameByMemberData(undefined, 1);

      expect(result).toBe('');
    });

    it('should return lowercase domain name when enterprise found', async () => {
      const memberData = { isAccount: true, EnterpriseId: 1 };
      mockModels.Enterprise.findOne.mockResolvedValue({
        name: 'TESTDOMAIN',
        id: 1,
        status: 'completed',
      });

      const result = await deliveryreportService.findDomainNameByMemberData(memberData, 1);

      expect(result).toBe('testdomain');
    });
  });

  describe('getEnterpriseById', () => {
    it('should get enterprise by ID', async () => {
      const result = await deliveryreportService.getEnterpriseById(1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { id: 1, status: 'completed' },
      });
      expect(result).toEqual({
        name: 'testdomain',
        id: 1,
        status: 'completed',
      });
    });
  });

  describe('getEnterpriseDomainName', () => {
    it('should get enterprise domain name', async () => {
      const result = await deliveryreportService.getEnterpriseDomainName(1);

      expect(result).toBe('testdomain');
    });

    it('should return empty string when no enterprise found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.getEnterpriseDomainName(1);

      expect(result).toBe('');
    });

    it('should return lowercase domain name', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue({
        name: 'TESTDOMAIN',
        id: 1,
        status: 'completed',
      });

      const result = await deliveryreportService.getEnterpriseDomainName(1);

      expect(result).toBe('testdomain');
    });

    it('should handle null ParentCompanyId', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.getEnterpriseDomainName(null);

      expect(result).toBe('');
    });
  });

  describe('getEnterpriseByParentCompanyId', () => {
    it('should get enterprise by parent company ID', async () => {
      const result = await deliveryreportService.getEnterpriseByParentCompanyId(1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' },
      });
      expect(result).toEqual({
        name: 'testdomain',
        id: 1,
        status: 'completed',
      });
    });
  });

  describe('isInvalidVoidParam', () => {
    it('should return false for valid void param "0"', () => {
      const result = deliveryreportService.isInvalidVoidParam('0');
      expect(result).toBe(false);
    });

    it('should return false for valid void param "1"', () => {
      const result = deliveryreportService.isInvalidVoidParam('1');
      expect(result).toBe(false);
    });

    it('should return true for invalid void param', () => {
      const result = deliveryreportService.isInvalidVoidParam('2');
      expect(result).toBe(true);
    });

    it('should return true for null void param', () => {
      const result = deliveryreportService.isInvalidVoidParam(null);
      expect(result).toBe(true);
    });
  });

  describe('getMemberDetails', () => {
    it('should get member details', async () => {
      const result = await deliveryreportService.getMemberDetails(1, 1);

      expect(mockModels.Member.findOne).toHaveBeenCalled();
      expect(result).toEqual({
        id: 1,
        UserId: 1,
        ProjectId: 1,
        RoleId: 2,
        isDeleted: false,
        isActive: true,
        isAccount: true,
        EnterpriseId: 1,
      });
    });

    it('should return null when member not found', async () => {
      mockModels.Member.findOne.mockResolvedValue(null);

      const result = await deliveryreportService.getMemberDetails(1, 1);

      expect(result).toBeNull();
    });
  });

  describe('buildSearchCondition', () => {
    it('should build search condition with search term', () => {
      const incomeData = { search: 'test' };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      // Check that the result has the expected structure
      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
      // The result should not be empty when search term is provided
      expect(Object.keys(result).length > 0 || Object.getOwnPropertySymbols(result).length > 0).toBe(true);
    });

    it('should return empty object when no search term', () => {
      const incomeData = {};

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toEqual({});
    });

    it('should return empty object when search is null', () => {
      const incomeData = { search: null };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toEqual({});
    });

    it('should return empty object when search is undefined', () => {
      const incomeData = { search: undefined };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toEqual({});
    });

    it('should return empty object when search is empty string', () => {
      const incomeData = { search: '' };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toEqual({});
    });

    it('should build correct search condition with iLike operator', () => {
      const incomeData = { search: 'concrete' };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toHaveProperty(mockModels.Sequelize.Op.or);
      const orConditions = result[mockModels.Sequelize.Op.or];

      expect(orConditions).toEqual([
        { '$approverDetails.User.firstName$': { [mockModels.Sequelize.Op.iLike]: '%concrete%' } },
        { '$equipmentDetails.Equipment.equipmentName$': { [mockModels.Sequelize.Op.iLike]: '%concrete%' } },
        { description: { [mockModels.Sequelize.Op.iLike]: '%concrete%' } },
        { cranePickUpLocation: { [mockModels.Sequelize.Op.iLike]: '%concrete%' } },
        { craneDropOffLocation: { [mockModels.Sequelize.Op.iLike]: '%concrete%' } },
      ]);
    });

    it('should handle special characters in search term', () => {
      const incomeData = { search: 'test%_search' };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toHaveProperty(mockModels.Sequelize.Op.or);
      const orConditions = result[mockModels.Sequelize.Op.or];

      expect(orConditions[0]).toEqual({
        '$approverDetails.User.firstName$': { [mockModels.Sequelize.Op.iLike]: '%test%_search%' }
      });
    });

    it('should handle whitespace in search term', () => {
      const incomeData = { search: '  test search  ' };

      const result = deliveryreportService.buildSearchCondition(incomeData);

      expect(result).toHaveProperty(mockModels.Sequelize.Op.or);
      const orConditions = result[mockModels.Sequelize.Op.or];

      expect(orConditions[2]).toEqual({
        description: { [mockModels.Sequelize.Op.iLike]: '%  test search  %' }
      });
    });
  });

  describe('calculateOffset', () => {
    it('should calculate offset correctly', () => {
      const result = deliveryreportService.calculateOffset('2', '10');
      expect(result).toBe(10);
    });

    it('should calculate offset for first page', () => {
      const result = deliveryreportService.calculateOffset('1', '10');
      expect(result).toBe(0);
    });

    it('should handle string numbers', () => {
      const result = deliveryreportService.calculateOffset('3', '5');
      expect(result).toBe(10);
    });
  });

  describe('buildCondition', () => {
    it('should build condition with ProjectId', () => {
      const incomeData = {};
      const ProjectId = 1;
      const voidParam = '0';

      const result = deliveryreportService.buildCondition(incomeData, ProjectId, voidParam);

      expect(result).toHaveProperty('ProjectId', 1);
      expect(result).toHaveProperty('isQueued', false);
    });

    it('should handle void param "1"', () => {
      const incomeData = {};
      const ProjectId = 1;
      const voidParam = '1';

      const result = deliveryreportService.buildCondition(incomeData, ProjectId, voidParam);

      expect(result).toEqual({
        ProjectId: 1,
        isQueued: false,
      });
    });

    it('should handle filter conditions', () => {
      const incomeData = { description: 'test', location: 'office' };
      const ProjectId = 1;
      const voidParam = '0';

      const result = deliveryreportService.buildCondition(incomeData, ProjectId, voidParam);

      expect(result).toHaveProperty('ProjectId', 1);
      expect(result).toHaveProperty('isQueued', false);
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('location');
    });

    it('should filter out empty/null values from incomeData', () => {
      const incomeData = {
        description: 'test',
        location: '',
        status: null,
        type: undefined,
        category: 'equipment'
      };
      const ProjectId = 1;
      const voidParam = '0';

      const result = deliveryreportService.buildCondition(incomeData, ProjectId, voidParam);

      expect(result).toHaveProperty('ProjectId', 1);
      expect(result).toHaveProperty('isQueued', false);
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('category');
      expect(result).not.toHaveProperty('location');
      expect(result).not.toHaveProperty('status');
      expect(result).not.toHaveProperty('type');
    });

    it('should handle string ProjectId conversion', () => {
      const incomeData = {};
      const ProjectId = '123';
      const voidParam = '0';

      const result = deliveryreportService.buildCondition(incomeData, ProjectId, voidParam);

      expect(result).toHaveProperty('ProjectId', 123);
      expect(result).toHaveProperty('isQueued', false);
    });

    it('should handle complex filter conditions with iLike operator', () => {
      const incomeData = {
        description: 'concrete delivery',
        equipmentType: 'crane'
      };
      const ProjectId = 1;
      const voidParam = '0';

      const result = deliveryreportService.buildCondition(incomeData, ProjectId, voidParam);

      expect(result.description).toEqual(expect.objectContaining({
        [mockModels.Sequelize.Op.iLike]: '%concrete delivery%'
      }));
      expect(result.equipmentType).toEqual(expect.objectContaining({
        [mockModels.Sequelize.Op.iLike]: '%crane%'
      }));
    });
  });

  describe('returnProjectModel', () => {
    it('should return project model', async () => {
      await deliveryreportService.returnProjectModel();

      expect(helper.returnProjectModel).toHaveBeenCalled();
    });
  });

  describe('getLimitData', () => {
    it('should process data within limit', async () => {
      const result = { id: 1, name: 'test' };
      const finalResult = [];
      const done = jest.fn();

      await deliveryreportService.getLimitData(result, 0, 2, finalResult, {}, '-300', done);

      expect(done).toHaveBeenCalledWith([result, result], false);
    });

    it('should call done when index reaches limit', async () => {
      const result = { id: 1, name: 'test' };
      const finalResult = [];
      const done = jest.fn();

      await deliveryreportService.getLimitData(result, 2, 2, finalResult, {}, '-300', done);

      expect(done).toHaveBeenCalledWith(finalResult, false);
    });

    it('should handle zero limit', async () => {
      const result = { id: 1, name: 'test' };
      const finalResult = [];
      const done = jest.fn();

      await deliveryreportService.getLimitData(result, 0, 0, finalResult, {}, '-300', done);

      expect(done).toHaveBeenCalledWith(finalResult, false);
    });

    it('should handle negative index', async () => {
      const result = { id: 1, name: 'test' };
      const finalResult = [];
      const done = jest.fn();

      await deliveryreportService.getLimitData(result, -1, 2, finalResult, {}, '-300', done);

      expect(done).toHaveBeenCalledWith([result, result, result], false);
    });

    it('should handle large limit recursively', async () => {
      const result = { id: 1, name: 'test' };
      const finalResult = [];
      const done = jest.fn();

      await deliveryreportService.getLimitData(result, 0, 5, finalResult, {}, '-300', done);

      expect(done).toHaveBeenCalledWith([result, result, result, result, result], false);
      expect(finalResult).toHaveLength(5);
    });

    it('should preserve original finalResult array reference', async () => {
      const result = { id: 1, name: 'test' };
      const finalResult = [{ existing: 'data' }];
      const done = jest.fn();

      await deliveryreportService.getLimitData(result, 0, 2, finalResult, {}, '-300', done);

      expect(done).toHaveBeenCalledWith([{ existing: 'data' }, result, result], false);
    });
  });

  describe('fetchDeliveryRequests', () => {
    it('should fetch delivery requests', async () => {
      const memberDetails = { RoleId: 2, id: 1 };
      const searchCondition = {};

      const result = await deliveryreportService.fetchDeliveryRequests(mockInputData, memberDetails, searchCondition);

      expect(mockModels.DeliveryRequest.getAll).toHaveBeenCalledWith({
        attr: expect.any(Object),
        roleId: 2,
        memberId: 1,
        limit: 10,
        offset: 0,
        searchCondition: {},
      });
      expect(result).toEqual([
        { id: 1, description: 'Test delivery' },
        { id: 2, description: 'Another delivery' },
      ]);
    });
  });

  describe('processDeliveryList', () => {
    it('should process delivery list successfully', async () => {
      const deliveryList = [{ id: 1 }, { id: 2 }];
      const done = jest.fn();

      // Mock getLimitData to call done immediately
      jest.spyOn(deliveryreportService, 'getLimitData').mockImplementation((_result, _index, _limit, _finalResult, _incomeData, _timezoneoffset, callback) => {
        callback([{ id: 1 }, { id: 2 }], null);
      });

      deliveryreportService.processDeliveryList(deliveryList, mockInputData, done);

      expect(done).toHaveBeenCalledWith({ count: 2, rows: [{ id: 1 }, { id: 2 }] }, null);
    });

    it('should handle error in processDeliveryList', async () => {
      const deliveryList = [{ id: 1 }];
      const done = jest.fn();
      const error = new Error('Processing error');

      // Mock getLimitData to call done with error
      jest.spyOn(deliveryreportService, 'getLimitData').mockImplementation((_result, _index, _limit, _finalResult, _incomeData, _timezoneoffset, callback) => {
        callback(null, error);
      });

      deliveryreportService.processDeliveryList(deliveryList, mockInputData, done);

      expect(done).toHaveBeenCalledWith(null, error);
    });
  });

  describe('listDeliveryRequest', () => {
    it('should list delivery requests successfully', async () => {
      const done = jest.fn();

      // Mock getLimitData to call done immediately
      jest.spyOn(deliveryreportService, 'getLimitData').mockImplementation((_result, _index, _limit, _finalResult, _incomeData, _timezoneoffset, callback) => {
        callback([{ id: 1 }, { id: 2 }], null);
      });

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(helper.getDynamicModel).toHaveBeenCalled();
      expect(mockModels.Member.findOne).toHaveBeenCalled();
      expect(mockModels.DeliveryRequest.getAll).toHaveBeenCalled();
      expect(done).toHaveBeenCalledWith({ count: 2, rows: [{ id: 1 }, { id: 2 }] }, null);
    });

    it('should handle invalid void parameter', async () => {
      const done = jest.fn();
      const inputDataWithInvalidVoid = {
        ...mockInputData,
        params: { ...mockInputData.params, void: '2' },
      };

      await deliveryreportService.listDeliveryRequest(inputDataWithInvalidVoid, done);

      expect(done).toHaveBeenCalledWith({ message: 'Please enter void as 1 or 0' }, null);
    });

    it('should handle member not found', async () => {
      const done = jest.fn();
      mockModels.Member.findOne.mockResolvedValue(null);

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Project Id/Member does not exist' }, null);
    });

    it('should handle errors in listDeliveryRequest', async () => {
      const done = jest.fn();
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Something went wrong' }, error);
    });

    it('should handle void parameter null', async () => {
      const done = jest.fn();
      const inputDataWithNullVoid = {
        ...mockInputData,
        params: { ...mockInputData.params, void: null },
      };

      await deliveryreportService.listDeliveryRequest(inputDataWithNullVoid, done);

      expect(done).toHaveBeenCalledWith({ message: 'Please enter void as 1 or 0' }, null);
    });

    it('should handle void parameter undefined', async () => {
      const done = jest.fn();
      const inputDataWithUndefinedVoid = {
        ...mockInputData,
        params: { ...mockInputData.params, void: undefined },
      };

      await deliveryreportService.listDeliveryRequest(inputDataWithUndefinedVoid, done);

      expect(done).toHaveBeenCalledWith({ message: 'Please enter void as 1 or 0' }, null);
    });

    it('should handle empty delivery list', async () => {
      const done = jest.fn();
      mockModels.DeliveryRequest.getAll.mockResolvedValue([]);

      // Mock getLimitData to call done immediately
      jest.spyOn(deliveryreportService, 'getLimitData').mockImplementation((_result, _index, _limit, _finalResult, _incomeData, _timezoneoffset, callback) => {
        callback([], null);
      });

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ count: 0, rows: [] }, null);
    });

    it('should handle error in getDynamicModel', async () => {
      const done = jest.fn();
      const error = new Error('Dynamic model error');
      helper.returnProjectModel.mockRejectedValue(error);

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Something went wrong' }, error);
    });

    it('should handle error in getMemberDetails', async () => {
      const done = jest.fn();
      const error = new Error('Member details error');
      mockModels.Member.findOne.mockRejectedValue(error);

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Something went wrong' }, error);
    });

    it('should handle error in fetchDeliveryRequests', async () => {
      const done = jest.fn();
      const error = new Error('Fetch delivery requests error');
      mockModels.DeliveryRequest.getAll.mockRejectedValue(error);

      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Something went wrong' }, error);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const dbError = new Error('Database connection failed');
      helper.getDynamicModel.mockRejectedValue(dbError);

      const done = jest.fn();
      await deliveryreportService.listDeliveryRequest(mockInputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Something went wrong' }, dbError);
    });

    it('should handle invalid input data', async () => {
      const invalidInputData = {
        user: { id: 1, email: '<EMAIL>', domainName: 'test' },
        body: null,
        params: { ProjectId: 1, void: '0', pageNo: '1', pageSize: '10' },
        headers: { timezoneoffset: '-300' }
      };

      const done = jest.fn();
      await deliveryreportService.listDeliveryRequest(invalidInputData, done);

      // Should handle gracefully
      expect(done).toHaveBeenCalled();
    });
  });
});
