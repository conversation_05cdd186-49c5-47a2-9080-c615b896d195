const moment = require('moment');
const fs = require('fs');
const awsConfig = require('../middlewares/awsConfig');
const { Project, Company } = require('../models');
const puppeteerService = require('./puppeteerService');

const pdfConcreteReportService = {
  extractConcreteHeaderSelection(req) {
    const header = [];
    const flags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isCompanySelected: false,
      isOrderNumberSelected: false,
      isSlumpSelected: false,
      isTruckSpacingSelected: false,
      isPrimerOrderedSelected: false,
      isPersonSelected: false,
      isQuantityOrderedSelected: false,
      isMixDesignSelected: false,
      isLocationSelected: false
    };

    req.body.selectedHeaders.forEach(obj => {
      if (obj.isActive) {
        const keyMap = {
          name: 'Person',
          mixDesign: 'MixDesign',
          orderNumber: 'OrderNumber',
          truckSpacing: 'TruckSpacing',
          primer: 'PrimerOrdered',
          quantity: 'QuantityOrdered'
        };
        const key = keyMap[obj.key] || obj.key.charAt(0).toUpperCase() + obj.key.slice(1);
        flags[`is${key}Selected`] = true;
        header.push(`<th style="text-align:center">${obj.title}</th>`);
      }
    });

    return { flags, header };
  },
  buildConcreteRows(data, flags, timezoneoffset) {
    const rows = [];
    const td = val => `<td style="color:#5B5B5B;font-weight:600;text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${val}</td>`;
    const wrapList = list => list.map(val => `<p>${val}</p>`).join('') || '-';

    for (const item of data) {
      const row = this.buildConcreteRow(item, flags, timezoneoffset, td, wrapList);
      rows.push(`<tr style="border-bottom: 1px solid #e0e0e0; font-size: 12px">${row}</tr>`);
    }

    return rows;
  },

  buildConcreteRow(item, flags, timezoneoffset, td, wrapList) {
    const start = moment(item.concretePlacementStart).add(Number(timezoneoffset), 'minutes');
    const end = moment(item.concretePlacementEnd).add(Number(timezoneoffset), 'minutes');

    const rowCells = [
      this.buildIdCell(item, flags, td),
      this.buildDescriptionCell(item, flags, td),
      this.buildDateCell(start, end, flags, td),
      this.buildStatusCell(item, flags, td),
      this.buildApprovedByCell(item, flags, td),
      this.buildCompanyCell(item, flags, td, wrapList),
      this.buildOrderNumberCell(item, flags, td),
      this.buildSlumpCell(item, flags, td),
      this.buildTruckSpacingCell(item, flags, td),
      this.buildPrimerCell(item, flags, td),
      this.buildPersonCell(item, flags, td, wrapList),
      this.buildQuantityCell(item, flags, td),
      this.buildMixDesignCell(item, flags, td, wrapList),
      this.buildLocationCell(item, flags, td)
    ];

    return rowCells.join('');
  },

  buildIdCell(item, flags, td) {
    return flags.isIdSelected ? td(item.ConcreteRequestId) : '';
  },

  buildDescriptionCell(item, flags, td) {
    return flags.isDescriptionSelected ? td(item.description) : '';
  },

  buildDateCell(start, end, flags, td) {
    return flags.isDateSelected ? td(`${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`) : '';
  },

  buildStatusCell(item, flags, td) {
    return flags.isStatusSelected ? td(item.status) : '';
  },

  buildApprovedByCell(item, flags, td) {
    if (!flags.isApprovedBySelected) return '';
    const approverName = item.approverDetails
      ? `${item.approverDetails.User.firstName} ${item.approverDetails.User.lastName}`
      : '-';
    return td(approverName);
  },

  buildCompanyCell(item, flags, td, wrapList) {
    if (!flags.isCompanySelected) return '';
    const companyNames = (item.concreteSupplierDetails || []).map(e => e.Company.companyName);
    return td(wrapList(companyNames));
  },

  buildOrderNumberCell(item, flags, td) {
    return flags.isOrderNumberSelected ? td(item.concreteOrderNumber || '-') : '';
  },

  buildSlumpCell(item, flags, td) {
    return flags.isSlumpSelected ? td(item.slump || '-') : '';
  },

  buildTruckSpacingCell(item, flags, td) {
    return flags.isTruckSpacingSelected ? td(item.truckSpacingHours || '-') : '';
  },

  buildPrimerCell(item, flags, td) {
    return flags.isPrimerOrderedSelected ? td(item.primerForPump || '-') : '';
  },

  buildPersonCell(item, flags, td, wrapList) {
    if (!flags.isPersonSelected) return '';
    const memberNames = (item.memberDetails || []).map(m => `${m.Member.User.firstName} ${m.Member.User.lastName}`);
    return td(wrapList(memberNames));
  },

  buildQuantityCell(item, flags, td) {
    return flags.isQuantityOrderedSelected ? td(item.concreteQuantityOrdered || '-') : '';
  },

  buildMixDesignCell(item, flags, td, wrapList) {
    if (!flags.isMixDesignSelected) return '';
    const mixDesigns = (item.mixDesignDetails || []).map(d => d.ConcreteMixDesign.mixDesign);
    return td(wrapList(mixDesigns));
  },

  buildLocationCell(item, flags, td) {
    return flags.isLocationSelected ? td(item.location?.locationPath || '-') : '';
  },
  generateConcretePdfTemplate(templatePath, projectData, companyData, loginUser, req, header, content) {
    let template = fs.readFileSync(templatePath, 'utf-8');

    return template
      .replace('$projectName', projectData.projectName)
      .replace('$companyName', companyData.companyName)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName}`)
      .replace('$reportType', 'Concrete')
      .replace('$header', header.join(''))
      .replace('$data', content.join(''))
      .replace(/,/g, '');
  },
  async pdfFormatOfConcreteRequest(params, loginUser, data, req, done) {
    try {
      const { timezoneoffset } = req.headers;
      const { flags, header } = this.extractConcreteHeaderSelection(req);

      const [projectData, companyData] = await Promise.all([
        Project.findOne({
          where: { isDeleted: false, id: +params.ProjectId },
          attributes: ['projectName']
        }),
        Company.findOne({
          where: { isDeleted: false, ParentCompanyId: +req.body.ParentCompanyId, isParent: true },
          attributes: ['companyName']
        })
      ]);

      const rows = this.buildConcreteRows(data, flags, timezoneoffset);
      const templatePath = '/usr/src/web/api/v21/views/mail-templates/deliveryReport.html';
      const finalHtml = this.generateConcretePdfTemplate(templatePath, projectData, companyData, loginUser, req, header, rows);

      const pdfBuffer = await puppeteerService.generatePdfBuffer(finalHtml);
      if (pdfBuffer) {
        awsConfig.reportUpload(pdfBuffer, req.body.reportName, req.body.exportType, (result, error) => {
          if (!error) done(result, false);
          else done(false, { message: 'Upload failed' });
        });
      } else {
        done(false, { message: 'Cannot export the document' });
      }
    } catch (err) {
      done(false, { message: 'Unexpected error during PDF generation', error: err.message });
    }
  }

};
module.exports = pdfConcreteReportService;
