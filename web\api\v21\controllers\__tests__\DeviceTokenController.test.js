const DeviceTokenController = require('../DeviceTokenController');
const { deviceTokenService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  deviceTokenService: {
    setDeviceToken: jest.fn(),
    clearDeviceToken: jest.fn(),
  },
}));

describe('DeviceTokenController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {};
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('setDeviceToken', () => {
    it('should set device token successfully', async () => {
      const mockResponse = { id: 1, token: 'test-token' };
      deviceTokenService.setDeviceToken.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeviceTokenController.setDeviceToken(mockReq, mockRes, mockNext);

      expect(deviceTokenService.setDeviceToken).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Token added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from device token setting', async () => {
      const mockError = new Error('Service error');
      deviceTokenService.setDeviceToken.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeviceTokenController.setDeviceToken(mockReq, mockRes, mockNext);

      expect(deviceTokenService.setDeviceToken).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle null response from device token setting', async () => {
      deviceTokenService.setDeviceToken.mockImplementation((req, callback) => {
        callback(null, null);
      });

      await DeviceTokenController.setDeviceToken(mockReq, mockRes, mockNext);

      expect(deviceTokenService.setDeviceToken).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Token added successfully.',
        data: null,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('clearDeviceToken', () => {
    it('should clear device token successfully', async () => {
      const mockResponse = { id: 1, cleared: true };
      deviceTokenService.clearDeviceToken.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DeviceTokenController.clearDeviceToken(mockReq, mockRes, mockNext);

      expect(deviceTokenService.clearDeviceToken).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Token cleared successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from device token clearing', async () => {
      const mockError = new Error('Service error');
      deviceTokenService.clearDeviceToken.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DeviceTokenController.clearDeviceToken(mockReq, mockRes, mockNext);

      expect(deviceTokenService.clearDeviceToken).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle null response from device token clearing', async () => {
      deviceTokenService.clearDeviceToken.mockImplementation((req, callback) => {
        callback(null, null);
      });

      await DeviceTokenController.clearDeviceToken(mockReq, mockRes, mockNext);

      expect(deviceTokenService.clearDeviceToken).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Token cleared successfully.',
        data: null,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
