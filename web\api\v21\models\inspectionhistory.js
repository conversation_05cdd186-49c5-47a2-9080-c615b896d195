module.exports = (sequelize, DataTypes) => {
  const InspectionHistory = sequelize.define(
    'InspectionHistory',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      InspectionRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      type: {
        type: DataTypes.STRING,
      },
      description: DataTypes.STRING,
    },
    {},
  );
  InspectionHistory.associate = (models) => {
    // associations can be defined
    InspectionHistory.belongsTo(models.Member);
    InspectionHistory.belongsTo(models.InspectionRequest);
  };
  InspectionHistory.getAll = async (attr) => {
    const newInspectionHistory = await InspectionHistory.findAll({
      where: { ...attr },
    });
    return newInspectionHistory;
  };
  InspectionHistory.createInstance = async (paramData) => {
    const newInspectionHistory = await InspectionHistory.create(paramData);
    return newInspectionHistory;
  };
  return InspectionHistory;
};
