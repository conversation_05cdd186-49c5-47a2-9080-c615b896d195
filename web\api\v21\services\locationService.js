const status = require('http-status');
const ExcelJS = require('exceljs');
const moment = require('moment');
require('moment-timezone');
const { Sequelize, Enterprise, Member, LocationNotificationPreferences } = require('../models');
let {
    Locations,
    User,
    Project,
    DeliveryRequest,
    CraneRequest,
    ConcreteRequest,
    InspectionRequest,
    CalendarSetting,
    EquipmentMapping,
    TimeZone,
    ProjectSettings
} = require('../models');
const helper = require('../helpers/domainHelper');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const ApiError = require('../helpers/apiError');

const locationService = {
    async addLocation(req) {
        try {
            await this.getDynamicModel(req);
            const loginUser = req.user;
            const { query } = req;
            const payload = req.body;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const memberDetails = await Member.findOne({
                where: {
                    UserId: loginUser.id,
                    ProjectId,
                    isDeleted: false,
                    isActive: true,
                },
            });
            if (!memberDetails) {
                const err = new ApiError('Member does not exist.', status.BAD_REQUEST);
                throw Error(err);
            }
            const projectDetails = await Project.getProject({ id: ProjectId });
            if (projectDetails) {
                const locationObject = {
                    ProjectId,
                    ParentCompanyId,
                    notes: payload.notes,
                    EquipmentId: payload.EquipmentId,
                    GateId: payload.GateId,
                    TimeZoneId: payload.TimeZoneId,
                    MemberId: memberDetails.MemberId,
                    createdBy: loginUser.id,
                    platform: query.platform,
                };
                locationObject.locationName = payload.mainCategory;
                if (payload.mainCategory) {
                    locationObject.locationPath = payload.mainCategory;
                    const location = await Locations.create(locationObject);
                    this.setLocationNotificationPreferenceForAMember(location.id, ProjectId);
                    if (location) {
                        delete locationObject.locationName;
                        locationObject.LocationId = location.id;
                        if (payload.paths && payload.paths.length > 0) {
                            payload.paths.forEach(async (element) => {
                                if (element.subCategory) {
                                    locationObject.locationName = element.subCategory;
                                    locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory}`;
                                    const subLocation = await Locations.create(locationObject);
                                    this.setLocationNotificationPreferenceForAMember(subLocation.id, ProjectId);
                                    delete locationObject.LocationId;
                                    locationObject.LocationId = subLocation.id;
                                    if (element.tier && element.tier.length > 0) {
                                        element.tier.forEach(async (element1) => {
                                            if (element1.tier) {
                                                locationObject.locationName = element1.tier;
                                                locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory} > ${element1.tier}`;
                                                const tierLocation = await Locations.create(locationObject);
                                                this.setLocationNotificationPreferenceForAMember(
                                                    tierLocation.id,
                                                    ProjectId,
                                                );
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    }
                    return location;
                }
            }
            const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
            throw Error(err);
        } catch (e) {
            throw (null, e);
        }
    },
    async getDynamicModel(inputData) {
        await this.returnProjectModel();
        const { domainName, enterpriseValue } = await this.resolveDomainName(inputData);
        const modelObj = await helper.getDynamicModel(domainName);

        const { Project, User, Locations } = modelObj;
        this.Locations = Locations;
        this.Project = Project;
        this.User = User;

        if (enterpriseValue) {
            const newUser = await User.findOne({ where: { email: inputData.user.email } });
            inputData.user = newUser;
        }

        return null; // Still returns ProjectId which is always undefined
    },

    async resolveDomainName(inputData) {
        let { domainName } = inputData.user;
        let enterpriseValue;
        const ParentCompanyId = inputData.body.ParentCompanyId || inputData.query.ParentCompanyId;

        if (domainName) {
            const domainEnterpriseValue = await Enterprise.findOne({
                where: { name: domainName.toLowerCase() },
            });
            if (!domainEnterpriseValue) {
                domainName = '';
            }
        }

        if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
            ({ domainName, enterpriseValue } = await this.lookupEnterpriseFromUser(inputData, ParentCompanyId));
        }

        return { domainName, enterpriseValue };
    },

    async lookupEnterpriseFromUser(inputData, ParentCompanyId) {
        const { email } = inputData.user;
        let domainName = '';
        let enterpriseValue;

        if (!email) return { domainName, enterpriseValue };

        const userData = await publicUser.findOne({ where: { email } });
        if (!userData) return { domainName, enterpriseValue };

        const memberData = await publicMember.findOne({
            where: {
                UserId: userData.id,
                RoleId: { [Op.ne]: 4 },
                isDeleted: false,
            },
        });

        if (memberData) {
            const query = memberData.isAccount
                ? { id: memberData.EnterpriseId, status: 'completed' }
                : { ParentCompanyId, status: 'completed' };
            enterpriseValue = await Enterprise.findOne({ where: query });
        } else {
            enterpriseValue = await Enterprise.findOne({
                where: { ParentCompanyId, status: 'completed' },
            });
        }

        if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
        }

        return { domainName, enterpriseValue };
    },

    async returnProjectModel() {
        const modelData = await helper.returnProjectModel();
        publicUser = modelData.User;
        publicMember = modelData.Member;
    },
    async listLocation(req) {
        try {
            await this.getDynamicModel(req);
            const { query } = req;
            const { sort } = query;
            const { sortByField } = query;
            const { search } = query;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const pageNumber = +query.pageNo || 1;
            const pageSize = +query.pageSize || 10;
            const offset = (pageNumber - 1) * pageSize;
            const condition = {
                ProjectId,
                ParentCompanyId,
                LocationId: null,
                isDeleted: false,
            };
            const locations = await Locations.get(condition, sort, sortByField, search);
            const transformedLocations = locations.getLocations.map(location => {
                const locationData = location.toJSON ? location.toJSON() : location;
                locationData.gateDetails = locationData.GateId;
                delete locationData.GateId;
                return locationData;
            });
            locations.defaultLocation = locations.defaultLocation.toJSON()
            locations.defaultLocation['gateDetails'] = locations.defaultLocation.GateId
            const result = {
                count: transformedLocations.length,
                rows: transformedLocations.slice(offset, offset + pageSize),
                defaultLocation: locations.defaultLocation,
            };
            return result;
        } catch (e) {
            throw (null, e);
        }
    },
    async editLocation(req) {
        try {
            await this.getDynamicModel(req);
            const loginUser = req.user;
            const { query } = req;
            const payload = req.body;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const memberDetails = await Member.findOne({
                where: {
                    UserId: loginUser.id,
                    ProjectId,
                    isDeleted: false,
                    isActive: true,
                },
            });
            if (!memberDetails) {
                const err = new ApiError('Member does not exist.', status.BAD_REQUEST);
                throw Error(err);
            }
            const projectDetails = await Project.getProject({ id: ProjectId });
            if (projectDetails) {
                const locationObject = {
                    ProjectId,
                    ParentCompanyId,
                    notes: payload.notes,
                    MemberId: memberDetails.MemberId,
                    createdBy: loginUser.id,
                    platform: query.platform,
                    EquipmentId: payload.EquipmentId,
                    GateId: payload.GateId,
                    TimeZoneId: payload.TimeZoneId,
                };
                locationObject.locationName = payload.mainCategory;
                locationObject.locationPath = payload.mainCategory;
                const location = await Locations.update(locationObject, {
                    where: {
                        id: payload.id,
                    },
                });
                if (location) {
                    delete locationObject.locationName;
                    locationObject.LocationId = payload.id;
                    if (payload.paths && payload.paths.length > 0) {
                        payload.paths.forEach(async (element) => {
                            locationObject.locationName = element.subCategory;
                            if (element.id && element.subCategory === '') {
                                await Locations.update(
                                    {
                                        isDeleted: true,
                                    },
                                    {
                                        where: {
                                            id: element.id,
                                        },
                                    },
                                );
                                await Locations.update(
                                    {
                                        isDeleted: true,
                                    },
                                    {
                                        where: {
                                            LocationId: element.id,
                                        },
                                    },
                                );
                                await LocationNotificationPreferences.update(
                                    { isDeleted: true },
                                    { where: { LocationId: element.id } },
                                );
                            } else if (
                                (element.id && element.subCategory) ||
                                (element.id === null && element.subCategory)
                            ) {
                                let subLocationId;
                                locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory}`;
                                if (element.id === null) {
                                    const subLocation = await Locations.create(locationObject);
                                    this.setLocationNotificationPreferenceForAMember(subLocation.id, ProjectId);
                                    subLocationId = subLocation.id;
                                } else if (element.id && element.isDeleted === 0) {
                                    subLocationId = element.id;
                                    await Locations.update(locationObject, {
                                        where: {
                                            id: element.id,
                                        },
                                    });
                                } else if (element.id && element.isDeleted === 1) {
                                    await Locations.update(
                                        {
                                            isDeleted: true,
                                        },
                                        {
                                            where: {
                                                id: element.id,
                                            },
                                        },
                                    );
                                    await LocationNotificationPreferences.update(
                                        { isDeleted: true },
                                        { where: { LocationId: element.id } },
                                    );
                                }
                                delete locationObject.LocationId;
                                locationObject.LocationId = subLocationId;
                                if (element.tier && element.tier.length > 0) {
                                    element.tier.forEach(async (element1) => {
                                        const tierObject = element1;
                                        if (tierObject.tier) {
                                            locationObject.locationName = tierObject.tier;
                                            locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory} > ${element1.tier}`;
                                            if (!tierObject.tier && tierObject.isDeleted === 0) {
                                                delete tierObject.isDeleted;
                                                tierObject.isDeleted = 1;
                                            }
                                            if (tierObject.id && tierObject.tier && tierObject.isDeleted === 1) {
                                                await Locations.update(
                                                    {
                                                        isDeleted: true,
                                                    },
                                                    {
                                                        where: {
                                                            id: tierObject.id,
                                                        },
                                                    },
                                                );
                                                await LocationNotificationPreferences.update(
                                                    { isDeleted: true },
                                                    { where: { LocationId: tierObject.id } },
                                                );
                                            } else if (tierObject.id && tierObject.tier && tierObject.isDeleted === 0) {
                                                await Locations.update(locationObject, {
                                                    where: {
                                                        id: tierObject.id,
                                                    },
                                                });
                                            } else if (
                                                tierObject.id === null &&
                                                tierObject.tier &&
                                                tierObject.isDeleted === 0
                                            ) {
                                                const tierLocation = await Locations.create(locationObject);
                                                this.setLocationNotificationPreferenceForAMember(
                                                    tierLocation.id,
                                                    ProjectId,
                                                );
                                            }
                                        }
                                    });
                                }
                            }
                        });
                        return location;
                    }
                    return location;
                }
            }
            const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
            throw Error(err);
        } catch (e) {
            throw (null, e);
        }
    },
    async deleteLocation(req) {
        try {
            const { query } = req;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const condition = {
                ProjectId,
                ParentCompanyId,
                LocationId: null,
                id: query.id,
                isDeleted: false,
            };
            const projectDetails = await Project.getProject({ id: ProjectId });
            if (projectDetails) {
                const getLocation = await Locations.getOne(condition);
                const location = await Locations.update(
                    {
                        isDeleted: true,
                    },
                    {
                        where: {
                            id: query.id,
                            ProjectId,
                            ParentCompanyId,
                        },
                    },
                );
                await LocationNotificationPreferences.update(
                    { isDeleted: true },
                    { where: { LocationId: query.id } },
                );
                const subLocationsId = [];
                if (getLocation.paths && getLocation.paths.length > 0) {
                    getLocation.paths.forEach((subLocationObject) => {
                        subLocationsId.push(subLocationObject.id);
                        if (subLocationObject.tier && subLocationObject.tier.length > 0) {
                            subLocationObject.tier.forEach((tierObject) => {
                                subLocationsId.push(tierObject.id);
                            });
                        }
                    });
                }
                if (subLocationsId && subLocationsId.length > 0) {
                    await Locations.update(
                        { isDeleted: true },
                        { where: { id: { [Op.in]: subLocationsId } } },
                    );
                    await LocationNotificationPreferences.update(
                        { isDeleted: true },
                        { where: { LocationId: { [Op.in]: subLocationsId } } },
                    );
                }
                return location;
            }
        } catch (e) {
            throw (null, e);
        }
    },
    async getLocation(req) {
        try {
            const { query } = req;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const condition = {
                ProjectId,
                ParentCompanyId,
                LocationId: null,
                id: query.id,
                isDeleted: false,
            };
            const location = await Locations.getOne(condition);
            return location;
        } catch (e) {
            throw (null, e);
        }
    },
    async getLocations(req) {
        try {
            const { query } = req;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const condition = {
                ProjectId,
                ParentCompanyId,
                isDeleted: false,
                isActive: true,
            };
            const locationList = await Locations.getLocations(condition);
            const locations = locationList.map((location) => {
                const locationData = location.toJSON();
                locationData.gateDetails = locationData.GateId;
                delete locationData.GateId;
                return locationData;
            });
            return locations;
        } catch (e) {
            throw (null, e);
        }
    },
    async bulkUploadLocation(req) {
        await this.getDynamicModel(req);
        const { file } = req;
        const { ParentCompanyId, ProjectId, platform } = req.query;
        const loginUser = req.user;

        const memberDetail = await Member.findOne({
            where: [
                Sequelize.and(
                    { UserId: loginUser.id, ProjectId, isDeleted: false },
                    Sequelize.or({ RoleId: [1, 2, 3] })
                )
            ]
        });

        if (!memberDetail) {
            return { error: true, message: 'Project Does not exist or you are not a valid member.' };
        }

        if (!file?.originalname) {
            return { error: true, message: 'Please select a file.' };
        }

        const locationRecords = await this.validateFileAndGetData(file, ProjectId, ParentCompanyId);
        if (!locationRecords) {
            return { error: true, message: 'Please choose valid file' };
        }

        const groupedArray = this.groupLocationRecords(locationRecords);

        await this.addBulkUploadLocations(
            groupedArray,
            ParentCompanyId,
            ProjectId,
            memberDetail,
            loginUser,
            platform
        );

        return { error: false };
    },


    async validateFileAndGetData(file, ProjectId, ParentCompanyId) {
        const projectDetails = await Project.findOne({
            where: { id: ProjectId, ParentCompanyId },
            attributes: ['projectName']
        });

        const splitFileName = file.originalname.split('_');
        if (
            ProjectId !== splitFileName[1] ||
            projectDetails?.dataValues.projectName !== splitFileName[0]
        ) {
            return null;
        }

        const extension = file.originalname.split('.').pop();
        if (extension !== 'xlsx') {
            return null;
        }

        const newWorkbook = new ExcelJS.Workbook();
        await newWorkbook.xlsx.readFile(file.path);
        const worksheet = newWorkbook.getWorksheet('Location');

        const locationRecords = [];
        worksheet.eachRow((rowData, rowNumber) => {
            const singleRowData = rowData.values;
            if (rowNumber >= 3 && singleRowData[1] && singleRowData[2] && singleRowData[3]) {
                locationRecords.push(singleRowData);
            }
        });

        return locationRecords;
    },

    groupLocationRecords(locationRecords) {
        const groupedData = {};
        for (const item of locationRecords) {
            const category = item[1];
            const subCategory = item[2];
            const tier = item[3];

            if (!groupedData[category]) {
                groupedData[category] = { category, paths: [] };
            }

            const mainCategoryData = groupedData[category];
            let subCategoryData = mainCategoryData.paths.find(p => p.subCategory === subCategory);

            if (!subCategoryData) {
                subCategoryData = { subCategory, tier: [] };
                mainCategoryData.paths.push(subCategoryData);
            }

            if (tier) {
                subCategoryData.tier.push({ tier });
            }
        }

        return Object.values(groupedData);
    },

    async addBulkUploadLocations(result, ParentCompanyId, ProjectId, memberDetails, loginUser, platform) {
        const projectDetails = await Project.getProject({ id: ProjectId });
        if (!projectDetails || result.length === 0) return;

        for (const locationData of result) {
            await this.processCategory(locationData, ParentCompanyId, ProjectId, memberDetails, loginUser, platform);
        }
    },

    async processCategory(locationData, ParentCompanyId, ProjectId, memberDetails, loginUser, platform) {
        if (!locationData.category || locationData.category === '') return;

        const baseLocation = await Locations.create({
            ProjectId,
            ParentCompanyId,
            notes: null,
            MemberId: memberDetails.MemberId,
            createdBy: loginUser.id,
            platform,
            locationName: locationData.category,
            locationPath: locationData.category,
        });

        this.setLocationNotificationPreferenceForAMember(baseLocation.id, ProjectId);
        if (locationData.paths && locationData.paths.length > 0) {
            for (const path of locationData.paths) {
                await this.processSubCategory({path, baseLocation, categoryName: locationData.category, ParentCompanyId, ProjectId, memberDetails, loginUser, platform});
            }
        }
    },

    async processSubCategory(params) {
        const {path, baseLocation, categoryName, ParentCompanyId, ProjectId, memberDetails, loginUser, platform} = params;
        if (!path.subCategory || path.subCategory === '') return;

        const subLocation = await Locations.create({
            ProjectId,
            ParentCompanyId,
            notes: null,
            MemberId: memberDetails.MemberId,
            createdBy: loginUser.id,
            platform,
            LocationId: baseLocation.id,
            locationName: path.subCategory,
            locationPath: `${categoryName} > ${path.subCategory}`,
        });

        this.setLocationNotificationPreferenceForAMember(subLocation.id, ProjectId);

        if (path.tier && path.tier.length > 0) {
            for (const tier of path.tier) {
                if (tier.tier && tier.tier !== '') {
                    await Locations.create({
                        ProjectId,
                        ParentCompanyId,
                        notes: null,
                        MemberId: memberDetails.MemberId,
                        createdBy: loginUser.id,
                        platform,
                        LocationId: subLocation.id,
                        locationName: tier.tier,
                        locationPath: `${categoryName} > ${path.subCategory} > ${tier.tier}`,
                    }).then((tierLocation) => {
                        this.setLocationNotificationPreferenceForAMember(tierLocation.id, ProjectId);
                    });
                }
            }
        }
    },

    async getDropdownValuesForLocation(req) {
        try {
            const { params } = req;
            const ProjectId = +params.ProjectId;
            const ParentCompanyId = +params.ParentCompanyId;
            const condition = {
                ProjectId,
                ParentCompanyId,
                isDeleted: false,
                isActive: true,
            };
            const locations = await Locations.getLocations(condition);
            return locations;
        } catch (e) {
            throw (null, e);
        }
    },
    async listLocations(req) {
        try {
            await this.getDynamicModel(req);
            const { query } = req;
            const { sort } = query;
            const { sortByField } = query;
            const { search } = query;
            const loginUser = req.user;
            const ProjectId = +query.ProjectId;
            const ParentCompanyId = +query.ParentCompanyId;
            const pageNumber = +query.pageNo;
            const pageSize = +query.pageSize;
            const offset = (pageNumber - 1) * pageSize;
            const condition = {
                ProjectId,
                ParentCompanyId,
                LocationId: null,
                isDeleted: false,
            };
            const memberDetails = await Member.findOne({
                where: {
                    UserId: loginUser.id,
                    ProjectId,
                    isDeleted: false,
                    isActive: true,
                },
            });
            const locations = await Locations.getLocationList(
                memberDetails.id,
                ProjectId,
                condition,
                sort,
                sortByField,
                search,
                // pageSize,
                // offset,
            );
            const result = {};
            result.count = locations.getLocations.length;
            result.rows = locations.getLocations.slice(offset, offset + pageSize);
            result.defaultLocation = locations.defaultLocation;
            return result;
            // return locations;
        } catch (e) {
            throw (null, e);
        }
    },
    async setLocationNotificationPreference() {
        try {
            const getLocationsList = await Locations.findAll({
                where: { isDeleted: false },
            });
            const projectList = await Project.findAll({
                where: { isDeleted: false },
            });
            for (let index = 0; index < projectList.length; index += 1) {
                const project = projectList[index];
                const membersList = await Member.findAll({
                    where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
                });
               for (const member of membersList) {
                    for (const location of getLocationsList) {
                        const object = {
                            MemberId: member.id,
                            ProjectId: project.id,
                            LocationId: location.id,
                            instant: false,
                            dailyDigest: false,
                            ParentCompanyId: member.ParentCompanyId,
                            isDeleted: false,
                        };
                        await LocationNotificationPreferences.createInstance(object);
                    }
                }

                if (+projectList.length === index + 1) {
                    return {
                        error: false,
                        status: 'done',
                    };
                }
            }
        } catch (e) {
            throw (null, e);
        }
    },
    async setLocationNotificationPreferenceForAMember(LocationId, ProjectId) {
        try {
            const membersList = await Member.findAll({
                where: { isDeleted: false, ProjectId: +ProjectId, status: 'completed' },
            });
            for (const member of membersList) {
                const object = {
                    MemberId: member.id,
                    ProjectId,
                    LocationId,
                    follow: false,
                    ParentCompanyId: member.ParentCompanyId,
                    isDeleted: false,
                };

                const findDefaultLocationPath = await Locations.findOne({
                    where: {
                        ProjectId,
                        isDefault: true,
                        isDeleted: false,
                    },
                });

                const checkMemberFollowDefaultLocationOrNot =
                    await LocationNotificationPreferences.findOne({
                        where: {
                            MemberId: member.id,
                            ProjectId,
                            follow: true,
                            LocationId: findDefaultLocationPath.id,
                        },
                    });

                if (checkMemberFollowDefaultLocationOrNot) {
                    object.follow = true;
                }

                await LocationNotificationPreferences.createInstance(object);
            }

            return {
                error: false,
                status: 'done',
            };

        } catch (e) {
            throw (null, e);
        }
    },
    async updateMemberLocationPreference(req) {
        try {
            const memberLocationPreference = req.body.chosenMemberPreference;
            let location;
            // eslint-disable-next-line no-await-in-loop
            for (const preference of memberLocationPreference) {
            location = await LocationNotificationPreferences.update(preference, {
                where: {
                id: preference.id,
                },
            });
            }
            const findDefaultLocationPath = await Locations.findOne({
                where: {
                    ProjectId: memberLocationPreference[0].ProjectId,
                    isDefault: true,
                    isDeleted: false,
                },
            });
            const checkAllLocationFollowOrNot = await LocationNotificationPreferences.findOne({
                where: {
                    LocationId: { [Op.ne]: findDefaultLocationPath.id },
                    MemberId: memberLocationPreference[0].MemberId,
                    ProjectId: memberLocationPreference[0].ProjectId,
                    follow: false,
                    isDeleted: false,
                },
            });
            if (!checkAllLocationFollowOrNot) {
                await LocationNotificationPreferences.update(
                    { follow: true },
                    {
                        where: {
                            MemberId: memberLocationPreference[0].MemberId,
                            ProjectId: memberLocationPreference[0].ProjectId,
                            LocationId: findDefaultLocationPath.id,
                            isDeleted: false,
                        },
                    },
                );
            } else {
                await LocationNotificationPreferences.update(
                    { follow: false },
                    {
                        where: {
                            MemberId: memberLocationPreference[0].MemberId,
                            ProjectId: memberLocationPreference[0].ProjectId,
                            LocationId: findDefaultLocationPath.id,
                            isDeleted: false,
                        },
                    },
                );
            }
            return location;
        } catch (e) {
            console.log(e);
            throw (null, e);
        }
    },
    async createDefaultLocationPathForExistingProject() {
        try {
            const projectList = await Project.findAll({
                where: { isDeleted: false },
            });
            for (let projectIndex = 0; projectIndex < projectList.length; projectIndex += 1) {
                const project = projectList[projectIndex];
                const memberDetails = await Member.findOne({
                    where: {
                        UserId: +project.createdBy,
                        ProjectId: +project.id,
                        isDeleted: false,
                        isActive: true,
                    },
                });
                if (memberDetails) {
                    const locationObject = {
                        ProjectId: +project.id,
                        ParentCompanyId: +project.ParentCompanyId,
                        notes: null,
                        MemberId: memberDetails.id,
                        createdBy: +project.createdBy,
                        platform: 'web',
                        locationName: project.projectName,
                        locationPath: project.projectName,
                        isDefault: true,
                    };
                    const location = await Locations.create(locationObject);
                    const membersList = await Member.findAll({
                        where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
                    });
                    for (const member of membersList) {
                        const object = {
                            MemberId: +member.id,
                            ProjectId: +project.id,
                            LocationId: location.id,
                            follow: false,
                            ParentCompanyId: +member.ParentCompanyId,
                            isDeleted: false,
                        };
                        if (+member.RoleId === 2) {
                            object.follow = true;
                        }
                        await LocationNotificationPreferences.createInstance(object);
                    }

                    if (+projectList.length === projectIndex + 1) {
                        return {
                            error: false,
                            status: 'done',
                        };
                    }
                }
            }
        } catch (e) {
            throw (null, e);
        }
    },
    async createDefaultLocationIDForExistingBookings() {
        try {
            const projectList = await Project.findAll({
                where: {
                    isDeleted: false,
                },
            });
            for (let projectIndex = 0; projectIndex < projectList.length; projectIndex += 1) {
                const project = projectList[projectIndex];
                const condition = {
                    ProjectId: project.id,
                    LocationId: null,
                };
                const locations = await Locations.getDefaultLocation(condition);
                const locationId = locations.id;
                await DeliveryRequest.update(
                    { LocationId: +locationId },
                    {
                        where: condition,
                    },
                );

                await CraneRequest.update(
                    { LocationId: +locationId },
                    {
                        where: condition,
                    },
                );

                await ConcreteRequest.update(
                    { LocationId: +locationId },
                    {
                        where: condition,
                    },
                );
                if (+projectList.length === projectIndex + 1) {
                    return {
                        error: false,
                        status: 'done',
                    };
                }
            }
        } catch (e) {
            throw (null, e);
        }
    },

    async findAvailableTimeSlot(payload) {
        try {
            let {
                date, equipmentId, timeZone, GateId,
                DeliveryId, CraneId, ConcreteId, InspectionId, ProjectId, bookingType, currentBookingId
            } = payload;

            const eventTimeZone = await TimeZone.findOne({
                where: { isDeleted: false, location: timeZone },
                attributes: ['id', 'location', 'isDayLightSavingEnabled', 'timeZoneOffsetInMinutes', 'dayLightSavingTimeInMinutes', 'timezone'],
            });

            if (eventTimeZone?.timezone) {
                timeZone = eventTimeZone.timezone;
            }

            const dayStart = moment.utc(date).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            const dayEnd = moment.utc(date).endOf('day').format('YYYY-MM-DD HH:mm:ss');

            const projectSetting = await ProjectSettings.findOne({ where: { ProjectId } });

            const bookingTypesMap = {
                deliveryRequest: {
                    model: DeliveryRequest,
                    startKey: 'deliveryStart',
                    endKey: 'deliveryEnd',
                    windowKey: 'deliveryWindowOpentime',
                    calendarFlag: 'isApplicableToDelivery',
                    excludeId: DeliveryId
                },
                craneRequest: {
                    model: CraneRequest,
                    startKey: 'craneDeliveryStart',
                    endKey: 'craneDeliveryEnd',
                    windowKey: 'craneWindowOpentime',
                    calendarFlag: 'isApplicableToCrane',
                    excludeId: CraneId
                },
                concreteRequest: {
                    model: ConcreteRequest,
                    startKey: 'concretePlacementStart',
                    endKey: 'concretePlacementEnd',
                    windowKey: 'concreteWindowOpentime',
                    calendarFlag: 'isApplicableToConcrete',
                    excludeId: ConcreteId
                },
                inspectionRequest: {
                    model: InspectionRequest,
                    startKey: 'inspectionStart',
                    endKey: 'inspectionEnd',
                    windowKey: 'inspectionWindowOpentime',
                    calendarFlag: 'isApplicableToInspection',
                    excludeId: InspectionId
                }
            };

            const allFormattedBookings = [];

            for (const [bookingType, config] of Object.entries(bookingTypesMap)) {
                const { model, startKey, endKey, excludeId } = config;

                const condition = {
                    ProjectId,
                    isDeleted: false,
                    [Op.or]: [
                        { [startKey]: { [Op.between]: [dayStart, dayEnd] } },
                        { [endKey]: { [Op.between]: [dayStart, dayEnd] } }
                    ]
                };

                if (excludeId) {
                    condition[`${bookingType.replace('Request', 'Id')}`] = { [Op.ne]: excludeId };
                }

                const bookings = await model.findAll({
                    where: condition,
                    order: [[startKey, 'ASC']],
                    include: [
                        {
                            required: true,
                            association: 'equipmentDetails',
                            where: { isDeleted: false, isActive: true },
                            attributes: ['id'],
                            include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }]
                        },
                        {
                            required: true,
                            association: 'gateDetails',
                            where: { isDeleted: false, isActive: true },
                            attributes: ['id'],
                            include: [{ association: 'Gate', attributes: ['gateName', 'id'] }]
                        }
                    ]
                });

                const formatted = bookings.map(booking => ({
                    id: booking.id,
                    type: bookingType,
                    start: moment.utc(booking[startKey]).tz(timeZone).format(),
                    end: moment.utc(booking[endKey]).tz(timeZone).format(),
                    gateDetails: booking.gateDetails,
                    equipmentDetail: booking.equipmentDetails,
                    requestType: booking.requestType === 'deliveryRequestWithCrane' ? 'deliveryRequest' : booking.requestType,
                }));

                allFormattedBookings.push(...formatted);
            }

            // Filter and merge all bookings
            const filteredData = await this.filterAndMergeBookings(allFormattedBookings, GateId, equipmentId);

            // Fetch calendar settings
            const calendarCondition = {
                ProjectId,
                isDeleted: false,
                [Op.or]: [
                    { fromDate: { [Op.between]: [dayStart, dayEnd] } },
                    { toDate: { [Op.between]: [dayStart, dayEnd] } }
                ],
            };

            calendarCondition[bookingTypesMap[bookingType].calendarFlag] = true;

            const calenderSettingsData = await CalendarSetting.getAll(calendarCondition);


            const formattedCalendarSettings = calenderSettingsData
                .filter(booking => {
                    try {
                        const equipmentIds = JSON.parse(booking.EquipmentId || '[]').map(Number);
                        const isMatchedEquipment = equipmentId.some(id => equipmentIds.includes(id));

                        const gateIds = JSON.parse(booking.GateId || '[]').map(Number);
                        const isMatchedGate = GateId.some(id => gateIds.includes(id));

                        return isMatchedEquipment || isMatchedGate;
                    } catch (error) {
                        console.log(error, "error in filteer")
                        return false;
                    }
                })
                .map(item => {
                    const fromDateStr = moment(item.fromDate).format('YYYY-MM-DD');
                    const toDateStr = moment(item.toDate).format('YYYY-MM-DD');
                    const rawStart = `${fromDateStr} ${item.startTime}`;
                    const rawEnd = `${toDateStr} ${item.endTime}`;
                    const startDateTime = moment.tz(rawStart, 'YYYY-MM-DD HH:mm', timeZone).format();
                    const endDateTime = moment.tz(rawEnd, 'YYYY-MM-DD HH:mm', timeZone).format();
                    return {
                        id: `calendar-${item.id}`,
                        start: startDateTime,
                        end: endDateTime,
                        title: item.description,
                        calendarEvent: true,
                        isAllDay: item.isAllDay
                    };
                });

            // Step 1: Parse exception lists
            const gateExceptions = projectSetting?.gateExceptions
                ? JSON.parse(projectSetting.gateExceptions)
                : [];

            const equipmentExceptions = projectSetting?.equipmentExceptions
                ? JSON.parse(projectSetting.equipmentExceptions)
                : [];

            // Step 2: Filter formattedBookings by exceptions
            const filteredByExceptions = filteredData.filter(item => {
                console.log(currentBookingId, item.id, bookingType, item.requestType)
                if (currentBookingId === item.id && item.requestType === bookingType) {
                    return true;
                }

                const gateIds = item.gateDetails?.map(gate => gate.Gate?.id).filter(Boolean) || [];
                const equipmentIds = item.equipmentDetail?.map(equip => equip.Equipment?.id).filter(Boolean) || [];

                const hasValidGate = gateIds.some(id => gateExceptions.includes(Number(id)));
                const hasValidEquipment = equipmentIds.some(id => equipmentExceptions.includes(Number(id)));

                return hasValidGate || hasValidEquipment;
            });

            console.log(formattedCalendarSettings, "formattedCalendarSettings")

            // Step 4: Return filtered results
            return [...filteredByExceptions, ...formattedCalendarSettings];

        } catch (err) {
            console.log(err, "error in TimeSlot API");
            throw err;
        }
    }
    ,

    async filterAndMergeBookings(data, gateId, equipmentIds) {
        const gateFiltered = data.filter(item =>
            item.gateDetails.some(gate => gate.Gate.id === gateId)
        );

        const equipmentFiltered = data.filter(item =>
            item.equipmentDetail.some(equip => equipmentIds.includes(equip.Equipment.id))
        );

        // Merge and deduplicate by item.id
        const merged = [...gateFiltered, ...equipmentFiltered];
        const uniqueResults = Array.from(new Map(merged.map(item => [item.id, item])).values());

        return uniqueResults;
    },

    async formatAvailableSlots(availableSlots, timeZone, duration) {
        const AM = [];
        const PM = [];
        availableSlots.forEach(slot => {
            const [hour, minute] = slot.split(':').map(Number); // Split time into hours and minutes
            const period = hour < 12 ? 'AM' : 'PM'; // Determine AM or PM
            const formattedHour = hour % 12 === 0 ? 12 : hour % 12; // Convert to 12-hour format
            const formattedTime = `${formattedHour}:${minute.toString().padStart(2, '0')}`; // Format time

            if (period === 'AM') {
                AM.push(formattedTime);
            } else {
                PM.push(formattedTime);
            }
        });

        return { AM, PM };
    }


};
module.exports = locationService;
