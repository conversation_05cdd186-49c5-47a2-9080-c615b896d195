const mockStripe = {
  customers: {
    create: jest.fn(),
    retrieve: jest.fn(),
    list: jest.fn(),
  },
  subscriptions: {
    create: jest.fn(),
    del: jest.fn(),
    update: jest.fn(),
    retrieve: jest.fn(),
    list: jest.fn(),
  },
};

// Mock stripe module
jest.mock('stripe', () => () => mockStripe);

// Mock generatePassword helper
jest.mock('../helpers/generatePassword', () => ({
  generatePassword: jest.fn(() => 'testPassword123'),
}));

// Mock helper module
const helper = require('../../helpers/domainHelper');

// Mock global.io
global.io = {
  emit: jest.fn(),
};

// Mock helper.returnProjectModel to return the expected models
jest.mock('../../helpers/domainHelper', () => ({
  ...jest.requireActual('../../helpers/domainHelper'),
  returnProjectModel: jest.fn().mockImplementation(async () => {
    // Set the global variables that the service expects
    global.publicUser = { findOne: jest.fn() };
    global.publicMember = { findOne: jest.fn() };
    global.publicProject = { findOne: jest.fn() };

    return {
      User: {
        findOne: jest.fn(),
        create: jest.fn(),
        createInstance: jest.fn(),
      },
      Member: {
        findOne: jest.fn(),
        create: jest.fn(),
        createInstance: jest.fn(),
        createMultipleInstance: jest.fn(),
      },
      Project: {
        findOne: jest.fn(),
        create: jest.fn(),
        createInstance: jest.fn(),
      },
    };
  }),
  getDynamicModel: jest.fn().mockResolvedValue({
    User: {
      findOne: jest.fn(),
      create: jest.fn(),
      createInstance: jest.fn(),
    },
    Member: {
      findOne: jest.fn(),
      create: jest.fn(),
      createInstance: jest.fn(),
    },
    Project: {
      findOne: jest.fn(),
      create: jest.fn(),
      createInstance: jest.fn(),
    },
  }),
}));

// Mock models
jest.mock('../../models', () => ({
  Sequelize: {
    and: jest.fn((conditions) => ({ [Symbol.for('and')]: conditions })),
    literal: jest.fn((value) => ({ [Symbol.for('literal')]: value })),
    where: jest.fn((column, operator, value) => ({ column, operator, value })),
    fn: jest.fn((functionName) => ({ functionName })),
    col: jest.fn((columnName) => ({ columnName })),
    Op: {
      and: Symbol('and'),
      or: Symbol('or'),
      eq: Symbol('eq'),
      ne: Symbol('ne'),
      gt: Symbol('gt'),
      gte: Symbol('gte'),
      lt: Symbol('lt'),
      lte: Symbol('lte'),
      in: Symbol('in'),
      notIn: Symbol('notIn'),
      like: Symbol('like'),
      notLike: Symbol('notLike'),
      iLike: Symbol('iLike'),
      notILike: Symbol('notILike'),
      between: Symbol('between'),
      notBetween: Symbol('notBetween'),
      is: Symbol('is'),
      not: Symbol('not'),
    },
  },
  NotificationPreference: {
    findOne: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
  },
  NotificationPreferenceItem: {
    findOne: jest.fn(),
    findAll: jest.fn().mockResolvedValue([
      {
        id: 1,
        description: 'Test notification',
        itemId: 1,
        emailNotification: true,
        inappNotification: false,
        isDeleted: false,
      },
      {
        id: 7,
        description: 'When a comment is added to a delivery/crane/concrete request',
        itemId: 4,
        emailNotification: true,
        inappNotification: false,
        isDeleted: false,
      },
    ]),
    create: jest.fn(),
  },
  DigestNotification: {
    create: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findByPk: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    count: jest.fn(),
    getPlansAndProjects: jest.fn(),
    getProjectsProjectAdmin: jest.fn(),
    getMemberProject: jest.fn(),
    createInstance: jest.fn(),
    getAllProjectsForBilling: jest.fn(),
    getAllProjects: jest.fn(),
    retoolParentCompanyWithProjects: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    getBy: jest.fn(),
    createInstance: jest.fn(),
    createMultipleInstance: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
    findByPk: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    getBy: jest.fn(),
    createInstance: jest.fn(),
  },
  ParentCompany: {
    findOne: jest.fn(),
    getBy: jest.fn(),
  },
  NotificationPreference: {
    findOne: jest.fn(),
    createInstance: jest.fn(),
  },
  TimeZone: {
    findOne: jest.fn(),
    getAll: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  StripePlan: {
    findOne: jest.fn(),
    getBy: jest.fn(),
  },
  Company: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  Role: {
    findOne: jest.fn(),
    getBy: jest.fn(),
  },
  Plan: {
    findOne: jest.fn(),
    getBy: jest.fn(),
  },
  StripeSubscription: {
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    createInstance: jest.fn(),
    updateInstance: jest.fn(),
    getBy: jest.fn(),
  },
  ProjectSettings: {
    findOne: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
  },
  RestrictEmail: {
    findOne: jest.fn(),
  },
  Notification: {
    create: jest.fn(),
  },
  DeliveryPersonNotification: {
    create: jest.fn(),
  },
  Locations: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  LocationNotificationPreferences: {
    findOne: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
  },
  ProjectBillingHistories: {
    findOne: jest.fn(),
    create: jest.fn(),
    getProjectsWithBillingHistories: jest.fn(),
  },
}));

// Mock other dependencies
jest.mock('../stripeService', () => ({
  cancelSubscription: jest.fn(),
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn().mockResolvedValue({
    Member: { findOne: jest.fn() },
    User: { findOne: jest.fn() },
    Project: { findOne: jest.fn() },
  }),
}));

jest.mock('../../helpers/notificationHelper', () => ({
  createNotification: jest.fn(),
}));

jest.mock('../../mailer', () => ({
  sendMail: jest.fn(),
}));

jest.mock('../../helpers/apiError', () => {
  return jest.fn().mockImplementation((message, statusCode) => ({
    message,
    statusCode,
  }));
});

jest.mock('../deepLinkService', () => ({
  generateDeepLink: jest.fn(),
  getGuestUserDeepLink: jest.fn(),
}));

jest.mock('../../middlewares/awsConfig', () => ({
  logisticPlanUpload: jest.fn(),
}));

// Mock moment
jest.mock('moment', () => {
  const originalMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((date) => {
    if (date) {
      return originalMoment(date);
    }
    return originalMoment('2023-01-01');
  });
  mockMoment.format = jest.fn((format) => {
    if (format === 'YYYY-MM-DD') return '2023-01-01';
    if (format === 'YYYY-MM-DD 00:00:00+00') return '2023-01-01 00:00:00+00';
    return '2023-01-01';
  });
  mockMoment.diff = jest.fn(() => 30);
  return mockMoment;
});

// Mock fs
jest.mock('fs', () => ({
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
}));

// Mock axios
jest.mock('axios', () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

// Mock PDFDocument
jest.mock('pdf-lib', () => ({
  PDFDocument: {
    load: jest.fn(),
  },
}));

// Mock Worker
jest.mock('worker_threads', () => ({
  Worker: jest.fn(),
}));

// Mock credit-card-type
jest.mock('credit-card-type', () => jest.fn());

// Mock Cryptr
jest.mock('cryptr', () => {
  return jest.fn().mockImplementation(() => ({
    encrypt: jest.fn((text) => `encrypted_${text}`),
    decrypt: jest.fn((text) => text.replace('encrypted_', '')),
  }));
});

// Mock path
jest.mock('path', () => ({
  join: jest.fn(),
}));

// Mock http-status
jest.mock('http-status', () => ({
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
}));

describe('ProjectService', () => {
  let projectService;

  beforeEach(() => {
    jest.clearAllMocks();

    // Import models
    const {
      User,
      Member,
      Project,
      Enterprise,
      StripePlan,
      Locations,
      LocationNotificationPreferences,
      NotificationPreference,
      NotificationPreferenceItem,
    } = require('../../models');

    // Set global variables
    global.publicUser = User;
    global.publicMember = Member;
    global.publicProject = Project;
    global.domainNewName = 'test.com';

    // Require the service after setting up mocks
    projectService = require('../projectService');

    // Restore any spies that might have been set in previous tests
    if (
      projectService.processSingleStripeData &&
      projectService.processSingleStripeData.mockRestore
    ) {
      projectService.processSingleStripeData.mockRestore();
    }
    if (projectService.returnProjectModel && projectService.returnProjectModel.mockRestore) {
      projectService.returnProjectModel.mockRestore();
    }
  });

  describe('existProject', () => {
    const { Project, Member } = require('../../models');

    test('should return success when project does not exist', async () => {
      const inputData = {
        user: { id: 1, email: '<EMAIL>' },
        body: { projectName: 'Test Project' },
      };
      const mockMember = { ParentCompanyId: 1 };
      const done = jest.fn();

      Member.findOne.mockResolvedValue(mockMember);
      Project.findOne.mockResolvedValue(null);
      const { Enterprise } = require('../../models');
      Enterprise.findOne.mockResolvedValue({ domainName: 'test.com' });

      // Mock helper functions
      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');

      await projectService.existProject(inputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'success' }, false);
    });

    test('should return error when project already exists', async () => {
      const inputData = {
        user: { id: 1, email: '<EMAIL>' },
        body: { projectName: 'Test Project' },
      };
      const mockMember = { ParentCompanyId: 1 };
      const mockProject = { id: 1, projectName: 'Test Project' };
      const done = jest.fn();

      Member.findOne.mockResolvedValue(mockMember);
      Project.findOne.mockResolvedValue(mockProject);
      const { Enterprise } = require('../../models');
      Enterprise.findOne.mockResolvedValue({ domainName: 'test.com' });

      // Mock helper functions
      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');

      await projectService.existProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Project Name Already exist.' });
    });
  });

  describe('findProjectPlan', () => {
    const { Project, StripePlan } = require('../../models');

    test('should return error when plan does not exist', async () => {
      const inputData = {
        body: { PlanId: 999 },
        user: { email: '<EMAIL>' },
      };
      const done = jest.fn();

      StripePlan.findOne.mockResolvedValue(null);
      const { Enterprise } = require('../../models');
      Enterprise.findOne.mockResolvedValue({ domainName: 'test.com' });

      // Mock helper functions
      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');

      await projectService.findProjectPlan(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Plan Does not exist.' });
    });

    test('should return error when project does not exist', async () => {
      const inputData = {
        body: { PlanId: 1, ProjectId: 999, stripeCustomerId: 'cus_123' },
        user: { email: '<EMAIL>' },
      };
      const mockPlan = {
        id: 1,
        Plan: { planType: 'project plan' },
      };
      const done = jest.fn();

      StripePlan.getBy.mockResolvedValue(mockPlan);
      const { Enterprise, User, Member, StripeSubscription } = require('../../models');
      Enterprise.findOne.mockResolvedValue({ domainName: 'test.com' });
      User.findOne.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      StripeSubscription.create.mockResolvedValue({ id: 1 });

      // Mock stripe subscriptions
      mockStripe.subscriptions.list.mockResolvedValue({
        data: [{ id: 'sub_123' }],
      });

      // Mock helper functions
      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');
      jest.spyOn(projectService, 'createNewProject').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.findProjectPlan(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should return error when already subscribed to same plan', async () => {
      const inputData = {
        body: { PlanId: 1, ProjectId: 1, stripeCustomerId: 'cus_123' },
        user: { email: '<EMAIL>' },
      };
      const mockPlan = {
        id: 1,
        Plan: { planType: 'project plan' },
      };
      const done = jest.fn();

      StripePlan.getBy.mockResolvedValue(mockPlan);
      const { Enterprise, User, Member, StripeSubscription } = require('../../models');
      Enterprise.findOne.mockResolvedValue({ domainName: 'test.com' });
      User.findOne.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      StripeSubscription.create.mockResolvedValue({ id: 1 });

      // Mock stripe subscriptions
      mockStripe.subscriptions.list.mockResolvedValue({
        data: [{ id: 'sub_123' }],
      });

      // Mock helper functions
      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');
      jest.spyOn(projectService, 'createNewProject').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.findProjectPlan(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });
  });

  describe('getPlansAndProjects', () => {
    const { Project } = require('../../models');

    test('should get plans and projects successfully', async () => {
      const inputData = {
        user: { id: 1 },
        params: { pageNo: 1, pageSize: 10 },
        body: {},
      };
      const mockProjectData = {
        rows: [
          { id: 1, projectName: 'Project 1' },
          { id: 2, projectName: 'Project 2' },
        ],
        count: 2,
      };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      Project.getPlansAndProjects.mockResolvedValue(mockProjectData);
      jest
        .spyOn(projectService, 'getStripeDate')
        .mockImplementation((_, rows, __, ___, callback) => {
          callback(rows, false);
        });

      await projectService.getPlansAndProjects(inputData, done);

      expect(done).toHaveBeenCalledWith({ rows: mockProjectData.rows, count: 2 }, false);
    });

    test('should handle getStripeDate error', async () => {
      const inputData = {
        user: { id: 1 },
        params: { pageNo: 1, pageSize: 10 },
        body: {},
      };
      const mockProjectData = {
        rows: [{ id: 1, projectName: 'Project 1' }],
        count: 1,
      };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      Project.getPlansAndProjects.mockResolvedValue(mockProjectData);
      jest
        .spyOn(projectService, 'getStripeDate')
        .mockImplementation((_, __, ___, ____, callback) => {
          callback(null, { message: 'Stripe error' });
        });

      await projectService.getPlansAndProjects(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Stripe error' });
    });
  });

  describe('getProjects', () => {
    const { Project } = require('../../models');

    test('should get projects with filters', async () => {
      const inputData = {
        user: { id: 1 },
        params: { pageNo: 1, pageSize: 10 },
        body: { search: 'test' },
      };
      const mockProjectData = [{ id: 1, projectName: 'Test Project' }];
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      Project.getProjectsProjectAdmin.mockResolvedValue(mockProjectData);

      await projectService.getProjects(inputData, done);

      expect(done).toHaveBeenCalledWith(mockProjectData, false);
    });

    test('should handle numeric search', async () => {
      const inputData = {
        user: { id: 1 },
        params: { pageNo: 1, pageSize: 10 },
        body: { search: '123' },
      };
      const mockProjectData = [{ id: 123, projectName: 'Project 123' }];
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      Project.getProjectsProjectAdmin.mockResolvedValue(mockProjectData);

      await projectService.getProjects(inputData, done);

      expect(done).toHaveBeenCalledWith(mockProjectData, false);
    });
  });

  describe('getPagination', () => {
    test('should filter results based on search', async () => {
      const projectData = [{ projectName: 'Test Project 1' }, { projectName: 'Another Project' }];
      const done = jest.fn();

      await projectService.getPagination(projectData, 0, 1, [], 'Test', done);

      expect(done).toHaveBeenCalledWith([{ projectName: 'Test Project 1' }], false);
    });

    test('should handle pagination error', async () => {
      const projectData = [];
      const done = jest.fn();

      await projectService.getPagination(projectData, 0, 1, [], null, done);

      expect(done).toHaveBeenCalledWith([], false);
    });
  });

  describe('editMemberProject', () => {
    const { Project, StripeSubscription } = require('../../models');

    test('should edit member project successfully', async () => {
      const req = {
        body: {
          ProjectId: 1,
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          planType: 'trial plan',
          interval: 'monthly',
          projectName: 'Test Project',
          projectLocation: 'Test Location',
          assignedTo: 'Test User',
          email: '<EMAIL>',
        },
        params: { id: 1 },
      };
      const mockProject = {
        id: 1,
        StripeSubscriptionId: 1,
      };
      const mockSubscription = {
        id: 1,
        subscriptionId: 'sub_123',
      };
      const mockPlan = { id: 2 };

      Project.findOne.mockResolvedValue(mockProject);
      StripeSubscription.findOne.mockResolvedValue(mockSubscription);
      mockStripe.subscriptions.update.mockResolvedValue({ id: 'sub_123' });
      Project.update.mockResolvedValue([1]);

      // Mock models used in prepareProjectUpdateData
      const {
        StripePlan,
        Project: ProjectModel,
        StripeSubscription: StripeSubscriptionModel,
      } = require('../../models');
      StripePlan.findOne.mockResolvedValue({ id: 1 });
      ProjectModel.findByPk.mockResolvedValue({ StripeSubscriptionId: 1 });
      StripeSubscriptionModel.getBy.mockResolvedValue({
        id: 1,
        subscriptionId: 'sub_123',
        status: 'active',
        updateInstance: jest.fn().mockResolvedValue({ id: 1 }),
      });

      const result = await projectService.editMemberProject(req);

      expect(result).toBeDefined();
    });

    test('should handle inactive subscription in editMemberProject', async () => {
      const req = {
        body: {
          ProjectId: 1,
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          planType: 'trial plan',
          interval: 'monthly',
          projectName: 'Test Project',
          projectLocation: 'Test Location',
          assignedTo: 'Test User',
          email: '<EMAIL>',
        },
        params: { id: 1 },
      };
      const mockProject = {
        id: 1,
        StripeSubscriptionId: 1,
      };
      const mockSubscription = {
        id: 1,
        subscriptionId: 'sub_123',
        status: 'inactive',
      };
      const mockPlan = { id: 1 };

      Project.findOne.mockResolvedValue(mockProject);
      StripeSubscription.findOne.mockResolvedValue(mockSubscription);
      Project.update.mockResolvedValue([1]);

      // Mock models used in prepareProjectUpdateData
      const {
        StripePlan,
        Project: ProjectModel,
        StripeSubscription: StripeSubscriptionModel,
      } = require('../../models');
      StripePlan.findOne.mockResolvedValue({ id: 1 });
      ProjectModel.findByPk.mockResolvedValue({ StripeSubscriptionId: 1 });
      StripeSubscriptionModel.getBy.mockResolvedValue({
        id: 1,
        subscriptionId: 'sub_123',
        status: 'inactive',
      });
      StripeSubscriptionModel.updateInstance.mockResolvedValue({ id: 1 });

      const result = await projectService.editMemberProject(req);

      expect(result).toBeDefined();
    });

    test('should return undefined when no project data', async () => {
      const req = {
        body: {
          ProjectId: 999,
        },
        params: { id: 999 },
      };

      Project.findOne.mockResolvedValue(null);

      const result = await projectService.editMemberProject(req);

      expect(result).toBeUndefined();
    });

    test('should handle errors in editMemberProject', async () => {
      const req = {
        body: {
          ProjectId: 1,
        },
      };

      Project.findOne.mockRejectedValue(new Error('Database error'));

      const result = await projectService.editMemberProject(req);

      expect(result).toBeUndefined();
    });
  });

  describe('getMemberProject', () => {
    const { Project } = require('../../models');

    test('should get member project successfully', async () => {
      const req = { params: { id: 1 } };
      const mockProject = { id: 1, projectName: 'Test Project' };

      Project.getMemberProject.mockResolvedValue(mockProject);

      const result = await projectService.getMemberProject(req);

      expect(result).toEqual(mockProject);
      expect(Project.getMemberProject).toHaveBeenCalledWith({ id: 1 });
    });

    test('should handle errors in getMemberProject', async () => {
      const req = { params: { id: 1 } };

      Project.getMemberProject.mockRejectedValue(new Error('Database error'));

      const result = await projectService.getMemberProject(req);

      expect(result).toBeUndefined();
    });
  });

  describe('assignNewProjectToMember', () => {
    const { Member, ParentCompany, Project, Role, Company } = require('../../models');

    test('should assign new project to member successfully', async () => {
      const req = {
        body: {
          userId: 1,
          MemberId: 1,
          ProjectId: 1,
          RoleId: 1,
          planType: 'trial plan',
          projectName: 'Test Project',
          projectLocation: 'Test Location',
          projectStartDate: '2023-01-01',
          projectEndDate: '2023-12-31',
          firstName: 'John',
          phoneNumber: '**********',
          phoneCode: '+1',
        },
        user: { id: 1 },
      };
      const mockMember = { id: 1, ParentCompanyId: 1 };
      const mockParentCompany = { id: 1 };
      const mockProject = { id: 1, projectName: 'Test Project' };
      const mockRole = { id: 1 };
      const mockCompany = { id: 1 };
      const mockNewMember = { id: 2 };
      const mockStripePlan = { PlanId: 1 };

      Member.getBy.mockResolvedValue(mockMember);
      Member.findOne.mockResolvedValue({
        id: 1,
        ProjectId: 1,
        ParentCompanyId: 1,
        Company: {
          companyName: 'Test Company',
          website: 'test.com',
          address: 'Test Address',
          secondAddress: 'Test Address 2',
          country: 'Test Country',
          city: 'Test City',
          companyAutoId: 'TC001',
          state: 'Test State',
          zipCode: '12345',
          scope: 'Test Scope',
          logo: 'test-logo.png',
          ParentCompanyId: 1,
        },
      });
      ParentCompany.getBy.mockResolvedValue(mockParentCompany);
      Project.createInstance.mockResolvedValue(mockProject);
      Role.getBy.mockResolvedValue(mockRole);
      Company.create.mockResolvedValue(mockCompany);
      Member.createInstance.mockResolvedValue(mockNewMember);
      const { StripePlan } = require('../../models');
      StripePlan.getBy.mockResolvedValue(mockStripePlan);

      jest.spyOn(projectService, 'createMemberNotificationPreference').mockResolvedValue();
      jest.spyOn(projectService, 'createMemberLocationFollowPreference').mockResolvedValue();

      const result = await projectService.assignNewProjectToMember(req);

      expect(result).toBeDefined();
    });
  });

  describe('projectsBillingHistories', () => {
    const { Project } = require('../../models');

    test('should get projects billing histories successfully', async () => {
      const req = {
        query: {
          pageNo: 1,
          pageSize: 10,
        },
      };
      const mockProjectList = [
        {
          id: 1,
          projectName: 'Project 1',
          billingHistories: [],
        },
        {
          id: 2,
          projectName: 'Project 2',
          billingHistories: [],
        },
      ];

      Project.getAllProjectsForBilling.mockResolvedValue(mockProjectList);

      const result = await projectService.projectsBillingHistories(req);

      expect(result).toEqual({ projectList: mockProjectList, count: 2 });
    });

    test('should handle missing pageNo', async () => {
      const req = {
        query: {
          pageSize: 10,
        },
      };

      const mockProjectList = [];

      Project.getAllProjectsForBilling.mockResolvedValue(mockProjectList);

      const result = await projectService.projectsBillingHistories(req);

      expect(result).toEqual({ projectList: mockProjectList, count: 0 });
    });

    test('should handle errors in projectsBillingHistories', async () => {
      const req = { query: {} };
      const error = new Error('Database error');

      Project.getAllProjectsForBilling.mockRejectedValue(error);

      const result = await projectService.projectsBillingHistories(req);

      expect(result).toBeUndefined();
    });
  });

  describe('createMemberNotificationPreference', () => {
    const {
      Project,
      Member,
      NotificationPreference,
      NotificationPreferenceItem,
    } = require('../../models');

    beforeEach(() => {
      // Mock the returnProjectModel function
      jest.spyOn(projectService, 'returnProjectModel').mockResolvedValue();

      NotificationPreferenceItem.findAll.mockResolvedValue([
        {
          id: 1,
          description: 'Test notification',
          itemId: 1,
          emailNotification: true,
          inappNotification: false,
          isDeleted: false,
        },
        {
          id: 7,
          description: 'When a comment is added to a delivery/crane/concrete request',
          itemId: 4,
          emailNotification: true,
          inappNotification: false,
          isDeleted: false,
        },
      ]);
    });

    test('should create notification preferences for single member', async () => {
      const memberData = { id: 1, ProjectId: 1, ParentCompanyId: 1 };

      // Import models
      const {
        User,
        Member,
        Project,
        NotificationPreferenceItem,
        NotificationPreference,
      } = require('../../models');

      // Set global variables
      global.publicUser = User;
      global.publicMember = Member;
      global.publicProject = Project;

      // Mock the returnProjectModel to set global variables
      jest.spyOn(projectService, 'returnProjectModel').mockImplementation(async () => {
        global.publicUser = User;
        global.publicMember = Member;
        global.publicProject = Project;
      });

      // Mock Member.findOne to return member with ProjectId
      Member.findOne.mockResolvedValue({
        id: 1,
        ProjectId: 1,
        ParentCompanyId: 1,
      });

      // Mock NotificationPreferenceItem.findAll
      NotificationPreferenceItem.findAll.mockResolvedValue([
        {
          id: 1,
          description: 'Test notification',
          itemId: 1,
          emailNotification: true,
          inappNotification: false,
          isDeleted: false,
        },
        {
          id: 7,
          description: 'When a comment is added to a delivery/crane/concrete request',
          itemId: 4,
          emailNotification: true,
          inappNotification: false,
          isDeleted: false,
        },
      ]);

      // Mock Project.findOne to return project with timezone
      Project.findOne.mockResolvedValue({
        id: 1,
        toJSON: () => ({
          id: 1,
          TimeZone: {
            id: 1,
            location: 'UTC',
            timeZoneOffsetInMinutes: 0,
          },
        }),
      });

      // Mock Member.update
      Member.update.mockResolvedValue([1]);

      // Mock NotificationPreference.createInstance
      NotificationPreference.createInstance.mockResolvedValue({ id: 1 });

      await projectService.createMemberNotificationPreference(memberData, 'member');

      expect(Project.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1,
        },
        include: [
          {
            where: { isDeleted: false },
            association: 'TimeZone',
            required: false,
            attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
          },
        ],
      });
    });

    test('should create notification preferences for multiple members', async () => {
      const memberData = [
        { id: 1, ProjectId: 1, ParentCompanyId: 1 },
        { id: 2, ProjectId: 2, ParentCompanyId: 1 },
      ];

      // Import models
      const {
        User,
        Member,
        Project,
        NotificationPreferenceItem,
        NotificationPreference,
      } = require('../../models');

      // Set global variables
      global.publicUser = User;
      global.publicMember = Member;
      global.publicProject = Project;

      // Mock the returnProjectModel to set global variables
      jest.spyOn(projectService, 'returnProjectModel').mockImplementation(async () => {
        global.publicUser = User;
        global.publicMember = Member;
        global.publicProject = Project;
      });

      // Mock NotificationPreferenceItem.findAll
      NotificationPreferenceItem.findAll.mockResolvedValue([
        {
          id: 1,
          description: 'Test notification',
          itemId: 1,
          emailNotification: true,
          inappNotification: false,
          isDeleted: false,
        },
      ]);

      // Mock Project.findOne to return project with timezone
      Project.findOne.mockResolvedValue({
        id: 1,
        toJSON: () => ({
          id: 1,
          TimeZone: {
            id: 1,
            location: 'UTC',
            timeZoneOffsetInMinutes: 0,
          },
        }),
      });

      // Mock Member.update
      Member.update.mockResolvedValue([1]);

      // Mock NotificationPreference.createInstance
      NotificationPreference.createInstance.mockResolvedValue({ id: 1 });

      await projectService.createMemberNotificationPreference(memberData, 'members');

      expect(Project.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1,
        },
        include: [
          {
            where: { isDeleted: false },
            association: 'TimeZone',
            required: false,
            attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
          },
        ],
      });
    });

    test('should handle project without timezone', async () => {
      const memberData = { id: 1, ProjectId: 1, ParentCompanyId: 1 };

      // Import models
      const {
        User,
        Member,
        Project,
        NotificationPreferenceItem,
        NotificationPreference,
      } = require('../../models');

      // Set global variables
      global.publicUser = User;
      global.publicMember = Member;
      global.publicProject = Project;

      // Mock the returnProjectModel to set global variables
      jest.spyOn(projectService, 'returnProjectModel').mockImplementation(async () => {
        global.publicUser = User;
        global.publicMember = Member;
        global.publicProject = Project;
      });

      // Mock Member.findOne to return member with ProjectId
      Member.findOne.mockResolvedValue({
        id: 1,
        ProjectId: 1,
        ParentCompanyId: 1,
      });

      // Mock NotificationPreferenceItem.findAll
      NotificationPreferenceItem.findAll.mockResolvedValue([
        {
          id: 1,
          description: 'Test notification',
          itemId: 1,
          emailNotification: true,
          inappNotification: false,
          isDeleted: false,
        },
      ]);

      // Mock Project.findOne to return project without timezone
      Project.findOne.mockResolvedValue({
        id: 1,
        toJSON: () => ({
          id: 1,
          TimeZone: null,
        }),
      });

      // Mock Member.update
      Member.update.mockResolvedValue([1]);

      // Mock NotificationPreference.createInstance
      NotificationPreference.createInstance.mockResolvedValue({ id: 1 });

      await projectService.createMemberNotificationPreference(memberData, 'member');

      expect(Project.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1,
        },
        include: [
          {
            where: { isDeleted: false },
            association: 'TimeZone',
            required: false,
            attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
          },
        ],
      });
    });
  });

  describe('extendProjectDuration', () => {
    const { Project, StripeSubscription } = require('../../models');

    test('should extend project duration with stripe subscription', async () => {
      const req = {
        body: {
          ProjectId: 1,
          date: '2024-12-31',
        },
      };
      const mockProject = {
        id: 1,
        StripeSubscriptionId: 1,
        status: 'active',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        PlanId: 1,
      };
      const mockSubscription = {
        id: 1,
        subscriptionId: 'sub_123',
      };

      Project.findOne.mockResolvedValue(mockProject);
      StripeSubscription.findOne.mockResolvedValue(mockSubscription);
      mockStripe.subscriptions.update.mockResolvedValue({ id: 'sub_123' });
      Project.update.mockResolvedValue([1]);

      const result = await projectService.extendProjectDuration(req);

      expect(result).toBe(true);
      expect(Project.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1,
        },
        attributes: ['id', 'StripeSubscriptionId', 'status', 'startDate', 'endDate', 'PlanId'],
      });
    });

    test('should extend project duration without stripe subscription', async () => {
      const req = {
        body: {
          ProjectId: 1,
          date: '2024-06-30',
        },
      };
      const mockProject = {
        id: 1,
        StripeSubscriptionId: null,
        status: 'active',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        PlanId: 1,
      };

      Project.findOne.mockResolvedValue(mockProject);
      Project.update.mockResolvedValue([1]);

      const result = await projectService.extendProjectDuration(req);

      expect(result).toBe(true);
      expect(StripeSubscription.findOne).not.toHaveBeenCalled();
    });

    test('should return false when project not found', async () => {
      const req = {
        body: {
          ProjectId: 999,
          date: '2024-12-31',
        },
      };

      Project.findOne.mockResolvedValue(null);

      const result = await projectService.extendProjectDuration(req);

      expect(result).toBe(false);
    });

    test('should return false when subscription not found', async () => {
      const req = {
        body: {
          ProjectId: 1,
          date: '2024-12-31',
        },
      };
      const mockProject = {
        id: 1,
        StripeSubscriptionId: 1,
        status: 'active',
      };

      Project.findOne.mockResolvedValue(mockProject);
      StripeSubscription.findOne.mockResolvedValue(null);

      const result = await projectService.extendProjectDuration(req);

      expect(result).toBe(false);
    });

    test('should handle errors and return false', async () => {
      const req = {
        body: {
          ProjectId: 1,
          date: '2024-12-31',
        },
      };

      Project.findOne.mockRejectedValue(new Error('Database error'));

      await expect(projectService.extendProjectDuration(req)).rejects.toThrow('Database error');
    });
  });

  describe('updateProjectSharingSettings', () => {
    const { ProjectSettings } = require('../../models');

    test('should update project sharing settings successfully', async () => {
      const payload = {
        ProjectId: 1,
        isPublicSharingEnabled: true,
        publicSharingPassword: 'password123',
      };

      ProjectSettings.update.mockResolvedValue([1]);

      const result = await projectService.updateProjectSharingSettings(payload);

      expect(result).toEqual([1]);
      expect(ProjectSettings.update).toHaveBeenCalledWith(payload, {
        where: {
          ProjectId: 1,
        },
      });
    });

    test('should handle update error', async () => {
      const payload = { ProjectId: 1 };
      const error = new Error('Update failed');

      ProjectSettings.update.mockRejectedValue(error);

      const result = await projectService.updateProjectSharingSettings(payload);

      expect(result).toEqual(error);
    });
  });

  describe('uploadLogisticPlan', () => {
    const { ProjectSettings } = require('../../models');
    const awsConfig = require('../../middlewares/awsConfig');

    test('should upload logistic plan successfully', async () => {
      const inputData = {
        params: { ProjectId: 1 },
        file: { originalname: 'test.pdf' },
      };
      const done = jest.fn();

      awsConfig.logisticPlanUpload.mockImplementation((data, callback) => {
        callback([{ fileLink: 'https://example.com/file.pdf', fileName: 'test.pdf' }], null);
      });
      ProjectSettings.update.mockResolvedValue([1]);

      await projectService.uploadLogisticPlan(inputData, done);

      expect(done).toHaveBeenCalledWith(
        {
          fileUrl: 'https://example.com/file.pdf',
          fileName: 'test.pdf',
        },
        false,
      );
    });

    test('should handle upload error', async () => {
      const inputData = {
        params: { ProjectId: 1 },
        file: { originalname: 'test.pdf' },
      };
      const done = jest.fn();
      const error = new Error('Upload failed');

      awsConfig.logisticPlanUpload.mockImplementation((data, callback) => {
        callback(null, error);
      });

      await projectService.uploadLogisticPlan(inputData, done);

      expect(done).toHaveBeenCalledWith(null, error);
    });

    test('should handle ProjectSettings update failure', async () => {
      const inputData = {
        params: { ProjectId: 1 },
        file: { originalname: 'test.pdf' },
      };
      const done = jest.fn();

      awsConfig.logisticPlanUpload.mockImplementation((data, callback) => {
        callback([{ fileLink: 'https://example.com/file.pdf', fileName: 'test.pdf' }], null);
      });
      ProjectSettings.update.mockResolvedValue(null);

      await projectService.uploadLogisticPlan(inputData, done);

      expect(done).toHaveBeenCalledWith(null, null);
    });
  });

  describe('retoolParentCompanyWithProjects', () => {
    const { Project } = require('../../models');

    test('should return sorted company list with projects', async () => {
      const mockProjects = [
        {
          id: 1,
          projectName: 'Project 1',
          ParentCompany: {
            id: 1,
            Company: [
              {
                id: 1,
                companyName: 'Company A',
              },
            ],
          },
        },
        {
          id: 2,
          projectName: 'Project 2',
          ParentCompany: {
            id: 2,
            Company: [
              {
                id: 2,
                companyName: 'Company B',
              },
            ],
          },
        },
      ];

      Project.retoolParentCompanyWithProjects.mockResolvedValue(mockProjects);

      const result = await projectService.retoolParentCompanyWithProjects();

      expect(result).toEqual([
        {
          id: 1,
          companyName: 'Company A',
          projectList: [{ id: 1, projectName: 'Project 1' }],
        },
        {
          id: 2,
          companyName: 'Company B',
          projectList: [{ id: 2, projectName: 'Project 2' }],
        },
      ]);
    });

    test('should handle empty project list', async () => {
      Project.retoolParentCompanyWithProjects.mockResolvedValue([]);

      const result = await projectService.retoolParentCompanyWithProjects();

      expect(result).toEqual([]);
    });

    test('should handle errors in retoolParentCompanyWithProjects', async () => {
      const error = new Error('Database error');
      Project.retoolParentCompanyWithProjects.mockRejectedValue(error);

      await expect(projectService.retoolParentCompanyWithProjects()).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('convertPlan and related methods', () => {
    test('should handle convertPlan for trial plan', async () => {
      const inputData = {
        body: {},
      };
      const planDetails = {
        id: 1,
        Plan: { planType: 'trial' },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();

      await projectService.convertPlan(inputData, planDetails, projectData, loginUser, done);

      expect(done).toHaveBeenCalled();
    });

    test('should handle convertPlan for project plan', async () => {
      const inputData = {
        body: {},
      };
      const planDetails = {
        id: 2,
        Plan: { planType: 'project plan' },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();

      await projectService.convertPlan(inputData, planDetails, projectData, loginUser, done);

      expect(done).toHaveBeenCalled();
    });
  });

  // getCompanyDetails method doesn't exist in the service, skipping these tests

  describe('createNewProject', () => {
    const { Project, Company, Role, Member } = require('../../models');

    test('should create new project successfully', async () => {
      const inputData = {
        body: {
          projectName: 'Test Project',
          PlanId: 1,
          timezone: 'UTC',
        },
        user: { id: 1 },
      };
      const mockProject = { id: 1, projectName: 'Test Project' };
      const mockPlan = {
        id: 1,
        stripePlanName: 'free',
        stripeProductName: 'free',
      };
      const done = jest.fn();

      const { StripePlan, ProjectSettings, User } = require('../../models');
      StripePlan.getBy.mockResolvedValue(mockPlan);
      Project.createInstance.mockResolvedValue(mockProject);
      ProjectSettings.create.mockResolvedValue({ id: 1 });
      User.findOne.mockResolvedValue(null);
      User.createInstance.mockResolvedValue({ id: 1 });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest
        .spyOn(projectService, 'createMember')
        .mockImplementation((data, user, project, publicUserId, publicProjectId, callback) => {
          callback({ success: true }, false);
        });

      await projectService.createNewProject(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle createNewProject error', async () => {
      const inputData = {
        body: {},
        user: { id: 1 },
      };
      const done = jest.fn();
      const error = new Error('Project creation failed');

      // Reset any previous mocks
      jest.restoreAllMocks();

      jest.spyOn(projectService, 'getDynamicModel').mockRejectedValue(error);

      await projectService.createNewProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, error);
    });
  });

  describe('Additional edge cases and error handling', () => {
    beforeEach(() => {
      // Set up module-level variables that are used by various methods
      // These are declared as let variables at the top of the projectService module
      // We need to mock them by setting them on the global scope
      global.publicUser = { findOne: jest.fn() };
      global.publicMember = { findOne: jest.fn() };
      global.publicProject = { findOne: jest.fn() };

      // Mock the returnProjectModel function to set up the global variables
      projectService.returnProjectModel = jest.fn().mockImplementation(() => {
        global.publicUser = { findOne: jest.fn() };
        global.publicMember = { findOne: jest.fn() };
        global.publicProject = { findOne: jest.fn() };
      });

      // Also set them on the module scope
      const projectServiceModule = require('../projectService');
      projectServiceModule.publicUser = global.publicUser;
      projectServiceModule.publicMember = global.publicMember;
      projectServiceModule.publicProject = global.publicProject;

      // Mock the module-level variables by setting them on the module scope
      Object.defineProperty(projectServiceModule, 'publicUser', {
        value: global.publicUser,
        writable: true,
        configurable: true,
      });
      Object.defineProperty(projectServiceModule, 'publicMember', {
        value: global.publicMember,
        writable: true,
        configurable: true,
      });
      Object.defineProperty(projectServiceModule, 'publicProject', {
        value: global.publicProject,
        writable: true,
        configurable: true,
      });

      // Also set them on the global scope for the module
      global.publicUser = global.publicUser;
      global.publicMember = global.publicMember;
      global.publicProject = global.publicProject;

      // Mock the module-level variables by setting them on the module scope
      const modulePath = require.resolve('../projectService');
      const moduleCache = require.cache[modulePath];
      if (moduleCache) {
        moduleCache.exports.publicUser = global.publicUser;
        moduleCache.exports.publicMember = global.publicMember;
        moduleCache.exports.publicProject = global.publicProject;
      }

      // Also set them on the global scope for the module
      global.publicUser = global.publicUser;
      global.publicMember = global.publicMember;
      global.publicProject = global.publicProject;
    });

    // getSubscriptionDetails method doesn't exist in the service, skipping these tests

    test('should handle getStripeDate with recursive processing', async () => {
      const mockUser = { id: 1 };
      const mockInputData = [
        {
          subscriptionId: 'sub_1',
          StripeSubscriptionId: 1,
          PlanId: 2,
          StripeSubscription: { UserId: 1 },
        },
        {
          subscriptionId: 'sub_2',
          StripeSubscriptionId: 2,
          PlanId: 2,
          StripeSubscription: { UserId: 1 },
        },
      ];
      const callback = jest.fn();
      const mockData = [];

      jest
        .spyOn(projectService, 'processSingleStripeData')
        .mockRejectedValue(new Error('Stripe error'));

      await projectService.getStripeDate(mockUser, mockInputData, 0, mockData, callback);

      expect(callback).toHaveBeenCalledWith(null, new Error('Stripe error'));
    });

    test('should handle findProjectPlan with project plan conversion', async () => {
      const inputData = {
        body: { PlanId: 2, ProjectId: 1, stripeCustomerId: 'cus_123' },
        user: { id: 1 },
      };
      const done = jest.fn();

      const { StripePlan, User, Member, StripeSubscription } = require('../../models');
      StripePlan.getBy.mockResolvedValue({
        id: 2,
        Plan: { planType: 'project plan' },
      });
      User.findOne.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      StripeSubscription.create.mockResolvedValue({ id: 1 });

      // Mock stripe subscriptions
      mockStripe.subscriptions.list.mockResolvedValue({
        data: [{ id: 'sub_123' }],
      });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');
      jest.spyOn(projectService, 'createNewProject').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.findProjectPlan(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle findProjectPlan with enterprise plan conversion', async () => {
      const inputData = {
        body: { PlanId: 3, ProjectId: 1 },
        user: { id: 1 },
      };
      const done = jest.fn();

      const { StripePlan } = require('../../models');
      StripePlan.getBy.mockResolvedValue({
        id: 3,
        Plan: { planType: 'enterprise plan' },
      });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');

      await projectService.findProjectPlan(inputData, done);

      expect(done).toHaveBeenCalledWith({ message: 'Please Contact Sales team.' }, false);
    });

    test('should handle createAccountProject with successful project creation', async () => {
      const inputData = {
        body: {
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          email: '<EMAIL>',
        },
        user: { id: 1, email: '<EMAIL>' },
      };
      const done = jest.fn();

      // Import models
      const { Enterprise, Member } = require('../../models');

      // Mock getDynamicModel to set domainNewName
      jest.spyOn(projectService, 'getDynamicModel').mockImplementation(async () => {
        global.domainNewName = 'test.com';
      });

      // Mock domainNewName global variable
      global.domainNewName = 'test.com';

      // Mock Enterprise.findOne to return enterprise with sufficient project count
      Enterprise.findOne.mockResolvedValue({
        id: 1,
        name: 'test.com',
        projectCount: 10,
      });

      // Mock Member.findAll to return existing projects
      Member.findAll.mockResolvedValue([{ id: 1 }, { id: 2 }]); // 2 existing projects

      // Mock initialSetupCreateProject to return success
      jest
        .spyOn(projectService, 'initialSetupCreateProject')
        .mockImplementation((data, callback) => {
          callback({ emailValidationData: {} }, false);
        });

      // Mock returnProjectModel
      jest.spyOn(projectService, 'returnProjectModel').mockResolvedValue();

      // Mock createNewProject to return success
      jest.spyOn(projectService, 'createNewProject').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle createAccountProject with initialSetupCreateProject error', async () => {
      const inputData = {
        body: {
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          email: '<EMAIL>',
        },
        user: { id: 1, email: '<EMAIL>' },
      };
      const done = jest.fn();

      // Import models
      const { Enterprise, Member } = require('../../models');

      // Mock getDynamicModel to set domainNewName
      jest.spyOn(projectService, 'getDynamicModel').mockImplementation(async () => {
        global.domainNewName = 'test.com';
      });

      // Mock domainNewName global variable
      global.domainNewName = 'test.com';

      // Mock Enterprise.findOne to return enterprise with sufficient project count
      Enterprise.findOne.mockResolvedValue({
        id: 1,
        name: 'test.com',
        projectCount: 10,
      });

      // Mock Member.findAll to return existing projects
      Member.findAll.mockResolvedValue([{ id: 1 }]); // 1 existing project

      // Mock initialSetupCreateProject to return error
      jest
        .spyOn(projectService, 'initialSetupCreateProject')
        .mockImplementation((data, callback) => {
          callback(null, { message: 'Something went wrong' });
        });

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
    });

    test('should handle createAccountProject with project limit reached', async () => {
      const inputData = {
        body: {
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          email: '<EMAIL>',
        },
        user: { id: 1, email: '<EMAIL>' },
      };
      const done = jest.fn();

      // Import models
      const { Enterprise, Member } = require('../../models');

      // Mock getDynamicModel to set domainNewName
      jest.spyOn(projectService, 'getDynamicModel').mockImplementation(async () => {
        global.domainNewName = 'test.com';
      });

      // Mock domainNewName global variable
      global.domainNewName = 'test.com';

      // Mock Enterprise.findOne to return enterprise with insufficient project count
      Enterprise.findOne.mockResolvedValue({
        id: 1,
        name: 'test.com',
        projectCount: 1, // Only 1 project allowed
      });

      // Mock Member.findAll to return existing projects
      Member.findAll.mockResolvedValue([{ id: 1 }]); // 1 existing project

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Your Project limit reached.!' });
    });

    test('should handle getProSubStripeDate with subscription details', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        id: 'sub_123',
        status: 'active',
        current_period_start: **********,
        current_period_end: **********,
      });

      const result = await projectService.getProSubStripeDate(newData);

      expect(result).toEqual({
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
        cancel_at_period_end: false,
        subDetail: {
          subscribedOn: expect.any(Date),
          autoRenewal: expect.any(Date),
        },
      });
    });

    // updateModelReferences and updateUserData methods don't exist in the service, skipping these tests

    test('should handle createAccountProject with enterprise not found', async () => {
      const inputData = {
        body: {
          projectName: 'Test Project',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        },
        user: { id: 1 },
      };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');

      const { Enterprise } = require('../../models');
      Enterprise.findOne.mockResolvedValue(null);

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
    });

    test('should handle getProjectList successfully', async () => {
      const req = {
        query: {
          pageNo: 1,
          pageSize: 10,
        },
      };
      const mockProjectList = [
        { id: 1, projectName: 'Project 1' },
        { id: 2, projectName: 'Project 2' },
      ];

      const { Project } = require('../../models');
      Project.getAllProjects.mockResolvedValue(mockProjectList);

      const result = await projectService.getProjectList(req);

      expect(result).toEqual({ projectList: mockProjectList, count: 2 });
    });

    test('should handle getProjectList with error', async () => {
      const req = {
        query: {
          pageNo: 1,
          pageSize: 10,
        },
      };
      const error = new Error('Database error');

      const { Project } = require('../../models');
      Project.getAllProjects.mockRejectedValue(error);

      const result = await projectService.getProjectList(req);

      expect(result).toBeUndefined();
    });

    // Additional test cases for better coverage
    test('should handle getDynamicModel successfully', async () => {
      const inputData = {
        user: { domainName: 'test.com' },
        body: { ParentCompanyId: 1 },
        params: { ParentCompanyId: 1 },
      };

      // Import models
      const { User, Member, Project } = require('../../models');

      // Mock helper.returnProjectModel
      jest.spyOn(helper, 'returnProjectModel').mockResolvedValue({
        User,
        Member,
        Project,
      });

      await projectService.getDynamicModel(inputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
    });

    test('should handle getDomainName successfully', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
      };

      const result = await projectService.getDomainName(inputData);

      expect(result).toBeDefined();
    });

    test('should handle getEnterpriseDomainNameFromEmail with valid email', async () => {
      const email = '<EMAIL>';
      const ParentCompanyId = 1;

      // Import models
      const { User, Member, Enterprise } = require('../../models');

      // Set global variables directly since the method doesn't call returnProjectModel
      global.publicUser = User;
      global.publicMember = Member;

      // Mock User.findOne
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });

      // Mock Member.findOne
      Member.findOne.mockResolvedValue({
        id: 1,
        UserId: 1,
        isAccount: false,
        EnterpriseId: null,
      });

      // Mock Enterprise.findOne
      Enterprise.findOne.mockResolvedValue({
        id: 1,
        name: 'test-enterprise',
        status: 'completed',
      });

      const result = await projectService.getEnterpriseDomainNameFromEmail(email, ParentCompanyId);

      expect(result).toBe('test-enterprise');
    });

    test('should handle getEnterpriseDomainNameFromEmail with user not found', async () => {
      const email = '<EMAIL>';
      const ParentCompanyId = 1;

      // Import models
      const { User, Member } = require('../../models');

      // Set global variables directly since the method doesn't call returnProjectModel
      global.publicUser = User;
      global.publicMember = Member;

      // Mock User.findOne to return null
      User.findOne.mockResolvedValue(null);

      const result = await projectService.getEnterpriseDomainNameFromEmail(email, ParentCompanyId);

      expect(result).toBe('');
    });

    test('should handle createProject successfully', async () => {
      const inputData = {
        body: {
          projectName: 'Test Project',
          PlanId: 1,
        },
        user: { id: 1 },
      };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest
        .spyOn(projectService, 'initialSetupCreateProject')
        .mockImplementation((data, callback) => {
          callback({ emailValidationData: {} }, false);
        });
      jest.spyOn(projectService, 'findProjectPlan').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.createProject(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle createProject with error', async () => {
      const inputData = {
        body: {},
        user: { id: 1 },
      };
      const done = jest.fn();
      const error = new Error('Project creation failed');

      jest.spyOn(projectService, 'getDynamicModel').mockRejectedValue(error);

      await projectService.createProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, error);
    });

    test('should handle createAccountProject with invalid date range', async () => {
      const inputData = {
        body: {
          projectName: 'Test Project',
          startDate: '2023-12-31',
          endDate: '2023-01-01',
        },
        user: { id: 1 },
      };
      const done = jest.fn();

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, {
        message: 'End Date must be higher than Start Date',
      });
    });

    test('should handle createAccountProject with no domain name', async () => {
      const inputData = {
        body: {
          projectName: 'Test Project',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        },
        user: { id: 1 },
      };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue(null);

      // Set the global domainNewName variable to null
      projectService.domainNewName = null;

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
    });

    test('should handle createAccountProject with error', async () => {
      const inputData = {
        body: {
          projectName: 'Test Project',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        },
        user: { id: 1 },
      };
      const done = jest.fn();
      const error = new Error('Database error');

      jest.spyOn(projectService, 'getDynamicModel').mockRejectedValue(error);

      await projectService.createAccountProject(inputData, done);

      expect(done).toHaveBeenCalledWith(null, error);
    });

    test('should handle handleTrialPlan successfully', async () => {
      const inputData = {
        body: { PlanId: 1 },
        user: { id: 1 },
      };
      const planDetails = {
        id: 1,
        Plan: { planType: 'trial plan' },
      };
      const done = jest.fn();

      const { Member, Project, User, StripeSubscription } = require('../../models');
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      Project.count.mockResolvedValue(0);
      User.getBy.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });

      mockStripe.customers.retrieve.mockResolvedValue({ id: 'cus_123' });
      mockStripe.subscriptions.create.mockResolvedValue({ id: 'sub_123', status: 'active' });

      StripeSubscription.createInstance.mockResolvedValue({ id: 1 });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'createStripeCustomer').mockResolvedValue({ id: 'cus_123' });

      await projectService.handleTrialPlan(inputData, planDetails, done);

      expect(done).toHaveBeenCalled();
    });

    test('should handle handleTrialPlan with trial limit reached', async () => {
      const inputData = {
        body: { PlanId: 1 },
        user: { id: 1 },
      };
      const planDetails = {
        id: 1,
        Plan: { planType: 'trial plan' },
      };
      const done = jest.fn();

      const { Member, Project } = require('../../models');
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      Project.count.mockResolvedValue(2); // More than max allowed

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();

      await projectService.handleTrialPlan(inputData, planDetails, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Trial Plan Limit Reached.' });
    });

    test('should handle createStripeCustomer successfully', async () => {
      const user = { id: 1, email: '<EMAIL>' };
      const company = { companyName: 'Test Company' };

      mockStripe.customers.create.mockResolvedValue({ id: 'cus_123' });

      const result = await projectService.createStripeCustomer(user, company);

      expect(result).toEqual({ id: 'cus_123' });
    });

    test('should handle handleProjectPlan successfully', async () => {
      const inputData = {
        body: { PlanId: 1, ProjectId: 1, stripeCustomerId: 'cus_123' },
        user: { id: 1 },
      };
      const planDetails = {
        id: 1,
        Plan: { planType: 'project plan' },
      };
      const done = jest.fn();

      const { User, Member, StripeSubscription } = require('../../models');
      User.findOne.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      StripeSubscription.create.mockResolvedValue({ id: 1 });

      mockStripe.subscriptions.list.mockResolvedValue({
        data: [{ id: 'sub_123' }],
      });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');
      jest.spyOn(projectService, 'createNewProject').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.handleProjectPlan(inputData, planDetails, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle getSubscriptionDetail successfully', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        id: 'sub_123',
        status: 'active',
        current_period_start: **********,
        current_period_end: **********,
      });

      await projectService.getSubscriptionDetail(newData);

      expect(newData.subDetail).toBeDefined();
    });

    test('should handle getSubscriptionDetail with PlanId 1', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 1,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      await projectService.getSubscriptionDetail(newData);

      expect(newData.subDetail).toBeDefined();
    });

    test('should handle processSingleStripeData successfully', async () => {
      const user = { id: 1 };
      const dataItem = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        id: 'sub_123',
        status: 'active',
        current_period_start: **********,
        current_period_end: **********,
      });

      const result = await projectService.processSingleStripeData(user, dataItem);

      expect(result).toBeDefined();
      expect(result.subDetail).toBeDefined();
    });

    test('should handle processSingleStripeData with PlanId 1', async () => {
      const user = { id: 1 };
      const dataItem = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 1,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      const result = await projectService.processSingleStripeData(user, dataItem);

      expect(result).toBeDefined();
      expect(result.cancel_at_period_end).toBe(false);
      expect(result.enableBilling).toBeDefined();
    });

    test('should handle getStripeDate with empty input data', async () => {
      const mockUser = { id: 1 };
      const mockInputData = [];
      const callback = jest.fn();
      const mockData = [];

      // Don't mock processSingleStripeData for this test
      await projectService.getStripeDate(mockUser, mockInputData, 0, mockData, callback);

      expect(callback).toHaveBeenCalledWith([], false);
    });

    test('should handle getStripeDate with error in processSingleStripeData', async () => {
      const mockUser = { id: 1 };
      const mockInputData = [
        {
          subscriptionId: 'sub_1',
          StripeSubscriptionId: 1,
          PlanId: 2,
          StripeSubscription: { UserId: 1 },
        },
      ];
      const callback = jest.fn();
      const mockData = [];

      jest
        .spyOn(projectService, 'processSingleStripeData')
        .mockRejectedValue(new Error('Stripe error'));

      await projectService.getStripeDate(mockUser, mockInputData, 0, mockData, callback);

      expect(callback).toHaveBeenCalledWith(null, new Error('Stripe error'));
    });

    test('should handle getStripeDate with general error', async () => {
      const mockUser = { id: 1 };
      const mockInputData = [
        {
          subscriptionId: 'sub_1',
          StripeSubscriptionId: 1,
          PlanId: 2,
          StripeSubscription: { UserId: 1 },
        },
      ];
      const callback = jest.fn();
      const mockData = [];

      jest
        .spyOn(projectService, 'processSingleStripeData')
        .mockRejectedValue(new Error('General error'));

      await projectService.getStripeDate(mockUser, mockInputData, 0, mockData, callback);

      expect(callback).toHaveBeenCalledWith(null, new Error('General error'));
    });

    test('should handle getProSubStripeDate with PlanId 1', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 1,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      const result = await projectService.getProSubStripeDate(newData);

      expect(result).toBeDefined();
    });

    test('should handle getProSubStripeDate with canceled subscription', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        id: 'sub_123',
        status: 'canceled',
        current_period_start: **********,
        current_period_end: **********,
      });

      const result = await projectService.getProSubStripeDate(newData);

      expect(result).toBeDefined();
    });

    test('should handle getProSubStripeDate with cancel_at_period_end', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        id: 'sub_123',
        status: 'active',
        current_period_start: **********,
        current_period_end: **********,
        cancel_at_period_end: true,
        cancel_at: **********,
      });

      const result = await projectService.getProSubStripeDate(newData);

      expect(result).toBeDefined();
    });

    test('should handle getProSubStripeDate without subscription detail', async () => {
      const newData = {
        subscriptionId: 'sub_123',
        status: 'active',
        PlanId: 2,
        StripeSubscription: {
          subscriptionId: 'sub_123',
        },
        subscribedOn: new Date(),
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue(null);

      const result = await projectService.getProSubStripeDate(newData);

      expect(result).toBeDefined();
    });

    test('should handle isMatchingSearch with string match', async () => {
      const element = { projectName: 'Test Project' };
      const search = 'Test';

      const result = projectService.isMatchingSearch(element, search);

      expect(result).toBe(true);
    });

    test('should handle isMatchingSearch with numeric match', async () => {
      const element = {
        id: '123',
        projectName: 'Test Project',
        projectAdminDetails: [
          {
            User: {
              email: '<EMAIL>',
              firstName: 'Admin',
            },
          },
        ],
      };
      const search = '123';

      const result = projectService.isMatchingSearch(element, search);

      expect(result).toBe(true);
    });

    test('should handle isMatchingSearch with no match', async () => {
      const element = {
        projectName: 'Test Project',
        projectAdminDetails: [
          {
            User: {
              email: '<EMAIL>',
              firstName: 'Test',
            },
          },
        ],
      };
      const search = 'Nonexistent';

      const result = projectService.isMatchingSearch(element, search);

      expect(result).toBe(false);
    });

    test('should handle getPagination with search filter', async () => {
      const projectData = [{ projectName: 'Test Project 1' }, { projectName: 'Another Project' }];
      const done = jest.fn();

      await projectService.getPagination(projectData, 0, 1, [], 'Test', done);

      expect(done).toHaveBeenCalledWith([{ projectName: 'Test Project 1' }], false);
    });

    test('should handle getPagination without search', async () => {
      const projectData = [{ projectName: 'Test Project 1' }, { projectName: 'Another Project' }];
      const done = jest.fn();

      await projectService.getPagination(projectData, 0, 2, [], null, done);

      expect(done).toHaveBeenCalledWith(projectData, false);
    });

    test('should handle getPagination with pagination', async () => {
      const projectData = [
        { projectName: 'Test Project 1' },
        { projectName: 'Another Project' },
        { projectName: 'Third Project' },
      ];
      const done = jest.fn();

      await projectService.getPagination(projectData, 1, 1, [], null, done);

      expect(done).toHaveBeenCalledWith([{ projectName: 'Another Project' }], false);
    });

    test('should handle createMember successfully', async () => {
      const inputData = {
        body: { ParentCompanyId: 1 },
        user: { id: 1 },
      };
      const user = { id: 1, email: '<EMAIL>' };
      const newProject = { id: 1, projectName: 'Test Project' };
      const publicUserId = 1;
      const publicProjectId = 1;
      const done = jest.fn();

      // Import models
      const { Member, Role } = require('../../models');

      // Mock getDynamicModel
      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();

      // Mock Member.findOne
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1 },
      });

      // Mock Role.getBy
      Role.getBy.mockResolvedValue({ id: 1, name: 'Project Admin' });

      // Mock prepareAndCreateMembers to return the member object
      jest.spyOn(projectService, 'prepareAndCreateMembers').mockResolvedValue({ id: 1 });

      await projectService.createMember(
        inputData,
        user,
        newProject,
        publicUserId,
        publicProjectId,
        done,
      );

      expect(done).toHaveBeenCalledWith({ id: 1 }, false);
    });

    test('should handle createMember with error', async () => {
      const inputData = {
        body: { email: '<EMAIL>' },
        user: { id: 1 },
      };
      const user = { id: 1, email: '<EMAIL>' };
      const newProject = { id: 1, projectName: 'Test Project' };
      const publicUserId = 1;
      const publicProjectId = 1;
      const done = jest.fn();
      const error = new Error('Member creation failed');

      const { Member } = require('../../models');
      Member.findOne.mockRejectedValue(error);

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();

      await projectService.createMember(
        inputData,
        user,
        newProject,
        publicUserId,
        publicProjectId,
        done,
      );

      expect(done).toHaveBeenCalledWith(null, error);
    });

    test('should handle prepareAndCreateMembers successfully', async () => {
      const inputData = {
        body: { ParentCompanyId: 1 },
        user: { id: 1 },
      };
      const loginUser = { id: 1, email: '<EMAIL>' };
      const user = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'Test',
        phoneNumber: '**********',
        phoneCode: '+1',
      };
      const newProject = { id: 1, projectName: 'Test Project' };
      const CompanyDet = { Company: { id: 1 } };
      const roleDetails = { id: 1, name: 'Project Admin' };
      const ParentCompanyId = 1;
      const publicUserId = 1;
      const publicProjectId = 1;

      // Import models
      const { Member, Enterprise } = require('../../models');

      // Mock Member.createInstance
      Member.createInstance.mockResolvedValue({ id: 1 });

      // Mock createMemberNotificationPreference
      jest.spyOn(projectService, 'createMemberNotificationPreference').mockResolvedValue();

      // Mock createMemberLocationFollowPreference
      jest.spyOn(projectService, 'createMemberLocationFollowPreference').mockResolvedValue();

      // Mock notificationHelper.memberNotificationCreation
      const notificationHelper = require('../../helpers/notificationHelper');
      notificationHelper.memberNotificationCreation = jest.fn().mockResolvedValue();

      // Mock Enterprise.findOne
      Enterprise.findOne.mockResolvedValue({ id: 1, name: 'test.com' });

      // Mock global.domainNewName
      global.domainNewName = 'test.com';

      // Mock publicMember.createInstance
      global.publicMember = { createInstance: jest.fn().mockResolvedValue({ id: 1 }) };

      const result = await projectService.prepareAndCreateMembers({
        inputData,
        loginUser,
        user,
        newProject,
        CompanyDet,
        roleDetails,
        ParentCompanyId,
        publicUserId,
        publicProjectId,
      });

      expect(result).toEqual({ id: 1 });
    });

    test('should handle getNewDynamicModel successfully', async () => {
      const inputData = {
        user: { email: '<EMAIL>' },
      };

      const { User, Member, Enterprise } = require('../../models');
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      Member.findOne.mockResolvedValue({
        id: 1,
        UserId: 1,
        isAccount: true,
        EnterpriseId: 1,
      });
      Enterprise.findOne.mockResolvedValue({
        id: 1,
        name: 'test-enterprise',
        status: 'completed',
      });

      const result = await projectService.getNewDynamicModel(inputData);

      expect(result).toBe('test-enterprise');
    });

    test('should handle getProjectsCompany successfully', async () => {
      const inputData = {
        user: { id: 1 },
      };
      const projectList = [
        {
          id: 1,
          projectName: 'Project 1',
          ParentCompany: {
            id: 1,
            Company: [
              {
                id: 1,
                companyName: 'Company 1',
              },
            ],
          },
        },
        {
          id: 2,
          projectName: 'Project 2',
          ParentCompany: {
            id: 2,
            Company: [
              {
                id: 2,
                companyName: 'Company 2',
              },
            ],
          },
        },
      ];
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();

      await projectService.getProjectsCompany(inputData, projectList, done);

      expect(done).toHaveBeenCalledWith(
        [
          {
            id: 1,
            companyName: 'Company 1',
            projectList: [{ id: 1, projectName: 'Project 1' }],
          },
          {
            id: 2,
            companyName: 'Company 2',
            projectList: [{ id: 2, projectName: 'Project 2' }],
          },
        ],
        false,
      );
    });

    test('should handle upgradePlan successfully', async () => {
      const inputData = {
        body: { PlanId: 2, ProjectId: 1 },
        user: { id: 1 },
      };
      const done = jest.fn();

      const { Project, StripePlan } = require('../../models');
      Project.findOne.mockResolvedValue({ id: 1, projectName: 'Test Project' });
      StripePlan.getBy.mockResolvedValue({ id: 2, Plan: { planType: 'project plan' } });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'validateUpgradeRequest').mockResolvedValue(true);
      jest
        .spyOn(projectService, 'handlePlanUpgrade')
        .mockImplementation(
          (data, planDetails, projectData, loginUser, existingPlanDetails, done) => {
            done({ success: true }, false);
          },
        );

      await projectService.upgradePlan(inputData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle upgradePlan with validation error', async () => {
      const inputData = {
        body: { PlanId: 2, ProjectId: 1 },
        user: { id: 1 },
      };
      const done = jest.fn();

      const { Project, StripePlan } = require('../../models');
      Project.findOne.mockResolvedValue({ id: 1, projectName: 'Test Project' });
      StripePlan.getBy.mockResolvedValue({ id: 2, Plan: { planType: 'project plan' } });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest
        .spyOn(projectService, 'validateUpgradeRequest')
        .mockResolvedValue({ error: { message: 'Invalid upgrade request' } });

      await projectService.upgradePlan(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Invalid upgrade request' });
    });

    test('should handle validateUpgradeRequest with valid request', async () => {
      const projectData = {
        PlanId: 2,
        ProjectId: 1,
      };

      // Import models
      const { StripePlan, Project } = require('../../models');

      // Mock StripePlan.getBy to return valid plan details for the new plan
      StripePlan.getBy.mockResolvedValueOnce({
        id: 2,
        Plan: { planType: 'project plan' },
      });

      // Mock Project.findByPk to return project with different PlanId and valid status
      Project.findByPk.mockResolvedValue({
        id: 1,
        PlanId: 1, // Different from projectData.PlanId (2)
        status: 'active', // Not 'canceled' or 'overdue'
      });

      // Mock StripePlan.getBy for existing plan details
      StripePlan.getBy.mockResolvedValueOnce({
        id: 1,
        Plan: { planType: 'trial plan' },
      });

      const result = await projectService.validateUpgradeRequest(projectData);

      expect(result).toHaveProperty('planDetails');
      expect(result).toHaveProperty('projectDetails');
      expect(result).toHaveProperty('existingPlanDetails');
    });

    test('should handle validateUpgradeRequest with invalid request', async () => {
      const projectData = {
        id: 1,
        projectName: 'Test Project',
        status: 'inactive',
        PlanId: 1,
        ProjectId: 1,
      };

      const { StripePlan, Project } = require('../../models');
      StripePlan.getBy.mockResolvedValue(null);
      Project.findByPk.mockResolvedValue(null);

      const result = await projectService.validateUpgradeRequest(projectData);

      expect(result).toHaveProperty('error');
    });

    test('should handle convertPlan for trial plan', async () => {
      const inputData = {
        body: {},
      };
      const planDetails = {
        id: 1,
        Plan: { planType: 'trial plan' },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest
        .spyOn(projectService, 'handleTrialPlanData')
        .mockImplementation((data, projectData, loginUser, done) => {
          done({ success: true }, false);
        });

      await projectService.convertPlan(inputData, planDetails, projectData, loginUser, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle convertPlan for project plan', async () => {
      const inputData = {
        body: {},
      };
      const planDetails = {
        id: 2,
        Plan: { planType: 'project plan' },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest
        .spyOn(projectService, 'handleProjectPlanData')
        .mockImplementation((data, projectData, loginUser, done) => {
          done({ success: true }, false);
        });

      await projectService.convertPlan(inputData, planDetails, projectData, loginUser, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle convertPlan for enterprise plan', async () => {
      const inputData = {
        body: {},
      };
      const planDetails = {
        id: 3,
        Plan: { planType: 'enterprise plan' },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'handleEnterprisePlan').mockImplementation((projectData, done) => {
        done({ success: true }, false);
      });

      await projectService.convertPlan(inputData, planDetails, projectData, loginUser, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle handleProjectPlanData successfully', async () => {
      const inputData = {
        body: { PlanId: 2, ProjectId: 1, stripeCustomerId: 'cus_123' },
        user: { id: 1 },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      const { User, Member, StripeSubscription } = require('../../models');
      User.findOne.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      StripeSubscription.create.mockResolvedValue({ id: 1 });

      mockStripe.subscriptions.list.mockResolvedValue({
        data: [{ id: 'sub_123' }],
      });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'getDomainName').mockResolvedValue('test.com');
      jest.spyOn(projectService, 'createNewProject').mockImplementation((data, callback) => {
        callback({ success: true }, false);
      });

      await projectService.handleProjectPlanData(inputData, projectData, loginUser, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle handleTrialPlanData successfully', async () => {
      const inputData = {
        body: { PlanId: 1 },
        user: { id: 1 },
      };
      const projectData = { id: 1 };
      const loginUser = { id: 1 };
      const done = jest.fn();

      const { Member, Project, User } = require('../../models');
      Member.findOne.mockResolvedValue({
        id: 1,
        Company: { id: 1, city: 'Test City' },
      });
      Project.count.mockResolvedValue(0);
      User.getBy.mockResolvedValue({ id: 1, stripeCustomerId: 'cus_123' });

      mockStripe.customers.retrieve.mockResolvedValue({ id: 'cus_123' });

      jest.spyOn(projectService, 'getDynamicModel').mockResolvedValue();
      jest.spyOn(projectService, 'createStripeCustomer').mockResolvedValue({ id: 'cus_123' });

      await projectService.handleTrialPlanData(inputData, projectData, loginUser, done);

      expect(done).toHaveBeenCalled();
    });

    test('should handle handleEnterprisePlan successfully', async () => {
      const projectData = { id: 1 };
      const done = jest.fn();

      await projectService.handleEnterprisePlan(projectData, done);

      expect(done).toHaveBeenCalledWith({ success: true }, false);
    });

    test('should handle getProjectDetails successfully', async () => {
      const req = {
        params: { id: 1 },
      };

      const { Project } = require('../../models');
      Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });

      const result = await projectService.getProjectDetails(req);

      expect(result).toEqual({ id: 1, projectName: 'Test Project' });
    });

    test('should handle getProjectDetails with error', async () => {
      const req = {
        params: { id: 1 },
      };

      const { Project } = require('../../models');
      Project.findByPk.mockRejectedValue(new Error('Database error'));

      const result = await projectService.getProjectDetails(req);

      expect(result).toBeUndefined();
    });

    test('should handle prepareProjectUpdateData with trial plan', async () => {
      const projectData = {
        planType: 'trial plan',
        interval: 'monthly',
        projectName: 'Test Project',
        projectLocation: 'Test Location',
      };
      const projectId = 1;

      const { StripePlan, Project, StripeSubscription } = require('../../models');
      StripePlan.findOne.mockResolvedValue({ id: 1 });
      Project.findByPk.mockResolvedValue({ StripeSubscriptionId: 1 });
      StripeSubscription.getBy.mockResolvedValue({
        id: 1,
        subscriptionId: 'sub_123',
        status: 'active',
        updateInstance: jest.fn().mockResolvedValue({ id: 1 }),
      });

      mockStripe.subscriptions.del.mockResolvedValue({ id: 'sub_123' });
      Project.update.mockResolvedValue([1]);

      const result = await projectService.prepareProjectUpdateData(projectData, projectId);

      expect(result).toBeDefined();
    });

    test('should handle prepareProjectUpdateData with project plan monthly', async () => {
      const projectData = {
        planType: 'project plan',
        interval: 'monthly',
        projectName: 'Test Project',
        projectLocation: 'Test Location',
      };
      const projectId = 1;

      const { StripePlan, Project, StripeSubscription } = require('../../models');
      StripePlan.findOne.mockResolvedValue({ id: 1 });
      Project.findByPk.mockResolvedValue({ StripeSubscriptionId: 1 });
      StripeSubscription.getBy.mockResolvedValue({
        id: 1,
        subscriptionId: 'sub_123',
        status: 'active',
        updateInstance: jest.fn().mockResolvedValue({ id: 1 }),
      });

      mockStripe.subscriptions.del.mockResolvedValue({ id: 'sub_123' });
      Project.update.mockResolvedValue([1]);

      const result = await projectService.prepareProjectUpdateData(projectData, projectId);

      expect(result).toBeDefined();
    });

    test('should handle prepareProjectUpdateData with project plan yearly', async () => {
      const projectData = {
        planType: 'project plan',
        interval: 'yearly',
        projectName: 'Test Project',
        projectLocation: 'Test Location',
      };
      const projectId = 1;

      const { StripePlan, Project, StripeSubscription } = require('../../models');
      StripePlan.findOne.mockResolvedValue({ id: 1 });
      Project.findByPk.mockResolvedValue({ StripeSubscriptionId: 1 });
      StripeSubscription.getBy.mockResolvedValue({
        id: 1,
        subscriptionId: 'sub_123',
        status: 'active',
        updateInstance: jest.fn().mockResolvedValue({ id: 1 }),
      });

      mockStripe.subscriptions.del.mockResolvedValue({ id: 'sub_123' });
      Project.update.mockResolvedValue([1]);

      const result = await projectService.prepareProjectUpdateData(projectData, projectId);

      expect(result).toBeDefined();
    });

    test('should handle prepareProjectUpdateData with inactive subscription', async () => {
      const projectData = {
        planType: 'trial plan',
        interval: 'monthly',
        projectName: 'Test Project',
        projectLocation: 'Test Location',
      };
      const projectId = 1;

      const { StripePlan, Project, StripeSubscription } = require('../../models');
      StripePlan.findOne.mockResolvedValue({ id: 1 });
      Project.findByPk.mockResolvedValue({ StripeSubscriptionId: 1 });
      StripeSubscription.getBy.mockResolvedValue({
        id: 1,
        subscriptionId: 'sub_123',
        status: 'inactive',
        updateInstance: jest.fn().mockResolvedValue({ id: 1 }),
      });

      Project.update.mockResolvedValue([1]);

      const result = await projectService.prepareProjectUpdateData(projectData, projectId);

      expect(result).toBeDefined();
    });

    test('should handle createMemberLocationFollowPreference successfully', async () => {
      const ProjectId = 1;
      const ParentCompanyId = 1;
      const MemberId = 1;
      const projectName = 'Test Project';
      const createdBy = 1;
      const data = 'member';

      // Import models
      const { Locations, LocationNotificationPreferences } = require('../../models');

      // Mock Locations.create
      Locations.create.mockResolvedValue({ id: 1 });

      // Mock LocationNotificationPreferences.createInstance
      LocationNotificationPreferences.createInstance.mockResolvedValue({ id: 1 });

      await projectService.createMemberLocationFollowPreference(
        ProjectId,
        ParentCompanyId,
        MemberId,
        projectName,
        createdBy,
        data,
      );

      expect(Locations.create).toHaveBeenCalledWith({
        ProjectId,
        ParentCompanyId,
        notes: null,
        MemberId,
        createdBy,
        platform: 'web',
        locationName: projectName,
        locationPath: projectName,
        isDefault: true,
      });

      expect(LocationNotificationPreferences.createInstance).toHaveBeenCalledWith({
        MemberId,
        ProjectId,
        LocationId: 1,
        follow: true,
        ParentCompanyId,
        isDeleted: false,
      });
    });

    test('should handle createMemberLocationFollowPreference for multiple members', async () => {
      const ProjectId = 1;
      const ParentCompanyId = 1;
      const MemberId = [{ id: 1 }, { id: 2 }];
      const projectName = 'Test Project';
      const createdBy = 1;
      const data = 'members';

      // Import models
      const { Locations, LocationNotificationPreferences } = require('../../models');

      // Mock Locations.create
      Locations.create.mockResolvedValue({ id: 1 });

      // Mock LocationNotificationPreferences.createInstance
      LocationNotificationPreferences.createInstance.mockResolvedValue({ id: 1 });

      await projectService.createMemberLocationFollowPreference(
        ProjectId,
        ParentCompanyId,
        MemberId,
        projectName,
        createdBy,
        data,
      );

      // The method uses map with async, so we need to wait for the promises
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(Locations.create).toHaveBeenCalledTimes(2);
      expect(LocationNotificationPreferences.createInstance).toHaveBeenCalledTimes(2);
    });

    test('should handle generatePublicUrlForCurrentProject successfully', async () => {
      const data = {
        ProjectId: 1,
        projectName: 'Test Project',
      };

      const deepLinkService = require('../deepLinkService');
      deepLinkService.getGuestUserDeepLink.mockResolvedValue('https://example.com/deeplink');

      await projectService.generatePublicUrlForCurrentProject(data);

      expect(deepLinkService.getGuestUserDeepLink).toHaveBeenCalled();
    });

    test('should handle updateDashboardLogisticPlan successfully', async () => {
      const payload = {
        ProjectId: 1,
        fileUrl: 'https://example.com/file.pdf',
        fileName: 'test.pdf',
      };

      const { ProjectSettings } = require('../../models');
      ProjectSettings.update.mockResolvedValue([1]);

      const result = await projectService.updateDashboardLogisticPlan(payload);

      expect(result).toEqual([1]);
    });

    test('should handle updatedSitePlanInAWS successfully', async () => {
      const req = {
        body: {
          ProjectId: 1,
          file: { originalname: 'test.pdf' },
        },
      };

      const awsConfig = require('../../middlewares/awsConfig');
      awsConfig.logisticPlanUpload.mockImplementation((data, callback) => {
        callback([{ fileLink: 'https://example.com/file.pdf', fileName: 'test.pdf' }], null);
      });

      const { ProjectSettings } = require('../../models');
      ProjectSettings.update.mockResolvedValue([1]);

      const result = await projectService.updatedSitePlanInAWS(req);

      expect(result).toBeDefined();
    });

    test('should handle updatedSitePlanInAWS with upload error', async () => {
      const req = {
        body: {
          ProjectId: 1,
          statusCode: 400,
          imageLinks: [],
        },
      };

      const { ProjectSettings } = require('../../models');
      ProjectSettings.update.mockResolvedValue([1]);

      const result = await projectService.updatedSitePlanInAWS(req);

      expect(result).toBe(true);
    });

    test('should handle getTotalProjects successfully', async () => {
      const { Project } = require('../../models');
      Project.findAll.mockResolvedValue([
        { id: 1, projectName: 'Project 1' },
        { id: 2, projectName: 'Project 2' },
      ]);

      const result = await projectService.getTotalProjects();

      expect(result).toEqual([
        { id: 1, projectName: 'Project 1', status: 'active' },
        { id: 2, projectName: 'Project 2', status: 'active' },
      ]);
    });

    test('should handle getTotalProjects with error', async () => {
      const { Project } = require('../../models');
      Project.findAll.mockRejectedValue(new Error('Database error'));

      await expect(projectService.getTotalProjects()).rejects.toThrow('Database error');
    });

    test('should handle generatePublicUrlForExistingProjects successfully', async () => {
      const { Project } = require('../../models');
      Project.findAll.mockResolvedValue([
        { id: 1, projectName: 'Project 1' },
        { id: 2, projectName: 'Project 2' },
      ]);

      const deepLinkService = require('../deepLinkService');
      deepLinkService.getGuestUserDeepLink.mockResolvedValue('https://example.com/deeplink');

      await projectService.generatePublicUrlForExistingProjects();

      expect(deepLinkService.getGuestUserDeepLink).toHaveBeenCalled();
    });

    test('should handle decodeProjectDetailUrl successfully', async () => {
      const req = {
        body: { encodeUrl: '1_test_encoded' },
      };

      const { Project } = require('../../models');
      Project.findOne.mockResolvedValue({ id: 1, projectName: 'Test Project' });

      const result = await projectService.decodeProjectDetailUrl(req);

      expect(result).toEqual({ projectList: { id: 1, projectName: 'Test Project' } });
    });

    test('should handle decodeProjectDetailUrl with error', async () => {
      const req = {
        body: { encodeUrl: '1_test_encoded' },
      };

      const { Project } = require('../../models');
      Project.findOne.mockRejectedValue(new Error('Database error'));

      await expect(projectService.decodeProjectDetailUrl(req)).rejects.toThrow('Database error');
    });

    test('should handle sendMail successfully', async () => {
      const userData = { name: 'Test User', email: '<EMAIL>' };
      const mailData = { subject: 'Test Subject' };
      const done = jest.fn();

      const MAILER = require('../../mailer');
      MAILER.sendMail.mockImplementation((mailObject, template, subject, message, callback) => {
        callback(mailObject, false);
      });

      await projectService.sendMail(userData, mailData, done);

      expect(done).toHaveBeenCalledWith(userData, false);
    });

    test('should handle sendMail with error', async () => {
      const userData = { name: 'Test User', email: '<EMAIL>' };
      const mailData = { subject: 'Test Subject' };
      const done = jest.fn();
      const error = new Error('Mail error');

      const MAILER = require('../../mailer');
      MAILER.sendMail.mockImplementation((mailObject, template, subject, message, callback) => {
        callback(null, error);
      });

      await projectService.sendMail(userData, mailData, done);

      expect(done).toHaveBeenCalledWith(null, { message: 'Mail error', statusCode: 400 });
    });

    test('should handle getProjectBillingHistories successfully', async () => {
      const req = {
        query: {
          pageNo: 1,
          pageSize: 10,
        },
        params: { id: 1 },
      };
      const mockBillingHistories = [
        {
          id: 1,
          projectName: 'Project 1',
          billingHistories: [],
        },
      ];

      const { ProjectBillingHistories } = require('../../models');
      ProjectBillingHistories.getProjectsWithBillingHistories.mockResolvedValue(
        mockBillingHistories,
      );

      const result = await projectService.getProjectBillingHistories(req);

      expect(result).toEqual({ projectBillingHistories: mockBillingHistories });
    });

    test('should handle getProjectBillingHistories with error', async () => {
      const req = {
        query: {
          pageNo: 1,
          pageSize: 10,
        },
      };

      const { Project } = require('../../models');
      Project.getAllProjectsForBilling.mockRejectedValue(new Error('Database error'));

      const result = await projectService.getProjectBillingHistories(req);

      expect(result).toBeUndefined();
    });
  });
});
