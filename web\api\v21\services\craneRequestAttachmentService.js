const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  Locations,
  LocationNotificationPreferences,
} = require('../models');

let {
  CraneRequest,
  CraneRequestHistory,
  Member,
  User,
  CraneRequestResponsiblePerson,
  DeliveryPersonNotification,
  Project,
  Notification,
  CraneRequestAttachment,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');

const pushNotification = require('../config/fcm');
const awsConfig = require('../middlewares/awsConfig');
const MAILER = require('../mailer');
const { Op } = Sequelize;
let publicUser;
let publicMember;

const craneRequestAttachmentService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const { user, body, params } = inputData;
    let domainName = inputData.domainName;
    const ParentCompanyId = body.ParentCompanyId || params.ParentCompanyId;
    let domainEnterpriseValue, enterpriseValue = null;

    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({ where: { name: domainName.toLowerCase() } });
      if (!domainEnterpriseValue) domainName = '';
    }

    if (!domainName && ParentCompanyId) {
      enterpriseValue = await this.getEnterpriseValue(inputData, ParentCompanyId);
      domainName = enterpriseValue ? enterpriseValue.name.toLowerCase() : domainName;
    }

    const modelObj = await helper.getDynamicModel(domainName);
    ({
      CraneRequest,
      CraneRequestAttachment,
      CraneRequestHistory,
      CraneRequestResponsiblePerson,
      Member,
      Project,
      User,
      Notification,
      DeliveryPersonNotification,
    } = modelObj);

    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: user.email } });
      inputData.user = newUser;
    }
    return true;
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getEnterpriseValue(inputData, ParentCompanyId) {
    const email = inputData.user.email;
    if (email) {
      const userData = await publicUser.findOne({ where: { email } });
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData?.isAccount) {
          return await Enterprise.findOne({ where: { id: memberData.EnterpriseId, status: 'completed' } });
        }
        return await Enterprise.findOne({ where: { ParentCompanyId, status: 'completed' } });
      }
    }
    return await Enterprise.findOne({ where: { ParentCompanyId, status: 'completed' } });
  },

  async getCraneRequestAttachements(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await CraneRequest.findOne({
        where: { CraneRequestId: inputData.params.CraneRequestId, ProjectId: +inputData.params.ProjectId },
      });

      if (!exist) return done(null, { message: 'Crane Booking id does not exist' });

      const attachementList = await CraneRequestAttachment.findAll({
        where: { CraneRequestId: exist.id, isDeleted: false },
        order: [['id', 'DESC']],
      });
      done(attachementList, false);
    } catch (e) {
      done(null, e);
    }
  },

  async deleteCraneRequestAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const { params } = inputData;

      const attachementRecord = await CraneRequestAttachment.findOne({
        where: { id: params.id, ProjectId: +params.ProjectId },
        include: [{ association: 'CraneRequest' }],
      });

      await CraneRequestAttachment.update({ isDeleted: true }, { where: { id: inputData.params.id } });
      const exist = await CraneRequest.findOne({ where: { id: attachementRecord.CraneRequestId, ProjectId: +params.ProjectId } });

      const [memberDetail, locationChosen, memberLocationPreference] = await Promise.all([
        this.getMemberDetail(inputData, +params.ProjectId),
        Locations.findOne({ where: { ProjectId: exist.ProjectId, id: exist.LocationId } }),
        this.getMemberLocationPreference(exist, memberDetail.id),
      ]);

      await this.handleCraneRequestHistoryAndNotifications(
        'Removed',
        exist,
        { loginUser, done },
        memberDetail,
        locationChosen,
        memberLocationPreference
      );
    } catch (e) {
      done(null, e);
    }
  },

  async getMemberDetail(inputData, ProjectId) {
    return await Member.findOne({
      where: [
        Sequelize.and({ UserId: inputData.user.id, ProjectId, isDeleted: false }),
      ],
    });
  },

  async getMemberLocationPreference(exist, memberId) {
    return await LocationNotificationPreferences.findAll({
      where: { ProjectId: exist.ProjectId, LocationId: exist.LocationId, follow: true },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: { [Op.and]: [{ id: { [Op.ne]: memberId } }] },
          include: [{ association: 'User', attributes: ['id', 'firstName', 'lastName', 'email'] }],
        },
      ],
    });
  },

  async handleCraneRequestHistoryAndNotifications(action, exist, context, memberDetail, locationChosen, memberLocationPreference) {
    const { loginUser, done } = context;
    const history = this.createHistoryObject(action, exist, loginUser, memberDetail, locationChosen);
    const notification = { ...history, title: 'Crane Booking Attachment', isDeliveryRequest: false, requestType: 'craneRequest' };

    CraneRequestHistory.createInstance(history);
    const newNotification = await Notification.createInstance(notification);

    const [personData, adminData] = await Promise.all([
      this.getPersonData(exist, memberLocationPreference),
      this.getAdminData(memberLocationPreference, exist.ProjectId, newNotification.MemberId),
    ]);

    await this.dispatchNotifications(newNotification, exist, notification, loginUser, memberDetail, personData, adminData);
    done(history, false);
  },

  async dispatchNotifications(newNotification, exist, notification, loginUser, memberDetail, personData, adminData) {
    await this.processNotifications(newNotification, exist, notification, loginUser, memberDetail, personData, adminData);
  },

  createHistoryObject(action, exist, loginUser, memberDetail, locationChosen) {
    const descriptionBase = `${loginUser.firstName} ${loginUser.lastName} ${action} the file in ${exist.description}`;
    return {
      CraneRequestId: exist.id,
      MemberId: memberDetail.id,
      type: 'attachement',
      description: descriptionBase,
      locationFollowDescription: `${descriptionBase}. Location: ${locationChosen?.locationPath}.`,
      ProjectId: exist.ProjectId,
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
      createdAt: new Date(),
    };
  },

  async getPersonData(exist, locationFollowMembers) {
    return await CraneRequestResponsiblePerson.findAll({
      where: { CraneRequestId: exist.id, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [{ association: 'User', attributes: ['id', 'firstName', 'lastName'] }],
          where: { [Op.and]: { RoleId: { [Op.notIn]: [1, 2] }, id: { [Op.notIn]: locationFollowMembers } } },
          attributes: ['id', 'RoleId'],
        },
      ],
      attributes: ['id'],
    });
  },

  async getAdminData(locationFollowMembers, ProjectId, notificationMemberId) {
    return await Member.findAll({
      where: { [Op.and]: [{ ProjectId, isDeleted: false, id: { [Op.notIn]: locationFollowMembers } }, { id: { [Op.ne]: notificationMemberId } }] },
      include: [{ association: 'User', attributes: ['id', 'firstName', 'lastName'] }],
      attributes: ['id'],
    });
  },

  async processNotifications(notification, exist, history, loginUser, memberDetail, personData, adminData) {
    if (history.memberLocationPreference?.length) {
      await this.sendLocationPreferenceNotifications(history.memberLocationPreference, exist, history);
    }

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      await Project.findByPk(exist.ProjectId),
      notification,
      DeliveryPersonNotification,
      memberDetail,
      loginUser,
      2,
      `${history.type} a file in a`,
      'Crane Request',
      `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
      exist.id
    );

    const checkMemberNotification = await this.getCheckMemberNotification(exist.ProjectId, 2);
    await pushNotification.sendPushNotificationForCrane({ ...history, notificationPreference: checkMemberNotification }, 2, exist.ProjectId);
  },

  async sendLocationPreferenceNotifications(memberLocationPreference, exist, history) {
    await Promise.all([
      pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
        memberLocationPreference,
        exist.CraneRequestId,
        history.locationFollowDescription,
        exist.requestType,
        exist.ProjectId,
        exist.id,
        2
      ),
      notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        exist.ProjectId,
        history.notification.id,
        memberLocationPreference,
        2
      )
    ]);
  },

  async getCheckMemberNotification(ProjectId, itemId) {
    return await NotificationPreference.findAll({
      where: { ProjectId, isDeleted: false },
      attributes: ['id', 'MemberId', 'ProjectId', 'ParentCompanyId', 'NotificationPreferenceItemId', 'instant', 'dailyDigest'],
      include: [{ association: 'NotificationPreferenceItem', where: { id: itemId, isDeleted: false }, attributes: ['id', 'description', 'inappNotification', 'emailNotification'] }],
    });
  },

  async createCraneRequestAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.params;
      const loginUser = inputData.user;
      const exist = await CraneRequest.findOne({ where: { CraneRequestId: incomeData.CraneRequestId, ProjectId: incomeData.ProjectId } });

      if (!exist) return done(null, { message: 'Crane Booking id does not exist' });

      const memberDetail = await this.getMemberDetail(inputData, +incomeData.ProjectId);
      awsConfig.upload(inputData.files, async (result, err) => {
        if (err) return done(null, err);

        const [exist2, locationChosen, memberLocationPreference] = await Promise.all([
          this.getExist2(incomeData, exist),
          Locations.findOne({ where: { ProjectId: exist.ProjectId, id: exist.LocationId } }),
          this.getMemberLocationPreference(exist, memberDetail.id),
        ]);

        const action = 'Attached';
        const context = { loginUser, result, action, memberDetail, done };

        await this.processCraneRequestAttachmentAction(context, inputData, incomeData, exist, exist2, locationChosen, memberLocationPreference);
      });
    } catch (e) {
      done(null, e);
    }
  },

  async getExist2(incomeData, exist) {
    return await CraneRequest.findOne({
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'] }],
            },
          ],
        },
      ],
      where: { CraneRequestId: incomeData.CraneRequestId, ProjectId: incomeData.ProjectId },
    });
  },

  async processCraneRequestAttachmentAction(context, inputData, incomeData, exist, exist2, locationChosen, memberLocationPreference) {
    const { action, result, loginUser, memberDetail, done } = context;
    const userDataMail = exist2.memberDetails;
    await this.sendGuestEmailNotifications(userDataMail, action, inputData, exist);

    const bulkData = this.prepareBulkData(result, inputData.files, exist.id, incomeData.ProjectId);
    if (!bulkData.length) return done(null, { message: 'No files to attach' });

    await CraneRequestAttachment.createMultipleInstance(bulkData);
    await this.handleCraneRequestHistoryAndNotifications(action, exist, { loginUser, done }, memberDetail, locationChosen, memberLocationPreference);
  },

  async sendGuestEmailNotifications(userDataMail, action, inputData, exist) {
    for (const userMail of userDataMail) {
      if (userMail.Member.isGuestUser) {
        const guestMailPayload = {
          email: userMail.Member.User.email,
          guestName: userMail.Member.User.firstName,
          content: `We would like to inform you that ${inputData.user.firstName} ${inputData.user.lastName} ${action} the file in Booking, ${exist.description}.`,
        };
        await MAILER.sendMail(
          guestMailPayload,
          'notifyGuestOnEdit',
          `${action} by ${inputData.user.firstName}`,
          `Attachment ${action} against Crane Booking`,
          async (info, err) => console.log(info, err),
        );
      }
    }
  },

  prepareBulkData(result, files, existId, ProjectId) {
    return result.map((element, index) => {
      const fileData = files[index];
      const [fileName, extension] = this.extractFileInfo(fileData);
      return {
        attachement: element.Location,
        filename: fileName,
        extension,
        CraneRequestId: existId,
        ProjectId,
        isDeleted: false,
      };
    });
  },

  extractFileInfo(fileData) {
    const fileName = fileData.originalname || fileData.name;
    const extension = fileName.split('.').pop();
    return [fileName, extension];
  }
};

module.exports = craneRequestAttachmentService;