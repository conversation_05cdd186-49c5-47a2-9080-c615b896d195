const fs = require('fs');
const moment = require('moment');

// Mock all external dependencies
jest.mock('fs');
jest.mock('../../middlewares/awsConfig');
jest.mock('../puppeteerService', () => ({
  generatePdfBuffer: jest.fn()
}));
jest.mock('../../models', () => ({
  Project: {
    findOne: jest.fn()
  },
  Company: {
    findOne: jest.fn()
  }
}));
jest.mock('moment', () => {
  const mockMoment = jest.fn(() => ({
    add: jest.fn().mockReturnThis(),
    format: jest.fn().mockReturnValue('01/15/2024 08:00 AM')
  }));
  mockMoment.add = jest.fn().mockReturnThis();
  return mockMoment;
});

const awsConfig = require('../../middlewares/awsConfig');
const puppeteerService = require('../puppeteerService');
const pdfInspectionReportService = require('../pdfInspectionReportService');
const { Project, Company } = require('../../models');

describe('pdfInspectionReportService', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock moment
    const mockMomentInstance = {
      format: jest.fn().mockReturnValue('01/15/2024 08:00 AM'),
      add: jest.fn().mockReturnThis()
    };
    moment.mockImplementation(() => mockMomentInstance);
    moment.add = jest.fn().mockReturnValue(mockMomentInstance);

    // Mock fs
    fs.readFileSync.mockReturnValue('<html>$projectName $companyName $generatedDate $generatedBy $reportType $header $data</html>');

    // Mock awsConfig
    awsConfig.reportUpload.mockImplementation((buffer, name, type, callback) => {
      callback('success-url', null);
    });

    // Mock puppeteerService
    puppeteerService.generatePdfBuffer.mockResolvedValue(Buffer.from('pdf-content'));

    // Mock models
    Project.findOne.mockResolvedValue({ projectName: 'Test Project' });
    Company.findOne.mockResolvedValue({ companyName: 'Test Company' });
  });

  describe('extractHeaderSelectionAndBuildTableHeader', () => {
    it('should extract headers and flags correctly for all active headers', () => {
      const mockReq = {
        body: {
          selectedHeaders: [
            { key: 'id', title: 'ID', isActive: true },
            { key: 'description', title: 'Description', isActive: true },
            { key: 'date', title: 'Date', isActive: true },
            { key: 'status', title: 'Status', isActive: true },
            { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
            { key: 'inspectionType', title: 'Inspection Type', isActive: true },
            { key: 'approvedBy', title: 'Approved By', isActive: true },
            { key: 'equipment', title: 'Equipment', isActive: true },
            { key: 'dfow', title: 'DFOW', isActive: true },
            { key: 'gate', title: 'Gate', isActive: true },
            { key: 'company', title: 'Company', isActive: true },
            { key: 'person', title: 'Person', isActive: true },
            { key: 'location', title: 'Location', isActive: true }
          ]
        }
      };

      const result = pdfInspectionReportService.extractHeaderSelectionAndBuildTableHeader(mockReq);

      expect(result.selectedFlags.isIdSelected).toBe(true);
      expect(result.selectedFlags.isDescriptionSelected).toBe(true);
      expect(result.selectedFlags.isDateSelected).toBe(true);
      expect(result.selectedFlags.isStatusSelected).toBe(true);
      expect(result.selectedFlags.isInspectionStatusSelected).toBe(true);
      expect(result.selectedFlags.isInspectionTypeSelected).toBe(true);
      expect(result.selectedFlags.isApprovedBySelected).toBe(true);
      expect(result.selectedFlags.isEquipmentSelected).toBe(true);
      expect(result.selectedFlags.isDfowSelected).toBe(true);
      expect(result.selectedFlags.isGateSelected).toBe(true);
      expect(result.selectedFlags.isCompanySelected).toBe(true);
      expect(result.selectedFlags.isPersonSelected).toBe(true);
      expect(result.selectedFlags.isLocationSelected).toBe(true);
      expect(result.header).toHaveLength(13);
    });

    it('should handle inactive headers correctly', () => {
      const mockReq = {
        body: {
          selectedHeaders: [
            { key: 'id', title: 'ID', isActive: true },
            { key: 'description', title: 'Description', isActive: false }
          ]
        }
      };

      const result = pdfInspectionReportService.extractHeaderSelectionAndBuildTableHeader(mockReq);

      expect(result.selectedFlags.isIdSelected).toBe(true);
      expect(result.selectedFlags.isDescriptionSelected).toBe(false);
      expect(result.header).toHaveLength(1);
    });

    it('should handle empty selectedHeaders array', () => {
      const mockReq = {
        body: {
          selectedHeaders: []
        }
      };

      const result = pdfInspectionReportService.extractHeaderSelectionAndBuildTableHeader(mockReq);

      expect(result.selectedFlags.isIdSelected).toBe(false);
      expect(result.header).toHaveLength(0);
    });

    it('should handle special key mappings correctly', () => {
      const mockReq = {
        body: {
          selectedHeaders: [
            { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
            { key: 'inspectionType', title: 'Inspection Type', isActive: true }
          ]
        }
      };

      const result = pdfInspectionReportService.extractHeaderSelectionAndBuildTableHeader(mockReq);

      expect(result.selectedFlags.isInspectionStatusSelected).toBe(true);
      expect(result.selectedFlags.isInspectionTypeSelected).toBe(true);
    });
  });

  describe('buildTableRows', () => {
    const mockData = [
      {
        InspectionId: 'INS001',
        description: 'Test inspection',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z',
        status: 'Delivered',
        inspectionStatus: 'Passed',
        inspectionType: 'Safety',
        approverDetails: {
          User: { firstName: 'John', lastName: 'Doe' }
        },
        equipmentDetails: [
          { Equipment: { equipmentName: 'Crane A' } }
        ],
        defineWorkDetails: [
          { DeliverDefineWork: { DFOW: 'Work 1' } }
        ],
        gateDetails: [
          { Gate: { gateName: 'Gate 1' } }
        ],
        companyDetails: [
          { Company: { companyName: 'Company A' } }
        ],
        memberDetails: [
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ],
        location: { locationPath: 'Location A' }
      }
    ];

    const mockFlags = {
      isIdSelected: true,
      isDescriptionSelected: true,
      isDateSelected: true,
      isStatusSelected: true,
      isinspectionStatusSelected: true,
      isinspectionTypeSelected: true,
      isApprovedBySelected: true,
      isEquipmentSelected: true,
      isDfowSelected: true,
      isGateSelected: true,
      isCompanySelected: true,
      isPersonSelected: true,
      isLocationSelected: true
    };

    it('should build rows correctly with selected flags', () => {
      const result = pdfInspectionReportService.buildTableRows(mockData, mockFlags, '0');

      expect(result).toHaveLength(1);
      expect(result[0]).toContain('<tr style="border-bottom:1px solid #e0e0e0;font-size:12px">');
      expect(result[0]).toContain('INS001');
      expect(result[0]).toContain('Test inspection');
      expect(result[0]).toContain('Completed'); // status should be converted from 'Delivered' to 'Completed'
      expect(result[0]).toContain('Passed');
      expect(result[0]).toContain('Safety');
      expect(result[0]).toContain('John Doe');
      expect(result[0]).toContain('Crane A');
      expect(result[0]).toContain('Work 1');
      expect(result[0]).toContain('Gate 1');
      expect(result[0]).toContain('Company A');
      expect(result[0]).toContain('Jane Smith');
      expect(result[0]).toContain('Location A');
    });

    it('should handle empty data array', () => {
      const result = pdfInspectionReportService.buildTableRows([], mockFlags, '0');

      expect(result).toHaveLength(0);
    });

    it('should handle timezone offset correctly', () => {
      const result = pdfInspectionReportService.buildTableRows(mockData, mockFlags, '120');

      expect(moment().add).toHaveBeenCalledWith(120, 'minutes');
      expect(result).toHaveLength(1);
    });

    it('should handle missing optional fields', () => {
      const dataWithMissingFields = [{
        InspectionId: 'INS002',
        description: 'Test inspection 2',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z',
        status: 'Pending'
      }];

      const result = pdfInspectionReportService.buildTableRows(dataWithMissingFields, mockFlags, '0');

      expect(result).toHaveLength(1);
      expect(result[0]).toContain('INS002');
      expect(result[0]).toContain('Pending');
    });

    it('should handle different flag combinations', () => {
      const limitedFlags = {
        isIdSelected: true,
        isDescriptionSelected: false,
        isDateSelected: true,
        isStatusSelected: false,
        isinspectionStatusSelected: false,
        isinspectionTypeSelected: false,
        isApprovedBySelected: false,
        isEquipmentSelected: false,
        isDfowSelected: false,
        isGateSelected: false,
        isCompanySelected: false,
        isPersonSelected: false,
        isLocationSelected: false
      };

      const result = pdfInspectionReportService.buildTableRows(mockData, limitedFlags, '0');

      expect(result).toHaveLength(1);
      expect(result[0]).toContain('INS001');
      expect(result[0]).not.toContain('Test inspection');
    });
  });

  describe('buildTableRow', () => {
    const mockItem = {
      InspectionId: 'INS001',
      description: 'Test inspection',
      deliveryStart: '2024-01-15T08:00:00Z',
      deliveryEnd: '2024-01-15T10:00:00Z',
      status: 'Delivered',
      inspectionStatus: 'Passed',
      inspectionType: 'Safety',
      approverDetails: {
        User: { firstName: 'John', lastName: 'Doe' }
      },
      equipmentDetails: [
        { Equipment: { equipmentName: 'Crane A' } }
      ],
      defineWorkDetails: [
        { DeliverDefineWork: { DFOW: 'Work 1' } }
      ],
      gateDetails: [
        { Gate: { gateName: 'Gate 1' } }
      ],
      companyDetails: [
        { Company: { companyName: 'Company A' } }
      ],
      memberDetails: [
        { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
      ],
      location: { locationPath: 'Location A' }
    };

    const mockFlags = {
      isIdSelected: true,
      isDescriptionSelected: true,
      isDateSelected: true,
      isStatusSelected: true,
      isinspectionStatusSelected: true,
      isinspectionTypeSelected: true,
      isApprovedBySelected: true,
      isEquipmentSelected: true,
      isDfowSelected: true,
      isGateSelected: true,
      isCompanySelected: true,
      isPersonSelected: true,
      isLocationSelected: true
    };

    const mockTd = (val) => `<td>${val}</td>`;
    const mockWrapList = (list) => Array.isArray(list) ? list.map(val => `<p>${val}</p>`).join('') : '-';

    it('should build table row correctly', () => {
      const result = pdfInspectionReportService.buildTableRow(mockItem, mockFlags, '0', mockTd, mockWrapList);

      expect(result).toContain('<td>INS001</td>');
      expect(result).toContain('<td>Test inspection</td>');
      expect(result).toContain('<td>Completed</td>'); // status conversion
      expect(result).toContain('<td>Passed</td>');
      expect(result).toContain('<td>Safety</td>');
      expect(result).toContain('<td>John Doe</td>');
      expect(result).toContain('<td><p>Crane A</p></td>');
      expect(result).toContain('<td><p>Work 1</p></td>');
      expect(result).toContain('<td>Gate 1</td>');
      expect(result).toContain('<td><p>Company A</p></td>');
      expect(result).toContain('<td><p>Jane Smith</p></td>');
      expect(result).toContain('<td>Location A</td>');
    });

    it('should handle missing fields gracefully', () => {
      const itemWithMissingFields = {
        InspectionId: 'INS002',
        description: 'Test inspection 2',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z',
        status: 'Pending'
      };

      const result = pdfInspectionReportService.buildTableRow(itemWithMissingFields, mockFlags, '0', mockTd, mockWrapList);

      expect(result).toContain('<td>INS002</td>');
      expect(result).toContain('<td>Pending</td>');
      expect(result).toContain('<td>-</td>'); // missing fields should show '-'
    });
  });

  describe('getApproverName', () => {
    it('should return approver name when approverDetails exists', () => {
      const approverDetails = {
        User: { firstName: 'John', lastName: 'Doe' }
      };

      const result = pdfInspectionReportService.getApproverName(approverDetails);

      expect(result).toBe('John Doe');
    });

    it('should return dash when approverDetails is null', () => {
      const result = pdfInspectionReportService.getApproverName(null);

      expect(result).toBe('-');
    });

    it('should return dash when approverDetails is undefined', () => {
      const result = pdfInspectionReportService.getApproverName(undefined);

      expect(result).toBe('-');
    });
  });

  describe('generatePdfTemplate', () => {
    it('should replace all template placeholders correctly', () => {
      const templatePath = '/test/template.html';
      const projectData = { projectName: 'Test Project' };
      const companyData = { companyName: 'Test Company' };
      const loginUser = { firstName: 'Admin', lastName: 'User' };
      const req = { body: { generatedDate: '01/15/2024' } };
      const header = ['<th>ID</th>', '<th>Description</th>'];
      const content = ['<tr><td>1</td><td>Test</td></tr>'];

      const result = pdfInspectionReportService.generatePdfTemplate(
        templatePath, projectData, companyData, loginUser, req, header, content
      );

      expect(fs.readFileSync).toHaveBeenCalledWith(templatePath, 'utf-8');
      expect(result).toContain('Test Project');
      expect(result).toContain('Test Company');
      expect(result).toContain('01/15/2024');
      expect(result).toContain('Admin User');
      expect(result).toContain('Inspection');
      expect(result).toContain('<th>ID</th><th>Description</th>');
      expect(result).toContain('<tr><td>1</td><td>Test</td></tr>');
    });

    it('should handle empty header and content arrays', () => {
      const templatePath = '/test/template.html';
      const projectData = { projectName: 'Test Project' };
      const companyData = { companyName: 'Test Company' };
      const loginUser = { firstName: 'Admin', lastName: 'User' };
      const req = { body: { generatedDate: '01/15/2024' } };
      const header = [];
      const content = [];

      const result = pdfInspectionReportService.generatePdfTemplate(
        templatePath, projectData, companyData, loginUser, req, header, content
      );

      expect(result).toContain('Test Project');
      expect(result).toContain('Test Company');
    });
  });

  describe('pdfFormatOfInspectionRequest', () => {
    const mockParams = { ProjectId: '1' };
    const mockLoginUser = { firstName: 'Admin', lastName: 'User' };
    const mockData = [
      {
        InspectionId: 'INS001',
        description: 'Test inspection',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z',
        status: 'Delivered',
        inspectionStatus: 'Passed',
        inspectionType: 'Safety'
      }
    ];
    const mockReq = {
      headers: { timezoneoffset: '0' },
      body: {
        selectedHeaders: [
          { key: 'id', title: 'ID', isActive: true }
        ],
        ParentCompanyId: '1',
        reportName: 'test-report',
        exportType: 'pdf',
        generatedDate: '01/15/2024'
      }
    };

    beforeEach(() => {
      Project.findOne.mockResolvedValue({ projectName: 'Test Project' });
      Company.findOne.mockResolvedValue({ companyName: 'Test Company' });
    });

    it('should generate PDF successfully', async () => {
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(Project.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, id: 1 },
        attributes: ['projectName']
      });
      expect(Company.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, ParentCompanyId: 1, isParent: true },
        attributes: ['companyName']
      });
      expect(puppeteerService.generatePdfBuffer).toHaveBeenCalled();
      expect(awsConfig.reportUpload).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith('success-url', false);
    });

    it('should handle project not found', async () => {
      Project.findOne.mockResolvedValue(null);
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: expect.any(String) });
    });

    it('should handle company not found', async () => {
      Company.findOne.mockResolvedValue(null);
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: expect.any(String) });
    });

    it('should handle PDF generation failure', async () => {
      puppeteerService.generatePdfBuffer.mockResolvedValue(null);
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Cannot export the document' });
    });

    it('should handle AWS upload failure', async () => {
      awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
        callback(null, 'Upload error');
      });
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Upload failed' });
    });

    it('should handle database errors', async () => {
      Project.findOne.mockRejectedValue(new Error('Database error'));
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: 'Database error' });
    });

    it('should handle puppeteer service errors', async () => {
      puppeteerService.generatePdfBuffer.mockRejectedValue(new Error('Puppeteer error'));
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: 'Puppeteer error' });
    });

    it('should handle empty data array', async () => {
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, [], mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('success-url', false);
    });

    it('should handle different timezone offsets', async () => {
      const reqWithTimezone = {
        ...mockReq,
        headers: { timezoneoffset: '120' }
      };
      const mockDone = jest.fn();

      await pdfInspectionReportService.pdfFormatOfInspectionRequest(
        mockParams, mockLoginUser, mockData, reqWithTimezone, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('success-url', false);
    });
  });
});