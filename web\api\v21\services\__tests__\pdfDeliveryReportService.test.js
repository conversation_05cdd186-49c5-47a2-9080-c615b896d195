// Mock dependencies first before requiring modules
jest.mock('fs');
jest.mock('../../middlewares/awsConfig');
jest.mock('puppeteer', () => ({
  launch: jest.fn(),
}));
jest.mock('../puppeteerService');
jest.mock('../../models', () => ({
  Project: {
    findOne: jest.fn(),
  },
  Company: {
    findOne: jest.fn(),
  },
}));
jest.mock('moment', () => {
  const mockMomentInstance = {
    format: jest.fn().mockReturnValue('01/15/2024 10:00 AM'),
    add: jest.fn().mockReturnThis(),
  };
  return jest.fn(() => mockMomentInstance);
});

const fs = require('fs');
const moment = require('moment');
const pdfDeliveryReportService = require('../pdfDeliveryReportService');
const puppeteerService = require('../puppeteerService');
const awsConfig = require('../../middlewares/awsConfig');
const { Project, Company } = require('../../models');

describe('pdfDeliveryReportService', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock fs.readFileSync
    fs.readFileSync.mockReturnValue(
      '$projectName $companyName $generatedDate $generatedBy $reportType $header $data'
    );

    // Mock puppeteerService
    puppeteerService.generatePdfBuffer.mockResolvedValue(Buffer.from('mock-pdf'));

    // Mock awsConfig
    awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
      callback('success-url', null);
    });
  });

  describe('extractDeliveryHeaderSelection', () => {
    it('should extract headers and flags correctly for all active headers', () => {
      const req = {
        body: {
          selectedHeaders: [
            { key: 'id', title: 'ID', isActive: true },
            { key: 'description', title: 'Description', isActive: true },
            { key: 'date', title: 'Date', isActive: true },
            { key: 'status', title: 'Status', isActive: true },
            { key: 'approvedBy', title: 'Approved By', isActive: true },
            { key: 'equipment', title: 'Equipment', isActive: true },
            { key: 'dfow', title: 'DFOW', isActive: true },
            { key: 'gate', title: 'Gate', isActive: true },
            { key: 'company', title: 'Company', isActive: true },
            { key: 'name', title: 'Person', isActive: true },
            { key: 'location', title: 'Location', isActive: true }
          ]
        }
      };

      const result = pdfDeliveryReportService.extractDeliveryHeaderSelection(req);

      expect(result.selectedFlags).toEqual({
        isIdSelected: true,
        isDescriptionSelected: true,
        isDateSelected: true,
        isStatusSelected: true,
        isApprovedBySelected: true,
        isEquipmentSelected: true,
        isDfowSelected: true,
        isGateSelected: true,
        isCompanySelected: true,
        isPersonSelected: true,
        isLocationSelected: true
      });

      expect(result.header).toHaveLength(11);
      expect(result.header[0]).toBe('<th style="text-align:center">ID</th>');
      expect(result.header[9]).toBe('<th style="text-align:center">Person</th>');
    });

    it('should handle inactive headers correctly', () => {
      const req = {
        body: {
          selectedHeaders: [
            { key: 'id', title: 'ID', isActive: true },
            { key: 'description', title: 'Description', isActive: false },
            { key: 'date', title: 'Date', isActive: true }
          ]
        }
      };

      const result = pdfDeliveryReportService.extractDeliveryHeaderSelection(req);

      expect(result.selectedFlags.isIdSelected).toBe(true);
      expect(result.selectedFlags.isDescriptionSelected).toBe(false);
      expect(result.selectedFlags.isDateSelected).toBe(true);
      expect(result.header).toHaveLength(2);
    });

    it('should handle empty selectedHeaders array', () => {
      const req = {
        body: {
          selectedHeaders: []
        }
      };

      const result = pdfDeliveryReportService.extractDeliveryHeaderSelection(req);

      expect(Object.values(result.selectedFlags).every(flag => flag === false)).toBe(true);
      expect(result.header).toHaveLength(0);
    });

    it('should handle special key mappings correctly', () => {
      const req = {
        body: {
          selectedHeaders: [
            { key: 'name', title: 'Person Name', isActive: true }
          ]
        }
      };

      const result = pdfDeliveryReportService.extractDeliveryHeaderSelection(req);

      expect(result.selectedFlags.isPersonSelected).toBe(true);
      expect(result.header[0]).toBe('<th style="text-align:center">Person Name</th>');
    });
  });

  describe('buildDeliveryRows', () => {
    const mockData = [
      {
        DeliveryId: 'DEL001',
        description: 'Test delivery',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z',
        status: 'Completed',
        approverDetails: {
          User: { firstName: 'John', lastName: 'Doe' }
        },
        equipmentDetails: [
          { Equipment: { equipmentName: 'Crane A' } },
          { Equipment: { equipmentName: 'Crane B' } }
        ],
        defineWorkDetails: [
          { DeliverDefineWork: { DFOW: 'Work 1' } }
        ],
        gateDetails: [
          { Gate: { gateName: 'Gate 1' } }
        ],
        companyDetails: [
          { Company: { companyName: 'Company A' } }
        ],
        memberDetails: [
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ],
        location: { locationPath: 'Location A' }
      }
    ];

    it('should build rows correctly with selected flags', () => {
      const flags = {
        isIdSelected: true,
        isDescriptionSelected: true,
        isDateSelected: true,
        isStatusSelected: true,
        isApprovedBySelected: true,
        isEquipmentSelected: true,
        isDfowSelected: true,
        isGateSelected: true,
        isCompanySelected: true,
        isPersonSelected: true,
        isLocationSelected: true
      };

      const result = pdfDeliveryReportService.buildDeliveryRows(mockData, flags, 0);

      expect(result).toHaveLength(1);
      expect(result[0]).toContain('DEL001');
      expect(result[0]).toContain('Test delivery');
      expect(result[0]).toContain('John Doe');
      expect(result[0]).toContain('Crane A');
      expect(result[0]).toContain('Work 1');
      expect(result[0]).toContain('Gate 1');
      expect(result[0]).toContain('Company A');
      expect(result[0]).toContain('Jane Smith');
      expect(result[0]).toContain('Location A');
    });

    it('should handle empty data array', () => {
      const flags = { isIdSelected: true };
      const result = pdfDeliveryReportService.buildDeliveryRows([], flags, 0);
      expect(result).toHaveLength(0);
    });

    it('should handle timezone offset correctly', () => {
      const flags = { isDateSelected: true };
      const result = pdfDeliveryReportService.buildDeliveryRows(mockData, flags, 60);

      expect(moment).toHaveBeenCalledWith(mockData[0].deliveryStart);
      expect(moment().add).toHaveBeenCalledWith(60, 'minutes');
    });

    it('should handle missing optional fields', () => {
      const dataWithMissingFields = [{
        DeliveryId: 'DEL002',
        description: 'Test delivery 2',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z'
      }];

      const flags = {
        isIdSelected: true,
        isApprovedBySelected: true,
        isEquipmentSelected: true,
        isGateSelected: true,
        isLocationSelected: true
      };

      const result = pdfDeliveryReportService.buildDeliveryRows(dataWithMissingFields, flags, 0);

      expect(result[0]).toContain('DEL002');
      expect(result[0]).toContain('-'); // Should contain dash for missing fields
    });

    it('should handle different flag combinations', () => {
      const flags = {
        isIdSelected: true,
        isDescriptionSelected: false,
        isDateSelected: true,
        isStatusSelected: false
      };

      const result = pdfDeliveryReportService.buildDeliveryRows(mockData, flags, 0);

      expect(result[0]).toContain('DEL001');
      expect(result[0]).not.toContain('Test delivery');
      expect(result[0]).toContain('01/15/2024 10:00 AM');
      expect(result[0]).not.toContain('Completed');
    });
  });

  describe('generateDeliveryPdfTemplate', () => {
    const mockProjectData = { projectName: 'Test Project' };
    const mockCompanyData = { companyName: 'Test Company' };
    const mockLoginUser = { firstName: 'Admin', lastName: 'User' };
    const mockReq = { body: { generatedDate: '01/15/2024' } };
    const mockHeader = ['<th>Header 1</th>', '<th>Header 2</th>'];
    const mockContent = ['<tr>Row 1</tr>', '<tr>Row 2</tr>'];

    it('should replace all template placeholders correctly', () => {
      const templatePath = '/mock/template/path';

      const result = pdfDeliveryReportService.generateDeliveryPdfTemplate(
        templatePath, mockProjectData, mockCompanyData, mockLoginUser, mockReq, mockHeader, mockContent
      );

      expect(fs.readFileSync).toHaveBeenCalledWith(templatePath, 'utf-8');
      expect(result).toBe('Test Project Test Company 01/15/2024 Admin User Delivery <th>Header 1</th><th>Header 2</th> <tr>Row 1</tr><tr>Row 2</tr>');
    });

    it('should handle empty header and content arrays', () => {
      const templatePath = '/mock/template/path';

      const result = pdfDeliveryReportService.generateDeliveryPdfTemplate(
        templatePath, mockProjectData, mockCompanyData, mockLoginUser, mockReq, [], []
      );

      expect(result).toBe('Test Project Test Company 01/15/2024 Admin User Delivery  ');
    });
  });

  describe('pdfFormatOfDeliveryRequest', () => {
    const mockParams = { ProjectId: '1' };
    const mockLoginUser = { firstName: 'Admin', lastName: 'User' };
    const mockData = [
      {
        DeliveryId: 'DEL001',
        description: 'Test delivery',
        deliveryStart: '2024-01-15T08:00:00Z',
        deliveryEnd: '2024-01-15T10:00:00Z',
        status: 'Completed'
      }
    ];
    const mockReq = {
      headers: { timezoneoffset: '0' },
      body: {
        selectedHeaders: [
          { key: 'id', title: 'ID', isActive: true }
        ],
        ParentCompanyId: '1',
        reportName: 'test-report',
        exportType: 'pdf',
        generatedDate: '01/15/2024'
      }
    };

    beforeEach(() => {
      Project.findOne.mockResolvedValue({ projectName: 'Test Project' });
      Company.findOne.mockResolvedValue({ companyName: 'Test Company' });
    });

    it('should generate PDF successfully', async () => {
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(Project.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, id: 1 },
        attributes: ['projectName']
      });
      expect(Company.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, ParentCompanyId: 1, isParent: true },
        attributes: ['companyName']
      });
      expect(puppeteerService.generatePdfBuffer).toHaveBeenCalled();
      expect(awsConfig.reportUpload).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith('success-url', false);
    });

    it('should handle project not found', async () => {
      Project.findOne.mockResolvedValue(null);
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: expect.any(String) });
    });

    it('should handle company not found', async () => {
      Company.findOne.mockResolvedValue(null);
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: expect.any(String) });
    });

    it('should handle PDF generation failure', async () => {
      puppeteerService.generatePdfBuffer.mockResolvedValue(null);
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Cannot export the document' });
    });

    it('should handle AWS upload failure', async () => {
      awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
        callback(null, 'Upload error');
      });
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Upload failed' });
    });

    it('should handle database errors', async () => {
      Project.findOne.mockRejectedValue(new Error('Database error'));
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: 'Database error' });
    });

    it('should handle puppeteer service errors', async () => {
      puppeteerService.generatePdfBuffer.mockRejectedValue(new Error('Puppeteer error'));
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Unexpected error during PDF generation', error: 'Puppeteer error' });
    });

    it('should handle empty data array', async () => {
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, [], mockReq, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('success-url', false);
    });

    it('should handle different timezone offsets', async () => {
      const reqWithTimezone = {
        ...mockReq,
        headers: { timezoneoffset: '120' }
      };
      const mockDone = jest.fn();

      await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
        mockParams, mockLoginUser, mockData, reqWithTimezone, mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('success-url', false);
    });
  });
});