const status = require('http-status');
const TimeZoneController = require('../TimeZoneController');

// Mock the models
jest.mock('../../models', () => ({
  TimeZone: {
    getAll: jest.fn(),
  },
}));

const { TimeZone } = require('../../models');

describe('TimeZoneController', () => {
  let mockReq;
  let mockRes;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock request and response
    mockReq = {};

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
  });

  describe('getTimeZoneList', () => {
    it('should return timezone list successfully', async () => {
      // Arrange
      const mockTimeZoneList = [
        { id: 1, name: 'UTC', offset: '+00:00' },
        { id: 2, name: 'EST', offset: '-05:00' },
        { id: 3, name: 'PST', offset: '-08:00' },
        { id: 4, name: 'IST', offset: '+05:30' },
      ];

      TimeZone.getAll.mockResolvedValue(mockTimeZoneList);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockTimeZoneList });
    });

    it('should return empty array when no timezones exist', async () => {
      // Arrange
      TimeZone.getAll.mockResolvedValue([]);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ data: [] });
    });

    it('should handle null response from model', async () => {
      // Arrange
      TimeZone.getAll.mockResolvedValue(null);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ data: null });
    });

    it('should handle undefined response from model', async () => {
      // Arrange
      TimeZone.getAll.mockResolvedValue(undefined);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ data: undefined });
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      TimeZone.getAll.mockRejectedValue(error);

      // Act & Assert
      await expect(TimeZoneController.getTimeZoneList(mockReq, mockRes)).rejects.toThrow(
        'Database connection failed',
      );
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
    });

    it('should handle single timezone in response', async () => {
      // Arrange
      const mockSingleTimeZone = [{ id: 1, name: 'UTC', offset: '+00:00' }];
      TimeZone.getAll.mockResolvedValue(mockSingleTimeZone);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockSingleTimeZone });
    });

    it('should handle large timezone list', async () => {
      // Arrange
      const mockLargeTimeZoneList = Array.from({ length: 100 }, (_, index) => ({
        id: index + 1,
        name: `Timezone_${index + 1}`,
        offset: `+${String(index).padStart(2, '0')}:00`,
      }));

      TimeZone.getAll.mockResolvedValue(mockLargeTimeZoneList);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockLargeTimeZoneList });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty request object', async () => {
      // Arrange
      const emptyReq = {};
      const mockTimeZoneList = [{ id: 1, name: 'UTC' }];
      TimeZone.getAll.mockResolvedValue(mockTimeZoneList);

      // Act
      await TimeZoneController.getTimeZoneList(emptyReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockTimeZoneList });
    });

    it('should handle timezone objects with missing properties', async () => {
      // Arrange
      const mockTimeZoneListWithMissingProps = [
        { id: 1 },
        { name: 'EST' },
        { offset: '+05:30' },
        {},
      ];

      TimeZone.getAll.mockResolvedValue(mockTimeZoneListWithMissingProps);

      // Act
      await TimeZoneController.getTimeZoneList(mockReq, mockRes);

      // Assert
      expect(TimeZone.getAll).toHaveBeenCalledTimes(1);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockTimeZoneListWithMissingProps });
    });
  });
});
