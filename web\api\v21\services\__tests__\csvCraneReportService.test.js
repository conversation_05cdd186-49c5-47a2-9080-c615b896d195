const csvCraneReportService = require('../csvCraneReportService');

// Mock all dependencies
jest.mock('export-to-csv', () => ({
  ExportToCsv: jest.fn().mockImplementation(() => ({
    generateCsv: jest.fn(),
  })),
}));

jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((date) => {
    if (date) {
      return actualMoment(date);
    }
    return actualMoment('2023-01-01T10:00:00Z');
  });

  // Copy all moment methods
  Object.keys(actualMoment).forEach(key => {
    mockMoment[key] = actualMoment[key];
  });

  return mockMoment;
});

jest.mock('../../middlewares/awsConfig', () => ({
  reportUpload: jest.fn(),
}));

describe('CsvCraneReportService', () => {
  let mockDone;
  let mockExportToCsv;
  let mockAwsConfig;
  let mockMoment;

  beforeEach(() => {
    jest.clearAllMocks();

    mockDone = jest.fn();

    // Get mocked modules
    const { ExportToCsv } = require('export-to-csv');
    mockExportToCsv = ExportToCsv;
    mockAwsConfig = require('../../middlewares/awsConfig');
    mockMoment = require('moment');
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(csvCraneReportService).toBeDefined();
    });

    it('should have exportCraneReportInCsvFormat method', () => {
      expect(typeof csvCraneReportService.exportCraneReportInCsvFormat).toBe('function');
    });
  });

  describe('exportCraneReportInCsvFormat', () => {
    let mockData;
    let mockSelectedHeaders;
    let mockGenerateCsv;

    beforeEach(() => {
      mockGenerateCsv = jest.fn();
      mockExportToCsv.mockImplementation(() => ({
        generateCsv: mockGenerateCsv,
      }));

      mockData = [
        {
          CraneRequestId: 1,
          description: 'Test crane request',
          requestType: 'craneRequest',
          craneDeliveryStart: '2023-01-01T10:00:00Z',
          deliveryStart: '2023-01-01T11:00:00Z',
          status: 'approved',
          pickUpLocation: 'Location A',
          dropOffLocation: 'Location B',
          cranePickUpLocation: 'Crane Location A',
          craneDropOffLocation: 'Crane Location B',
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: 'Doe',
            },
          },
          equipmentDetails: [
            {
              Equipment: {
                equipmentName: 'Crane 1',
                PresetEquipmentType: {
                  isCraneType: true,
                },
              },
            },
          ],
          defineWorkDetails: [
            {
              DeliverDefineWork: {
                DFOW: 'Test DFOW',
              },
            },
          ],
          gateDetails: [
            {
              Gate: {
                gateName: 'Gate 1',
              },
            },
          ],
          companyDetails: [
            {
              Company: {
                companyName: 'Test Company',
              },
            },
          ],
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'Jane',
                  lastName: 'Smith',
                },
              },
            },
          ],
          location: {
            locationPath: 'Test Location Path',
          },
        },
      ];

      mockSelectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'DFOW', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Company', isActive: true },
        { key: 'name', title: 'Name', isActive: true },
        { key: 'pickingFrom', title: 'Picking From', isActive: true },
        { key: 'pickingTo', title: 'Picking To', isActive: true },
        { key: 'location', title: 'Location', isActive: true },
      ];
    });

    it('should handle empty data array', async () => {
      await csvCraneReportService.exportCraneReportInCsvFormat(
        [],
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'No data available to export' });
      expect(mockExportToCsv).not.toHaveBeenCalled();
    });

    it('should process data and generate CSV successfully', async () => {
      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockExportToCsv).toHaveBeenCalledWith({
        showLabels: true,
        showTitle: false,
        useTextFile: false,
        useBom: false,
        useKeysAsHeaders: true,
      });
      expect(mockGenerateCsv).toHaveBeenCalledWith(expect.any(Array), true);
      expect(mockAwsConfig.reportUpload).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle AWS upload error', async () => {
      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback(null, new Error('Upload failed'));
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'cannot export document' });
    });

    it('should handle different requestType (non-craneRequest)', async () => {
      const mockDataWithDeliveryRequest = [
        {
          ...mockData[0],
          requestType: 'deliveryRequest',
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 11:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithDeliveryRequest,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle timezone offset correctly', async () => {
      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 12:00 pm';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        '120', // 2 hours offset
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing approverDetails', async () => {
      const mockDataWithoutApprover = [
        {
          ...mockData[0],
          approverDetails: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutApprover,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing User in approverDetails', async () => {
      const mockDataWithoutUser = [
        {
          ...mockData[0],
          approverDetails: {},
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutUser,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle equipment details with non-crane type', async () => {
      const mockDataWithNonCrane = [
        {
          ...mockData[0],
          equipmentDetails: [
            {
              Equipment: {
                equipmentName: 'Truck 1',
                PresetEquipmentType: {
                  isCraneType: false,
                },
              },
            },
          ],
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithNonCrane,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing equipment details', async () => {
      const mockDataWithoutEquipment = [
        {
          ...mockData[0],
          equipmentDetails: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutEquipment,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing defineWorkDetails', async () => {
      const mockDataWithoutDefineWork = [
        {
          ...mockData[0],
          defineWorkDetails: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutDefineWork,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing gateDetails', async () => {
      const mockDataWithoutGate = [
        {
          ...mockData[0],
          gateDetails: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutGate,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty gateDetails array', async () => {
      const mockDataWithEmptyGate = [
        {
          ...mockData[0],
          gateDetails: [],
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithEmptyGate,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing companyDetails', async () => {
      const mockDataWithoutCompany = [
        {
          ...mockData[0],
          companyDetails: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutCompany,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing memberDetails', async () => {
      const mockDataWithoutMember = [
        {
          ...mockData[0],
          memberDetails: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutMember,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing User in memberDetails', async () => {
      const mockDataWithoutMemberUser = [
        {
          ...mockData[0],
          memberDetails: [
            {
              Member: {},
            },
          ],
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutMemberUser,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle missing location', async () => {
      const mockDataWithoutLocation = [
        {
          ...mockData[0],
          location: null,
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 10:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataWithoutLocation,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle pickingFrom and pickingTo for non-crane request', async () => {
      const mockDataNonCrane = [
        {
          ...mockData[0],
          requestType: 'deliveryRequest',
          pickUpLocation: null,
          dropOffLocation: null,
          cranePickUpLocation: 'Crane Pick Up',
          craneDropOffLocation: 'Crane Drop Off',
        },
      ];

      const mockCsvContent = 'ID,Description,Date\n1,Test,Jan-01-2023 11:00 am';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataNonCrane,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle inactive headers', async () => {
      const mockInactiveHeaders = [
        { key: 'id', title: 'ID', isActive: false },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date', isActive: false },
      ];

      const mockCsvContent = 'Description\nTest crane request';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockData,
        mockInactiveHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle no CSV file generated', async () => {
      mockGenerateCsv.mockResolvedValue(null);

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockAwsConfig.reportUpload).not.toHaveBeenCalled();
      expect(mockDone).not.toHaveBeenCalled();
    });

    it('should handle empty CSV file generated', async () => {
      mockGenerateCsv.mockResolvedValue('');

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockAwsConfig.reportUpload).not.toHaveBeenCalled();
      expect(mockDone).not.toHaveBeenCalled();
    });

    it('should handle multiple equipment details with mixed crane types', async () => {
      const mockDataMultipleEquipment = [
        {
          ...mockData[0],
          equipmentDetails: [
            {
              Equipment: {
                equipmentName: 'Crane 1',
                PresetEquipmentType: {
                  isCraneType: true,
                },
              },
            },
            {
              Equipment: {
                equipmentName: 'Truck 1',
                PresetEquipmentType: {
                  isCraneType: false,
                },
              },
            },
            {
              Equipment: {
                equipmentName: 'Crane 2',
                PresetEquipmentType: {
                  isCraneType: true,
                },
              },
            },
          ],
        },
      ];

      const mockCsvContent = 'ID,Description,Equipment\n1,Test,Crane 1, Crane 2';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataMultipleEquipment,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle multiple defineWorkDetails', async () => {
      const mockDataMultipleDefineWork = [
        {
          ...mockData[0],
          defineWorkDetails: [
            {
              DeliverDefineWork: {
                DFOW: 'DFOW 1',
              },
            },
            {
              DeliverDefineWork: {
                DFOW: 'DFOW 2',
              },
            },
          ],
        },
      ];

      const mockCsvContent = 'ID,Description,DFOW\n1,Test,DFOW 1, DFOW 2';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataMultipleDefineWork,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle multiple companyDetails', async () => {
      const mockDataMultipleCompany = [
        {
          ...mockData[0],
          companyDetails: [
            {
              Company: {
                companyName: 'Company 1',
              },
            },
            {
              Company: {
                companyName: 'Company 2',
              },
            },
          ],
        },
      ];

      const mockCsvContent = 'ID,Description,Company\n1,Test,Company 1, Company 2';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataMultipleCompany,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle multiple memberDetails', async () => {
      const mockDataMultipleMember = [
        {
          ...mockData[0],
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'John',
                  lastName: 'Doe',
                },
              },
            },
            {
              Member: {
                User: {
                  firstName: 'Jane',
                  lastName: 'Smith',
                },
              },
            },
          ],
        },
      ];

      const mockCsvContent = 'ID,Description,Name\n1,Test,John Doe, Jane Smith';
      mockGenerateCsv.mockResolvedValue(mockCsvContent);
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback('http://example.com/file.csv', false);
      });

      await csvCraneReportService.exportCraneReportInCsvFormat(
        mockDataMultipleMember,
        mockSelectedHeaders,
        '0',
        'test.csv',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });
});
