// Mock models first
jest.mock('../../models', () => ({
  Project: {
    findAll: jest.fn(),
  },
  ProjectSettings: {
    create: jest.fn(),
  },
}));

// Mock axios
jest.mock('axios', () => ({
  get: jest.fn(),
}));

// Mock services
jest.mock('../../services', () => ({
  projectSettingsService: {
    getProjectSettings: jest.fn(),
    updateProjectSettings: jest.fn(),
  },
}));

const ProjectSettingsController = require('../ProjectSettingsController');
const { projectSettingsService } = require('../../services');
const { Project, ProjectSettings } = require('../../models');
const axios = require('axios');

describe('ProjectSettingsController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      query: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('getProjectSettings', () => {
    it('should get project settings successfully', async () => {
      const mockResponse = {
        id: 1,
        projectId: 123,
        settings: { notifications: true, privacy: 'private' },
      };
      mockReq.query = { ProjectId: 123 };
      projectSettingsService.getProjectSettings.mockResolvedValue(mockResponse);

      await ProjectSettingsController.getProjectSettings(mockReq, mockRes, mockNext);

      expect(projectSettingsService.getProjectSettings).toHaveBeenCalledWith(123);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Settings listed Successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from project settings retrieval', async () => {
      const mockError = new Error('Service error');
      mockReq.query = { ProjectId: 123 };
      projectSettingsService.getProjectSettings.mockRejectedValue(mockError);

      await ProjectSettingsController.getProjectSettings(mockReq, mockRes, mockNext);

      expect(projectSettingsService.getProjectSettings).toHaveBeenCalledWith(123);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('updateProjectSettings', () => {
    it('should update project settings successfully', async () => {
      mockReq.body = {
        projectId: 123,
        settings: { notifications: false, privacy: 'public' },
      };
      projectSettingsService.updateProjectSettings.mockResolvedValue(true);

      await ProjectSettingsController.updateProjectSettings(mockReq, mockRes, mockNext);

      expect(projectSettingsService.updateProjectSettings).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Settings Updated Successfully!',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure in project settings update', async () => {
      mockReq.body = {
        projectId: 123,
        settings: { notifications: false, privacy: 'public' },
      };
      projectSettingsService.updateProjectSettings.mockResolvedValue(false);

      await ProjectSettingsController.updateProjectSettings(mockReq, mockRes, mockNext);

      expect(projectSettingsService.updateProjectSettings).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot update project settings',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from project settings update', async () => {
      const mockError = new Error('Service error');
      mockReq.body = {
        projectId: 123,
        settings: { notifications: false, privacy: 'public' },
      };
      projectSettingsService.updateProjectSettings.mockRejectedValue(mockError);

      await ProjectSettingsController.updateProjectSettings(mockReq, mockRes, mockNext);

      expect(projectSettingsService.updateProjectSettings).toHaveBeenCalledWith(mockReq.body);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('setDefaultDeliveryWindow', () => {
    it('should set default delivery window successfully', async () => {
      const mockProjects = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' },
      ];
      Project.findAll.mockResolvedValue(mockProjects);
      ProjectSettings.create.mockResolvedValue({ id: 1 });

      await ProjectSettingsController.setDefaultDeliveryWindow(mockReq, mockRes, mockNext);

      expect(Project.findAll).toHaveBeenCalled();
      expect(ProjectSettings.create).toHaveBeenCalledTimes(2);
      expect(ProjectSettings.create).toHaveBeenCalledWith({
        deliveryWindowTime: 0,
        deliveryWindowTimeUnit: 'minutes',
        ProjectId: 1,
      });
      expect(ProjectSettings.create).toHaveBeenCalledWith({
        deliveryWindowTime: 0,
        deliveryWindowTimeUnit: 'minutes',
        ProjectId: 2,
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'success',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error in set default delivery window', async () => {
      const mockError = new Error('Database error');
      Project.findAll.mockRejectedValue(mockError);

      await ProjectSettingsController.setDefaultDeliveryWindow(mockReq, mockRes, mockNext);

      expect(Project.findAll).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('proxyDownloadFile', () => {
    it('should proxy download file successfully', async () => {
      const mockFileUrl = 'https://s3.amazonaws.com/bucket/file.pdf';
      const mockStream = {
        pipe: jest.fn(),
      };
      const mockResponse = {
        data: mockStream,
      };

      mockReq.query = { fileUrl: mockFileUrl };
      axios.get.mockResolvedValue(mockResponse);

      await ProjectSettingsController.proxyDownloadFile(mockReq, mockRes, mockNext);

      expect(axios.get).toHaveBeenCalledWith(mockFileUrl, { responseType: 'stream' });
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=file.pdf');
      expect(mockStream.pipe).toHaveBeenCalledWith(mockRes);
    });

    it('should handle missing file URL', async () => {
      mockReq.query = {};
      mockRes.status.mockReturnValue({
        send: jest.fn(),
      });

      await ProjectSettingsController.proxyDownloadFile(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.status().send).toHaveBeenCalledWith('No file URL provided');
    });

    it('should handle error in proxy download file', async () => {
      const mockFileUrl = 'https://s3.amazonaws.com/bucket/file.pdf';
      const mockError = new Error('Network error');

      mockReq.query = { fileUrl: mockFileUrl };
      axios.get.mockRejectedValue(mockError);
      mockRes.status.mockReturnValue({
        send: jest.fn(),
      });

      // Mock console.error to avoid output during tests
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await ProjectSettingsController.proxyDownloadFile(mockReq, mockRes, mockNext);

      expect(axios.get).toHaveBeenCalledWith(mockFileUrl, { responseType: 'stream' });
      expect(consoleSpy).toHaveBeenCalledWith('Error fetching file:', mockError);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.status().send).toHaveBeenCalledWith('Error downloading file.');

      consoleSpy.mockRestore();
    });
  });
});


