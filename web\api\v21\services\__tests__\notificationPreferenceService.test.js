const notificationPreferenceService = require('../notificationPreferenceService');
const { Sequelize, Enterprise } = require('../../models');
const { NotificationPreferenceItem, NotificationPreference, Member, Gates, Project, User } = require('../../models');
const helper = require('../../helpers/domainHelper');

// Mock all required dependencies
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            ne: 'ne'
        }
    },
    Enterprise: {
        findOne: jest.fn()
    },
    NotificationPreferenceItem: {
        findAll: jest.fn()
    },
    NotificationPreference: {
        findAll: jest.fn(),
        update: jest.fn()
    },
    Member: {
        findOne: jest.fn(),
        update: jest.fn()
    },
    Gates: {},
    Project: {},
    User: {
        findOne: jest.fn()
    }
}));

jest.mock('../../helpers/domainHelper', () => ({
    getDynamicModel: jest.fn(),
    returnProjectModel: jest.fn()
}));

describe('NotificationPreferenceService', () => {
    let mockReq;
    let mockDone;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common mock request object
        mockReq = {
            query: {
                MemberId: '1',
                ProjectId: '1',
                ParentCompanyId: '1'
            },
            body: {
                inAppNotification: [
                    {
                        NotificationPreferenceItemId: 1,
                        instant: true,
                        dailyDigest: false
                    }
                ],
                emailNotification: [
                    {
                        NotificationPreferenceItemId: 2,
                        instant: true,
                        dailyDigest: true
                    }
                ],
                dailyDigestTiming: {
                    time: '09:00',
                    timeFormat: 'AM',
                    TimeZoneId: 1
                },
                ParentCompanyId: '1'
            },
            params: {
                ParentCompanyId: '1'
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                domainName: 'test.com'
            }
        };

        mockDone = jest.fn();

        // Setup default mocks for helper functions
        helper.getDynamicModel.mockResolvedValue({
            Gates: {},
            Project: {},
            User: { findOne: jest.fn() },
            Member: {}
        });

        helper.returnProjectModel.mockResolvedValue({
            User: { findOne: jest.fn() },
            Member: { findOne: jest.fn() }
        });
    });

    describe('setNotificationPreference', () => {
        it('should successfully update notification preferences', async () => {
            // Mock successful updates
            NotificationPreference.update.mockResolvedValue([1]);
            Member.update.mockResolvedValue([1]);

            await notificationPreferenceService.setNotificationPreference(mockReq, mockDone);

            expect(NotificationPreference.update).toHaveBeenCalledTimes(2);
            expect(Member.update).toHaveBeenCalledTimes(1);
            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should handle errors during update', async () => {
            const error = new Error('Update failed');
            helper.getDynamicModel.mockRejectedValue(error);

            await notificationPreferenceService.setNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle empty inAppNotification array', async () => {
            const reqWithEmptyInApp = {
                ...mockReq,
                body: {
                    ...mockReq.body,
                    inAppNotification: [],
                    emailNotification: [
                        {
                            NotificationPreferenceItemId: 2,
                            instant: true,
                            dailyDigest: true
                        }
                    ]
                }
            };

            NotificationPreference.update.mockResolvedValue([1]);
            Member.update.mockResolvedValue([1]);

            await notificationPreferenceService.setNotificationPreference(reqWithEmptyInApp, mockDone);

            expect(NotificationPreference.update).toHaveBeenCalledTimes(1);
            expect(Member.update).toHaveBeenCalledTimes(1);
            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should handle empty emailNotification array', async () => {
            const reqWithEmptyEmail = {
                ...mockReq,
                body: {
                    ...mockReq.body,
                    inAppNotification: [
                        {
                            NotificationPreferenceItemId: 1,
                            instant: true,
                            dailyDigest: false
                        }
                    ],
                    emailNotification: []
                }
            };

            NotificationPreference.update.mockResolvedValue([1]);
            Member.update.mockResolvedValue([1]);

            await notificationPreferenceService.setNotificationPreference(reqWithEmptyEmail, mockDone);

            expect(NotificationPreference.update).toHaveBeenCalledTimes(1);
            expect(Member.update).toHaveBeenCalledTimes(1);
            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should handle Member.update failure', async () => {
            const error = new Error('Member update failed');
            NotificationPreference.update.mockResolvedValue([1]);
            Member.update.mockRejectedValue(error);

            await notificationPreferenceService.setNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });

    describe('getDomainNameFromEnterprise', () => {
        beforeEach(() => {
            // Setup publicUser and publicMember mocks
            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
        });

        it('should return domain name when provided and enterprise exists', async () => {
            Enterprise.findOne.mockResolvedValue({ name: 'test.com' });

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                'test.com',
                '1',
                { user: { email: '<EMAIL>' } }
            );
            expect(result).toBe('test.com');
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'test.com' }
            });
        });

        it('should return empty string when domain name is provided but enterprise does not exist', async () => {
            Enterprise.findOne.mockResolvedValue(null);

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                'nonexistent.com',
                '1',
                { user: { email: '<EMAIL>' } }
            );
            expect(result).toBe('');
        });

        it('should return empty string when domain name is not provided and ParentCompanyId is undefined', async () => {
            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                'undefined',
                { user: { email: '<EMAIL>' } }
            );
            expect(result).toBe('');
        });

        it('should return empty string when domain name is not provided and ParentCompanyId is null', async () => {
            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                null,
                { user: { email: '<EMAIL>' } }
            );
            expect(result).toBe('');
        });

        it('should return empty string when email is not provided', async () => {
            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: {} }
            );
            expect(result).toBe('');
        });

        it('should return empty string when user is not found', async () => {
            // First call returnProjectModel to set up publicUser
            await notificationPreferenceService.returnProjectModel();

            const mockPublicUser = { findOne: jest.fn().mockResolvedValue(null) };
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: { findOne: jest.fn() }
            });

            // Call returnProjectModel again to update the mocks
            await notificationPreferenceService.returnProjectModel();

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: { email: '<EMAIL>' } }
            );
            expect(result).toBe('');
        });

        it('should return domain name from enterprise when user is not a member', async () => {
            // Setup mocks
            await notificationPreferenceService.returnProjectModel();

            const mockPublicUser = { findOne: jest.fn().mockResolvedValue({ id: 1 }) };
            const mockPublicMember = { findOne: jest.fn().mockResolvedValue(null) };
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await notificationPreferenceService.returnProjectModel();

            Enterprise.findOne.mockResolvedValue({ name: 'enterprise.com' });

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: { email: '<EMAIL>' } }
            );

            expect(result).toBe('enterprise.com');
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: '1', status: 'completed' }
            });
        });

        it('should return domain name from member enterprise when member isAccount is true', async () => {
            // Setup mocks
            await notificationPreferenceService.returnProjectModel();

            const mockPublicUser = { findOne: jest.fn().mockResolvedValue({ id: 1 }) };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    isAccount: true,
                    EnterpriseId: 2
                })
            };
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await notificationPreferenceService.returnProjectModel();

            Enterprise.findOne.mockResolvedValue({ name: 'member-enterprise.com' });

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: { email: '<EMAIL>' } }
            );

            expect(result).toBe('member-enterprise.com');
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: 2, status: 'completed' }
            });
        });

        it('should return domain name from parent company when member isAccount is false', async () => {
            // Setup mocks
            await notificationPreferenceService.returnProjectModel();

            const mockPublicUser = { findOne: jest.fn().mockResolvedValue({ id: 1 }) };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    isAccount: false,
                    EnterpriseId: 2
                })
            };
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await notificationPreferenceService.returnProjectModel();

            Enterprise.findOne.mockResolvedValue({ name: 'parent-company.com' });

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: { email: '<EMAIL>' } }
            );

            expect(result).toBe('parent-company.com');
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: '1', status: 'completed' }
            });
        });

        it('should return empty string when enterprise is not found', async () => {
            // Setup mocks
            await notificationPreferenceService.returnProjectModel();

            const mockPublicUser = { findOne: jest.fn().mockResolvedValue({ id: 1 }) };
            const mockPublicMember = { findOne: jest.fn().mockResolvedValue(null) };
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await notificationPreferenceService.returnProjectModel();

            Enterprise.findOne.mockResolvedValue(null);

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: { email: '<EMAIL>' } }
            );

            expect(result).toBe('');
        });
    });

    describe('listNotificationPreference', () => {
        it('should successfully list notification preferences', async () => {
            const mockNotificationItems = [
                {
                    id: 1,
                    MemberId: 1,
                    ProjectId: 1,
                    ParentCompanyId: 1,
                    NotificationPreferenceItemId: 1,
                    instant: true,
                    dailyDigest: false,
                    NotificationPreferenceItem: {
                        id: 1,
                        description: 'Test Notification',
                        inappNotification: true,
                        emailNotification: false,
                        itemId: 1
                    }
                },
                {
                    id: 2,
                    MemberId: 1,
                    ProjectId: 1,
                    ParentCompanyId: 1,
                    NotificationPreferenceItemId: 2,
                    instant: false,
                    dailyDigest: true,
                    NotificationPreferenceItem: {
                        id: 2,
                        description: 'Email Notification',
                        inappNotification: false,
                        emailNotification: true,
                        itemId: 2
                    }
                }
            ];

            NotificationPreference.findAll.mockResolvedValue(mockNotificationItems);
            Member.findOne.mockResolvedValue({
                id: 1,
                time: '09:00',
                timeFormat: 'AM',
                TimeZoneId: 1
            });

            await notificationPreferenceService.listNotificationPreference(mockReq, mockDone);

            expect(NotificationPreference.findAll).toHaveBeenCalledWith({
                where: {
                    MemberId: 1,
                    ProjectId: 1,
                    ParentCompanyId: 1,
                    isDeleted: false
                },
                attributes: [
                    'id',
                    'MemberId',
                    'ProjectId',
                    'ParentCompanyId',
                    'NotificationPreferenceItemId',
                    'instant',
                    'dailyDigest'
                ],
                include: [
                    {
                        association: 'NotificationPreferenceItem',
                        where: { isDeleted: false },
                        attributes: ['id', 'description', 'inappNotification', 'emailNotification', 'itemId']
                    }
                ]
            });
            expect(Member.findOne).toHaveBeenCalledWith({
                where: {
                    UserId: 1,
                    isDeleted: false,
                    ProjectId: 1,
                    ParentCompanyId: 1
                },
                attributes: ['id', 'time', 'timeFormat', 'TimeZoneId']
            });
            expect(mockDone).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        inAppNotification: expect.any(Array),
                        emailNotification: expect.any(Array),
                        digestTiming: expect.any(Object)
                    })
                ]),
                false
            );
        });

        it('should handle errors during listing', async () => {
            const error = new Error('List failed');
            NotificationPreference.findAll.mockRejectedValue(error);

            await notificationPreferenceService.listNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle errors during getDynamicModel', async () => {
            const error = new Error('Dynamic model failed');
            helper.getDynamicModel.mockRejectedValue(error);

            await notificationPreferenceService.listNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle Member.findOne error', async () => {
            const mockNotificationItems = [
                {
                    id: 1,
                    MemberId: 1,
                    ProjectId: 1,
                    ParentCompanyId: 1,
                    NotificationPreferenceItemId: 1,
                    instant: true,
                    dailyDigest: false,
                    NotificationPreferenceItem: {
                        id: 1,
                        description: 'Test Notification',
                        inappNotification: true,
                        emailNotification: false,
                        itemId: 1
                    }
                }
            ];

            const error = new Error('Member findOne failed');
            NotificationPreference.findAll.mockResolvedValue(mockNotificationItems);
            Member.findOne.mockRejectedValue(error);

            await notificationPreferenceService.listNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should properly sort notifications by itemId', async () => {
            const mockNotificationItems = [
                {
                    id: 1,
                    MemberId: 1,
                    ProjectId: 1,
                    ParentCompanyId: 1,
                    NotificationPreferenceItemId: 1,
                    instant: true,
                    dailyDigest: false,
                    NotificationPreferenceItem: {
                        id: 1,
                        description: 'Test Notification 3',
                        inappNotification: true,
                        emailNotification: true,
                        itemId: 3
                    }
                },
                {
                    id: 2,
                    MemberId: 1,
                    ProjectId: 1,
                    ParentCompanyId: 1,
                    NotificationPreferenceItemId: 2,
                    instant: false,
                    dailyDigest: true,
                    NotificationPreferenceItem: {
                        id: 2,
                        description: 'Test Notification 1',
                        inappNotification: true,
                        emailNotification: true,
                        itemId: 1
                    }
                }
            ];

            NotificationPreference.findAll.mockResolvedValue(mockNotificationItems);
            Member.findOne.mockResolvedValue({
                id: 1,
                time: '09:00',
                timeFormat: 'AM',
                TimeZoneId: 1
            });

            await notificationPreferenceService.listNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        inAppNotification: expect.arrayContaining([
                            expect.objectContaining({
                                NotificationPreferenceItem: expect.objectContaining({
                                    itemId: expect.any(Number)
                                })
                            })
                        ]),
                        emailNotification: expect.arrayContaining([
                            expect.objectContaining({
                                NotificationPreferenceItem: expect.objectContaining({
                                    itemId: expect.any(Number)
                                })
                            })
                        ])
                    })
                ]),
                false
            );
        });
    });

    describe('notificationPreferenceItems', () => {
        it('should successfully get notification preference items', async () => {
            const mockItems = [
                {
                    id: 1,
                    description: 'Test Item 1',
                    inappNotification: true,
                    emailNotification: false,
                    itemId: 1
                },
                {
                    id: 2,
                    description: 'Test Item 2',
                    inappNotification: false,
                    emailNotification: true,
                    itemId: 2
                }
            ];

            NotificationPreferenceItem.findAll.mockResolvedValue(mockItems);

            await notificationPreferenceService.notificationPreferenceItems(mockReq, mockDone);

            expect(NotificationPreferenceItem.findAll).toHaveBeenCalledWith({
                attributes: ['id', 'description', 'inappNotification', 'emailNotification', 'itemId'],
                group: ['NotificationPreferenceItem.id', 'NotificationPreferenceItem.description']
            });
            expect(mockDone).toHaveBeenCalledWith(mockItems, false);
        });

        it('should handle errors during fetching items', async () => {
            const error = new Error('Fetch failed');
            NotificationPreferenceItem.findAll.mockRejectedValue(error);

            await notificationPreferenceService.notificationPreferenceItems(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle errors during getDynamicModel', async () => {
            const error = new Error('Dynamic model failed');
            helper.getDynamicModel.mockRejectedValue(error);

            await notificationPreferenceService.notificationPreferenceItems(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle empty result from findAll', async () => {
            NotificationPreferenceItem.findAll.mockResolvedValue([]);

            await notificationPreferenceService.notificationPreferenceItems(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith([], false);
        });
    });

    describe('getDynamicModel', () => {
        it('should successfully get dynamic model with ParentCompanyId from body', async () => {
            const mockModelObj = {
                Gates: {},
                Project: {},
                User: { findOne: jest.fn() },
                Member: {}
            };

            helper.getDynamicModel.mockResolvedValue(mockModelObj);
            Enterprise.findOne.mockResolvedValue({ name: 'test.com' });

            const result = await notificationPreferenceService.getDynamicModel(mockReq);

            expect(helper.returnProjectModel).toHaveBeenCalled();
            expect(helper.getDynamicModel).toHaveBeenCalled();
            expect(result).toBe(mockReq.query.ProjectId);
        });

        it('should successfully get dynamic model with ParentCompanyId from params', async () => {
            const mockModelObj = {
                Gates: {},
                Project: {},
                User: { findOne: jest.fn() },
                Member: {}
            };

            const reqWithParams = {
                ...mockReq,
                body: {},
                params: { ParentCompanyId: '2' }
            };

            helper.getDynamicModel.mockResolvedValue(mockModelObj);
            Enterprise.findOne.mockResolvedValue({ name: 'test.com' });

            const result = await notificationPreferenceService.getDynamicModel(reqWithParams);

            expect(result).toBe(reqWithParams.query.ProjectId);
        });

        it('should handle domain name and update user data', async () => {
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockModelObj = {
                Gates: {},
                Project: {},
                User: { findOne: jest.fn().mockResolvedValue(mockUser) },
                Member: {}
            };

            helper.getDynamicModel.mockResolvedValue(mockModelObj);
            Enterprise.findOne.mockResolvedValue({ name: 'test.com' });

            const reqWithDomain = {
                ...mockReq,
                user: { ...mockReq.user, domainName: 'test.com' }
            };

            await notificationPreferenceService.getDynamicModel(reqWithDomain);

            expect(mockModelObj.User.findOne).toHaveBeenCalledWith({
                where: { email: reqWithDomain.user.email }
            });
            expect(reqWithDomain.user).toBe(mockUser);
        });

        it('should handle empty domain name', async () => {
            const mockModelObj = {
                Gates: {},
                Project: {},
                User: { findOne: jest.fn() },
                Member: {}
            };

            helper.getDynamicModel.mockResolvedValue(mockModelObj);
            Enterprise.findOne.mockResolvedValue(null);

            const reqWithoutDomain = {
                ...mockReq,
                user: { ...mockReq.user, domainName: null }
            };

            const result = await notificationPreferenceService.getDynamicModel(reqWithoutDomain);

            expect(mockModelObj.User.findOne).not.toHaveBeenCalled();
            expect(result).toBe(reqWithoutDomain.query.ProjectId);
        });

        it('should handle getDomainNameFromEnterprise returning empty string', async () => {
            const mockModelObj = {
                Gates: {},
                Project: {},
                User: { findOne: jest.fn() },
                Member: {}
            };

            helper.getDynamicModel.mockResolvedValue(mockModelObj);
            Enterprise.findOne.mockResolvedValue(null);

            const reqWithInvalidDomain = {
                ...mockReq,
                user: { ...mockReq.user, domainName: 'invalid.com' }
            };

            const result = await notificationPreferenceService.getDynamicModel(reqWithInvalidDomain);

            expect(mockModelObj.User.findOne).not.toHaveBeenCalled();
            expect(result).toBe(reqWithInvalidDomain.query.ProjectId);
        });
    });

    describe('returnProjectModel', () => {
        it('should successfully return project model', async () => {
            const mockModelData = {
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            };

            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await notificationPreferenceService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle errors during returnProjectModel', async () => {
            const error = new Error('Return project model failed');
            helper.returnProjectModel.mockRejectedValue(error);

            await expect(notificationPreferenceService.returnProjectModel()).rejects.toThrow(error);
        });
    });

    describe('Edge Cases and Error Scenarios', () => {
        it('should handle setNotificationPreference with missing body properties', async () => {
            const reqWithMissingProps = {
                ...mockReq,
                body: {
                    inAppNotification: [],
                    emailNotification: [],
                    dailyDigestTiming: {
                        time: '10:00',
                        timeFormat: 'PM',
                        TimeZoneId: 2
                    }
                }
            };

            NotificationPreference.update.mockResolvedValue([1]);
            Member.update.mockResolvedValue([1]);

            await notificationPreferenceService.setNotificationPreference(reqWithMissingProps, mockDone);

            expect(NotificationPreference.update).toHaveBeenCalledTimes(0);
            expect(Member.update).toHaveBeenCalledTimes(1);
            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should handle setNotificationPreference with multiple notification items', async () => {
            const reqWithMultipleItems = {
                ...mockReq,
                body: {
                    inAppNotification: [
                        {
                            NotificationPreferenceItemId: 1,
                            instant: true,
                            dailyDigest: false
                        },
                        {
                            NotificationPreferenceItemId: 3,
                            instant: false,
                            dailyDigest: true
                        }
                    ],
                    emailNotification: [
                        {
                            NotificationPreferenceItemId: 2,
                            instant: true,
                            dailyDigest: true
                        },
                        {
                            NotificationPreferenceItemId: 4,
                            instant: false,
                            dailyDigest: false
                        }
                    ],
                    dailyDigestTiming: {
                        time: '08:30',
                        timeFormat: 'AM',
                        TimeZoneId: 3
                    }
                }
            };

            NotificationPreference.update.mockResolvedValue([1]);
            Member.update.mockResolvedValue([1]);

            await notificationPreferenceService.setNotificationPreference(reqWithMultipleItems, mockDone);

            expect(NotificationPreference.update).toHaveBeenCalledTimes(4);
            expect(Member.update).toHaveBeenCalledTimes(1);
            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should handle listNotificationPreference with no notification items', async () => {
            NotificationPreference.findAll.mockResolvedValue([]);
            Member.findOne.mockResolvedValue({
                id: 1,
                time: '09:00',
                timeFormat: 'AM',
                TimeZoneId: 1
            });

            await notificationPreferenceService.listNotificationPreference(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        inAppNotification: [],
                        emailNotification: [],
                        digestTiming: expect.any(Object)
                    })
                ]),
                false
            );
        });

        it('should handle getDomainNameFromEnterprise with member having RoleId 4', async () => {
            // Setup mocks
            await notificationPreferenceService.returnProjectModel();

            const mockPublicUser = { findOne: jest.fn().mockResolvedValue({ id: 1 }) };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    RoleId: 4,  // This should be filtered out by Op.ne
                    isAccount: false
                })
            };
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await notificationPreferenceService.returnProjectModel();

            Enterprise.findOne.mockResolvedValue({ name: 'enterprise.com' });

            const result = await notificationPreferenceService.getDomainNameFromEnterprise(
                null,
                '1',
                { user: { email: '<EMAIL>' } }
            );

            expect(mockPublicMember.findOne).toHaveBeenCalledWith({
                where: {
                    UserId: 1,
                    RoleId: { ne: 4 },
                    isDeleted: false
                }
            });
            expect(result).toBe('enterprise.com');
        });
    });
});