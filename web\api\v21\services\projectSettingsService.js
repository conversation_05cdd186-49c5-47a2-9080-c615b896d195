const { ProjectSettings, Member, Project, TimeZone, Equipments, Gates, Locations } = require('../models');
const moment = require('moment');


const ProjectSettingsService = {
  async getProjectSettings(ProjectId) {
    const projectSettings = await ProjectSettings.findOne({
      where: {
        ProjectId,
      },
      attributes: [
        'id',
        'deliveryWindowTime',
        'deliveryWindowTimeUnit',
        'inspectionWindowTime',
        'inspectionWindowTimeUnit',
        'craneWindowTime',
        'craneWindowTimeUnit',
        'concreteWindowTime',
        'concreteWindowTimeUnit',
        'ProjectId',
        'deliveryAllowOverlappingBooking',
        'deliveryAllowOverlappingCalenderEvents',
        'craneAllowOverlappingBooking',
        'craneAllowOverlappingCalenderEvents',
        'concreteAllowOverlappingBooking',
        'concreteAllowOverlappingCalenderEvents',
        'inspectionAllowOverlappingBooking',
        'inspectionAllowOverlappingCalenderEvents',
        'isAutoApprovalEnabled',
        'statusColorCode',
        'deliveryCard',
        'craneCard',
        'concreteCard',
        'inspectionCard',
        'isPublicWebsiteEnabled',
        'shareProjectInformation',
        'projectLogisticPlanUrl',
        'allowGuestToAddDeliveryBooking',
        'allowGuestToAddCraneBooking',
        'allowGuestToAddConcreteBooking',
        'allowGuestToViewDeliveryCalendar',
        'allowGuestToViewCraneCalendar',
        'allowGuestToViewConcreteCalendar',
        'autoRefreshRateInMinutes',
        'publicWebsiteUrl',
        'shareprojectLogisticPlan',
        'pdfOriginalName',
        'isPdfUploaded',
        'isDefaultColor',
        'useTextColorAsLegend',
        'convertedImageLinks',
        'sitePlanStatus',
        'workingWindowStartHours',
        'workingWindowStartMinutes',
        'workingWindowEndHours',
        'workingWindowEndMinutes',
        'equipmentExceptions',
        'gateExceptions'
      ],
      raw: true,
    });
    const autoApproveMembers = await Member.findAll({
      where: {
        ProjectId,
        isAutoApproveEnabled: true,
      },
      attributes: ['id', 'firstName', 'isAutoApproveEnabled', 'memberId'],
      include: [
        {
          association: 'User',
          attributes: ['email', 'isAccount', 'firstName', 'lastName', 'versionFlag'],
        },
      ],
    });
    if (projectSettings.isPdfUploaded) {
      const fileName = projectSettings.pdfOriginalName;
      const array = fileName.split('.');
      const extension = array[array.length - 1];
      projectSettings.fileExtension = extension;
      if (projectSettings.convertedImageLinks) {

        projectSettings.pdfToImageLinks = JSON.parse(projectSettings.convertedImageLinks);
      } else {
        projectSettings.pdfToImageLinks = [];
      }
    } else {
      projectSettings.fileExtension = null;
      projectSettings.pdfToImageLinks = [];
    }
    return { projectSettings, autoApproveMembers };
  },
  async updateProjectSettings(payload) {
    try {
      const getProjectSettingsData = await ProjectSettings.findOne({
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      console.log(payload, "payload======")
      if (payload.isDefaultColor) {
        getProjectSettingsData.statusColorCode = getProjectSettingsData.defaultStatusColor;
        getProjectSettingsData.isDefaultColor = payload.isDefaultColor;
        getProjectSettingsData.useTextColorAsLegend = true;
        await getProjectSettingsData.save();
      }
      if (payload.setDefaultCard) {
        getProjectSettingsData.deliveryCard = getProjectSettingsData.defaultDeliveryCard;
        getProjectSettingsData.craneCard = getProjectSettingsData.defaultCraneCard;
        getProjectSettingsData.concreteCard = getProjectSettingsData.defaultConcreteCard;
        getProjectSettingsData.inspectionCard = getProjectSettingsData.defaultInspectionCard;
        await getProjectSettingsData.save();
      }
      if (payload.deliveryWindowTime) {
        const currentDatetime = moment
          .tz('UTC')
          .add(payload.deliveryWindowTime, payload.deliveryWindowTimeUnit)
          .format('YYYY-MM-DD HH:mm:ssZ')
        payload.deliveryWindowOpentime = currentDatetime
      }
      if (payload.craneWindowTime) {
        const currentDatetime = moment
          .tz('UTC')
          .add(payload.craneWindowTime, payload.craneWindowTimeUnit)
          .format('YYYY-MM-DD HH:mm:ssZ')
        payload.craneWindowOpentime = currentDatetime
      }
      if (payload.concreteWindowTime) {
        const currentDatetime = moment
          .tz('UTC')
          .add(payload.concreteWindowTime, payload.concreteWindowTimeUnit)
          .format('YYYY-MM-DD HH:mm:ssZ')
        payload.concreteWindowOpentime = currentDatetime
      }
      if (payload.inspectionWindowTime) {
        const currentDatetime = moment
          .tz('UTC')
          .add(payload.inspectionWindowTime, payload.inspectionWindowTimeUnit)
          .format('YYYY-MM-DD HH:mm:ssZ')
        payload.inspectionWindowOpentime = currentDatetime
      }
      const projectSettings = await ProjectSettings.update(payload, {
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      if (payload.enabledUser && payload.enabledUser.length > 0) {
        await Member.update(
          { isAutoApproveEnabled: true },
          {
            where: {
              id: payload.enabledUser,
            },
          },
        );
      }
      if (payload.disabledUser && payload.disabledUser.length > 0) {
        await Member.update(
          { isAutoApproveEnabled: false },
          {
            where: {
              id: payload.disabledUser,
            },
          },
        );
      }
      return projectSettings;
    } catch (err) {
      console.log(err, "err====")
      return err;
    }
  },

  async mapGatesAndEquipmentToLocation() {
    try {
      const projects = await Project.findAll({
        where: { isDeleted: false },
        attributes: ['id'],
      });
      console.log(projects, 'projects');
      await Promise.all(
        projects.map(async (project) => {
          const gates = await Gates.findAll({
            where: { ProjectId: project.id, isDeleted: false },
            attributes: ['id', 'gateName'],
          });

          const equipments = await Equipments.findAll({
            where: { ProjectId: project.id, isDeleted: false },
            include: [
              {
                required: false,
                where: { isDeleted: false, isActive: true },
                association: 'PresetEquipmentType',
                attributes: ['id', 'equipmentType', 'isCraneType'],
              },
            ],
            attributes: ['id', 'equipmentName'],
          });

          await Locations.update(
            {
              GateId: gates,
              EquipmentId: equipments,
            },
            {
              where: { ProjectId: project.id },
            },
          ).then(() => {
            console.log('done for Project', project.id);
          });
        }),
      );

    } catch (error) {
      console.log(error, "errorsss=====")
      return error;
    }
  }
};

module.exports = ProjectSettingsService;
