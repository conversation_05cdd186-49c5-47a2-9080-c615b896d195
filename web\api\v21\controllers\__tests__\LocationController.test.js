const LocationController = require('../LocationController');
const { locationService } = require('../../services');
const exportService = require('../../services/exportService');
const { Project } = require('../../models');

// Mock dependencies
jest.mock('../../services', () => ({
  locationService: {
    addLocation: jest.fn(),
    listLocation: jest.fn(),
    editLocation: jest.fn(),
    deleteLocation: jest.fn(),
    getLocation: jest.fn(),
    getLocations: jest.fn(),
    bulkUploadLocation: jest.fn(),
    listLocations: jest.fn(),
    setLocationNotificationPreference: jest.fn(),
    updateMemberLocationPreference: jest.fn(),
    createDefaultLocationPathForExistingProject: jest.fn(),
    createDefaultLocationIDForExistingBookings: jest.fn(),
    findAvailableTimeSlot: jest.fn(),
  },
}));

jest.mock('../../services/exportService', () => ({
  exportSampleLocationDocument: jest.fn(),
}));

jest.mock('../../models', () => ({
  Project: {
    findOne: jest.fn(),
  },
}));

describe('LocationController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
      end: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('addLocation', () => {
    it('should add location successfully', async () => {
      locationService.addLocation.mockResolvedValue(true);

      await LocationController.addLocation(mockReq, mockRes);

      expect(locationService.addLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Added successfully.',
      });
    });

    it('should handle location addition failure', async () => {
      locationService.addLocation.mockResolvedValue(false);

      await LocationController.addLocation(mockReq, mockRes);

      expect(locationService.addLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot add location',
      });
    });
  });

  describe('listLocation', () => {
    it('should list locations successfully', async () => {
      const mockLocations = [{ id: 1, name: 'Location 1' }];
      locationService.listLocation.mockResolvedValue(mockLocations);

      await LocationController.listLocation(mockReq, mockRes);

      expect(locationService.listLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Listed successfully.',
        data: mockLocations,
      });
    });

    it('should handle location listing failure', async () => {
      locationService.listLocation.mockResolvedValue(null);

      await LocationController.listLocation(mockReq, mockRes);

      expect(locationService.listLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot get locations',
        data: [],
      });
    });
  });

  describe('editLocation', () => {
    it('should edit location successfully', async () => {
      locationService.editLocation.mockResolvedValue(true);

      await LocationController.editLocation(mockReq, mockRes);

      expect(locationService.editLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Updated successfully.',
      });
    });

    it('should handle location edit failure', async () => {
      locationService.editLocation.mockResolvedValue(false);

      await LocationController.editLocation(mockReq, mockRes);

      expect(locationService.editLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot update location',
      });
    });
  });

  describe('deleteLocation', () => {
    it('should delete location successfully', async () => {
      locationService.deleteLocation.mockResolvedValue(true);

      await LocationController.deleteLocation(mockReq, mockRes);

      expect(locationService.deleteLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Deleted successfully.',
      });
    });

    it('should handle location deletion failure', async () => {
      locationService.deleteLocation.mockResolvedValue(false);

      await LocationController.deleteLocation(mockReq, mockRes);

      expect(locationService.deleteLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot delete location',
      });
    });
  });

  describe('getLocation', () => {
    it('should get location successfully', async () => {
      const mockLocation = { id: 1, name: 'Location 1' };
      locationService.getLocation.mockResolvedValue(mockLocation);

      await LocationController.getLocation(mockReq, mockRes);

      expect(locationService.getLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Viewed successfully.',
        data: mockLocation,
      });
    });

    it('should handle location retrieval failure', async () => {
      locationService.getLocation.mockResolvedValue(null);

      await LocationController.getLocation(mockReq, mockRes);

      expect(locationService.getLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot view location',
        data: null,
      });
    });
  });

  describe('sampleExcelDownload', () => {
    it('should download sample excel successfully', async () => {
      const mockWorkbook = {
        xlsx: {
          write: jest.fn().mockResolvedValue(),
        },
      };
      const mockProject = {
        dataValues: {
          projectName: 'Test Project',
          id: 123,
        },
      };

      mockReq.body.ProjectId = '123';

      exportService.exportSampleLocationDocument.mockResolvedValue(mockWorkbook);
      Project.findOne.mockResolvedValue(mockProject);

      await LocationController.sampleExcelDownload(mockReq, mockRes, mockNext);

      expect(exportService.exportSampleLocationDocument).toHaveBeenCalled();
      expect(Project.findOne).toHaveBeenCalledWith({
        where: { id: '123' },
        attribute: ['projectName'],
      });
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/excel');
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringContaining('Test Project_123_'),
      );
      expect(mockWorkbook.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle null workbook from export service', async () => {
      exportService.exportSampleLocationDocument.mockResolvedValue(null);

      await LocationController.sampleExcelDownload(mockReq, mockRes, mockNext);

      expect(exportService.exportSampleLocationDocument).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'cannot export document', status: 422 });
      expect(mockRes.setHeader).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in sample excel download', async () => {
      const mockError = new Error('Exception error');
      exportService.exportSampleLocationDocument.mockRejectedValue(mockError);

      await LocationController.sampleExcelDownload(mockReq, mockRes, mockNext);

      expect(exportService.exportSampleLocationDocument).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getLocations', () => {
    it('should get locations successfully', async () => {
      const mockLocations = [{ id: 1, name: 'Location 1' }];
      locationService.getLocations.mockResolvedValue(mockLocations);

      await LocationController.getLocations(mockReq, mockRes);

      expect(locationService.getLocations).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Listed successfully.',
        data: mockLocations,
      });
    });

    it('should handle locations retrieval failure', async () => {
      locationService.getLocations.mockResolvedValue(null);

      await LocationController.getLocations(mockReq, mockRes);

      expect(locationService.getLocations).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot get locations',
        data: [],
      });
    });
  });

  describe('importLocation', () => {
    it('should import locations successfully', async () => {
      const mockResponse = { success: true, data: 'imported' };
      locationService.bulkUploadLocation.mockResolvedValue(mockResponse);

      await LocationController.importLocation(mockReq, mockRes, mockNext);

      expect(locationService.bulkUploadLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Locations Added successfully.',
        isLocationsAdded: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle import failure', async () => {
      const mockResponse = { error: true, message: 'Import failed' };
      locationService.bulkUploadLocation.mockResolvedValue(mockResponse);

      await LocationController.importLocation(mockReq, mockRes, mockNext);

      expect(locationService.bulkUploadLocation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: mockResponse.message,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in import', async () => {
      const mockError = new Error('Exception error');
      locationService.bulkUploadLocation.mockRejectedValue(mockError);

      await LocationController.importLocation(mockReq, mockRes, mockNext);

      expect(locationService.bulkUploadLocation).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listLocations', () => {
    it('should list locations successfully', async () => {
      const mockLocations = [{ id: 1, name: 'Location 1' }];
      locationService.listLocations.mockResolvedValue(mockLocations);

      await LocationController.listLocations(mockReq, mockRes);

      expect(locationService.listLocations).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Listed successfully.',
        data: mockLocations,
      });
    });

    it('should handle locations listing failure', async () => {
      locationService.listLocations.mockResolvedValue(null);

      await LocationController.listLocations(mockReq, mockRes);

      expect(locationService.listLocations).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot get locations',
        data: [],
      });
    });
  });

  describe('setLocationNotificationPreference', () => {
    it('should set location notification preference successfully', async () => {
      const mockResponse = { success: true, data: 'preference set' };
      locationService.setLocationNotificationPreference.mockResolvedValue(mockResponse);

      await LocationController.setLocationNotificationPreference(mockReq, mockRes);

      expect(locationService.setLocationNotificationPreference).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Notification Preference set successfully.',
        data: mockResponse,
      });
    });

    it('should handle notification preference setting failure', async () => {
      const mockResponse = { error: true };
      locationService.setLocationNotificationPreference.mockResolvedValue(mockResponse);

      await LocationController.setLocationNotificationPreference(mockReq, mockRes);

      expect(locationService.setLocationNotificationPreference).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot set location notification preference',
        data: [],
      });
    });
  });

  describe('updateMemberLocationPreference', () => {
    it('should update member location preference successfully', async () => {
      const mockResponse = { success: true, data: 'preference updated' };
      locationService.updateMemberLocationPreference.mockResolvedValue(mockResponse);

      await LocationController.updateMemberLocationPreference(mockReq, mockRes);

      expect(locationService.updateMemberLocationPreference).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Notification Preference set successfully.',
        data: mockResponse,
      });
    });

    it('should handle member location preference update failure', async () => {
      const mockResponse = { error: true };
      locationService.updateMemberLocationPreference.mockResolvedValue(mockResponse);

      await LocationController.updateMemberLocationPreference(mockReq, mockRes);

      expect(locationService.updateMemberLocationPreference).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot set location notification preference',
        data: [],
      });
    });
  });

  describe('createDefaultLocationPathForExistingProject', () => {
    it('should create default location path successfully', async () => {
      const mockResponse = { success: true };
      locationService.createDefaultLocationPathForExistingProject.mockResolvedValue(mockResponse);

      await LocationController.createDefaultLocationPathForExistingProject(mockReq, mockRes);

      expect(locationService.createDefaultLocationPathForExistingProject).toHaveBeenCalledWith(
        mockReq,
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Default Location Path created successfully.',
      });
    });

    it('should handle default location path creation failure', async () => {
      const mockResponse = { error: true };
      locationService.createDefaultLocationPathForExistingProject.mockResolvedValue(mockResponse);

      await LocationController.createDefaultLocationPathForExistingProject(mockReq, mockRes);

      expect(locationService.createDefaultLocationPathForExistingProject).toHaveBeenCalledWith(
        mockReq,
      );
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot create default location path',
      });
    });
  });

  describe('createDefaultLocationIDForExistingBookings', () => {
    it('should create default location ID successfully', async () => {
      const mockResponse = { success: true };
      locationService.createDefaultLocationIDForExistingBookings.mockResolvedValue(mockResponse);

      await LocationController.createDefaultLocationIDForExistingBookings(mockReq, mockRes);

      expect(locationService.createDefaultLocationIDForExistingBookings).toHaveBeenCalledWith(
        mockReq,
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Default Location ID created successfully.',
      });
    });

    it('should handle default location ID creation failure', async () => {
      const mockResponse = { error: true };
      locationService.createDefaultLocationIDForExistingBookings.mockResolvedValue(mockResponse);

      await LocationController.createDefaultLocationIDForExistingBookings(mockReq, mockRes);

      expect(locationService.createDefaultLocationIDForExistingBookings).toHaveBeenCalledWith(
        mockReq,
      );
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot create default location path',
      });
    });
  });

  describe('findAvailableSlots', () => {
    it('should find available slots successfully', async () => {
      const mockResponse = { success: true, data: 'available slots' };
      mockReq.body = { someData: 'test' };
      mockReq.query.ProjectId = '123';

      locationService.findAvailableTimeSlot.mockResolvedValue(mockResponse);

      await LocationController.findAvailableSlots(mockReq, mockRes);

      expect(mockReq.body.ProjectId).toBe('123');
      expect(locationService.findAvailableTimeSlot).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Mapped Data listed Successfully',
        data: mockResponse,
      });
    });

    it('should handle available slots finding failure', async () => {
      const mockResponse = { error: true };
      mockReq.body = { someData: 'test' };
      mockReq.query.ProjectId = '123';

      locationService.findAvailableTimeSlot.mockResolvedValue(mockResponse);

      await LocationController.findAvailableSlots(mockReq, mockRes);

      expect(mockReq.body.ProjectId).toBe('123');
      expect(locationService.findAvailableTimeSlot).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot find available slots',
      });
    });
  });
});
