const billingService = require('../billingService');
const { BillingHistory, Enterprise, Sequelize } = require('../../models');
const stripeService = require('../stripeService');
const awsConfig = require('../../middlewares/awsConfig');
const helper = require('../../helpers/domainHelper');
const MAILER = require('../../mailer');
const ApiError = require('../../helpers/apiError');

// Mock all dependencies
jest.mock('../../models', () => ({
    BillingHistory: {
        create: jest.fn(),
        findAndCountAll: jest.fn(),
        findOne: jest.fn(),
    },
    Enterprise: {
        findOne: jest.fn(),
    },
    Sequelize: {
        or: jest.fn(),
        Op: {
            ne: 'ne',
        },
    },
    User: {
        findAll: jest.fn(),
        findOne: jest.fn(),
    },
}));

jest.mock('../stripeService', () => ({
    payOnline: jest.fn(),
}));

jest.mock('../../middlewares/awsConfig', () => ({
    singleUpload: jest.fn(),
}));

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn(),
}));

jest.mock('../../mailer', () => ({
    sendMail: jest.fn(),
}));

describe('BillingService', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
        // Reset any spies
        jest.restoreAllMocks();
    });

    describe('uploadProfile', () => {
        it('should successfully upload profile image', (done) => {
            const mockResult = [{ Location: 'https://example.com/image.jpg' }];
            awsConfig.singleUpload.mockImplementation((inputData, callback) => {
                callback(mockResult, null);
            });

            billingService.uploadProfile({}, (result, error) => {
                expect(result).toEqual({ imageUrl: 'https://example.com/image.jpg' });
                expect(error).toBeFalsy();
                done();
            });
        });

        it('should handle upload error', (done) => {
            const mockError = new Error('Upload failed');
            awsConfig.singleUpload.mockImplementation((inputData, callback) => {
                callback(null, mockError);
            });

            billingService.uploadProfile({}, (result, error) => {
                expect(result).toBeNull();
                expect(error).toBe(mockError);
                done();
            });
        });

        it('should handle empty result array', (done) => {
            awsConfig.singleUpload.mockImplementation((inputData, callback) => {
                callback([], null);
            });

            billingService.uploadProfile({}, (result, error) => {
                expect(result).toBeNull();
                expect(error).toBeTruthy();
                done();
            });
        });

        it('should handle null result', (done) => {
            awsConfig.singleUpload.mockImplementation((inputData, callback) => {
                callback(null, null);
            });

            billingService.uploadProfile({}, (result, error) => {
                expect(result).toBeNull();
                expect(error).toBeTruthy();
                done();
            });
        });
    });

    describe('payOnline', () => {
        const mockInputData = {
            query: { ParentCompanyId: '123' },
            body: { amount: 100 },
            user: { id: 1, firstName: 'John', email: '<EMAIL>' },
        };

        const mockEnterprise = { id: 1 };
        const mockStripeResponse = {
            charges: {
                data: [{ receipt_url: 'https://receipt.example.com' }],
            },
        };
        const mockAdminUsers = [{ email: '<EMAIL>' }];

        beforeEach(() => {
            Enterprise.findOne.mockResolvedValue(mockEnterprise);
            stripeService.payOnline.mockResolvedValue(mockStripeResponse);
            BillingHistory.create.mockResolvedValue({ id: 1 });
            require('../../models').User.findAll.mockResolvedValue(mockAdminUsers);
            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            helper.getDynamicModel.mockResolvedValue({ User: { findOne: jest.fn() } });
            // Mock the getDynamicModel method
            jest.spyOn(billingService, 'getDynamicModel').mockResolvedValue();
        });

        it('should process online payment successfully', async () => {
            const done = jest.fn();

            await billingService.payOnline(mockInputData, done);

            expect(stripeService.payOnline).toHaveBeenCalledWith(mockInputData);
            expect(BillingHistory.create).toHaveBeenCalledWith(expect.objectContaining({
                EnterpriseId: mockEnterprise.id,
                status: 'completed',
                paymentMethod: 'online',
                amount: mockInputData.body.amount,
            }));
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle missing ParentCompanyId', async () => {
            const done = jest.fn();
            const inputData = { ...mockInputData, query: {} };

            await billingService.payOnline(inputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle enterprise not found', async () => {
            const done = jest.fn();
            Enterprise.findOne.mockResolvedValue(null);

            await billingService.payOnline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle stripe service failure', async () => {
            const done = jest.fn();
            const stripeError = new Error('Stripe payment failed');
            stripeService.payOnline.mockRejectedValue(stripeError);

            await billingService.payOnline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, stripeError);
        });

        it('should handle billing history creation failure', async () => {
            const done = jest.fn();
            const dbError = new Error('Database error');
            BillingHistory.create.mockRejectedValue(dbError);

            await billingService.payOnline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, dbError);
        });

        it('should handle null stripe response', async () => {
            const done = jest.fn();
            stripeService.payOnline.mockResolvedValue(null);

            await billingService.payOnline(mockInputData, done);

            expect(BillingHistory.create).toHaveBeenCalledWith(expect.objectContaining({
                receiptUrl: null,
            }));
        });

        it('should handle stripe response without charges', async () => {
            const done = jest.fn();
            stripeService.payOnline.mockResolvedValue({ charges: { data: [] } });

            await billingService.payOnline(mockInputData, done);

            expect(BillingHistory.create).toHaveBeenCalledWith(expect.objectContaining({
                receiptUrl: null,
            }));
        });

        it('should handle multiple admin users', async () => {
            const done = jest.fn();
            const multipleAdmins = [
                { email: '<EMAIL>' },
                { email: '<EMAIL>' },
                { email: '<EMAIL>' }
            ];
            require('../../models').User.findAll.mockResolvedValue(multipleAdmins);

            await billingService.payOnline(mockInputData, done);

            expect(done).toHaveBeenCalledTimes(3); // Called once for each admin
        });

        it('should handle admin users query failure', async () => {
            const done = jest.fn();
            const dbError = new Error('User query failed');
            require('../../models').User.findAll.mockRejectedValue(dbError);

            await billingService.payOnline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, dbError);
        });

        it('should handle getDynamicModel failure', async () => {
            const done = jest.fn();
            const modelError = new Error('Model error');
            // Mock the getDynamicModel method to throw an error
            jest.spyOn(billingService, 'getDynamicModel').mockRejectedValue(modelError);

            await billingService.payOnline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, modelError);
        });
    });

    describe('getBillingInfo', () => {
        const mockInputData = {
            query: { ParentCompanyId: '123' },
            user: { email: '<EMAIL>' },
        };

        const mockEnterprise = { id: 1 };
        const mockBillingDetails = {
            rows: [],
            count: 0,
            nextPayDet: {
                lastPayment: new Date(),
            },
        };

        beforeEach(() => {
            Enterprise.findOne.mockResolvedValue(mockEnterprise);
            BillingHistory.findAndCountAll.mockResolvedValue(mockBillingDetails);
            BillingHistory.findOne.mockResolvedValue(mockBillingDetails.nextPayDet);
            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            helper.getDynamicModel.mockResolvedValue({ User: { findOne: jest.fn() } });
            jest.spyOn(billingService, 'getDynamicModel').mockResolvedValue();
        });

        it('should get billing info successfully', async () => {
            const done = jest.fn();

            await billingService.getBillingInfo(mockInputData, done);

            expect(BillingHistory.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
                where: { EnterpriseId: mockEnterprise.id },
            }));
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle missing ParentCompanyId', async () => {
            const done = jest.fn();
            const inputData = { ...mockInputData, query: {} };

            await billingService.getBillingInfo(inputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle enterprise not found', async () => {
            const done = jest.fn();
            Enterprise.findOne.mockResolvedValue(null);

            await billingService.getBillingInfo(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle billing history query failure', async () => {
            const done = jest.fn();
            const dbError = new Error('Database query failed');
            BillingHistory.findAndCountAll.mockRejectedValue(dbError);

            await billingService.getBillingInfo(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, dbError);
        });

        it('should handle missing nextPayDet', async () => {
            const done = jest.fn();
            BillingHistory.findOne.mockResolvedValue(null);

            await billingService.getBillingInfo(mockInputData, done);

            expect(() => {
                // This should cause an error when trying to access lastPayment on null
            }).not.toThrow(); // The actual service might handle this gracefully
        });

        it('should calculate next payment date correctly', async () => {
            const done = jest.fn();
            const lastPaymentDate = new Date('2023-01-01');
            const mockNextPayDet = { lastPayment: lastPaymentDate };
            BillingHistory.findOne.mockResolvedValue(mockNextPayDet);

            await billingService.getBillingInfo(mockInputData, done);

            const expectedNextPayment = new Date('2024-01-01'); // One year later
            expect(done).toHaveBeenCalledWith(
                expect.objectContaining({
                    nextPayDet: expect.objectContaining({
                        nextPayment: expectedNextPayment
                    })
                }),
                false
            );
        });

        it('should handle getDynamicModel failure', async () => {
            const done = jest.fn();
            const modelError = new Error('Model error');
            jest.spyOn(billingService, 'getDynamicModel').mockRejectedValue(modelError);

            await billingService.getBillingInfo(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, modelError);
        });

        it('should include correct associations in query', async () => {
            const done = jest.fn();

            await billingService.getBillingInfo(mockInputData, done);

            expect(BillingHistory.findAndCountAll).toHaveBeenCalledWith(
                expect.objectContaining({
                    include: [
                        {
                            association: 'User',
                            attributes: ['id', 'firstName', 'lastName'],
                        },
                        {
                            association: 'Enterprise',
                            attributes: ['id', 'name'],
                        },
                    ],
                })
            );
        });
    });

    describe('getDomainEnterprise', () => {
        it('should return domain name when enterprise exists', async () => {
            const mockDomain = 'test.com';
            Enterprise.findOne.mockResolvedValue({ name: mockDomain });

            const result = await billingService.getDomainEnterprise(mockDomain);

            expect(result).toBe(mockDomain);
        });

        it('should return empty string when enterprise not found', async () => {
            Enterprise.findOne.mockResolvedValue(null);

            const result = await billingService.getDomainEnterprise('nonexistent.com');

            expect(result).toBe('');
        });

        it('should return null for empty domain name', async () => {
            const result = await billingService.getDomainEnterprise(null);

            expect(result).toBeNull();
        });

        it('should return null for undefined domain name', async () => {
            const result = await billingService.getDomainEnterprise(undefined);

            expect(result).toBeNull();
        });

        it('should return null for empty string domain name', async () => {
            const result = await billingService.getDomainEnterprise('');

            expect(result).toBeNull();
        });

        it('should handle database error gracefully', async () => {
            const dbError = new Error('Database connection failed');
            Enterprise.findOne.mockRejectedValue(dbError);

            await expect(billingService.getDomainEnterprise('test.com')).rejects.toThrow(dbError);
        });

        it('should convert domain name to lowercase', async () => {
            const mockDomain = 'TEST.COM';
            Enterprise.findOne.mockResolvedValue({ name: mockDomain.toLowerCase() });

            const result = await billingService.getDomainEnterprise(mockDomain);

            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: mockDomain.toLowerCase() }
            });
            expect(result).toBe(mockDomain);
        });

        it('should handle special characters in domain name', async () => {
            const mockDomain = 'test-domain_123.co.uk';
            Enterprise.findOne.mockResolvedValue({ name: mockDomain });

            const result = await billingService.getDomainEnterprise(mockDomain);

            expect(result).toBe(mockDomain);
        });
    });

    describe('findEnterpriseByParentId', () => {
        const mockUserData = { id: 1 };
        const mockEnterprise = { id: 1, status: 'completed' };
        const mockMember = { UserId: 1, RoleId: 2, isDeleted: false };

        beforeEach(() => {
            global.publicMember = {
                findOne: jest.fn(),
            };
        });

        it('should return null for invalid ParentCompanyId', async () => {
            const result = await billingService.findEnterpriseByParentId(null, mockUserData);
            expect(result).toBeNull();
        });

        it('should return enterprise for account member', async () => {
            global.publicMember.findOne.mockResolvedValue({ ...mockMember, isAccount: true });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await billingService.findEnterpriseByParentId('123', mockUserData);

            expect(result).toBe(mockEnterprise);
        });

        it('should return enterprise for non-account member', async () => {
            global.publicMember.findOne.mockResolvedValue(mockMember);
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await billingService.findEnterpriseByParentId('123', mockUserData);

            expect(result).toBe(mockEnterprise);
        });

        it('should return null for undefined ParentCompanyId', async () => {
            const result = await billingService.findEnterpriseByParentId('undefined', mockUserData);
            expect(result).toBeNull();
        });

        it('should return null for empty string ParentCompanyId', async () => {
            const result = await billingService.findEnterpriseByParentId('', mockUserData);
            expect(result).toBeNull();
        });

        it('should handle member not found scenario', async () => {
            global.publicMember.findOne.mockResolvedValue(null);
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await billingService.findEnterpriseByParentId('123', mockUserData);

            expect(result).toBe(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: '123', status: 'completed' }
            });
        });

        it('should handle enterprise not found for account member', async () => {
            global.publicMember.findOne.mockResolvedValue({ ...mockMember, isAccount: true });
            Enterprise.findOne.mockResolvedValue(null);

            const result = await billingService.findEnterpriseByParentId('123', mockUserData);

            expect(result).toBeNull();
        });

        it('should handle database error in member query', async () => {
            const dbError = new Error('Member query failed');
            global.publicMember.findOne.mockRejectedValue(dbError);

            await expect(billingService.findEnterpriseByParentId('123', mockUserData))
                .rejects.toThrow(dbError);
        });

        it('should handle database error in enterprise query', async () => {
            const dbError = new Error('Enterprise query failed');
            global.publicMember.findOne.mockResolvedValue(null);
            Enterprise.findOne.mockRejectedValue(dbError);

            await expect(billingService.findEnterpriseByParentId('123', mockUserData))
                .rejects.toThrow(dbError);
        });

        it('should use correct query parameters for member lookup', async () => {
            global.publicMember.findOne.mockResolvedValue(null);

            await billingService.findEnterpriseByParentId('123', mockUserData);

            expect(global.publicMember.findOne).toHaveBeenCalledWith({
                where: {
                    UserId: mockUserData.id,
                    RoleId: { [require('../../models').Sequelize.Op.ne]: 4 },
                    isDeleted: false
                }
            });
        });
    });

    describe('payOffline', () => {
        const mockInputData = {
            query: { ParentCompanyId: '123' },
            body: { amount: 100 },
            user: { id: 1, firstName: 'John', email: '<EMAIL>' },
        };

        const mockEnterprise = { id: 1 };
        const mockAdminUsers = [{ email: '<EMAIL>' }];

        beforeEach(() => {
            Enterprise.findOne.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });
            require('../../models').User.findAll.mockResolvedValue(mockAdminUsers);
            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            helper.getDynamicModel.mockResolvedValue({ User: { findOne: jest.fn() } });
            jest.spyOn(billingService, 'getDynamicModel').mockResolvedValue();
        });

        it('should process offline payment successfully', async () => {
            const done = jest.fn();

            await billingService.payOffline(mockInputData, done);

            expect(BillingHistory.create).toHaveBeenCalledWith(expect.objectContaining({
                EnterpriseId: mockEnterprise.id,
                status: 'pending',
                paymentMethod: 'offline',
                amount: mockInputData.body.amount,
            }));
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle missing ParentCompanyId', async () => {
            const done = jest.fn();
            const inputData = { ...mockInputData, query: {} };

            await billingService.payOffline(inputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle enterprise not found', async () => {
            const done = jest.fn();
            Enterprise.findOne.mockResolvedValue(null);

            await billingService.payOffline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle billing history creation failure', async () => {
            const done = jest.fn();
            const dbError = new Error('Database error');
            BillingHistory.create.mockRejectedValue(dbError);

            await billingService.payOffline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, dbError);
        });

        it('should handle admin users query failure', async () => {
            const done = jest.fn();
            const dbError = new Error('User query failed');
            require('../../models').User.findAll.mockRejectedValue(dbError);

            await billingService.payOffline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, dbError);
        });

        it('should handle multiple admin users', async () => {
            const done = jest.fn();
            const multipleAdmins = [
                { email: '<EMAIL>' },
                { email: '<EMAIL>' },
                { email: '<EMAIL>' }
            ];
            require('../../models').User.findAll.mockResolvedValue(multipleAdmins);

            await billingService.payOffline(mockInputData, done);

            expect(done).toHaveBeenCalledTimes(3); // Called once for each admin
        });

        it('should handle empty admin users array', async () => {
            const done = jest.fn();
            require('../../models').User.findAll.mockResolvedValue([]);

            await billingService.payOffline(mockInputData, done);

            // Should not call done since no admins to notify
            expect(done).not.toHaveBeenCalled();
        });

        it('should handle getDynamicModel failure', async () => {
            const done = jest.fn();
            const modelError = new Error('Model error');
            jest.spyOn(billingService, 'getDynamicModel').mockRejectedValue(modelError);

            await billingService.payOffline(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, modelError);
        });

        it('should create billing record with correct status', async () => {
            const done = jest.fn();

            await billingService.payOffline(mockInputData, done);

            expect(BillingHistory.create).toHaveBeenCalledWith(expect.objectContaining({
                status: 'pending',
                paymentMethod: 'offline',
            }));
        });
    });

    describe('sendMail', () => {
        const mockUserData = { firstName: 'John', email: '<EMAIL>' };
        const mockMailData = 'accountadminpay';

        it('should send mail successfully', (done) => {
            MAILER.sendMail.mockImplementation((userData, mailData, subject, title, callback) => {
                callback(null, null);
            });

            billingService.sendMail(mockUserData, mockMailData, (result, error) => {
                expect(result).toBe(mockUserData);
                expect(error).toBeFalsy();
                done();
            });
        });

        it('should handle mail error', (done) => {
            const mockError = new Error('Mail failed');
            MAILER.sendMail.mockImplementation((userData, mailData, subject, title, callback) => {
                callback(null, mockError);
            });

            billingService.sendMail(mockUserData, mockMailData, (result, error) => {
                expect(result).toBeNull();
                expect(error).toBeInstanceOf(ApiError);
                done();
            });
        });

        it('should call MAILER.sendMail with correct parameters', (done) => {
            MAILER.sendMail.mockImplementation((userData, mailData, subject, title, callback) => {
                callback(null, null);
            });

            billingService.sendMail(mockUserData, mockMailData, () => {
                expect(MAILER.sendMail).toHaveBeenCalledWith(
                    mockUserData,
                    mockMailData,
                    'Account Admin Payment',
                    'Account Admin Payment',
                    expect.any(Function)
                );
                done();
            });
        });

        it('should handle different mail data types', (done) => {
            const differentMailData = 'customMailType';
            MAILER.sendMail.mockImplementation((userData, mailData, subject, title, callback) => {
                callback(null, null);
            });

            billingService.sendMail(mockUserData, differentMailData, (result, error) => {
                expect(result).toBe(mockUserData);
                expect(error).toBeFalsy();
                done();
            });
        });

        it('should handle null userData', (done) => {
            MAILER.sendMail.mockImplementation((userData, mailData, subject, title, callback) => {
                callback(null, null);
            });

            billingService.sendMail(null, mockMailData, (result, error) => {
                expect(result).toBeNull();
                expect(error).toBeFalsy();
                done();
            });
        });
    });

    describe('returnProjectModel', () => {
        it('should call helper.returnProjectModel and set global variables', async () => {
            const mockModelData = {
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            };
            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await billingService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
            expect(global.publicUser).toBe(mockModelData.User);
            expect(global.publicMember).toBe(mockModelData.Member);
        });

        it('should handle helper.returnProjectModel failure', async () => {
            const modelError = new Error('Model retrieval failed');
            helper.returnProjectModel.mockRejectedValue(modelError);

            await expect(billingService.returnProjectModel()).rejects.toThrow(modelError);
        });
    });

    describe('getDynamicModel', () => {
        const mockInputData = {
            body: { ParentCompanyId: '123' },
            params: {},
            user: { email: '<EMAIL>', domainName: 'test.com' }
        };

        beforeEach(() => {
            global.publicUser = {
                findOne: jest.fn()
            };
            helper.getDynamicModel.mockResolvedValue({ User: { findOne: jest.fn() } });
            jest.spyOn(billingService, 'returnProjectModel').mockResolvedValue();
            jest.spyOn(billingService, 'getDomainEnterprise').mockResolvedValue('test.com');
            jest.spyOn(billingService, 'findEnterpriseByParentId').mockResolvedValue({ name: 'test' });
        });

        it('should process input data with domain name', async () => {
            const result = await billingService.getDynamicModel(mockInputData);

            expect(billingService.returnProjectModel).toHaveBeenCalled();
            expect(billingService.getDomainEnterprise).toHaveBeenCalledWith('test.com');
            expect(helper.getDynamicModel).toHaveBeenCalledWith('test.com');
        });

        it('should handle missing domain name and use ParentCompanyId', async () => {
            const inputDataNoDomain = {
                ...mockInputData,
                user: { email: '<EMAIL>' }
            };
            global.publicUser.findOne.mockResolvedValue({ id: 1 });
            jest.spyOn(billingService, 'getDomainEnterprise').mockResolvedValue('');

            await billingService.getDynamicModel(inputDataNoDomain);

            expect(billingService.findEnterpriseByParentId).toHaveBeenCalledWith('123', { id: 1 });
        });

        it('should handle ParentCompanyId from params', async () => {
            const inputDataWithParams = {
                body: {},
                params: { ParentCompanyId: '456' },
                user: { email: '<EMAIL>' }
            };
            global.publicUser.findOne.mockResolvedValue({ id: 1 });
            jest.spyOn(billingService, 'getDomainEnterprise').mockResolvedValue('');

            await billingService.getDynamicModel(inputDataWithParams);

            expect(billingService.findEnterpriseByParentId).toHaveBeenCalledWith('456', { id: 1 });
        });

        it('should update user data when domain name exists', async () => {
            const newUser = { id: 2, email: '<EMAIL>' };
            helper.getDynamicModel.mockResolvedValue({ User: { findOne: jest.fn().mockResolvedValue(newUser) } });

            const result = await billingService.getDynamicModel(mockInputData);

            expect(mockInputData.user).toBe(newUser);
        });

        it('should handle user not found in dynamic model', async () => {
            helper.getDynamicModel.mockResolvedValue({ User: { findOne: jest.fn().mockResolvedValue(null) } });

            await billingService.getDynamicModel(mockInputData);

            expect(mockInputData.user).toBe(mockInputData.user); // Should remain unchanged
        });
    });
});