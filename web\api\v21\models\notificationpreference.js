module.exports = (sequelize, DataTypes) => {
  const NotificationPreference = sequelize.define(
    'NotificationPreference',
    {
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      ParentCompanyId: DataTypes.INTEGER,
      NotificationPreferenceItemId: DataTypes.INTEGER,
      instant: DataTypes.BOOLEAN,
      dailyDigest: DataTypes.BOOLEAN,
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  NotificationPreference.associate = (models) => {
    NotificationPreference.belongsTo(models.NotificationPreferenceItem);
  };
  NotificationPreference.getAll = async (attr) => {
    const city = await NotificationPreference.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return city;
  };
  NotificationPreference.createInstance = async (attr) => {
    const getItems = await NotificationPreference.create(attr);
    return getItems;
  };
  return NotificationPreference;
};
