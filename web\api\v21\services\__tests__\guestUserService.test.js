// Mock all external dependencies BEFORE importing the service (deliveryService pattern)

// Mock sequelize
const mockSequelize = {
  DataTypes: {
    STRING: 'STRING',
    INTEGER: 'INTEGER',
    BOOLEAN: 'BOOLEAN',
    DATE: 'DATE',
    TEXT: 'TEXT',
    DECIMAL: 'DECIMAL'
  },
  and: jest.fn(() => ({ and: 'condition' })),
  or: jest.fn(() => ({ or: 'condition' })),
  literal: jest.fn(() => 'LITERAL_RESULT'),
  Op: {
    and: Symbol('and'),
    or: Symbol('or'),
    eq: Symbol('eq'),
    ne: Symbol('ne'),
    in: Symbol('in'),
    notIn: Symbol('notIn'),
    like: Symbol('like'),
    notLike: Symbol('notLike'),
    gt: Symbol('gt'),
    gte: Symbol('gte'),
    lt: Symbol('lt'),
    lte: Symbol('lte')
  }
};

jest.mock('sequelize', () => mockSequelize);

// Mock the mailer
jest.mock('../../mailer', () => ({
  sendMail: jest.fn().mockResolvedValue(true)
}));

// Mock all models
const mockModels = {
  Sequelize: {
    Op: {
      and: 'and',
      or: 'or',
      in: 'in',
      notIn: 'notIn',
      between: 'between',
      gte: 'gte',
      lte: 'lte',
      ne: 'ne',
      like: 'like'
    }
  },
  Enterprise: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  TimeZone: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getProjectAndSettings: jest.fn()
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
    count: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  GuestUser: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn()
  },
  GuestSession: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn()
  },
  GuestRequest: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    getAll: jest.fn()
  },
  GuestActivity: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn()
  },
  GuestPermission: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  Company: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  Role: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  ProjectSettings: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Notification: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  }
};

// Mock external services
jest.mock('../../models', () => mockModels);

jest.mock('../../helpers/domainHelper', () => ({
  returnProjectModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  }),
  getDynamicModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  })
}));

jest.mock('../../helpers/notificationHelper', () => ({
  sendNotification: jest.fn()
}));

jest.mock('../../config/fcm', () => ({
  sendPushNotification: jest.fn()
}));

jest.mock('../voidService', () => ({
  checkVoidStatus: jest.fn()
}));

jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(() => 'mock-jwt-token'),
  verify: jest.fn(() => ({ userId: 1, sessionId: 'session-123' }))
}));

jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({ toString: () => 'random-session-id' }))
}));

// Import the actual service AFTER all mocks are set up (deliveryService pattern)
const guestUserService = require('../guestUserService');

describe('GuestUserService Comprehensive Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateGuestAccess', () => {
    it('should validate guest access successfully', async () => {
      const inputData = {
        body: {
          email: '<EMAIL>',
          projectCode: 'PROJ001'
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockProject = {
        id: 1,
        projectCode: 'PROJ001',
        allowGuestAccess: true,
        ProjectSettings: {
          guestAccessEnabled: true,
          guestSessionDuration: 24
        }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Project.findOne.mockResolvedValue(mockProject);

      const done = jest.fn();
      await guestUserService.validateGuestAccess(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        isValid: true,
        project: mockProject
      }), false);
    });

    it('should handle invalid project code', async () => {
      const inputData = {
        body: {
          email: '<EMAIL>',
          projectCode: 'INVALID'
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Project.findOne.mockResolvedValue(null);

      const done = jest.fn();
      await guestUserService.validateGuestAccess(inputData, done);

      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('createGuestSession', () => {
    it('should create guest session successfully', async () => {
      const inputData = {
        body: {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Guest',
          projectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockGuestUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Guest'
      };
      const mockSession = {
        id: 1,
        sessionId: 'session-123',
        guestUserId: 1,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.GuestUser.findOne.mockResolvedValue(null);
      mockModels.GuestUser.create.mockResolvedValue(mockGuestUser);
      mockModels.GuestSession.create.mockResolvedValue(mockSession);

      const done = jest.fn();
      await guestUserService.createGuestSession(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        guestUser: mockGuestUser,
        session: mockSession,
        token: 'mock-jwt-token'
      }), false);
    });
  });

  describe('createGuestUser', () => {
    it('should create guest user successfully', async () => {
      const inputData = {
        body: {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Guest',
          projectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockGuestUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Guest'
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.GuestUser.findOne.mockResolvedValue(null);
      mockModels.GuestUser.create.mockResolvedValue(mockGuestUser);

      const result = await guestUserService.createGuestUser(inputData);

      expect(result).toEqual({ newUser: mockGuestUser });
    });

    it('should handle existing guest user', async () => {
      const inputData = {
        body: {
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Guest',
          projectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockExistingUser = {
        id: 2,
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Guest'
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.GuestUser.findOne.mockResolvedValue(mockExistingUser);

      const result = await guestUserService.createGuestUser(inputData);

      expect(result).toEqual({ existUser: mockExistingUser });
    });
  });

  describe('listGuestRequests', () => {
    it('should list guest requests successfully', async () => {
      const inputData = {
        params: {
          ProjectId: 1,
          ParentCompanyId: null,
          pageSize: 10,
          pageNumber: 1
        },
        body: {
          ParentCompanyId: null
        },
        user: {
          id: 1,
          domainName: 'example.com'
        }
      };

      const mockGuestRequests = [
        { id: 1, description: 'Guest Request 1', status: 'Pending', guestEmail: '<EMAIL>' },
        { id: 2, description: 'Guest Request 2', status: 'Approved', guestEmail: '<EMAIL>' }
      ];
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.GuestRequest.getAll.mockResolvedValue(mockGuestRequests);

      const done = jest.fn();
      await guestUserService.listGuestRequests(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array)
      }), false);
    });
  });

  describe('Utility Methods', () => {
    describe('validateGuestPermissions', () => {
      it('should validate guest permissions successfully', async () => {
        const guestUserId = 1;
        const projectId = 1;
        const mockPermissions = [
          { id: 1, permission: 'read', resource: 'deliveries' },
          { id: 2, permission: 'create', resource: 'requests' }
        ];

        mockModels.GuestPermission.findAll.mockResolvedValue(mockPermissions);

        const result = await guestUserService.validateGuestPermissions(guestUserId, projectId);

        expect(result).toEqual(mockPermissions);
      });

      it('should handle no permissions', async () => {
        const guestUserId = 999;
        const projectId = 1;

        mockModels.GuestPermission.findAll.mockResolvedValue([]);

        const result = await guestUserService.validateGuestPermissions(guestUserId, projectId);

        expect(result).toEqual([]);
      });
    });

    describe('generateGuestToken', () => {
      it('should generate guest token successfully', () => {
        const guestUser = {
          id: 1,
          email: '<EMAIL>',
          projectId: 1
        };
        const sessionId = 'session-123';

        const result = guestUserService.generateGuestToken(guestUser, sessionId);

        expect(result).toBe('mock-jwt-token');
        expect(require('jsonwebtoken').sign).toHaveBeenCalledWith(
          expect.objectContaining({
            userId: guestUser.id,
            email: guestUser.email,
            sessionId: sessionId
          }),
          expect.any(String),
          expect.objectContaining({
            expiresIn: expect.any(String)
          })
        );
      });
    });

    describe('trackGuestActivity', () => {
      it('should track guest activity successfully', async () => {
        const activityData = {
          guestUserId: 1,
          action: 'view_delivery',
          resource: 'delivery_123',
          projectId: 1
        };

        const mockActivity = {
          id: 1,
          ...activityData,
          timestamp: new Date()
        };

        mockModels.GuestActivity.create.mockResolvedValue(mockActivity);

        const result = await guestUserService.trackGuestActivity(activityData);

        expect(result).toEqual(mockActivity);
        expect(mockModels.GuestActivity.create).toHaveBeenCalledWith(
          expect.objectContaining(activityData)
        );
      });
    });

    describe('cleanupGuestSessions', () => {
      it('should cleanup expired guest sessions', async () => {
        const mockExpiredSessions = [
          { id: 1, sessionId: 'expired-1', expiresAt: new Date(Date.now() - 1000) },
          { id: 2, sessionId: 'expired-2', expiresAt: new Date(Date.now() - 2000) }
        ];

        mockModels.GuestSession.findAll.mockResolvedValue(mockExpiredSessions);
        mockModels.GuestSession.destroy.mockResolvedValue(2);

        const result = await guestUserService.cleanupGuestSessions();

        expect(result).toEqual({
          cleanedSessions: 2,
          expiredSessions: mockExpiredSessions
        });
      });

      it('should handle no expired sessions', async () => {
        mockModels.GuestSession.findAll.mockResolvedValue([]);
        mockModels.GuestSession.destroy.mockResolvedValue(0);

        const result = await guestUserService.cleanupGuestSessions();

        expect(result).toEqual({
          cleanedSessions: 0,
          expiredSessions: []
        });
      });
    });
  });
});