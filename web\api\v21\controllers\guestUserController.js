const status = require('http-status');
const guestUserService = require('../services/guestUserService');
const { deliveryService, craneRequestService } = require('../services');
const { ProjectSettings, TimeZone } = require('../models');
const { Project } = require('../models');

const guestUserController = {
  async getCompanyList(req, res, next) {
    try {
      const company = await guestUserService.getCompanies(req);
      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }
      return res.status(200).json(company);
    } catch (error) {
      next(error);
    }
  },
  async createGuestUser(req, res, next) {
    try {
      const user = await guestUserService.createGuestUser(req);
      if (user.activeMember) {
        return res.status(400).json(user);
      }
      return res.status(201).json(user);
    } catch (error) {
      console.log(error);
      return res.status(500).json({ message: error.message });
    }
  },
  async alreadyVisited(req, res, next) {
    try {
      const user = await guestUserService.alreadyVisited(req);
      if (user.newUser || user.data) {
        return res.status(201).json(user);
      }
      if (user.activeMember) {
        return res.status(200).json(user);
      }
      return res.status(404).json({ message: 'User not found' });
    } catch (error) {
      next(error)
    }
  },
  async guestUserDetails(req, res, next) {
    try {
      const user = await guestUserService.guestUserDetail(req);
      if (user) {
        return res.status(201).json(user);
      }
      return res.status(404).json({ message: 'User details not found' });
    } catch (error) {
      next(error)
    }
  },
  async lastDeliveryId(req, res, next) {
    try {
      const data = await guestUserService.lastDeliveryId(req);
      if (data) {
        return res.status(200).json({ lastId: data });
      }
    } catch (error) {
      next(error);
    }
  },
  async listAllMember(req, res, next) {
    try {
      const data = await guestUserService.listAllMember(req);
      if (data) {
        return res.status(200).json({ message: 'Member listed Successfully.', data });
      }
    } catch (error) {
      next(error);
    }
  },

  async getEventNDR(req, res, next) {
    try {
      const response = await guestUserService.getEventNDR(req);
      req.query.start = req.body.start;
      req.query.end = req.body.end;
      req.query.ProjectId = req.params.ProjectId;
      req.query.ParentCompanyId = req.body.ParentCompanyId;
      req.query.search = req.body.search;
      req.body.isApplicableToDelivery = true;
      let response1 = [];
      let response2;
      let response3;
      if (req.body.filterCount === 0) {
        response1 = await guestUserService.getAll(req, next);
      }
      if (req.params.ProjectId !== '') {
        response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
        response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
      }
      deliveryService.lastDelivery(req, (lastDetail, error1) => {
        if (!error1) {
          const requestData = [];
          if (response?.rows?.length > 0) {
            requestData.push(...response.rows);
          }
          if (response1 && response1.length > 0) {
            requestData.push(...response1);
          }
          res.status(status.OK).json({
            message: 'Delivery Booking listed Successfully.',
            data: requestData,
            statusData: response2,
            cardData: response3,
            lastId: lastDetail,
          });
        } else {
          next(error1);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getDeliveryRequestWithCrane(req, res, next) {
    try {
      const response = await guestUserService.getDeliveryRequestWithCrane(req);
      req.query.start = req.body.start;
      req.query.end = req.body.end;
      req.query.ProjectId = req.params.ProjectId;
      req.query.ParentCompanyId = req.body.ParentCompanyId;
      req.query.search = req.body.search;
      req.body.isApplicableToCrane = true;
      let response1 = [];
      let response2 = '';
      let response3;
      if (req.body.filterCount === 0) {
        response1 = await guestUserService.getAll(req, next);
      }
      if (req.params.ProjectId !== '') {
        response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
        response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
      }
      craneRequestService.lastCraneRequest(req, (lastDetail, error1) => {
        if (!error1) {
          const requestData = [];
          if (response && response.length > 0) {
            requestData.push(...response);
          }
          if (response1 && response1.length > 0) {
            requestData.push(...response1);
          }
          res.status(status.OK).json({
            message: 'Delivery Booking Associated With Crane Equipment Type listed Successfully.',
            data: requestData,
            statusData: response2,
            cardData: response3,
            lastId: lastDetail,
          });
        } else {
          next(error1);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequest(req, res, next) {
    try {
      await guestUserService.getConcreteRequest(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToConcrete = true;
          let response1 = [];
          let response2 = '';
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await guestUserService.getAll(req);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }
          const requestData = [];
          if (response && response.length > 0) {
            requestData.push(...response);
          }
          if (response1 && response1.length > 0) {
            requestData.push(...response1);
          }
          res.status(status.OK).json({
            message: 'Concrete booking listed successfully',
            data: requestData,
            statusData: response2,
            cardData: response3,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async listGates(req, res, next) {
    try {
      const gateDetail = await guestUserService.listGates(req);
      const lastDetail = await guestUserService.lastGate(req);

      res.status(status.OK).json({
        message: 'Gate Listed successfully.',
        data: gateDetail,
        lastId: lastDetail,
      });
    } catch (error) {
      next(error);
    }
  },
  async listEquipment(req, res, next) {
    try {
      const equipmentDetail = await guestUserService.listEquipment(req);
      const lastDetail = await guestUserService.lastEquipment(req);

      res.status(status.OK).json({
        message: 'Equipment Listed successfully.',
        data: equipmentDetail,
        lastId: lastDetail,
      });
    } catch (error) {
      next(error);
    }
  },

  async getCompanies(req, res, next) {
    try {
      const response = await guestUserService.getAllCompany(req);
      if (response) {
        const { companyList, parentCompany } = response;
        const newCompanyList = [];
        await companyList.rows.forEach((element) => {
          newCompanyList.push({
            id: element.id,
            companyName: element.companyName,
          });
        });
        if (parentCompany) {
          const index = newCompanyList.findIndex(
            (item) =>
              item.id === parentCompany.id ||
              item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
          );
          if (index === -1) {
            newCompanyList.push({
              id: parentCompany.id,
              companyName: parentCompany.companyName,
            });
          }
        }
        newCompanyList.sort((a, b) =>
          a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
        );
        res.status(status.OK).json({
          message: 'Company list.',
          data: newCompanyList,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getDefinableWork(req, res, next) {
    try {
      const response = await guestUserService.getDefinableWork(req);
      if (response) {
        res.status(status.OK).json({
          message: 'Definable work list.',
          data: response.defineRecord,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getLocations(req, res, next) {
    try {
      const locations = await guestUserService.getLocations(req);
      if (locations) {
        res.status(status.OK).json({
          message: 'Location Listed successfully.',
          data: locations,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getLastCraneRequestId(req, res, next) {
    try {
      const lastDetail = await guestUserService.lastCraneRequest(req);
      if (lastDetail) {
        res.status(status.OK).json({
          message: 'Delivery Booking Last Id Viewed Successfully.',
          lastId: lastDetail,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getTimeZoneList(req, res, next) {
    try {
      const timeZoneList = await TimeZone.getAll();
      res.status(status.OK).json({ data: timeZoneList });
    } catch (e) {
      next(e);
    }
  },
  async getSingleProjectDetail(req, res, next) {
    try {

      const projectList = await Project.findOne({
        include: [{ association: 'stripePlan', attribute: ['stripePlanName'] }],
        where: { id: req.params.ProjectId },
        attribute: ['PlanId', 'projectName'],
      });
      res.status(status.OK).json({
        message: 'Project List.',
        data: projectList,
      });
    } catch (e) {
      next(e);
    }
  },
  async getMemberDataMixPanel(req, res, next) {
    try {
      const response = await guestUserService.getMemberDataMixPanel(req);
      if (response) {
        res.status(status.OK).json({
          message: 'Member Data Sent Successfully.',
          data: response,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getMemberData(req, res, next) {
    try {
      const response = await guestUserService.getMemberData(req);
      if (response) {
        res.status(status.OK).json({
          message: 'Member listed Successfully.',
          data: response,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async searchMember(req, res, next) {
    try {
      const response = await guestUserService.searchMember(req);
      if (response) {
        res.status(status.OK).json(response);
      }
    } catch (e) {
      next(e);
    }
  },
  async newRequest(req, res, next) {
    try {
      await guestUserService.newRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({ response });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async craneListEquipment(req, res, next) {
    try {
      const equipmentDetail = await guestUserService.craneListEquipment(req);
      res.status(status.OK).json({
        message: 'Crane Equipment Listed successfully.',
        data: equipmentDetail,
      });
    } catch (e) {
      next(e);
    }
  },
  async createCraneRequest(req, res, next) {
    try {
      await guestUserService.newCraneRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Guest Crane Booking Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteDropdownDetail(req, res, next) {
    try {
      const getConcreteDropdownDetail = await guestUserService.getConcreteDropdownDetail(req);
      if (getConcreteDropdownDetail.status === 200) {
        res.status(status.OK).json({
          message: 'Concrete Booking Dropdown Details Viewed Successfully.',
          data: getConcreteDropdownDetail.data,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: getConcreteDropdownDetail.msg,
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async createConcreteRequest(req, res, next) {
    try {
      await guestUserService.newConcreteRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getNDRData(req, res, next) {
    try {
      const response = await guestUserService.getNDRData(req);
      if (response) {
        res.status(status.OK).json({
          message: 'Delivery Booking listed Successfully.',
          data: response,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getSingleCraneRequest(req, res, next) {
    try {
      const response = await guestUserService.getSingleCraneRequest(req);
      if (response) {
        res.status(status.OK).json({
          message: 'Crane Booking Viewed Successfully.',
          data: response,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getSingleConcreteRequest(req, res, next) {
    try {
      const response = await guestUserService.getSingleConcreteRequest(req);
      if (response) {
        res.status(status.OK).json({
          message: 'Concrete Booking Viewed Successfully.',
          data: response,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async editRequest(req, res, next) {
    try {
      await guestUserService.editRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Delivery Booking Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editCraneRequest(req, res, next) {
    try {
      await guestUserService.editCraneRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editConcreteRequest(req, res, next) {
    try {
      await guestUserService.editConcreteRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createAttachement(req, res, next) {
    try {
      await guestUserService.createAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createComment(req, res, next) {
    try {
      await guestUserService.createComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Comment added successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createCraneRequestAttachement(req, res, next) {
    try {
      await guestUserService.createCraneRequestAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createCraneRequestComment(req, res, next) {
    try {
      await guestUserService.createCraneRequestComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking Comment added successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createConcreteRequestAttachment(req, res, next) {
    try {
      await guestUserService.createConcreteRequestAttachment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createConcreteRequestComment(req, res, next) {
    try {
      await guestUserService.createConcreteRequestComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Comment added successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async isRequestToMember(req, res, next) {
    try {
      const user = await guestUserService.isRequestToMember(req);
      if (user) {
        return res.status(200).json(user);
      }
    } catch (error) {
      next(error);
    }
  },
  async updateGuestMember(req, res, next) {
    try {
      const user = await guestUserService.updateGuestMember(req);
      if (user) {
        return res.status(200).json(user);
      }
    } catch (error) {
      return res.status(500).json({ message: 'Internal server error', error });
    }
  },
};

module.exports = guestUserController;
