const moment = require('moment');
const fs = require('fs');
const _ = require('lodash');
const awsConfig = require('../middlewares/awsConfig');
const { Project, Company } = require('../models');
const puppeteerService = require('./puppeteerService');

const pdfHeatMapService = {
  async pdfFormatOfDeliveryRequest(params, loginUser, data, req, done) {
    const mailContent = [];

    const projectData = await Project.findOne({
      where: {
        isDeleted: false,
        id: +params.ProjectId,
      },
      attributes: ['projectName'],
    });
    const companyData = await Company.findOne({
      where: {
        isDeleted: false,
        ParentCompanyId: +req.body.ParentCompanyId,
        isParent: true,
      },
      attributes: ['companyName'],
    });
    let pdftemplate = fs.readFileSync(
      '/usr/src/web/api/v20/views/mail-templates/report-heat-map.html',
      {
        encoding: 'utf-8',
      },
    );
    let heatMapReportList = [];
    heatMapReportList = Object.entries(data).map(function ([date, { timeslots, totalCount }]) {
      return {
        date,
        timeslots: Object.values(timeslots),
        totalCount,
      };
    });
    const maxCount = _.max(_.flatMap(_.map(_.values(heatMapReportList), 'timeslots'), _.values));

    for (const report of heatMapReportList) {
      let content = `<tr style="font-size: 14px;border-top:5px solid #fff;border-bottom:5px solid #fff;">
                        <td style="padding:12px;"> ${report.date.replace(
        ',',
        '&comma;',
      )}</td>`;
      for (const timeslot of report.timeslots) {
        const colorCode = timeslot / maxCount;
        if (colorCode === 0) {
          content += `<td style="text-align: center;color:#000;background-color: rgb(227, 226, 227);">${timeslot}</td>`;
        } else {
          content += `<td style="text-align: center;color:#000;background: rgb(244, 94, 40); opacity: ${colorCode};">${timeslot}</td>`;
        }
      }

      content += `<td style="background-color: #F0F0F0;text-align: center;color:#000;">${report.totalCount}</td>
                      </tr>`;
      mailContent.push(content);
    }

    pdftemplate = pdftemplate
      .replace('$projectName', `${projectData.projectName} `)
      .replace('$companyName', `${companyData.companyName} `)
      .replace('$ReportName', req.body.reportName)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName} `)
      .replace('$data', `${mailContent} `);
    pdftemplate = pdftemplate.replace(/,/g, '');

    const pdfBuffer = await puppeteerService.generatePdfBuffer(pdftemplate);
    if (pdfBuffer) {
      awsConfig.reportUpload(
        pdfBuffer,
        req.body.reportName,
        req.body.exportType,
        async (result, error1) => {
          if (!error1) {
            done(result, false);
          }
        },
      );
    } else {
      done(false, { message: 'cannot export the document' });
    }
  },
};
module.exports = pdfHeatMapService;
