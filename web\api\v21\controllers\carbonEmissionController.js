const carbonEmissionService = require('../services/carbonEmissionService');
const axios = require('axios');
const ML_API_URL = process.env.ML_API_URL;

const CarbonEmissionController = {
    async fileUpload(req, res, next) {
        try {
            await carbonEmissionService.fileUpload(req, async (uploadResponse, error) => {
                if (!error) {
                    try {
                        const co2EmissionValues = await carbonEmissionService.getEmissionValues();
                        const mlRequest = {
                            s3_url: uploadResponse.S3URI
                        };
                        const mlApiResponse = await axios.post(ML_API_URL, mlRequest, {
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        if (mlApiResponse) {
                            const totalEnergyConsumed = mlApiResponse.data['Utility Invoice']['Items'].find(item => item['item_name'] === 'Electricity')
                            const co2Emissions = await carbonEmissionService.calculateCO2Emissions(req.params.zipcode, totalEnergyConsumed.Quantity);
                            if (mlApiResponse.data['Utility Invoice']['Items'].find(item => item['item_name'] === 'Electricity')) {
                                mlApiResponse.data['Utility Invoice']['Items'].find(item => item['item_name'] === 'Electricity')['CO2 Emissions'] = co2Emissions.totalCO2Emission;
                            };
                            mlApiResponse.data['Utility Invoice']['Items'].forEach(utilityItem => {
                                const matchingCo2Value = co2EmissionValues.find(co2Item => co2Item.utilityType === utilityItem.item_name);
                                if (matchingCo2Value) {
                                    utilityItem['CO2 Emissions'] = utilityItem['Quantity'] * parseFloat(matchingCo2Value.co2PerUnit);
                                }
                            });
                            res.json({ data: mlApiResponse.data, invoice_URL: uploadResponse.Location, co2Emissions });
                        }

                    } catch (mlError) {
                        next(mlError);
                    }
                } else {
                    next(error);
                }
            });
        } catch (e) {
            next(e);
        }
    },

    async dashboardData(req, res, next) {
        try {
            await carbonEmissionService.getDashboardData(req.params, async (response, error) => {
                if (error) {
                    console.log(error, 'error');
                    next(error);
                } else {
                    res.json({
                        message: 'Dashboard data Listed Successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    }
}
module.exports = CarbonEmissionController;
