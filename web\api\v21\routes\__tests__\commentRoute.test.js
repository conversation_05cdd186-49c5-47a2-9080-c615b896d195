const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  CommentController: {
    createComment: jest.fn(),
    createInspectionComment: jest.fn(),
    getComment: jest.fn(),
    getInspectionComment: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  commentValidation: {
    addComment: jest.fn(),
    getComment: jest.fn(),
    getInspectionComment: jest.fn(),
  },
}));

describe('commentRoute', () => {
  let router;
  let commentRoute;
  let CommentController;
  let passportConfig;
  let commentValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    commentRoute = require('../commentRoute');
    const controllers = require('../../controllers');
    CommentController = controllers.CommentController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    commentValidation = validations.commentValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = commentRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(2);
      expect(router.get).toHaveBeenCalledTimes(2);

      // Verify POST routes
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/create_comment',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CommentController.createComment,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/create_inspection_comment',
        passportConfig.isAuthenticated,
        CommentController.createInspectionComment,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_comment/:DeliveryRequestId/:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CommentController.getComment,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_inspection_comment/:InspectionRequestId/:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CommentController.getInspectionComment,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(3);
      expect(validate).toHaveBeenCalledWith(
        commentValidation.addComment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        commentValidation.getComment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        commentValidation.getInspectionComment,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = commentRoute.router;
      const result2 = commentRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes in the correct order', () => {
      commentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      expect(postCalls[0][0]).toBe('/create_comment');
      expect(postCalls[1][0]).toBe('/create_inspection_comment');
      expect(getCalls[0][0]).toBe('/get_comment/:DeliveryRequestId/:ParentCompanyId/:ProjectId');
      expect(getCalls[1][0]).toBe('/get_inspection_comment/:InspectionRequestId/:ParentCompanyId/:ProjectId');
    });

    it('should use authentication middleware for all routes', () => {
      commentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should use validation middleware for most routes except create_inspection_comment', () => {
      commentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // First POST route should have validation
      expect(postCalls[0]).toHaveLength(4); // path + validation + auth + controller
      expect(postCalls[0][1]).toBe('mocked-validate-middleware');

      // Second POST route should not have validation (commented out in source)
      expect(postCalls[1]).toHaveLength(3); // path + auth + controller
      expect(postCalls[1][1]).toBe(passportConfig.isAuthenticated);

      // GET routes should have validation
      getCalls.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof commentRoute).toBe('object');
      expect(commentRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(commentRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(commentRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
