const Joi = require('joi');

const gateValidation = {
  addGates: {
    body: Joi.object({
      gateName: Joi.string().min(3).required(),
      createdBy: Joi.string(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  updateGates: {
    body: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      gateName: Joi.string().required(),
      ParentCompanyId: Joi.any(),
      isActive: Joi.boolean().optional(),
    }),
  },
  deactiveGates: {
    body: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      gateName: Joi.string().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  gateDetail: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      search: Joi.optional().allow(''),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      isFilter: Joi.boolean().required(),
      showActivatedAlone: Joi.boolean().required(),
    }),
  },
  gateListDetail: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
  },
  deleteGates: {
    body: Joi.object({
      id: Joi.array(),
      ProjectId: Joi.number().required(),
      isSelectAll: Joi.boolean().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = gateValidation;
