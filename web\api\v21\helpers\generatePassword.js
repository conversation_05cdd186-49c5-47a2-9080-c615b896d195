const crypto = require('crypto');

exports.generatePassword = () => {
  const pLength = 10;
  const keyListAlpha = 'abcdefghijklmnopqrstuvwxyz';
  const keyListInt = '123456789';
  const keyListSpec = '!@#_';

  const alphaIndex = crypto.randomInt(0, keyListAlpha.length);
  const index = crypto.randomInt(0, keyListInt.length);
  const specIndex = crypto.randomInt(0, keyListSpec.length);

  let password = JSON.parse(process.env.SERVICE_PASSWORDS).genPass;
  let len = Math.ceil(pLength / 2);
  let i;
  len -= 1;
  const lenSpec = pLength - 2 * len;
  for (i = 0; i < len; i += 1) {
    password += keyListAlpha.charAt(alphaIndex);
    password += keyListInt.charAt(index);
  }
  for (i = 0; i < lenSpec; i += 1)
    password += keyListSpec.charAt(specIndex);

  // Secure Fisher-Yates shuffle using crypto.randomInt
  let arr = password.split('');
  for (let j = arr.length - 1; j > 0; j--) {
    const k = crypto.randomInt(0, j + 1);
    [arr[j], arr[k]] = [arr[k], arr[j]];
  }
  password = arr.join('');

  return password;
};
