const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('../../controllers', () => ({
  BillingController: {
    payOnline: jest.fn(),
    getBillingInfo: jest.fn(),
    payOffline: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAccountAdmin: jest.fn(),
}));

describe('billingRoute', () => {
  let router;
  let billingRoute;
  let BillingController;
  let passportConfig;
  let checkAdmin;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    billingRoute = require('../billingRoute');
    const controllers = require('../../controllers');
    BillingController = controllers.BillingController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = billingRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(2);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST routes
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/payOnline',
        passportConfig.isAuthenticated,
        checkAdmin.isAccountAdmin,
        BillingController.payOnline,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/payOffline',
        passportConfig.isAuthenticated,
        checkAdmin.isAccountAdmin,
        BillingController.payOffline,
      );

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/listBilling',
        passportConfig.isAuthenticated,
        checkAdmin.isAccountAdmin,
        BillingController.getBillingInfo,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = billingRoute.router;
      const result2 = billingRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes in the correct order', () => {
      billingRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      expect(postCalls[0][0]).toBe('/payOnline');
      expect(postCalls[1][0]).toBe('/payOffline');
      expect(getCalls[0][0]).toBe('/listBilling');
    });

    it('should use authentication and account admin middleware for all routes', () => {
      billingRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication and account admin check
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toContain(checkAdmin.isAccountAdmin);
        expect(call).toHaveLength(4); // path + auth + admin + controller
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof billingRoute).toBe('object');
      expect(billingRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(billingRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(billingRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
