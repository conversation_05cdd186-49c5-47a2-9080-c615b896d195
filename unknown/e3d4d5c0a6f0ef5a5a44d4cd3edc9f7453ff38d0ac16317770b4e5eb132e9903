const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  memberValidation: {
    inviteMembers: jest.fn(),
    getUserDetail: jest.fn(),
    editMember: jest.fn(),
    deleteMember: jest.fn(),
    listMember: jest.fn(),
    searchMember: jest.fn(),
    listAllMember: jest.fn(),
    getOverViewDetail: jest.fn(),
    updateUserProfile: jest.fn(),
    listGuestMember: jest.fn(),
    addGuestAsMember: jest.fn(),
    rejectGuestRequest: jest.fn(),
  },
}));

jest.mock('../../controllers', () => ({
  MemberController: {
    inviteMembers: jest.fn(),
    resendInviteLink: jest.fn(),
    getUserDetail: jest.fn(),
    editMember: jest.fn(),
    updateInviteMember: jest.fn(),
    deleteMember: jest.fn(),
    checkExistMember: jest.fn(),
    listMember: jest.fn(),
    searchMember: jest.fn(),
    searchAllMember: jest.fn(),
    listAllMember: jest.fn(),
    getOverViewDetail: jest.fn(),
    updateUserProfile: jest.fn(),
    getRoles: jest.fn(),
    getAllMemberLists: jest.fn(),
    getMemberDetail: jest.fn(),
    getMemberProjects: jest.fn(),
    getAllMemberListsForAssignProject: jest.fn(),
    changeMemberPassword: jest.fn(),
    updateMemberProfile: jest.fn(),
    updateMemberProjectStatus: jest.fn(),
    activateMember: jest.fn(),
    deactivateMember: jest.fn(),
    getMappedRequests: jest.fn(),
    getOnboardingInviteLink: jest.fn(),
    getMemberData: jest.fn(),
    searchAutoApproveMember: jest.fn(),
    listRetoolMembers: jest.fn(),
    listRegisteredMembers: jest.fn(),
    listGuestMembers: jest.fn(),
    addGuestAsMember: jest.fn(),
    rejectGuestRequest: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isAdmin: jest.fn(),
}));

describe('memberRoute', () => {
  let router;
  let memberRoute;
  let MemberController;
  let passportConfig;
  let checkAdmin;
  let memberValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    memberRoute = require('../memberRoute');
    const controllers = require('../../controllers');
    MemberController = controllers.MemberController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    const validations = require('../../middlewares/validations');
    memberValidation = validations.memberValidation;
    validate = require('express-validation').validate;
  });

  describe('router', () => {
    it('should create a router instance', () => {
      const result = memberRoute.router;
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);
    });

    it('should register POST /invite_member route with validation, authentication, and project admin check', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/invite_member',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        MemberController.inviteMembers,
      );
    });

    it('should register POST /resend_invite_link route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/resend_invite_link',
        passportConfig.isAuthenticated,
        MemberController.resendInviteLink,
      );
    });

    it('should register POST /get_user_detail route with validation only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/get_user_detail',
        'mocked-validate-middleware',
        MemberController.getUserDetail,
      );
    });

    it('should register POST /edit_member route with validation, authentication, and project admin check', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/edit_member',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        MemberController.editMember,
      );
    });

    it('should register POST /update_member route without middleware', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/update_member',
        MemberController.updateInviteMember,
      );
    });

    it('should register POST /delete_member route with validation, authentication, and project admin check', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/delete_member',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        MemberController.deleteMember,
      );
    });

    it('should register POST /check_user route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/check_user',
        passportConfig.isAuthenticated,
        MemberController.checkExistMember,
      );
    });

    it('should register POST /list_member/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/list_member/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.listMember,
      );
    });

    it('should register GET /search_member/:ProjectId/:search/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/search_member/:ProjectId/:search/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.searchMember,
      );
    });

    it('should register GET /search_all_member/:ProjectId/:search/?:ParentCompanyId route with authentication only', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/search_all_member/:ProjectId/:search/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        MemberController.searchAllMember,
      );
    });

    it('should register GET /list_all_member/:ProjectId/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/list_all_member/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.listAllMember,
      );
    });

    it('should register GET /get_overview_detail/:ProjectId/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_overview_detail/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.getOverViewDetail,
      );
    });

    it('should register POST /update_profile/ route with validation and authentication', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/update_profile/',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.updateUserProfile,
      );
    });

    it('should register GET /get_roles route with authentication only', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_roles',
        passportConfig.isAuthenticated,
        MemberController.getRoles,
      );
    });

    it('should register GET /members_list route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/members_list',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getAllMemberLists,
      );
    });

    it('should register GET /get_member_detail/:id route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_member_detail/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getMemberDetail,
      );
    });

    it('should register GET /get_member_projects/:id route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_member_projects/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getMemberProjects,
      );
    });

    it('should register GET /get_all_members route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_all_members',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getAllMemberListsForAssignProject,
      );
    });

    it('should register PUT /change_member_password/:id route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/change_member_password/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.changeMemberPassword,
      );
    });

    it('should register PUT /update_member_detail/:id route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/update_member_detail/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.updateMemberProfile,
      );
    });

    it('should register PUT /update_member_project_status/:id route with authentication and admin check', () => {
      memberRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/update_member_project_status/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.updateMemberProjectStatus,
      );
    });

    it('should register POST /activate_member route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/activate_member',
        passportConfig.isAuthenticated,
        MemberController.activateMember,
      );
    });

    it('should register POST /deactivate_member route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/deactivate_member',
        passportConfig.isAuthenticated,
        MemberController.deactivateMember,
      );
    });

    it('should register POST /get_mapped_requests route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/get_mapped_requests',
        passportConfig.isAuthenticated,
        MemberController.getMappedRequests,
      );
    });

    it('should register POST /get_onboarding_invite_link route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/get_onboarding_invite_link',
        passportConfig.isAuthenticated,
        MemberController.getOnboardingInviteLink,
      );
    });

    it('should register POST /get_member_data route with authentication only', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/get_member_data',
        passportConfig.isAuthenticated,
        MemberController.getMemberData,
      );
    });

    it('should register GET /search_member_auto_approve/:ProjectId/:search/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/search_member_auto_approve/:ProjectId/:search/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.searchAutoApproveMember,
      );
    });

    it('should register POST /list_retool_members route without middleware', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/list_retool_members',
        MemberController.listRetoolMembers,
      );
    });

    it('should register GET /list_registered_members/:ProjectId/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/list_registered_members/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.listRegisteredMembers,
      );
    });

    it('should register POST /list_guest_member/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId route with validation and authentication', () => {
      memberRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/list_guest_member/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        MemberController.listGuestMembers,
      );
    });

    it('should register PUT /add_guest_as_member route with validation and authentication', () => {
      memberRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/add_guest_as_member',
        passportConfig.isAuthenticated,
        'mocked-validate-middleware',
        MemberController.addGuestAsMember,
      );
    });

    it('should register PUT /reject_guest_request route with validation and authentication', () => {
      memberRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/reject_guest_request',
        passportConfig.isAuthenticated,
        'mocked-validate-middleware',
        MemberController.rejectGuestRequest,
      );
    });
  });
});
