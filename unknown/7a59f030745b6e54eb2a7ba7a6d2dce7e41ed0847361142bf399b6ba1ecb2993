// Mock models first
jest.mock('../../models', () => {
  const mockProject = {
    getUserProjects: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };

  return {
    Enterprise: {
      findOne: jest.fn(),
    },
    Sequelize: {
      Op: {
        and: Symbol('and'),
      },
      and: jest.fn(),
      where: jest.fn(),
      fn: jest.fn(),
      col: jest.fn(),
    },
    Project: mockProject,
    User: {
      findOne: jest.fn(),
    },
  };
});

// Mock helpers
jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
}));

jest.mock('../../helpers/enterpriseCheckHelper', () => ({
  checkEnterPrise: jest.fn(),
}));

// Mock services
jest.mock('../../services', () => ({
  projectService: {
    createProject: jest.fn(),
    getProjects: jest.fn(),
    createAccountProject: jest.fn(),
    existProject: jest.fn(),
    upgradePlan: jest.fn(),
    getNewDynamicModel: jest.fn(),
    getProjectsCompany: jest.fn(),
    getProSubStripeDate: jest.fn(),
    getPlansAndProjects: jest.fn(),
    getProjectList: jest.fn(),
    assignNewProjectToMember: jest.fn(),
    editMemberProject: jest.fn(),
    getMemberProject: jest.fn(),
    projectsBillingHistories: jest.fn(),
    getProjectBillingHistories: jest.fn(),
    getTotalProjects: jest.fn(),
    extendProjectDuration: jest.fn(),
    updateProjectSharingSettings: jest.fn(),
    uploadLogisticPlan: jest.fn(),
    generatePublicUrlForExistingProjects: jest.fn(),
    decodeProjectDetailUrl: jest.fn(),
    updateDashboardLogisticPlan: jest.fn(),
    retoolParentCompanyWithProjects: jest.fn(),
    updatedSitePlanInAWS: jest.fn(),
  },
}));

const ProjectController = require('../ProjectController');
const { projectService } = require('../../services');
const helper = require('../../helpers/domainHelper');
const enterpriseCheck = require('../../helpers/enterpriseCheckHelper');
const { Enterprise, Sequelize, Project, User } = require('../../models');

describe('ProjectController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      user: { domainName: 'test' },
      params: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createProject', () => {
    it('should create project successfully', async () => {
      const mockResponse = { id: 1, name: 'Test Project' };
      projectService.createProject.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.createProject(mockReq, mockRes, mockNext);

      expect(projectService.createProject).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from project creation', async () => {
      const mockError = new Error('Service error');
      projectService.createProject.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.createProject(mockReq, mockRes, mockNext);

      expect(projectService.createProject).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in project creation', async () => {
      const mockError = new Error('Exception error');
      projectService.createProject.mockImplementation(() => {
        throw mockError;
      });

      await ProjectController.createProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getProjects', () => {
    it('should get projects successfully', async () => {
      const mockResponse = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' },
      ];
      projectService.getProjects.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.getProjects(mockReq, mockRes, mockNext);

      expect(projectService.getProjects).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get projects', async () => {
      const mockError = new Error('Service error');
      projectService.getProjects.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.getProjects(mockReq, mockRes, mockNext);

      expect(projectService.getProjects).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getUserProjects', () => {
    it('should get user projects successfully with rows', async () => {
      const mockProjectList = {
        rows: [
          { projectName: 'Project B' },
          { projectName: 'Project A' },
        ],
      };
      helper.getDynamicModel.mockResolvedValue({ Project: { getUserProjects: jest.fn().mockResolvedValue(mockProjectList) } });

      await ProjectController.getUserProjects(mockReq, mockRes, mockNext);

      expect(helper.getDynamicModel).toHaveBeenCalledWith('test');
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project List.',
        data: mockProjectList,
      });
      // Check that projects are sorted alphabetically
      expect(mockProjectList.rows[0].projectName).toBe('Project A');
      expect(mockProjectList.rows[1].projectName).toBe('Project B');
    });

    it('should get user projects successfully without rows', async () => {
      const mockProjectList = [
        { projectName: 'Project Z' },
        { projectName: 'Project A' },
      ];
      helper.getDynamicModel.mockResolvedValue({ Project: { getUserProjects: jest.fn().mockResolvedValue(mockProjectList) } });

      await ProjectController.getUserProjects(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project List.',
        data: mockProjectList,
      });
      // Check that projects are sorted alphabetically
      expect(mockProjectList[0].projectName).toBe('Project A');
      expect(mockProjectList[1].projectName).toBe('Project Z');
    });

    it('should handle error in getUserProjects', async () => {
      const mockError = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(mockError);

      await ProjectController.getUserProjects(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getProjectList', () => {
    it('should get project list successfully with data', async () => {
      const mockProjects = {
        projectList: [
          { id: 1, name: 'Project 1' },
          { id: 2, name: 'Project 2' },
        ],
        count: 2,
      };
      projectService.getProjectList.mockResolvedValue(mockProjects);

      await ProjectController.getProjectList(mockReq, mockRes, mockNext);

      expect(projectService.getProjectList).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Projects listed Successfully.',
        data: mockProjects.projectList,
        count: mockProjects.count,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle empty project list', async () => {
      projectService.getProjectList.mockResolvedValue(null);

      await ProjectController.getProjectList(mockReq, mockRes, mockNext);

      expect(projectService.getProjectList).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Projects listed successfully.',
        data: [],
        count: 0,
      });
    });

    it('should handle error from project list', async () => {
      const mockError = new Error('Service error');
      projectService.getProjectList.mockRejectedValue(mockError);

      await ProjectController.getProjectList(mockReq, mockRes, mockNext);

      expect(projectService.getProjectList).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getTotalProjects', () => {
    it('should get total projects successfully', async () => {
      const mockProjects = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' },
      ];
      projectService.getTotalProjects.mockResolvedValue(mockProjects);

      await ProjectController.getTotalProjects(mockReq, mockRes, mockNext);

      expect(projectService.getTotalProjects).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project listed Successfully.',
        data: mockProjects,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get total projects', async () => {
      const mockError = new Error('Service error');
      projectService.getTotalProjects.mockRejectedValue(mockError);

      await ProjectController.getTotalProjects(mockReq, mockRes, mockNext);

      expect(projectService.getTotalProjects).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('createAccountProject', () => {
    it('should create account project successfully', async () => {
      const mockResponse = { id: 1, name: 'Account Project' };
      projectService.createAccountProject.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.createAccountProject(mockReq, mockRes, mockNext);

      expect(projectService.createAccountProject).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project Created Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from account project creation', async () => {
      const mockError = new Error('Service error');
      projectService.createAccountProject.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.createAccountProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in account project creation', async () => {
      const mockError = new Error('Exception error');
      projectService.createAccountProject.mockImplementation(() => {
        throw mockError;
      });

      await ProjectController.createAccountProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully', async () => {
      const inputData = { user: { domainName: 'test' } };
      helper.getDynamicModel.mockResolvedValue({ Project: { name: 'MockProject' } });

      const result = await ProjectController.getDynamicModel(inputData);

      expect(helper.getDynamicModel).toHaveBeenCalledWith('test');
      expect(result).toBe(true);
    });

    it('should handle error in get dynamic model', async () => {
      const inputData = { user: { domainName: 'test' } };
      const mockError = new Error('Helper error');
      helper.getDynamicModel.mockRejectedValue(mockError);

      const result = await ProjectController.getDynamicModel(inputData);

      expect(result).toBe(mockError);
    });
  });

  describe('existProject', () => {
    it('should check project existence successfully', async () => {
      const mockResponse = { exists: true };
      projectService.existProject.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.existProject(mockReq, mockRes, mockNext);

      expect(projectService.existProject).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        response: mockResponse,
      });
    });

    it('should handle error from project existence check', async () => {
      const mockError = new Error('Service error');
      projectService.existProject.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.existProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in project existence check', async () => {
      const mockError = new Error('Exception error');
      projectService.existProject.mockImplementation(() => {
        throw mockError;
      });

      await ProjectController.existProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('upgradePlan', () => {
    it('should upgrade plan successfully', async () => {
      const mockResponse = { planId: 2, upgraded: true };
      projectService.upgradePlan.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.upgradePlan(mockReq, mockRes, mockNext);

      expect(projectService.upgradePlan).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Plan Updated Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from plan upgrade', async () => {
      const mockError = new Error('Service error');
      projectService.upgradePlan.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.upgradePlan(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in plan upgrade', async () => {
      const mockError = new Error('Exception error');
      projectService.upgradePlan.mockImplementation(() => {
        throw mockError;
      });

      await ProjectController.upgradePlan(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getAccountProjects', () => {
    it('should get account projects successfully without enterprise', async () => {
      mockReq.params = { companyId: 1 };
      mockReq.user = { email: '<EMAIL>' };

      enterpriseCheck.checkEnterPrise.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);

      const mockProjectList = [{ id: 1, name: 'Project 1' }];
      helper.getDynamicModel.mockResolvedValue({
        Project: { getUserProjects: jest.fn().mockResolvedValue(mockProjectList) },
        User: { findOne: jest.fn().mockResolvedValue({ id: 1 }) }
      });

      await ProjectController.getAccountProjects(mockReq, mockRes, mockNext);

      expect(enterpriseCheck.checkEnterPrise).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project List.',
        data: mockProjectList,
      });
    });

    it('should get account projects with enterprise', async () => {
      mockReq.params = { companyId: 1 };
      mockReq.user = { email: '<EMAIL>' };

      enterpriseCheck.checkEnterPrise.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue({
        name: 'TestEnterprise',
        ParentCompanyId: 1,
        status: 'completed'
      });

      const mockProjectList = [{ id: 1, name: 'Project 1' }];
      const mockUser = { id: 1, email: '<EMAIL>' };
      helper.getDynamicModel.mockResolvedValue({
        Project: { getUserProjects: jest.fn().mockResolvedValue(mockProjectList) },
        User: { findOne: jest.fn().mockResolvedValue(mockUser) }
      });

      await ProjectController.getAccountProjects(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project List.',
        data: mockProjectList,
      });
    });

    it('should handle error in get account projects', async () => {
      const mockError = new Error('Database error');
      enterpriseCheck.checkEnterPrise.mockRejectedValue(mockError);

      await ProjectController.getAccountProjects(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getProjectsCompany', () => {
    it('should get projects company successfully', async () => {
      mockReq.user = { domainName: 'test' };

      projectService.getNewDynamicModel.mockResolvedValue(null);
      const mockProjectList = [{ id: 1, name: 'Project 1' }];
      helper.getDynamicModel.mockResolvedValue({
        Project: { getUserProjects: jest.fn().mockResolvedValue(mockProjectList) }
      });

      const mockResponse = { projects: mockProjectList };
      projectService.getProjectsCompany.mockImplementation((req, projectList, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.getProjectsCompany(mockReq, mockRes, mockNext);

      expect(projectService.getProjectsCompany).toHaveBeenCalledWith(
        mockReq,
        mockProjectList,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        data: mockResponse,
      });
    });

    it('should handle error from get projects company', async () => {
      mockReq.user = { domainName: 'test' };

      projectService.getNewDynamicModel.mockResolvedValue(null);
      helper.getDynamicModel.mockResolvedValue({
        Project: { getUserProjects: jest.fn().mockResolvedValue([]) }
      });

      const mockError = new Error('Service error');
      projectService.getProjectsCompany.mockImplementation((req, projectList, callback) => {
        callback(null, mockError);
      });

      await ProjectController.getProjectsCompany(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in get projects company', async () => {
      const mockError = new Error('Exception error');
      projectService.getNewDynamicModel.mockRejectedValue(mockError);

      await ProjectController.getProjectsCompany(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getSingleProjectDetail', () => {
    it('should get single project detail successfully', async () => {
      mockReq.user = { domainName: 'test' };
      mockReq.params = { ProjectId: 1 };

      const mockProject = {
        id: 1,
        projectName: 'Test Project',
        PlanId: 1,
        stripePlan: { stripePlanName: 'Basic' }
      };
      helper.getDynamicModel.mockResolvedValue({
        Project: { findOne: jest.fn().mockResolvedValue(mockProject) }
      });

      await ProjectController.getSingleProjectDetail(mockReq, mockRes, mockNext);

      expect(helper.getDynamicModel).toHaveBeenCalledWith('test');
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project List.',
        data: mockProject,
      });
    });

    it('should handle error in get single project detail', async () => {
      const mockError = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(mockError);

      await ProjectController.getSingleProjectDetail(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getSingleProjectProfileDetail', () => {
    it('should get single project profile detail successfully', async () => {
      mockReq.user = { domainName: 'test' };
      mockReq.params = { ProjectId: 1 };

      const mockProject = {
        dataValues: {
          id: 1,
          projectName: 'Test Project',
          PlanId: 1,
          stripePlan: { stripePlanName: 'Basic' },
          StripeSubscription: { id: 'sub_123' }
        }
      };
      const mockSubDetail = { subscription: 'details' };

      helper.getDynamicModel.mockResolvedValue({
        Project: { findOne: jest.fn().mockResolvedValue(mockProject) }
      });
      projectService.getProSubStripeDate.mockResolvedValue(mockSubDetail);

      await ProjectController.getSingleProjectProfileDetail(mockReq, mockRes, mockNext);

      expect(projectService.getProSubStripeDate).toHaveBeenCalledWith(mockProject.dataValues);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project Lists.',
        data: mockSubDetail,
      });
    });

    it('should handle error in get single project profile detail', async () => {
      const mockError = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(mockError);

      await ProjectController.getSingleProjectProfileDetail(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getPlansAndProjects', () => {
    it('should get plans and projects successfully', async () => {
      const mockResponse = { plans: [], projects: [] };
      projectService.getPlansAndProjects.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.getPlansAndProjects(mockReq, mockRes, mockNext);

      expect(projectService.getPlansAndProjects).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        data: mockResponse,
      });
    });

    it('should handle error from get plans and projects', async () => {
      const mockError = new Error('Service error');
      projectService.getPlansAndProjects.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.getPlansAndProjects(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in get plans and projects', async () => {
      const mockError = new Error('Exception error');
      projectService.getPlansAndProjects.mockImplementation(() => {
        throw mockError;
      });

      await ProjectController.getPlansAndProjects(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('assignNewProjectToMember', () => {
    it('should assign new project to member successfully', async () => {
      projectService.assignNewProjectToMember.mockResolvedValue(true);

      await ProjectController.assignNewProjectToMember(mockReq, mockRes, mockNext);

      expect(projectService.assignNewProjectToMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Created Successfully.',
      });
    });

    it('should handle failure in assign new project to member', async () => {
      projectService.assignNewProjectToMember.mockResolvedValue(false);

      await ProjectController.assignNewProjectToMember(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 422,
        message: 'Cannot able to create project.',
      });
    });

    it('should handle error in assign new project to member', async () => {
      const mockError = new Error('Service error');
      projectService.assignNewProjectToMember.mockRejectedValue(mockError);

      await ProjectController.assignNewProjectToMember(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editMemberProject', () => {
    it('should edit member project successfully', async () => {
      projectService.editMemberProject.mockResolvedValue(true);

      await ProjectController.editMemberProject(mockReq, mockRes, mockNext);

      expect(projectService.editMemberProject).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Updated Successfully.',
      });
    });

    it('should handle failure in edit member project', async () => {
      projectService.editMemberProject.mockResolvedValue(false);

      await ProjectController.editMemberProject(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 422,
        message: 'Cannot able to update project.',
      });
    });

    it('should handle error in edit member project', async () => {
      const mockError = new Error('Service error');
      projectService.editMemberProject.mockRejectedValue(mockError);

      await ProjectController.editMemberProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getMemberProject', () => {
    it('should get member project successfully', async () => {
      const mockProject = { id: 1, name: 'Member Project' };
      projectService.getMemberProject.mockResolvedValue(mockProject);

      await ProjectController.getMemberProject(mockReq, mockRes, mockNext);

      expect(projectService.getMemberProject).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Viewed Successfully.',
        data: mockProject,
      });
    });

    it('should handle no member project found', async () => {
      projectService.getMemberProject.mockResolvedValue(null);

      await ProjectController.getMemberProject(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Cannot able to view project.',
      });
    });

    it('should handle error in get member project', async () => {
      const mockError = new Error('Service error');
      projectService.getMemberProject.mockRejectedValue(mockError);

      await ProjectController.getMemberProject(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('projectsBillingHistories', () => {
    it('should get projects billing histories successfully', async () => {
      const mockHistories = [{ id: 1, amount: 100 }];
      projectService.projectsBillingHistories.mockResolvedValue(mockHistories);

      await ProjectController.projectsBillingHistories(mockReq, mockRes, mockNext);

      expect(projectService.projectsBillingHistories).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Projects Billing Histories Listed Successfully.',
        data: mockHistories,
      });
    });

    it('should handle no billing histories found', async () => {
      projectService.projectsBillingHistories.mockResolvedValue(null);

      await ProjectController.projectsBillingHistories(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Cannot able to view Projects Billing Histories.',
      });
    });

    it('should handle error in projects billing histories', async () => {
      const mockError = new Error('Service error');
      projectService.projectsBillingHistories.mockRejectedValue(mockError);

      await ProjectController.projectsBillingHistories(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getProjectBillingHistories', () => {
    it('should get project billing histories successfully', async () => {
      const mockHistory = { id: 1, projectId: 1, amount: 100 };
      projectService.getProjectBillingHistories.mockResolvedValue(mockHistory);

      await ProjectController.getProjectBillingHistories(mockReq, mockRes, mockNext);

      expect(projectService.getProjectBillingHistories).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Billing History Viewed Successfully.',
        data: mockHistory,
      });
    });

    it('should handle no project billing history found', async () => {
      projectService.getProjectBillingHistories.mockResolvedValue(null);

      await ProjectController.getProjectBillingHistories(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Cannot able to view Project Billing History.',
      });
    });

    it('should handle error in get project billing histories', async () => {
      const mockError = new Error('Service error');
      projectService.getProjectBillingHistories.mockRejectedValue(mockError);

      await ProjectController.getProjectBillingHistories(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('extendProjectDuration', () => {
    it('should extend project duration successfully', async () => {
      projectService.extendProjectDuration.mockResolvedValue(true);

      await ProjectController.extendProjectDuration(mockReq, mockRes, mockNext);

      expect(projectService.extendProjectDuration).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project duration extended',
      });
    });

    it('should handle failure in extend project duration', async () => {
      projectService.extendProjectDuration.mockResolvedValue(false);

      await ProjectController.extendProjectDuration(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 500,
        message: 'Cannot able to extend the duration. Try again after sometime.!',
      });
    });

    it('should handle error in extend project duration', async () => {
      const mockError = new Error('Service error');
      projectService.extendProjectDuration.mockRejectedValue(mockError);

      await ProjectController.extendProjectDuration(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateProjectSharingSettings', () => {
    it('should update project sharing settings successfully', async () => {
      mockReq.body = { projectId: 1, sharing: true };
      projectService.updateProjectSharingSettings.mockResolvedValue(true);

      await ProjectController.updateProjectSharingSettings(mockReq, mockRes, mockNext);

      expect(projectService.updateProjectSharingSettings).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Settings Updated Successfully',
      });
    });

    it('should handle failure in update project sharing settings', async () => {
      mockReq.body = { projectId: 1, sharing: true };
      projectService.updateProjectSharingSettings.mockResolvedValue(false);

      await ProjectController.updateProjectSharingSettings(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot update project settings',
      });
    });

    it('should handle error in update project sharing settings', async () => {
      const mockError = new Error('Service error');
      projectService.updateProjectSharingSettings.mockRejectedValue(mockError);

      await ProjectController.updateProjectSharingSettings(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('uploadProjectLogisticPlanUrl', () => {
    it('should upload project logistic plan url successfully', async () => {
      const mockResponse = { url: 'https://example.com/plan.pdf' };
      projectService.uploadLogisticPlan.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ProjectController.uploadProjectLogisticPlanUrl(mockReq, mockRes, mockNext);

      expect(projectService.uploadLogisticPlan).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Site Plan updated successfully. Please note that it may take a few minutes for the changes to reflect in the dashboard',
        data: mockResponse,
      });
    });

    it('should handle error from upload project logistic plan url', async () => {
      const mockError = new Error('Service error');
      projectService.uploadLogisticPlan.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ProjectController.uploadProjectLogisticPlanUrl(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in upload project logistic plan url', async () => {
      const mockError = new Error('Exception error');
      projectService.uploadLogisticPlan.mockImplementation(() => {
        throw mockError;
      });

      await ProjectController.uploadProjectLogisticPlanUrl(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('generatePublicUrlForExistingProjects', () => {
    it('should generate public url for existing projects successfully', async () => {
      projectService.generatePublicUrlForExistingProjects.mockResolvedValue(true);

      await ProjectController.generatePublicUrlForExistingProjects(mockReq, mockRes, mockNext);

      expect(projectService.generatePublicUrlForExistingProjects).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project`s Public URL Updated Successfully',
      });
    });

    it('should handle failure in generate public url for existing projects', async () => {
      projectService.generatePublicUrlForExistingProjects.mockResolvedValue(false);

      await ProjectController.generatePublicUrlForExistingProjects(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot Project`s Public URL',
      });
    });

    it('should handle error in generate public url for existing projects', async () => {
      const mockError = new Error('Service error');
      projectService.generatePublicUrlForExistingProjects.mockRejectedValue(mockError);

      await ProjectController.generatePublicUrlForExistingProjects(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('decodeProjectDetailUrl', () => {
    it('should decode project detail url successfully', async () => {
      const mockResponse = { projectId: 1, decoded: true };
      projectService.decodeProjectDetailUrl.mockResolvedValue(mockResponse);

      await ProjectController.decodeProjectDetailUrl(mockReq, mockRes);

      expect(projectService.decodeProjectDetailUrl).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Decrypted URL Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error in decode project detail url', async () => {
      const mockError = new Error('Decode error');
      projectService.decodeProjectDetailUrl.mockRejectedValue(mockError);

      await ProjectController.decodeProjectDetailUrl(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: mockError.message,
      });
    });
  });

  describe('updateDashboardLogisticPlan', () => {
    it('should update dashboard logistic plan successfully', async () => {
      mockReq.body = { planData: 'test' };
      projectService.updateDashboardLogisticPlan.mockResolvedValue(true);

      await ProjectController.updateDashboardLogisticPlan(mockReq, mockRes, mockNext);

      expect(projectService.updateDashboardLogisticPlan).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Logistic Plan Uploaded Successfully',
      });
    });

    it('should handle failure in update dashboard logistic plan', async () => {
      mockReq.body = { planData: 'test' };
      projectService.updateDashboardLogisticPlan.mockResolvedValue(false);

      await ProjectController.updateDashboardLogisticPlan(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot upload Logistic Plan',
      });
    });

    it('should handle error in update dashboard logistic plan', async () => {
      const mockError = new Error('Service error');
      projectService.updateDashboardLogisticPlan.mockRejectedValue(mockError);

      await ProjectController.updateDashboardLogisticPlan(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('retoolParentCompanyWithProjects', () => {
    it('should get retool parent company with projects successfully', async () => {
      const mockCompanies = [{ id: 1, name: 'Company 1' }];
      projectService.retoolParentCompanyWithProjects.mockResolvedValue(mockCompanies);

      await ProjectController.retoolParentCompanyWithProjects(mockReq, mockRes);

      expect(projectService.retoolParentCompanyWithProjects).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        data: mockCompanies,
      });
    });

    it('should handle error in retool parent company with projects', async () => {
      const mockError = new Error('Service error');
      projectService.retoolParentCompanyWithProjects.mockRejectedValue(mockError);

      await ProjectController.retoolParentCompanyWithProjects(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: mockError.message,
      });
    });
  });

  describe('updatedSitePlanInAWS', () => {
    it('should update site plan in AWS successfully', async () => {
      const mockResponse = { url: 'https://aws.com/plan.pdf' };
      projectService.updatedSitePlanInAWS.mockResolvedValue(mockResponse);

      await ProjectController.updatedSitePlanInAWS(mockReq, mockRes, mockNext);

      expect(projectService.updatedSitePlanInAWS).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Project Settings Updated Successfully',
        data: mockResponse,
      });
    });

    it('should handle failure in update site plan in AWS', async () => {
      projectService.updatedSitePlanInAWS.mockResolvedValue(false);

      await ProjectController.updatedSitePlanInAWS(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot update project settings',
      });
    });

    it('should handle error in update site plan in AWS', async () => {
      const mockError = new Error('Service error');
      projectService.updatedSitePlanInAWS.mockRejectedValue(mockError);

      await ProjectController.updatedSitePlanInAWS(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
