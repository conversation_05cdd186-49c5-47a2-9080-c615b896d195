// Mock models first
jest.mock('../../models', () => ({
  NotificationPreferenceItem: {
    findAll: jest.fn(),
    findOne: jest.fn(),
  },
  NotificationPreference: {
    createInstance: jest.fn(),
    update: jest.fn(),
  },
  Member: {
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  },
  Project: {
    findAll: jest.fn(),
    findOne: jest.fn(),
  },
}));

// Mock services
jest.mock('../../services', () => ({
  notificationPreferenceService: {
    setNotificationPreference: jest.fn(),
    listNotificationPreference: jest.fn(),
    notificationPreferenceItems: jest.fn(),
  },
}));

const NotificationPreferenceController = require('../NotificationPreferenceController');
const { notificationPreferenceService } = require('../../services');
const {
  NotificationPreferenceItem,
  NotificationPreference,
  Member,
  Project,
} = require('../../models');

describe('NotificationPreferenceController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('setNotificationPreference', () => {
    it('should set notification preference successfully', async () => {
      const mockResponse = { id: 1, type: 'email', enabled: true };
      notificationPreferenceService.setNotificationPreference.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationPreferenceController.setNotificationPreference(mockReq, mockRes, mockNext);

      expect(notificationPreferenceService.setNotificationPreference).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification Preference Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from notification preference setting', async () => {
      const mockError = new Error('Service error');
      notificationPreferenceService.setNotificationPreference.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationPreferenceController.setNotificationPreference(mockReq, mockRes, mockNext);

      expect(notificationPreferenceService.setNotificationPreference).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listNotificationPreference', () => {
    it('should list notification preferences successfully', async () => {
      const mockResponse = [{ id: 1, type: 'email', enabled: true }];
      notificationPreferenceService.listNotificationPreference.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationPreferenceController.listNotificationPreference(mockReq, mockRes, mockNext);

      expect(notificationPreferenceService.listNotificationPreference).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification Preference Listed Successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from notification preferences listing', async () => {
      const mockError = new Error('Service error');
      notificationPreferenceService.listNotificationPreference.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationPreferenceController.listNotificationPreference(mockReq, mockRes, mockNext);

      expect(notificationPreferenceService.listNotificationPreference).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('notificationPreferenceItems', () => {
    it('should get notification preference items successfully', async () => {
      const mockResponse = [{ id: 1, description: 'Email notifications' }];
      notificationPreferenceService.notificationPreferenceItems.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationPreferenceController.notificationPreferenceItems(mockReq, mockRes, mockNext);

      expect(notificationPreferenceService.notificationPreferenceItems).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification Preference Items Listed Successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from notification preference items', async () => {
      const mockError = new Error('Service error');
      notificationPreferenceService.notificationPreferenceItems.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationPreferenceController.notificationPreferenceItems(mockReq, mockRes, mockNext);

      expect(notificationPreferenceService.notificationPreferenceItems).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('addNotificationPreferenceToAllMembers', () => {
    it('should add notification preferences to all members successfully', async () => {
      const mockNotificationItems = [
        { id: 1, description: 'Email notifications', inappNotification: false },
        { id: 7, description: 'When a comment is added to a delivery/crane/concrete request', itemId: 4, emailNotification: true, inappNotification: false, isDeleted: false },
      ];
      const mockProjects = [{ id: 1, name: 'Project 1' }];
      const mockMembers = [{ id: 1, ProjectId: 1, ParentCompanyId: 1 }];

      NotificationPreferenceItem.findAll.mockResolvedValue(mockNotificationItems);
      Project.findAll.mockResolvedValue(mockProjects);
      Member.findAll.mockResolvedValue(mockMembers);
      Project.findOne.mockResolvedValue({ TimeZone: { id: 1 } });
      Member.update.mockResolvedValue([1]);
      NotificationPreference.createInstance.mockResolvedValue({ id: 1 });

      await NotificationPreferenceController.addNotificationPreferenceToAllMembers(mockReq, mockRes);

      expect(NotificationPreferenceItem.findAll).toHaveBeenCalledWith({
        where: { isDeleted: false },
      });
      expect(Project.findAll).toHaveBeenCalledWith({
        where: { isDeleted: false },
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success',
      });
    });

    it('should handle error in adding notification preferences to all members', async () => {
      const mockError = new Error('Database error');
      NotificationPreferenceItem.findAll.mockRejectedValue(mockError);

      await NotificationPreferenceController.addNotificationPreferenceToAllMembers(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Error adding notification preferences',
        error: mockError.message,
      });
    });
  });

  describe('addNotificationPreferenceToAMember', () => {
    it('should add notification preference to a member successfully', async () => {
      const mockNotificationItems = [
        { id: 1, description: 'Email notifications', inappNotification: false },
        { id: 7, description: 'When a comment is added to a delivery/crane/concrete request', itemId: 4, emailNotification: true, inappNotification: false, isDeleted: false },
      ];
      const mockMember = { id: 1, ProjectId: 1, ParentCompanyId: 1 };
      const mockProject = { TimeZone: { id: 1 }, toJSON: () => ({ TimeZone: { id: 1 } }) };

      mockReq.body = { MemberId: 1, ProjectId: 1, ParentCompanyId: 1 };

      NotificationPreferenceItem.findAll.mockResolvedValue(mockNotificationItems);
      Member.findOne.mockResolvedValue(mockMember);
      Project.findOne.mockResolvedValue(mockProject);
      Member.update.mockResolvedValue([1]);
      NotificationPreference.createInstance.mockResolvedValue({ id: 1 });

      await NotificationPreferenceController.addNotificationPreferenceToAMember(mockReq, mockRes);

      expect(NotificationPreferenceItem.findAll).toHaveBeenCalledWith({
        where: { isDeleted: false },
      });
      expect(Member.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1,
          ProjectId: 1,
          ParentCompanyId: 1,
        },
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success',
      });
    });
  });

  describe('removeNotificationPreferenceForDeactivatedMembers', () => {
    it('should remove notification preferences for deactivated members successfully', async () => {
      const mockDeactivatedMembers = [
        { id: 1, ProjectId: 1, ParentCompanyId: 1 },
        { id: 2, ProjectId: 1, ParentCompanyId: 1 },
      ];

      Member.findAll.mockResolvedValue(mockDeactivatedMembers);
      NotificationPreference.update.mockResolvedValue([2]);

      await NotificationPreferenceController.removeNotificationPreferenceForDeactivatedMembers(mockReq, mockRes);

      expect(Member.findAll).toHaveBeenCalledWith({
        where: {
          isActive: false,
          isDeleted: false,
        },
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success',
      });
    });
  });

  describe('addOneNotificationPreferenceToMembers', () => {
    it('should add one notification preference to members successfully', async () => {
      const mockNotificationItem = { id: 12, description: 'Special notification' };
      const mockProjects = [{ id: 1, name: 'Project 1' }];
      const mockMembers = [{ id: 1, ProjectId: 1, ParentCompanyId: 1 }];

      NotificationPreferenceItem.findOne.mockResolvedValue(mockNotificationItem);
      Project.findAll.mockResolvedValue(mockProjects);
      Member.findAll.mockResolvedValue(mockMembers);
      NotificationPreference.createInstance.mockResolvedValue({ id: 1 });

      await NotificationPreferenceController.addOneNotificationPreferenceToMembers(mockReq, mockRes);

      expect(NotificationPreferenceItem.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, id: 12 },
      });
      expect(Project.findAll).toHaveBeenCalledWith({
        where: { isDeleted: false },
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success',
      });
    });

    it('should handle error in adding one notification preference to members', async () => {
      const mockError = new Error('Database error');
      NotificationPreferenceItem.findOne.mockRejectedValue(mockError);

      await NotificationPreferenceController.addOneNotificationPreferenceToMembers(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Error adding notification preferences',
        error: mockError.message,
      });
    });
  });
});
