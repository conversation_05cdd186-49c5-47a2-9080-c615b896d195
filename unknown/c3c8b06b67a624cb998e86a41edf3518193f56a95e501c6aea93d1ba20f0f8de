const status = require('http-status');
const BillingController = require('../BillingController');

// Mock the services
jest.mock('../../services', () => ({
  billingService: {
    payOffline: jest.fn(),
    payOnline: jest.fn(),
    getBillingInfo: jest.fn(),
  },
}));

const { billingService } = require('../../services');

describe('BillingController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock request, response, and next function
    mockReq = {
      body: {},
      params: {},
      query: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('payOffline', () => {
    it('should process offline payment successfully', async () => {
      // Arrange
      billingService.payOffline.mockImplementation((req, callback) =>
        callback({ success: true }, null),
      );

      // Act
      await BillingController.payOffline(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.payOffline).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Payment Request Sent Successfully.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Payment failed');
      billingService.payOffline.mockImplementation((req, callback) => callback(null, error));

      // Act
      await BillingController.payOffline(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.payOffline).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle request with payment data', async () => {
      // Arrange
      const mockReqWithPaymentData = {
        ...mockReq,
        body: {
          amount: 100.0,
          currency: 'USD',
          paymentMethod: 'bank_transfer',
          description: 'Monthly subscription',
        },
      };
      billingService.payOffline.mockImplementation((req, callback) =>
        callback({ success: true }, null),
      );

      // Act
      await BillingController.payOffline(mockReqWithPaymentData, mockRes, mockNext);

      // Assert
      expect(billingService.payOffline).toHaveBeenCalledWith(
        mockReqWithPaymentData,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Payment Request Sent Successfully.',
      });
    });
  });

  describe('payOnline', () => {
    it('should process online payment successfully', async () => {
      // Arrange
      billingService.payOnline.mockImplementation((req, callback) =>
        callback({ success: true }, null),
      );

      // Act
      await BillingController.payOnline(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.payOnline).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Payment Successfull.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Payment failed');
      billingService.payOnline.mockImplementation((req, callback) => callback(null, error));

      // Act
      await BillingController.payOnline(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.payOnline).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle request with card data', async () => {
      // Arrange
      const mockReqWithCardData = {
        ...mockReq,
        body: {
          amount: 50.0,
          currency: 'USD',
          cardNumber: '****************',
          expMonth: 12,
          expYear: 2025,
          cvc: '123',
        },
      };
      billingService.payOnline.mockImplementation((req, callback) =>
        callback({ success: true }, null),
      );

      // Act
      await BillingController.payOnline(mockReqWithCardData, mockRes, mockNext);

      // Assert
      expect(billingService.payOnline).toHaveBeenCalledWith(
        mockReqWithCardData,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Payment Successfull.',
      });
    });
  });

  describe('getBillingInfo', () => {
    it('should get billing information successfully', async () => {
      // Arrange
      const mockBillingInfo = {
        invoices: [
          { id: 1, amount: 100.0, status: 'paid', date: '2023-01-01' },
          { id: 2, amount: 150.0, status: 'pending', date: '2023-02-01' },
        ],
        totalPaid: 100.0,
        totalPending: 150.0,
      };
      billingService.getBillingInfo.mockImplementation((req, callback) =>
        callback(mockBillingInfo, null),
      );

      // Act
      await BillingController.getBillingInfo(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.getBillingInfo).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: mockBillingInfo,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to fetch billing info');
      billingService.getBillingInfo.mockImplementation((req, callback) => callback(null, error));

      // Act
      await BillingController.getBillingInfo(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.getBillingInfo).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle empty billing info', async () => {
      // Arrange
      const mockEmptyBillingInfo = {
        invoices: [],
        totalPaid: 0,
        totalPending: 0,
      };
      billingService.getBillingInfo.mockImplementation((req, callback) =>
        callback(mockEmptyBillingInfo, null),
      );

      // Act
      await BillingController.getBillingInfo(mockReq, mockRes, mockNext);

      // Assert
      expect(billingService.getBillingInfo).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: mockEmptyBillingInfo,
      });
    });

    it('should handle request with query parameters', async () => {
      // Arrange
      const mockReqWithQuery = {
        ...mockReq,
        query: {
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          status: 'paid',
        },
      };
      const mockBillingInfo = { invoices: [], totalPaid: 0, totalPending: 0 };
      billingService.getBillingInfo.mockImplementation((req, callback) =>
        callback(mockBillingInfo, null),
      );

      // Act
      await BillingController.getBillingInfo(mockReqWithQuery, mockRes, mockNext);

      // Assert
      expect(billingService.getBillingInfo).toHaveBeenCalledWith(
        mockReqWithQuery,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: mockBillingInfo,
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle null response from service', async () => {
      // Arrange
      billingService.getBillingInfo.mockImplementation((req, callback) => callback(null, null));

      // Act
      await BillingController.getBillingInfo(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: null,
      });
    });

    it('should handle undefined response from service', async () => {
      // Arrange
      billingService.getBillingInfo.mockImplementation((req, callback) =>
        callback(undefined, null),
      );

      // Act
      await BillingController.getBillingInfo(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: undefined,
      });
    });

    it('should handle empty object response from service', async () => {
      // Arrange
      billingService.getBillingInfo.mockImplementation((req, callback) => callback({}, null));

      // Act
      await BillingController.getBillingInfo(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: {},
      });
    });

    it('should handle request with user context', async () => {
      // Arrange
      const mockReqWithUser = {
        ...mockReq,
        user: {
          id: 1,
          email: '<EMAIL>',
          companyId: 123,
        },
      };
      const mockBillingInfo = { invoices: [], totalPaid: 0, totalPending: 0 };
      billingService.getBillingInfo.mockImplementation((req, callback) =>
        callback(mockBillingInfo, null),
      );

      // Act
      await BillingController.getBillingInfo(mockReqWithUser, mockRes, mockNext);

      // Assert
      expect(billingService.getBillingInfo).toHaveBeenCalledWith(
        mockReqWithUser,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: mockBillingInfo,
      });
    });

    it('should handle request with company ID parameter', async () => {
      // Arrange
      const mockReqWithCompanyId = {
        ...mockReq,
        params: { companyId: '456' },
      };
      const mockBillingInfo = { invoices: [], totalPaid: 0, totalPending: 0 };
      billingService.getBillingInfo.mockImplementation((req, callback) =>
        callback(mockBillingInfo, null),
      );

      // Act
      await BillingController.getBillingInfo(mockReqWithCompanyId, mockRes, mockNext);

      // Assert
      expect(billingService.getBillingInfo).toHaveBeenCalledWith(
        mockReqWithCompanyId,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Billing listed successfully.',
        data: mockBillingInfo,
      });
    });
  });
});
