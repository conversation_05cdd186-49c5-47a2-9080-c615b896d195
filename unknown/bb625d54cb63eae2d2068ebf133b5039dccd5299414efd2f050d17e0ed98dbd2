// Mock Sequelize first to avoid constructor issues
jest.mock('sequelize', () => {
  const mockSequelize = {
    authenticate: jest.fn(),
    close: jest.fn(),
  };
  return jest.fn(() => mockSequelize);
});

// Mock models
jest.mock('../../models', () => ({
  CalendarSetting: {
    isExits: jest.fn(),
    updateInstance: jest.fn(),
  },
  sequelize: {
    authenticate: jest.fn(),
    close: jest.fn(),
  },
}));

// Mock services
jest.mock('../../services', () => ({
  calendarSettingsService: {
    getAll: jest.fn(),
    addEvent: jest.fn(),
    updateEvent: jest.fn(),
    getCalendarEvent: jest.fn(),
  },
}));

// Mock ApiError
jest.mock('../helpers/apiError', () => {
  return jest.fn().mockImplementation((message, statusCode) => {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
  });
});

const calendarsettingsController = require('../calendarsettingsController');
const { calendarSettingsService } = require('../../services');
const { CalendarSetting } = require('../../models');
const ApiError = require('../helpers/apiError');

describe('calendarsettingsController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('getCalendarEvents', () => {
    it('should get calendar events successfully', async () => {
      const mockEvents = [
        { id: 1, title: 'Event 1', date: '2023-01-01' },
        { id: 2, title: 'Event 2', date: '2023-01-02' },
      ];
      calendarSettingsService.getAll.mockResolvedValue(mockEvents);

      await calendarsettingsController.getCalendarEvents(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        events: mockEvents,
        message: 'Events listed successfully',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get calendar events', async () => {
      const mockError = new Error('Service error');
      calendarSettingsService.getAll.mockRejectedValue(mockError);

      await calendarsettingsController.getCalendarEvents(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getCalendarMonthEvents', () => {
    it('should get calendar month events successfully', async () => {
      const mockEvents = [
        { id: 1, title: 'Monthly Event 1', date: '2023-01-15' },
        { id: 2, title: 'Monthly Event 2', date: '2023-01-20' },
      ];
      calendarSettingsService.getAll.mockResolvedValue(mockEvents);

      await calendarsettingsController.getCalendarMonthEvents(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        events: mockEvents,
        message: 'Events listed successfully',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get calendar month events', async () => {
      const mockError = new Error('Service error');
      calendarSettingsService.getAll.mockRejectedValue(mockError);

      await calendarsettingsController.getCalendarMonthEvents(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('addCalendarEvent', () => {
    it('should add calendar event successfully', async () => {
      calendarSettingsService.addEvent.mockResolvedValue(true);

      await calendarsettingsController.addCalendarEvent(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.addEvent).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Event added',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure to add calendar event', async () => {
      calendarSettingsService.addEvent.mockResolvedValue(false);

      await calendarsettingsController.addCalendarEvent(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.addEvent).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from add calendar event', async () => {
      const mockError = new Error('Service error');
      calendarSettingsService.addEvent.mockRejectedValue(mockError);

      await calendarsettingsController.addCalendarEvent(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.addEvent).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('editCalendarEvent', () => {
    beforeEach(() => {
      mockReq.params = { id: 1 };
    });

    it('should edit calendar event successfully', async () => {
      CalendarSetting.isExits.mockResolvedValue(true);
      calendarSettingsService.updateEvent.mockResolvedValue(true);

      await calendarsettingsController.editCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(calendarSettingsService.updateEvent).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Event updated',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure to update calendar event', async () => {
      CalendarSetting.isExits.mockResolvedValue(true);
      calendarSettingsService.updateEvent.mockResolvedValue(false);

      await calendarsettingsController.editCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(calendarSettingsService.updateEvent).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot update an event',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle event not found', async () => {
      CalendarSetting.isExits.mockResolvedValue(false);

      await calendarsettingsController.editCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'No event found',
      });
      expect(calendarSettingsService.updateEvent).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from edit calendar event', async () => {
      const mockError = new Error('Service error');
      CalendarSetting.isExits.mockRejectedValue(mockError);

      await calendarsettingsController.editCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteCalendarEvent', () => {
    beforeEach(() => {
      mockReq.params = { id: 1 };
    });

    it('should delete calendar event successfully', async () => {
      CalendarSetting.isExits.mockResolvedValue(true);
      CalendarSetting.updateInstance.mockResolvedValue(true);

      await calendarsettingsController.deleteCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(CalendarSetting.updateInstance).toHaveBeenCalledWith(1, { isDeleted: true });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Event deleted',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure to delete calendar event', async () => {
      CalendarSetting.isExits.mockResolvedValue(true);
      CalendarSetting.updateInstance.mockResolvedValue(false);

      await calendarsettingsController.deleteCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(CalendarSetting.updateInstance).toHaveBeenCalledWith(1, { isDeleted: true });
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Cannot delete an event',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle event not found for deletion', async () => {
      CalendarSetting.isExits.mockResolvedValue(false);

      await calendarsettingsController.deleteCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'No event found',
      });
      expect(CalendarSetting.updateInstance).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delete calendar event', async () => {
      const mockError = new Error('Service error');
      CalendarSetting.isExits.mockRejectedValue(mockError);

      await calendarsettingsController.deleteCalendarEvent(mockReq, mockRes, mockNext);

      expect(CalendarSetting.isExits).toHaveBeenCalledWith(1);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getCalendarEvent', () => {
    beforeEach(() => {
      mockReq.params = { id: 1 };
    });

    it('should get calendar event successfully', async () => {
      const mockEvent = { id: 1, title: 'Test Event', date: '2023-01-01' };
      calendarSettingsService.getCalendarEvent.mockResolvedValue(mockEvent);

      await calendarsettingsController.getCalendarEvent(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.getCalendarEvent).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        event: mockEvent,
        message: 'Event listed successfully',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get calendar event', async () => {
      const mockError = new Error('Service error');
      calendarSettingsService.getCalendarEvent.mockRejectedValue(mockError);

      await calendarsettingsController.getCalendarEvent(mockReq, mockRes, mockNext);

      expect(calendarSettingsService.getCalendarEvent).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
