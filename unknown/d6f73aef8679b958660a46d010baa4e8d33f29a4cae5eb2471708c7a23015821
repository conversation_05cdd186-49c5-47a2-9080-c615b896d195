const CraneRequestController = require('../CraneRequestController');
const { craneRequestService } = require('../../services');
const { ProjectSettings } = require('../../models');

// Mock dependencies
jest.mock('../../services', () => ({
  craneRequestService: {
    newCraneRequest: jest.fn(),
    editCraneRequest: jest.fn(),
    listCraneRequest: jest.fn(),
    getCraneRequestData: jest.fn(),
    updateCraneRequestStatus: jest.fn(),
    lastCraneRequest: jest.fn(),
    getSingleCraneRequest: jest.fn(),
    upcomingRequestListForMobile: jest.fn(),
    upcomingRequestList: jest.fn(),
    editMultipleCraneRequest: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  ProjectSettings: {
    getCalendarStatusColor: jest.fn(),
  },
}));

describe('CraneRequestController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createCraneRequest', () => {
    it('should create crane request successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      craneRequestService.newCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CraneRequestController.createCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.newCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request creation', async () => {
      const mockError = new Error('Service error');
      craneRequestService.newCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.createCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.newCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in crane request creation', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.newCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.createCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.newCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editCraneRequest', () => {
    it('should edit crane request successfully', async () => {
      const mockResponse = { id: 1, data: 'updated' };
      craneRequestService.editCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CraneRequestController.editCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request edit', async () => {
      const mockError = new Error('Service error');
      craneRequestService.editCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.editCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in crane request edit', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.editCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.editCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getCraneRequestList', () => {
    it('should get crane request list successfully with project data', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };
      const mockStatusData = { status: 'active' };

      mockReq.params.ProjectId = '123';

      craneRequestService.listCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);
      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await CraneRequestController.getCraneRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
        statusData: mockStatusData,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should get crane request list successfully without project data', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };

      mockReq.params.ProjectId = '';

      craneRequestService.listCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await CraneRequestController.getCraneRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(ProjectSettings.getCalendarStatusColor).not.toHaveBeenCalled();
      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
        statusData: undefined,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request list', async () => {
      const mockError = new Error('Service error');
      craneRequestService.listCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.getCraneRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from last crane request', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockError = new Error('Last request error');

      craneRequestService.listCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.getCraneRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in crane request list', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.listCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.getCraneRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getCraneRequestData', () => {
    it('should get crane request data successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      craneRequestService.getCraneRequestData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CraneRequestController.getCraneRequestData(mockReq, mockRes, mockNext);

      expect(craneRequestService.getCraneRequestData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request data', async () => {
      const mockError = new Error('Service error');
      craneRequestService.getCraneRequestData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.getCraneRequestData(mockReq, mockRes, mockNext);

      expect(craneRequestService.getCraneRequestData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in crane request data', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.getCraneRequestData.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.getCraneRequestData(mockReq, mockRes, mockNext);

      expect(craneRequestService.getCraneRequestData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateCraneRequestStatus', () => {
    it('should update crane request status successfully', async () => {
      const mockResponse = { id: 1, status: 'completed' };
      mockReq.body.status = 'completed';

      craneRequestService.updateCraneRequestStatus.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CraneRequestController.updateCraneRequestStatus(mockReq, mockRes, mockNext);

      expect(craneRequestService.updateCraneRequestStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'completed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request status update', async () => {
      const mockError = new Error('Service error');
      craneRequestService.updateCraneRequestStatus.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.updateCraneRequestStatus(mockReq, mockRes, mockNext);

      expect(craneRequestService.updateCraneRequestStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in crane request status update', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.updateCraneRequestStatus.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.updateCraneRequestStatus(mockReq, mockRes, mockNext);

      expect(craneRequestService.updateCraneRequestStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getLastCraneRequestId', () => {
    it('should get last crane request id successfully', async () => {
      const mockLastDetail = { id: 1 };
      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await CraneRequestController.getLastCraneRequestId(mockReq, mockRes, mockNext);

      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Last Id Viewed Successfully.',
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from last crane request id', async () => {
      const mockError = new Error('Service error');
      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.getLastCraneRequestId(mockReq, mockRes, mockNext);

      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in last crane request id', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.lastCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.getLastCraneRequestId(mockReq, mockRes, mockNext);

      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getSingleCraneRequest', () => {
    it('should get single crane request successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      craneRequestService.getSingleCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CraneRequestController.getSingleCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.getSingleCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from single crane request', async () => {
      const mockError = new Error('Service error');
      craneRequestService.getSingleCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CraneRequestController.getSingleCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.getSingleCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in single crane request', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.getSingleCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await CraneRequestController.getSingleCraneRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.getSingleCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('upcomingRequestListForMobile', () => {
    it('should get upcoming request list for mobile successfully', async () => {
      const mockResponse = { status: 200, data: [{ id: 1, data: 'test' }] };
      craneRequestService.upcomingRequestListForMobile.mockResolvedValue(mockResponse);

      await CraneRequestController.upcomingRequestListForMobile(mockReq, mockRes, mockNext);

      expect(craneRequestService.upcomingRequestListForMobile).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Upcoming Crane Booking Viewed Successfully.',
        data: mockResponse.data,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error status from upcoming request list for mobile', async () => {
      const mockResponse = { status: 422, msg: 'Error message' };
      craneRequestService.upcomingRequestListForMobile.mockResolvedValue(mockResponse);

      await CraneRequestController.upcomingRequestListForMobile(mockReq, mockRes, mockNext);

      expect(craneRequestService.upcomingRequestListForMobile).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: mockResponse.msg,
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in upcoming request list for mobile', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.upcomingRequestListForMobile.mockRejectedValue(mockError);

      await CraneRequestController.upcomingRequestListForMobile(mockReq, mockRes, mockNext);

      expect(craneRequestService.upcomingRequestListForMobile).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('upcomingRequestList', () => {
    it('should get upcoming request list successfully', async () => {
      const mockResponse = { status: 200, data: [{ id: 1, data: 'test' }] };
      craneRequestService.upcomingRequestList.mockResolvedValue(mockResponse);

      await CraneRequestController.upcomingRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.upcomingRequestList).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Upcoming Crane Booking Viewed Successfully.',
        data: mockResponse.data,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error status from upcoming request list', async () => {
      const mockResponse = { status: 422, msg: 'Error message' };
      craneRequestService.upcomingRequestList.mockResolvedValue(mockResponse);

      await CraneRequestController.upcomingRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.upcomingRequestList).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: mockResponse.msg,
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in upcoming request list', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.upcomingRequestList.mockRejectedValue(mockError);

      await CraneRequestController.upcomingRequestList(mockReq, mockRes, mockNext);

      expect(craneRequestService.upcomingRequestList).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editMultipleDeliveryRequest', () => {
    it('should edit multiple delivery request successfully', async () => {
      const mockResponse = { success: true, data: [{ id: 1, data: 'updated' }] };
      craneRequestService.editMultipleCraneRequest.mockResolvedValue(mockResponse);

      await CraneRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editMultipleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Updated Successfully.',
        data: mockResponse.data,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle unsuccessful edit multiple delivery request', async () => {
      const mockResponse = { success: false, message: 'Update failed' };
      craneRequestService.editMultipleCraneRequest.mockResolvedValue(mockResponse);

      await CraneRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editMultipleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({ message: mockResponse.message });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle server error in edit multiple delivery request', async () => {
      const mockResponse = { message: 'Server error' };
      craneRequestService.editMultipleCraneRequest.mockResolvedValue(mockResponse);

      await CraneRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editMultipleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: mockResponse.message });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in edit multiple delivery request', async () => {
      const mockError = new Error('Exception error');
      craneRequestService.editMultipleCraneRequest.mockRejectedValue(mockError);

      await CraneRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      expect(craneRequestService.editMultipleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
