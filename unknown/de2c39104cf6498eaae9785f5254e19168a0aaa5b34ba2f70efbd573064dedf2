const EquipmentController = require('../EquipmentController');
const { equipmentService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  equipmentService: {
    addEquipment: jest.fn(),
    listEquipment: jest.fn(),
    craneListEquipment: jest.fn(),
    getAllEquipmentType: jest.fn(),
    updateEquipment: jest.fn(),
    deleteEquipment: jest.fn(),
    getPresetEquipmentTypeList: jest.fn(),
    getMappedRequests: jest.fn(),
    deactivateEquipment: jest.fn(),
    lastEquipment: jest.fn(),
  },
}));

describe('EquipmentController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('addEquipment', () => {
    it('should add equipment successfully', async () => {
      const mockResponse = { id: 1, name: 'Test Equipment' };
      equipmentService.addEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.addEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.addEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from equipment addition', async () => {
      const mockError = new Error('Service error');
      equipmentService.addEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.addEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.addEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listEquipment', () => {
    it('should list equipment successfully', async () => {
      const mockResponse = [{ id: 1, name: 'Equipment 1' }];
      const mockLastDetail = { id: 1 };

      equipmentService.listEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      equipmentService.lastEquipment.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await EquipmentController.listEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.listEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(equipmentService.lastEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Listed successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from equipment listing', async () => {
      const mockError = new Error('Service error');
      equipmentService.listEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.listEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.listEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from last equipment', async () => {
      const mockResponse = [{ id: 1, name: 'Equipment 1' }];
      const mockError = new Error('Last equipment error');

      equipmentService.listEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      equipmentService.lastEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.listEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.listEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(equipmentService.lastEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('craneListEquipment', () => {
    it('should list crane equipment successfully', async () => {
      const mockResponse = [{ id: 1, name: 'Crane Equipment' }];
      equipmentService.craneListEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.craneListEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.craneListEquipment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Equipment Listed successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane equipment listing', async () => {
      const mockError = new Error('Service error');
      equipmentService.craneListEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.craneListEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.craneListEquipment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listEquipmentType', () => {
    it('should list equipment type successfully', async () => {
      const mockResponse = [{ id: 1, type: 'Type 1' }];
      equipmentService.getAllEquipmentType.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.listEquipmentType(mockReq, mockRes, mockNext);

      expect(equipmentService.getAllEquipmentType).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Type Listed successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from equipment type listing', async () => {
      const mockError = new Error('Service error');
      equipmentService.getAllEquipmentType.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.listEquipmentType(mockReq, mockRes, mockNext);

      expect(equipmentService.getAllEquipmentType).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('updateEquipment', () => {
    it('should update equipment successfully with default message', async () => {
      const mockResponse = { id: 1, name: 'Updated Equipment' };
      mockReq.body.isActive = false;

      equipmentService.updateEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.updateEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.updateEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Updated successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should update equipment successfully with activation message', async () => {
      const mockResponse = { id: 1, name: 'Updated Equipment' };
      mockReq.body.isActive = true;

      equipmentService.updateEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.updateEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.updateEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Activated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from equipment update', async () => {
      const mockError = new Error('Service error');
      equipmentService.updateEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.updateEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.updateEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteEquipment', () => {
    it('should delete equipment successfully', async () => {
      const mockResponse = { id: 1, deleted: true };
      equipmentService.deleteEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.deleteEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.deleteEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment deleted successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from equipment deletion', async () => {
      const mockError = new Error('Service error');
      equipmentService.deleteEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.deleteEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.deleteEquipment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getPresetEquipmentTypeList', () => {
    it('should get preset equipment type list successfully', async () => {
      const mockResponse = [{ id: 1, type: 'Preset Type' }];
      equipmentService.getPresetEquipmentTypeList.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.getPresetEquipmentTypeList(mockReq, mockRes, mockNext);

      expect(equipmentService.getPresetEquipmentTypeList).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Preset Equipment Type Listed successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from preset equipment type list', async () => {
      const mockError = new Error('Service error');
      equipmentService.getPresetEquipmentTypeList.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.getPresetEquipmentTypeList(mockReq, mockRes, mockNext);

      expect(equipmentService.getPresetEquipmentTypeList).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getMappedRequests', () => {
    it('should get mapped requests successfully', async () => {
      const mockResponse = { data: 'mapped requests' };
      equipmentService.getMappedRequests.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.getMappedRequests(mockReq, mockRes, mockNext);

      expect(equipmentService.getMappedRequests).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Mapped Bookings Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from mapped requests', async () => {
      const mockError = new Error('Service error');
      equipmentService.getMappedRequests.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.getMappedRequests(mockReq, mockRes, mockNext);

      expect(equipmentService.getMappedRequests).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in mapped requests', async () => {
      const mockError = new Error('Exception error');
      equipmentService.getMappedRequests.mockRejectedValue(mockError);

      await EquipmentController.getMappedRequests(mockReq, mockRes, mockNext);

      expect(equipmentService.getMappedRequests).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('deactivateEquipment', () => {
    it('should deactivate equipment successfully', async () => {
      const mockResponse = { id: 1, deactivated: true };
      equipmentService.deactivateEquipment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await EquipmentController.deactivateEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.deactivateEquipment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment deactivated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from equipment deactivation', async () => {
      const mockError = new Error('Service error');
      equipmentService.deactivateEquipment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await EquipmentController.deactivateEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.deactivateEquipment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in equipment deactivation', async () => {
      const mockError = new Error('Exception error');
      equipmentService.deactivateEquipment.mockRejectedValue(mockError);

      await EquipmentController.deactivateEquipment(mockReq, mockRes, mockNext);

      expect(equipmentService.deactivateEquipment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
