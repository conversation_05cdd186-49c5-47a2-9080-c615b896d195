const axios = require('axios');
const status = require('http-status');
const CarbonEmissionController = require('../carbonEmissionController');
const carbonEmissionService = require('../../services/carbonEmissionService');

// Mock dependencies
jest.mock('../../services/carbonEmissionService', () => ({
  fileUpload: jest.fn(),
  getEmissionValues: jest.fn(),
  calculateCO2Emissions: jest.fn(),
  getDashboardData: jest.fn(),
}));

jest.mock('axios');

describe('CarbonEmissionController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {
        zipcode: '12345',
      },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
    process.env.ML_API_URL = 'http://ml-api.example.com';
  });

  describe('fileUpload', () => {
    it('should upload file and process successfully', async () => {
      const mockUploadResponse = {
        S3URI: 's3://bucket/file.pdf',
        Location: 'https://s3.amazonaws.com/bucket/file.pdf',
      };
      const mockEmissionValues = [
        { utilityType: 'Electricity', co2PerUnit: 0.5 },
        { utilityType: 'Gas', co2PerUnit: 0.3 },
      ];
      const mockMLResponse = {
        data: {
          'Utility Invoice': {
            Items: [
              { item_name: 'Electricity', Quantity: 100 },
              { item_name: 'Gas', Quantity: 50 },
            ],
          },
        },
      };
      const mockCO2Emissions = {
        totalCO2Emission: 50,
      };

      carbonEmissionService.fileUpload.mockImplementation((req, callback) => {
        callback(mockUploadResponse, null);
      });
      carbonEmissionService.getEmissionValues.mockResolvedValue(mockEmissionValues);
      carbonEmissionService.calculateCO2Emissions.mockResolvedValue(mockCO2Emissions);
      axios.post.mockResolvedValue(mockMLResponse);

      await CarbonEmissionController.fileUpload(mockReq, mockRes, mockNext);

      expect(carbonEmissionService.fileUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(carbonEmissionService.getEmissionValues).toHaveBeenCalled();
      expect(axios.post).toHaveBeenCalledWith(
        'http://ml-api.example.com',
        { s3_url: mockUploadResponse.S3URI },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      expect(carbonEmissionService.calculateCO2Emissions).toHaveBeenCalledWith('12345', 100);
      expect(mockRes.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          'Utility Invoice': {
            Items: [
              { item_name: 'Electricity', Quantity: 100, 'CO2 Emissions': 50 },
              { item_name: 'Gas', Quantity: 50, 'CO2 Emissions': 15 },
            ],
          },
        }),
        invoice_URL: mockUploadResponse.Location,
        co2Emissions: mockCO2Emissions,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle file upload service error', async () => {
      const mockError = new Error('Upload failed');
      carbonEmissionService.fileUpload.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CarbonEmissionController.fileUpload(mockReq, mockRes, mockNext);

      expect(carbonEmissionService.fileUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle ML API error', async () => {
      const mockUploadResponse = {
        S3URI: 's3://bucket/file.pdf',
        Location: 'https://s3.amazonaws.com/bucket/file.pdf',
      };
      const mockMLError = new Error('ML API error');

      carbonEmissionService.fileUpload.mockImplementation((req, callback) => {
        callback(mockUploadResponse, null);
      });
      axios.post.mockRejectedValue(mockMLError);

      await CarbonEmissionController.fileUpload(mockReq, mockRes, mockNext);

      expect(carbonEmissionService.fileUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockMLError);
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle general error', async () => {
      const mockError = new Error('General error');
      carbonEmissionService.fileUpload.mockImplementation(() => {
        throw mockError;
      });

      await CarbonEmissionController.fileUpload(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle missing electricity item', async () => {
      const mockUploadResponse = {
        S3URI: 's3://bucket/file.pdf',
        Location: 'https://s3.amazonaws.com/bucket/file.pdf',
      };
      const mockEmissionValues = [{ utilityType: 'Gas', co2PerUnit: 0.3 }];
      const mockMLResponse = {
        data: {
          'Utility Invoice': {
            Items: [{ item_name: 'Gas', Quantity: 50 }],
          },
        },
      };

      carbonEmissionService.fileUpload.mockImplementation((req, callback) => {
        callback(mockUploadResponse, null);
      });
      carbonEmissionService.getEmissionValues.mockResolvedValue(mockEmissionValues);
      axios.post.mockResolvedValue(mockMLResponse);

      await CarbonEmissionController.fileUpload(mockReq, mockRes, mockNext);

      expect(carbonEmissionService.fileUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(carbonEmissionService.getEmissionValues).toHaveBeenCalled();
      expect(axios.post).toHaveBeenCalled();
      expect(mockRes.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          'Utility Invoice': {
            Items: [{ item_name: 'Gas', Quantity: 50, 'CO2 Emissions': 15 }],
          },
        }),
        invoice_URL: mockUploadResponse.Location,
        co2Emissions: undefined,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('dashboardData', () => {
    it('should get dashboard data successfully', async () => {
      const mockResponse = {
        totalEmissions: 100,
        monthlyData: [
          { month: 'Jan', emissions: 25 },
          { month: 'Feb', emissions: 30 },
        ],
      };

      carbonEmissionService.getDashboardData.mockImplementation((params, callback) => {
        callback(mockResponse, null);
      });

      await CarbonEmissionController.dashboardData(mockReq, mockRes, mockNext);

      expect(carbonEmissionService.getDashboardData).toHaveBeenCalledWith(
        mockReq.params,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard data Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service error', async () => {
      const mockError = new Error('Service error');
      carbonEmissionService.getDashboardData.mockImplementation((params, callback) => {
        callback(null, mockError);
      });

      await CarbonEmissionController.dashboardData(mockReq, mockRes, mockNext);

      expect(carbonEmissionService.getDashboardData).toHaveBeenCalledWith(
        mockReq.params,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle general error', async () => {
      const mockError = new Error('General error');
      carbonEmissionService.getDashboardData.mockImplementation(() => {
        throw mockError;
      });

      await CarbonEmissionController.dashboardData(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
