const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  concreteRequestHistoryController: {
    getConcreteRequestHistories: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  concreteRequestHistoryValidation: {
    getConcreteRequestHistories: jest.fn(),
  },
}));

describe('concreteRequestHistoryRoute', () => {
  let router;
  let concreteRequestHistoryRoute;
  let concreteRequestHistoryController;
  let passportConfig;
  let concreteRequestHistoryValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    concreteRequestHistoryRoute = require('../concreteRequestHistoryRoute');
    const controllers = require('../../controllers');
    concreteRequestHistoryController = controllers.concreteRequestHistoryController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    concreteRequestHistoryValidation = validations.concreteRequestHistoryValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = concreteRequestHistoryRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_concrete_request_histories/:ConcreteRequestId/?:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        concreteRequestHistoryController.getConcreteRequestHistories,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(1);
      expect(validate).toHaveBeenCalledWith(
        concreteRequestHistoryValidation.getConcreteRequestHistories,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = concreteRequestHistoryRoute.router;
      const result2 = concreteRequestHistoryRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication and validation middleware for all routes', () => {
      concreteRequestHistoryRoute.router;

      const getCalls = router.get.mock.calls;

      // All routes should have validation and authentication
      getCalls.forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toHaveLength(4); // path + validation + auth + controller
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof concreteRequestHistoryRoute).toBe('object');
      expect(concreteRequestHistoryRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestHistoryRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestHistoryRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
