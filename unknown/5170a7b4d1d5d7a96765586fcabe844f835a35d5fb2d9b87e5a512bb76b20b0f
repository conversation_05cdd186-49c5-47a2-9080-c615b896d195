const deepLinkService = require('../deepLinkService');

// Mock axios
jest.mock('axios', () => ({
  request: jest.fn(),
}));

// Mock Cryptr
jest.mock('cryptr', () => {
  return jest.fn().mockImplementation(() => ({
    encrypt: jest.fn(),
  }));
});

const axios = require('axios');
const Cryptr = require('cryptr');

describe('DeepLinkService', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup environment variables
    process.env.ADMIN_URL = 'https://admin.example.com';
    process.env.BASE_URL = 'https://app.example.com';
    process.env.DEEPLINK_REQUEST_URL = 'https://api.branch.io/v1/url';
    process.env.DEEPLINK_BRANCH_KEY = 'test_branch_key';

    // Mock axios response
    axios.request.mockResolvedValue({
      data: {
        url: 'https://example.app.link/test-deeplink'
      }
    });

    // Mock Cryptr encrypt method
    const mockCryptr = Cryptr.mock.instances[0] || { encrypt: jest.fn() };
    if (mockCryptr.encrypt) {
      mockCryptr.encrypt.mockImplementation((text) => `encrypted_${text}`);
    }
  });

  describe('getForgotPasswordDeepLink', () => {
    it('should create deeplink for super admin user', async () => {
      const user = {
        userType: 'super admin',
        resetPasswordToken: 'test-token-123',
        email: '<EMAIL>'
      };

      const result = await deepLinkService.getForgotPasswordDeepLink(user);

      expect(axios.request).toHaveBeenCalledWith({
        url: process.env.DEEPLINK_REQUEST_URL,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          branch_key: process.env.DEEPLINK_BRANCH_KEY,
          data: {
            type: 'reset_password',
            resetPasswordToken: 'test-token-123',
            $desktop_url: `${process.env.ADMIN_URL}/reset-password/test-token-123`,
          },
        },
      });

      expect(result.link).toBe('https://example.app.link/test-deeplink');
      expect(result.userType).toBe('super admin');
    });

    it('should create deeplink for regular user', async () => {
      const user = {
        userType: 'regular',
        resetPasswordToken: 'test-token-456',
        email: '<EMAIL>'
      };

      const result = await deepLinkService.getForgotPasswordDeepLink(user);

      expect(axios.request).toHaveBeenCalledWith({
        url: process.env.DEEPLINK_REQUEST_URL,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          branch_key: process.env.DEEPLINK_BRANCH_KEY,
          data: {
            type: 'reset_password',
            resetPasswordToken: 'test-token-456',
            $desktop_url: `${process.env.BASE_URL}/reset-password/test-token-456`,
          },
        },
      });

      expect(result.link).toBe('https://example.app.link/test-deeplink');
      expect(result.userType).toBe('regular');
    });
  });

  describe('getInviteMemberDeepLink', () => {
    beforeEach(() => {
      // Reset Cryptr mock for each test
      Cryptr.mockClear();
      const mockCryptrInstance = {
        encrypt: jest.fn().mockImplementation((text) => `encrypted_${text}`)
      };
      Cryptr.mockImplementation(() => mockCryptrInstance);
    });

    it('should create deeplink for member with domainName', async () => {
      const userData = {
        id: 123,
        email: '<EMAIL>',
        domainName: 'testdomain',
        ParentCompanyId: 456,
        link: 'https://app.example.com'
      };

      const result = await deepLinkService.getInviteMemberDeepLink(userData);

      expect(Cryptr).toHaveBeenCalledWith('a0b1c2d3e4f5g6h7i8j9k10');

      const cryptrInstance = Cryptr.mock.results[0].value;
      expect(cryptrInstance.encrypt).toHaveBeenCalledWith('<EMAIL>');
      expect(cryptrInstance.encrypt).toHaveBeenCalledWith('testdomain');

      expect(axios.request).toHaveBeenCalledWith({
        url: process.env.DEEPLINK_REQUEST_URL,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          branch_key: process.env.DEEPLINK_BRANCH_KEY,
          data: {
            memberId: 123,
            ParentCompanyId: 456,
            domainName: 'testdomain',
            email: '<EMAIL>',
            type: 'invite_member',
            $desktop_url: 'https://app.example.com/invite-member/123/456/<EMAIL>/encrypted_testdomain',
          },
        },
      });

      expect(result.link).toBe('https://example.app.link/test-deeplink');
    });

    it('should create deeplink for member without domainName (public)', async () => {
      const userData = {
        id: 789,
        email: '<EMAIL>',
        ParentCompanyId: 101,
        link: 'https://app.example.com'
      };

      const result = await deepLinkService.getInviteMemberDeepLink(userData);

      const cryptrInstance = Cryptr.mock.results[0].value;
      expect(cryptrInstance.encrypt).toHaveBeenCalledWith('<EMAIL>');
      expect(cryptrInstance.encrypt).toHaveBeenCalledWith('public');

      expect(axios.request).toHaveBeenCalledWith({
        url: process.env.DEEPLINK_REQUEST_URL,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          branch_key: process.env.DEEPLINK_BRANCH_KEY,
          data: {
            memberId: 789,
            ParentCompanyId: 101,
            domainName: 'public',
            email: '<EMAIL>',
            type: 'invite_member',
            $desktop_url: 'https://app.example.com/invite-member/789/101/<EMAIL>/encrypted_public',
          },
        },
      });

      expect(result.link).toBe('https://example.app.link/test-deeplink');
    });
  });

  describe('getRegistrationDeepLink', () => {
    it('should create deeplink for user registration', async () => {
      const user = {
        email: '<EMAIL>',
        generatedPassword: 'temp123',
        firstName: 'John',
        lastName: 'Doe'
      };

      const result = await deepLinkService.getRegistrationDeepLink(user);

      expect(axios.request).toHaveBeenCalledWith({
        url: process.env.DEEPLINK_REQUEST_URL,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          branch_key: process.env.DEEPLINK_BRANCH_KEY,
          data: {
            type: 'register',
            email: '<EMAIL>',
            password: 'temp123',
            $desktop_url: `${process.env.BASE_URL}/login`,
          },
        },
      });

      expect(result.link).toBe('https://example.app.link/test-deeplink');
      expect(result.email).toBe('<EMAIL>');
      expect(result.generatedPassword).toBe('temp123');
    });
  });

  describe('getGuestUserDeepLink', () => {
    it('should create deeplink for guest user', async () => {
      const uniqueString = 'unique-project-123';

      const result = await deepLinkService.getGuestUserDeepLink(uniqueString);

      expect(axios.request).toHaveBeenCalledWith({
        url: process.env.DEEPLINK_REQUEST_URL,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          branch_key: process.env.DEEPLINK_BRANCH_KEY,
          data: {
            type: 'guestUser',
            $fallback_url: `${process.env.BASE_URL}/project-detail/unique-project-123`,
            $web_only: true,
          },
        },
      });

      expect(result.link).toBe('https://example.app.link/test-deeplink');
    });
  });

  describe('Error Handling', () => {
    it('should handle axios request errors', async () => {
      const axiosError = new Error('Network error');
      axios.request.mockRejectedValueOnce(axiosError);

      const user = {
        userType: 'regular',
        resetPasswordToken: 'test-token',
        email: '<EMAIL>'
      };

      await expect(deepLinkService.getForgotPasswordDeepLink(user))
        .rejects.toThrow('Network error');
    });
  });
});
