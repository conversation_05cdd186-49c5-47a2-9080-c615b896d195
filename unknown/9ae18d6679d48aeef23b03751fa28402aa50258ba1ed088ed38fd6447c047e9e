// Mock concreteRequestCommentService
jest.mock('../../services', () => ({
  concreteRequestCommentService: {
    createConcreteRequestComment: jest.fn(),
    getConcreteRequestComments2: jest.fn(),
  },
}));

const concreteRequestCommentController = require('../concreteRequestCommentController');
const { concreteRequestCommentService } = require('../../services');

describe('concreteRequestCommentController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createConcreteRequestComment', () => {
    it('should create concrete request comment successfully', async () => {
      const mockResponse = { id: 1, content: 'Test comment', userId: 123 };

      concreteRequestCommentService.createConcreteRequestComment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestCommentController.createConcreteRequestComment(mockReq, mockRes, mockNext);

      expect(concreteRequestCommentService.createConcreteRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create concrete request comment', async () => {
      const mockError = new Error('Service error');
      concreteRequestCommentService.createConcreteRequestComment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestCommentController.createConcreteRequestComment(mockReq, mockRes, mockNext);

      expect(concreteRequestCommentService.createConcreteRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create concrete request comment', async () => {
      const mockError = new Error('Exception error');
      concreteRequestCommentService.createConcreteRequestComment.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestCommentController.createConcreteRequestComment(mockReq, mockRes, mockNext);

      expect(concreteRequestCommentService.createConcreteRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getConcreteRequestComments1', () => {
    it('should get concrete request comments successfully', async () => {
      const mockResponse = {
        commentList: [
          { id: 1, content: 'Comment 1' },
          { id: 2, content: 'Comment 2' },
        ],
        exist: { id: 1, requestData: 'test' },
      };

      concreteRequestCommentService.getConcreteRequestComments2.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestCommentController.getConcreteRequestComments1(mockReq, mockRes, mockNext);

      expect(concreteRequestCommentService.getConcreteRequestComments2).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Comment Viewed Successfully.',
        data: mockResponse.commentList,
        concreteRequest: mockResponse.exist,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get concrete request comments', async () => {
      const mockError = new Error('Service error');
      concreteRequestCommentService.getConcreteRequestComments2.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestCommentController.getConcreteRequestComments1(mockReq, mockRes, mockNext);

      expect(concreteRequestCommentService.getConcreteRequestComments2).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get concrete request comments', async () => {
      const mockError = new Error('Exception error');
      concreteRequestCommentService.getConcreteRequestComments2.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestCommentController.getConcreteRequestComments1(mockReq, mockRes, mockNext);

      expect(concreteRequestCommentService.getConcreteRequestComments2).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
