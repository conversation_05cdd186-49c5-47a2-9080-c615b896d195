const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  HaulingController: {
    // Add actual methods from haulingRoute.js
  },
}));

jest.mock('../../controllers/haulingLogController', () => ({
  addHaulingLog: jest.fn(),
  listHaulingLog: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
  isProjectAdminOnly: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  haulingValidation: {
    // Add actual validation objects from haulingRoute.js
  },
}));

describe('haulingRoute', () => {
  let router;
  let haulingRoute;
  let HaulingController;
  let passportConfig;
  let haulingValidation;
  let validate;
  let haulingLogController;
  let checkAdmin;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    haulingRoute = require('../haulingRoute');
    const controllers = require('../../controllers');
    HaulingController = controllers.HaulingController;
    haulingLogController = require('../../controllers/haulingLogController');
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    haulingValidation = validations.haulingValidation;
    validate = require('express-validation').validate;
    checkAdmin = require('../../middlewares/checkAdmin');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = haulingRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify POST route
      expect(router.post).toHaveBeenCalledWith(
        '/add_haulinglog',
        passportConfig.isAuthenticated,
        haulingLogController.addHaulingLog,
      );

      // Verify GET route
      expect(router.get).toHaveBeenCalledWith(
        '/haulinglog_list/:pageSize/:pageNo',
        passportConfig.isAuthenticated,
        haulingLogController.listHaulingLog,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = haulingRoute.router;
      const result2 = haulingRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      haulingRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];

      allCalls.forEach((call) => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof haulingRoute).toBe('object');
      expect(haulingRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(haulingRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(haulingRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
