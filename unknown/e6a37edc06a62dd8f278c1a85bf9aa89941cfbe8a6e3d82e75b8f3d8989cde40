// Mock Sequelize first to avoid constructor issues
jest.mock('sequelize', () => {
  const mockSequelize = {
    authenticate: jest.fn(),
    close: jest.fn(),
  };
  return jest.fn(() => mockSequelize);
});

// Mock models
jest.mock('../../models', () => ({
  sequelize: {
    authenticate: jest.fn(),
    close: jest.fn(),
  },
}));

// Mock UtilitiesService
jest.mock('../../services/UtilitiesService', () => ({
  addUtilities: jest.fn(),
  listUtilities: jest.fn(),
}));

const UtilitiesController = require('../UtilitiesController');
const UtilitiesService = require('../../services/UtilitiesService');

describe('UtilitiesController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('addUtilities', () => {
    it('should add utilities successfully', async () => {
      const mockUtilities = {
        id: 1,
        name: 'Test Utility',
        type: 'electricity',
        cost: 100,
      };

      UtilitiesService.addUtilities.mockImplementation((req, callback) => {
        callback(mockUtilities, null);
      });

      await UtilitiesController.addUtilities(mockReq, mockRes, mockNext);

      expect(UtilitiesService.addUtilities).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Utilities added successfully.',
        data: mockUtilities,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from add utilities', async () => {
      const mockError = new Error('Service error');
      UtilitiesService.addUtilities.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await UtilitiesController.addUtilities(mockReq, mockRes, mockNext);

      expect(UtilitiesService.addUtilities).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });


  });

  describe('listUtilities', () => {
    it('should list utilities successfully', async () => {
      const mockUtilitiesDetail = [
        {
          id: 1,
          name: 'Electricity',
          type: 'electricity',
          cost: 100,
        },
        {
          id: 2,
          name: 'Water',
          type: 'water',
          cost: 50,
        },
      ];

      UtilitiesService.listUtilities.mockImplementation((req, callback) => {
        callback(mockUtilitiesDetail, null);
      });

      await UtilitiesController.listUtilities(mockReq, mockRes, mockNext);

      expect(UtilitiesService.listUtilities).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Utilities Listed successfully.',
        data: mockUtilitiesDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list utilities', async () => {
      const mockError = new Error('Service error');
      UtilitiesService.listUtilities.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await UtilitiesController.listUtilities(mockReq, mockRes, mockNext);

      expect(UtilitiesService.listUtilities).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });


  });
});


