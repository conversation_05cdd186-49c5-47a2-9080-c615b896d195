const moment = require('moment');
const { Op } = require('sequelize');
const concreteReportService = require('../concreteReportService');
const helper = require('../../helpers/domainHelper');

// Mock all dependencies
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            and: Symbol('and'),
            or: Symbol('or'),
            in: Symbol('in'),
            notIn: Symbol('notIn'),
            between: Symbol('between'),
            gte: Symbol('gte'),
            iLike: Symbol('iLike'),
            ne: Symbol('ne')
        }
    },
    Enterprise: {
        findOne: jest.fn()
    },
    Member: {
        findOne: jest.fn(),
        findAll: jest.fn()
    },
    User: {
        findOne: jest.fn()
    },
    VoidList: {
        findAll: jest.fn()
    },
    ConcreteRequest: {
        findAll: jest.fn()
    }
}));

jest.mock('../exportService', () => ({
    createWorkbook: jest.fn()
}));

jest.mock('../pdfConcreteReportService', () => ({
    pdfFormatOfConcreteRequest: jest.fn()
}));

jest.mock('../csvConcreteReportService', () => ({
    exportConcreteReportInCsvFormat: jest.fn()
}));

jest.mock('../excelConcreteReportService', () => ({
    concreteReport: jest.fn()
}));

jest.mock('../deliveryreportService', () => ({
    saveExcelReport: jest.fn(),
    createSavedReports: jest.fn()
}));

jest.mock('../../helpers/domainHelper', () => ({
    getDynamicModel: jest.fn(),
    returnProjectModel: jest.fn()
}));

describe('concreteReportService', () => {
    let mockModels;

    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();

        // Setup default mock implementations
        mockModels = require('../../models');
        mockModels.Enterprise.findOne.mockResolvedValue({ name: 'test-enterprise' });
        mockModels.Member.findOne.mockResolvedValue({
            id: 1,
            UserId: 1,
            isAccount: true,
            EnterpriseId: 1
        });
        mockModels.User.findOne.mockResolvedValue({
            id: 1,
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User'
        });
        mockModels.VoidList.findAll.mockResolvedValue([]);
        mockModels.ConcreteRequest.findAll.mockResolvedValue([]);

        helper.getDynamicModel.mockResolvedValue({
            Member: mockModels.Member,
            User: mockModels.User,
            VoidList: mockModels.VoidList
        });

        helper.returnProjectModel.mockResolvedValue({
            Member: mockModels.Member,
            User: mockModels.User
        });
    });

    describe('getDomainFromUserData', () => {
        it('should return null when email is not provided', async () => {
            const result = await concreteReportService.getDomainFromUserData();
            expect(result).toBeNull();
        });

        it('should return user data when email is provided', async () => {
            const email = '<EMAIL>';
            const result = await concreteReportService.getDomainFromUserData(email);
            expect(mockModels.User.findOne).toHaveBeenCalledWith({ where: { email } });
            expect(result).toEqual(expect.objectContaining({ email }));
        });

        it('should handle database errors', async () => {
            mockModels.User.findOne.mockRejectedValue(new Error('DB Error'));
            await expect(concreteReportService.getDomainFromUserData('<EMAIL>'))
                .rejects.toThrow('DB Error');
        });
    });

    describe('getDomainFromMemberData', () => {
        it('should return enterprise name when userData is not provided', async () => {
            const result = await concreteReportService.getDomainFromMemberData(null, 1);
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' }
            });
            expect(result).toBe('test-enterprise');
        });

        it('should return enterprise name when member is not an account', async () => {
            mockModels.Member.findOne.mockResolvedValue({ isAccount: false });
            const result = await concreteReportService.getDomainFromMemberData({ id: 1 }, 1);
            expect(result).toBe('test-enterprise');
        });

        it('should return enterprise name by ID when member is an account', async () => {
            const result = await concreteReportService.getDomainFromMemberData({ id: 1 }, 1);
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: 1, status: 'completed' }
            });
            expect(result).toBe('test-enterprise');
        });
    });

    describe('resolveDomainName', () => {
        it('should verify domain name when provided', async () => {
            const result = await concreteReportService.resolveDomainName('test-domain', null, null);
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'test-domain' }
            });
            expect(result).toBe('test-domain');
        });

        it('should return empty string when domain name is invalid', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue(null);
            const result = await concreteReportService.resolveDomainName('invalid-domain', null, null);
            expect(result).toBe('');
        });

        it('should get domain from user data when ParentCompanyId is provided', async () => {
            const result = await concreteReportService.resolveDomainName(null, 1, '<EMAIL>');
            expect(result).toBe('test-enterprise');
        });
    });

    describe('buildConcreteConditions', () => {
        it('should build basic concrete conditions', async () => {
            const params = { ProjectId: 1 };
            const incomeData = { statusFilter: [], locationFilter: [] };
            const voidConcreteDelivery = [];

            const result = await concreteReportService.buildConcreteConditions(
                params,
                incomeData,
                voidConcreteDelivery
            );

            expect(result).toEqual({
                ProjectId: 1,
                isDeleted: false,
                '$ConcreteRequest.id$': {
                    [Op.and]: [{
                        [Op.notIn]: []
                    }]
                }
            });
        });

        it('should add status filter when provided', async () => {
            const params = { ProjectId: 1 };
            const incomeData = {
                statusFilter: ['pending', 'approved'],
                locationFilter: []
            };
            const voidConcreteDelivery = [];

            const result = await concreteReportService.buildConcreteConditions(
                params,
                incomeData,
                voidConcreteDelivery
            );

            expect(result.status).toEqual({ [Op.in]: ['pending', 'approved'] });
        });
    });

    describe('validateMemberAccess', () => {
        it('should return member details when access is valid', async () => {
            const loginUser = { id: 1 };
            const ProjectId = 1;

            const result = await concreteReportService.validateMemberAccess(loginUser, ProjectId);

            expect(mockModels.Member.findOne).toHaveBeenCalledWith({
                where: expect.objectContaining({
                    UserId: 1,
                    ProjectId: 1,
                    isDeleted: false,
                    isActive: true
                })
            });
            expect(result).toEqual(expect.objectContaining({ id: 1 }));
        });

        it('should throw error when member access is invalid', async () => {
            mockModels.Member.findOne.mockResolvedValue(null);

            await expect(concreteReportService.validateMemberAccess({ id: 1 }, 1))
                .rejects.toThrow('Project Id/Member does not exist');
        });
    });

    describe('listConcreteRequest', () => {
        const mockInputData = {
            user: { id: 1, email: '<EMAIL>' },
            params: { ProjectId: 1, void: 0 },
            body: {},
            headers: { timezoneoffset: '-240' }
        };

        it('should handle invalid void parameter', async () => {
            const done = jest.fn();
            await concreteReportService.listConcreteRequest(
                { ...mockInputData, params: { ...mockInputData.params, void: 2 } },
                done
            );
            expect(done).toHaveBeenCalledWith(
                { message: 'Please enter void as 1 or 0' },
                false
            );
        });

        it('should process concrete request successfully', async () => {
            const done = jest.fn();
            mockModels.ConcreteRequest.findAll.mockResolvedValue([
                { id: 1, description: 'Test Request' }
            ]);

            await concreteReportService.listConcreteRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 1,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle export requests', async () => {
            const done = jest.fn();
            const exportInputData = {
                ...mockInputData,
                body: { exportType: 'PDF', saved: true }
            };

            mockModels.ConcreteRequest.findAll.mockResolvedValue([
                { id: 1, description: 'Test Request' }
            ]);

            await concreteReportService.listConcreteRequest(exportInputData, done);

            // Verify export handling was triggered
            expect(done).toHaveBeenCalled();
        });
    });

    describe('handleExportResponse', () => {
        const mockReq = {
            body: { exportType: 'PDF', saved: true },
            params: { ProjectId: 1 }
        };
        const mockResponse = {
            count: 1,
            rows: [{ id: 1, description: 'Test Request' }]
        };

        it('should handle PDF export', async () => {
            const done = jest.fn();
            await concreteReportService.handleExportResponse(mockReq, mockResponse, done);
            expect(done).toHaveBeenCalled();
        });

        it('should handle Excel export', async () => {
            const done = jest.fn();
            const excelReq = { ...mockReq, body: { ...mockReq.body, exportType: 'EXCEL' } };
            await concreteReportService.handleExportResponse(excelReq, mockResponse, done);
            expect(done).toHaveBeenCalled();
        });

        it('should handle CSV export', async () => {
            const done = jest.fn();
            const csvReq = { ...mockReq, body: { ...mockReq.body, exportType: 'CSV' } };
            await concreteReportService.handleExportResponse(csvReq, mockResponse, done);
            expect(done).toHaveBeenCalled();
        });

        it('should handle no data found', async () => {
            const done = jest.fn();
            await concreteReportService.handleExportResponse(
                mockReq,
                { count: 0, rows: [] },
                done
            );
            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle unsupported export type', async () => {
            const done = jest.fn();
            const invalidReq = { ...mockReq, body: { ...mockReq.body, exportType: 'INVALID' } };
            await concreteReportService.handleExportResponse(invalidReq, mockResponse, done);
            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('buildSearchConditions', () => {
        it('should return empty object when search is not provided', async () => {
            const result = await concreteReportService.buildSearchConditions();
            expect(result).toEqual({});
        });

        it('should build search conditions with provided search term', async () => {
            const result = await concreteReportService.buildSearchConditions('test');
            expect(result).toEqual({
                [Op.and]: [{
                    [Op.or]: expect.arrayContaining([
                        expect.objectContaining({
                            description: { [Op.iLike]: '%test%' }
                        })
                    ])
                }]
            });
        });
    });

    describe('buildDateConditions', () => {
        it('should build conditions for current date when startdate is not provided', async () => {
            const result = await concreteReportService.buildDateConditions(null, null, '-240');
            expect(result).toEqual({
                concretePlacementStart: {
                    [Op.gte]: expect.any(Object)
                }
            });
        });

        it('should build conditions for date range when both dates are provided', async () => {
            const startdate = '2024-01-01';
            const enddate = '2024-01-31';
            const result = await concreteReportService.buildDateConditions(
                startdate,
                enddate,
                '-240'
            );
            expect(result).toEqual({
                concretePlacementStart: {
                    [Op.between]: expect.arrayContaining([
                        expect.any(Object),
                        expect.any(Object)
                    ])
                }
            });
        });
    });

    describe('buildOrderQuery', () => {
        it('should return correct order for member field', async () => {
            const result = await concreteReportService.buildOrderQuery('member', 'ASC');
            expect(result).toEqual([
                ['memberDetails', 'Member', 'User', 'firstName', 'ASC']
            ]);
        });

        it('should return correct order for direct field', async () => {
            const result = await concreteReportService.buildOrderQuery('description', 'DESC');
            expect(result).toEqual([['description', 'DESC']]);
        });

        it('should return null for invalid field', async () => {
            const result = await concreteReportService.buildOrderQuery('invalidField', 'ASC');
            expect(result).toBeNull();
        });

        it('should return correct order for company field', async () => {
            const result = await concreteReportService.buildOrderQuery('company', 'ASC');
            expect(result).toEqual([
                ['concreteSupplierDetails', 'Company', 'companyName', 'ASC']
            ]);
        });

        it('should return correct order for approvedUser field', async () => {
            const result = await concreteReportService.buildOrderQuery('approvedUser', 'DESC');
            expect(result).toEqual([
                ['approverDetails', 'User', 'firstName', 'DESC']
            ]);
        });

        it('should return correct order for pumpsize field', async () => {
            const result = await concreteReportService.buildOrderQuery('pumpsize', 'ASC');
            expect(result).toEqual([
                ['pumpSizeDetails', 'ConcretePumpSize', 'pumpSize', 'ASC']
            ]);
        });

        it('should return correct order for mixDesign field', async () => {
            const result = await concreteReportService.buildOrderQuery('mixDesign', 'DESC');
            expect(result).toEqual([
                ['mixDesignDetails', 'ConcreteMixDesign', 'mixDesign', 'DESC']
            ]);
        });

        it('should handle all direct fields', async () => {
            const directFields = [
                'description', 'id', 'status', 'slump', 'concreteOrderNumber',
                'truckSpacingHours', 'primerForPump', 'concreteQuantityOrdered',
                'concretePlacementStart'
            ];

            for (const field of directFields) {
                const result = await concreteReportService.buildOrderQuery(field, 'ASC');
                expect(result).toEqual([[field, 'ASC']]);
            }
        });
    });

    describe('getEnterpriseName', () => {
        it('should return enterprise name when found', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue({ name: 'TEST-ENTERPRISE' });
            const result = await concreteReportService.getEnterpriseName(1);
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' }
            });
            expect(result).toBe('test-enterprise');
        });

        it('should return empty string when enterprise not found', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue(null);
            const result = await concreteReportService.getEnterpriseName(1);
            expect(result).toBe('');
        });

        it('should handle database errors', async () => {
            mockModels.Enterprise.findOne.mockRejectedValue(new Error('DB Error'));
            await expect(concreteReportService.getEnterpriseName(1))
                .rejects.toThrow('DB Error');
        });
    });

    describe('getEnterpriseNameById', () => {
        it('should return enterprise name when found', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue({ name: 'TEST-ENTERPRISE' });
            const result = await concreteReportService.getEnterpriseNameById(1);
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: 1, status: 'completed' }
            });
            expect(result).toBe('test-enterprise');
        });

        it('should return empty string when enterprise not found', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue(null);
            const result = await concreteReportService.getEnterpriseNameById(1);
            expect(result).toBe('');
        });

        it('should handle database errors', async () => {
            mockModels.Enterprise.findOne.mockRejectedValue(new Error('DB Error'));
            await expect(concreteReportService.getEnterpriseNameById(1))
                .rejects.toThrow('DB Error');
        });
    });

    describe('verifyDomainName', () => {
        it('should return domain name when enterprise exists', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue({ name: 'test-domain' });
            const result = await concreteReportService.verifyDomainName('Test-Domain');
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'test-domain' }
            });
            expect(result).toBe('Test-Domain');
        });

        it('should return empty string when enterprise does not exist', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue(null);
            const result = await concreteReportService.verifyDomainName('invalid-domain');
            expect(result).toBe('');
        });

        it('should handle database errors', async () => {
            mockModels.Enterprise.findOne.mockRejectedValue(new Error('DB Error'));
            await expect(concreteReportService.verifyDomainName('test-domain'))
                .rejects.toThrow('DB Error');
        });
    });

    describe('updateUserData', () => {
        it('should return original inputData when no domain name', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            const result = await concreteReportService.updateUserData(inputData, '');
            expect(result).toBe(inputData);
            expect(mockModels.User.findOne).not.toHaveBeenCalled();
        });

        it('should update user data when domain name exists and user found', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            const newUser = { id: 2, email: '<EMAIL>', firstName: 'Updated' };
            mockModels.User.findOne.mockResolvedValue(newUser);

            const result = await concreteReportService.updateUserData(inputData, 'test-domain');

            expect(mockModels.User.findOne).toHaveBeenCalledWith({
                where: { email: '<EMAIL>' }
            });
            expect(result.user).toBe(newUser);
        });

        it('should not update user data when user not found', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            mockModels.User.findOne.mockResolvedValue(null);

            const result = await concreteReportService.updateUserData(inputData, 'test-domain');

            expect(result.user).toBe(inputData.user);
        });

        it('should handle database errors', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            mockModels.User.findOne.mockRejectedValue(new Error('DB Error'));

            await expect(concreteReportService.updateUserData(inputData, 'test-domain'))
                .rejects.toThrow('DB Error');
        });
    });

    describe('addFilterConditions', () => {
        it('should add status filter when provided', () => {
            const concreteCondition = { ProjectId: 1, isDeleted: false };
            const incomeData = { statusFilter: ['pending', 'approved'], locationFilter: [] };
            const voidConcreteDelivery = [1, 2];
            const params = { void: 0 };

            concreteReportService.addFilterConditions(concreteCondition, incomeData, voidConcreteDelivery, params);

            expect(concreteCondition.status).toEqual({ [Op.in]: ['pending', 'approved'] });
        });

        it('should add location filter when provided', () => {
            const concreteCondition = { ProjectId: 1, isDeleted: false };
            const incomeData = { statusFilter: [], locationFilter: [1, 2, 3] };
            const voidConcreteDelivery = [];
            const params = { void: 0 };

            concreteReportService.addFilterConditions(concreteCondition, incomeData, voidConcreteDelivery, params);

            expect(concreteCondition['$location.id$']).toEqual({ [Op.in]: [1, 2, 3] });
        });

        it('should handle void=1 parameter correctly', () => {
            const concreteCondition = { ProjectId: 1, isDeleted: false };
            const incomeData = { statusFilter: [], locationFilter: [] };
            const voidConcreteDelivery = [1, 2];
            const params = { void: 1 };

            concreteReportService.addFilterConditions(concreteCondition, incomeData, voidConcreteDelivery, params);

            expect(concreteCondition['$ConcreteRequest.id$']).toEqual({
                [Op.and]: [{ [Op.in]: [1, 2] }]
            });
        });

        it('should handle void=0 parameter correctly', () => {
            const concreteCondition = { ProjectId: 1, isDeleted: false };
            const incomeData = { statusFilter: [], locationFilter: [] };
            const voidConcreteDelivery = [1, 2];
            const params = { void: 0 };

            concreteReportService.addFilterConditions(concreteCondition, incomeData, voidConcreteDelivery, params);

            expect(concreteCondition['$ConcreteRequest.id$']).toEqual({
                [Op.and]: [{ [Op.notIn]: [1, 2] }]
            });
        });
    });

    describe('addSpecificFilters', () => {
        it('should add all specific filters when provided', () => {
            const concreteCondition = {};
            const incomeData = {
                idFilter: 123,
                quantityFilter: 50,
                slumpFilter: 4,
                truckspacingFilter: 2,
                primerFilter: 'yes'
            };

            concreteReportService.addSpecificFilters(concreteCondition, incomeData);

            expect(concreteCondition.ConcreteRequestId).toBe(123);
            expect(concreteCondition.concreteQuantityOrdered).toBe(50);
            expect(concreteCondition.slump).toBe(4);
            expect(concreteCondition.truckSpacingHours).toBe(2);
            expect(concreteCondition.primerForPump).toBe('yes');
        });

        it('should not add filters when not provided', () => {
            const concreteCondition = {};
            const incomeData = {};

            concreteReportService.addSpecificFilters(concreteCondition, incomeData);

            expect(concreteCondition.ConcreteRequestId).toBeUndefined();
            expect(concreteCondition.concreteQuantityOrdered).toBeUndefined();
            expect(concreteCondition.slump).toBeUndefined();
            expect(concreteCondition.truckSpacingHours).toBeUndefined();
            expect(concreteCondition.primerForPump).toBeUndefined();
        });

        it('should handle partial filter data', () => {
            const concreteCondition = {};
            const incomeData = {
                idFilter: 123,
                slumpFilter: 4
            };

            concreteReportService.addSpecificFilters(concreteCondition, incomeData);

            expect(concreteCondition.ConcreteRequestId).toBe(123);
            expect(concreteCondition.slump).toBe(4);
            expect(concreteCondition.concreteQuantityOrdered).toBeUndefined();
            expect(concreteCondition.truckSpacingHours).toBeUndefined();
            expect(concreteCondition.primerForPump).toBeUndefined();
        });
    });

    describe('getVoidConcreteDeliveryList', () => {
        it('should return array of ConcreteRequestIds', async () => {
            const mockVoidList = [
                { ConcreteRequestId: 1 },
                { ConcreteRequestId: 2 },
                { ConcreteRequestId: 3 }
            ];
            mockModels.VoidList.findAll.mockResolvedValue(mockVoidList);

            const result = await concreteReportService.getVoidConcreteDeliveryList(1);

            expect(mockModels.VoidList.findAll).toHaveBeenCalledWith({
                where: {
                    ProjectId: 1,
                    ConcreteRequestId: { [Op.ne]: null }
                }
            });
            expect(result).toEqual([1, 2, 3]);
        });

        it('should return empty array when no void requests found', async () => {
            mockModels.VoidList.findAll.mockResolvedValue([]);

            const result = await concreteReportService.getVoidConcreteDeliveryList(1);

            expect(result).toEqual([]);
        });

        it('should handle database errors', async () => {
            mockModels.VoidList.findAll.mockRejectedValue(new Error('DB Error'));

            await expect(concreteReportService.getVoidConcreteDeliveryList(1))
                .rejects.toThrow('DB Error');
        });
    });

    describe('processConcreteRequestResults', () => {
        it('should return all rows for export type', async () => {
            const inputData = { body: { exportType: 'PDF' } };
            const getConcreteRequest = [{ id: 1 }, { id: 2 }, { id: 3 }];
            const params = { pageNo: 1, pageSize: 10 };

            const result = await concreteReportService.processConcreteRequestResults(
                inputData, getConcreteRequest, params
            );

            expect(result.rows).toEqual(getConcreteRequest);
            expect(result.count).toBe(3);
        });

        it('should return paginated rows for non-export requests', async () => {
            const inputData = { body: {} };
            const getConcreteRequest = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }];
            const params = { pageNo: 2, pageSize: 2 };

            const result = await concreteReportService.processConcreteRequestResults(
                inputData, getConcreteRequest, params
            );

            expect(result.rows).toEqual([{ id: 3 }, { id: 4 }]);
            expect(result.count).toBe(5);
        });

        it('should handle first page correctly', async () => {
            const inputData = { body: {} };
            const getConcreteRequest = [{ id: 1 }, { id: 2 }, { id: 3 }];
            const params = { pageNo: 1, pageSize: 2 };

            const result = await concreteReportService.processConcreteRequestResults(
                inputData, getConcreteRequest, params
            );

            expect(result.rows).toEqual([{ id: 1 }, { id: 2 }]);
            expect(result.count).toBe(3);
        });

        it('should handle empty results', async () => {
            const inputData = { body: {} };
            const getConcreteRequest = [];
            const params = { pageNo: 1, pageSize: 10 };

            const result = await concreteReportService.processConcreteRequestResults(
                inputData, getConcreteRequest, params
            );

            expect(result.rows).toEqual([]);
            expect(result.count).toBe(0);
        });
    });

    describe('Include/Association Methods', () => {
        describe('getMemberDetails', () => {
            it('should return correct member details configuration', () => {
                const result = concreteReportService.getMemberDetails();
                expect(result).toEqual({
                    required: false,
                    association: 'memberDetails',
                    where: { isDeleted: false, isActive: true },
                    attributes: ['id'],
                    include: [
                        {
                            association: 'Member',
                            where: { isDeleted: false },
                            attributes: ['id', 'UserId'],
                            include: [
                                {
                                    association: 'User',
                                    where: { isDeleted: false },
                                    attributes: ['id', 'email', 'firstName', 'lastName'],
                                },
                            ],
                        },
                    ],
                });
            });
        });

        describe('getProjectDetails', () => {
            it('should return correct project details configuration', () => {
                const result = concreteReportService.getProjectDetails();
                expect(result).toEqual({
                    association: 'Project',
                    where: { isDeleted: false },
                    attributes: ['id', 'projectName'],
                });
            });
        });

        describe('getCreatedUserDetails', () => {
            it('should return correct created user details configuration', () => {
                const result = concreteReportService.getCreatedUserDetails();
                expect(result).toEqual({
                    association: 'createdUserDetails',
                    required: false,
                    attributes: ['id', 'RoleId'],
                    include: [
                        {
                            association: 'User',
                            where: { isDeleted: false },
                            attributes: ['email', 'id', 'firstName', 'lastName'],
                        },
                    ],
                });
            });
        });

        describe('getLocationDetails', () => {
            it('should return correct location details configuration', () => {
                const result = concreteReportService.getLocationDetails();
                expect(result).toEqual({
                    association: 'locationDetails',
                    where: { isDeleted: false },
                    required: false,
                    attributes: ['id'],
                    include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
                });
            });
        });

        describe('getMixDesignDetails', () => {
            it('should return correct mix design details configuration', () => {
                const result = concreteReportService.getMixDesignDetails();
                expect(result).toEqual({
                    association: 'mixDesignDetails',
                    where: { isDeleted: false },
                    required: false,
                    attributes: ['id'],
                    include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
                });
            });
        });

        describe('getPumpSizeDetails', () => {
            it('should return correct pump size details configuration', () => {
                const result = concreteReportService.getPumpSizeDetails();
                expect(result).toEqual({
                    association: 'pumpSizeDetails',
                    where: { isDeleted: false },
                    required: false,
                    attributes: ['id'],
                    include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
                });
            });
        });

        describe('getConcreteSupplierDetails', () => {
            it('should return correct concrete supplier details configuration', () => {
                const result = concreteReportService.getConcreteSupplierDetails();
                expect(result).toEqual({
                    association: 'concreteSupplierDetails',
                    where: { isDeleted: false },
                    required: false,
                    attributes: ['id'],
                    include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
                });
            });
        });

        describe('getVoidListDetails', () => {
            it('should return correct void list details configuration', () => {
                const result = concreteReportService.getVoidListDetails();
                expect(result).toEqual({
                    association: 'voidList',
                    attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId'],
                });
            });
        });

        describe('getApproverDetails', () => {
            it('should return correct approver details configuration', () => {
                const result = concreteReportService.getApproverDetails();
                expect(result).toEqual({
                    association: 'approverDetails',
                    attributes: ['id'],
                    include: [
                        {
                            association: 'User',
                            attributes: ['email', 'firstName', 'lastName'],
                        },
                    ],
                });
            });
        });

        describe('getLocationAttrs', () => {
            it('should return correct location attributes configuration', () => {
                const result = concreteReportService.getLocationAttrs();
                expect(result).toEqual({
                    association: 'location',
                    required: false,
                    attributes: ['id', 'locationPath'],
                });
            });
        });
    });

    describe('applyFilters', () => {
        it('should apply description filter', () => {
            const attr = {};
            const filters = { descriptionFilter: 'test description' };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ description: { [Op.iLike]: '%test description%' } }]
            });
        });

        it('should apply location filter', () => {
            const attr = {};
            const filters = { locationFilter: [1, 2, 3] };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ '$location.id$': { [Op.in]: [1, 2, 3] } }]
            });
        });

        it('should apply location path filter', () => {
            const attr = {};
            const filters = { locationPathFilter: 'Building A' };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ '$location.locationPath$': { [Op.iLike]: 'Building A' } }]
            });
        });

        it('should apply concrete supplier filter', () => {
            const attr = {};
            const filters = { concreteSupplierFilter: [1, 2] };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ '$concreteSupplierDetails.Company.id$': { [Op.in]: [1, 2] } }]
            });
        });

        it('should apply order number filter', () => {
            const attr = {};
            const filters = { orderNumberFilter: 'ORD123' };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ concreteOrderNumber: { [Op.iLike]: '%ORD123%' } }]
            });
        });

        it('should apply status filter', () => {
            const attr = {};
            const filters = { statusFilter: ['pending', 'approved'] };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ status: ['pending', 'approved'] }]
            });
        });

        it('should apply member filter', () => {
            const attr = {};
            const filters = { memberFilter: 5 };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ '$memberDetails.Member.id$': 5 }]
            });
        });

        it('should apply mix design filter', () => {
            const attr = {};
            const filters = { mixDesignFilter: 'Mix A' };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toContainEqual({
                [Op.or]: [{ '$mixDesignDetails.ConcreteMixDesign.mixDesign$': { [Op.iLike]: '%Mix A%' } }]
            });
        });

        it('should apply date conditions', () => {
            const attr = {};
            const dateConditions = { concretePlacementStart: { [Op.gte]: new Date() } };
            const filters = { dateConditions };

            concreteReportService.applyFilters(attr, filters);

            expect(attr.concretePlacementStart).toEqual(dateConditions.concretePlacementStart);
        });

        it('should apply search conditions', () => {
            const attr = {};
            const searchConditions = { [Op.and]: [{ [Op.or]: [{ description: { [Op.iLike]: '%test%' } }] }] };
            const filters = { searchConditions };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toEqual(searchConditions[Op.and]);
        });

        it('should handle multiple filters together', () => {
            const attr = {};
            const filters = {
                descriptionFilter: 'test',
                locationFilter: [1, 2],
                memberFilter: 5,
                statusFilter: ['pending']
            };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toHaveLength(4);
        });

        it('should not apply filters when values are falsy or empty', () => {
            const attr = {};
            const filters = {
                descriptionFilter: '',
                locationFilter: [],
                memberFilter: 0,
                statusFilter: []
            };

            concreteReportService.applyFilters(attr, filters);

            expect(attr[Op.and]).toBeUndefined();
        });
    });

    describe('Export Methods', () => {
        const mockExportService = require('../exportService');
        const mockPdfService = require('../pdfConcreteReportService');
        const mockCsvService = require('../csvConcreteReportService');
        const mockExcelService = require('../excelConcreteReportService');
        const mockDeliveryService = require('../deliveryreportService');

        describe('handlePdfExport', () => {
            it('should handle successful PDF export', async () => {
                const mockReq = {
                    user: { id: 1 },
                    params: { ProjectId: 1 }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockPdfFile = 'pdf-file-data';

                mockPdfService.pdfFormatOfConcreteRequest.mockImplementation((_params, _user, _rows, _req, callback) => {
                    callback(mockPdfFile, null);
                });

                const result = await concreteReportService.handlePdfExport(mockReq, mockResponse);

                expect(mockPdfService.pdfFormatOfConcreteRequest).toHaveBeenCalledWith(
                    mockReq.params,
                    mockReq.user,
                    mockResponse.rows,
                    mockReq,
                    expect.any(Function)
                );
                expect(result).toBe(mockPdfFile);
            });

            it('should handle PDF export errors', async () => {
                const mockReq = {
                    user: { id: 1 },
                    params: { ProjectId: 1 }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockError = { message: 'PDF generation failed' };

                mockPdfService.pdfFormatOfConcreteRequest.mockImplementation((_params, _user, _rows, _req, callback) => {
                    callback(null, mockError);
                });

                await expect(concreteReportService.handlePdfExport(mockReq, mockResponse))
                    .rejects.toThrow('PDF generation failed');
            });

            it('should handle PDF export errors without message', async () => {
                const mockReq = {
                    user: { id: 1 },
                    params: { ProjectId: 1 }
                };
                const mockResponse = { rows: [{ id: 1 }] };

                mockPdfService.pdfFormatOfConcreteRequest.mockImplementation((_params, _user, _rows, _req, callback) => {
                    callback(null, {});
                });

                await expect(concreteReportService.handlePdfExport(mockReq, mockResponse))
                    .rejects.toThrow('Failed to generate PDF');
            });
        });

        describe('handleExcelExport', () => {
            it('should handle successful Excel export', async () => {
                const mockReq = {
                    body: { selectedHeaders: [], reportName: 'test', exportType: 'EXCEL' },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockWorkbook = { xlsx: { writeBuffer: jest.fn().mockResolvedValue(Buffer.from('excel-data')) } };
                const mockExcelFile = 'excel-file-url';

                mockExportService.createWorkbook.mockResolvedValue(mockWorkbook);
                mockExcelService.concreteReport.mockResolvedValue(mockWorkbook);
                mockDeliveryService.saveExcelReport.mockResolvedValue(mockExcelFile);
                mockDeliveryService.createSavedReports.mockResolvedValue(true);

                const result = await concreteReportService.handleExcelExport(mockReq, mockResponse);

                expect(mockExportService.createWorkbook).toHaveBeenCalled();
                expect(mockExcelService.concreteReport).toHaveBeenCalledWith(
                    mockWorkbook,
                    mockResponse.rows,
                    mockReq.body.selectedHeaders,
                    mockReq.headers.timezoneoffset
                );
                expect(mockDeliveryService.saveExcelReport).toHaveBeenCalledWith(
                    expect.any(Buffer),
                    mockReq.body.reportName,
                    mockReq.body.exportType
                );
                expect(mockDeliveryService.createSavedReports).toHaveBeenCalledWith(mockReq, mockExcelFile);
                expect(result).toBe(mockExcelFile);
                expect(mockReq.body).toHaveProperty('reportType', 'Concrete');
            });

            it('should handle Excel report generation failure', async () => {
                const mockReq = {
                    body: { selectedHeaders: [], reportName: 'test', exportType: 'EXCEL' },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockWorkbook = {};

                mockExportService.createWorkbook.mockResolvedValue(mockWorkbook);
                mockExcelService.concreteReport.mockResolvedValue(null);

                await expect(concreteReportService.handleExcelExport(mockReq, mockResponse))
                    .rejects.toThrow('Failed to generate Excel report');
            });

            it('should handle Excel file save failure', async () => {
                const mockReq = {
                    body: { selectedHeaders: [], reportName: 'test', exportType: 'EXCEL' },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockWorkbook = { xlsx: { writeBuffer: jest.fn().mockResolvedValue(Buffer.from('excel-data')) } };

                mockExportService.createWorkbook.mockResolvedValue(mockWorkbook);
                mockExcelService.concreteReport.mockResolvedValue(mockWorkbook);
                mockDeliveryService.saveExcelReport.mockResolvedValue(null);

                await expect(concreteReportService.handleExcelExport(mockReq, mockResponse))
                    .rejects.toThrow('Failed to create saved reports');
            });
        });

        describe('handleCsvExport', () => {
            it('should handle successful CSV export', async () => {
                const mockReq = {
                    body: { selectedHeaders: [], reportName: 'test', exportType: 'CSV' },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockCsvFile = 'csv-file-data';

                mockCsvService.exportConcreteReportInCsvFormat.mockImplementation((_rows, _headers, _timezone, _name, _type, callback) => {
                    callback(mockCsvFile, null);
                });

                const result = await concreteReportService.handleCsvExport(mockReq, mockResponse);

                expect(mockCsvService.exportConcreteReportInCsvFormat).toHaveBeenCalledWith(
                    mockResponse.rows,
                    mockReq.body.selectedHeaders,
                    mockReq.headers.timezoneoffset,
                    mockReq.body.reportName,
                    mockReq.body.exportType,
                    expect.any(Function)
                );
                expect(result).toBe(mockCsvFile);
            });

            it('should handle CSV export errors', async () => {
                const mockReq = {
                    body: { selectedHeaders: [], reportName: 'test', exportType: 'CSV' },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { rows: [{ id: 1 }] };
                const mockError = { message: 'CSV generation failed' };

                mockCsvService.exportConcreteReportInCsvFormat.mockImplementation((_rows, _headers, _timezone, _name, _type, callback) => {
                    callback(null, mockError);
                });

                await expect(concreteReportService.handleCsvExport(mockReq, mockResponse))
                    .rejects.toThrow('CSV generation failed');
            });

            it('should handle CSV export errors without message', async () => {
                const mockReq = {
                    body: { selectedHeaders: [], reportName: 'test', exportType: 'CSV' },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { rows: [{ id: 1 }] };

                mockCsvService.exportConcreteReportInCsvFormat.mockImplementation((_rows, _headers, _timezone, _name, _type, callback) => {
                    callback(null, {});
                });

                await expect(concreteReportService.handleCsvExport(mockReq, mockResponse))
                    .rejects.toThrow('Failed to generate CSV');
            });
        });

        describe('saveReportIfNeeded', () => {
            it('should return result when not saved', async () => {
                const mockReq = { body: { saved: false } };
                const mockResult = 'test-result';
                const exportType = 'PDF';

                const result = await concreteReportService.saveReportIfNeeded(mockReq, mockResult, exportType);

                expect(result).toBe(mockResult);
                expect(mockDeliveryService.createSavedReports).not.toHaveBeenCalled();
            });

            it('should return result when export type is EXCEL', async () => {
                const mockReq = { body: { saved: true } };
                const mockResult = 'test-result';
                const exportType = 'EXCEL';

                const result = await concreteReportService.saveReportIfNeeded(mockReq, mockResult, exportType);

                expect(result).toBe(mockResult);
                expect(mockDeliveryService.createSavedReports).not.toHaveBeenCalled();
            });

            it('should save report when saved is true and not EXCEL', async () => {
                const mockReq = { body: { saved: true } };
                const mockResult = 'test-result';
                const exportType = 'PDF';

                mockDeliveryService.createSavedReports.mockResolvedValue(true);

                const result = await concreteReportService.saveReportIfNeeded(mockReq, mockResult, exportType);

                expect(mockDeliveryService.createSavedReports).toHaveBeenCalledWith(mockReq, mockResult);
                expect(mockReq.body).toHaveProperty('reportType', 'Concrete');
                expect(result).toBe(mockResult);
            });

            it('should throw error when save fails', async () => {
                const mockReq = { body: { saved: true } };
                const mockResult = 'test-result';
                const exportType = 'PDF';

                mockDeliveryService.createSavedReports.mockResolvedValue(null);

                await expect(concreteReportService.saveReportIfNeeded(mockReq, mockResult, exportType))
                    .rejects.toThrow('Failed to create saved reports');
            });
        });
    });

    describe('Main Service Methods', () => {
        describe('getDynamicModel', () => {
            it('should resolve domain and update models successfully', async () => {
                const inputData = {
                    user: { email: '<EMAIL>', domainName: 'test-domain' },
                    body: { ParentCompanyId: 1 },
                    params: {}
                };

                helper.returnProjectModel.mockResolvedValue({
                    Member: mockModels.Member,
                    User: mockModels.User
                });

                mockModels.Enterprise.findOne.mockResolvedValue({ name: 'test-domain' });
                helper.getDynamicModel.mockResolvedValue({
                    Member: mockModels.Member,
                    User: mockModels.User,
                    VoidList: mockModels.VoidList
                });

                const result = await concreteReportService.getDynamicModel(inputData);

                expect(helper.returnProjectModel).toHaveBeenCalled();
                expect(helper.getDynamicModel).toHaveBeenCalledWith('test-domain');
                expect(result).toBeUndefined();
            });

            it('should handle missing domain name', async () => {
                const inputData = {
                    user: { email: '<EMAIL>' },
                    body: { ParentCompanyId: 1 },
                    params: {}
                };

                helper.returnProjectModel.mockResolvedValue({
                    Member: mockModels.Member,
                    User: mockModels.User
                });

                helper.getDynamicModel.mockResolvedValue({
                    Member: mockModels.Member,
                    User: mockModels.User,
                    VoidList: mockModels.VoidList
                });

                await concreteReportService.getDynamicModel(inputData);

                expect(helper.getDynamicModel).toHaveBeenCalled();
            });

            it('should handle errors in domain resolution', async () => {
                const inputData = {
                    user: { email: '<EMAIL>', domainName: 'test-domain' },
                    body: { ParentCompanyId: 1 },
                    params: {}
                };

                helper.returnProjectModel.mockRejectedValue(new Error('Model error'));

                await expect(concreteReportService.getDynamicModel(inputData))
                    .rejects.toThrow('Model error');
            });
        });

        describe('returnProjectModel', () => {
            it('should set public models successfully', async () => {
                const mockModelData = {
                    Member: mockModels.Member,
                    User: mockModels.User
                };

                helper.returnProjectModel.mockResolvedValue(mockModelData);

                await concreteReportService.returnProjectModel();

                expect(helper.returnProjectModel).toHaveBeenCalled();
            });

            it('should handle errors in model retrieval', async () => {
                helper.returnProjectModel.mockRejectedValue(new Error('Model retrieval failed'));

                await expect(concreteReportService.returnProjectModel())
                    .rejects.toThrow('Model retrieval failed');
            });
        });

        describe('fetchConcreteRequestData', () => {
            it('should fetch data with all filters', async () => {
                const inputData = {
                    body: {
                        descriptionFilter: 'test',
                        locationFilter: [1, 2],
                        concreteSupplierFilter: [1],
                        orderNumberFilter: 'ORD123',
                        statusFilter: ['pending'],
                        mixDesignFilter: 'Mix A',
                        startdate: '2024-01-01',
                        enddate: '2024-01-31',
                        memberFilter: 5,
                        search: 'search term',
                        locationPathFilter: 'Building A',
                        sort: 'ASC',
                        sortByField: 'description'
                    },
                    headers: { timezoneoffset: '-240' }
                };
                const concreteCondition = { ProjectId: 1 };
                const memberDetails = { id: 1 };

                const mockConcreteRequests = [{ id: 1, description: 'Test' }];
                mockModels.ConcreteRequest.findAll.mockResolvedValue(mockConcreteRequests);

                const result = await concreteReportService.fetchConcreteRequestData(
                    inputData, concreteCondition, memberDetails
                );

                expect(result).toBe(mockConcreteRequests);
            });

            it('should handle minimal filter data', async () => {
                const inputData = {
                    body: {},
                    headers: { timezoneoffset: '-240' }
                };
                const concreteCondition = { ProjectId: 1 };
                const memberDetails = { id: 1 };

                const mockConcreteRequests = [];
                mockModels.ConcreteRequest.findAll.mockResolvedValue(mockConcreteRequests);

                const result = await concreteReportService.fetchConcreteRequestData(
                    inputData, concreteCondition, memberDetails
                );

                expect(result).toBe(mockConcreteRequests);
            });
        });

        describe('exportReportForScheduler', () => {
            it('should export PDF report successfully', async () => {
                const mockReq = {
                    body: { exportType: 'PDF' },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };

                const mockResponse = { count: 1, rows: [{ id: 1 }] };
                const mockPdfFile = 'pdf-file-data';

                // Mock the listConcreteRequest method
                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                jest.spyOn(concreteReportService, 'handlePdfExport')
                    .mockResolvedValue(mockPdfFile);

                const result = await concreteReportService.exportReportForScheduler(mockReq);

                expect(result).toBe(mockPdfFile);
            });

            it('should handle no data found error', async () => {
                const mockReq = {
                    body: { exportType: 'PDF' },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };

                const mockResponse = { count: 0, rows: [] };

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                await expect(concreteReportService.exportReportForScheduler(mockReq))
                    .rejects.toThrow('No Data Found');
            });

            it('should handle listConcreteRequest errors', async () => {
                const mockReq = {
                    body: { exportType: 'PDF' },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };

                const mockError = new Error('Database error');

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(null, mockError);
                    });

                await expect(concreteReportService.exportReportForScheduler(mockReq))
                    .rejects.toThrow('Database error');
            });

            it('should handle export errors', async () => {
                const mockReq = {
                    body: { exportType: 'PDF' },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };

                const mockResponse = { count: 1, rows: [{ id: 1 }] };

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                jest.spyOn(concreteReportService, 'handlePdfExport')
                    .mockRejectedValue(new Error('Export failed'));

                await expect(concreteReportService.exportReportForScheduler(mockReq))
                    .rejects.toThrow('Export failed');
            });
        });

        describe('exportReport', () => {
            it('should export and save PDF report successfully', async () => {
                const mockReq = {
                    body: { exportType: 'PDF', saved: true },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { count: 1, rows: [{ id: 1 }] };
                const mockPdfFile = 'pdf-file-data';
                const done = jest.fn();

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                jest.spyOn(concreteReportService, 'handlePdfExport')
                    .mockResolvedValue(mockPdfFile);

                const mockDeliveryService = require('../deliveryreportService');
                mockDeliveryService.createSavedReports.mockResolvedValue(true);

                await concreteReportService.exportReport(mockReq, done);

                expect(done).toHaveBeenCalledWith(mockPdfFile, false);
                expect(mockDeliveryService.createSavedReports).toHaveBeenCalledWith(mockReq, mockPdfFile);
                expect(mockReq.body).toHaveProperty('reportType', 'Concrete');
            });

            it('should export Excel report without saving', async () => {
                const mockReq = {
                    body: { exportType: 'EXCEL', saved: false },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { count: 1, rows: [{ id: 1 }] };
                const mockExcelFile = 'excel-file-data';
                const done = jest.fn();

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                jest.spyOn(concreteReportService, 'handleExcelExport')
                    .mockResolvedValue(mockExcelFile);

                await concreteReportService.exportReport(mockReq, done);

                expect(done).toHaveBeenCalledWith(mockExcelFile, false);
            });

            it('should handle listConcreteRequest errors', async () => {
                const mockReq = {
                    body: { exportType: 'PDF' },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };
                const mockError = new Error('Database error');
                const done = jest.fn();

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(null, mockError);
                    });

                await concreteReportService.exportReport(mockReq, done);

                expect(done).toHaveBeenCalledWith(null, expect.any(Error));
            });

            it('should handle export errors', async () => {
                const mockReq = {
                    body: { exportType: 'PDF' },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { count: 1, rows: [{ id: 1 }] };
                const done = jest.fn();

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                jest.spyOn(concreteReportService, 'handlePdfExport')
                    .mockRejectedValue(new Error('Export failed'));

                await concreteReportService.exportReport(mockReq, done);

                expect(done).toHaveBeenCalledWith(null, expect.any(Error));
            });

            it('should handle save report failure', async () => {
                const mockReq = {
                    body: { exportType: 'PDF', saved: true },
                    user: { id: 1 },
                    params: { ProjectId: 1, void: 0 },
                    headers: { timezoneoffset: '-240' }
                };
                const mockResponse = { count: 1, rows: [{ id: 1 }] };
                const mockPdfFile = 'pdf-file-data';
                const done = jest.fn();

                jest.spyOn(concreteReportService, 'listConcreteRequest')
                    .mockImplementation((_req, callback) => {
                        callback(mockResponse, null);
                    });

                jest.spyOn(concreteReportService, 'handlePdfExport')
                    .mockResolvedValue(mockPdfFile);

                const mockDeliveryService = require('../deliveryreportService');
                mockDeliveryService.createSavedReports.mockResolvedValue(null);

                await concreteReportService.exportReport(mockReq, done);

                expect(done).toHaveBeenCalledWith(null, expect.any(Error));
            });
        });
    });

    describe('Edge Cases and Error Handling', () => {
        describe('resolveDomainName edge cases', () => {
            it('should handle undefined ParentCompanyId', async () => {
                const result = await concreteReportService.resolveDomainName(null, 'undefined', '<EMAIL>');
                expect(result).toBe('');
            });

            it('should handle null ParentCompanyId', async () => {
                const result = await concreteReportService.resolveDomainName(null, null, '<EMAIL>');
                expect(result).toBe('');
            });

            it('should handle empty string ParentCompanyId', async () => {
                const result = await concreteReportService.resolveDomainName(null, '', '<EMAIL>');
                expect(result).toBe('');
            });
        });

        describe('getDomainFromMemberData edge cases', () => {
            it('should handle member with null isAccount', async () => {
                mockModels.Member.findOne.mockResolvedValue({ isAccount: null });
                const result = await concreteReportService.getDomainFromMemberData({ id: 1 }, 1);
                expect(result).toBe('test-enterprise');
            });

            it('should handle member with undefined isAccount', async () => {
                mockModels.Member.findOne.mockResolvedValue({});
                const result = await concreteReportService.getDomainFromMemberData({ id: 1 }, 1);
                expect(result).toBe('test-enterprise');
            });

            it('should handle null memberData', async () => {
                mockModels.Member.findOne.mockResolvedValue(null);
                const result = await concreteReportService.getDomainFromMemberData({ id: 1 }, 1);
                expect(result).toBe('test-enterprise');
            });
        });

        describe('buildConcreteConditions edge cases', () => {
            it('should handle empty filters', async () => {
                const params = { ProjectId: 1 };
                const incomeData = { statusFilter: [], locationFilter: [] };
                const voidConcreteDelivery = [];

                const result = await concreteReportService.buildConcreteConditions(
                    params, incomeData, voidConcreteDelivery
                );

                expect(result.ProjectId).toBe(1);
                expect(result.isDeleted).toBe(false);
                expect(result.status).toBeUndefined();
                expect(result['$location.id$']).toBeUndefined();
            });

            it('should handle null incomeData properties', async () => {
                const params = { ProjectId: 1 };
                const incomeData = { statusFilter: null, locationFilter: null };
                const voidConcreteDelivery = [];

                const result = await concreteReportService.buildConcreteConditions(
                    params, incomeData, voidConcreteDelivery
                );

                expect(result.ProjectId).toBe(1);
                expect(result.isDeleted).toBe(false);
            });
        });

        describe('listConcreteRequest edge cases', () => {
            it('should handle void parameter validation - negative value', async () => {
                const inputData = {
                    user: { id: 1, email: '<EMAIL>' },
                    params: { ProjectId: 1, void: -1 },
                    body: {},
                    headers: { timezoneoffset: '-240' }
                };
                const done = jest.fn();

                await concreteReportService.listConcreteRequest(inputData, done);

                expect(done).toHaveBeenCalledWith(null, { message: 'Please enter void as 1 or 0' });
            });

            it('should handle void parameter validation - value greater than 1', async () => {
                const inputData = {
                    user: { id: 1, email: '<EMAIL>' },
                    params: { ProjectId: 1, void: 2 },
                    body: {},
                    headers: { timezoneoffset: '-240' }
                };
                const done = jest.fn();

                await concreteReportService.listConcreteRequest(inputData, done);

                expect(done).toHaveBeenCalledWith(null, { message: 'Please enter void as 1 or 0' });
            });

            it('should handle getDynamicModel errors', async () => {
                const inputData = {
                    user: { id: 1, email: '<EMAIL>' },
                    params: { ProjectId: 1, void: 0 },
                    body: {},
                    headers: { timezoneoffset: '-240' }
                };
                const done = jest.fn();

                jest.spyOn(concreteReportService, 'getDynamicModel')
                    .mockRejectedValue(new Error('Dynamic model error'));

                await concreteReportService.listConcreteRequest(inputData, done);

                expect(done).toHaveBeenCalledWith(null, expect.any(Error));
            });
        });
    });
});