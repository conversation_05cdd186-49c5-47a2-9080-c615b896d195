const status = require('http-status');
const ConcreteRequestController = require('../ConcreteRequestController');

// Mock the services
jest.mock('../../services', () => ({
  concreteRequestService: {
    newConcreteRequest: jest.fn(),
    editConcreteRequest: jest.fn(),
    listConcreteBookingRequest: jest.fn(),
    getConcreteRequestData: jest.fn(),
    updateConcreteRequestStatus: jest.fn(),
    lastConcreteRequest: jest.fn(),
    getSingleConcreteRequest: jest.fn(),
    upcomingConcreteRequest: jest.fn(),
    upcomingRequestList: jest.fn(),
    getConcreteDropdownDetail: jest.fn(),
    deleteConcreteRequest: jest.fn(),
    editMultipleDeliveryRequest: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  ProjectSettings: {
    getCalendarStatusColor: jest.fn(),
  },
}));

const { concreteRequestService } = require('../../services');
const { ProjectSettings } = require('../../models');

describe('ConcreteRequestController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    jest.clearAllMocks();

    mockReq = {
      body: {},
      params: {},
      query: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('createConcreteRequest', () => {
    it('should create concrete request successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        projectName: 'Test Project',
        concreteType: 'C25',
        quantity: 100,
        status: 'pending',
      };
      concreteRequestService.newConcreteRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.createConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.newConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Concrete request creation failed');
      concreteRequestService.newConcreteRequest.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.createConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.newConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      concreteRequestService.newConcreteRequest.mockImplementation(() => {
        throw error;
      });

      // Act
      await ConcreteRequestController.createConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('editConcreteRequest', () => {
    it('should edit concrete request successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        projectName: 'Updated Project',
        concreteType: 'C30',
        quantity: 150,
        status: 'approved',
      };
      concreteRequestService.editConcreteRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.editConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.editConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Updated Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Concrete request update failed');
      concreteRequestService.editConcreteRequest.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.editConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.editConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getConcreteRequestList', () => {
    it('should get concrete request list successfully with status data', async () => {
      // Arrange
      const mockResponse = [
        { id: 1, projectName: 'Project 1', status: 'pending' },
        { id: 2, projectName: 'Project 2', status: 'approved' },
      ];
      const mockStatusData = { status: 'active', color: '#00ff00' };

      mockReq.params = { ProjectId: '123' };
      concreteRequestService.listConcreteBookingRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);

      // Act
      await ConcreteRequestController.getConcreteRequestList(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.listConcreteBookingRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking listed Successfully.',
        data: mockResponse,
        statusData: mockStatusData,
      });
    });

    it('should get concrete request list without status data when ProjectId is empty', async () => {
      // Arrange
      const mockResponse = [{ id: 1, projectName: 'Project 1', status: 'pending' }];

      mockReq.params = { ProjectId: '' };
      concreteRequestService.listConcreteBookingRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestList(mockReq, mockRes, mockNext);

      // Assert
      expect(ProjectSettings.getCalendarStatusColor).not.toHaveBeenCalled();
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking listed Successfully.',
        data: mockResponse,
        statusData: undefined,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to get concrete request list');
      concreteRequestService.listConcreteBookingRequest.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestList(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.listConcreteBookingRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getConcreteRequestData', () => {
    it('should get concrete request data successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        projectName: 'Test Project',
        concreteType: 'C25',
        quantity: 100,
        deliveryDate: '2023-01-15',
        status: 'pending',
      };
      concreteRequestService.getConcreteRequestData.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestData(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.getConcreteRequestData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking listed Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to get concrete request data');
      concreteRequestService.getConcreteRequestData.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestData(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.getConcreteRequestData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('updateConcreteRequestStatus', () => {
    it('should update concrete request status successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        status: 'approved',
        updatedAt: '2023-01-01T00:00:00Z',
      };
      mockReq.body = { status: 'approved' };
      concreteRequestService.updateConcreteRequestStatus.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.updateConcreteRequestStatus(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.updateConcreteRequestStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'approved Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to update status');
      concreteRequestService.updateConcreteRequestStatus.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.updateConcreteRequestStatus(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.updateConcreteRequestStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getLastConcreteRequestId', () => {
    it('should get last concrete request ID successfully', async () => {
      // Arrange
      const mockLastDetail = { id: 10, requestNumber: 'CR-2023-0010' };
      concreteRequestService.lastConcreteRequest.mockImplementation((req, callback) =>
        callback(mockLastDetail, null),
      );

      // Act
      await ConcreteRequestController.getLastConcreteRequestId(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.lastConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Last Id Viewed Successfully.',
        lastId: mockLastDetail,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to get last ID');
      concreteRequestService.lastConcreteRequest.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.getLastConcreteRequestId(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.lastConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getSingleConcreteRequest', () => {
    it('should get single concrete request successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        projectName: 'Single Project',
        concreteType: 'C25',
        quantity: 100,
        status: 'pending',
      };
      concreteRequestService.getSingleConcreteRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.getSingleConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.getSingleConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Viewed Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to get single request');
      concreteRequestService.getSingleConcreteRequest.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.getSingleConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.getSingleConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('upcomingConcreteRequest', () => {
    it('should get upcoming concrete request successfully', async () => {
      // Arrange
      const mockResponse = {
        status: 200,
        data: [
          { id: 1, projectName: 'Upcoming Project 1', deliveryDate: '2023-01-15' },
          { id: 2, projectName: 'Upcoming Project 2', deliveryDate: '2023-01-20' },
        ],
      };
      concreteRequestService.upcomingConcreteRequest.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.upcomingConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.upcomingConcreteRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Upcoming Concrete Booking Viewed Successfully.',
        data: mockResponse.data,
      });
    });

    it('should handle error response from service', async () => {
      // Arrange
      const mockResponse = {
        status: 422,
        msg: 'No upcoming requests found',
      };
      concreteRequestService.upcomingConcreteRequest.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.upcomingConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.upcomingConcreteRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.UNPROCESSABLE_ENTITY);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'No upcoming requests found',
        data: [],
      });
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      concreteRequestService.upcomingConcreteRequest.mockRejectedValue(error);

      // Act
      await ConcreteRequestController.upcomingConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('upcomingRequestList', () => {
    it('should get upcoming request list successfully', async () => {
      // Arrange
      const mockResponse = {
        status: 200,
        data: [
          { id: 1, projectName: 'Upcoming List Project 1' },
          { id: 2, projectName: 'Upcoming List Project 2' },
        ],
      };
      concreteRequestService.upcomingRequestList.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.upcomingRequestList(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.upcomingRequestList).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Upcoming Concrete Booking Viewed Successfully.',
        data: mockResponse.data,
      });
    });

    it('should handle error response from service', async () => {
      // Arrange
      const mockResponse = {
        status: 422,
        msg: 'No requests found',
      };
      concreteRequestService.upcomingRequestList.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.upcomingRequestList(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.UNPROCESSABLE_ENTITY);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'No requests found',
        data: [],
      });
    });
  });

  describe('getConcreteDropdownDetail', () => {
    it('should get concrete dropdown details successfully', async () => {
      // Arrange
      const mockResponse = {
        status: 200,
        data: {
          concreteTypes: ['C25', 'C30', 'C35'],
          projects: ['Project 1', 'Project 2'],
          suppliers: ['Supplier 1', 'Supplier 2'],
        },
      };
      concreteRequestService.getConcreteDropdownDetail.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.getConcreteDropdownDetail(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.getConcreteDropdownDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Dropdown Details Viewed Successfully.',
        data: mockResponse.data,
      });
    });

    it('should handle error response from service', async () => {
      // Arrange
      const mockResponse = {
        status: 422,
        msg: 'Failed to load dropdown details',
      };
      concreteRequestService.getConcreteDropdownDetail.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.getConcreteDropdownDetail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.UNPROCESSABLE_ENTITY);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Failed to load dropdown details',
        data: [],
      });
    });
  });

  describe('deleteConcreteRequest', () => {
    it('should delete concrete request successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        status: 'deleted',
        deletedAt: '2023-01-01T00:00:00Z',
      };
      concreteRequestService.deleteConcreteRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.deleteConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.deleteConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Deleted Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to delete request');
      concreteRequestService.deleteConcreteRequest.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await ConcreteRequestController.deleteConcreteRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.deleteConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('editMultipleDeliveryRequest', () => {
    it('should edit multiple delivery requests successfully', async () => {
      // Arrange
      const mockResponse = {
        updated: 5,
        failed: 0,
        message: 'All requests updated successfully',
      };
      concreteRequestService.editMultipleDeliveryRequest.mockResolvedValue(mockResponse);

      // Act
      await ConcreteRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.editMultipleDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'concrete Booking Updated Successfully.',
        data: mockResponse,
      });
    });

    it('should handle falsy response from service', async () => {
      // Arrange
      concreteRequestService.editMultipleDeliveryRequest.mockResolvedValue(null);

      // Act
      await ConcreteRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'concrete Booking Updated Successfully.',
        data: null,
      });
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      concreteRequestService.editMultipleDeliveryRequest.mockRejectedValue(error);

      // Act
      await ConcreteRequestController.editMultipleDeliveryRequest(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('Edge Cases', () => {
    it('should handle null response from service', async () => {
      // Arrange
      concreteRequestService.getConcreteRequestData.mockImplementation((req, callback) =>
        callback(null, null),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestData(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(null);
    });

    it('should handle empty array response', async () => {
      // Arrange
      const mockResponse = [];
      concreteRequestService.listConcreteBookingRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestList(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking listed Successfully.',
        data: [],
        statusData: undefined,
      });
    });

    it('should handle request with complex data', async () => {
      // Arrange
      const mockReqWithData = {
        ...mockReq,
        body: {
          projectName: 'Complex Project',
          concreteType: 'C40',
          quantity: 500,
          deliveryDate: '2023-01-15',
          deliveryTime: '09:00',
          siteAddress: '123 Construction Site',
          contactPerson: 'John Doe',
          phoneNumber: '******-123-4567',
          specialRequirements: 'High strength concrete with fiber reinforcement',
          notes: 'Please deliver before 10 AM',
        },
      };
      const mockResponse = {
        id: 1,
        ...mockReqWithData.body,
        status: 'pending',
        createdAt: '2023-01-01T00:00:00Z',
      };
      concreteRequestService.newConcreteRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.createConcreteRequest(mockReqWithData, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.newConcreteRequest).toHaveBeenCalledWith(
        mockReqWithData,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Created Successfully.',
        data: mockResponse,
      });
    });

    it('should handle request with query parameters', async () => {
      // Arrange
      const mockReqWithQuery = {
        ...mockReq,
        query: {
          page: '1',
          limit: '10',
          status: 'pending',
          projectId: '123',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        },
      };
      const mockResponse = [
        { id: 1, projectName: 'Project 1', status: 'pending' },
        { id: 2, projectName: 'Project 2', status: 'pending' },
      ];
      concreteRequestService.listConcreteBookingRequest.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await ConcreteRequestController.getConcreteRequestList(mockReqWithQuery, mockRes, mockNext);

      // Assert
      expect(concreteRequestService.listConcreteBookingRequest).toHaveBeenCalledWith(
        mockReqWithQuery,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking listed Successfully.',
        data: mockResponse,
        statusData: undefined,
      });
    });
  });
});
