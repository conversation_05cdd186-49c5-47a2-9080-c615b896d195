const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  multer.memoryStorage = jest.fn(() => 'mocked-memory-storage');
  return multer;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  ProjectController: {
    createProject: jest.fn(),
    createAccountProject: jest.fn(),
    editProject: jest.fn(),
    existProject: jest.fn(),
    upgradePlan: jest.fn(),
    getPlansAndProjects: jest.fn(),
    getProjects: jest.fn(),
    getUserProjects: jest.fn(),
    getProjectsCompany: jest.fn(),
    getAccountProjects: jest.fn(),
    getSingleProjectDetail: jest.fn(),
    getSingleProjectProfileDetail: jest.fn(),
    getProjectList: jest.fn(),
    assignNewProjectToMember: jest.fn(),
    getMemberProject: jest.fn(),
    editMemberProject: jest.fn(),
    projectsBillingHistories: jest.fn(),
    getProjectBillingHistories: jest.fn(),
    getTotalProjects: jest.fn(),
    extendProjectDuration: jest.fn(),
    updateProjectSharingSettings: jest.fn(),
    uploadProjectLogisticPlanUrl: jest.fn(),
    generatePublicUrlForExistingProjects: jest.fn(),
    decodeProjectDetailUrl: jest.fn(),
    updateDashboardLogisticPlan: jest.fn(),
    retoolParentCompanyWithProjects: jest.fn(),
    updatedSitePlanInAWS: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  projectValidation: {
    createProject: jest.fn(),
    createAccountProject: jest.fn(),
    existProject: jest.fn(),
    upgradePlan: jest.fn(),
    getPlansAndProjects: jest.fn(),
    getProjects: jest.fn(),
    getSingleProjectDetail: jest.fn(),
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isProjectAdminOnly: jest.fn(),
  isAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
}));

describe('projectRoute', () => {
  let router;
  let projectRoute;
  let ProjectController;
  let passportConfig;
  let projectValidation;
  let checkAdmin;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    projectRoute = require('../projectRoute');
    const controllers = require('../../controllers');
    ProjectController = controllers.ProjectController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    projectValidation = validations.projectValidation;
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = projectRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify that routes are configured
      expect(router.post).toHaveBeenCalled();
      expect(router.get).toHaveBeenCalled();
      expect(router.put).toHaveBeenCalled();
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = projectRoute.router;
      const result2 = projectRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof projectRoute).toBe('object');
      expect(projectRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(projectRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(projectRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
