const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  calendarsettingsController: {
    addCalendarEvent: jest.fn(),
    getCalendarEvent: jest.fn(),
    editCalendarEvent: jest.fn(),
    deleteCalendarEvent: jest.fn(),
    getCalendarEvents: jest.fn(),
    getCalendarMonthEvents: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  calendarSettingsValidation: {
    addCalendarEvent: jest.fn(),
    getCalendarEvent: jest.fn(),
    editCalendarEvent: jest.fn(),
    deleteCalendarEvent: jest.fn(),
    getCalendarEvents: jest.fn(),
    getCalendarMonthEvents: jest.fn(),
  },
}));

describe('calendarSettingsRoute', () => {
  let router;
  let calendarSettingsRoute;
  let calendarsettingsController;
  let passportConfig;
  let calendarSettingsValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    calendarSettingsRoute = require('../calendarSettingsRoute');
    const controllers = require('../../controllers');
    calendarsettingsController = controllers.calendarsettingsController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    calendarSettingsValidation = validations.calendarSettingsValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = calendarSettingsRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(3);
      expect(router.put).toHaveBeenCalledTimes(2);

      // Verify POST route
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/add_event',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        calendarsettingsController.addCalendarEvent,
      );

      // Verify GET routes (including chained ones)
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_event/:id',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        calendarsettingsController.getCalendarEvent,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_calendar_events',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        calendarsettingsController.getCalendarEvents,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/get_calendar_month_events',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        calendarsettingsController.getCalendarMonthEvents,
      );

      // Verify PUT routes (chained)
      expect(router.put).toHaveBeenNthCalledWith(
        1,
        '/edit_event/:id',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        calendarsettingsController.editCalendarEvent,
      );

      expect(router.put).toHaveBeenNthCalledWith(
        2,
        '/delete_event/:id',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        calendarsettingsController.deleteCalendarEvent,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(6);
      expect(validate).toHaveBeenCalledWith(
        calendarSettingsValidation.addCalendarEvent,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        calendarSettingsValidation.getCalendarEvent,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        calendarSettingsValidation.editCalendarEvent,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        calendarSettingsValidation.deleteCalendarEvent,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        calendarSettingsValidation.getCalendarEvents,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        calendarSettingsValidation.getCalendarMonthEvents,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = calendarSettingsRoute.router;
      const result2 = calendarSettingsRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes with method chaining', () => {
      calendarSettingsRoute.router;

      // Verify that router methods return this for chaining
      expect(router.get).toHaveReturnedWith(router);
      expect(router.put).toHaveReturnedWith(router);
    });

    it('should use validation and authentication middleware for all routes', () => {
      calendarSettingsRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;
      const putCalls = router.put.mock.calls;

      // All routes should have validation and authentication
      [...postCalls, ...getCalls, ...putCalls].forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toHaveLength(4); // path + validation + auth + controller
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof calendarSettingsRoute).toBe('object');
      expect(calendarSettingsRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(calendarSettingsRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(calendarSettingsRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
