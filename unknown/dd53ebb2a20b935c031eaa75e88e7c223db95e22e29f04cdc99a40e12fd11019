const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('../../controllers', () => ({
  BookingTemplatesController: {
    createTemplate: jest.fn(),
    getTemplates: jest.fn(),
    updateTemplate: jest.fn(),
    getTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  templatesValidation: {
    createTemplate: jest.fn(),
    getTemplates: jest.fn(),
    getTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
  },
}));

describe('bookingTemplatesRoute', () => {
  let router;
  let bookingTemplatesRoute;
  let BookingTemplatesController;
  let passportConfig;
  let templatesValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    bookingTemplatesRoute = require('../bookingTemplatesRoute');
    const controllers = require('../../controllers');
    BookingTemplatesController = controllers.BookingTemplatesController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    templatesValidation = validations.templatesValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = bookingTemplatesRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(2);
      expect(router.get).toHaveBeenCalledTimes(2);
      expect(router.put).toHaveBeenCalledTimes(1);

      // Verify POST routes
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        BookingTemplatesController.createTemplate,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        BookingTemplatesController.deleteTemplate,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        BookingTemplatesController.getTemplates,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        BookingTemplatesController.getTemplate,
      );

      // Verify PUT route
      expect(router.put).toHaveBeenNthCalledWith(
        1,
        '/',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        BookingTemplatesController.updateTemplate,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(5);
      expect(validate).toHaveBeenCalledWith(
        templatesValidation.createTemplate,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        templatesValidation.getTemplates,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        templatesValidation.getTemplate,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        templatesValidation.deleteTemplate,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = bookingTemplatesRoute.router;
      const result2 = bookingTemplatesRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes in the correct order', () => {
      bookingTemplatesRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;
      const putCalls = router.put.mock.calls;

      expect(postCalls[0][0]).toBe('/');
      expect(getCalls[0][0]).toBe('/:ProjectId');
      expect(putCalls[0][0]).toBe('/');
      expect(getCalls[1][0]).toBe('/');
      expect(postCalls[1][0]).toBe('/:ProjectId');
    });

    it('should use validation and authentication middleware for all routes', () => {
      bookingTemplatesRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;
      const putCalls = router.put.mock.calls;

      // All routes should have validation and authentication
      [...postCalls, ...getCalls, ...putCalls].forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toHaveLength(4); // path + validation + auth + controller
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof bookingTemplatesRoute).toBe('object');
      expect(bookingTemplatesRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(bookingTemplatesRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(bookingTemplatesRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
