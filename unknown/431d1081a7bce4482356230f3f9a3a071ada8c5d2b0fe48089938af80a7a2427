const moment = require('moment');
const Cryptr = require('cryptr');

// Mock the service module before requiring it
jest.mock('../../models', () => ({
    Sequelize: {
        and: jest.fn(),
        Op: {
            ne: 'ne'
        }
    },
    Enterprise: {
        findOne: jest.fn()
    },
    NotificationPreference: {
        findOne: jest.fn()
    },
    DigestNotification: {
        create: jest.fn()
    },
    Locations: {
        findOne: jest.fn()
    },
    LocationNotificationPreferences: {
        findOne: jest.fn(),
        findAll: jest.fn()
    },
    CraneRequest: {
        findOne: jest.fn(),
        findAndCountAll: jest.fn()
    },
    CraneRequestResponsiblePerson: {},
    CraneRequestComment: {
        create: jest.fn(),
        findAll: jest.fn(),
        findAndCountAll: jest.fn()
    },
    Member: {
        findOne: jest.fn()
    },
    CraneRequestHistory: {
        create: jest.fn()
    },
    User: {
        findOne: jest.fn()
    },
    DeliveryPersonNotification: {},
    Project: {},
    Notification: {}
}));

// Mock process.env
process.env.BASE_URL = 'http://localhost:3000';

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn()
}));

jest.mock('../../helpers/notificationHelper', () => ({}));

jest.mock('../../config/fcm', () => ({}));

jest.mock('../../mailer', () => ({
    sendMail: jest.fn((_payload, _template, _subject, _title, callback) => {
        callback(null, { success: true });
    })
}));

const craneRequestCommentService = require('../craneRequestCommentService');

describe('CraneRequestCommentService', () => {
    let mockModels;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common mock data
        mockModels = {
            User: {
                findOne: jest.fn()
            },
            Member: {
                findOne: jest.fn()
            },
            CraneRequest: {
                findOne: jest.fn(),
                findAndCountAll: jest.fn()
            },
            CraneRequestComment: {
                create: jest.fn(),
                findAll: jest.fn(),
                findAndCountAll: jest.fn()
            },
            CraneRequestHistory: {
                create: jest.fn()
            }
        };

        // Mock helper.returnProjectModel
        require('../../helpers/domainHelper').returnProjectModel.mockResolvedValue({
            User: mockModels.User,
            Member: mockModels.Member
        });

        // Mock helper.getDynamicModel
        require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
            CraneRequest: mockModels.CraneRequest,
            Member: mockModels.Member,
            User: mockModels.User,
            CraneRequestComment: mockModels.CraneRequestComment,
            CraneRequestHistory: mockModels.CraneRequestHistory,
            CraneRequestResponsiblePerson: {},
            Project: {},
            DeliveryPersonNotification: {},
            Notification: {}
        });
    });

    describe('getCraneRequestComments', () => {
        const mockInputData = {
            params: {
                CraneRequestId: 123,
                ProjectId: 456
            },
            body: {
                ParentCompanyId: 101
            },
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain'
            }
        };

        it('should successfully fetch comments for existing crane request', async () => {
            // Mock successful crane request lookup
            mockModels.CraneRequest.findOne.mockResolvedValue({
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456
            });

            // Mock successful comment fetch
            const mockComments = {
                rows: [
                    {
                        id: 1,
                        comment: 'Test comment',
                        Member: {
                            id: 1,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'John',
                                lastName: 'Doe',
                                profilePic: 'pic.jpg'
                            }
                        }
                    }
                ],
                count: 1
            };
            mockModels.CraneRequestComment.findAndCountAll.mockResolvedValue(mockComments);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockComments, false);
        });

        it('should return error when crane request does not exist', async () => {
            // Mock crane request not found
            mockModels.CraneRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Crane Booking id does not exist' });
        });

        it('should handle errors during comment fetch', async () => {
            // Mock error during crane request lookup
            const mockError = new Error('Database error');
            mockModels.CraneRequest.findOne.mockRejectedValue(mockError);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('createCraneRequestComment', () => {
        const mockInputData = {
            body: {
                CraneRequestId: 123,
                ProjectId: 456,
                comment: 'Test comment',
                locationId: 789,
                ParentCompanyId: 101
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe'
            }
        };

        const mockCraneRequest = {
            id: 123,
            CraneRequestId: 123,
            ProjectId: 456,
            description: 'Test crane request',
            LocationId: 789,
            memberDetails: [{
                Member: {
                    id: 2,
                    isGuestUser: false,
                    User: {
                        email: '<EMAIL>',
                        firstName: 'Jane',
                        lastName: 'Smith'
                    }
                }
            }]
        };

        it('should successfully create a comment and send notifications', async () => {
            // Mock successful crane request lookup
            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);

            // Mock member data
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });

            // Mock location data
            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);

            // Mock notification preferences
            require('../../models').NotificationPreference.findOne.mockResolvedValue({
                instant: true,
                dailyDigest: true
            });

            // Mock comment creation
            mockModels.CraneRequestComment.create.mockResolvedValue({
                id: 1,
                comment: 'Test comment'
            });

            // Mock history creation
            mockModels.CraneRequestHistory.create.mockResolvedValue({
                id: 1,
                description: 'Test notification'
            });

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(mockModels.CraneRequestComment.create).toHaveBeenCalled();
            expect(mockModels.CraneRequestHistory.create).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle non-existent crane request', async () => {
            // Mock crane request not found
            mockModels.CraneRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Crane Booking does not exist' });
        });

        it('should handle errors during comment creation', async () => {
            // Mock error during crane request lookup
            const mockError = new Error('Database error');
            mockModels.CraneRequest.findOne.mockRejectedValue(mockError);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('getMemberDetailData', () => {
        it('should correctly populate email array from member data', async () => {
            const mockData = {
                memberData: [{
                    User: {
                        email: '<EMAIL>',
                        firstName: 'John',
                        lastName: 'Doe'
                    },
                    id: 1
                }],
                adminData: [{
                    User: {
                        email: '<EMAIL>',
                        firstName: 'Admin',
                        lastName: 'User'
                    },
                    id: 2
                }]
            };

            const mockLocationPreference = [{
                Member: {
                    id: 3,
                    User: {
                        email: '<EMAIL>',
                        firstName: 'Location',
                        lastName: 'User'
                    }
                }
            }];

            const result = await craneRequestCommentService.getMemberDetailData(mockData, mockLocationPreference);

            expect(result).toHaveLength(3);
            expect(result[0].email).toBe('<EMAIL>');
            expect(result[1].email).toBe('<EMAIL>');
            expect(result[2].email).toBe('<EMAIL>');
        });

        it('should handle empty input data', async () => {
            const result = await craneRequestCommentService.getMemberDetailData({}, []);
            expect(result).toHaveLength(0);
        });
    });

    describe('createDailyDigestData', () => {
        it('should create digest notification with encrypted data', async () => {
            const mockParams = {
                requestId: 123,
                MemberId: 456,
                ProjectId: 789,
                ParentCompanyId: 101,
                loginUser: {
                    firstName: 'John',
                    lastName: 'Doe'
                },
                dailyDigestMessage: 'commented in a',
                requestType: 'Crane Request',
                messages: 'Test message'
            };

            const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
            const expectedEncryptedRequestId = cryptr.encrypt('123');

            await craneRequestCommentService.createDailyDigestData(mockParams);

            expect(require('../../models').DigestNotification.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    MemberId: 456,
                    ProjectId: 789,
                    ParentCompanyId: 101,
                    isSent: false,
                    isDeleted: false,
                    description: expect.stringContaining(expectedEncryptedRequestId)
                })
            );
        });
    });

    describe('getDynamicModel', () => {
        const mockInputData = {
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain'
            },
            body: {
                ParentCompanyId: 123
            }
        };

        beforeEach(() => {
            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });
        });

        it('should successfully initialize dynamic models with domain name', async () => {
            require('../../models').Enterprise.findOne.mockResolvedValue({
                name: 'testdomain'
            });

            const result = await craneRequestCommentService.getDynamicModel(mockInputData);

            expect(result).toBe(true);
            expect(require('../../helpers/domainHelper').getDynamicModel).toHaveBeenCalledWith('testdomain');
        });

        it('should handle missing domain name and use ParentCompanyId', async () => {
            const inputWithoutDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 123 }
            };

            require('../../models').Enterprise.findOne.mockResolvedValue(null);
            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                isAccount: true,
                EnterpriseId: 456
            });
            require('../../models').Enterprise.findOne
                .mockResolvedValueOnce(null) // First call for domain name
                .mockResolvedValueOnce({ name: 'enterprise123' }); // Second call for enterprise

            const result = await craneRequestCommentService.getDynamicModel(inputWithoutDomain);

            expect(result).toBe(true);
        });

        it('should handle undefined ParentCompanyId', async () => {
            const inputWithUndefinedParent = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 'undefined' }
            };

            require('../../models').Enterprise.findOne.mockResolvedValue(null);

            const result = await craneRequestCommentService.getDynamicModel(inputWithUndefinedParent);

            expect(result).toBe(true);
        });

        it('should handle missing user email', async () => {
            const inputWithoutEmail = {
                user: {},
                body: { ParentCompanyId: 123 }
            };

            require('../../models').Enterprise.findOne.mockResolvedValue(null);

            const result = await craneRequestCommentService.getDynamicModel(inputWithoutEmail);

            expect(result).toBe(true);
        });
    });

    describe('createCraneRequestComment - Additional Edge Cases', () => {
        const mockInputData = {
            body: {
                CraneRequestId: 123,
                ProjectId: 456,
                comment: 'Test comment',
                locationId: 789,
                ParentCompanyId: 101
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe'
            }
        };

        it('should handle missing member data', async () => {
            const mockCraneRequest = {
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456,
                description: 'Test crane request',
                LocationId: 789
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue(null);

            const done = jest.fn();

            try {
                await craneRequestCommentService.createCraneRequestComment(mockInputData, done);
            } catch (error) {
                expect(done).toHaveBeenCalledWith(null, error);
            }
        });

        it('should handle missing location data', async () => {
            const mockCraneRequest = {
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456,
                description: 'Test crane request',
                LocationId: 789
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });
            require('../../models').Locations.findOne.mockResolvedValue(null);

            const done = jest.fn();

            try {
                await craneRequestCommentService.createCraneRequestComment(mockInputData, done);
            } catch (error) {
                expect(done).toHaveBeenCalledWith(null, error);
            }
        });

        it('should handle comment creation failure', async () => {
            const mockCraneRequest = {
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456,
                description: 'Test crane request',
                LocationId: 789
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });
            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);

            const createError = new Error('Comment creation failed');
            mockModels.CraneRequestComment.create.mockRejectedValue(createError);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, createError);
        });

        it('should handle history creation failure', async () => {
            const mockCraneRequest = {
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456,
                description: 'Test crane request',
                LocationId: 789
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });
            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.CraneRequestComment.create.mockResolvedValue({
                id: 1,
                comment: 'Test comment'
            });

            const historyError = new Error('History creation failed');
            mockModels.CraneRequestHistory.create.mockRejectedValue(historyError);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, historyError);
        });
    });

    // Test helper functions
    describe('Helper Functions', () => {
        describe('fetchPreviousComments', () => {
            it('should fetch and format previous comments', async () => {
                const mockComments = [
                    { id: 1, comment: 'Comment 1' },
                    { id: 2, comment: 'Comment 2' }
                ];

                require('../../models').CraneRequestComment.findAll.mockResolvedValue(mockComments);

                const result = await craneRequestCommentService.fetchPreviousComments(123, 456);

                expect(result).toBe('"Comment 1","Comment 2"');
            });

            it('should return empty string when no comments exist', async () => {
                require('../../models').CraneRequestComment.findAll.mockResolvedValue([]);

                const result = await craneRequestCommentService.fetchPreviousComments(123, 456);

                expect(result).toBe('');
            });

            it('should handle database error during comment fetch', async () => {
                const fetchError = new Error('Database connection failed');
                require('../../models').CraneRequestComment.findAll.mockRejectedValue(fetchError);

                try {
                    await craneRequestCommentService.fetchPreviousComments(123, 456);
                } catch (error) {
                    expect(error).toBe(fetchError);
                }
            });
        });

        describe('getRequestDetails', () => {
            it('should return correct link for crane request', () => {
                const result = craneRequestCommentService.getRequestDetails('Crane Request');
                expect(result).toEqual({ link: 'crane-request' });
            });

            it('should return correct link for delivery request', () => {
                const result = craneRequestCommentService.getRequestDetails('Delivery Request');
                expect(result).toEqual({ link: 'delivery-request' });
            });

            it('should return correct link for concrete request', () => {
                const result = craneRequestCommentService.getRequestDetails('Concrete Request');
                expect(result).toEqual({ link: 'concrete-request' });
            });

            it('should return undefined for invalid request type', () => {
                const result = craneRequestCommentService.getRequestDetails('Invalid Type');
                expect(result).toBeUndefined();
            });

            it('should return undefined for null request type', () => {
                const result = craneRequestCommentService.getRequestDetails(null);
                expect(result).toBeUndefined();
            });

            it('should return undefined for empty string request type', () => {
                const result = craneRequestCommentService.getRequestDetails('');
                expect(result).toBeUndefined();
            });
        });
    });

    describe('getMemberDetailData - Extended Cases', () => {
        it('should handle mixed data types correctly', async () => {
            const mockData = {
                memberData: [
                    {
                        User: {
                            email: '<EMAIL>',
                            firstName: 'John',
                            lastName: 'Doe',
                            id: 1
                        },
                        id: 1
                    }
                ],
                adminData: [
                    {
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Admin',
                            lastName: 'User',
                            id: 2
                        },
                        id: 2
                    }
                ]
            };

            const mockLocationPreference = [
                {
                    Member: {
                        id: 3,
                        RoleId: 5,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Location',
                            lastName: 'User',
                            id: 3
                        }
                    }
                }
            ];

            const result = await craneRequestCommentService.getMemberDetailData(mockData, mockLocationPreference);

            expect(result).toHaveLength(3);
            expect(result[0]).toEqual({
                email: '<EMAIL>',
                firstName: 'John',
                UserId: 1,
                MemberId: 1,
                RoleId: undefined
            });
            expect(result[2]).toEqual({
                email: '<EMAIL>',
                firstName: 'Location',
                UserId: 3,
                MemberId: 3,
                RoleId: 5
            });
        });

        it('should handle duplicate emails correctly', async () => {
            const mockData = {
                memberData: [
                    {
                        User: {
                            email: '<EMAIL>',
                            firstName: 'John',
                            lastName: 'Doe',
                            id: 1
                        },
                        id: 1
                    }
                ],
                adminData: [
                    {
                        User: {
                            email: '<EMAIL>',
                            firstName: 'John',
                            lastName: 'Doe',
                            id: 1
                        },
                        id: 1
                    }
                ]
            };

            const result = await craneRequestCommentService.getMemberDetailData(mockData, []);

            expect(result).toHaveLength(1);
            expect(result[0].email).toBe('<EMAIL>');
        });

        it('should handle null/undefined data gracefully', async () => {
            const mockData = {
                memberData: null,
                adminData: undefined
            };

            const result = await craneRequestCommentService.getMemberDetailData(mockData, null);

            expect(result).toHaveLength(0);
        });

        it('should handle empty arrays', async () => {
            const mockData = {
                memberData: [],
                adminData: []
            };

            const result = await craneRequestCommentService.getMemberDetailData(mockData, []);

            expect(result).toHaveLength(0);
        });
    });

    describe('createDailyDigestData - Extended Cases', () => {
        it('should handle different request types', async () => {
            const mockParams = {
                requestId: 123,
                MemberId: 456,
                ProjectId: 789,
                ParentCompanyId: 101,
                loginUser: {
                    firstName: 'John',
                    lastName: 'Doe'
                },
                dailyDigestMessage: 'commented in a',
                requestType: 'Delivery Request',
                messages: 'Test delivery message'
            };

            await craneRequestCommentService.createDailyDigestData(mockParams);

            expect(require('../../models').DigestNotification.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    MemberId: 456,
                    ProjectId: 789,
                    ParentCompanyId: 101,
                    isSent: false,
                    isDeleted: false,
                    description: expect.stringContaining('delivery-request')
                })
            );
        });

        it('should handle concrete request type', async () => {
            const mockParams = {
                requestId: 123,
                MemberId: 456,
                ProjectId: 789,
                ParentCompanyId: 101,
                loginUser: {
                    firstName: 'Jane',
                    lastName: 'Smith'
                },
                dailyDigestMessage: 'updated',
                requestType: 'Concrete Request',
                messages: 'Test concrete message'
            };

            await craneRequestCommentService.createDailyDigestData(mockParams);

            expect(require('../../models').DigestNotification.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    description: expect.stringContaining('concrete-request')
                })
            );
        });

        it('should handle digest creation failure', async () => {
            const mockParams = {
                requestId: 123,
                MemberId: 456,
                ProjectId: 789,
                ParentCompanyId: 101,
                loginUser: {
                    firstName: 'John',
                    lastName: 'Doe'
                },
                dailyDigestMessage: 'commented in a',
                requestType: 'Crane Request',
                messages: 'Test message'
            };

            const digestError = new Error('Digest creation failed');
            require('../../models').DigestNotification.create.mockRejectedValue(digestError);

            try {
                await craneRequestCommentService.createDailyDigestData(mockParams);
            } catch (error) {
                expect(error).toBe(digestError);
            }
        });
    });

    describe('Notification and Mail Handling', () => {
        const mockInputData = {
            body: {
                CraneRequestId: 123,
                ProjectId: 456,
                comment: 'Test comment',
                locationId: 789,
                ParentCompanyId: 101
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe'
            }
        };

        const mockCraneRequest = {
            id: 123,
            CraneRequestId: 123,
            ProjectId: 456,
            description: 'Test crane request',
            LocationId: 789,
            craneDeliveryStart: '2024-01-01',
            craneDeliveryEnd: '2024-01-02'
        };

        it('should handle notification preferences with instant mail enabled', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });

            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });

            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([
                {
                    Member: {
                        id: 2,
                        RoleId: 3,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Jane',
                            lastName: 'Smith',
                            id: 2
                        }
                    }
                }
            ]);

            // Mock location notification preferences check
            require('../../models').LocationNotificationPreferences.findOne.mockResolvedValue({
                id: 1,
                MemberId: 2,
                ProjectId: 456,
                LocationId: 789
            });

            // Mock notification preferences with instant enabled
            require('../../models').NotificationPreference.findOne.mockResolvedValue({
                instant: true,
                dailyDigest: false,
                NotificationPreferenceItem: [{ id: 7 }]
            });

            mockModels.CraneRequestComment.create.mockResolvedValue({
                id: 1,
                comment: 'Test comment'
            });

            mockModels.CraneRequestHistory.create.mockResolvedValue({
                id: 1,
                description: 'Test notification'
            });

            require('../../models').CraneRequestComment.findAll.mockResolvedValue([
                { id: 1, comment: 'Previous comment' }
            ]);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(require('../../mailer').sendMail).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle notification preferences with daily digest enabled', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });

            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });

            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([
                {
                    Member: {
                        id: 2,
                        RoleId: 3,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Jane',
                            lastName: 'Smith',
                            id: 2
                        }
                    }
                }
            ]);

            require('../../models').LocationNotificationPreferences.findOne.mockResolvedValue({
                id: 1,
                MemberId: 2,
                ProjectId: 456,
                LocationId: 789
            });

            require('../../models').NotificationPreference.findOne.mockResolvedValue({
                instant: false,
                dailyDigest: true,
                NotificationPreferenceItem: [{ id: 7 }]
            });

            mockModels.CraneRequestComment.create.mockResolvedValue({
                id: 1,
                comment: 'Test comment'
            });

            mockModels.CraneRequestHistory.create.mockResolvedValue({
                id: 1,
                description: 'Test notification'
            });

            require('../../models').CraneRequestComment.findAll.mockResolvedValue([]);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(require('../../models').DigestNotification.create).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle no notification preferences found', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });

            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });

            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([
                {
                    Member: {
                        id: 2,
                        RoleId: 3,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Jane',
                            lastName: 'Smith',
                            id: 2
                        }
                    }
                }
            ]);

            // Mock no location notification preferences
            require('../../models').LocationNotificationPreferences.findOne.mockResolvedValue(null);

            mockModels.CraneRequestComment.create.mockResolvedValue({
                id: 1,
                comment: 'Test comment'
            });

            mockModels.CraneRequestHistory.create.mockResolvedValue({
                id: 1,
                description: 'Test notification'
            });

            require('../../models').CraneRequestComment.findAll.mockResolvedValue([]);

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            // Should not send mail or create digest when no preferences found
            expect(require('../../mailer').sendMail).not.toHaveBeenCalled();
            expect(require('../../models').DigestNotification.create).not.toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle mail sending failure gracefully', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1
            });

            require('../../models').Locations.findOne.mockResolvedValue({
                id: 789,
                locationPath: 'Test Location'
            });

            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([
                {
                    Member: {
                        id: 2,
                        RoleId: 3,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Jane',
                            lastName: 'Smith',
                            id: 2
                        }
                    }
                }
            ]);

            require('../../models').LocationNotificationPreferences.findOne.mockResolvedValue({
                id: 1,
                MemberId: 2,
                ProjectId: 456,
                LocationId: 789
            });

            require('../../models').NotificationPreference.findOne.mockResolvedValue({
                instant: true,
                dailyDigest: false,
                NotificationPreferenceItem: [{ id: 7 }]
            });

            mockModels.CraneRequestComment.create.mockResolvedValue({
                id: 1,
                comment: 'Test comment'
            });

            mockModels.CraneRequestHistory.create.mockResolvedValue({
                id: 1,
                description: 'Test notification'
            });

            require('../../models').CraneRequestComment.findAll.mockResolvedValue([]);

            // Mock mail sending failure
            require('../../mailer').sendMail.mockImplementation((_payload, _template, _subject, _title, callback) => {
                callback('Mail sending failed', null);
            });

            const done = jest.fn();
            await craneRequestCommentService.createCraneRequestComment(mockInputData, done);

            expect(require('../../mailer').sendMail).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });
    });

    describe('Enterprise and Domain Handling', () => {
        beforeEach(() => {
            // Reset mocks for enterprise tests
            jest.clearAllMocks();
            require('../../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: mockModels.User,
                Member: mockModels.Member
            });
        });

        it('should handle enterprise lookup with valid domain name', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'validDomain'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            require('../../models').Enterprise.findOne.mockResolvedValue({
                name: 'validDomain'
            });

            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });

            const result = await craneRequestCommentService.getDynamicModel(inputData);

            expect(result).toBe(true);
            expect(require('../../models').Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'validdomain' }
            });
        });

        it('should handle enterprise lookup with ParentCompanyId when domain not found', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'invalidDomain'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            // Mock enterprise not found for domain
            require('../../models').Enterprise.findOne
                .mockResolvedValueOnce(null) // First call for domain name
                .mockResolvedValueOnce({ name: 'parentCompanyEnterprise' }); // Second call for parent company

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                isAccount: false,
                EnterpriseId: 456
            });

            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });

            const result = await craneRequestCommentService.getDynamicModel(inputData);

            expect(result).toBe(true);
        });

        it('should handle member with account enterprise', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            require('../../models').Enterprise.findOne
                .mockResolvedValueOnce(null) // First call for domain name
                .mockResolvedValueOnce({ name: 'accountEnterprise' }); // Second call for account enterprise

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                isAccount: true,
                EnterpriseId: 456
            });

            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });

            const result = await craneRequestCommentService.getDynamicModel(inputData);

            expect(result).toBe(true);
            expect(require('../../models').Enterprise.findOne).toHaveBeenCalledWith({
                where: { EnterpriseId: 456, status: 'completed' }
            });
        });

        it('should handle member not found scenario', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            require('../../models').Enterprise.findOne
                .mockResolvedValueOnce(null) // First call for domain name
                .mockResolvedValueOnce({ name: 'fallbackEnterprise' }); // Second call for fallback

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            mockModels.Member.findOne.mockResolvedValue(null);

            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });

            const result = await craneRequestCommentService.getDynamicModel(inputData);

            expect(result).toBe(true);
            expect(require('../../models').Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 123, status: 'completed' }
            });
        });

        it('should handle user not found scenario', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            require('../../models').Enterprise.findOne
                .mockResolvedValueOnce(null) // First call for domain name
                .mockResolvedValueOnce({ name: 'fallbackEnterprise' }); // Second call for fallback

            mockModels.User.findOne.mockResolvedValue(null);

            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });

            const result = await craneRequestCommentService.getDynamicModel(inputData);

            expect(result).toBe(true);
        });

        it('should handle empty domain name', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: ''
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            require('../../models').Enterprise.findOne
                .mockResolvedValueOnce('') // First call returns empty string
                .mockResolvedValueOnce({ name: 'parentEnterprise' }); // Second call for parent company

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                isAccount: false
            });

            require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                CraneRequest: mockModels.CraneRequest,
                Member: mockModels.Member,
                User: mockModels.User,
                CraneRequestComment: mockModels.CraneRequestComment,
                CraneRequestHistory: mockModels.CraneRequestHistory
            });

            const result = await craneRequestCommentService.getDynamicModel(inputData);

            expect(result).toBe(true);
        });
    });

    describe('getCraneRequestComments - Additional Edge Cases', () => {
        const mockInputData = {
            params: {
                CraneRequestId: 123,
                ProjectId: 456
            },
            user: {
                email: '<EMAIL>'
            }
        };

        it('should handle comments with missing member associations', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue({
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456
            });

            const mockComments = {
                rows: [
                    {
                        id: 1,
                        comment: 'Test comment',
                        Member: null // Missing member association
                    },
                    {
                        id: 2,
                        comment: 'Another comment',
                        Member: {
                            id: 1,
                            User: null // Missing user association
                        }
                    }
                ],
                count: 2
            };
            mockModels.CraneRequestComment.findAndCountAll.mockResolvedValue(mockComments);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockComments, false);
        });

        it('should handle empty comments result', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue({
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456
            });

            const mockComments = {
                rows: [],
                count: 0
            };
            mockModels.CraneRequestComment.findAndCountAll.mockResolvedValue(mockComments);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockComments, false);
        });

        it('should handle error during comment retrieval', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue({
                id: 123,
                CraneRequestId: 123,
                ProjectId: 456
            });

            const commentError = new Error('Comment retrieval failed');
            mockModels.CraneRequestComment.findAndCountAll.mockRejectedValue(commentError);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, commentError);
        });

        it('should handle invalid CraneRequestId parameter', async () => {
            const invalidInputData = {
                params: {
                    CraneRequestId: 'invalid',
                    ProjectId: 456
                },
                user: {
                    email: '<EMAIL>'
                }
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(invalidInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Crane Booking id does not exist' });
        });

        it('should handle invalid ProjectId parameter', async () => {
            const invalidInputData = {
                params: {
                    CraneRequestId: 123,
                    ProjectId: 'invalid'
                },
                user: {
                    email: '<EMAIL>'
                }
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(invalidInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Crane Booking id does not exist' });
        });

        it('should handle getDynamicModel failure', async () => {
            const dynamicModelError = new Error('Dynamic model initialization failed');
            require('../../helpers/domainHelper').returnProjectModel.mockRejectedValue(dynamicModelError);

            const done = jest.fn();
            await craneRequestCommentService.getCraneRequestComments(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, dynamicModelError);
        });
    });

    describe('Error Handling and Edge Cases', () => {
        it('should handle returnProjectModel failure', async () => {
            const modelError = new Error('Project model retrieval failed');
            require('../../helpers/domainHelper').returnProjectModel.mockRejectedValue(modelError);

            try {
                await craneRequestCommentService.returnProjectModel();
            } catch (error) {
                expect(error).toBe(modelError);
            }
        });

        it('should handle getDynamicModel helper failure', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            require('../../models').Enterprise.findOne.mockResolvedValue({
                name: 'testdomain'
            });

            const dynamicModelError = new Error('Dynamic model helper failed');
            require('../../helpers/domainHelper').getDynamicModel.mockRejectedValue(dynamicModelError);

            try {
                await craneRequestCommentService.getDynamicModel(inputData);
            } catch (error) {
                expect(error).toBe(dynamicModelError);
            }
        });

        it('should handle enterprise lookup failure', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain'
                },
                body: {
                    ParentCompanyId: 123
                }
            };

            const enterpriseError = new Error('Enterprise lookup failed');
            require('../../models').Enterprise.findOne.mockRejectedValue(enterpriseError);

            try {
                await craneRequestCommentService.getDynamicModel(inputData);
            } catch (error) {
                expect(error).toBe(enterpriseError);
            }
        });
    });
});