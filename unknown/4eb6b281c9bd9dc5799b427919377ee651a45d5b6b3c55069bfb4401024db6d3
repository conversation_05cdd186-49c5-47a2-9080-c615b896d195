// Mock craneRequestAttachmentService
jest.mock('../../services', () => ({
  craneRequestAttachmentService: {
    createCraneRequestAttachement: jest.fn(),
    getCraneRequestAttachements: jest.fn(),
    deleteCraneRequestAttachement: jest.fn(),
  },
}));

const craneRequestAttachmentController = require('../craneRequestAttachmentController');
const { craneRequestAttachmentService } = require('../../services');

describe('craneRequestAttachmentController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createCraneRequestAttachement', () => {
    it('should create crane request attachment successfully', async () => {
      const mockResponse = { id: 1, filename: 'crane_document.pdf', size: 1024 };

      craneRequestAttachmentService.createCraneRequestAttachement.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestAttachmentController.createCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.createCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create crane request attachment', async () => {
      const mockError = new Error('Service error');
      craneRequestAttachmentService.createCraneRequestAttachement.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestAttachmentController.createCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.createCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create crane request attachment', async () => {
      const mockError = new Error('Exception error');
      craneRequestAttachmentService.createCraneRequestAttachement.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestAttachmentController.createCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.createCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getCraneRequestAttachements', () => {
    it('should get crane request attachments successfully', async () => {
      const mockResponse = [
        { id: 1, filename: 'crane_doc1.pdf' },
        { id: 2, filename: 'crane_doc2.pdf' },
      ];

      craneRequestAttachmentService.getCraneRequestAttachements.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestAttachmentController.getCraneRequestAttachements(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.getCraneRequestAttachements).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Attachment Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get crane request attachments', async () => {
      const mockError = new Error('Service error');
      craneRequestAttachmentService.getCraneRequestAttachements.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestAttachmentController.getCraneRequestAttachements(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.getCraneRequestAttachements).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get crane request attachments', async () => {
      const mockError = new Error('Exception error');
      craneRequestAttachmentService.getCraneRequestAttachements.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestAttachmentController.getCraneRequestAttachements(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.getCraneRequestAttachements).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteCraneRequestAttachement', () => {
    it('should delete crane request attachment successfully', async () => {
      const mockResponse = { deleted: true, id: 1 };

      craneRequestAttachmentService.deleteCraneRequestAttachement.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestAttachmentController.deleteCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.deleteCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Attachment Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delete crane request attachment', async () => {
      const mockError = new Error('Service error');
      craneRequestAttachmentService.deleteCraneRequestAttachement.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestAttachmentController.deleteCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.deleteCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delete crane request attachment', async () => {
      const mockError = new Error('Exception error');
      craneRequestAttachmentService.deleteCraneRequestAttachement.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestAttachmentController.deleteCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(craneRequestAttachmentService.deleteCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
