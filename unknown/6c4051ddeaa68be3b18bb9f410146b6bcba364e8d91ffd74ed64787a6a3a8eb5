const status = require('http-status');
const BookingTemplatesController = require('../BookingTemplatesController');
const { bookingTemplatesService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  bookingTemplatesService: {
    createTemplate: jest.fn(),
    getTemplates: jest.fn(),
    updateTemplate: jest.fn(),
    getTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
  },
}));

describe('BookingTemplatesController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      user: { id: 1 },
      body: {},
      params: {},
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createTemplate', () => {
    it('should create template successfully', async () => {
      const mockResponse = { id: 1, template_name: 'Test Template' };
      bookingTemplatesService.createTemplate.mockResolvedValue(mockResponse);

      await BookingTemplatesController.createTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.createTemplate).toHaveBeenCalledWith(
        mockReq.user,
        mockReq.body,
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Booking Template Created Successfully!',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service returning false', async () => {
      bookingTemplatesService.createTemplate.mockResolvedValue(false);

      await BookingTemplatesController.createTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.createTemplate).toHaveBeenCalledWith(
        mockReq.user,
        mockReq.body,
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.INTERNAL_SERVER_ERROR);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot create booking template',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      const mockError = new Error('Service error');
      bookingTemplatesService.createTemplate.mockRejectedValue(mockError);

      await BookingTemplatesController.createTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.createTemplate).toHaveBeenCalledWith(
        mockReq.user,
        mockReq.body,
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getTemplates', () => {
    it('should get templates successfully', async () => {
      const mockResponse = [
        { id: 1, template_name: 'Template 1' },
        { id: 2, template_name: 'Template 2' },
      ];
      bookingTemplatesService.getTemplates.mockResolvedValue(mockResponse);

      await BookingTemplatesController.getTemplates(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.getTemplates).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Booking Templates listed Successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      const mockError = new Error('Service error');
      bookingTemplatesService.getTemplates.mockRejectedValue(mockError);

      await BookingTemplatesController.getTemplates(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.getTemplates).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('updateTemplate', () => {
    it('should update template successfully', async () => {
      const mockResponse = { id: 1, template_name: 'Updated Template' };
      bookingTemplatesService.updateTemplate.mockResolvedValue(mockResponse);

      await BookingTemplatesController.updateTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.updateTemplate).toHaveBeenCalledWith(
        mockReq.user,
        mockReq.body,
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Booking Template Updated Successfully!',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service returning false', async () => {
      bookingTemplatesService.updateTemplate.mockResolvedValue(false);

      await BookingTemplatesController.updateTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.updateTemplate).toHaveBeenCalledWith(
        mockReq.user,
        mockReq.body,
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.INTERNAL_SERVER_ERROR);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 400,
        message: 'Cannot update booking template',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      const mockError = new Error('Service error');
      bookingTemplatesService.updateTemplate.mockRejectedValue(mockError);

      await BookingTemplatesController.updateTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.updateTemplate).toHaveBeenCalledWith(
        mockReq.user,
        mockReq.body,
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getTemplate', () => {
    it('should get template successfully', async () => {
      const mockResponse = { id: 1, template_name: 'Test Template' };
      bookingTemplatesService.getTemplate.mockResolvedValue(mockResponse);

      await BookingTemplatesController.getTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.getTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Booking Template listed Successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      const mockError = new Error('Service error');
      bookingTemplatesService.getTemplate.mockRejectedValue(mockError);

      await BookingTemplatesController.getTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.getTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteTemplate', () => {
    it('should delete template successfully', async () => {
      const mockResponse = { deleted: true };
      bookingTemplatesService.deleteTemplate.mockResolvedValue(mockResponse);

      await BookingTemplatesController.deleteTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.deleteTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Booking Template deleted Successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      const mockError = new Error('Service error');
      bookingTemplatesService.deleteTemplate.mockRejectedValue(mockError);

      await BookingTemplatesController.deleteTemplate(mockReq, mockRes, mockNext);

      expect(bookingTemplatesService.deleteTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
