const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  return multer;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  inspectionController: {
    newRequest: jest.fn(),
    editRequest: jest.fn(),
    updateNDRStatus: jest.fn(),
    updateDeliveredStatus: jest.fn(),
    listNDR: jest.fn(),
    getNDRData: jest.fn(),
    getMemberData: jest.fn(),
    sampleBulkinspectionRequestTemplate: jest.fn(),
    bulkUploadinspectionRequest: jest.fn(),
    deleteQueuedNdr: jest.fn(),
    editMultipleinspectionRequest: jest.fn(),
    getLastinspectionRequestId: jest.fn(),
    decryption: jest.fn(),
    setReadAllNotification: jest.fn(),
    markAllNotification: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  inspectionValidation: {
    newRequest: jest.fn(),
    updateQueuedRequest: jest.fn(),
    updateNDRStatus: jest.fn(),
    listNDR: jest.fn(),
    getMemberData: jest.fn(),
    uploadBulkNDRTemplate: jest.fn(),
    bulkUploadinspectionRequest: jest.fn(),
  },
}));

describe('inspectionRoute', () => {
  let router;
  let inspectionRoute;
  let inspectionController;
  let passportConfig;
  let inspectionValidation;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    inspectionRoute = require('../inspectionRoute');
    const controllers = require('../../controllers');
    inspectionController = controllers.inspectionController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    inspectionValidation = validations.inspectionValidation;
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = inspectionRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer).toHaveBeenCalledWith({ dest: 'uploads/' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(13);
      expect(router.get).toHaveBeenCalledTimes(5);

      // Verify POST routes with validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/new_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.newRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/edit_request',
        passportConfig.isAuthenticated,
        inspectionController.editRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/edit_queued_request',
        passportConfig.isAuthenticated,
        inspectionController.editRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/update_to_current_NDR',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.editRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/update_status',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.updateNDRStatus,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/update_existing_status',
        inspectionController.updateDeliveredStatus,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        7,
        '/list_NDR/:ProjectId/:pageSize/:pageNo/:void',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.listNDR,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        8,
        '/list_Queued_NDR/:ProjectId/:pageSize/:pageNo/:void',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.listNDR,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        9,
        '/sample_inspection_request_template/?:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.sampleBulkinspectionRequestTemplate,
      );

      // POST route with multer
      expect(router.post).toHaveBeenNthCalledWith(
        10,
        '/bulk_upload_inspection_request/?:ProjectId/?:ParentCompanyId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.bulkUploadinspectionRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        11,
        '/delete_queued_Ndr',
        passportConfig.isAuthenticated,
        inspectionController.deleteQueuedNdr,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        12,
        '/edit_multiple_request',
        passportConfig.isAuthenticated,
        inspectionController.editMultipleinspectionRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        13,
        '/decrypt',
        passportConfig.isAuthenticated,
        inspectionController.decryption,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_single_NDR/:inspectionRequestId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        inspectionController.getNDRData,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_user_role/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        inspectionController.getMemberData,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/get_last_inspection_request_id/:ProjectId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        inspectionController.getLastinspectionRequestId,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        4,
        '/readall_notification',
        passportConfig.isAuthenticated,
        inspectionController.setReadAllNotification,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        5,
        '/markall_notification/:ProjectId',
        passportConfig.isAuthenticated,
        inspectionController.markAllNotification,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(8);
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.newRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.updateQueuedRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.updateNDRStatus,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.listNDR,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.getMemberData,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.uploadBulkNDRTemplate,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        inspectionValidation.bulkUploadinspectionRequest,
        { keyByField: true },
        { abortEarly: false },
      );

      // Verify multer single method is called
      const uploadInstance = multer.mock.results[0].value;
      expect(uploadInstance.single).toHaveBeenCalledWith('inspection_request');
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = inspectionRoute.router;
      const result2 = inspectionRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for most routes', () => {
      inspectionRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Most routes should have authentication (except update_existing_status)
      const routesWithAuth = [
        ...postCalls.slice(0, 5), // first 5 POST routes
        ...postCalls.slice(6), // POST routes after update_existing_status
        ...getCalls
      ];

      routesWithAuth.forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure different middleware combinations correctly', () => {
      inspectionRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Routes with validation + auth (4 parameters)
      const routesWithValidation = [
        postCalls[0], // new_request
        postCalls[3], // update_to_current_NDR
        postCalls[4], // update_status
        postCalls[6], // list_NDR
        postCalls[7], // list_Queued_NDR
        postCalls[8], // sample_inspection_request_template
        getCalls[1],  // get_user_role
      ];

      routesWithValidation.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // Route with multer + validation + auth (5 parameters)
      expect(postCalls[9]).toHaveLength(5); // bulk_upload_inspection_request
      expect(postCalls[9][1]).toBe('mocked-multer-middleware');
      expect(postCalls[9][2]).toBe('mocked-validate-middleware');

      // Routes with auth only (3 parameters)
      const routesWithAuthOnly = [
        postCalls[1], postCalls[2], // edit_request, edit_queued_request
        postCalls[10], postCalls[11], postCalls[12], // delete_queued_Ndr, edit_multiple_request, decrypt
        getCalls[0], getCalls[2], getCalls[3], getCalls[4] // GET routes without validation
      ];

      routesWithAuthOnly.forEach(call => {
        expect(call).toHaveLength(3); // path + auth + controller
        expect(call[1]).toBe(passportConfig.isAuthenticated);
      });

      // Route with no middleware (1 parameter)
      expect(postCalls[5]).toHaveLength(2); // update_existing_status
      expect(postCalls[5][0]).toBe('/update_existing_status');
      expect(postCalls[5][1]).toBe(inspectionController.updateDeliveredStatus);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof inspectionRoute).toBe('object');
      expect(inspectionRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(inspectionRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(inspectionRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
