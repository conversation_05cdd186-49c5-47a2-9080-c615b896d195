// Mock craneRequestCommentService
jest.mock('../../services', () => ({
  craneRequestCommentService: {
    createCraneRequestComment: jest.fn(),
    getCraneRequestComments: jest.fn(),
  },
}));

const craneRequestCommentController = require('../craneRequestCommentController');
const { craneRequestCommentService } = require('../../services');

describe('craneRequestCommentController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createCraneRequestComment', () => {
    it('should create crane request comment successfully', async () => {
      const mockResponse = { id: 1, content: 'Crane request comment', userId: 123 };

      craneRequestCommentService.createCraneRequestComment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestCommentController.createCraneRequestComment(mockReq, mockRes, mockNext);

      expect(craneRequestCommentService.createCraneRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create crane request comment', async () => {
      const mockError = new Error('Service error');
      craneRequestCommentService.createCraneRequestComment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestCommentController.createCraneRequestComment(mockReq, mockRes, mockNext);

      expect(craneRequestCommentService.createCraneRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create crane request comment', async () => {
      const mockError = new Error('Exception error');
      craneRequestCommentService.createCraneRequestComment.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestCommentController.createCraneRequestComment(mockReq, mockRes, mockNext);

      expect(craneRequestCommentService.createCraneRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getCraneRequestComments', () => {
    it('should get crane request comments successfully', async () => {
      const mockResponse = [
        { id: 1, content: 'Comment 1' },
        { id: 2, content: 'Comment 2' },
      ];

      craneRequestCommentService.getCraneRequestComments.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestCommentController.getCraneRequestComments(mockReq, mockRes, mockNext);

      expect(craneRequestCommentService.getCraneRequestComments).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Comment Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get crane request comments', async () => {
      const mockError = new Error('Service error');
      craneRequestCommentService.getCraneRequestComments.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestCommentController.getCraneRequestComments(mockReq, mockRes, mockNext);

      expect(craneRequestCommentService.getCraneRequestComments).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get crane request comments', async () => {
      const mockError = new Error('Exception error');
      craneRequestCommentService.getCraneRequestComments.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestCommentController.getCraneRequestComments(mockReq, mockRes, mockNext);

      expect(craneRequestCommentService.getCraneRequestComments).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
