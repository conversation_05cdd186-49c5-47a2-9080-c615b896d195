// Mock voidService
jest.mock('../../services', () => ({
  voidService: {
    createVoidList: jest.fn(),
    createInspectionVoidList: jest.fn(),
    removeVoidList: jest.fn(),
    createCraneRequestVoid: jest.fn(),
    createConcreteRequestVoid: jest.fn(),
    getVoidList: jest.fn(),
    createMultipleVoidList: jest.fn(),
  },
}));

const voidController = require('../voidController');
const { voidService } = require('../../services');

describe('voidController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createVoidList', () => {
    it('should create void list successfully', async () => {
      const mockResponse = { id: 1, status: 'voided', bookingId: 'book_123' };

      voidService.createVoidList.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.createVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Marked as Void Successfully.',
        data: mockResponse,
        status: 201,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create void list', async () => {
      const mockError = new Error('Service error');
      voidService.createVoidList.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.createVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create void list', async () => {
      const mockError = new Error('Exception error');
      voidService.createVoidList.mockImplementation(() => {
        throw mockError;
      });

      await voidController.createVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createInspectionVoidList', () => {
    it('should create inspection void list successfully', async () => {
      const mockResponse = { id: 1, status: 'voided', inspectionId: 'insp_123' };

      voidService.createInspectionVoidList.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.createInspectionVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createInspectionVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Marked as Void Successfully.',
        data: mockResponse,
        status: 201,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create inspection void list', async () => {
      const mockError = new Error('Service error');
      voidService.createInspectionVoidList.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.createInspectionVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createInspectionVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create inspection void list', async () => {
      const mockError = new Error('Exception error');
      voidService.createInspectionVoidList.mockImplementation(() => {
        throw mockError;
      });

      await voidController.createInspectionVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createInspectionVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('removeVoidList', () => {
    it('should remove void list successfully', async () => {
      const mockResponse = { id: 1, status: 'restored', bookingId: 'book_123' };

      voidService.removeVoidList.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.removeVoidList(mockReq, mockRes, mockNext);

      expect(voidService.removeVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Booking Restored Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from remove void list', async () => {
      const mockError = new Error('Service error');
      voidService.removeVoidList.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.removeVoidList(mockReq, mockRes, mockNext);

      expect(voidService.removeVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in remove void list', async () => {
      const mockError = new Error('Exception error');
      voidService.removeVoidList.mockImplementation(() => {
        throw mockError;
      });

      await voidController.removeVoidList(mockReq, mockRes, mockNext);

      expect(voidService.removeVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createCraneRequestVoid', () => {
    it('should create crane request void successfully', async () => {
      const mockResponse = { id: 1, status: 'voided', craneRequestId: 'crane_123' };

      voidService.createCraneRequestVoid.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.createCraneRequestVoid(mockReq, mockRes, mockNext);

      expect(voidService.createCraneRequestVoid).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Marked as Void Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create crane request void', async () => {
      const mockError = new Error('Service error');
      voidService.createCraneRequestVoid.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.createCraneRequestVoid(mockReq, mockRes, mockNext);

      expect(voidService.createCraneRequestVoid).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create crane request void', async () => {
      const mockError = new Error('Exception error');
      voidService.createCraneRequestVoid.mockImplementation(() => {
        throw mockError;
      });

      await voidController.createCraneRequestVoid(mockReq, mockRes, mockNext);

      expect(voidService.createCraneRequestVoid).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createConcreteRequestVoid', () => {
    it('should create concrete request void successfully', async () => {
      const mockResponse = { id: 1, status: 'voided', concreteRequestId: 'concrete_123' };

      voidService.createConcreteRequestVoid.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.createConcreteRequestVoid(mockReq, mockRes, mockNext);

      expect(voidService.createConcreteRequestVoid).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Marked as Void Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create concrete request void', async () => {
      const mockError = new Error('Service error');
      voidService.createConcreteRequestVoid.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.createConcreteRequestVoid(mockReq, mockRes, mockNext);

      expect(voidService.createConcreteRequestVoid).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create concrete request void', async () => {
      const mockError = new Error('Exception error');
      voidService.createConcreteRequestVoid.mockImplementation(() => {
        throw mockError;
      });

      await voidController.createConcreteRequestVoid(mockReq, mockRes, mockNext);

      expect(voidService.createConcreteRequestVoid).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getVoidList', () => {
    it('should get void list successfully', async () => {
      const mockResponse = [
        { id: 1, status: 'voided', bookingId: 'book_123' },
        { id: 2, status: 'voided', bookingId: 'book_456' },
      ];

      voidService.getVoidList.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.getVoidList(mockReq, mockRes, mockNext);

      expect(voidService.getVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Void list displayed successfully',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get void list', async () => {
      const mockError = new Error('Service error');
      voidService.getVoidList.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.getVoidList(mockReq, mockRes, mockNext);

      expect(voidService.getVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get void list', async () => {
      const mockError = new Error('Exception error');
      voidService.getVoidList.mockImplementation(() => {
        throw mockError;
      });

      await voidController.getVoidList(mockReq, mockRes, mockNext);

      expect(voidService.getVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createMultipleVoidList', () => {
    it('should create multiple void list successfully', async () => {
      const mockResponse = {
        voidedCount: 3,
        voidedBookings: ['book_123', 'book_456', 'book_789']
      };

      voidService.createMultipleVoidList.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await voidController.createMultipleVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createMultipleVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Multiple Delivery Booking Marked as Void Successfully.',
        data: mockResponse,
        status: 201,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create multiple void list', async () => {
      const mockError = new Error('Service error');
      voidService.createMultipleVoidList.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await voidController.createMultipleVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createMultipleVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create multiple void list', async () => {
      const mockError = new Error('Exception error');
      voidService.createMultipleVoidList.mockImplementation(() => {
        throw mockError;
      });

      await voidController.createMultipleVoidList(mockReq, mockRes, mockNext);

      expect(voidService.createMultipleVoidList).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});