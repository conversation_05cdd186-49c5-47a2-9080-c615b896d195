// Mock notificationService
jest.mock('../../services', () => ({
  notificationService: {
    listNotification: jest.fn(),
    setReadNotification: jest.fn(),
    setReadAllNotification: jest.fn(),
    getNotificationCount: jest.fn(),
    deleteNotification: jest.fn(),
    versionUpdation: jest.fn(),
    getVersion: jest.fn(),
  },
}));

const NotificationController = require('../NotificationController');
const { notificationService } = require('../../services');

describe('NotificationController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('listNotification', () => {
    it('should list notifications successfully', async () => {
      const mockResponse = [
        { id: 1, message: 'Notification 1', read: false },
        { id: 2, message: 'Notification 2', read: true },
      ];

      notificationService.listNotification.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationController.listNotification(mockReq, mockRes, mockNext);

      expect(notificationService.listNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list notification', async () => {
      const mockError = new Error('Service error');
      notificationService.listNotification.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationController.listNotification(mockReq, mockRes, mockNext);

      expect(notificationService.listNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in list notification', async () => {
      const mockError = new Error('Exception error');
      notificationService.listNotification.mockImplementation(() => {
        throw mockError;
      });

      await NotificationController.listNotification(mockReq, mockRes, mockNext);

      expect(notificationService.listNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('setReadNotification', () => {
    it('should set notification as read successfully', async () => {
      const mockResponse = { id: 1, read: true, message: 'Notification marked as read' };

      notificationService.setReadNotification.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationController.setReadNotification(mockReq, mockRes, mockNext);

      expect(notificationService.setReadNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification Read Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from set read notification', async () => {
      const mockError = new Error('Service error');
      notificationService.setReadNotification.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationController.setReadNotification(mockReq, mockRes, mockNext);

      expect(notificationService.setReadNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in set read notification', async () => {
      const mockError = new Error('Exception error');
      notificationService.setReadNotification.mockImplementation(() => {
        throw mockError;
      });

      await NotificationController.setReadNotification(mockReq, mockRes, mockNext);

      expect(notificationService.setReadNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('setReadAllNotification', () => {
    it('should set all notifications as read successfully', async () => {
      const mockResponse = { readCount: 5, message: 'All notifications marked as read' };

      notificationService.setReadAllNotification.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationController.setReadAllNotification(mockReq, mockRes, mockNext);

      expect(notificationService.setReadAllNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification Read Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from set read all notification', async () => {
      const mockError = new Error('Service error');
      notificationService.setReadAllNotification.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationController.setReadAllNotification(mockReq, mockRes, mockNext);

      expect(notificationService.setReadAllNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in set read all notification', async () => {
      const mockError = new Error('Exception error');
      notificationService.setReadAllNotification.mockImplementation(() => {
        throw mockError;
      });

      await NotificationController.setReadAllNotification(mockReq, mockRes, mockNext);

      expect(notificationService.setReadAllNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getNotificationCount', () => {
    it('should get notification count successfully', async () => {
      const mockResponse = { count: 10, unreadCount: 3 };

      notificationService.getNotificationCount.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationController.getNotificationCount(mockReq, mockRes, mockNext);

      expect(notificationService.getNotificationCount).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification count.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get notification count', async () => {
      const mockError = new Error('Service error');
      notificationService.getNotificationCount.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationController.getNotificationCount(mockReq, mockRes, mockNext);

      expect(notificationService.getNotificationCount).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get notification count', async () => {
      const mockError = new Error('Exception error');
      notificationService.getNotificationCount.mockImplementation(() => {
        throw mockError;
      });

      await NotificationController.getNotificationCount(mockReq, mockRes, mockNext);

      expect(notificationService.getNotificationCount).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteNotification', () => {
    it('should delete notification successfully', async () => {
      const mockResponse = { deleted: true, id: 1 };

      notificationService.deleteNotification.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await NotificationController.deleteNotification(mockReq, mockRes, mockNext);

      expect(notificationService.deleteNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Notification Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delete notification', async () => {
      const mockError = new Error('Service error');
      notificationService.deleteNotification.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await NotificationController.deleteNotification(mockReq, mockRes, mockNext);

      expect(notificationService.deleteNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delete notification', async () => {
      const mockError = new Error('Exception error');
      notificationService.deleteNotification.mockImplementation(() => {
        throw mockError;
      });

      await NotificationController.deleteNotification(mockReq, mockRes, mockNext);

      expect(notificationService.deleteNotification).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('versionUpdate', () => {
    it('should update version successfully', async () => {
      const mockResponse = true;
      notificationService.versionUpdation.mockResolvedValue(mockResponse);

      await NotificationController.versionUpdate(mockReq, mockRes, mockNext);

      expect(notificationService.versionUpdation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Version updated Successfully',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle falsy response from version update', async () => {
      const mockResponse = false;
      notificationService.versionUpdation.mockResolvedValue(mockResponse);

      await NotificationController.versionUpdate(mockReq, mockRes, mockNext);

      expect(notificationService.versionUpdation).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in version update', async () => {
      const mockError = new Error('Exception error');
      notificationService.versionUpdation.mockRejectedValue(mockError);

      await NotificationController.versionUpdate(mockReq, mockRes, mockNext);

      expect(notificationService.versionUpdation).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getVersion', () => {
    it('should get version successfully', async () => {
      const mockResponse = { data: { version: '1.0.0', buildNumber: 123 } };
      notificationService.getVersion.mockResolvedValue(mockResponse);

      await NotificationController.getVersion(mockReq, mockRes, mockNext);

      expect(notificationService.getVersion).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Version fetched Successfully',
        data: mockResponse.data,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in get version', async () => {
      const mockError = new Error('Exception error');
      notificationService.getVersion.mockRejectedValue(mockError);

      await NotificationController.getVersion(mockReq, mockRes, mockNext);

      expect(notificationService.getVersion).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
