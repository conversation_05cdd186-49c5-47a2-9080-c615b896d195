const status = require('http-status');
const RestrictMailController = require('../RestrictMailController');
const { restrictMailService } = require('../../services');
const { RestrictEmail } = require('../../models');

// Mock dependencies
jest.mock('../../services', () => ({
  restrictMailService: {
    updateRestrictMail: jest.fn(),
    addRestrictMail: jest.fn(),
    deleteRestrictMail: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  RestrictEmail: {
    getMailList: jest.fn(),
  },
}));

describe('RestrictMailController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {
        pageNo: '1',
        pageSize: '10',
      },
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('restrictMailList', () => {
    it('should get restrict mail list successfully', async () => {
      const mockMailList = [
        { id: 1, email: '<EMAIL>' },
        { id: 2, email: '<EMAIL>' },
      ];
      RestrictEmail.getMailList.mockResolvedValue(mockMailList);

      await RestrictMailController.restrictMailList(mockReq, mockRes, mockNext);

      expect(RestrictEmail.getMailList).toHaveBeenCalledWith(10, 0);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Mail listed Successfully.',
        data: mockMailList,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle pagination correctly', async () => {
      mockReq.params.pageNo = '3';
      mockReq.params.pageSize = '20';
      const mockMailList = [{ id: 1, email: '<EMAIL>' }];
      RestrictEmail.getMailList.mockResolvedValue(mockMailList);

      await RestrictMailController.restrictMailList(mockReq, mockRes, mockNext);

      expect(RestrictEmail.getMailList).toHaveBeenCalledWith(20, 40);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Mail listed Successfully.',
        data: mockMailList,
      });
    });

    it('should handle error from database', async () => {
      const mockError = new Error('Database error');
      RestrictEmail.getMailList.mockRejectedValue(mockError);

      await RestrictMailController.restrictMailList(mockReq, mockRes, mockNext);

      expect(RestrictEmail.getMailList).toHaveBeenCalledWith(10, 0);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('updateRestrictMail', () => {
    it('should update restrict mail successfully', async () => {
      const mockResponse = { id: 1, email: '<EMAIL>' };
      restrictMailService.updateRestrictMail.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await RestrictMailController.updateRestrictMail(mockReq, mockRes, mockNext);

      expect(restrictMailService.updateRestrictMail).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Mail Details Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service error', async () => {
      const mockError = new Error('Service error');
      restrictMailService.updateRestrictMail.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await RestrictMailController.updateRestrictMail(mockReq, mockRes, mockNext);

      expect(restrictMailService.updateRestrictMail).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle general error', async () => {
      const mockError = new Error('General error');
      restrictMailService.updateRestrictMail.mockImplementation(() => {
        throw mockError;
      });

      await RestrictMailController.updateRestrictMail(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('addRestrictMail', () => {
    it('should add restrict mail successfully', async () => {
      const mockResponse = { id: 1, email: '<EMAIL>' };
      restrictMailService.addRestrictMail.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await RestrictMailController.addRestrictMail(mockReq, mockRes, mockNext);

      expect(restrictMailService.addRestrictMail).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Mail list added Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service error', async () => {
      const mockError = new Error('Service error');
      restrictMailService.addRestrictMail.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await RestrictMailController.addRestrictMail(mockReq, mockRes, mockNext);

      expect(restrictMailService.addRestrictMail).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle general error', async () => {
      const mockError = new Error('General error');
      restrictMailService.addRestrictMail.mockImplementation(() => {
        throw mockError;
      });

      await RestrictMailController.addRestrictMail(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteRestrictMail', () => {
    it('should delete restrict mail successfully', async () => {
      const mockResponse = { deleted: true };
      restrictMailService.deleteRestrictMail.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await RestrictMailController.deleteRestrictMail(mockReq, mockRes, mockNext);

      expect(restrictMailService.deleteRestrictMail).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Mail list Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service error', async () => {
      const mockError = new Error('Service error');
      restrictMailService.deleteRestrictMail.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await RestrictMailController.deleteRestrictMail(mockReq, mockRes, mockNext);

      expect(restrictMailService.deleteRestrictMail).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle general error', async () => {
      const mockError = new Error('General error');
      restrictMailService.deleteRestrictMail.mockImplementation(() => {
        throw mockError;
      });

      await RestrictMailController.deleteRestrictMail(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
