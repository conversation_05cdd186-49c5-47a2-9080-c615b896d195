module.exports = (sequelize, DataTypes) => {
  const presetEquipmentTypes = sequelize.define(
    'PresetEquipmentType',
    {
      equipmentType: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      isActive: DataTypes.BOOLEAN,
      isCraneType: DataTypes.BOOLEAN,
    },
    {},
  );
  presetEquipmentTypes.associate = (models) => {
    presetEquipmentTypes.hasOne(models.Equipments);
    return presetEquipmentTypes;
  };
  return presetEquipmentTypes;
};
