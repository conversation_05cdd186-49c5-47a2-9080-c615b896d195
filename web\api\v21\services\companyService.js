/* eslint-disable no-await-in-loop */
const path = require('path');
const ExcelJS = require('exceljs');
const { stringify } = require('flatted');
const { Worker } = require('worker_threads');
let { Company, Project, Member, DeliverDefineWork, CompanyDefine, User } = require('../models');
const {
  Sequelize,
  Enterprise,
  DeliverCompany,
  CraneRequestCompany,
  ConcreteRequestCompany,
} = require('../models');

const { Op } = Sequelize;
const helper = require('../helpers/domainHelper');
const awsConfig = require('../middlewares/awsConfig');

const bulkCompanyProcess = path.join(__dirname, './bulkCompanyProcess.js');

let publicCompany;
let publicUser;
let publicMember;
const companyService = {
  async addCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyData = inputData.body;
      const projectDetails = await Project.findByPk(companyData.ProjectId);
      if (projectDetails) {
        const exist = await Company.findOne({
          where: Sequelize.and(
            { ProjectId: companyData.ProjectId, isDeleted: false },
            Sequelize.or({
              companyName: companyData.companyName.trim().toLowerCase(),
              website: companyData.website?.trim().toLowerCase() ?? companyData.website,
            }),
          ),
        });
        if (exist) {
          done(null, { message: 'Company name/Website Already exist.' });
        } else {
          this.checkInputDatas(inputData, async (checkResponse, checkError) => {
            if (checkError) {
              done(null, checkError);
            } else {
              companyData.createdBy = inputData.user.id;
              const lastIdValue = await Company.findOne({
                where: { ProjectId: companyData.ProjectId, isDeleted: false },
                order: [['companyAutoId', 'DESC']],
              });
              let id = 1;
              const newValue = JSON.parse(JSON.stringify(lastIdValue));
              if (newValue?.companyAutoId != null) {
                id = newValue.companyAutoId;
              }
              companyData.companyAutoId = id + 1;
              const newCompany = await Company.createInstance(companyData);
              if (inputData.user.domainName != null) {
                await this.createPublicCompany(companyData);
              }
              const definable = companyData.definableWorkId;
              definable.forEach(async (element) => {
                const companyParam = {
                  DeliverDefineWorkId: element,
                  CompanyId: newCompany.id,
                  ProjectId: companyData.ProjectId,
                };
                await CompanyDefine.createInstance(companyParam);
              });
              done(newCompany, false);
            }
          });
        }
      } else {
        done(null, 'Project Does not exist.');
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicCompany = modelData.Company;
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async createPublicCompany(newCompany) {
    await this.returnProjectModel();
    const tempData = newCompany;
    const existCompany = await publicCompany.findOne({
      where: { companyName: tempData.companyName, website: tempData.website },
    });
    if (!existCompany) {
      delete tempData.id;
      await publicCompany.createInstance(tempData);
    }
  },
  async getDomainEnterpriseValue(domainName) {
    if (!domainName) return null;

    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });

    return domainEnterpriseValue || null;
  },

  async getUserEnterpriseValue(userData, ParentCompanyId) {
    if (!userData) return null;

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });

    if (!memberData) {
      return await this.getEnterpriseByParentCompany(ParentCompanyId);
    }

    if (memberData.isAccount) {
      return await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' },
      });
    }

    return await this.getEnterpriseByParentCompany(ParentCompanyId);
  },

  async getEnterpriseByParentCompany(ParentCompanyId) {
    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
  },

  async updateUserData(enterpriseValue, inputData, User) {
    if (!enterpriseValue) return inputData;

    const newUser = await User.findOne({ where: { email: inputData.user.email } });
    inputData.user = newUser;
    return inputData;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    let { domainName } = inputData.user;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body?.ParentCompanyId ?? inputData.params?.ParentCompanyId;

    // Get domain enterprise value
    const domainEnterpriseValue = await this.getDomainEnterpriseValue(domainName);
    if (!domainEnterpriseValue) {
      domainName = '';
    }

    // Get enterprise value if needed
    let enterpriseValue = null;
    if (!domainName && ParentCompanyId != null && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      const userData = email ? await publicUser.findOne({ where: { email } }) : null;
      enterpriseValue = await this.getUserEnterpriseValue(userData, ParentCompanyId);

      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
      }
    }

    // Get and set model objects
    const modelObj = await helper.getDynamicModel(domainName);
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    DeliverDefineWork = modelObj.DeliverDefineWork;
    CompanyDefine = modelObj.CompanyDefine;
    User = modelObj.User;

    // Update user data if needed
    const updatedInputData = await this.updateUserData(enterpriseValue, incomeData, User);

    return updatedInputData.ProjectId;
  },
  async companyLogoUpload(inputData, done) {
    try {
      await awsConfig.singleUpload(inputData, async (result, err) => {
        if (!err) {
          done(result, err);
        } else {
          done(null, err);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const companyData = inputData.body;
    const { definableWorkId } = companyData;
    const defineList = await DeliverDefineWork.count({
      where: { id: { [Op.in]: definableWorkId }, ProjectId: companyData.ProjectId },
    });
    if (defineList === definableWorkId.length) {
      done(true, false);
    } else {
      done(null, { message: 'Some Definable feature not in this list.' });
    }
  },
  async editCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyData = inputData.body;
      const definable = companyData.definableWorkId;
      const { id } = companyData;
      const projectDetails = await Project.findByPk(companyData.ProjectId);
      if (projectDetails) {
        const exist = await Company.findOne({
          where: Sequelize.and(
            {
              ProjectId: companyData.ProjectId,
              id: { [Op.not]: companyData.id },
            },
            Sequelize.or({
              companyName: companyData.companyName.trim().toLowerCase(),
              website: companyData.website
                ? companyData.website.trim().toLowerCase()
                : companyData.website,
            }),
          ),
        });
        if (exist) {
          done(null, { message: 'Company name/Website Already exist.' });
        } else {
          delete companyData.id;
          const updateCompany = await Company.update(companyData, { where: { id } });
          const condition = Sequelize.and({
            ProjectId: companyData.ProjectId,
            CompanyId: id,
          });
          this.updateValues(condition, inputData, async (response, error) => {
            if (!error) {
              const existDefine = await CompanyDefine.findAll({ where: condition });
              definable.forEach(async (element) => {
                const index = existDefine.findIndex((item) => item.DeliverDefineWorkId === element);
                const defineParam = {
                  CompanyId: id,
                  DeliverDefineWorkId: element,
                  ProjectId: companyData.ProjectId,
                  isDeleted: false,
                };
                if (index !== -1) {
                  await CompanyDefine.update(defineParam, {
                    where: { id: existDefine[index].id },
                  });
                } else {
                  await CompanyDefine.createInstance(defineParam);
                }
              });
              done(updateCompany, false);
            } else {
              done(null, error);
            }
          });
        }
      } else {
        done(null, 'Project Does not exist.');
      }
    } catch (e) {
      done(null, e);
    }
  },
  async updateValues(condition, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      await CompanyDefine.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getAll(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const isInviteMemberPage = inputData.body.inviteMember;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      let searchCondition = {};
      const offset = (pageNumber - 1) * pageSize;
      const parentCompany = await Company.findAll({
        required: false,
        subQuery: false,
        include: [
          {
            association: 'Members',
            required: false,
            attributes: ['id'],
            where: {
              ProjectId: +params.ProjectId,
              isDeleted: false,
            },
          },
          {
            association: 'define',
            where: { isDeleted: false },
            required: false,
            include: ['DeliverDefineWork'],
          },
        ],
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: {
          isParent: true,
          ParentCompanyId: +params.ParentCompanyId,
          isDeleted: false,
        },
        group: ['Company.id', 'define.id', 'define.DeliverDefineWork.id', 'Members.id'],
      });
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      };
      if (incomeData.dfowFilter) {
        condition['$define.DeliverDefineWork.id$'] = incomeData.dfowFilter;
      }
      if (incomeData.companyFilter) {
        condition.companyName = {
          [Sequelize.Op.iLike]: `%${incomeData.companyFilter}%`,
        };
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            companyName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$define.DeliverDefineWork.DFOW$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        companyAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const companyList = await Company.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      const companyArray = {};
      companyArray.count = companyList.rows.length;
      if (!isInviteMemberPage) {
        companyArray.rows = companyList.rows.slice(offset, offset + pageSize);
      } else {
        companyArray.rows = companyList.rows;
      }
      if (companyArray?.rows?.length > 0 && isInviteMemberPage) {
        companyArray.rows.sort((a, b) =>
          a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
        );
      }
      done({ companyArray, parentCompany }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getAllCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const parentCompany = await Company.findOne({
        required: false,
        subQuery: false,
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: { isParent: true, ParentCompanyId: +params.ParentCompanyId, isDeleted: false },
      });
      const companyList = await Company.getAllCompany({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      });
      done({ companyList, parentCompany }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getDefinableWork(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const defineRecord = await DeliverDefineWork.findAll({
        where: Sequelize.and({ ProjectId: params.ProjectId, isDeleted: false }),
      });
      defineRecord.sort((a, b) => (a.DFOW.toLowerCase() > b.DFOW.toLowerCase() ? 1 : -1));
      done({ defineRecord }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async checkCompanyMappings(item, companyData) {
    const mappings = await Promise.all([
      DeliverCompany.findOne({
        where: {
          CompanyId: +item.id,
          isDeleted: false,
          ProjectId: companyData.ProjectId,
        },
      }),
      CraneRequestCompany.findOne({
        where: {
          CompanyId: +item.id,
          isDeleted: false,
          ProjectId: companyData.ProjectId,
        },
      }),
      ConcreteRequestCompany.findOne({
        where: {
          CompanyId: +item.id,
          isDeleted: false,
          ProjectId: companyData.ProjectId,
        },
      }),
      Member.findOne({
        where: {
          CompanyId: +item.id,
          isDeleted: false,
          ProjectId: companyData.ProjectId,
        },
      }),
    ]);

    const [deliveryRequest, craneRequest, concreteRequest, member] = mappings;

    if (deliveryRequest || craneRequest || concreteRequest) {
      return {
        canDelete: false,
        message: `${item.companyName} cannot be deleted. ${item.companyName} is mapped to submitted bookings`,
      };
    }

    if (member) {
      return {
        canDelete: false,
        message: `Company ${item.companyName} cannot be deleted. The company is mapped to a member`,
      };
    }

    return { canDelete: true };
  },

  async deleteCompanyRecord(item, companyData) {
    await Company.update(
      { isDeleted: true },
      {
        where: {
          id: +item.id,
          ProjectId: companyData.ProjectId,
          isDeleted: false,
        },
      },
    );
  },

  async processCompanyDeletion(item, companyData) {
    const companyDetails = await Company.findByPk(item.id);

    if (!companyDetails) {
      return { error: 'Company Does not Exist.' };
    }

    if (companyDetails.isParent) {
      return { error: 'You cannot able to delete, because this is Base Company.' };
    }

    const mappingCheck = await this.checkCompanyMappings(item, companyData);
    if (!mappingCheck.canDelete) {
      return { error: mappingCheck.message };
    }

    await this.deleteCompanyRecord(item, companyData);
    return { success: true };
  },

  async deleteCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyData = inputData.body;
      const { id } = inputData.body;

      const getCompanies = await Company.findAll({
        where: companyData.isSelectAll
          ? { ProjectId: companyData.ProjectId, isDeleted: false }
          : { ProjectId: companyData.ProjectId, isDeleted: false, id: { [Op.in]: id } },
      });

      if (!getCompanies?.length) {
        return done(null, { message: 'No companies found to delete' });
      }

      for (const company of getCompanies) {
        const result = await this.processCompanyDeletion(company, companyData);

        if (result.error) {
          return done(null, { message: result.error });
        }
      }

      done('success', false);
    } catch (e) {
      done(null, e);
    }
  },
  async dfowAndCompanyForBulkUploadDeliveryRequest(inputData) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const defineRecord = await DeliverDefineWork.findAll({
        where: Sequelize.and({ ProjectId: params.ProjectId, isDeleted: false }),
      });
      const parentCompany = await Company.findOne({
        required: false,
        subQuery: false,
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: { isParent: true, ParentCompanyId: +params.ParentCompanyId, isDeleted: false },
      });
      const companyList = await Company.getAllCompany({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      });
      const newCompanyList = [];
      await companyList.rows.forEach((element) => {
        newCompanyList.push({
          id: element.id,
          companyName: element.companyName,
        });
      });
      if (parentCompany) {
        const index = newCompanyList.findIndex(
          (item) =>
            item.id === parentCompany.id ||
            item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
        );
        if (index === -1) {
          newCompanyList.push({
            id: parentCompany.id,
            companyName: parentCompany.companyName,
          });
        }
      }
      const returnData = { defineRecord, newCompanyList };
      return returnData;
    } catch (e) {
      console.log(e);
    }
  },
  async checkExistCompany(req) {
    try {
      await this.getDynamicModel(req);
      const projectDetail = req.body;
      let condition = {
        isDeleted: false,
      };
      let parentCompanyCondition = {
        isDeleted: false,
        isParent: true,
      };
      if (projectDetail.id) {
        condition = {
          ...condition,
          id: { [Op.not]: projectDetail.id },
        };
      }
      if (projectDetail.id) {
        parentCompanyCondition = {
          ...parentCompanyCondition,
          id: { [Op.not]: projectDetail.id },
        };
      }
      const companyName = projectDetail.companyName.split(' ')[0];
      const { ProjectId } = req.params;
      const { ParentCompanyId } = req.params;
      const projectDetails = await Project.findByPk(ProjectId);
      if (projectDetails) {
        const existInProject = await Company.findAll({
          where: Sequelize.and(
            condition,
            { ProjectId },
            { companyName: { [Sequelize.Op.iLike]: `${companyName}%` } },
          ),
        });
        const sameAsParentCompany = await Company.findOne({
          where: Sequelize.and(
            parentCompanyCondition,
            { ParentCompanyId },
            { companyName: { [Sequelize.Op.iLike]: `${companyName}%` } },
          ),
        });
        return { existInProject, sameAsParentCompany };
      }
      return { status: 500, message: 'ProjectId does not exist.' };
    } catch (e) {
      return e;
    }
  },
  async validateMemberAccess(inputData, ProjectId) {
    const memberDetail = await Member.findOne({
      where: [
        Sequelize.and(
          {
            UserId: inputData.user.id,
            ProjectId,
            isDeleted: false,
          },
          Sequelize.or({ RoleId: [1, 2, 3, 4] }),
        ),
      ],
    });

    if (!memberDetail) {
      throw new Error('Project Does not exist or you are not a valid member.');
    }

    return memberDetail;
  },

  async validateFileFormat(file) {
    if (!file?.originalname) {
      throw new Error('Please select a file.');
    }

    const splitValue = file.originalname.split('.');
    const extension = splitValue[splitValue.length - 1];
    const fileName = splitValue[0];
    const firstSplitFileName = fileName.split('_');

    if (firstSplitFileName.length !== 3) {
      throw new Error('Invalid file');
    }

    return {
      extension,
      projectFileName: firstSplitFileName[0],
      projectId: firstSplitFileName[1],
    };
  },

  async validateProjectDetails(ProjectId, projectFileName, projectId) {
    const projectDetails = await Project.findByPk(ProjectId);

    if (!projectDetails) {
      throw new Error('Project not found');
    }

    if (
      projectDetails.projectName.toLowerCase() !== projectFileName.toLowerCase() ||
      +ProjectId !== +projectId
    ) {
      throw new Error('Invalid file');
    }

    return projectDetails;
  },

  async processExcelFile(file, inputData, done) {
    const newWorkbook = new ExcelJS.Workbook();
    await newWorkbook.xlsx.readFile(file.path);
    const worksheet = newWorkbook.getWorksheet('Company');

    this.createCompanyData(worksheet, inputData, (resValue, error) => {
      if (!error) {
        return done(resValue, false);
      }
      return done(null, error);
    });
  },

  async createCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { file } = inputData;
      const ProjectId = +inputData.params.ProjectId;

      // Validate member access
      await this.validateMemberAccess(inputData, ProjectId);

      // Validate file format
      const { extension, projectFileName, projectId } = await this.validateFileFormat(file);

      // Validate project details
      await this.validateProjectDetails(ProjectId, projectFileName, projectId);

      // Process Excel file
      if (extension === 'xlsx') {
        await this.processExcelFile(file, inputData, done);
      } else {
        done(null, { message: 'Please choose valid file' });
      }
    } catch (error) {
      done(null, { message: error.message });
    }
  },
  async createCompanyData(companyWorksheet, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const existProjectId = inputData.params.ProjectId;
      const ProjectId = +existProjectId;
      const loginUser = inputData.user;
      const projectDetails = await Project.findByPk(ProjectId);
      let fileFormat = true;
      const worksheet = companyWorksheet;
      const companyRecords = [];
      let headers;
      if (worksheet) {
        worksheet.eachRow(async (rowData, rowNumber) => {
          const singleRowData = rowData.values;
          singleRowData.shift();
          if (rowNumber === 2) {
            headers = singleRowData;
          } else if (singleRowData?.length > 1 && rowNumber >= 2) {
            const getRow = singleRowData;
            const companyName = getRow[1];
            if (companyName) {
              companyRecords.push(singleRowData);
            }
          }
        });
        if (!companyRecords?.length) {
          return done(null, {
            message: 'Please upload proper document / Please fill mandatory column.',
          });
        }
        if (inputData.file && +headers?.length !== 11) {
          fileFormat = false;
        }
        if (fileFormat) {
          const worker = new Worker(bulkCompanyProcess);
          const object = stringify({
            projectDetails,
            loginUser,
            companyRecords,
            ProjectId,
            inputData,
          });
          worker.postMessage(object);
          worker.on('message', (data) => {
            if (data === 'done') {
              const socketObject = {
                message: data,
                loginUserId: loginUser.id,
              };
              global.io.emit('bulkCompanyNotification', socketObject);
              worker.terminate();
            }
          });
          worker.on('exit', (data) => {
            console.log('worker thread exit ', data);
          });
          done({ message: 'success' }, false);
        } else {
          done(null, { message: 'Invalid File.!' });
        }
      } else {
        done(null, { message: 'Invalid File' });
      }
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = companyService;
