const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../middlewares/awsConfig');

const csvConcreteReportService = {
  async exportConcreteReportInCsvFormat(data, selectedHeaders, timezoneoffset, fileName, exportType, done) {
    const {selectedFlags } = this.extractHeaders(selectedHeaders);
    const values = this.constructCsvRows(data, selectedFlags, timezoneoffset);
    const csvFile = await this.generateCsvFile(values);

    if (csvFile) {
      const buffer = Buffer.from(csvFile, 'utf-8');
      awsConfig.reportUpload(buffer, fileName, exportType, async (result, error1) => {
        if (!error1) {
          return done(result, false);
        }
        return done(null, { message: 'cannot export document' });
      });
    }
  },
  extractHeaders(selectedHeaders) {
    const rowValues = [];
    const columns = [];
    const selectedFlags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isCompanySelected: false,
      isOrderNumberSelected: false,
      isSlumpSelected: false,
      isTruckSpacingSelected: false,
      isPrimerOrderedSelected: false,
      isPersonSelected: false,
      isQuantityOrderedSelected: false,
      isMixDesignSelected: false,
      isLocationSelected: false,
    };

    selectedHeaders.forEach((object) => {
      if (object.isActive) {
        rowValues.push(object.title);
        columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });
        const keyMap = {
          id: 'isIdSelected',
          description: 'isDescriptionSelected',
          date: 'isDateSelected',
          status: 'isStatusSelected',
          approvedby: 'isApprovedBySelected',
          company: 'isCompanySelected',
          orderNumber: 'isOrderNumberSelected',
          slump: 'isSlumpSelected',
          truckSpacing: 'isTruckSpacingSelected',
          primer: 'isPrimerOrderedSelected',
          name: 'isPersonSelected',
          quantity: 'isQuantityOrderedSelected',
          mixDesign: 'isMixDesignSelected',
          location: 'isLocationSelected',
        };
        const flag = keyMap[object.key];
        if (flag) selectedFlags[flag] = true;
      }
    });

    return { rowValues, columns, selectedFlags };
  },
  // Helper function to format date and time
  _formatDateTime(item, timezoneoffset) {
    const start = moment(item.concretePlacementStart).add(Number(timezoneoffset), 'minutes');
    const end = moment(item.concretePlacementEnd).add(Number(timezoneoffset), 'minutes');
    return `${start.format('MMM-DD-YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
  },

  // Helper function to format approved by information
  _formatApprovedBy(item) {
    return item.approverDetails?.User?.firstName
      ? `${item.approverDetails.User.firstName} ${item.approverDetails.User.lastName}`
      : '-';
  },

  // Helper function to format company information
  _formatCompany(item) {
    const companies = item.concreteSupplierDetails?.map(s => s.Company?.companyName).filter(Boolean) || [];
    return companies.length ? companies.join(', ') : '-';
  },

  // Helper function to format person information
  _formatPerson(item) {
    const people = item.memberDetails?.map(m => `${m.Member?.User?.firstName} ${m.Member?.User?.lastName}`.trim()).filter(Boolean) || [];
    return people.length ? people.join(', ') : '-';
  },

  // Helper function to format mix design information
  _formatMixDesign(item) {
    const mixes = item.mixDesignDetails?.map(m => m.ConcreteMixDesign?.mixDesign).filter(Boolean) || [];
    return mixes.length ? mixes.join(', ') : '-';
  },

  // Helper function to format location information
  _formatLocation(item) {
    return item.location?.locationPath ? `${item.location.locationPath}` : '-';
  },

  // Helper function to build row based on flags
  _buildRow(item, flags, timezoneoffset) {
    const row = {};
    
    if (flags.isIdSelected) row.Id = item.ConcreteRequestId;
    if (flags.isDescriptionSelected) row.Description = item.description;
    if (flags.isDateSelected) row['Date & Time'] = this._formatDateTime(item, timezoneoffset);
    if (flags.isStatusSelected) row.Status = item.status;
    if (flags.isApprovedBySelected) row['Approved By'] = this._formatApprovedBy(item);
    if (flags.isCompanySelected) row['Concrete Supplier'] = this._formatCompany(item);
    if (flags.isOrderNumberSelected) row['Order Number'] = item.concreteOrderNumber || '-';
    if (flags.isSlumpSelected) row.Slump = item.slump || '-';
    if (flags.isTruckSpacingSelected) row['Truck Spacing'] = item.truckSpacingHours || '-';
    if (flags.isPrimerOrderedSelected) row['Primer Ordered'] = item.primerForPump || '-';
    if (flags.isPersonSelected) row['Responsible Person'] = this._formatPerson(item);
    if (flags.isQuantityOrderedSelected) row['Quantity Ordered'] = item.concreteQuantityOrdered || '-';
    if (flags.isMixDesignSelected) row['Mix Design'] = this._formatMixDesign(item);
    if (flags.isLocationSelected) row.Location = this._formatLocation(item);
    
    return row;
  },

  constructCsvRows(data, flags, timezoneoffset) {
    return data.map(item => this._buildRow(item, flags, timezoneoffset));
  },
  async generateCsvFile(values) {
    const options = {
      showLabels: true,
      showTitle: false,
      useTextFile: false,
      useBom: false,
      useKeysAsHeaders: true,
    };

    const csvExporter = new ExportToCsv(options);
    return await csvExporter.generateCsv(values, true);
  }

};
module.exports = csvConcreteReportService;
