const fs = require('fs');
const formData = require('form-data');
const Mailgun = require('mailgun.js');

const mailgun = new Mailgun(formData);
const puppeteer = require('puppeteer');
const moment = require('moment');
const deepLinkService = require('../services/deepLinkService');

let invoiceAttachment;
const mg = mailgun.client({ username: 'api', key: process.env.MAILGUN_API_KEY });

const TEMPLATE_MAPPINGS = {
  projectSubscriptionInvoice: 'projectSubscriptionInvoiceNotification.html',
  upgradeplan: 'upgradeProjectPlanSubscription.html',
  signUpSubscription: 'sendReceiptAfterSignUp.html',
  forgotPassword: 'forgot-password.html',
  register: 'signupTemplate.html',
  addproject: 'memberAddedInNewproject.html',
  trailPlanEndingRemainder: 'trialPlanEndingRemainder.html',
  trailPlanExpired: 'trialPlanOverNotification.html',
  trialPlanBegins: 'trialPlanBeginsNotification.html',
  'Account Creation': 'accountAdminCredentials.html',
  'Account Member': 'accountmember.html',
  accountadminpay: 'accountadminpay.html',
  invite_member: 'memberOnboarding.html',
  commentadded: 'commentAddedAgainstNDR.html',
  cranecommentadded: 'commentAddedAgainstCraneRequest.html',
  concretecommentadded: 'commentAddedAgainstConcreteRequest.html',
  adminoverride: 'adminOverRideStatus.html',
  assignPAtoNewProjectByAccountadmin: 'assignProjectToExistingPA_by_AccountAdmin.html',
  userPasswordChangeBySuperAdmin: 'notifyUserPasswordChangedBySuperAdmin.html',
  editProjectBySA: 'notifyMembers_If_SuperAdmin_Edited_Project.html',
  dailyDigestNotification: 'dailyDigestEmailNotification.html',
  deliveredDR: 'notifyUsersIfDeliveryRequestDelivered.html',
  completedCraneRequest: 'notifyUserIfCraneRequestCompleted.html',
  completedConcreteRequest: 'notifyUserIfConcreteRequestCompleted.html',
  notifyPAForApproval: 'Notify_PA_forApproval.html',
  notifyPAForReApproval: 'Notify_PA_forReApproval.html',
  deliveryRequestCreated: 'NotifyUsersIfDeliveryRequestCreated.html',
  craneRequestCreated: 'NotifyUsersIfCraneRequestCreated.html',
  concreteRequestCreated: 'NotifyUserIfConcreteRequestCreated.html',
  schedulerReport: 'schedulerReportModel.html',
  schedulerReportNoData: 'schedulerReportModelNoData.html',
  guestApproved: 'guestRequestApprovedByAdmin.html',
  guestRejected: 'guestRequestRejected.html',
  guestRequested: 'guestRequestToMember.html',
  notifyGuestOnEdit: 'notifyGuestOnEdit.html'
};

const processInvoiceAttachment = (userData) => {
  const attachment = fs.readFileSync(
    '/usr/src/web/api/v20/views/mail-templates/projectSubscriptionInvoiceDocument.html',
    { encoding: 'utf-8' }
  );
  return Object.entries(userData).reduce((acc, [key, value]) => {
    return acc.replace(`$${key}`, value);
  }, attachment);
};

const processReceiptAttachment = (userData) => {
  const attachment = fs.readFileSync(
    '/usr/src/web/api/v20/views/mail-templates/projectSubscriptionReceiptDocument.html',
    { encoding: 'utf-8' }
  );
  let processed = Object.entries(userData).reduce((acc, [key, value]) => {
    return acc.replace(`$${key}`, value);
  }, attachment);

  const cardTypeImages = {
    'american express': 'https://d1k9lc9xzt55uy.cloudfront.net/b060fb7b-ef9b-4f93-8e28-d885181c5e4b.png',
    'master card': 'https://d1k9lc9xzt55uy.cloudfront.net/8cf8bb34-53c8-4988-8e9e-910ae297c5bc.png',
    'visa': 'https://d1k9lc9xzt55uy.cloudfront.net/f12a8d0e-0d55-471e-941d-93791d87c538.png'
  };

  if (userData.cardType && cardTypeImages[userData.cardType]) {
    processed = processed.replace('$cardImageType', cardTypeImages[userData.cardType]);
  }

  return processed;
};

const getTemplateReplacements = (templateName, user) => {
  const replacements = {
    register: () => ({
      $firstName: user.firstName,
      $email: user.email,
      $link: user.link,
      $password: user.generatedPassword
    }),
    forgotPassword: () => ({
      $name: user.firstName,
      $link: user.link
    }),
    schedulerReport: () => ({
      $Subject: user.subject,
      $Report_Name: user.reportName,
      $S3URL: user.s3_url,
      $Message: user.message
    }),
    schedulerReportNoData: () => ({
      $StartDate: moment(user.customStartDate).format('ddd, MMM DD YYYY'),
      $EndDate: moment(user.customEndDate).format('ddd, MMM DD YYYY')
    }),
    addproject: () => ({
      $name: user.firstName,
      $projectName: user.projectName,
      $type: user.type
    }),
    trailPlanEndingRemainder: () => ({
      $name: user.firstName,
      $projectName: user.projectName,
      $plan: user.planName,
      $link: `${user.link}/login`,
      $expiryDays: user.expiryDays
    }),
    'Account Creation': () => ({
      $firstName: user.firstName,
      $email: user.email,
      $password: user.password,
      domainURL: user.domainURL,
      $link: `${user.domainURL}/login`,
      $amount: user.amount
    }),
    'Account Member': () => ({
      $firstName: user.firstName,
      $email: user.email,
      $password: user.password,
      domainURL: user.domainURL,
      $link: `${user.domainURL}/login`
    }),
    accountadminpay: () => ({
      $firstName: user.firstName,
      $email: user.email,
      $status: user.status,
      amount: user.amount,
      currency: user.currency
    }),
    invite_member: () => ({
      $type: user.type,
      $link: user.link
    }),
    commentadded: () => ({
      $NDR_AuthorName: user.toEmailUserName,
      $deliveryID: user.deliveryId,
      $commentedUserName: user.commentedPersonname,
      $deliveryId: user.deliveryId,
      $description: user.deliveryDescription,
      $timestamp: user.deliveryStart,
      $comment_timestamp: user.commentTimeStamp,
      $newComment: user.newComment,
      $previousComments: user.previousComments
    }),
    cranecommentadded: () => ({
      $NDR_AuthorName: user.toEmailUserName,
      $craneID: user.craneId,
      $commentedUserName: user.commentedPersonname,
      $craneId: user.craneId,
      $description: user.craneDescription,
      $timestamp: user.craneDeliveryStart,
      $comment_timestamp: user.commentTimeStamp,
      $newComment: user.newComment,
      $previousComments: user.previousComments
    }),
    concretecommentadded: () => ({
      $NDR_AuthorName: user.toEmailUserName,
      $concreteID: user.concreteId,
      $commentedUserName: user.commentedPersonname,
      $concreteId: user.concreteId,
      $description: user.concreteDescription,
      $timestamp: user.concreteStart,
      $comment_timestamp: user.commentTimeStamp,
      $newComment: user.newComment,
      $previousComments: user.previousComments
    }),
    adminoverride: () => ({
      $name: user.firstName,
      $content: `Your Override Booking status"${user.status}"`
    }),
    upgradeplan: () => ({
      $userName: user.firstName,
      $projectInterval: user.interval,
      $projectSubscribedName: user.projectName,
      $projectSubscribedStartDate: user.projectStartDate,
      $projectSubscribedEndDate: user.projectEndDate
    }),
    signUpSubscription: () => ({
      $name: user.firstName,
      $interval: user.interval,
      $projectName: user.projectName,
      $projectStartDate: user.projectStartDate,
      $projectEndDate: user.projectEndDate
    }),
    trailPlanExpired: () => ({
      $name: user.firstName,
      $projectName: user.projectName,
      $plan: user.planName,
      $link: `${user.link}/login`
    }),
    trialPlanBegins: () => ({
      $name: user.firstName
    }),
    assignPAtoNewProjectByAccountadmin: () => ({
      $projectAdminName: user.projectAdminName,
      $accountAdminName: user.accountAdminName,
      $projectName: user.projectName,
      $link: `${user.link}/login`
    }),
    userPasswordChangeBySuperAdmin: () => ({
      $firstName: user.firstName,
      $email: user.email,
      $password: user.password
    }),
    projectSubscriptionInvoice: () => ({
      $name: user.firstName,
      $date: user.projectEndDate
    }),
    editProjectBySA: () => ({
      $name: user.name,
      $projectName: user.projectName
    }),
    dailyDigestNotification: () => ({
      $mailContent: user.mailContent,
      $mailSentTime: user.mailSentTime
    }),
    notifyPAForApproval: () => ({
      $content: user.content,
      $userName: user.name
    }),
    notifyPAForReApproval: () => ({
      $content: user.content,
      $userName: user.name
    }),
    completedConcreteRequest: () => ({
      $userName: user.userName,
      $userName1: user.userName1,
      $concreteID: user.concreteID,
      $concreteId: user.concreteId,
      $description: user.description,
      $status_timestamp: user.status_timestamp,
      $timestamp: user.timestamp
    }),
    deliveredDR: () => ({
      $userName: user.userName,
      $userName1: user.userName1,
      $deliveryID: user.deliveryID,
      $deliveryId: user.deliveryId,
      $description: user.description,
      $status_timestamp: user.status_timestamp,
      $timestamp: user.timestamp
    }),
    completedCraneRequest: () => ({
      $userName: user.userName,
      $userName1: user.userName1,
      $craneID: user.craneID,
      $craneId: user.craneId,
      $description: user.description,
      $status_timestamp: user.status_timestamp,
      $timestamp: user.timestamp
    }),
    concreteRequestCreated: () => ({
      $userName: user.userName,
      $concreteId: user.concreteId,
      $description: user.description,
      $created_timestamp: user.createdTimestamp,
      $timestamp: user.timestamp,
      $content: user.content
    }),
    deliveryRequestCreated: () => ({
      $userName: user.userName,
      $deliveryId: user.deliveryId,
      $description: user.description,
      $created_timestamp: user.createdTimestamp,
      $timestamp: user.timestamp,
      $content: user.content
    }),
    craneRequestCreated: () => ({
      $userName: user.userName,
      $craneId: user.craneId,
      $description: user.description,
      $created_timestamp: user.createdTimestamp,
      $timestamp: user.timestamp,
      $content: user.content
    }),
    guestApproved: () => ({
      $firstName: user.guestFirstName,
      $lastName: user.guestLastName,
      $email: user.email,
      $projectName: user.projectName,
      $link: `${user.link}/login`,
      $password: user.password
    }),
    guestRejected: () => ({
      $firstName: user.guestFirstName,
      $lastName: user.guestLastName,
      $projectName: user.projectName
    }),
    guestRequested: () => ({
      $firstName: user.adminFirstName,
      $guestFirstName: user.guestFirstName,
      $projectName: user.projectName
    }),
    notifyGuestOnEdit: () => ({
      $content: user.content,
      $userName: user.guestName
    })
  };

  return replacements[templateName] ? replacements[templateName]() : {};
};

const processTemplate = (template, replacements) => {
  return Object.entries(replacements).reduce((acc, [key, value]) => {
    return acc.replace(key, value);
  }, template);
};

async function getHtml(templateName, userData) {
  const templateFile = TEMPLATE_MAPPINGS[templateName];
  if (!templateFile) {
    throw new Error(`Unknown template: ${templateName}`);
  }

  // Handle attachments for specific templates
  if (templateName === 'projectSubscriptionInvoice') {
    invoiceAttachment = processInvoiceAttachment(userData);
  } else if (templateName === 'upgradeplan' || templateName === 'signUpSubscription') {
    invoiceAttachment = processReceiptAttachment(userData);
  }

  // Read the email template
  const emailTemplate = fs.readFileSync(
    `/usr/src/web/api/v20/views/mail-templates/${templateFile}`,
    { encoding: 'utf-8' }
  );

  if (process.env.NODE_ENV === 'test') return 'success';

  // Process user data
  let user = userData;
  if (!user.origin) {
    user.link = process.env.BASE_URL;
  }

  // Handle deep links for specific templates
  const deepLinkTemplates = {
    invite_member: deepLinkService.getInviteMemberDeepLink,
    forgotPassword: deepLinkService.getForgotPasswordDeepLink,
    register: deepLinkService.getRegistrationDeepLink
  };

  if (deepLinkTemplates[templateName]) {
    user = await deepLinkTemplates[templateName](user);
  }

  // Get template-specific replacements and process the template
  const replacements = getTemplateReplacements(templateName, user);
  return processTemplate(emailTemplate, replacements);
}

async function sendReportMail(user) {
  try {
    const html = await getHtml(user.emailTemplate, user);
    const mailOptions = {
      'h:sender': 'Follo <<EMAIL>>',
      from: 'Follo <<EMAIL>>',
      to: user.sendTo,
      subject: user.subject,
      html,
    };
    const msg = await mg.messages.create(process.env.MAILGUN_DOMAIN_URL, mailOptions);
    console.log(msg);
    return msg;
  } catch (e) {
    console.log('*******sendReportMail******8', e);
    throw e;
  }
}

async function sendMail(user, templateName, subject, tagName, callback) {
  const html = await getHtml(templateName, user);
  const mailOptions = {
    'h:sender': 'Follo <<EMAIL>>',
    from: 'Follo <<EMAIL>>',
    to: user.email,
    subject,
    html,
    'o:tag': [tagName],
  };
  if (
    templateName === 'projectSubscriptionInvoice' ||
    templateName === 'upgradeplan' ||
    templateName === 'signUpSubscription'
  ) {
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--disable-gpu',
        '--disable-dev-shm-usage',
        '--disable-setuid-sandbox',
        '--no-sandbox',
      ],
    });
    const page = await browser.newPage();
    await page.setContent(invoiceAttachment, { waitUntil: 'networkidle0' });
    const pdf = await page.pdf({
      format: 'A4',
      margin: {
        top: '15px',
        bottom: '15px',
        left: '15px',
        right: '15px',
      },
      pageRanges: '',
    });
    await browser.close();
    mailOptions.attachment = pdf;
    console.log('File created by puppeteer');
    await mg.messages
      .create(process.env.MAILGUN_DOMAIN_URL, mailOptions)
      .then((msg) => {
        console.log(msg);
        callback(msg);
      })
      .catch((err) => console.log(err));
  } else {
    await mg.messages
      .create(process.env.MAILGUN_DOMAIN_URL, mailOptions)
      .then((msg) => {
        console.log(msg);
        callback(msg);
      })
      .catch((err) => console.log(err));
  }
}

module.exports = {
  sendMail,
  sendReportMail,
};
