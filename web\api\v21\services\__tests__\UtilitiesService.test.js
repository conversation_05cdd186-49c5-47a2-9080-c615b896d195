const status = require('http-status');
const UtilitiesService = require('../UtilitiesService');
const { Utilities } = require('../../models');

// Mock the models
jest.mock('../../models', () => ({
    Utilities: {
        createUtilities: jest.fn(),
        bulkCreate: jest.fn(),
        getAll: jest.fn(),
    },
}));

describe('UtilitiesService', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('addUtilities', () => {
        it('should successfully add utilities with item details', async () => {
            // Mock data
            const mockResponse = { id: 1 };
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: [
                        {
                            item_name: 'Electricity',
                            quantity: 100,
                            unit: 'kWh',
                            cost: 50,
                            'CO2 Emissions': 25,
                        },
                        {
                            Material: 'Water',
                            Quantity: 200,
                            unit: 'L',
                            Cost: 30,
                        },
                    ],
                },
            };

            // Mock implementation
            Utilities.createUtilities.mockResolvedValue(mockResponse);
            Utilities.bulkCreate.mockResolvedValue([{ id: 1 }, { id: 2 }]);

            // Execute
            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            // Assertions
            expect(Utilities.createUtilities).toHaveBeenCalledWith(mockInputData.body);
            expect(Utilities.bulkCreate).toHaveBeenCalledWith([
                {
                    utilityType: 'Electricity',
                    quantity: 100,
                    unit: 'kWh',
                    parentId: 1,
                    projectId: 123,
                    cost: 50,
                    co2_emissions: 25,
                },
                {
                    utilityType: 'Water',
                    quantity: 200,
                    unit: 'L',
                    parentId: 1,
                    projectId: 123,
                    cost: 30,
                    co2_emissions: '',
                },
            ]);
            expect(result.data).toBeUndefined(); // The method returns undefined due to .then() not returning anything
            expect(result.error).toBe(false);
        });

        it('should handle empty item details', async () => {
            const mockResponse = { id: 1 };
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: [],
                },
            };

            Utilities.createUtilities.mockResolvedValue(mockResponse);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.createUtilities).toHaveBeenCalledWith(mockInputData.body);
            expect(Utilities.bulkCreate).toHaveBeenCalledWith([]); // Empty array is still passed to bulkCreate
            expect(result.data).toBeUndefined();
            expect(result.error).toBe(false);
        });

        it('should handle missing item_details property', async () => {
            const mockResponse = { id: 1 };
            const mockInputData = {
                body: {
                    projectId: 123,
                    // No item_details property
                },
            };

            Utilities.createUtilities.mockResolvedValue(mockResponse);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.createUtilities).toHaveBeenCalledWith(mockInputData.body);
            expect(Utilities.bulkCreate).toHaveBeenCalledWith([]);
            expect(result.data).toBeUndefined();
            expect(result.error).toBe(false);
        });

        it('should handle null item_details', async () => {
            const mockResponse = { id: 1 };
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: null,
                },
            };

            Utilities.createUtilities.mockResolvedValue(mockResponse);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.createUtilities).toHaveBeenCalledWith(mockInputData.body);
            expect(Utilities.bulkCreate).toHaveBeenCalledWith([]);
            expect(result.data).toBeUndefined();
            expect(result.error).toBe(false);
        });

        it('should handle items with missing optional fields', async () => {
            const mockResponse = { id: 1 };
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: [
                        {
                            item_name: 'Gas',
                            quantity: 50,
                            unit: 'm³',
                            // Missing cost and CO2 Emissions
                        },
                        {
                            Material: 'Steam',
                            Quantity: 75,
                            // Missing unit, cost, and CO2 Emissions
                        },
                    ],
                },
            };

            Utilities.createUtilities.mockResolvedValue(mockResponse);
            Utilities.bulkCreate.mockResolvedValue([{ id: 1 }, { id: 2 }]);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.bulkCreate).toHaveBeenCalledWith([
                {
                    utilityType: 'Gas',
                    quantity: 50,
                    unit: 'm³',
                    parentId: 1,
                    projectId: 123,
                    cost: undefined,
                    co2_emissions: '',
                },
                {
                    utilityType: 'Steam',
                    quantity: 75,
                    unit: undefined,
                    parentId: 1,
                    projectId: 123,
                    cost: undefined,
                    co2_emissions: '',
                },
            ]);
            expect(result.data).toBeUndefined();
            expect(result.error).toBe(false);
        });

        it('should handle bulkCreate errors', async () => {
            const mockResponse = { id: 1 };
            const mockError = new Error('BulkCreate failed');
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: [
                        {
                            item_name: 'Electricity',
                            quantity: 100,
                            unit: 'kWh',
                            cost: 50,
                        },
                    ],
                },
            };

            Utilities.createUtilities.mockResolvedValue(mockResponse);
            Utilities.bulkCreate.mockRejectedValue(mockError);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(result.data).toBeNull();
            expect(result.error).toBe(mockError);
        });

        it('should handle database errors', async () => {
            const mockError = new Error('Database error');
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: [],
                },
            };

            Utilities.createUtilities.mockRejectedValue(mockError);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(result.data).toBeNull();
            expect(result.error).toBe(mockError);
        });

        it('should handle mixed field naming conventions', async () => {
            const mockResponse = { id: 1 };
            const mockInputData = {
                body: {
                    projectId: 123,
                    item_details: [
                        {
                            item_name: 'Electricity',
                            quantity: 100,
                            unit: 'kWh',
                            cost: 50,
                            'CO2 Emissions': 25,
                        },
                        {
                            Material: 'Water',
                            Quantity: 200,
                            unit: 'L',
                            Cost: 30,
                            // No CO2 Emissions field
                        },
                        {
                            item_name: 'Gas',
                            Quantity: 150, // Mixed naming: item_name with Quantity
                            unit: 'm³',
                            Cost: 75,
                            'CO2 Emissions': 40,
                        },
                    ],
                },
            };

            Utilities.createUtilities.mockResolvedValue(mockResponse);
            Utilities.bulkCreate.mockResolvedValue([{ id: 1 }, { id: 2 }, { id: 3 }]);

            const result = await new Promise((resolve) => {
                UtilitiesService.addUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.bulkCreate).toHaveBeenCalledWith([
                {
                    utilityType: 'Electricity',
                    quantity: 100,
                    unit: 'kWh',
                    parentId: 1,
                    projectId: 123,
                    cost: 50,
                    co2_emissions: 25,
                },
                {
                    utilityType: 'Water',
                    quantity: 200,
                    unit: 'L',
                    parentId: 1,
                    projectId: 123,
                    cost: 30,
                    co2_emissions: '',
                },
                {
                    utilityType: 'Gas',
                    quantity: 150,
                    unit: 'm³',
                    parentId: 1,
                    projectId: 123,
                    cost: 75,
                    co2_emissions: 40,
                },
            ]);
            expect(result.data).toBeUndefined();
            expect(result.error).toBe(false);
        });
    });

    describe('listUtilities', () => {
        it('should successfully list utilities with pagination and sorting', async () => {
            const mockUtilities = {
                rows: [
                    { id: 1, equipmentName: 'Utility A' },
                    { id: 2, equipmentName: 'Utility B' },
                ],
                count: 2,
            };

            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                    isFilter: true,
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.getAll).toHaveBeenCalledWith(
                {
                    isDeleted: false,
                    projectId: 123,
                    parentId: null,
                },
                10,
                0,
                {},
                'ASC',
                'equipmentName',
            );
            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle empty utilities list', async () => {
            const mockUtilities = {
                rows: [],
                count: 0,
            };

            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                    isFilter: true,
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle array response format', async () => {
            const mockUtilities = [
                { id: 1, equipmentName: 'Utility A' },
                { id: 2, equipmentName: 'Utility B' },
            ];

            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                    isFilter: true,
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle database errors', async () => {
            const mockError = new Error('Database error');
            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockRejectedValue(mockError);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(result.data).toBeNull();
            expect(result.error).toBe(mockError);
        });

        it('should handle pagination correctly', async () => {
            const mockUtilities = {
                rows: [],
                count: 0,
            };

            const mockInputData = {
                params: {
                    pageNo: 2,
                    pageSize: 5,
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.getAll).toHaveBeenCalledWith(
                expect.any(Object),
                5,
                5, // offset = (pageNo - 1) * pageSize
                expect.any(Object),
                'ASC',
                'equipmentName',
            );
            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle missing body parameters', async () => {
            const mockUtilities = {
                rows: [{ id: 1, equipmentName: 'Utility A' }],
                count: 1,
            };

            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {}, // Empty body
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.getAll).toHaveBeenCalledWith(
                {
                    isDeleted: false,
                    projectId: 123,
                    parentId: null,
                },
                10,
                0,
                {},
                undefined, // sort is undefined
                undefined, // sortByField is undefined
            );
            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle DESC sort order', async () => {
            const mockUtilities = {
                rows: [
                    { id: 1, equipmentName: 'Utility B' },
                    { id: 2, equipmentName: 'Utility A' },
                ],
                count: 2,
            };

            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {
                    sort: 'DESC',
                    sortByField: 'equipmentName',
                    isFilter: true,
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.getAll).toHaveBeenCalledWith(
                {
                    isDeleted: false,
                    projectId: 123,
                    parentId: null,
                },
                10,
                0,
                {},
                'DESC',
                'equipmentName',
            );
            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle isFilter = false', async () => {
            const mockUtilities = {
                rows: [
                    { id: 1, equipmentName: 'Utility B' },
                    { id: 2, equipmentName: 'Utility A' },
                ],
                count: 2,
            };

            const mockInputData = {
                params: {
                    pageNo: 1,
                    pageSize: 10,
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                    isFilter: false,
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            // When isFilter is false, no sorting should be applied
            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });

        it('should handle string pageNo and pageSize conversion', async () => {
            const mockUtilities = {
                rows: [],
                count: 0,
            };

            const mockInputData = {
                params: {
                    pageNo: '3', // String instead of number
                    pageSize: '15', // String instead of number
                },
                body: {
                    sort: 'ASC',
                    sortByField: 'equipmentName',
                },
                query: {
                    projectId: 123,
                },
            };

            Utilities.getAll.mockResolvedValue(mockUtilities);

            const result = await new Promise((resolve) => {
                UtilitiesService.listUtilities(mockInputData, (data, error) => {
                    resolve({ data, error });
                });
            });

            expect(Utilities.getAll).toHaveBeenCalledWith(
                expect.any(Object),
                15, // Converted to number
                30, // offset = (3 - 1) * 15
                expect.any(Object),
                'ASC',
                'equipmentName',
            );
            expect(result.data).toEqual(mockUtilities);
            expect(result.error).toBe(false);
        });
    });
});
