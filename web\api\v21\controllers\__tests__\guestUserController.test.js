const guestUserController = require('../guestUserController');
const guestUserService = require('../../services/guestUserService');
const { deliveryService, craneRequestService } = require('../../services');
const { ProjectSettings, TimeZone, Project } = require('../../models');

// Mock dependencies
jest.mock('../../services/guestUserService', () => ({
  getCompanies: jest.fn(),
  createGuestUser: jest.fn(),
  alreadyVisited: jest.fn(),
  guestUserDetail: jest.fn(),
  lastDeliveryId: jest.fn(),
  listAllMember: jest.fn(),
  getEventNDR: jest.fn(),
  getDeliveryRequestWithCrane: jest.fn(),
  getConcreteRequest: jest.fn(),
  getAll: jest.fn(),
  listGates: jest.fn(),
  lastGate: jest.fn(),
  listEquipment: jest.fn(),
  lastEquipment: jest.fn(),
  getAllCompany: jest.fn(),
  getDefinableWork: jest.fn(),
  getLocations: jest.fn(),
  lastCraneRequest: jest.fn(),
  getMemberDataMixPanel: jest.fn(),
  getMemberData: jest.fn(),
  searchMember: jest.fn(),
  newRequest: jest.fn(),
  craneListEquipment: jest.fn(),
  newCraneRequest: jest.fn(),
  getConcreteDropdownDetail: jest.fn(),
  newConcreteRequest: jest.fn(),
  getNDRData: jest.fn(),
  getSingleCraneRequest: jest.fn(),
  getSingleConcreteRequest: jest.fn(),
  editRequest: jest.fn(),
  editCraneRequest: jest.fn(),
  editConcreteRequest: jest.fn(),
  createAttachement: jest.fn(),
  createComment: jest.fn(),
  createCraneRequestAttachement: jest.fn(),
  createCraneRequestComment: jest.fn(),
  createConcreteRequestAttachment: jest.fn(),
  createConcreteRequestComment: jest.fn(),
  isRequestToMember: jest.fn(),
  updateGuestMember: jest.fn(),
}));

jest.mock('../../services', () => ({
  deliveryService: {
    lastDelivery: jest.fn(),
  },
  craneRequestService: {
    lastCraneRequest: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  ProjectSettings: {
    getCalendarStatusColor: jest.fn(),
    getCalendarCard: jest.fn(),
  },
  TimeZone: {
    getAll: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
  },
}));

describe('guestUserController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      body: {},
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('getCompanyList', () => {
    it('should get company list successfully', async () => {
      const mockResponse = { companies: [{ id: 1, name: 'Company 1' }] };
      guestUserService.getCompanies.mockResolvedValue(mockResponse);

      await guestUserController.getCompanyList(mockReq, mockRes, mockNext);

      expect(guestUserService.getCompanies).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle company not found', async () => {
      guestUserService.getCompanies.mockResolvedValue(null);

      await guestUserController.getCompanyList(mockReq, mockRes, mockNext);

      expect(guestUserService.getCompanies).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Company not found' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from company list', async () => {
      const mockError = new Error('Service error');
      guestUserService.getCompanies.mockRejectedValue(mockError);

      await guestUserController.getCompanyList(mockReq, mockRes, mockNext);

      expect(guestUserService.getCompanies).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createGuestUser', () => {
    it('should create guest user successfully', async () => {
      const mockResponse = { id: 1, name: 'Guest User' };
      guestUserService.createGuestUser.mockResolvedValue(mockResponse);

      await guestUserController.createGuestUser(mockReq, mockRes);

      expect(guestUserService.createGuestUser).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
    });

    it('should handle active member error', async () => {
      const mockResponse = { activeMember: true, message: 'Already active' };
      guestUserService.createGuestUser.mockResolvedValue(mockResponse);

      await guestUserController.createGuestUser(mockReq, mockRes);

      expect(guestUserService.createGuestUser).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
    });

    it('should handle error from guest user creation', async () => {
      const mockError = new Error('Service error');
      guestUserService.createGuestUser.mockRejectedValue(mockError);

      await guestUserController.createGuestUser(mockReq, mockRes);

      expect(guestUserService.createGuestUser).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: mockError.message });
    });
  });

  describe('alreadyVisited', () => {
    it('should handle new user response', async () => {
      const mockResponse = { newUser: true, data: 'new user' };
      guestUserService.alreadyVisited.mockResolvedValue(mockResponse);

      await guestUserController.alreadyVisited(mockReq, mockRes, mockNext);

      expect(guestUserService.alreadyVisited).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle active member response', async () => {
      const mockResponse = { activeMember: true };
      guestUserService.alreadyVisited.mockResolvedValue(mockResponse);

      await guestUserController.alreadyVisited(mockReq, mockRes, mockNext);

      expect(guestUserService.alreadyVisited).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle user not found', async () => {
      const mockResponse = { someOtherProperty: 'value' };
      guestUserService.alreadyVisited.mockResolvedValue(mockResponse);

      await guestUserController.alreadyVisited(mockReq, mockRes, mockNext);

      expect(guestUserService.alreadyVisited).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User not found' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from already visited', async () => {
      const mockError = new Error('Service error');
      guestUserService.alreadyVisited.mockRejectedValue(mockError);

      await guestUserController.alreadyVisited(mockReq, mockRes, mockNext);

      expect(guestUserService.alreadyVisited).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('guestUserDetails', () => {
    it('should get guest user details successfully', async () => {
      const mockResponse = { id: 1, details: 'user details' };
      guestUserService.guestUserDetail.mockResolvedValue(mockResponse);

      await guestUserController.guestUserDetails(mockReq, mockRes, mockNext);

      expect(guestUserService.guestUserDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle user details not found', async () => {
      guestUserService.guestUserDetail.mockResolvedValue(null);

      await guestUserController.guestUserDetails(mockReq, mockRes, mockNext);

      expect(guestUserService.guestUserDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User details not found' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from guest user details', async () => {
      const mockError = new Error('Service error');
      guestUserService.guestUserDetail.mockRejectedValue(mockError);

      await guestUserController.guestUserDetails(mockReq, mockRes, mockNext);

      expect(guestUserService.guestUserDetail).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('lastDeliveryId', () => {
    it('should get last delivery id successfully', async () => {
      const mockResponse = { id: 1 };
      guestUserService.lastDeliveryId.mockResolvedValue(mockResponse);

      await guestUserController.lastDeliveryId(mockReq, mockRes, mockNext);

      expect(guestUserService.lastDeliveryId).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ lastId: mockResponse });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no last delivery id', async () => {
      guestUserService.lastDeliveryId.mockResolvedValue(null);

      await guestUserController.lastDeliveryId(mockReq, mockRes, mockNext);

      expect(guestUserService.lastDeliveryId).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from last delivery id', async () => {
      const mockError = new Error('Service error');
      guestUserService.lastDeliveryId.mockRejectedValue(mockError);

      await guestUserController.lastDeliveryId(mockReq, mockRes, mockNext);

      expect(guestUserService.lastDeliveryId).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listAllMember', () => {
    it('should list all members successfully', async () => {
      const mockResponse = [{ id: 1, name: 'Member 1' }];
      guestUserService.listAllMember.mockResolvedValue(mockResponse);

      await guestUserController.listAllMember(mockReq, mockRes, mockNext);

      expect(guestUserService.listAllMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Member listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no members found', async () => {
      guestUserService.listAllMember.mockResolvedValue(null);

      await guestUserController.listAllMember(mockReq, mockRes, mockNext);

      expect(guestUserService.listAllMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list all members', async () => {
      const mockError = new Error('Service error');
      guestUserService.listAllMember.mockRejectedValue(mockError);

      await guestUserController.listAllMember(mockReq, mockRes, mockNext);

      expect(guestUserService.listAllMember).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getEventNDR', () => {
    it('should get event NDR successfully with filter count 0', async () => {
      const mockResponse = { rows: [{ id: 1, event: 'test' }] };
      const mockLastDetail = { id: 1 };
      const mockStatusData = { status: 'active' };
      const mockCardData = { card: 'data' };

      mockReq.body.filterCount = 0;
      mockReq.params.ProjectId = '123';
      mockReq.body.start = '2023-01-01';
      mockReq.body.end = '2023-01-31';
      mockReq.body.ParentCompanyId = '456';
      mockReq.body.search = 'test';

      guestUserService.getEventNDR.mockResolvedValue(mockResponse);
      guestUserService.getAll.mockResolvedValue([{ id: 2, additional: 'data' }]);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardData);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await guestUserController.getEventNDR(mockReq, mockRes, mockNext);

      expect(guestUserService.getEventNDR).toHaveBeenCalledWith(mockReq);
      expect(guestUserService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('123');
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: [...mockResponse.rows, { id: 2, additional: 'data' }],
        statusData: mockStatusData,
        cardData: mockCardData,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should get event NDR successfully with filter count > 0', async () => {
      const mockResponse = { rows: [{ id: 1, event: 'test' }] };
      const mockLastDetail = { id: 1 };

      mockReq.body.filterCount = 1;
      mockReq.params.ProjectId = '';

      guestUserService.getEventNDR.mockResolvedValue(mockResponse);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await guestUserController.getEventNDR(mockReq, mockRes, mockNext);

      expect(guestUserService.getEventNDR).toHaveBeenCalledWith(mockReq);
      expect(guestUserService.getAll).not.toHaveBeenCalled();
      expect(ProjectSettings.getCalendarStatusColor).not.toHaveBeenCalled();
      expect(ProjectSettings.getCalendarCard).not.toHaveBeenCalled();
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: mockResponse.rows,
        statusData: undefined,
        cardData: undefined,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from last delivery', async () => {
      const mockResponse = { rows: [{ id: 1, event: 'test' }] };
      const mockError = new Error('Last delivery error');

      guestUserService.getEventNDR.mockResolvedValue(mockResponse);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.getEventNDR(mockReq, mockRes, mockNext);

      expect(guestUserService.getEventNDR).toHaveBeenCalledWith(mockReq);
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from event NDR', async () => {
      const mockError = new Error('Service error');
      guestUserService.getEventNDR.mockRejectedValue(mockError);

      await guestUserController.getEventNDR(mockReq, mockRes, mockNext);

      expect(guestUserService.getEventNDR).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getDeliveryRequestWithCrane', () => {
    it('should get delivery request with crane successfully with filter count 0', async () => {
      const mockResponse = [{ id: 1, delivery: 'test' }];
      const mockLastDetail = { id: 1 };
      const mockStatusData = { status: 'active' };
      const mockCardData = { card: 'data' };

      mockReq.body.filterCount = 0;
      mockReq.params.ProjectId = '123';
      mockReq.body.start = '2023-01-01';
      mockReq.body.end = '2023-01-31';
      mockReq.body.ParentCompanyId = '456';
      mockReq.body.search = 'test';

      guestUserService.getDeliveryRequestWithCrane.mockResolvedValue(mockResponse);
      guestUserService.getAll.mockResolvedValue([{ id: 2, additional: 'data' }]);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardData);
      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await guestUserController.getDeliveryRequestWithCrane(mockReq, mockRes, mockNext);

      expect(guestUserService.getDeliveryRequestWithCrane).toHaveBeenCalledWith(mockReq);
      expect(guestUserService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('123');
      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Associated With Crane Equipment Type listed Successfully.',
        data: [...mockResponse, { id: 2, additional: 'data' }],
        statusData: mockStatusData,
        cardData: mockCardData,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request service', async () => {
      const mockResponse = [{ id: 1, delivery: 'test' }];
      const mockError = new Error('Crane request error');

      guestUserService.getDeliveryRequestWithCrane.mockResolvedValue(mockResponse);
      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.getDeliveryRequestWithCrane(mockReq, mockRes, mockNext);

      expect(guestUserService.getDeliveryRequestWithCrane).toHaveBeenCalledWith(mockReq);
      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in getDeliveryRequestWithCrane', async () => {
      const mockError = new Error('Service error');
      guestUserService.getDeliveryRequestWithCrane.mockRejectedValue(mockError);

      await guestUserController.getDeliveryRequestWithCrane(mockReq, mockRes, mockNext);

      expect(guestUserService.getDeliveryRequestWithCrane).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getConcreteRequest', () => {
    it('should get concrete request successfully', async () => {
      const mockResponse = [{ id: 1, concrete: 'test' }];
      const mockStatusData = { status: 'active' };
      const mockCardData = { card: 'data' };

      mockReq.body.filterCount = 0;
      mockReq.params.ProjectId = '123';
      mockReq.body.start = '2023-01-01';
      mockReq.body.end = '2023-01-31';
      mockReq.body.ParentCompanyId = '456';
      mockReq.body.search = 'test';

      guestUserService.getConcreteRequest.mockImplementation((req, callback) => {
        // Simulate async callback
        setTimeout(async () => {
          await callback(mockResponse, null);
        }, 0);
      });
      guestUserService.getAll.mockResolvedValue([{ id: 2, additional: 'data' }]);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardData);

      await guestUserController.getConcreteRequest(mockReq, mockRes, mockNext);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(guestUserService.getConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(guestUserService.getAll).toHaveBeenCalledWith(mockReq);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('123');
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete booking listed successfully',
        data: [...mockResponse, { id: 2, additional: 'data' }],
        statusData: mockStatusData,
        cardData: mockCardData,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from concrete request service', async () => {
      const mockError = new Error('Concrete request error');

      guestUserService.getConcreteRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.getConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in getConcreteRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.getConcreteRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.getConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listGates', () => {
    it('should list gates successfully', async () => {
      const mockGateDetail = [{ id: 1, name: 'Gate 1' }];
      const mockLastDetail = { id: 1 };

      guestUserService.listGates.mockResolvedValue(mockGateDetail);
      guestUserService.lastGate.mockResolvedValue(mockLastDetail);

      await guestUserController.listGates(mockReq, mockRes, mockNext);

      expect(guestUserService.listGates).toHaveBeenCalledWith(mockReq);
      expect(guestUserService.lastGate).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate Listed successfully.',
        data: mockGateDetail,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from listGates', async () => {
      const mockError = new Error('Service error');
      guestUserService.listGates.mockRejectedValue(mockError);

      await guestUserController.listGates(mockReq, mockRes, mockNext);

      expect(guestUserService.listGates).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listEquipment', () => {
    it('should list equipment successfully', async () => {
      const mockEquipmentDetail = [{ id: 1, name: 'Equipment 1' }];
      const mockLastDetail = { id: 1 };

      guestUserService.listEquipment.mockResolvedValue(mockEquipmentDetail);
      guestUserService.lastEquipment.mockResolvedValue(mockLastDetail);

      await guestUserController.listEquipment(mockReq, mockRes, mockNext);

      expect(guestUserService.listEquipment).toHaveBeenCalledWith(mockReq);
      expect(guestUserService.lastEquipment).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Listed successfully.',
        data: mockEquipmentDetail,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from listEquipment', async () => {
      const mockError = new Error('Service error');
      guestUserService.listEquipment.mockRejectedValue(mockError);

      await guestUserController.listEquipment(mockReq, mockRes, mockNext);

      expect(guestUserService.listEquipment).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getCompanies', () => {
    it('should get companies successfully with parent company', async () => {
      const mockResponse = {
        companyList: {
          rows: [
            { id: 1, companyName: 'Company A' },
            { id: 2, companyName: 'Company B' },
          ],
        },
        parentCompany: { id: 3, companyName: 'Parent Company' },
      };

      guestUserService.getAllCompany.mockResolvedValue(mockResponse);

      await guestUserController.getCompanies(mockReq, mockRes, mockNext);

      expect(guestUserService.getAllCompany).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: [
          { id: 1, companyName: 'Company A' },
          { id: 2, companyName: 'Company B' },
          { id: 3, companyName: 'Parent Company' },
        ],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should get companies successfully without parent company', async () => {
      const mockResponse = {
        companyList: {
          rows: [
            { id: 1, companyName: 'Company A' },
            { id: 2, companyName: 'Company B' },
          ],
        },
        parentCompany: null,
      };

      guestUserService.getAllCompany.mockResolvedValue(mockResponse);

      await guestUserController.getCompanies(mockReq, mockRes, mockNext);

      expect(guestUserService.getAllCompany).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: [
          { id: 1, companyName: 'Company A' },
          { id: 2, companyName: 'Company B' },
        ],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getAllCompany', async () => {
      guestUserService.getAllCompany.mockResolvedValue(null);

      await guestUserController.getCompanies(mockReq, mockRes, mockNext);

      expect(guestUserService.getAllCompany).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getCompanies', async () => {
      const mockError = new Error('Service error');
      guestUserService.getAllCompany.mockRejectedValue(mockError);

      await guestUserController.getCompanies(mockReq, mockRes, mockNext);

      expect(guestUserService.getAllCompany).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getDefinableWork', () => {
    it('should get definable work successfully', async () => {
      const mockResponse = { defineRecord: [{ id: 1, work: 'Work 1' }] };

      guestUserService.getDefinableWork.mockResolvedValue(mockResponse);

      await guestUserController.getDefinableWork(mockReq, mockRes, mockNext);

      expect(guestUserService.getDefinableWork).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable work list.',
        data: mockResponse.defineRecord,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getDefinableWork', async () => {
      guestUserService.getDefinableWork.mockResolvedValue(null);

      await guestUserController.getDefinableWork(mockReq, mockRes, mockNext);

      expect(guestUserService.getDefinableWork).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getDefinableWork', async () => {
      const mockError = new Error('Service error');
      guestUserService.getDefinableWork.mockRejectedValue(mockError);

      await guestUserController.getDefinableWork(mockReq, mockRes, mockNext);

      expect(guestUserService.getDefinableWork).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getLocations', () => {
    it('should get locations successfully', async () => {
      const mockLocations = [{ id: 1, name: 'Location 1' }];

      guestUserService.getLocations.mockResolvedValue(mockLocations);

      await guestUserController.getLocations(mockReq, mockRes, mockNext);

      expect(guestUserService.getLocations).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Location Listed successfully.',
        data: mockLocations,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no locations found', async () => {
      guestUserService.getLocations.mockResolvedValue(null);

      await guestUserController.getLocations(mockReq, mockRes, mockNext);

      expect(guestUserService.getLocations).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getLocations', async () => {
      const mockError = new Error('Service error');
      guestUserService.getLocations.mockRejectedValue(mockError);

      await guestUserController.getLocations(mockReq, mockRes, mockNext);

      expect(guestUserService.getLocations).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getLastCraneRequestId', () => {
    it('should get last crane request id successfully', async () => {
      const mockLastDetail = { id: 1 };

      guestUserService.lastCraneRequest.mockResolvedValue(mockLastDetail);

      await guestUserController.getLastCraneRequestId(mockReq, mockRes, mockNext);

      expect(guestUserService.lastCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Last Id Viewed Successfully.',
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no last crane request id', async () => {
      guestUserService.lastCraneRequest.mockResolvedValue(null);

      await guestUserController.getLastCraneRequestId(mockReq, mockRes, mockNext);

      expect(guestUserService.lastCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getLastCraneRequestId', async () => {
      const mockError = new Error('Service error');
      guestUserService.lastCraneRequest.mockRejectedValue(mockError);

      await guestUserController.getLastCraneRequestId(mockReq, mockRes, mockNext);

      expect(guestUserService.lastCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getTimeZoneList', () => {
    it('should get timezone list successfully', async () => {
      const mockTimeZoneList = [{ id: 1, name: 'UTC' }];

      TimeZone.getAll.mockResolvedValue(mockTimeZoneList);

      await guestUserController.getTimeZoneList(mockReq, mockRes, mockNext);

      expect(TimeZone.getAll).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockTimeZoneList });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getTimeZoneList', async () => {
      const mockError = new Error('Service error');
      TimeZone.getAll.mockRejectedValue(mockError);

      await guestUserController.getTimeZoneList(mockReq, mockRes, mockNext);

      expect(TimeZone.getAll).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getSingleProjectDetail', () => {
    it('should get single project detail successfully', async () => {
      const mockProjectList = { id: 1, projectName: 'Project 1' };
      mockReq.params.ProjectId = '1';

      Project.findOne.mockResolvedValue(mockProjectList);

      await guestUserController.getSingleProjectDetail(mockReq, mockRes, mockNext);

      expect(Project.findOne).toHaveBeenCalledWith({
        include: [{ association: 'stripePlan', attribute: ['stripePlanName'] }],
        where: { id: '1' },
        attribute: ['PlanId', 'projectName'],
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Project List.',
        data: mockProjectList,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getSingleProjectDetail', async () => {
      const mockError = new Error('Service error');
      mockReq.params.ProjectId = '1';
      Project.findOne.mockRejectedValue(mockError);

      await guestUserController.getSingleProjectDetail(mockReq, mockRes, mockNext);

      expect(Project.findOne).toHaveBeenCalledWith({
        include: [{ association: 'stripePlan', attribute: ['stripePlanName'] }],
        where: { id: '1' },
        attribute: ['PlanId', 'projectName'],
      });
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getMemberDataMixPanel', () => {
    it('should get member data mix panel successfully', async () => {
      const mockResponse = { mixPanelData: 'test data' };

      guestUserService.getMemberDataMixPanel.mockResolvedValue(mockResponse);

      await guestUserController.getMemberDataMixPanel(mockReq, mockRes, mockNext);

      expect(guestUserService.getMemberDataMixPanel).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Member Data Sent Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getMemberDataMixPanel', async () => {
      guestUserService.getMemberDataMixPanel.mockResolvedValue(null);

      await guestUserController.getMemberDataMixPanel(mockReq, mockRes, mockNext);

      expect(guestUserService.getMemberDataMixPanel).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getMemberDataMixPanel', async () => {
      const mockError = new Error('Service error');
      guestUserService.getMemberDataMixPanel.mockRejectedValue(mockError);

      await guestUserController.getMemberDataMixPanel(mockReq, mockRes, mockNext);

      expect(guestUserService.getMemberDataMixPanel).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getMemberData', () => {
    it('should get member data successfully', async () => {
      const mockResponse = { memberData: 'test data' };

      guestUserService.getMemberData.mockResolvedValue(mockResponse);

      await guestUserController.getMemberData(mockReq, mockRes, mockNext);

      expect(guestUserService.getMemberData).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Member listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getMemberData', async () => {
      guestUserService.getMemberData.mockResolvedValue(null);

      await guestUserController.getMemberData(mockReq, mockRes, mockNext);

      expect(guestUserService.getMemberData).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getMemberData', async () => {
      const mockError = new Error('Service error');
      guestUserService.getMemberData.mockRejectedValue(mockError);

      await guestUserController.getMemberData(mockReq, mockRes, mockNext);

      expect(guestUserService.getMemberData).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('searchMember', () => {
    it('should search member successfully', async () => {
      const mockResponse = { searchResults: [{ id: 1, name: 'Member 1' }] };

      guestUserService.searchMember.mockResolvedValue(mockResponse);

      await guestUserController.searchMember(mockReq, mockRes, mockNext);

      expect(guestUserService.searchMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from searchMember', async () => {
      guestUserService.searchMember.mockResolvedValue(null);

      await guestUserController.searchMember(mockReq, mockRes, mockNext);

      expect(guestUserService.searchMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from searchMember', async () => {
      const mockError = new Error('Service error');
      guestUserService.searchMember.mockRejectedValue(mockError);

      await guestUserController.searchMember(mockReq, mockRes, mockNext);

      expect(guestUserService.searchMember).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('newRequest', () => {
    it('should create new request successfully', async () => {
      const mockResponse = { id: 1, requestData: 'test' };

      guestUserService.newRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.newRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({ response: mockResponse });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from newRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.newRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.newRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in newRequest', async () => {
      const mockError = new Error('Exception error');
      guestUserService.newRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.newRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('craneListEquipment', () => {
    it('should list crane equipment successfully', async () => {
      const mockEquipmentDetail = [{ id: 1, name: 'Crane Equipment 1' }];

      guestUserService.craneListEquipment.mockResolvedValue(mockEquipmentDetail);

      await guestUserController.craneListEquipment(mockReq, mockRes, mockNext);

      expect(guestUserService.craneListEquipment).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Equipment Listed successfully.',
        data: mockEquipmentDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from craneListEquipment', async () => {
      const mockError = new Error('Service error');
      guestUserService.craneListEquipment.mockRejectedValue(mockError);

      await guestUserController.craneListEquipment(mockReq, mockRes, mockNext);

      expect(guestUserService.craneListEquipment).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createCraneRequest', () => {
    it('should create crane request successfully', async () => {
      const mockResponse = { id: 1, craneRequestData: 'test' };

      guestUserService.newCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Guest Crane Booking Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createCraneRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.newCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createCraneRequest', async () => {
      const mockError = new Error('Exception error');
      guestUserService.newCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getConcreteDropdownDetail', () => {
    it('should get concrete dropdown detail successfully with status 200', async () => {
      const mockResponse = {
        status: 200,
        data: [{ id: 1, name: 'Concrete Type 1' }],
      };

      guestUserService.getConcreteDropdownDetail.mockResolvedValue(mockResponse);

      await guestUserController.getConcreteDropdownDetail(mockReq, mockRes, mockNext);

      expect(guestUserService.getConcreteDropdownDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Dropdown Details Viewed Successfully.',
        data: mockResponse.data,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle concrete dropdown detail with non-200 status', async () => {
      const mockResponse = {
        status: 422,
        msg: 'Validation error',
        data: [],
      };

      guestUserService.getConcreteDropdownDetail.mockResolvedValue(mockResponse);

      await guestUserController.getConcreteDropdownDetail(mockReq, mockRes, mockNext);

      expect(guestUserService.getConcreteDropdownDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: mockResponse.msg,
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getConcreteDropdownDetail', async () => {
      const mockError = new Error('Service error');
      guestUserService.getConcreteDropdownDetail.mockRejectedValue(mockError);

      await guestUserController.getConcreteDropdownDetail(mockReq, mockRes, mockNext);

      expect(guestUserService.getConcreteDropdownDetail).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createConcreteRequest', () => {
    it('should create concrete request successfully', async () => {
      const mockResponse = { id: 1, concreteRequestData: 'test' };

      guestUserService.newConcreteRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createConcreteRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.newConcreteRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createConcreteRequest', async () => {
      const mockError = new Error('Exception error');
      guestUserService.newConcreteRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.newConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getNDRData', () => {
    it('should get NDR data successfully', async () => {
      const mockResponse = [{ id: 1, ndrData: 'test' }];

      guestUserService.getNDRData.mockResolvedValue(mockResponse);

      await guestUserController.getNDRData(mockReq, mockRes, mockNext);

      expect(guestUserService.getNDRData).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getNDRData', async () => {
      guestUserService.getNDRData.mockResolvedValue(null);

      await guestUserController.getNDRData(mockReq, mockRes, mockNext);

      expect(guestUserService.getNDRData).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getNDRData', async () => {
      const mockError = new Error('Service error');
      guestUserService.getNDRData.mockRejectedValue(mockError);

      await guestUserController.getNDRData(mockReq, mockRes, mockNext);

      expect(guestUserService.getNDRData).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getSingleCraneRequest', () => {
    it('should get single crane request successfully', async () => {
      const mockResponse = { id: 1, craneRequest: 'test' };

      guestUserService.getSingleCraneRequest.mockResolvedValue(mockResponse);

      await guestUserController.getSingleCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getSingleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getSingleCraneRequest', async () => {
      guestUserService.getSingleCraneRequest.mockResolvedValue(null);

      await guestUserController.getSingleCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getSingleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getSingleCraneRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.getSingleCraneRequest.mockRejectedValue(mockError);

      await guestUserController.getSingleCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getSingleCraneRequest).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getSingleConcreteRequest', () => {
    it('should get single concrete request successfully', async () => {
      const mockResponse = { id: 1, concreteRequest: 'test' };

      guestUserService.getSingleConcreteRequest.mockResolvedValue(mockResponse);

      await guestUserController.getSingleConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getSingleConcreteRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from getSingleConcreteRequest', async () => {
      guestUserService.getSingleConcreteRequest.mockResolvedValue(null);

      await guestUserController.getSingleConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getSingleConcreteRequest).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from getSingleConcreteRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.getSingleConcreteRequest.mockRejectedValue(mockError);

      await guestUserController.getSingleConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.getSingleConcreteRequest).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('editRequest', () => {
    it('should edit request successfully', async () => {
      const mockResponse = { id: 1, updatedData: 'test' };

      guestUserService.editRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.editRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from editRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.editRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.editRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in editRequest', async () => {
      const mockError = new Error('Exception error');
      guestUserService.editRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.editRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('editCraneRequest', () => {
    it('should edit crane request successfully', async () => {
      const mockResponse = { id: 1, updatedCraneData: 'test' };

      guestUserService.editCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.editCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from editCraneRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.editCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.editCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in editCraneRequest', async () => {
      const mockError = new Error('Exception error');
      guestUserService.editCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.editCraneRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editCraneRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('editConcreteRequest', () => {
    it('should edit concrete request successfully', async () => {
      const mockResponse = { id: 1, updatedConcreteData: 'test' };

      guestUserService.editConcreteRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.editConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from editConcreteRequest', async () => {
      const mockError = new Error('Service error');
      guestUserService.editConcreteRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.editConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in editConcreteRequest', async () => {
      const mockError = new Error('Exception error');
      guestUserService.editConcreteRequest.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.editConcreteRequest(mockReq, mockRes, mockNext);

      expect(guestUserService.editConcreteRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createAttachement', () => {
    it('should create attachment successfully', async () => {
      const mockResponse = { id: 1, attachmentData: 'test' };

      guestUserService.createAttachement.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createAttachement(mockReq, mockRes, mockNext);

      expect(guestUserService.createAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createAttachement', async () => {
      const mockError = new Error('Service error');
      guestUserService.createAttachement.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createAttachement(mockReq, mockRes, mockNext);

      expect(guestUserService.createAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createAttachement', async () => {
      const mockError = new Error('Exception error');
      guestUserService.createAttachement.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createAttachement(mockReq, mockRes, mockNext);

      expect(guestUserService.createAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createComment', () => {
    it('should create comment successfully', async () => {
      const mockResponse = { id: 1, commentData: 'test' };

      guestUserService.createComment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createComment', async () => {
      const mockError = new Error('Service error');
      guestUserService.createComment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createComment', async () => {
      const mockError = new Error('Exception error');
      guestUserService.createComment.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createCraneRequestAttachement', () => {
    it('should create crane request attachment successfully', async () => {
      const mockResponse = { id: 1, craneAttachmentData: 'test' };

      guestUserService.createCraneRequestAttachement.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(guestUserService.createCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createCraneRequestAttachement', async () => {
      const mockError = new Error('Service error');
      guestUserService.createCraneRequestAttachement.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(guestUserService.createCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createCraneRequestAttachement', async () => {
      const mockError = new Error('Exception error');
      guestUserService.createCraneRequestAttachement.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createCraneRequestAttachement(mockReq, mockRes, mockNext);

      expect(guestUserService.createCraneRequestAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createCraneRequestComment', () => {
    it('should create crane request comment successfully', async () => {
      const mockResponse = { id: 1, craneCommentData: 'test' };

      guestUserService.createCraneRequestComment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createCraneRequestComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createCraneRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createCraneRequestComment', async () => {
      const mockError = new Error('Service error');
      guestUserService.createCraneRequestComment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createCraneRequestComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createCraneRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createCraneRequestComment', async () => {
      const mockError = new Error('Exception error');
      guestUserService.createCraneRequestComment.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createCraneRequestComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createCraneRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createConcreteRequestAttachment', () => {
    it('should create concrete request attachment successfully', async () => {
      const mockResponse = { id: 1, concreteAttachmentData: 'test' };

      guestUserService.createConcreteRequestAttachment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(guestUserService.createConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createConcreteRequestAttachment', async () => {
      const mockError = new Error('Service error');
      guestUserService.createConcreteRequestAttachment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(guestUserService.createConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createConcreteRequestAttachment', async () => {
      const mockError = new Error('Exception error');
      guestUserService.createConcreteRequestAttachment.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(guestUserService.createConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('createConcreteRequestComment', () => {
    it('should create concrete request comment successfully', async () => {
      const mockResponse = { id: 1, concreteCommentData: 'test' };

      guestUserService.createConcreteRequestComment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await guestUserController.createConcreteRequestComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createConcreteRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from createConcreteRequestComment', async () => {
      const mockError = new Error('Service error');
      guestUserService.createConcreteRequestComment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await guestUserController.createConcreteRequestComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createConcreteRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in createConcreteRequestComment', async () => {
      const mockError = new Error('Exception error');
      guestUserService.createConcreteRequestComment.mockImplementation(() => {
        throw mockError;
      });

      await guestUserController.createConcreteRequestComment(mockReq, mockRes, mockNext);

      expect(guestUserService.createConcreteRequestComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('isRequestToMember', () => {
    it('should check if request is to member successfully', async () => {
      const mockResponse = { isMember: true, data: 'test' };

      guestUserService.isRequestToMember.mockResolvedValue(mockResponse);

      await guestUserController.isRequestToMember(mockReq, mockRes, mockNext);

      expect(guestUserService.isRequestToMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from isRequestToMember', async () => {
      guestUserService.isRequestToMember.mockResolvedValue(null);

      await guestUserController.isRequestToMember(mockReq, mockRes, mockNext);

      expect(guestUserService.isRequestToMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from isRequestToMember', async () => {
      const mockError = new Error('Service error');
      guestUserService.isRequestToMember.mockRejectedValue(mockError);

      await guestUserController.isRequestToMember(mockReq, mockRes, mockNext);

      expect(guestUserService.isRequestToMember).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('updateGuestMember', () => {
    it('should update guest member successfully', async () => {
      const mockResponse = { id: 1, updatedMemberData: 'test' };

      guestUserService.updateGuestMember.mockResolvedValue(mockResponse);

      await guestUserController.updateGuestMember(mockReq, mockRes, mockNext);

      expect(guestUserService.updateGuestMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no response from updateGuestMember', async () => {
      guestUserService.updateGuestMember.mockResolvedValue(null);

      await guestUserController.updateGuestMember(mockReq, mockRes, mockNext);

      expect(guestUserService.updateGuestMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from updateGuestMember', async () => {
      const mockError = new Error('Service error');
      guestUserService.updateGuestMember.mockRejectedValue(mockError);

      await guestUserController.updateGuestMember(mockReq, mockRes, mockNext);

      expect(guestUserService.updateGuestMember).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Internal server error', error: mockError });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
