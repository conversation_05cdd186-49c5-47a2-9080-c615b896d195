const dashboardService = require('../dashboardService');
const helper = require('../../helpers/domainHelper');
const moment = require('moment');

// Mock all dependencies
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((date) => actualMoment(date));

  // Copy all moment methods
  Object.keys(actualMoment).forEach(key => {
    mockMoment[key] = actualMoment[key];
  });

  // Add chainable methods
  mockMoment.prototype = actualMoment.prototype;
  mockMoment.startOf = jest.fn(() => mockMoment);
  mockMoment.endOf = jest.fn(() => mockMoment);
  mockMoment.subtract = jest.fn(() => mockMoment);
  mockMoment.format = jest.fn(() => '01-2024');
  mockMoment.toDate = jest.fn(() => new Date('2024-01-01'));

  return mockMoment;
});

jest.mock('lodash', () => ({
  sortBy: jest.fn((arr) => arr),
  chain: jest.fn(() => ({
    groupBy: jest.fn(() => ({
      map: jest.fn(() => ({
        value: jest.fn(() => [
          { total: 5, month: '01', year: '2024', shortmonth: 'Jan' }
        ]),
      })),
    })),
  })),
}));

jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      between: 'between',
      ne: 'ne',
      notIn: 'notIn',
      and: 'and',
      gt: 'gt',
      in: 'in',
    },
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  Project: {
    getUserProjects: jest.fn(),
    getProjectCount: jest.fn(),
    findAll: jest.fn(),
    findByPk: jest.fn(),
  },
  Company: {
    count: jest.fn(),
  },
  DeliveryRequest: {
    count: jest.fn(),
    findAndCountAll: jest.fn(),
    findAll: jest.fn(),
  },
  Member: {
    count: jest.fn(),
    findOne: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
    update: jest.fn(),
  },
  Equipments: {
    count: jest.fn(),
  },
  CraneRequest: {
    count: jest.fn(),
    findAll: jest.fn(),
  },
  ConcreteRequest: {
    count: jest.fn(),
    findAndCountAll: jest.fn(),
  },
  VoidList: {
    findAll: jest.fn(),
  },
  InspectionRequest: {
    count: jest.fn(),
    findAndCountAll: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

describe('DashboardService', () => {
  let mockInputData;
  let mockDone;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get mocked models
    mockModels = require('../../models');

    mockInputData = {
      user: {
        id: 1,
        email: '<EMAIL>',
        domainName: 'testdomain',
      },
      body: {
        RoleId: 2,
        ProjectId: 1,
        ParentCompanyId: 1,
      },
      params: {
        ProjectId: 1,
        pageNo: '1',
        pageSize: '10',
      },
    };

    mockDone = jest.fn();

    // Setup default mock implementations
    helper.getDynamicModel.mockResolvedValue({
      Project: mockModels.Project,
      Company: mockModels.Company,
      DeliveryRequest: mockModels.DeliveryRequest,
      Member: mockModels.Member,
      User: mockModels.User,
      Equipments: mockModels.Equipments,
    });

    helper.returnProjectModel.mockResolvedValue({
      Project: mockModels.Project,
      User: mockModels.User,
      Member: mockModels.Member,
    });

    mockModels.Enterprise.findOne.mockResolvedValue({
      name: 'testdomain',
      id: 1,
    });

    mockModels.Project.getUserProjects.mockResolvedValue([
      { id: 1, projectName: 'Project 1' },
      { id: 2, projectName: 'Project 2' },
    ]);

    mockModels.Project.getProjectCount.mockResolvedValue([]);
    mockModels.Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Project 1' });

    mockModels.Company.count.mockResolvedValue(5);
    mockModels.Member.count.mockResolvedValue(10);
    mockModels.Member.findOne.mockResolvedValue({ id: 1, UserId: 1, EnterpriseId: 1 });
    mockModels.Equipments.count.mockResolvedValue(8);
    mockModels.DeliveryRequest.count.mockResolvedValue(15);
    mockModels.DeliveryRequest.findAndCountAll.mockResolvedValue({
      rows: [{ id: 1, DeliveryId: 'D001', description: 'Test delivery', deliveryStart: new Date() }],
      count: 1
    });
    mockModels.ConcreteRequest.findAndCountAll.mockResolvedValue({
      rows: [{ id: 1, ConcreteRequestId: 'C001', description: 'Test concrete', concretePlacementStart: new Date() }],
      count: 1
    });
    mockModels.InspectionRequest.findAndCountAll.mockResolvedValue({
      rows: [{ id: 1, InspectionId: 'I001', description: 'Test inspection', inspectionStart: new Date() }],
      count: 1
    });
    mockModels.CraneRequest.findAll.mockResolvedValue([
      { id: 1, CraneRequestId: 'CR001', description: 'Test crane', craneDeliveryStart: new Date(), requestType: 'craneRequest' }
    ]);
    mockModels.VoidList.findAll.mockResolvedValue([]);
    mockModels.User.update.mockResolvedValue([1]);
  });

  describe('returnProjectModel', () => {
    it('should return project model successfully', async () => {
      await dashboardService.returnProjectModel();

      expect(helper.returnProjectModel).toHaveBeenCalled();
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully', async () => {
      await dashboardService.getDynamicModel(mockInputData);

      expect(helper.getDynamicModel).toHaveBeenCalled();
      expect(mockModels.Enterprise.findOne).toHaveBeenCalled();
    });

    it('should handle missing domain name', async () => {
      const inputDataWithoutDomain = {
        ...mockInputData,
        user: {
          ...mockInputData.user,
          domainName: null,
        },
      };

      mockModels.Enterprise.findOne.mockResolvedValue(null);

      await dashboardService.getDynamicModel(inputDataWithoutDomain);

      expect(helper.getDynamicModel).toHaveBeenCalled();
    });

    it('should update user when enterprise value exists', async () => {
      const newUser = { id: 2, email: '<EMAIL>' };
      mockModels.User.findOne.mockResolvedValue(newUser);

      await dashboardService.getDynamicModel(mockInputData);

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: mockInputData.user.email },
      });
    });
  });

  describe('getDashboardData', () => {
    it('should get dashboard data successfully for non-admin role', async () => {
      const mockProjects = [
        { id: 1, projectName: 'Project 1' },
        { id: 2, projectName: 'Project 2' },
      ];

      mockModels.Project.getUserProjects.mockResolvedValue(mockProjects);

      await dashboardService.getDashboardData(mockInputData, mockDone);

      expect(helper.getDynamicModel).toHaveBeenCalledWith(mockInputData);
      expect(mockModels.Project.getUserProjects).toHaveBeenCalledWith(mockInputData.user);
      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        projects: 2,
        deliveryRequest: expect.any(Number),
        member: expect.any(Number),
        company: expect.any(Number),
        equipment: expect.any(Number),
      }), false);
    });

    it('should get dashboard data successfully for admin role (RoleId 4)', async () => {
      const adminInputData = {
        ...mockInputData,
        body: {
          ...mockInputData.body,
          RoleId: 4,
        },
      };

      const mockProjects = [{ id: 1, projectName: 'Project 1' }];
      mockModels.Project.getUserProjects.mockResolvedValue(mockProjects);

      await dashboardService.getDashboardData(adminInputData, mockDone);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(mockModels.Project.getUserProjects).toHaveBeenCalledWith(adminInputData.user);
      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        projects: 1,
      }), false);
    });

    it('should handle empty projects list', async () => {
      mockModels.Project.getUserProjects.mockResolvedValue([]);

      await dashboardService.getDashboardData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        projects: 0,
      }), false);
    });

    it('should handle errors during dashboard data retrieval', async () => {
      const error = new Error('Database error');
      mockModels.Project.getUserProjects.mockRejectedValue(error);

      await dashboardService.getDashboardData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should calculate date ranges correctly', async () => {
      const mockProjects = [{ id: 1, projectName: 'Project 1' }];
      mockModels.Project.getUserProjects.mockResolvedValue(mockProjects);

      await dashboardService.getDashboardData(mockInputData, mockDone);

      expect(mockModels.Project.getProjectCount).toHaveBeenCalledTimes(2);
    });
  });

  describe('resolveModel', () => {
    it('should resolve to dynamic model for non-admin role', async () => {
      const result = await dashboardService.resolveModel(mockInputData, 2);

      expect(helper.getDynamicModel).toHaveBeenCalledWith(mockInputData);
      expect(result).toBeDefined();
    });

    it('should resolve to public model for admin role', async () => {
      const result = await dashboardService.resolveModel(mockInputData, 4);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('initializeDashData', () => {
    it('should initialize dashboard data with correct structure', () => {
      const result = dashboardService.initializeDashData(5);

      expect(result).toEqual({
        projects: 5,
        deliveryRequest: 0,
        member: 0,
        company: 0,
        equipment: 0,
        diffEquipment: 0,
        diffCompany: 0,
        diffMember: 0,
        diffDeliveryRequest: 0,
        diffProjects: 0,
      });
    });
  });

  describe('calculateDates', () => {
    it('should calculate current and previous month dates', () => {
      const result = dashboardService.calculateDates();

      expect(result).toHaveProperty('currentFirstDay');
      expect(result).toHaveProperty('currentLastDay');
      expect(result).toHaveProperty('previousFirstDay');
      expect(result).toHaveProperty('previousLastDay');
    });
  });

  describe('getProjectId', () => {
    it('should get project ID from input data body', () => {
      const result = dashboardService.getProjectId(mockInputData, mockInputData.body);

      expect(result).toBe(mockInputData.body.ProjectId);
    });

    it('should get project ID from params when body is empty', () => {
      const inputDataWithoutBodyProjectId = {
        ...mockInputData,
        body: {},
        params: { ProjectId: 5 }
      };

      const result = dashboardService.getProjectId(inputDataWithoutBodyProjectId, {});

      expect(result).toBe(5);
    });

    it('should handle missing project ID in both params and body', () => {
      const inputDataWithoutProjectId = {
        ...mockInputData,
        body: {},
        params: {}
      };

      const result = dashboardService.getProjectId(inputDataWithoutProjectId, {});

      expect(result).toBeUndefined();
    });

    it('should handle undefined ProjectId in params', () => {
      const inputDataWithUndefinedProjectId = {
        ...mockInputData,
        body: {},
        params: { ProjectId: 'undefined' }
      };

      const result = dashboardService.getProjectId(inputDataWithUndefinedProjectId, {});

      expect(result).toBeUndefined();
    });
  });

  describe('getCurrentAndPreviousProjectsCount', () => {
    it('should get current and previous projects count', async () => {
      const mockModel = {
        getProjectCount: jest.fn().mockResolvedValue([]),
      };

      const currentFirstDay = new Date('2024-01-01');
      const currentLastDay = new Date('2024-01-31');
      const previousFirstDay = new Date('2023-12-01');
      const previousLastDay = new Date('2023-12-31');

      const result = await dashboardService.getCurrentAndPreviousProjectsCount(
        mockInputData,
        mockModel,
        currentFirstDay,
        currentLastDay,
        previousFirstDay,
        previousLastDay
      );

      expect(mockModel.getProjectCount).toHaveBeenCalledTimes(2);
      expect(result).toHaveLength(2);
    });
  });

  describe('getRelevantProjects', () => {
    it('should get relevant projects when ProjectId is provided', async () => {
      const totalProjects = [
        { id: 1, projectName: 'Project 1' },
        { id: 2, projectName: 'Project 2' },
      ];

      const result = await dashboardService.getRelevantProjects(
        1,
        mockInputData.body,
        totalProjects,
        mockModels.Project
      );

      expect(result).toEqual([{ id: 1, projectName: 'Project 1' }]);
    });

    it('should return all projects when ProjectId is not provided', async () => {
      const totalProjects = [
        { id: 1, projectName: 'Project 1' },
        { id: 2, projectName: 'Project 2' },
      ];

      const result = await dashboardService.getRelevantProjects(
        null,
        mockInputData.body,
        totalProjects,
        mockModels.Project
      );

      expect(result).toEqual(totalProjects);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const dbError = new Error('Database connection failed');
      helper.getDynamicModel.mockRejectedValue(dbError);

      await dashboardService.getDashboardData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, dbError);
    });

    it('should handle invalid input data', async () => {
      const invalidInputData = { ...mockInputData, body: null };

      await dashboardService.getDashboardData(invalidInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('resolveDomainName', () => {
    it('should return domain name when enterprise exists', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue({ name: 'testdomain', id: 1 });

      const result = await dashboardService.resolveDomainName('testdomain', null, '<EMAIL>');

      expect(result).toBe('testdomain');
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' }
      });
    });

    it('should return empty string when enterprise does not exist', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await dashboardService.resolveDomainName('nonexistent', null, '<EMAIL>');

      expect(result).toBe('');
    });

    it('should resolve domain from ParentCompanyId when domain is empty', async () => {
      const mockUser = { id: 1 };
      const mockMember = { UserId: 1, EnterpriseId: 2 };
      const mockEnterprise = { name: 'CompanyDomain', id: 2 };

      helper.returnProjectModel.mockResolvedValue({
        User: { findOne: jest.fn().mockResolvedValue(mockUser) },
        Member: { findOne: jest.fn().mockResolvedValue(mockMember) }
      });
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await dashboardService.resolveDomainName('', 1, '<EMAIL>');

      expect(result).toBe('companydomain');
    });

    it('should handle missing user data', async () => {
      helper.returnProjectModel.mockResolvedValue({
        User: { findOne: jest.fn().mockResolvedValue(null) },
        Member: { findOne: jest.fn().mockResolvedValue(null) }
      });

      const result = await dashboardService.resolveDomainName('', 1, '<EMAIL>');

      expect(result).toBe('');
    });
  });

  describe('resolveUser', () => {
    it('should update user when domain name exists', async () => {
      const newUser = { id: 2, email: '<EMAIL>' };
      mockModels.User.findOne.mockResolvedValue(newUser);

      await dashboardService.resolveUser(mockInputData, '<EMAIL>', 'testdomain');

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' }
      });
      expect(mockInputData.user).toBe(newUser);
    });

    it('should not update user when domain name is empty', async () => {
      const originalUser = mockInputData.user;

      await dashboardService.resolveUser(mockInputData, '<EMAIL>', '');

      expect(mockModels.User.findOne).not.toHaveBeenCalled();
      expect(mockInputData.user).toBe(originalUser);
    });
  });

  describe('calculateTotalAndDiff', () => {
    it('should calculate total and difference for projects', async () => {
      const totalProject = [{ id: 1 }, { id: 2 }];
      const additionalCondition = { isDeleted: false };
      const entityModel = mockModels.Company;
      const dateRange = {
        currentFirstDay: new Date('2024-01-01'),
        currentLastDay: new Date('2024-01-31'),
        previousFirstDay: new Date('2023-12-01'),
        previousLastDay: new Date('2023-12-31')
      };

      mockModels.Company.count
        .mockResolvedValueOnce(5) // total for project 1
        .mockResolvedValueOnce(3) // current month for project 1
        .mockResolvedValueOnce(2) // previous month for project 1
        .mockResolvedValueOnce(7) // total for project 2
        .mockResolvedValueOnce(4) // current month for project 2
        .mockResolvedValueOnce(1); // previous month for project 2

      const result = await dashboardService.calculateTotalAndDiff(
        totalProject,
        additionalCondition,
        entityModel,
        dateRange
      );

      expect(result.total).toBe(12); // 5 + 7
      expect(result.diff).toBe(4); // (3-2) + (4-1)
    });
  });

  describe('processEntityForProject', () => {
    it('should count entities for a project', async () => {
      mockModels.Company.count.mockResolvedValue(5);

      const result = await dashboardService.processEntityForProject(
        mockModels.Company,
        { isDeleted: false },
        1
      );

      expect(result).toBe(5);
      expect(mockModels.Company.count).toHaveBeenCalledWith({
        where: { isDeleted: false, ProjectId: 1, isDeleted: false }
      });
    });
  });

  describe('processEntityInRange', () => {
    it('should count entities in date range for a project', async () => {
      mockModels.Company.count.mockResolvedValue(3);

      const result = await dashboardService.processEntityInRange(
        mockModels.Company,
        [new Date('2024-01-01'), new Date('2024-01-31')],
        1,
        { status: 'active' }
      );

      expect(result).toBe(3);
      expect(mockModels.Company.count).toHaveBeenCalledWith({
        where: {
          createdAt: { between: [new Date('2024-01-01'), new Date('2024-01-31')] },
          ProjectId: 1,
          status: 'active',
          isDeleted: false
        }
      });
    });
  });

  describe('getTotalCompany', () => {
    it('should get total company count and difference', async () => {
      const totalProject = [{ id: 1 }];
      const dateRange = {
        currentFirstDay: new Date('2024-01-01'),
        currentLastDay: new Date('2024-01-31'),
        previousFirstDay: new Date('2023-12-01'),
        previousLastDay: new Date('2023-12-31')
      };

      mockModels.Company.count
        .mockResolvedValueOnce(10)
        .mockResolvedValueOnce(5)
        .mockResolvedValueOnce(3);

      const result = await dashboardService.getTotalCompany(totalProject, dateRange);

      expect(result.total).toBe(10);
      expect(result.diff).toBe(2); // 5 - 3
    });
  });

  describe('getTotalMember', () => {
    it('should get total member count excluding role 1 and guest users', async () => {
      const totalProject = [{ id: 1 }];
      const dateRange = {
        currentFirstDay: new Date('2024-01-01'),
        currentLastDay: new Date('2024-01-31'),
        previousFirstDay: new Date('2023-12-01'),
        previousLastDay: new Date('2023-12-31')
      };

      mockModels.Member.count
        .mockResolvedValueOnce(8)
        .mockResolvedValueOnce(4)
        .mockResolvedValueOnce(2);

      const result = await dashboardService.getTotalMember(totalProject, dateRange);

      expect(result.total).toBe(8);
      expect(result.diff).toBe(2); // 4 - 2
    });
  });

  describe('getVoidDeliveryRequests', () => {
    it('should get void delivery request IDs', async () => {
      const totalProject = [{ id: 1 }];
      mockModels.VoidList.findAll.mockResolvedValue([
        { DeliveryRequestId: 10 },
        { DeliveryRequestId: 20 }
      ]);

      const result = await dashboardService.getVoidDeliveryRequests(totalProject);

      expect(result).toEqual([10, 20]);
      expect(mockModels.VoidList.findAll).toHaveBeenCalledWith({
        where: {
          ProjectId: 1,
          isDeliveryRequest: true,
          DeliveryRequestId: { ne: null }
        }
      });
    });
  });

  describe('populateProjectData', () => {
    it('should return array with single project ID when provided', async () => {
      const result = await dashboardService.populateProjectData(5, mockInputData.user);

      expect(result).toEqual([5]);
    });

    it('should return all user project IDs when no project ID provided', async () => {
      mockModels.Project.getUserProjects.mockResolvedValue([
        { id: 1 }, { id: 2 }, { id: 3 }
      ]);

      const result = await dashboardService.populateProjectData(null, mockInputData.user);

      expect(result).toEqual([1, 2, 3]);
      expect(mockModels.Project.getUserProjects).toHaveBeenCalledWith(mockInputData.user);
    });
  });

  describe('getOverAllDelivery', () => {
    it('should get delivery requests with upcoming filter', async () => {
      const newCondition = { upcoming: true, ProjectId: 1 };
      mockModels.DeliveryRequest.findAndCountAll.mockResolvedValue({
        rows: [{ id: 1, DeliveryId: 'D001' }],
        count: 1
      });

      const result = await dashboardService.getOverAllDelivery(
        mockInputData,
        newCondition,
        10,
        0
      );

      expect(result.rows).toHaveLength(1);
      expect(mockModels.DeliveryRequest.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            deliveryStart: { gt: expect.any(Date) }
          })
        })
      );
    });

    it('should handle errors in getOverAllDelivery', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      const result = await dashboardService.getOverAllDelivery(
        mockInputData,
        { ProjectId: 1 },
        10,
        0
      );

      expect(result).toBe(error);
    });
  });

  describe('formatGraphData', () => {
    it('should format graph data correctly', () => {
      const itemList = [
        { id: 1, deliveryStart: new Date('2024-01-15') },
        { id: 2, deliveryStart: new Date('2024-02-20') }
      ];

      const result = dashboardService.formatGraphData(
        itemList,
        (item) => item.deliveryStart
      );

      expect(itemList[0]).toHaveProperty('deliveryDate');
      expect(itemList[0]).toHaveProperty('shortmonth');
      expect(itemList[0]).toHaveProperty('month');
      expect(itemList[0]).toHaveProperty('year');
      expect(result).toEqual([
        { total: 5, month: '01', year: '2024', shortmonth: 'Jan' }
      ]);
    });
  });

  describe('getReleasenoteVersion', () => {
    it('should update user version flag', async () => {
      mockModels.User.update.mockResolvedValue([1]);

      await dashboardService.getReleasenoteVersion(mockInputData, mockDone);

      expect(mockModels.User.update).toHaveBeenCalledWith(
        { versionFlag: true },
        { where: { id: mockInputData.user.id } }
      );
      expect(mockDone).toHaveBeenCalledWith([1], false);
    });

    it('should handle errors in getReleasenoteVersion', async () => {
      const error = new Error('Update failed');
      mockModels.User.update.mockRejectedValue(error);

      await dashboardService.getReleasenoteVersion(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('Concrete Request Functions', () => {
    it('should get overall concrete requests', async () => {
      const newCondition = { ProjectId: 1 };
      mockModels.ConcreteRequest.findAndCountAll.mockResolvedValue({
        rows: [{ id: 1, ConcreteRequestId: 'C001' }],
        count: 1
      });

      const result = await dashboardService.getOverAllConcreteRequest(
        mockInputData,
        newCondition,
        10,
        0
      );

      expect(result.rows).toHaveLength(1);
    });

    it('should get void concrete request IDs', async () => {
      mockModels.VoidList.findAll.mockResolvedValue([
        { ConcreteRequestId: 30 }
      ]);

      const result = await dashboardService.getVoidConcretes(1);

      expect(result).toEqual([30]);
    });

    it('should get concrete graph data', async () => {
      const mockConcreteData = {
        rows: [{ id: 1, concretePlacementStart: new Date('2024-01-15') }]
      };

      // Mock the getOverAllConcreteRequest method
      jest.spyOn(dashboardService, 'getOverAllConcreteRequest')
        .mockResolvedValue(mockConcreteData);

      await dashboardService.getConcreteGraphData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        { count: [{ total: 5, month: '01', year: '2024', shortmonth: 'Jan' }] },
        false
      );
    });
  });

  describe('Crane Request Functions', () => {
    it('should get overall crane requests', async () => {
      const newCondition = { ProjectId: 1 };
      mockModels.CraneRequest.findAll.mockResolvedValue([
        { id: 1, requestType: 'craneRequest', craneDeliveryStart: new Date() }
      ]);
      mockModels.DeliveryRequest.findAll.mockResolvedValue([
        { id: 2, requestType: 'deliveryRequestWithCrane', deliveryStart: new Date() }
      ]);

      const result = await dashboardService.getOverAllCrane(mockInputData, newCondition);

      expect(result).toHaveLength(2);
    });

    it('should get void crane delivery IDs', async () => {
      mockModels.VoidList.findAll.mockResolvedValue([
        { CraneRequestId: 40 }
      ]);

      const result = await dashboardService.getVoidCraneDeliveries(1);

      expect(result).toEqual([40]);
    });

    it('should get crane graph data', async () => {
      const mockCraneData = [
        { id: 1, requestType: 'craneRequest', craneDeliveryStart: new Date('2024-01-15') }
      ];

      jest.spyOn(dashboardService, 'getOverAllCrane')
        .mockResolvedValue(mockCraneData);

      await dashboardService.getCraneGraphData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        { count: [{ total: 5, month: '01', year: '2024', shortmonth: 'Jan' }] },
        false
      );
    });
  });

  describe('Inspection Request Functions', () => {
    it('should get overall inspection requests', async () => {
      const newCondition = { ProjectId: 1 };
      mockModels.InspectionRequest.findAndCountAll.mockResolvedValue({
        rows: [{ id: 1, InspectionId: 'I001' }],
        count: 1
      });

      const result = await dashboardService.getOverAllInspection(
        mockInputData,
        newCondition,
        10,
        0
      );

      expect(result.rows).toHaveLength(1);
    });

    it('should get void inspection request IDs', async () => {
      mockModels.VoidList.findAll.mockResolvedValue([
        { InspectionRequestId: 50 }
      ]);

      const result = await dashboardService.getVoidInspections(1);

      expect(result).toEqual([50]);
    });

    it('should get inspection graph data', async () => {
      const mockInspectionData = {
        rows: [{ id: 1, inspectionStart: new Date('2024-01-15') }]
      };

      jest.spyOn(dashboardService, 'getOverAllInspection')
        .mockResolvedValue(mockInspectionData);

      await dashboardService.getInspectionGraphData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        { count: [{ total: 5, month: '01', year: '2024', shortmonth: 'Jan' }] },
        false
      );
    });
  });

  describe('upcomingDelivery', () => {
    it('should get upcoming deliveries with pagination', async () => {
      const mockDeliveryData = {
        rows: [{ id: 1, DeliveryId: 'D001' }],
        count: 1
      };

      jest.spyOn(dashboardService, 'getOverAllDelivery')
        .mockResolvedValue(mockDeliveryData);

      await dashboardService.upcomingDelivery(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockDeliveryData, false);
    });

    it('should handle errors in upcomingDelivery', async () => {
      const error = new Error('Delivery error');
      jest.spyOn(dashboardService, 'getOverAllDelivery')
        .mockRejectedValue(error);

      await dashboardService.upcomingDelivery(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('Negative Test Cases', () => {
    it('should handle PAadmin condition in getRelevantProjects', async () => {
      const incomeData = { PAadmin: true };
      const totalNewProject = [{ id: 1 }, { id: 2 }];

      mockModels.Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Project 1' });

      const result = await dashboardService.getRelevantProjects(
        null,
        incomeData,
        totalNewProject,
        mockModels.Project
      );

      expect(result).toEqual([{ id: 1, projectName: 'Project 1' }]);
    });

    it('should return empty array when project not found in getRelevantProjects', async () => {
      const incomeData = {};
      const totalNewProject = [{ id: 1 }, { id: 2 }];

      mockModels.Project.findByPk.mockResolvedValue(null);

      const result = await dashboardService.getRelevantProjects(
        999,
        incomeData,
        totalNewProject,
        mockModels.Project
      );

      expect(result).toEqual([]);
    });

    it('should handle errors in concrete graph data', async () => {
      const error = new Error('Concrete error');
      jest.spyOn(dashboardService, 'getOverAllConcreteRequest')
        .mockRejectedValue(error);

      await dashboardService.getConcreteGraphData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle errors in crane graph data', async () => {
      const error = new Error('Crane error');
      jest.spyOn(dashboardService, 'getOverAllCrane')
        .mockRejectedValue(error);

      await dashboardService.getCraneGraphData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle errors in inspection graph data', async () => {
      const error = new Error('Inspection error');
      jest.spyOn(dashboardService, 'getOverAllInspection')
        .mockRejectedValue(error);

      await dashboardService.getInspectionGraphData(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle errors in delivery graph data', async () => {
      const error = new Error('Delivery graph error');
      jest.spyOn(dashboardService, 'getOverAllDelivery')
        .mockRejectedValue(error);

      await dashboardService.getGraphDelivery(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });

    it('should handle errors in crane requests', async () => {
      const error = new Error('Crane request error');
      helper.getDynamicModel.mockRejectedValue(error);

      const result = await dashboardService.getOverAllCrane(mockInputData, { ProjectId: 1 });

      expect(result).toBe(error);
    });

    it('should handle errors in concrete requests', async () => {
      const error = new Error('Concrete request error');
      helper.getDynamicModel.mockRejectedValue(error);

      const result = await dashboardService.getOverAllConcreteRequest(
        mockInputData,
        { ProjectId: 1 },
        10,
        0
      );

      expect(result).toBe(error);
    });

    it('should handle errors in inspection requests', async () => {
      const error = new Error('Inspection request error');
      helper.getDynamicModel.mockRejectedValue(error);

      const result = await dashboardService.getOverAllInspection(
        mockInputData,
        { ProjectId: 1 },
        10,
        0
      );

      expect(result).toBe(error);
    });
  });

  describe('Edge Cases', () => {
    it('should handle null user in input data', async () => {
      const nullUserInputData = {
        ...mockInputData,
        user: null
      };

      await dashboardService.getDashboardData(nullUserInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
    });

    it('should handle empty date ranges', () => {
      const result = dashboardService.calculateDates();

      expect(result.currentFirstDay).toBeInstanceOf(Date);
      expect(result.currentLastDay).toBeInstanceOf(Date);
      expect(result.previousFirstDay).toBeInstanceOf(Date);
      expect(result.previousLastDay).toBeInstanceOf(Date);
    });

    it('should handle upcoming condition in delivery requests', async () => {
      const newCondition = { upcoming: true, ProjectId: 1 };

      await dashboardService.getOverAllDelivery(
        mockInputData,
        newCondition,
        10,
        0
      );

      expect(mockModels.DeliveryRequest.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            deliveryStart: { gt: expect.any(Date) }
          })
        })
      );
    });

    it('should get delivery graph data successfully', async () => {
      const mockDeliveryData = {
        rows: [{ id: 1, deliveryStart: new Date('2024-01-15') }]
      };

      jest.spyOn(dashboardService, 'getOverAllDelivery')
        .mockResolvedValue(mockDeliveryData);

      await dashboardService.getGraphDelivery(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(
        { count: [{ total: 5, month: '01', year: '2024', shortmonth: 'Jan' }] },
        false
      );
    });

    it('should handle upcoming condition in concrete requests', async () => {
      const newCondition = { upcoming: true, ProjectId: 1 };

      await dashboardService.getOverAllConcreteRequest(
        mockInputData,
        newCondition,
        10,
        0
      );

      expect(mockModels.ConcreteRequest.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            concretePlacementStart: { gt: expect.any(Date) }
          })
        })
      );
    });

    it('should handle upcoming condition in inspection requests', async () => {
      const newCondition = { upcoming: true, ProjectId: 1 };

      await dashboardService.getOverAllInspection(
        mockInputData,
        newCondition,
        10,
        0
      );

      expect(mockModels.InspectionRequest.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            inspectionStart: { gt: expect.any(Date) }
          })
        })
      );
    });
  });
});
