const Joi = require('joi');

const overRideValidation = {
  applyOverRide: {
    body: Joi.object({
      MemberId: Joi.number(),
      ProjectId: Joi.number().required(),
      comment: Joi.string().required(),
    }),
  },
  adminAction: {
    body: Joi.object({
      id: Joi.number().required(),
      status: Joi.string().required(),
      reason: Joi.string(),
    }),
  },
  listOverRide: {
    params: Joi.object({
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
    }),
  },
};
module.exports = overRideValidation;
