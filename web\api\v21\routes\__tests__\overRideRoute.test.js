const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  overRideValidation: {
    applyOverRide: jest.fn(),
    listOverRide: jest.fn(),
    adminAction: jest.fn(),
  },
}));

jest.mock('../../controllers', () => ({
  OverRideController: {
    applyOverRide: jest.fn(),
    listOverRide: jest.fn(),
    adminAction: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isAdmin: jest.fn(),
}));

describe('overRideRoute', () => {
  let router;
  let overRideRoute;
  let OverRideController;
  let passportConfig;
  let checkAdmin;
  let overRideValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn(),
      get: jest.fn(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    overRideRoute = require('../overRideRoute');
    const controllers = require('../../controllers');
    OverRideController = controllers.OverRideController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    const validations = require('../../middlewares/validations');
    overRideValidation = validations.overRideValidation;
    validate = require('express-validation').validate;
  });

  describe('router', () => {
    it('should create a router instance', () => {
      const result = overRideRoute.router;
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);
    });

    it('should register POST /apply_override route with validation, authentication, and project admin check', () => {
      overRideRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/apply_override',
        'mocked-validate-middleware', // validate middleware
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        OverRideController.applyOverRide,
      );

      expect(validate).toHaveBeenCalledWith(
        overRideValidation.applyOverRide,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should register GET /list_override/:pageSize/:pageNo route with authentication, admin check, and validation', () => {
      overRideRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/list_override/:pageSize/:pageNo',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        'mocked-validate-middleware', // validate middleware
        OverRideController.listOverRide,
      );

      expect(validate).toHaveBeenCalledWith(
        overRideValidation.listOverRide,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should register POST /admin_action route with authentication, admin check, and validation', () => {
      overRideRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/admin_action',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        'mocked-validate-middleware', // validate middleware
        OverRideController.adminAction,
      );

      expect(validate).toHaveBeenCalledWith(
        overRideValidation.adminAction,
        { keyByField: true },
        { abortEarly: false },
      );
    });
  });
});
