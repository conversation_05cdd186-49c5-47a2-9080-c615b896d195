const moment = require('moment');
const Cryptr = require('cryptr');

// Mock all dependencies before requiring the service
jest.mock('../../helpers/domainHelper');
jest.mock('../../helpers/notificationHelper');
jest.mock('cryptr', () => {
    return jest.fn().mockImplementation(() => ({
        encrypt: jest.fn((value) => `encrypted_${value}`),
        decrypt: jest.fn((value) => value.replace('encrypted_', ''))
    }));
});
jest.mock('../../config/fcm', () => ({
    sendDeviceToken: jest.fn(),
    sendMemberLocationPreferencePushNotification: jest.fn(),
    checkMemberNotificationPreference: jest.fn()
}));
jest.mock('../../mailer', () => ({
    sendMail: jest.fn((...args) => {
        const callback = args[args.length - 1];
        if (typeof callback === 'function') callback(null, 'success');
    })
}));

const commentService = require('../commentService');
const helper = require('../../helpers/domainHelper');
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            ne: 'ne',
            notIn: 'notIn',
            and: 'and',
            in: 'in'
        },
        and: jest.fn((...args) => ({ [Symbol.for('and')]: args })),
        or: jest.fn((...args) => ({ [Symbol.for('or')]: args })),
        where: jest.fn((col, op, val) => ({ [Symbol.for('where')]: { col, op, val } })),
        col: jest.fn((name) => ({ [Symbol.for('col')]: name })),
        fn: jest.fn((name, ...args) => ({ [Symbol.for('fn')]: { name, args } })),
        literal: jest.fn((val) => ({ [Symbol.for('literal')]: val }))
    },
    Enterprise: {
        findOne: jest.fn()
    },
    NotificationPreference: {
        findAll: jest.fn().mockResolvedValue([]),
        findOne: jest.fn()
    },
    Role: {},
    DigestNotification: {
        create: jest.fn().mockResolvedValue({ id: 1 })
    },
    Locations: {
        findOne: jest.fn()
    },
    LocationNotificationPreferences: {
        findAll: jest.fn(),
        findOne: jest.fn()
    },
    DeliveryRequest: {
        findOne: jest.fn(),
        findAll: jest.fn()
    },
    InspectionRequest: {
        findOne: jest.fn()
    },
    DeliveryPerson: {
        findAll: jest.fn().mockResolvedValue([])
    },
    DeliverComment: {
        findAndCountAll: jest.fn(),
        findAll: jest.fn().mockResolvedValue([]),
        createInstance: jest.fn().mockResolvedValue({ id: 1, comment: 'Test comment' })
    },
    InspectionComment: {
        findAndCountAll: jest.fn(),
        findAll: jest.fn().mockResolvedValue([]),
        createInstance: jest.fn().mockResolvedValue({ id: 1, comment: 'Test inspection comment' })
    },
    InspectionHistory: {
        createInstance: jest.fn().mockResolvedValue({ id: 1 })
    },
    InspectionPerson: {
        findAll: jest.fn().mockResolvedValue([])
    },
    Member: {
        findOne: jest.fn(),
        findAll: jest.fn().mockResolvedValue([])
    },
    DeliverHistory: {
        createInstance: jest.fn().mockResolvedValue({ id: 1 })
    },
    User: {
        findOne: jest.fn()
    },
    Project: {
        findByPk: jest.fn()
    },
    DeliveryPersonNotification: {},
    Notification: {
        createInstance: jest.fn().mockResolvedValue({ id: 1, MemberId: 1 })
    }
}));

describe('Comment Service', () => {
    let mockModels;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();
        jest.restoreAllMocks();

        // Setup common mock data
        mockModels = {
            DeliveryRequest: require('../../models').DeliveryRequest,
            InspectionRequest: require('../../models').InspectionRequest,
            DeliverComment: require('../../models').DeliverComment,
            InspectionComment: require('../../models').InspectionComment,
            Member: require('../../models').Member,
            User: require('../../models').User,
            Project: require('../../models').Project,
            Notification: require('../../models').Notification,
            Enterprise: require('../../models').Enterprise,
            LocationNotificationPreferences: require('../../models').LocationNotificationPreferences,
            Locations: require('../../models').Locations,
            DeliveryPerson: require('../../models').DeliveryPerson,
            InspectionPerson: require('../../models').InspectionPerson,
            NotificationPreference: require('../../models').NotificationPreference,
            DeliverHistory: require('../../models').DeliverHistory,
            InspectionHistory: require('../../models').InspectionHistory
        };

        // Mock helper functions
        helper.returnProjectModel.mockResolvedValue({
            User: mockModels.User,
            Member: mockModels.Member
        });

        helper.getDynamicModel.mockResolvedValue({
            DeliveryRequest: mockModels.DeliveryRequest,
            DeliveryPerson: mockModels.DeliveryPerson,
            DeliverComment: mockModels.DeliverComment,
            Member: mockModels.Member,
            User: mockModels.User,
            DeliverHistory: mockModels.DeliverHistory,
            Project: mockModels.Project,
            DeliveryPersonNotification: mockModels.DeliveryPersonNotification,
            Notification: mockModels.Notification,
            InspectionRequest: mockModels.InspectionRequest,
            InspectionComment: mockModels.InspectionComment,
            InspectionHistory: mockModels.InspectionHistory,
            InspectionPerson: mockModels.InspectionPerson
        });
    });

    describe('getComment', () => {
        const mockInputData = {
            params: {
                DeliveryRequestId: 1,
                ProjectId: 1
            },
            user: {
                email: '<EMAIL>'
            }
        };

        beforeEach(() => {
            // Mock getDynamicModel for getComment tests
            jest.spyOn(commentService, 'getDynamicModel').mockResolvedValue(true);
        });

        it('should successfully get comments for a delivery request', async () => {
            // Mock successful delivery request lookup
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1
            });

            // Mock successful comment retrieval
            const mockComments = {
                rows: [
                    {
                        id: 1,
                        comment: 'Test comment',
                        Member: {
                            id: 1,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'Test',
                                lastName: 'User',
                                profilePic: 'pic.jpg'
                            }
                        }
                    }
                ],
                count: 1
            };
            mockModels.DeliverComment.findAndCountAll.mockResolvedValue(mockComments);

            const done = jest.fn();

            await commentService.getComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockComments, false);
            expect(mockModels.DeliveryRequest.findOne).toHaveBeenCalledWith({
                where: { id: 1, ProjectId: 1 }
            });
        });

        it('should handle non-existent delivery request', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.getComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
        });

        it('should handle errors during comment retrieval', async () => {
            mockModels.DeliveryRequest.findOne.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();

            await commentService.getComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing ProjectId parameter', async () => {
            const invalidInput = {
                params: {
                    DeliveryRequestId: 1
                    // Missing ProjectId
                },
                user: {
                    email: '<EMAIL>'
                }
            };

            const done = jest.fn();

            await commentService.getComment(invalidInput, done);

            expect(mockModels.DeliveryRequest.findOne).toHaveBeenCalledWith({
                where: { id: 1, ProjectId: NaN }
            });
        });

        it('should handle missing DeliveryRequestId parameter', async () => {
            const invalidInput = {
                params: {
                    ProjectId: 1
                    // Missing DeliveryRequestId
                },
                user: {
                    email: '<EMAIL>'
                }
            };

            const done = jest.fn();

            await commentService.getComment(invalidInput, done);

            expect(mockModels.DeliveryRequest.findOne).toHaveBeenCalledWith({
                where: { id: NaN, ProjectId: 1 }
            });
        });

        it('should handle empty comments result', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1
            });

            const emptyComments = {
                rows: [],
                count: 0
            };
            mockModels.DeliverComment.findAndCountAll.mockResolvedValue(emptyComments);

            const done = jest.fn();

            await commentService.getComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(emptyComments, false);
        });

        it('should handle comment retrieval database error', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1
            });

            mockModels.DeliverComment.findAndCountAll.mockRejectedValue(new Error('Comment DB error'));

            const done = jest.fn();

            await commentService.getComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('getInspectionComment', () => {
        const mockInputData = {
            params: {
                InspectionRequestId: 1,
                ProjectId: 1
            },
            user: {
                email: '<EMAIL>'
            }
        };

        beforeEach(() => {
            // Mock getDynamicModel for getInspectionComment tests
            jest.spyOn(commentService, 'getDynamicModel').mockResolvedValue(true);
        });

        it('should successfully get comments for an inspection request', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1
            });

            const mockComments = {
                rows: [
                    {
                        id: 1,
                        comment: 'Test inspection comment',
                        Member: {
                            id: 1,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'Test',
                                lastName: 'User',
                                profilePic: 'pic.jpg'
                            }
                        }
                    }
                ],
                count: 1
            };
            mockModels.InspectionComment.findAndCountAll.mockResolvedValue(mockComments);

            const done = jest.fn();

            await commentService.getInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockComments, false);
        });

        it('should handle non-existent inspection request', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.getInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Inspection Booking id does not exist' });
        });

        it('should handle errors during inspection comment retrieval', async () => {
            mockModels.InspectionRequest.findOne.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();

            await commentService.getInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing InspectionRequestId parameter', async () => {
            const invalidInput = {
                params: {
                    ProjectId: 1
                    // Missing InspectionRequestId
                },
                user: {
                    email: '<EMAIL>'
                }
            };

            const done = jest.fn();

            await commentService.getInspectionComment(invalidInput, done);

            expect(mockModels.InspectionRequest.findOne).toHaveBeenCalledWith({
                where: { id: NaN, ProjectId: 1 }
            });
        });

        it('should handle empty inspection comments result', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1
            });

            const emptyComments = {
                rows: [],
                count: 0
            };
            mockModels.InspectionComment.findAndCountAll.mockResolvedValue(emptyComments);

            const done = jest.fn();

            await commentService.getInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(emptyComments, false);
        });

        it('should handle inspection comment database error after finding request', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1
            });

            mockModels.InspectionComment.findAndCountAll.mockRejectedValue(new Error('Comment DB error'));

            const done = jest.fn();

            await commentService.getInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createComment', () => {
        const mockInputData = {
            body: {
                DeliveryRequestId: 1,
                comment: 'Test comment',
                ParentCompanyId: 1
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                profilePic: 'pic.jpg'
            }
        };

        beforeEach(() => {
            // Mock getDynamicModel for createComment tests
            jest.spyOn(commentService, 'getDynamicModel').mockResolvedValue(true);
        });

        it('should successfully create a comment and send notifications', async () => {
            // Mock delivery request lookup
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            // Mock member lookup
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            // Mock location lookup
            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location notification preferences
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);

            // Mock project lookup
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            // Mock notification creation
            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            // Mock additional required methods
            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(mockModels.DeliverComment.createInstance).toHaveBeenCalled();
            expect(mockModels.DeliverHistory.createInstance).toHaveBeenCalled();
            expect(mockModels.Notification.createInstance).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle non-existent delivery request during comment creation', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
        });

        it('should handle errors during comment creation', async () => {
            mockModels.DeliveryRequest.findOne.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing member data during comment creation', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing location data during comment creation', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing project data during comment creation', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle comment creation database error', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.DeliverComment.createInstance.mockRejectedValue(new Error('Comment creation failed'));

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing comment in request body', async () => {
            const invalidInput = {
                body: {
                    DeliveryRequestId: 1,
                    // Missing comment
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            const done = jest.fn();

            await commentService.createComment(invalidInput, done);

            // Should still proceed but with undefined comment
            expect(mockModels.DeliveryRequest.findOne).toHaveBeenCalled();
        });

        it('should handle successful comment creation with email notifications', async () => {
            // Mock delivery request lookup
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: false,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Member',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            // Mock member lookup
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            // Mock location lookup
            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location notification preferences with members
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            // Mock project lookup
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            // Mock notification creation
            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            // Mock additional required methods
            mockModels.DeliverComment.findAll.mockResolvedValue([
                { id: 1, comment: 'Previous comment' }
            ]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification'
                }
            }]);

            // Mock location notification preference lookup for email
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            // Don't mock getMemberDetailData - let it run normally

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(mockModels.DeliverComment.createInstance).toHaveBeenCalled();
            expect(mockModels.DeliverHistory.createInstance).toHaveBeenCalled();
            expect(mockModels.Notification.createInstance).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle guest user email notifications', async () => {
            // Mock delivery request with guest user
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: true,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Guest',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            // Setup other required mocks
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle complete notification flow with location preferences', async () => {
            // Mock delivery request
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: false,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Member',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences with members
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            // Mock delivery person data
            const mockDeliveryPersons = [{
                id: 1,
                Member: {
                    id: 4,
                    RoleId: 4,
                    User: {
                        id: 4,
                        email: '<EMAIL>',
                        firstName: 'Delivery',
                        lastName: 'Person'
                    }
                }
            }];
            mockModels.DeliveryPerson.findAll.mockResolvedValue(mockDeliveryPersons);

            // Mock admin data
            const mockAdminData = [{
                id: 5,
                RoleId: 1,
                User: {
                    id: 5,
                    email: '<EMAIL>',
                    firstName: 'Admin',
                    lastName: 'User'
                }
            }];
            mockModels.Member.findAll.mockResolvedValue(mockAdminData);

            mockModels.DeliverComment.findAll.mockResolvedValue([
                { id: 1, comment: 'Previous comment' }
            ]);

            // Mock notification preferences
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification',
                    inappNotification: true,
                    emailNotification: true
                }
            }]);

            // Mock location notification preference lookup
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(mockModels.DeliverComment.createInstance).toHaveBeenCalled();
            expect(mockModels.DeliverHistory.createInstance).toHaveBeenCalled();
            expect(mockModels.Notification.createInstance).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle email notifications with instant and daily digest preferences', async () => {
            // Mock delivery request
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: true,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Guest',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.DeliverComment.findAll.mockResolvedValue([]);

            // Mock notification preferences with both instant and daily digest
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification',
                    inappNotification: true,
                    emailNotification: true
                }
            }]);

            // Mock location notification preference lookup
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            // Mock member notification preference for location
            mockModels.NotificationPreference.findOne.mockResolvedValue({
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 7,
                    description: 'Location notification'
                }
            });

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle email notifications with only daily digest preference', async () => {
            // Mock delivery request
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.DeliverComment.findAll.mockResolvedValue([]);

            // Mock notification preferences with only daily digest
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: false,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification'
                }
            }]);

            // Mock location notification preference lookup
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            // Mock member notification preference for location
            mockModels.NotificationPreference.findOne.mockResolvedValue({
                id: 1,
                MemberId: 3,
                instant: false,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 7,
                    description: 'Location notification'
                }
            });

            const done = jest.fn();

            await commentService.createComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });
    });

    describe('createInspectionComment', () => {
        const mockInputData = {
            body: {
                InspectionRequestId: 1,
                comment: 'Test inspection comment',
                ParentCompanyId: 1
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                profilePic: 'pic.jpg'
            }
        };

        beforeEach(() => {
            // Mock getDynamicModel for createInspectionComment tests
            jest.spyOn(commentService, 'getDynamicModel').mockResolvedValue(true);
        });

        it('should successfully create an inspection comment and send notifications', async () => {
            // Mock inspection request lookup
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            // Mock member lookup
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            // Mock location lookup
            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location notification preferences
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);

            // Mock project lookup
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            // Mock notification creation
            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            // Mock additional required methods
            mockModels.InspectionComment.findAll.mockResolvedValue([]);
            mockModels.InspectionPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(mockModels.InspectionComment.createInstance).toHaveBeenCalled();
            expect(mockModels.InspectionHistory.createInstance).toHaveBeenCalled();
            expect(mockModels.Notification.createInstance).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle non-existent inspection request during comment creation', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
        });

        it('should handle errors during inspection comment creation', async () => {
            mockModels.InspectionRequest.findOne.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing member data during inspection comment creation', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle inspection comment creation database error', async () => {
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.InspectionComment.createInstance.mockRejectedValue(new Error('Inspection comment creation failed'));

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing InspectionRequestId in request body', async () => {
            const invalidInput = {
                body: {
                    // Missing InspectionRequestId
                    comment: 'Test inspection comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            mockModels.InspectionRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await commentService.createInspectionComment(invalidInput, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
        });

        it('should handle successful inspection comment creation with notifications', async () => {
            // Mock inspection request lookup
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: false,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Member',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            // Mock member lookup
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            // Mock location lookup
            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location notification preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            // Mock project lookup
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            // Mock notification creation
            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            // Mock additional required methods
            mockModels.InspectionComment.findAll.mockResolvedValue([
                { id: 1, comment: 'Previous comment' }
            ]);
            mockModels.InspectionPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: false,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification'
                }
            }]);

            // Mock location notification preference lookup for email
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(mockModels.InspectionComment.createInstance).toHaveBeenCalled();
            expect(mockModels.InspectionHistory.createInstance).toHaveBeenCalled();
            expect(mockModels.Notification.createInstance).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle inspection comment with guest user notifications', async () => {
            // Mock inspection request with guest user
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: true,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Guest',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            // Setup other required mocks
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.InspectionComment.findAll.mockResolvedValue([]);
            mockModels.InspectionPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle complete inspection notification flow with email preferences', async () => {
            // Mock inspection request
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: true,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Guest',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.InspectionPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.InspectionComment.findAll.mockResolvedValue([]);

            // Mock notification preferences
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification'
                }
            }]);

            // Mock location notification preference lookup
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            // Mock member notification preference for location
            mockModels.NotificationPreference.findOne.mockResolvedValue({
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 7,
                    description: 'Location notification'
                }
            });

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle inspection comment without location preferences', async () => {
            // Mock inspection request
            mockModels.InspectionRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                InspectionId: 'INS-001',
                description: 'Test inspection',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // No location preferences
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.InspectionPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.InspectionComment.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            const done = jest.fn();

            await commentService.createInspectionComment(mockInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });
    });

    describe('findDomainEnterprise', () => {
        it('should return domain name when enterprise exists', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'testdomain'
            });

            const result = await commentService.findDomainEnterprise('testdomain.com');
            expect(result).toBe('testdomain');
        });

        it('should return empty string when enterprise does not exist', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue(null);

            const result = await commentService.findDomainEnterprise('nonexistent.com');
            expect(result).toBe('');
        });

        it('should return null when no domain name provided', async () => {
            const result = await commentService.findDomainEnterprise(null);
            expect(result).toBeNull();
        });

        it('should return null when undefined domain name provided', async () => {
            const result = await commentService.findDomainEnterprise(undefined);
            expect(result).toBeNull();
        });

        it('should return empty string when domain name is empty string', async () => {
            const result = await commentService.findDomainEnterprise('');
            expect(result).toBeNull();
        });

        it('should handle database error during enterprise lookup', async () => {
            mockModels.Enterprise.findOne.mockRejectedValue(new Error('Database error'));

            await expect(commentService.findDomainEnterprise('testdomain.com')).rejects.toThrow('Database error');
        });

        it('should handle case insensitive domain lookup', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'testdomain'
            });

            const result = await commentService.findDomainEnterprise('TESTDOMAIN.COM');
            expect(result).toBe('testdomain');
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'testdomain.com' }
            });
        });
    });

    describe('findEnterpriseByParentCompany', () => {
        const mockEmail = '<EMAIL>';
        const mockParentCompanyId = 1;

        beforeEach(async () => {
            // Ensure returnProjectModel is called to set up publicUser and publicMember
            await commentService.returnProjectModel();
        });

        it('should return enterprise name for account member', async () => {
            // Mock the helper to return proper models with findOne methods
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    email: mockEmail
                })
            };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    UserId: 1,
                    isAccount: true,
                    EnterpriseId: 1
                })
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'testenterprise',
                status: 'completed'
            });

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBe('testenterprise');
        });

        it('should return null when user not found', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue(null)
            };
            const mockPublicMember = {
                findOne: jest.fn()
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBeNull();
        });

        it('should return null when no parent company id provided', async () => {
            const result = await commentService.findEnterpriseByParentCompany(null, mockEmail);
            expect(result).toBeNull();
        });

        it('should return null when parent company id is undefined string', async () => {
            const result = await commentService.findEnterpriseByParentCompany('undefined', mockEmail);
            expect(result).toBeNull();
        });

        it('should return null when no email provided', async () => {
            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, null);
            expect(result).toBeNull();
        });

        it('should return null when email is empty string', async () => {
            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, '');
            expect(result).toBeNull();
        });

        it('should handle member not found scenario', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    email: mockEmail
                })
            };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue(null)
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'fallbackenterprise',
                status: 'completed'
            });

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBe('fallbackenterprise');
        });

        it('should handle non-account member scenario', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    email: mockEmail
                })
            };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    UserId: 1,
                    isAccount: false,
                    EnterpriseId: 1
                })
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'nonaccountenterprise',
                status: 'completed'
            });

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBe('nonaccountenterprise');
        });

        it('should handle account member with enterprise scenario', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    email: mockEmail
                })
            };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    UserId: 1,
                    isAccount: true,
                    EnterpriseId: 2
                })
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'accountenterprise',
                status: 'completed'
            });

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBe('accountenterprise');
        });

        it('should handle database error during user lookup', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockRejectedValue(new Error('User lookup error'))
            };
            const mockPublicMember = {
                findOne: jest.fn()
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await expect(commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail))
                .rejects.toThrow('User lookup error');
        });

        it('should handle database error during member lookup', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    email: mockEmail
                })
            };
            const mockPublicMember = {
                findOne: jest.fn().mockRejectedValue(new Error('Member lookup error'))
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            await expect(commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail))
                .rejects.toThrow('Member lookup error');
        });

        it('should handle enterprise not found scenario', async () => {
            const mockPublicUser = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    email: mockEmail
                })
            };
            const mockPublicMember = {
                findOne: jest.fn().mockResolvedValue({
                    id: 1,
                    UserId: 1,
                    isAccount: true,
                    EnterpriseId: 1
                })
            };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockPublicMember
            });

            mockModels.Enterprise.findOne.mockResolvedValue(null);

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBeNull();
        });
    });

    describe('createDailyDigestData', () => {
        const mockData = {
            MemberId: 1,
            ProjectId: 1,
            ParentCompanyId: 1,
            loginUser: {
                firstName: 'Test',
                lastName: 'User'
            },
            dailyDigestMessage: 'commented in a',
            requestType: 'Delivery Request',
            messages: 'Test message',
            requestId: 1
        };

        it('should create daily digest for delivery request', async () => {
            const { DigestNotification } = require('../../models');

            await commentService.createDailyDigestData(mockData);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                MemberId: mockData.MemberId,
                ProjectId: mockData.ProjectId,
                ParentCompanyId: mockData.ParentCompanyId,
                isSent: false,
                isDeleted: false
            }));
        });

        it('should create daily digest for crane request', async () => {
            const { DigestNotification } = require('../../models');
            const craneData = { ...mockData, requestType: 'Crane Request' };
            await commentService.createDailyDigestData(craneData);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                description: expect.stringContaining('crane-request')
            }));
        });

        it('should create daily digest for concrete request', async () => {
            const { DigestNotification } = require('../../models');
            const concreteData = { ...mockData, requestType: 'Concrete Request' };
            await commentService.createDailyDigestData(concreteData);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                description: expect.stringContaining('concrete-request')
            }));
        });

        it('should handle missing request type gracefully', async () => {
            const { DigestNotification } = require('../../models');
            const unknownData = { ...mockData, requestType: 'Unknown Request' };
            await commentService.createDailyDigestData(unknownData);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                MemberId: mockData.MemberId,
                ProjectId: mockData.ProjectId,
                ParentCompanyId: mockData.ParentCompanyId
            }));
        });

        it('should handle missing login user data', async () => {
            const { DigestNotification } = require('../../models');
            const dataWithoutUser = { ...mockData, loginUser: {} };
            await commentService.createDailyDigestData(dataWithoutUser);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                description: expect.stringContaining('undefined  undefined')
            }));
        });

        it('should handle inspection request type', async () => {
            const { DigestNotification } = require('../../models');
            const inspectionData = { ...mockData, requestType: 'Inspection Request' };
            await commentService.createDailyDigestData(inspectionData);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                MemberId: mockData.MemberId,
                ProjectId: mockData.ProjectId,
                ParentCompanyId: mockData.ParentCompanyId
            }));
        });

        it('should handle missing request id', async () => {
            const { DigestNotification } = require('../../models');
            const dataWithoutRequestId = { ...mockData, requestId: 1 }; // Use valid requestId
            await commentService.createDailyDigestData(dataWithoutRequestId);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                description: expect.any(String)
            }));
        });

        it('should handle null login user', async () => {
            const { DigestNotification } = require('../../models');
            const dataWithNullUser = {
                ...mockData,
                loginUser: { firstName: null, lastName: null }
            };
            await commentService.createDailyDigestData(dataWithNullUser);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                description: expect.any(String)
            }));
        });

        it('should handle missing member id', async () => {
            const { DigestNotification } = require('../../models');
            const dataWithoutMemberId = { ...mockData, MemberId: 1 }; // Use valid MemberId
            await commentService.createDailyDigestData(dataWithoutMemberId);

            expect(DigestNotification.create).toHaveBeenCalledWith(expect.objectContaining({
                MemberId: 1,
                ProjectId: mockData.ProjectId,
                ParentCompanyId: mockData.ParentCompanyId
            }));
        });

        it('should handle database error during digest creation', async () => {
            const { DigestNotification } = require('../../models');
            DigestNotification.create.mockRejectedValue(new Error('Database error'));

            await expect(commentService.createDailyDigestData(mockData))
                .rejects.toThrow('Database error');
        });
    });

    describe('getMemberDetailData', () => {
        const mockData = {
            memberData: [
                {
                    Member: {
                        id: 1,
                        User: {
                            id: 1,
                            email: '<EMAIL>',
                            firstName: 'Member',
                            lastName: 'One'
                        }
                    }
                }
            ],
            adminData: [
                {
                    id: 2,
                    User: {
                        id: 2,
                        email: '<EMAIL>',
                        firstName: 'Admin',
                        lastName: 'User'
                    }
                }
            ]
        };

        const mockLocationPreference = [
            {
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        email: '<EMAIL>',
                        firstName: 'Location',
                        lastName: 'User'
                    }
                }
            }
        ];

        it('should return unique email array from member data', async () => {
            const result = await commentService.getMemberDetailData(mockData, []);

            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({
                email: '<EMAIL>',
                firstName: 'Member',
                UserId: 1,
                MemberId: 1
            });
            expect(result[1]).toEqual({
                email: '<EMAIL>',
                firstName: 'Admin',
                UserId: 2,
                MemberId: 2
            });
        });

        it('should include location preference members', async () => {
            const result = await commentService.getMemberDetailData(mockData, mockLocationPreference);

            expect(result).toHaveLength(3);
            expect(result[2]).toEqual({
                email: '<EMAIL>',
                firstName: 'Location',
                lastName: 'User',
                UserId: 3,
                MemberId: 3,
                RoleId: 3
            });
        });

        it('should handle duplicate emails correctly', async () => {
            const dataWithDuplicates = {
                memberData: [
                    {
                        Member: {
                            id: 1,
                            User: {
                                id: 1,
                                email: '<EMAIL>',
                                firstName: 'First'
                            }
                        }
                    }
                ],
                adminData: [
                    {
                        id: 1,
                        User: {
                            id: 1,
                            email: '<EMAIL>',
                            firstName: 'Second'
                        }
                    }
                ]
            };

            const result = await commentService.getMemberDetailData(dataWithDuplicates, []);

            expect(result).toHaveLength(1);
            expect(result[0].email).toBe('<EMAIL>');
            expect(result[0].firstName).toBe('First');
        });

        it('should handle empty data gracefully', async () => {
            const result = await commentService.getMemberDetailData({}, []);
            expect(result).toEqual([]);
        });

        it('should handle undefined member data', async () => {
            const result = await commentService.getMemberDetailData({ memberData: undefined, adminData: undefined }, undefined);
            expect(result).toEqual([]);
        });

        it('should handle null member data', async () => {
            await expect(commentService.getMemberDetailData({ memberData: null, adminData: null }, null))
                .rejects.toThrow();
        });

        it('should handle mixed valid and invalid member data', async () => {
            const mixedData = {
                memberData: [
                    {
                        Member: {
                            id: 1,
                            User: {
                                id: 1,
                                email: '<EMAIL>',
                                firstName: 'Valid'
                            }
                        }
                    }
                ],
                adminData: []
            };

            const result = await commentService.getMemberDetailData(mixedData, []);

            // Should only include valid entries
            expect(result).toHaveLength(1);
            expect(result[0].email).toBe('<EMAIL>');
        });

        it('should handle location preference members with missing user data', async () => {
            const mockLocationPreference = [];

            const result = await commentService.getMemberDetailData({}, mockLocationPreference);
            expect(result).toEqual([]);
        });

        it('should handle large datasets efficiently', async () => {
            const largeData = {
                memberData: Array.from({ length: 100 }, (_, i) => ({
                    Member: {
                        id: i + 1,
                        User: {
                            id: i + 1,
                            email: `member${i}@example.com`,
                            firstName: `Member${i}`
                        }
                    }
                })),
                adminData: Array.from({ length: 50 }, (_, i) => ({
                    id: i + 101,
                    User: {
                        id: i + 101,
                        email: `admin${i}@example.com`,
                        firstName: `Admin${i}`
                    }
                }))
            };

            const result = await commentService.getMemberDetailData(largeData, []);
            expect(result).toHaveLength(150);
        });
    });

    describe('getDynamicModel', () => {
        const mockInputData = {
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain'
            },
            body: {
                ParentCompanyId: 1
            }
        };

        beforeEach(() => {
            // Mock helper functions
            helper.returnProjectModel.mockResolvedValue({
                User: mockModels.User,
                Member: mockModels.Member
            });

            helper.getDynamicModel.mockResolvedValue({
                DeliveryRequest: mockModels.DeliveryRequest,
                DeliveryPerson: {},
                DeliverComment: mockModels.DeliverComment,
                Member: mockModels.Member,
                User: mockModels.User,
                DeliverHistory: {},
                Project: mockModels.Project,
                DeliveryPersonNotification: {},
                Notification: mockModels.Notification
            });
        });

        it('should successfully get dynamic model with domain name', async () => {
            // Mock findDomainEnterprise to return a domain name
            jest.spyOn(commentService, 'findDomainEnterprise').mockResolvedValue('testdomain');
            jest.spyOn(commentService, 'findEnterpriseByParentCompany').mockResolvedValue(null);
            jest.spyOn(commentService, 'updateModels').mockResolvedValue();

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            const result = await commentService.getDynamicModel(mockInputData);

            expect(result).toBe(true);
            expect(helper.returnProjectModel).toHaveBeenCalled();
            expect(commentService.findDomainEnterprise).toHaveBeenCalledWith('testdomain');
        });

        it('should handle missing domain name and use parent company', async () => {
            const inputWithoutDomain = {
                user: {
                    email: '<EMAIL>',
                    domainName: null
                },
                body: {
                    ParentCompanyId: 1
                }
            };

            // Mock findDomainEnterprise to return null (no domain found)
            jest.spyOn(commentService, 'findDomainEnterprise').mockResolvedValue(null);
            jest.spyOn(commentService, 'findEnterpriseByParentCompany').mockResolvedValue('parentcompany');
            jest.spyOn(commentService, 'updateModels').mockResolvedValue();

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            const result = await commentService.getDynamicModel(inputWithoutDomain);

            expect(result).toBe(true);
            expect(commentService.findEnterpriseByParentCompany).toHaveBeenCalledWith(1, '<EMAIL>');
        });

        it('should handle missing user data', async () => {
            const inputWithoutUser = {
                user: {
                    email: '<EMAIL>',
                    domainName: null
                },
                body: {
                    ParentCompanyId: 1
                }
            };

            // Mock findDomainEnterprise to return null (no domain found)
            jest.spyOn(commentService, 'findDomainEnterprise').mockResolvedValue(null);
            jest.spyOn(commentService, 'findEnterpriseByParentCompany').mockResolvedValue(null);
            jest.spyOn(commentService, 'updateModels').mockResolvedValue();

            const result = await commentService.getDynamicModel(inputWithoutUser);

            expect(result).toBe(true);
            expect(commentService.findEnterpriseByParentCompany).toHaveBeenCalledWith(1, '<EMAIL>');
        });

        it('should handle params ParentCompanyId', async () => {
            const inputWithParams = {
                user: {
                    email: '<EMAIL>',
                    domainName: null
                },
                params: {
                    ParentCompanyId: 2
                }
            };

            mockModels.Enterprise.findOne
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({ name: 'paramscompany' });

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            mockModels.Member.findOne.mockResolvedValue(null);

            const result = await commentService.getDynamicModel(inputWithParams);

            expect(result).toBe(true);
        });

        it('should handle missing user object', async () => {
            const inputWithoutUser = {
                user: {},
                body: {
                    ParentCompanyId: 1
                }
            };

            jest.spyOn(commentService, 'findDomainEnterprise').mockResolvedValue(null);
            jest.spyOn(commentService, 'findEnterpriseByParentCompany').mockResolvedValue(null);
            jest.spyOn(commentService, 'updateModels').mockResolvedValue();

            const result = await commentService.getDynamicModel(inputWithoutUser);

            expect(result).toBe(true);
            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle missing body and params', async () => {
            const inputMinimal = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain'
                }
            };

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'testdomain'
            });

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            const result = await commentService.getDynamicModel(inputMinimal);

            expect(result).toBe(true);
        });

        it('should handle helper.returnProjectModel error', async () => {
            helper.returnProjectModel.mockRejectedValue(new Error('Helper error'));

            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain'
                },
                body: {}
            };

            await expect(commentService.getDynamicModel(inputData)).rejects.toThrow('Helper error');
        });

        it('should handle helper.getDynamicModel error', async () => {
            jest.spyOn(commentService, 'findDomainEnterprise').mockResolvedValue('testdomain');
            jest.spyOn(commentService, 'updateModels').mockRejectedValue(new Error('Dynamic model error'));

            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain'
                },
                body: {}
            };

            await expect(commentService.getDynamicModel(inputData)).rejects.toThrow('Dynamic model error');
        });

        it('should handle both domain name and parent company id present', async () => {
            const inputWithBoth = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'primarydomain'
                },
                body: {
                    ParentCompanyId: 1
                }
            };

            // Mock findDomainEnterprise to return the primary domain
            jest.spyOn(commentService, 'findDomainEnterprise').mockResolvedValue('primarydomain');
            jest.spyOn(commentService, 'findEnterpriseByParentCompany').mockResolvedValue('parentcompany');
            jest.spyOn(commentService, 'updateModels').mockResolvedValue();

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            const result = await commentService.getDynamicModel(inputWithBoth);

            expect(result).toBe(true);
            // Domain name takes precedence, so it should be called with primarydomain
            expect(commentService.findDomainEnterprise).toHaveBeenCalledWith('primarydomain');
        });

        it('should handle empty user email', async () => {
            const inputWithEmptyEmail = {
                user: {
                    email: '',
                    domainName: null
                },
                body: {
                    ParentCompanyId: 1
                }
            };

            mockModels.Enterprise.findOne
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({ name: 'fallbackcompany' });

            const result = await commentService.getDynamicModel(inputWithEmptyEmail);

            expect(result).toBe(true);
        });

        it('should handle null parent company id', async () => {
            const inputWithNullParentId = {
                user: {
                    email: '<EMAIL>',
                    domainName: null
                },
                body: {
                    ParentCompanyId: null
                }
            };

            mockModels.Enterprise.findOne.mockResolvedValue(null);

            const result = await commentService.getDynamicModel(inputWithNullParentId);

            expect(result).toBe(true);
        });

        it('should handle user update failure', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'testdomain'
                },
                body: {}
            };

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'testdomain'
            });

            mockModels.User.findOne.mockResolvedValue(null); // User not found

            const result = await commentService.getDynamicModel(inputData);

            expect(result).toBe(true);
            // Should not update user data if user not found
            expect(inputData.user.email).toBe('<EMAIL>');
        });

        it('should handle enterprise with incomplete status', async () => {
            const inputData = {
                user: {
                    email: '<EMAIL>',
                    domainName: null
                },
                body: {
                    ParentCompanyId: 1
                }
            };

            mockModels.Enterprise.findOne
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({ name: 'incompleteenterprise', status: 'pending' });

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>'
            });

            mockModels.Member.findOne.mockResolvedValue(null);

            const result = await commentService.getDynamicModel(inputData);

            expect(result).toBe(true);
        });
    });

    describe('returnProjectModel', () => {
        it('should successfully call helper.returnProjectModel', async () => {
            const mockProjectModel = {
                User: mockModels.User,
                Member: mockModels.Member
            };

            helper.returnProjectModel.mockResolvedValue(mockProjectModel);

            await commentService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle error in returnProjectModel', async () => {
            helper.returnProjectModel.mockRejectedValue(new Error('Project model error'));

            await expect(commentService.returnProjectModel()).rejects.toThrow('Project model error');
        });
    });

    describe('updateModels', () => {
        it('should update models with domain name', async () => {
            const mockModelObj = {
                DeliveryRequest: 'mockDeliveryRequest',
                DeliveryPerson: 'mockDeliveryPerson',
                DeliverComment: 'mockDeliverComment',
                Member: 'mockMember',
                User: 'mockUser',
                DeliverHistory: 'mockDeliverHistory',
                Project: 'mockProject',
                DeliveryPersonNotification: 'mockDeliveryPersonNotification',
                Notification: 'mockNotification'
            };

            helper.getDynamicModel.mockResolvedValue(mockModelObj);

            await commentService.updateModels('testdomain');

            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
        });

        it('should handle null domain name', async () => {
            helper.getDynamicModel.mockResolvedValue({});

            await commentService.updateModels(null);

            expect(helper.getDynamicModel).toHaveBeenCalledWith(null);
        });
    });

    describe('returnProjectModel', () => {
        it('should return project model successfully', async () => {
            const mockModelData = {
                User: 'mockUser',
                Member: 'mockMember'
            };

            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await commentService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle errors in returnProjectModel', async () => {
            helper.returnProjectModel.mockRejectedValue(new Error('Model error'));

            await expect(commentService.returnProjectModel()).rejects.toThrow('Model error');
        });
    });



    describe('Edge Cases and Error Handling', () => {
        beforeEach(() => {
            // Mock getDynamicModel for edge case tests
            jest.spyOn(commentService, 'getDynamicModel').mockResolvedValue(true);
        });

        it('should handle malformed input data in getComment', async () => {
            const malformedInput = {
                params: null,
                user: {
                    email: '<EMAIL>'
                }
            };

            const done = jest.fn();

            await commentService.getComment(malformedInput, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle malformed input data in getInspectionComment', async () => {
            const malformedInput = {
                params: null,
                user: {
                    email: '<EMAIL>'
                }
            };

            const done = jest.fn();

            await commentService.getInspectionComment(malformedInput, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle null user in createComment', async () => {
            const inputWithNullUser = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: null
            };

            const done = jest.fn();

            await commentService.createComment(inputWithNullUser, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle null body in createComment', async () => {
            const inputWithNullBody = {
                body: null,
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            };

            const done = jest.fn();

            await commentService.createComment(inputWithNullBody, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle notification preference database error', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);

            // Mock notification preference error
            mockModels.NotificationPreference.findAll.mockRejectedValue(new Error('Notification preference error'));

            const done = jest.fn();
            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle notification preferences with different role combinations', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: false,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Member',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences with different roles
            const mockLocationPreferences = [
                {
                    Member: {
                        id: 3,
                        RoleId: 3, // Role 3
                        User: {
                            id: 3,
                            firstName: 'Role3',
                            lastName: 'User',
                            email: '<EMAIL>'
                        }
                    }
                },
                {
                    Member: {
                        id: 4,
                        RoleId: 4, // Role 4
                        User: {
                            id: 4,
                            firstName: 'Role4',
                            lastName: 'User',
                            email: '<EMAIL>'
                        }
                    }
                }
            ];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);

            // Mock notification preferences for different roles
            mockModels.NotificationPreference.findAll.mockResolvedValue([
                {
                    id: 1,
                    MemberId: 3,
                    instant: true,
                    dailyDigest: false,
                    NotificationPreferenceItem: {
                        id: 4,
                        description: 'Comment notification'
                    }
                },
                {
                    id: 2,
                    MemberId: 4,
                    instant: false,
                    dailyDigest: true,
                    NotificationPreferenceItem: {
                        id: 4,
                        description: 'Comment notification'
                    }
                }
            ]);

            // Mock location notification preference lookup
            mockModels.LocationNotificationPreferences.findOne
                .mockResolvedValueOnce({
                    MemberId: 3,
                    ProjectId: 1,
                    LocationId: 1,
                    follow: true
                })
                .mockResolvedValueOnce({
                    MemberId: 4,
                    ProjectId: 1,
                    LocationId: 1,
                    follow: true
                });

            // Mock member notification preferences for location
            mockModels.NotificationPreference.findOne
                .mockResolvedValueOnce({
                    id: 1,
                    MemberId: 3,
                    instant: true,
                    dailyDigest: false,
                    NotificationPreferenceItem: {
                        id: 7,
                        description: 'Location notification'
                    }
                })
                .mockResolvedValueOnce({
                    id: 2,
                    MemberId: 4,
                    instant: false,
                    dailyDigest: true,
                    NotificationPreferenceItem: {
                        id: 7,
                        description: 'Location notification'
                    }
                });

            const done = jest.fn();
            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle complex email notification scenarios', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [{
                    Member: {
                        id: 2,
                        isGuestUser: true,
                        User: {
                            email: '<EMAIL>',
                            firstName: 'Guest',
                            lastName: 'User'
                        }
                    }
                }],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([{
                id: 1,
                MemberId: 3,
                instant: false,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 4,
                    description: 'Comment notification'
                }
            }]);

            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            const done = jest.fn();

            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle empty memberLocationPreference in createComment', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            const done = jest.fn();

            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle findEnterpriseByParentCompany with account member', async () => {
            const mockEmail = '<EMAIL>';
            const mockParentCompanyId = 1;

            // First call returnProjectModel to set up publicUser and publicMember
            await commentService.returnProjectModel();

            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: mockEmail
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                isAccount: true,
                EnterpriseId: 2
            });

            mockModels.Enterprise.findOne.mockResolvedValue({
                name: 'accountenterprise',
                status: 'completed'
            });

            const result = await commentService.findEnterpriseByParentCompany(mockParentCompanyId, mockEmail);
            expect(result).toBe('accountenterprise');
        });

        it('should handle findDomainEnterprise with various inputs', async () => {
            // Reset mocks for this test
            jest.clearAllMocks();

            // Test with valid domain
            mockModels.Enterprise.findOne.mockResolvedValueOnce({
                name: 'testdomain'
            });

            let result = await commentService.findDomainEnterprise('testdomain.com');
            expect(result).toBe('testdomain');

            // Test with null domain
            result = await commentService.findDomainEnterprise(null);
            expect(result).toBeNull();

            // Test with undefined domain
            result = await commentService.findDomainEnterprise(undefined);
            expect(result).toBeNull();

            // Test with empty string
            result = await commentService.findDomainEnterprise('');
            expect(result).toBeNull();
        });

        it('should handle createDailyDigestData with all request types', async () => {
            const baseData = {
                MemberId: 1,
                ProjectId: 1,
                ParentCompanyId: 1,
                loginUser: {
                    firstName: 'Test',
                    lastName: 'User'
                },
                dailyDigestMessage: 'commented in a',
                messages: 'Test message',
                requestId: 1
            };

            // Test Delivery Request
            await commentService.createDailyDigestData({
                ...baseData,
                requestType: 'Delivery Request'
            });

            // Test Crane Request
            await commentService.createDailyDigestData({
                ...baseData,
                requestType: 'Crane Request'
            });

            // Test Concrete Request
            await commentService.createDailyDigestData({
                ...baseData,
                requestType: 'Concrete Request'
            });

            // Test Unknown Request Type
            await commentService.createDailyDigestData({
                ...baseData,
                requestType: 'Unknown Request'
            });

            expect(mockModels.DigestNotification.create).toHaveBeenCalledTimes(4);
        });

        it('should handle getMemberDetailData with complex scenarios', async () => {
            const complexData = {
                memberData: [
                    {
                        Member: {
                            id: 1,
                            User: {
                                id: 1,
                                email: '<EMAIL>',
                                firstName: 'Member1'
                            }
                        }
                    },
                    {
                        Member: {
                            id: 2,
                            User: {
                                id: 2,
                                email: '<EMAIL>', // Duplicate email
                                firstName: 'Member2'
                            }
                        }
                    }
                ],
                adminData: [
                    {
                        id: 3,
                        User: {
                            id: 3,
                            email: '<EMAIL>',
                            firstName: 'Admin'
                        }
                    }
                ]
            };

            const locationPreference = [
                {
                    Member: {
                        id: 4,
                        RoleId: 4,
                        User: {
                            id: 4,
                            email: '<EMAIL>',
                            firstName: 'Location',
                            lastName: 'User'
                        }
                    }
                }
            ];

            const result = await commentService.getMemberDetailData(complexData, locationPreference);

            // Should handle duplicates correctly
            expect(result).toHaveLength(3);
            expect(result.find(r => r.email === '<EMAIL>').firstName).toBe('Member1');
        });

        it('should handle notification preference item ID 7 (location notifications)', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            // Mock location notification preference lookup
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: true
            });

            // Mock member notification preference for location with ID 7
            mockModels.NotificationPreference.findOne.mockResolvedValue({
                id: 1,
                MemberId: 3,
                instant: true,
                dailyDigest: true,
                NotificationPreferenceItem: {
                    id: 7,
                    description: 'Location notification'
                }
            });

            const done = jest.fn();

            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle notification with no location notification preference', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            // Mock location notification preference lookup to return null
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue(null);

            // Mock member notification preference for location to return null
            mockModels.NotificationPreference.findOne.mockResolvedValue(null);

            const done = jest.fn();

            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });

        it('should handle notification with location preference follow false', async () => {
            mockModels.DeliveryRequest.findOne.mockResolvedValue({
                id: 1,
                ProjectId: 1,
                DeliveryId: 'DEL-001',
                description: 'Test delivery',
                deliveryStart: new Date(),
                deliveryEnd: new Date(),
                memberDetails: [],
                LocationId: 1
            });

            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                UserId: 1,
                ProjectId: 1
            });

            mockModels.Locations.findOne.mockResolvedValue({
                id: 1,
                locationPath: 'Test Location'
            });

            // Mock location preferences
            const mockLocationPreferences = [{
                Member: {
                    id: 3,
                    RoleId: 3,
                    User: {
                        id: 3,
                        firstName: 'Location',
                        lastName: 'User',
                        email: '<EMAIL>'
                    }
                }
            }];
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockLocationPreferences);

            mockModels.Project.findByPk.mockResolvedValue({
                id: 1,
                projectName: 'Test Project'
            });

            mockModels.Notification.createInstance.mockResolvedValue({
                id: 1,
                MemberId: 1
            });

            mockModels.DeliverComment.findAll.mockResolvedValue([]);
            mockModels.DeliveryPerson.findAll.mockResolvedValue([]);
            mockModels.Member.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            // Mock location notification preference lookup with follow false
            mockModels.LocationNotificationPreferences.findOne.mockResolvedValue({
                MemberId: 3,
                ProjectId: 1,
                LocationId: 1,
                follow: false
            });

            const done = jest.fn();

            const testInputData = {
                body: {
                    DeliveryRequestId: 1,
                    comment: 'Test comment',
                    ParentCompanyId: 1
                },
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User',
                    profilePic: 'pic.jpg'
                }
            };

            await commentService.createComment(testInputData, done);

            expect(done).toHaveBeenCalledWith(expect.any(Object), false);
        });
    });
});