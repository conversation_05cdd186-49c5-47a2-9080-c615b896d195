// Mock concreteRequestHistoryService
jest.mock('../../services', () => ({
  concreteRequestHistoryService: {
    createConcreteRequestHistory: jest.fn(),
    getConcreteRequestHistories: jest.fn(),
  },
}));

const concreteRequestHistoryController = require('../concreteRequestHistoryController');
const { concreteRequestHistoryService } = require('../../services');

describe('concreteRequestHistoryController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createConcreteRequestHistory', () => {
    it('should create concrete request history successfully', async () => {
      const mockResponse = { id: 1, action: 'created', timestamp: '2023-01-01T10:00:00Z' };

      concreteRequestHistoryService.createConcreteRequestHistory.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestHistoryController.createConcreteRequestHistory(mockReq, mockRes, mockNext);

      expect(concreteRequestHistoryService.createConcreteRequestHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking History created successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create concrete request history', async () => {
      const mockError = new Error('Service error');
      concreteRequestHistoryService.createConcreteRequestHistory.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestHistoryController.createConcreteRequestHistory(mockReq, mockRes, mockNext);

      expect(concreteRequestHistoryService.createConcreteRequestHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create concrete request history', async () => {
      const mockError = new Error('Exception error');
      concreteRequestHistoryService.createConcreteRequestHistory.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestHistoryController.createConcreteRequestHistory(mockReq, mockRes, mockNext);

      expect(concreteRequestHistoryService.createConcreteRequestHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getConcreteRequestHistories', () => {
    it('should get concrete request histories successfully', async () => {
      const mockResponse = {
        historyList: [
          { id: 1, action: 'created', timestamp: '2023-01-01T10:00:00Z' },
          { id: 2, action: 'updated', timestamp: '2023-01-01T11:00:00Z' },
        ],
        exist: { id: 1, requestData: 'test' },
      };

      concreteRequestHistoryService.getConcreteRequestHistories.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestHistoryController.getConcreteRequestHistories(mockReq, mockRes, mockNext);

      expect(concreteRequestHistoryService.getConcreteRequestHistories).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking History Viewed Successfully.',
        data: mockResponse.historyList,
        concreteRequest: mockResponse.exist,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get concrete request histories', async () => {
      const mockError = new Error('Service error');
      concreteRequestHistoryService.getConcreteRequestHistories.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestHistoryController.getConcreteRequestHistories(mockReq, mockRes, mockNext);

      expect(concreteRequestHistoryService.getConcreteRequestHistories).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get concrete request histories', async () => {
      const mockError = new Error('Exception error');
      concreteRequestHistoryService.getConcreteRequestHistories.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestHistoryController.getConcreteRequestHistories(mockReq, mockRes, mockNext);

      expect(concreteRequestHistoryService.getConcreteRequestHistories).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
