const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { CalendarController } = require('../controllers');
const { calendarValidation } = require('../middlewares/validations');
const cacheMiddleware = require('../middlewares/cacheMiddleware');

const deliveryRoute = {
  get router() {
    const router = Router();
    router.post(
      '/event_NDR/:ProjectId/:void',
      validate(calendarValidation.getEventNDR, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CalendarController.getEventNDR,
    );
    router.post(
      '/get_crane_associated_request/:ProjectId/:void',
      validate(
        calendarValidation.getDeliveryRequestWithCrane,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CalendarController.getDeliveryRequestWithCrane,
    );
    router.post(
      '/get_concrete_request/:ProjectId/:void',
      passportConfig.isAuthenticated,
      CalendarController.getConcreteRequest,
    );
    router.post(
      '/get_inspection_request/:ProjectId/:void',
      passportConfig.isAuthenticated,
      CalendarController.getInspectionEventNDR,
    );
    router.post(
      '/get_all_calendar/:ProjectId/:void',
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheAllCalendar(),
      CalendarController.captureInspectionEventNDR,
      CalendarController.captureEventNDR,
      CalendarController.captureConcreteEventNDR,
      CalendarController.captureCraneEventNDR,
      CalendarController.getAllCalendarData
    );
    router.post('/checkOverlapping', passportConfig.isAuthenticated, CalendarController.checkOverlappingToRestore)
    return router;
  },
};
module.exports = deliveryRoute;
