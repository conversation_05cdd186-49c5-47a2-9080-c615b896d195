const inspectionController = require('../inspectionController');
const { inspectionService, projectService } = require('../../services');
const { ProjectSettings } = require('../../models');
const exportService = require('../../services/exportService');

// Mock dependencies
jest.mock('../../services', () => ({
  inspectionService: {
    newRequest: jest.fn(),
    editRequest: jest.fn(),
    listNDR: jest.fn(),
    getNDRData: jest.fn(),
    getMemberData: jest.fn(),
    updateNDRStatus: jest.fn(),
    updateDeliveredStatus: jest.fn(),
    bulkinspectionRequestUpload: jest.fn(),
    deleteQueuedNdr: jest.fn(),
    editMultipleinspectionRequest: jest.fn(),
    lastinspection: jest.fn(),
  },
  projectService: {
    getProjectDetails: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  ProjectSettings: {
    getCalendarStatusColor: jest.fn(),
    getCalendarCard: jest.fn(),
  },
}));

jest.mock('../../services/exportService', () => ({
  sampleinspectionRequestTemplate: jest.fn(),
}));

describe('inspectionController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
      end: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('newRequest', () => {
    it('should create inspection request successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      inspectionService.newRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.newRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from inspection request creation', async () => {
      const mockError = new Error('Service error');
      inspectionService.newRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.newRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in inspection request creation', async () => {
      const mockError = new Error('Exception error');
      inspectionService.newRequest.mockRejectedValue(mockError);

      await inspectionController.newRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.newRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editRequest', () => {
    it('should edit inspection request successfully', async () => {
      const mockResponse = { id: 1, data: 'updated' };
      inspectionService.editRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.editRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from inspection request edit', async () => {
      const mockError = new Error('Service error');
      inspectionService.editRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.editRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in inspection request edit', async () => {
      const mockError = new Error('Exception error');
      inspectionService.editRequest.mockRejectedValue(mockError);

      await inspectionController.editRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.editRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('listNDR', () => {
    it('should list NDR successfully with project data', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };
      const mockStatusData = { status: 'active' };
      const mockCardData = { card: 'data' };

      mockReq.params.ProjectId = '123';

      inspectionService.listNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusData);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardData);
      inspectionService.lastinspection.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await inspectionController.listNDR(mockReq, mockRes, mockNext);

      expect(inspectionService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('123');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('123');
      expect(inspectionService.lastinspection).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
        statusData: mockStatusData,
        cardData: mockCardData,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should list NDR successfully without project data', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };

      mockReq.params.ProjectId = '';

      inspectionService.listNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      inspectionService.lastinspection.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await inspectionController.listNDR(mockReq, mockRes, mockNext);

      expect(inspectionService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(ProjectSettings.getCalendarStatusColor).not.toHaveBeenCalled();
      expect(ProjectSettings.getCalendarCard).not.toHaveBeenCalled();
      expect(inspectionService.lastinspection).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
        statusData: undefined,
        cardData: undefined,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from NDR list', async () => {
      const mockError = new Error('Service error');
      inspectionService.listNDR.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.listNDR(mockReq, mockRes, mockNext);

      expect(inspectionService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from last inspection', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockError = new Error('Last inspection error');

      inspectionService.listNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      inspectionService.lastinspection.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.listNDR(mockReq, mockRes, mockNext);

      expect(inspectionService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(inspectionService.lastinspection).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in NDR list', async () => {
      const mockError = new Error('Exception error');
      inspectionService.listNDR.mockRejectedValue(mockError);

      await inspectionController.listNDR(mockReq, mockRes, mockNext);

      expect(inspectionService.listNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getNDRData', () => {
    it('should get NDR data successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      inspectionService.getNDRData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.getNDRData(mockReq, mockRes, mockNext);

      expect(inspectionService.getNDRData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from NDR data', async () => {
      const mockError = new Error('Service error');
      inspectionService.getNDRData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.getNDRData(mockReq, mockRes, mockNext);

      expect(inspectionService.getNDRData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in NDR data', async () => {
      const mockError = new Error('Exception error');
      inspectionService.getNDRData.mockRejectedValue(mockError);

      await inspectionController.getNDRData(mockReq, mockRes, mockNext);

      expect(inspectionService.getNDRData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getMemberData', () => {
    it('should get member data successfully', async () => {
      const mockResponse = { id: 1, data: 'member' };
      inspectionService.getMemberData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.getMemberData(mockReq, mockRes, mockNext);

      expect(inspectionService.getMemberData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Member listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from member data', async () => {
      const mockError = new Error('Service error');
      inspectionService.getMemberData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.getMemberData(mockReq, mockRes, mockNext);

      expect(inspectionService.getMemberData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in member data', async () => {
      const mockError = new Error('Exception error');
      inspectionService.getMemberData.mockRejectedValue(mockError);

      await inspectionController.getMemberData(mockReq, mockRes, mockNext);

      expect(inspectionService.getMemberData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateNDRStatus', () => {
    it('should update NDR status successfully', async () => {
      const mockResponse = { id: 1, status: 'completed' };
      mockReq.body.status = 'completed';

      inspectionService.updateNDRStatus.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.updateNDRStatus(mockReq, mockRes, mockNext);

      expect(inspectionService.updateNDRStatus).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'completed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from NDR status update', async () => {
      const mockError = new Error('Service error');
      inspectionService.updateNDRStatus.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.updateNDRStatus(mockReq, mockRes, mockNext);

      expect(inspectionService.updateNDRStatus).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in NDR status update', async () => {
      const mockError = new Error('Exception error');
      inspectionService.updateNDRStatus.mockRejectedValue(mockError);

      await inspectionController.updateNDRStatus(mockReq, mockRes, mockNext);

      expect(inspectionService.updateNDRStatus).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateDeliveredStatus', () => {
    it('should update delivered status successfully', async () => {
      const mockResponse = { id: 1, delivered: true };
      inspectionService.updateDeliveredStatus.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.updateDeliveredStatus(mockReq, mockRes, mockNext);

      expect(inspectionService.updateDeliveredStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delivered status update', async () => {
      const mockError = new Error('Service error');
      inspectionService.updateDeliveredStatus.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.updateDeliveredStatus(mockReq, mockRes, mockNext);

      expect(inspectionService.updateDeliveredStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delivered status update', async () => {
      const mockError = new Error('Exception error');
      inspectionService.updateDeliveredStatus.mockRejectedValue(mockError);

      await inspectionController.updateDeliveredStatus(mockReq, mockRes, mockNext);

      expect(inspectionService.updateDeliveredStatus).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('sampleBulkinspectionRequestTemplate', () => {
    it('should download sample bulk inspection request template successfully', async () => {
      const mockWorkbook = {
        xlsx: {
          write: jest.fn().mockResolvedValue(),
        },
      };
      const mockProjectDetail = { projectName: 'Test Project', id: 123 };

      mockReq.params.ProjectId = '123';
      mockReq.params.ParentCompanyId = '456';

      exportService.sampleinspectionRequestTemplate.mockResolvedValue(mockWorkbook);
      projectService.getProjectDetails.mockResolvedValue(mockProjectDetail);

      await inspectionController.sampleBulkinspectionRequestTemplate(mockReq, mockRes, mockNext);

      expect(mockReq.data).toEqual({
        ProjectId: '123',
        ParentCompanyId: '456',
      });
      expect(exportService.sampleinspectionRequestTemplate).toHaveBeenCalledWith(mockReq);
      expect(projectService.getProjectDetails).toHaveBeenCalledWith(mockReq);
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringContaining('Test Project_123_'),
      );
      expect(mockWorkbook.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle null workbook from export service', async () => {
      exportService.sampleinspectionRequestTemplate.mockResolvedValue(null);

      await inspectionController.sampleBulkinspectionRequestTemplate(mockReq, mockRes, mockNext);

      expect(exportService.sampleinspectionRequestTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'cannot export document', status: 422 });
      expect(mockRes.setHeader).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in sample template download', async () => {
      const mockError = new Error('Exception error');
      exportService.sampleinspectionRequestTemplate.mockRejectedValue(mockError);

      await inspectionController.sampleBulkinspectionRequestTemplate(mockReq, mockRes, mockNext);

      expect(exportService.sampleinspectionRequestTemplate).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('bulkUploadinspectionRequest', () => {
    it('should upload bulk inspection request successfully', async () => {
      const mockResponse = { data: 'uploaded' };
      inspectionService.bulkinspectionRequestUpload.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.bulkUploadinspectionRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.bulkinspectionRequestUpload).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Bookings uploaded successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from bulk upload', async () => {
      const mockError = new Error('Service error');
      inspectionService.bulkinspectionRequestUpload.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.bulkUploadinspectionRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.bulkinspectionRequestUpload).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in bulk upload', async () => {
      const mockError = new Error('Exception error');
      inspectionService.bulkinspectionRequestUpload.mockRejectedValue(mockError);

      await inspectionController.bulkUploadinspectionRequest(mockReq, mockRes, mockNext);

      expect(inspectionService.bulkinspectionRequestUpload).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('deleteQueuedNdr', () => {
    it('should delete queued NDR successfully', async () => {
      const mockResponse = { id: 1, deleted: true };
      inspectionService.deleteQueuedNdr.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await inspectionController.deleteQueuedNdr(mockReq, mockRes, mockNext);

      expect(inspectionService.deleteQueuedNdr).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Queued Inspection Booking Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from queued NDR deletion', async () => {
      const mockError = new Error('Service error');
      inspectionService.deleteQueuedNdr.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await inspectionController.deleteQueuedNdr(mockReq, mockRes, mockNext);

      expect(inspectionService.deleteQueuedNdr).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in queued NDR deletion', async () => {
      const mockError = new Error('Exception error');
      inspectionService.deleteQueuedNdr.mockRejectedValue(mockError);

      await inspectionController.deleteQueuedNdr(mockReq, mockRes, mockNext);

      expect(inspectionService.deleteQueuedNdr).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
