const carbonEmissionService = require('../carbonEmissionService');
const awsConfig = require('../../middlewares/awsConfig');
const xlsx = require('xlsx');
const path = require('path');
const { Sequelize, Utilities, co2EmissionPerUnits } = require('../../models');
const { Op, fn, literal } = require('sequelize');

// Mock all dependencies
jest.mock('../../middlewares/awsConfig');
jest.mock('xlsx');
jest.mock('path');

// Mock the models module
jest.mock('../../models', () => ({
    Sequelize: jest.fn(),
    Utilities: {
        findAll: jest.fn()
    },
    co2EmissionPerUnits: {
        findAll: jest.fn()
    }
}));

// Mock sequelize operators and functions
jest.mock('sequelize', () => {
    const mockSequelize = jest.fn().mockImplementation(() => ({
        showAllSchemas: jest.fn(),
        createSchema: jest.fn(),
        getQueryInterface: jest.fn()
    }));

    mockSequelize.Op = {
        ne: Symbol('ne')
    };
    mockSequelize.fn = jest.fn();
    mockSequelize.literal = jest.fn();
    mockSequelize.DataTypes = {
        STRING: 'STRING',
        INTEGER: 'INTEGER',
        BOOLEAN: 'BOOLEAN'
    };

    return mockSequelize;
});

describe('Carbon Emission Service', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('fileUpload', () => {
        it('should successfully upload file', (done) => {
            const mockInputData = { file: 'test.csv' };
            const mockResult = { success: true };

            awsConfig.carbonEmissionFileUpload.mockImplementation((input, callback) => {
                callback(mockResult, null);
            });

            carbonEmissionService.fileUpload(mockInputData, (result, err) => {
                expect(result).toEqual(mockResult);
                expect(err).toBeNull();
                expect(awsConfig.carbonEmissionFileUpload).toHaveBeenCalledWith(mockInputData, expect.any(Function));
                done();
            });
        });

        it('should handle file upload error', (done) => {
            const mockInputData = { file: 'test.csv' };
            const mockError = new Error('Upload failed');

            awsConfig.carbonEmissionFileUpload.mockImplementation((input, callback) => {
                callback(null, mockError);
            });

            carbonEmissionService.fileUpload(mockInputData, (result, err) => {
                expect(result).toBeNull();
                expect(err).toEqual(mockError);
                done();
            });
        });

        it('should handle unexpected errors', (done) => {
            const mockInputData = { file: 'test.csv' };
            const mockError = new Error('Unexpected error');

            awsConfig.carbonEmissionFileUpload.mockImplementation(() => {
                throw mockError;
            });

            carbonEmissionService.fileUpload(mockInputData, (result, err) => {
                expect(result).toBeNull();
                expect(err).toEqual(mockError);
                done();
            });
        });
    });

    describe('calculateCO2Emissions', () => {
        const mockZipSubregionData = [
            { zip: "12345", 'Subregion 1': 'TestRegion' }
        ];
        const mockCo2RatesData = [
            { 'Column1': 'ID', 'Column2': 'TestRegion', 'Column3': '500' }
        ];
        const mockGrossLossData = [
            { 'Column1': 'ID', 'Column2': 'TestRegion', 'Column3': 'Col3', 'Column4': 'Col4', 'Column5': 'Col5', 'Column6': 'Col6', 'Column7': 'Col7', 'Column8': 'Col8', 'Column9': '0.1' }
        ];

        beforeEach(() => {
            // Mock path.resolve
            path.resolve.mockReturnValue('/mock/path/file.xlsx');

            // Mock xlsx workbook and sheets
            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return mockZipSubregionData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return mockCo2RatesData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return mockGrossLossData;
                    return [];
                });
        });

        it('should calculate CO2 emissions successfully', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('12345', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500,
                totalCO2WoLineLoss: expect.any(Number),
                gridGrossLossFactor: "0.1",
                gridLossEmission: expect.any(Number),
                totalCO2Emission: expect.any(Number)
            }));
        });

        it('should handle invalid zip code', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('99999', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: null,
                co2PerMWh: null,
                totalCO2WoLineLoss: 0,
                gridGrossLossFactor: null,
                gridLossEmission: null,
                totalCO2Emission: 0
            }));
        });

        it('should handle zero energy consumption', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('12345', 0);

            expect(result.totalCO2WoLineLoss).toBe(0);
            expect(result.totalCO2Emission).toBe(0);
        });

        it('should handle negative energy consumption', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('12345', -50);

            expect(result.totalCO2WoLineLoss).toBeLessThan(0);
            expect(result.totalCO2Emission).toBeLessThan(0);
        });

        it('should handle string zip code input', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('12345', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500,
                totalCO2WoLineLoss: expect.any(Number),
                gridGrossLossFactor: "0.1",
                gridLossEmission: expect.any(Number),
                totalCO2Emission: expect.any(Number)
            }));
        });

        it('should handle numeric zip code input', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions(12345, 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500
            }));
        });

        it('should handle empty string zip code', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: null,
                co2PerMWh: null,
                totalCO2WoLineLoss: 0,
                gridGrossLossFactor: null,
                gridLossEmission: null,
                totalCO2Emission: 0
            }));
        });

        it('should handle null zip code', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions(null, 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: null,
                co2PerMWh: null,
                totalCO2WoLineLoss: 0,
                gridGrossLossFactor: null,
                gridLossEmission: null,
                totalCO2Emission: 0
            }));
        });

        it('should handle undefined energy consumption', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('12345', undefined);

            expect(result.totalCO2WoLineLoss).toBeNaN();
            expect(result.totalCO2Emission).toBeNaN();
        });

        it('should handle very large energy consumption values', async () => {
            const result = await carbonEmissionService.calculateCO2Emissions('12345', 999999);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500,
                totalCO2WoLineLoss: expect.any(Number),
                gridGrossLossFactor: "0.1",
                gridLossEmission: expect.any(Number),
                totalCO2Emission: expect.any(Number)
            }));
            expect(result.totalCO2WoLineLoss).toBeGreaterThan(0);
            expect(result.totalCO2Emission).toBeGreaterThan(0);
        });

        it('should handle missing co2 rates data', async () => {
            // Override mock to return empty co2 rates data
            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return mockZipSubregionData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return [];
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return mockGrossLossData;
                    return [];
                });

            const result = await carbonEmissionService.calculateCO2Emissions('12345', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: null,
                totalCO2WoLineLoss: 0,
                gridGrossLossFactor: "0.1",
                gridLossEmission: null,
                totalCO2Emission: 0
            }));
        });

        it('should handle missing gross loss data', async () => {
            // Override mock to return empty gross loss data
            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return mockZipSubregionData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return mockCo2RatesData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return [];
                    return [];
                });

            const result = await carbonEmissionService.calculateCO2Emissions('12345', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500,
                totalCO2WoLineLoss: expect.any(Number),
                gridGrossLossFactor: null,
                gridLossEmission: expect.any(Number),
                totalCO2Emission: expect.any(Number)
            }));
        });

        it('should handle malformed co2 rates data', async () => {
            const malformedCo2RatesData = [
                { 'InvalidKey': 'TestRegion', 'InvalidRate': 'invalid_number' }
            ];

            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return mockZipSubregionData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return malformedCo2RatesData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return mockGrossLossData;
                    return [];
                });

            const result = await carbonEmissionService.calculateCO2Emissions('12345', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: null,
                totalCO2WoLineLoss: 0,
                gridGrossLossFactor: "0.1",
                gridLossEmission: null,
                totalCO2Emission: 0
            }));
        });

        it('should handle Excel file reading errors', async () => {
            xlsx.readFile.mockImplementation(() => {
                throw new Error('File not found');
            });

            await expect(carbonEmissionService.calculateCO2Emissions('12345', 100))
                .rejects.toThrow('File not found');
        });

        it('should handle path resolution errors', async () => {
            path.resolve.mockImplementation(() => {
                throw new Error('Path resolution failed');
            });

            await expect(carbonEmissionService.calculateCO2Emissions('12345', 100))
                .rejects.toThrow('Path resolution failed');
        });

        it('should handle missing Excel sheets', async () => {
            const mockWorkbook = {
                Sheets: {}
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json.mockReturnValue([]);

            const result = await carbonEmissionService.calculateCO2Emissions('12345', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: null,
                co2PerMWh: null,
                totalCO2WoLineLoss: 0,
                gridGrossLossFactor: null,
                gridLossEmission: null,
                totalCO2Emission: 0
            }));
        });

        it('should handle xlsx.utils.sheet_to_json errors', async () => {
            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json.mockImplementation(() => {
                throw new Error('Sheet parsing failed');
            });

            await expect(carbonEmissionService.calculateCO2Emissions('12345', 100))
                .rejects.toThrow('Sheet parsing failed');
        });

        it('should handle complex zip code formats', async () => {
            const complexZipData = [
                { zip: '"12345-6789"', 'Subregion 1': 'ComplexRegion' }
            ];

            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return complexZipData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return mockCo2RatesData;
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return mockGrossLossData;
                    return [];
                });

            const result = await carbonEmissionService.calculateCO2Emissions('12345-6789', 100);

            expect(result).toEqual(expect.objectContaining({
                subregion: null, // Won't match because parseInt('12345-6789') !== '12345-6789'
                co2PerMWh: null,
                totalCO2WoLineLoss: 0
            }));
        });
    });

    describe('getEmissionValues', () => {
        it('should successfully fetch emission values', async () => {
            const mockEmissionValues = [
                { id: 1, value: 100 },
                { id: 2, value: 200 }
            ];

            co2EmissionPerUnits.findAll.mockResolvedValue(mockEmissionValues);

            const result = await carbonEmissionService.getEmissionValues();
            expect(result).toEqual(mockEmissionValues);
            expect(co2EmissionPerUnits.findAll).toHaveBeenCalled();
        });

        it('should handle database error', async () => {
            const mockError = new Error('Database error');
            co2EmissionPerUnits.findAll.mockRejectedValue(mockError);

            await expect(carbonEmissionService.getEmissionValues()).rejects.toThrow('Database error');
        });

        it('should handle empty emission values', async () => {
            co2EmissionPerUnits.findAll.mockResolvedValue([]);

            const result = await carbonEmissionService.getEmissionValues();
            expect(result).toEqual([]);
            expect(co2EmissionPerUnits.findAll).toHaveBeenCalled();
        });

        it('should handle null response from database', async () => {
            co2EmissionPerUnits.findAll.mockResolvedValue(null);

            const result = await carbonEmissionService.getEmissionValues();
            expect(result).toBeNull();
            expect(co2EmissionPerUnits.findAll).toHaveBeenCalled();
        });

        it('should handle connection timeout error', async () => {
            const timeoutError = new Error('Connection timeout');
            timeoutError.name = 'SequelizeConnectionError';
            co2EmissionPerUnits.findAll.mockRejectedValue(timeoutError);

            await expect(carbonEmissionService.getEmissionValues()).rejects.toThrow('Connection timeout');
        });

        it('should handle validation error', async () => {
            const validationError = new Error('Validation failed');
            validationError.name = 'SequelizeValidationError';
            co2EmissionPerUnits.findAll.mockRejectedValue(validationError);

            await expect(carbonEmissionService.getEmissionValues()).rejects.toThrow('Validation failed');
        });

        it('should handle large dataset', async () => {
            const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
                id: i + 1,
                utilityType: `Type${i}`,
                co2PerUnit: `${i * 10}`
            }));

            co2EmissionPerUnits.findAll.mockResolvedValue(largeDataset);

            const result = await carbonEmissionService.getEmissionValues();
            expect(result).toEqual(largeDataset);
            expect(result).toHaveLength(1000);
            expect(co2EmissionPerUnits.findAll).toHaveBeenCalled();
        });
    });

    describe('getDashboardData', () => {
        const mockPayload = { ProjectId: 123 };
        const mockUtilitiesData = [
            {
                utilityType: 'Electricity',
                totalQuantity: 1000,
                totalCost: 5000,
                totalEmissions: 2000
            }
        ];

        beforeEach(() => {
            // Mock sequelize operators and functions for each test
            fn.mockReturnValue('SUM_FUNCTION');
            literal.mockReturnValue('LITERAL_EXPRESSION');
        });

        it('should successfully fetch dashboard data', (done) => {
            Utilities.findAll.mockResolvedValue(mockUtilitiesData);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toEqual(mockUtilitiesData);
                expect(err).toBeNull();
                expect(Utilities.findAll).toHaveBeenCalledWith(expect.objectContaining({
                    where: {
                        projectId: mockPayload.ProjectId,
                        parentId: { [Op.ne]: null }
                    },
                    attributes: expect.arrayContaining([
                        'utilityType',
                        expect.any(Array),
                        expect.any(Array),
                        expect.any(Array)
                    ]),
                    group: ['utilityType']
                }));
                done();
            });
        });

        it('should handle database error in dashboard data', (done) => {
            const mockError = new Error('Database error');
            Utilities.findAll.mockRejectedValue(mockError);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toBeUndefined();
                expect(err).toEqual(mockError);
                done();
            });
        });

        it('should handle empty project data', (done) => {
            Utilities.findAll.mockResolvedValue([]);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toEqual([]);
                expect(err).toBeNull();
                done();
            });
        });

        it('should handle null project data', (done) => {
            Utilities.findAll.mockResolvedValue(null);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toBeNull();
                expect(err).toBeNull();
                done();
            });
        });

        it('should handle missing ProjectId in payload', (done) => {
            const invalidPayload = {};
            Utilities.findAll.mockResolvedValue([]);

            carbonEmissionService.getDashboardData(invalidPayload, (result, err) => {
                expect(result).toEqual([]);
                expect(err).toBeNull();
                expect(Utilities.findAll).toHaveBeenCalledWith(expect.objectContaining({
                    where: {
                        projectId: undefined,
                        parentId: { [Op.ne]: null }
                    }
                }));
                done();
            });
        });

        it('should handle null payload', (done) => {
            const nullPayload = null;

            // This should cause an error when trying to access payload.ProjectId
            carbonEmissionService.getDashboardData(nullPayload, (result, err) => {
                expect(result).toBeUndefined();
                expect(err).toBeDefined();
                expect(err.message).toContain("Cannot read");
                done();
            });
        });

        it('should handle connection timeout error', (done) => {
            const timeoutError = new Error('Connection timeout');
            timeoutError.name = 'SequelizeConnectionError';
            Utilities.findAll.mockRejectedValue(timeoutError);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toBeUndefined();
                expect(err).toEqual(timeoutError);
                done();
            });
        });

        it('should handle multiple utility types', (done) => {
            const multipleUtilitiesData = [
                {
                    utilityType: 'Electricity',
                    totalQuantity: 1000,
                    totalCost: 5000,
                    totalEmissions: 2000
                },
                {
                    utilityType: 'Gas',
                    totalQuantity: 500,
                    totalCost: 2500,
                    totalEmissions: 1000
                },
                {
                    utilityType: 'Water',
                    totalQuantity: 200,
                    totalCost: 800,
                    totalEmissions: 100
                }
            ];

            Utilities.findAll.mockResolvedValue(multipleUtilitiesData);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toEqual(multipleUtilitiesData);
                expect(result).toHaveLength(3);
                expect(err).toBeNull();
                done();
            });
        });

        it('should handle zero values in utilities data', (done) => {
            const zeroValuesData = [
                {
                    utilityType: 'Electricity',
                    totalQuantity: 0,
                    totalCost: 0,
                    totalEmissions: 0
                }
            ];

            Utilities.findAll.mockResolvedValue(zeroValuesData);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toEqual(zeroValuesData);
                expect(err).toBeNull();
                done();
            });
        });

        it('should handle negative values in utilities data', (done) => {
            const negativeValuesData = [
                {
                    utilityType: 'Electricity',
                    totalQuantity: -100,
                    totalCost: -500,
                    totalEmissions: -200
                }
            ];

            Utilities.findAll.mockResolvedValue(negativeValuesData);

            carbonEmissionService.getDashboardData(mockPayload, (result, err) => {
                expect(result).toEqual(negativeValuesData);
                expect(err).toBeNull();
                done();
            });
        });

        it('should handle very large ProjectId', (done) => {
            const largeProjectPayload = { ProjectId: 999999999999 };
            Utilities.findAll.mockResolvedValue(mockUtilitiesData);

            carbonEmissionService.getDashboardData(largeProjectPayload, (result, err) => {
                expect(result).toEqual(mockUtilitiesData);
                expect(err).toBeNull();
                expect(Utilities.findAll).toHaveBeenCalledWith(expect.objectContaining({
                    where: {
                        projectId: 999999999999,
                        parentId: { [Op.ne]: null }
                    }
                }));
                done();
            });
        });

        it('should handle string ProjectId', (done) => {
            const stringProjectPayload = { ProjectId: "123" };
            Utilities.findAll.mockResolvedValue(mockUtilitiesData);

            carbonEmissionService.getDashboardData(stringProjectPayload, (result, err) => {
                expect(result).toEqual(mockUtilitiesData);
                expect(err).toBeNull();
                expect(Utilities.findAll).toHaveBeenCalledWith(expect.objectContaining({
                    where: {
                        projectId: "123",
                        parentId: { [Op.ne]: null }
                    }
                }));
                done();
            });
        });
    });

    // Additional integration-style tests
    describe('Integration Tests', () => {
        it('should handle fileUpload with valid callback', () => {
            const mockInputData = { file: 'test.csv' };
            const mockCallback = jest.fn();

            awsConfig.carbonEmissionFileUpload.mockImplementation((_, callback) => {
                // Simulate AWS service calling the callback
                callback({ success: true }, null);
            });

            // Test with valid callback
            carbonEmissionService.fileUpload(mockInputData, mockCallback);

            expect(awsConfig.carbonEmissionFileUpload).toHaveBeenCalledWith(mockInputData, expect.any(Function));
            expect(mockCallback).toHaveBeenCalledWith({ success: true }, null);
        });

        it('should handle calculateCO2Emissions with extreme values', async () => {
            // Reset mock data for this test
            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return [{ zip: "12345", 'Subregion 1': 'TestRegion' }];
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return [{ 'Column1': 'ID', 'Column2': 'TestRegion', 'Column3': '500' }];
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return [{ 'Column1': 'ID', 'Column2': 'TestRegion', 'Column3': 'Col3', 'Column4': 'Col4', 'Column5': 'Col5', 'Column6': 'Col6', 'Column7': 'Col7', 'Column8': 'Col8', 'Column9': '0.1' }];
                    return [];
                });

            const result = await carbonEmissionService.calculateCO2Emissions('12345', Number.MAX_SAFE_INTEGER);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500,
                totalCO2WoLineLoss: expect.any(Number),
                gridGrossLossFactor: "0.1",
                gridLossEmission: expect.any(Number),
                totalCO2Emission: expect.any(Number)
            }));
            expect(result.totalCO2WoLineLoss).toBeGreaterThan(0);
            expect(result.totalCO2Emission).toBeGreaterThan(0);
        });

        it('should handle calculateCO2Emissions with minimum safe integer', async () => {
            // Reset mock data for this test
            const mockWorkbook = {
                Sheets: {
                    'Zip-subregion': {},
                    'Subregion Rates (kg-MWh)': {},
                    'Subregion Rates (lbs-MWh)': {}
                }
            };
            xlsx.readFile.mockReturnValue(mockWorkbook);
            xlsx.utils.sheet_to_json
                .mockImplementation((sheet) => {
                    if (sheet === mockWorkbook.Sheets['Zip-subregion']) return [{ zip: "12345", 'Subregion 1': 'TestRegion' }];
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (kg-MWh)']) return [{ 'Column1': 'ID', 'Column2': 'TestRegion', 'Column3': '500' }];
                    if (sheet === mockWorkbook.Sheets['Subregion Rates (lbs-MWh)']) return [{ 'Column1': 'ID', 'Column2': 'TestRegion', 'Column3': 'Col3', 'Column4': 'Col4', 'Column5': 'Col5', 'Column6': 'Col6', 'Column7': 'Col7', 'Column8': 'Col8', 'Column9': '0.1' }];
                    return [];
                });

            const result = await carbonEmissionService.calculateCO2Emissions('12345', Number.MIN_SAFE_INTEGER);

            expect(result).toEqual(expect.objectContaining({
                subregion: 'TestRegion',
                co2PerMWh: 500,
                totalCO2WoLineLoss: expect.any(Number),
                gridGrossLossFactor: "0.1",
                gridLossEmission: expect.any(Number),
                totalCO2Emission: expect.any(Number)
            }));
            expect(result.totalCO2WoLineLoss).toBeLessThan(0);
            expect(result.totalCO2Emission).toBeLessThan(0);
        });
    });
});