const moment = require('moment');
const fs = require('fs');
const awsConfig = require('../middlewares/awsConfig');
const { Project, Company } = require('../models');
const puppeteerService = require('./puppeteerService');

const pdfCraneReportService = {
  extractCraneHeaderSelection(req) {
    const header = [];
    const flags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isEquipmentSelected: false,
      isDfowSelected: false,
      isGateSelected: false,
      isCompanySelected: false,
      isPersonSelected: false,
      isPickingFromSelected: false,
      isPickingToSelected: false,
      isLocationSelected: false
    };

    req.body.selectedHeaders.forEach(item => {
      if (item.isActive) {
        const key = item.key === 'name' ? 'Person' : item.key.charAt(0).toUpperCase() + item.key.slice(1);
        flags[`is${key}Selected`] = true;
        header.push(`<th style="text-align:center">${item.title}</th>`);
      }
    });

    return { flags, header };
  },
  buildCraneRows(data, flags, timezoneoffset) {
    const rows = [];

    const td = (val) => `<td style="color: #5B5B5B; font-weight: 600; text-align: center; font-size: 12px; font-family: 'Cairo', sans-serif">${val}</td>`;
    const wrapList = (list) => list.map(val => `<p>${val}</p>`).join('') || '-';

    const getDateRange = (item, timezoneoffset) => {
      const isCraneRequest = item.requestType === 'craneRequest';
      const startField = isCraneRequest ? 'craneDeliveryStart' : 'deliveryStart';
      const endField = isCraneRequest ? 'craneDeliveryEnd' : 'deliveryEnd';

      return {
        start: moment(item[startField]).add(Number(timezoneoffset), 'minutes'),
        end: moment(item[endField]).add(Number(timezoneoffset), 'minutes')
      };
    };

    const getPickingLocation = (item, isFrom) => {
      const isCraneRequest = item.requestType === 'craneRequest';
      if (isFrom) {
        return isCraneRequest ? (item.pickUpLocation || '-') : (item.cranePickUpLocation || '-');
      }
      return isCraneRequest ? (item.dropOffLocation || '-') : (item.craneDropOffLocation || '-');
    };

    const getApproverName = (approverDetails) => {
      return approverDetails ? `${approverDetails.User.firstName} ${approverDetails.User.lastName}` : '-';
    };

    const getEquipmentNames = (equipmentDetails) => {
      return wrapList((equipmentDetails || [])
        .filter(e => e.Equipment?.PresetEquipmentType?.isCraneType)
        .map(e => e.Equipment.equipmentName));
    };

    const getDfowList = (defineWorkDetails) => {
      return wrapList((defineWorkDetails || []).map(d => d.DeliverDefineWork.DFOW));
    };

    const getGateName = (gateDetails) => {
      return gateDetails?.[0]?.Gate?.gateName || '-';
    };

    const getCompanyNames = (companyDetails) => {
      return wrapList((companyDetails || []).map(c => c.Company.companyName));
    };

    const getMemberNames = (memberDetails) => {
      return wrapList((memberDetails || []).map(m => `${m.Member.User.firstName} ${m.Member.User.lastName}`));
    };

    data.forEach(item => {
      const date = getDateRange(item, timezoneoffset);
      const pickingFromValue = getPickingLocation(item, true);
      const pickingToValue = getPickingLocation(item, false);

      const row = [
        flags.isIdSelected ? td(item.CraneRequestId) : '',
        flags.isDescriptionSelected ? td(item.description) : '',
        flags.isDateSelected ? td(`${date.start.format('MM/DD/YYYY hh:mm a')} - ${date.end.format('hh:mm a')}`) : '',
        flags.isStatusSelected ? td(item.status) : '',
        flags.isApprovedBySelected ? td(getApproverName(item.approverDetails)) : '',
        flags.isEquipmentSelected ? td(getEquipmentNames(item.equipmentDetails)) : '',
        flags.isDfowSelected ? td(getDfowList(item.defineWorkDetails)) : '',
        flags.isGateSelected ? td(getGateName(item.gateDetails)) : '',
        flags.isCompanySelected ? td(getCompanyNames(item.companyDetails)) : '',
        flags.isPersonSelected ? td(getMemberNames(item.memberDetails)) : '',
        flags.isPickingFromSelected ? td(pickingFromValue) : '',
        flags.isPickingToSelected ? td(pickingToValue) : '',
        flags.isLocationSelected ? td(item.location?.locationPath || '-') : ''
      ].join('');

      rows.push(`<tr style="border-bottom: 1px solid #e0e0e0; font-size: 12px">${row}</tr>`);
    });

    return rows;
  },
  generateCranePdfTemplate(templatePath, projectData, companyData, loginUser, req, header, content) {
    let template = fs.readFileSync(templatePath, 'utf-8');

    return template
      .replace('$projectName', projectData.projectName)
      .replace('$companyName', companyData.companyName)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName}`)
      .replace('$reportType', 'Crane')
      .replace('$header', header.join(''))
      .replace('$data', content.join(''))
      .replace(/,/g, '');
  },
  async pdfFormatOfCraneRequest(params, loginUser, data, req, done) {
    try {
      const { timezoneoffset } = req.headers;
      const { flags, header } = this.extractCraneHeaderSelection(req);

      const projectData = await Project.findOne({
        where: { isDeleted: false, id: +params.ProjectId },
        attributes: ['projectName']
      });

      const companyData = await Company.findOne({
        where: { isDeleted: false, ParentCompanyId: +req.body.ParentCompanyId, isParent: true },
        attributes: ['companyName']
      });

      const rows = this.buildCraneRows(data, flags, timezoneoffset);
      const templatePath = '/usr/src/web/api/v21/views/mail-templates/deliveryReport.html';
      const finalHtml = this.generateCranePdfTemplate(templatePath, projectData, companyData, loginUser, req, header, rows);

      const pdfBuffer = await puppeteerService.generatePdfBuffer(finalHtml);
      if (pdfBuffer) {
        awsConfig.reportUpload(pdfBuffer, req.body.reportName, req.body.exportType, (result, error) => {
          if (!error) done(result, false);
          else done(false, { message: 'Upload failed' });
        });
      } else {
        done(false, { message: 'Cannot export the document' });
      }
    } catch (err) {
      done(false, { message: 'Unexpected error during PDF generation', error: err.message });
    }
  }

};
module.exports = pdfCraneReportService;
