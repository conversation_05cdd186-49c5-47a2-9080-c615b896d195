/* eslint-disable prettier/prettier */
const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../middlewares/awsConfig');

// Helper functions for repeated logic
function formatDateRange(start, end, offset) {
    const s = moment(start).add(Number(offset), 'minutes');
    const e = moment(end).add(Number(offset), 'minutes');
    return `${s.format('MMM-DD-YYYY hh:mm a')} - ${e.format('hh:mm a')}`;
}
function joinNames(arr, getName) {
    return (arr?.map(getName).filter(Boolean) || []).join(', ') || '-';
}
function getFullName(user) {
    return user?.firstName ? `${user.firstName} ${user.lastName}`.trim() : null;
}

const csvInspectionReportService = {
    async exportInspectionReportInCsvFormat(data, selectedHeaders, timezoneoffset, fileName, exportType, done) {
        const { selectedFlags } = this.extractInspectionHeaders(selectedHeaders);
        const values = this.constructInspectionCsvRows(data, selectedFlags, timezoneoffset);
        const csvFile = await this.generateCsvFile(values);

        if (csvFile) {
            const buffer = Buffer.from(csvFile, 'utf-8');
            awsConfig.reportUpload(buffer, fileName, exportType, async (result, error1) => {
            if (!error1) {
                return done(result, false);
            }
            return done(null, { message: 'cannot export document' });
            });
        }
    },
    extractInspectionHeaders(selectedHeaders) {
        const rowValues = [];
        const columns = [];
        const selectedFlags = {
            isIdSelected: false,
            isDescriptionSelected: false,
            isDateSelected: false,
            isStatusSelected: false,
            isinspectionStatusSelected: false,
            isinspectionTypeSelected: false,
            isApprovedBySelected: false,
            isEquipmentSelected: false,
            isDfowSelected: false,
            isGateSelected: false,
            isCompanySelected: false,
            isPersonSelected: false,
            isLocationSelected: false,
        };

        const keyMap = {
            id: 'isIdSelected',
            description: 'isDescriptionSelected',
            date: 'isDateSelected',
            status: 'isStatusSelected',
            inspectionStatus: 'isinspectionStatusSelected',
            inspectionType: 'isinspectionTypeSelected',
            approvedby: 'isApprovedBySelected',
            equipment: 'isEquipmentSelected',
            dfow: 'isDfowSelected',
            gate: 'isGateSelected',
            company: 'isCompanySelected',
            name: 'isPersonSelected',
            location: 'isLocationSelected',
        };

        selectedHeaders.forEach((object) => {
            if (object.isActive) {
            rowValues.push(object.title);
            columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });
            const flag = keyMap[object.key];
            if (flag) selectedFlags[flag] = true;
            }
        });

        return { rowValues, columns, selectedFlags };
    },
    constructInspectionCsvRows(data, flags, timezoneoffset) {
        const config = [
            { flag: 'isIdSelected', key: 'Id', get: item => item.InspectionId },
            { flag: 'isDescriptionSelected', key: 'Description', get: item => item.description },
            { flag: 'isDateSelected', key: 'Date & Time', get: item => formatDateRange(item.deliveryStart, item.deliveryEnd, timezoneoffset) },
            { flag: 'isStatusSelected', key: 'Status', get: item => item.status === 'Delivered' ? 'Completed' : item.status },
            { flag: 'isinspectionStatusSelected', key: 'Inspection Status', get: item => item.inspectionStatus ?? '-' },
            { flag: 'isinspectionTypeSelected', key: 'Inspection Type', get: item => item.inspectionType },
            { flag: 'isApprovedBySelected', key: 'Approved By', get: item => getFullName(item.approverDetails?.User) || '-' },
            { flag: 'isEquipmentSelected', key: 'Equipment', get: item => joinNames(item.equipmentDetails, e => e.Equipment?.equipmentName) },
            { flag: 'isDfowSelected', key: 'Definable Feature of Work', get: item => joinNames(item.defineWorkDetails, d => d.DeliverDefineWork?.DFOW) },
            { flag: 'isGateSelected', key: 'Gate', get: item => item.gateDetails?.[0]?.Gate?.gateName || '-' },
            { flag: 'isCompanySelected', key: 'Responsible Company', get: item => joinNames(item.companyDetails, c => c.Company?.companyName) },
            { flag: 'isPersonSelected', key: 'Responsible Person', get: item => joinNames(item.memberDetails, m => getFullName(m.Member?.User)) },
            { flag: 'isLocationSelected', key: 'Location', get: item => item.location?.locationPath || '-' },
        ];

        const values = data.map(item => {
            const row = {};
            for (const { flag, key, get } of config) {
                if (flags[flag]) {
                    row[key] = get(item);
                }
            }
            return row;
        });
        return values;
    },
    async generateCsvFile(values) {
        const options = {
            showLabels: true,
            showTitle: false,
            useTextFile: false,
            useBom: false,
            useKeysAsHeaders: true,
        };

        const csvExporter = new ExportToCsv(options);
        return await csvExporter.generateCsv(values, true);
    },

};
module.exports = csvInspectionReportService;
