const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  addressValidation: {
    getState: jest.fn(),
    getCity: jest.fn(),
  },
}));

jest.mock('../../controllers', () => ({
  AddressController: {
    getCountry: jest.fn(),
    getState: jest.fn(),
    getCity: jest.fn(),
  },
}));

describe('addressRoute', () => {
  let router;
  let addressRoute;
  let AddressController;
  let addressValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    addressRoute = require('../addressRoute');
    const controllers = require('../../controllers');
    AddressController = controllers.AddressController;
    const validations = require('../../middlewares/validations');
    addressValidation = validations.addressValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = addressRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered
      expect(router.get).toHaveBeenCalledWith('/get_country', AddressController.getCountry);
      expect(router.get).toHaveBeenCalledWith(
        '/get_state/:CountryId',
        'mocked-validate-middleware',
        AddressController.getState,
      );
      expect(router.get).toHaveBeenCalledWith(
        '/get_city/:StateId',
        'mocked-validate-middleware',
        AddressController.getCity,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = addressRoute.router;
      const result2 = addressRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof addressRoute).toBe('object');
      expect(addressRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(addressRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(addressRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });

  describe('routes', () => {
    it('should register GET /get_country route without validation', () => {
      addressRoute.router;

      expect(router.get).toHaveBeenCalledWith('/get_country', AddressController.getCountry);
    });

    it('should register GET /get_state/:CountryId route with validation', () => {
      addressRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_state/:CountryId',
        'mocked-validate-middleware',
        AddressController.getState,
      );
    });

    it('should register GET /get_city/:StateId route with validation', () => {
      addressRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_city/:StateId',
        'mocked-validate-middleware',
        AddressController.getCity,
      );
    });
  });
});
