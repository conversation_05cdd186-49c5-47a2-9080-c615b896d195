const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../controllers', () => ({
  AuthController: {
    register: jest.fn(),
    login: jest.fn(),
    existEmail: jest.fn(),
    removeFromInvitedProject: jest.fn(),
    getResendInviteLink: jest.fn(),
    checkResetToken: jest.fn(),
    forgotPassword: jest.fn(),
    resetPasswordByEmail: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  userValidation: {
    register: jest.fn(),
    login: jest.fn(),
    existEmail: jest.fn(),
    email: jest.fn(),
    checkResetToken: jest.fn(),
    forgotPassword: jest.fn(),
    resetPasswordEmail: jest.fn(),
  },
}));

describe('authRoute', () => {
  let router;
  let authRoute;
  let AuthController;
  let userValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    authRoute = require('../authRoute');
    const controllers = require('../../controllers');
    AuthController = controllers.AuthController;
    const validations = require('../../middlewares/validations');
    userValidation = validations.userValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = authRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(7);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST routes
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/register',
        'mocked-validate-middleware',
        AuthController.register,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/login',
        'mocked-validate-middleware',
        AuthController.login,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/exist_user',
        'mocked-validate-middleware',
        AuthController.existEmail,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/remove_invited_member',
        'mocked-validate-middleware',
        AuthController.removeFromInvitedProject,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/request_invited_link',
        'mocked-validate-middleware',
        AuthController.getResendInviteLink,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/forgot_password',
        'mocked-validate-middleware',
        AuthController.forgotPassword,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        7,
        '/reset_password_email/:reset_password_token',
        'mocked-validate-middleware',
        AuthController.resetPasswordByEmail,
      );

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/check_reset_token/:resetToken',
        'mocked-validate-middleware',
        AuthController.checkResetToken,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(8);
      expect(validate).toHaveBeenCalledWith(
        userValidation.register,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        userValidation.login,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        userValidation.existEmail,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        userValidation.email,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        userValidation.checkResetToken,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        userValidation.forgotPassword,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        userValidation.resetPasswordEmail,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = authRoute.router;
      const result2 = authRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes in the correct order', () => {
      authRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      expect(postCalls[0][0]).toBe('/register');
      expect(postCalls[1][0]).toBe('/login');
      expect(postCalls[2][0]).toBe('/exist_user');
      expect(postCalls[3][0]).toBe('/remove_invited_member');
      expect(postCalls[4][0]).toBe('/request_invited_link');
      expect(postCalls[5][0]).toBe('/forgot_password');
      expect(postCalls[6][0]).toBe('/reset_password_email/:reset_password_token');

      expect(getCalls[0][0]).toBe('/check_reset_token/:resetToken');
    });

    it('should use validation middleware for all routes', () => {
      authRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All POST routes should have validation middleware
      postCalls.forEach(call => {
        expect(call).toHaveLength(3); // path + validation + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // GET route should also have validation middleware
      getCalls.forEach(call => {
        expect(call).toHaveLength(3); // path + validation + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof authRoute).toBe('object');
      expect(authRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(authRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(authRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
