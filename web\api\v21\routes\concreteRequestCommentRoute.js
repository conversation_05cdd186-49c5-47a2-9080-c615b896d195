const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { concreteRequestCommentController } = require('../controllers');
const { concreteRequestCommentValidation } = require('../middlewares/validations');
const cacheMiddleware = require('../middlewares/cacheMiddleware');

const concreteRequestCommentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_concrete_request_comment',
      validate(
        concreteRequestCommentValidation.createConcreteRequestComment,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterConcreteRequestMutation(),
      concreteRequestCommentController.createConcreteRequestComment,
    );
    router.get(
      '/get_concrete_request_comments/:ConcreteRequestId/:ParentCompanyId/:ProjectId',
      validate(
        concreteRequestCommentValidation.getConcreteRequestComments,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheConcreteRequestComments(),
      concreteRequestCommentController.getConcreteRequestComments1,
    );
    return router;
  },
};
module.exports = concreteRequestCommentRoute;
