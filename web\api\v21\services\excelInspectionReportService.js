/* eslint-disable prettier/prettier */
const moment = require('moment');

const excelInspectionReportService = {
    async inspectionReport(workbook, responseData, selectedHeaders, timezoneoffset) {
        const worksheet = workbook.addWorksheet('Inspection Report');

        const {
            rowValues,
            columns,
            selectedFlags
        } = this.prepareWorksheetMetadata(selectedHeaders);

        worksheet.getRow(1).values = rowValues;
        worksheet.columns = columns;

        const cellRange = this.getCellRange();
        this.populateWorksheetRows(worksheet, rowValues, responseData, selectedFlags, cellRange, timezoneoffset);

        return workbook;
    },
    prepareWorksheetMetadata(selectedHeaders) {
        const rowValues = [];
        const columns = [];
        const selectedFlags = {
            isIdSelected: false,
            isDescriptionSelected: false,
            isDateSelected: false,
            isStatusSelected: false,
            isInspectionStatusSelected: false,
            isInspectionTypeSelected: false,
            isApprovedBySelected: false,
            isEquipmentSelected: false,
            isDfowSelected: false,
            isGateSelected: false,
            isCompanySelected: false,
            isPersonSelected: false,
            isLocationSelected: false
        };

        selectedHeaders.forEach((object) => {
            if (!object.isActive) return;

            rowValues.push(object.title);
            columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });

            switch (object.key) {
                case 'id': selectedFlags.isIdSelected = true; break;
                case 'description': selectedFlags.isDescriptionSelected = true; break;
                case 'date': selectedFlags.isDateSelected = true; break;
                case 'status': selectedFlags.isStatusSelected = true; break;
                case 'inspectionStatus': selectedFlags.isInspectionStatusSelected = true; break;
                case 'inspectionType': selectedFlags.isInspectionTypeSelected = true; break;
                case 'approvedby': selectedFlags.isApprovedBySelected = true; break;
                case 'equipment': selectedFlags.isEquipmentSelected = true; break;
                case 'dfow': selectedFlags.isDfowSelected = true; break;
                case 'gate': selectedFlags.isGateSelected = true; break;
                case 'company': selectedFlags.isCompanySelected = true; break;
                case 'name': selectedFlags.isPersonSelected = true; break;
                case 'location': selectedFlags.isLocationSelected = true; break;
            }
        });

        return { rowValues, columns, selectedFlags };
    },
    getCellRange() {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const range = {};
        for (let i = 0; i < 26; i++) {
            range[i] = alphabet[i];
        }
        return range;
    },

    populateWorksheetRows(worksheet, rowValues, responseData, flags, cellRange, timezoneoffset) {
        for (let index = 1; index <= responseData.length; index++) {
            const data = responseData[index - 1];
            worksheet.addRow();

            const cell = (title) => cellRange[rowValues.indexOf(title)] + (index + 1);

            this.populateBasicFields(worksheet, cell, data, flags, timezoneoffset);
            this.populateMultiValueFields(worksheet, cell, data, flags);
            this.populateNestedFields(worksheet, cell, data, flags);
        }
    },
    populateBasicFields(worksheet, cell, data, flags, timezoneoffset) {
        if (flags.isIdSelected) worksheet.getCell(cell('Id')).value = data.InspectionId;
        if (flags.isDescriptionSelected) worksheet.getCell(cell('Description')).value = data.description;

        if (flags.isDateSelected) {
            const start = moment(data.inspectionStart).add(Number(timezoneoffset), 'minutes');
            const end = moment(data.inspectionEnd).add(Number(timezoneoffset), 'minutes');
            worksheet.getCell(cell('Date & Time')).value = `${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
        }

        if (flags.isStatusSelected) {
            worksheet.getCell(cell('Status')).value = data.status === 'Delivered' ? 'Completed' : data.status;
        }

        if (flags.isInspectionStatusSelected) {
            worksheet.getCell(cell('Inspection Status')).value = data.inspectionStatus || '-';
        }

        if (flags.isInspectionTypeSelected) {
            worksheet.getCell(cell('Inspection Type')).value = data.inspectionType;
        }
    },
    populateMultiValueFields(worksheet, cell, data, flags) {
        if (flags.isEquipmentSelected) {
            const equipment = (data.equipmentDetails || [])
                .map(e => e.Equipment?.equipmentName)
                .filter(Boolean)
                .join(', ');
            worksheet.getCell(cell('Equipment')).value = equipment || '-';
        }

        if (flags.isDfowSelected) {
            const dfow = (data.defineWorkDetails || [])
                .map(d => d.DeliverDefineWork?.DFOW)
                .filter(Boolean)
                .join(', ');
            worksheet.getCell(cell('Definable Feature of Work')).value = dfow || '-';
        }

        if (flags.isCompanySelected) {
            const companies = (data.companyDetails || [])
                .map(c => c.Company?.companyName)
                .filter(Boolean)
                .join(', ');
            worksheet.getCell(cell('Responsible Company')).value = companies || '-';
        }
    },

    populateNestedFields(worksheet, cell, data, flags) {
        if (flags.isApprovedBySelected) {
            const user = data.approverDetails?.User;
            worksheet.getCell(cell('Approved By')).value = user
                ? `${user.firstName} ${user.lastName}`
                : '-';
        }

        if (flags.isPersonSelected) {
            const members = (data.memberDetails || [])
                .map(m => m.Member?.User ? `${m.Member.User.firstName} ${m.Member.User.lastName}` : null)
                .filter(Boolean)
                .join(', ');
            worksheet.getCell(cell('Responsible Person')).value = members || '-';
        }

        if (flags.isGateSelected) {
            worksheet.getCell(cell('Gate')).value = data.gateDetails?.[0]?.Gate?.gateName || '-';
        }

        if (flags.isLocationSelected) {
            worksheet.getCell(cell('Location')).value = data.location?.locationPath || '-';
        }
    }


};
module.exports = excelInspectionReportService;
