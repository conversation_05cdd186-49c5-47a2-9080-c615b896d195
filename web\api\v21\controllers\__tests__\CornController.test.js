const CornController = require('../CornController');
const { cornService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  cornService: {
    checkOverDue: jest.fn(),
    checkNDRExpiration: jest.fn(),
    checkDailyDigestEmailNotification: jest.fn(),
    schedulerReportRequest: jest.fn(),
    getSchedulerReportRequest: jest.fn(),
    getRerunReportRequest: jest.fn(),
    getSchedulerTimelineNames: jest.fn(),
    runtimeScheduler: jest.fn(),
  },
}));

describe('CornController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {};
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('checkOverDue', () => {
    it('should call cornService.checkOverDue successfully', async () => {
      cornService.checkOverDue.mockResolvedValue();

      await CornController.checkOverDue();

      expect(cornService.checkOverDue).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from cornService.checkOverDue', async () => {
      const error = new Error('Service error');
      cornService.checkOverDue.mockRejectedValue(error);

      await expect(CornController.checkOverDue()).rejects.toThrow('Service error');
    });
  });

  describe('checkNDRExpiration', () => {
    it('should call cornService.checkNDRExpiration successfully', async () => {
      cornService.checkNDRExpiration.mockResolvedValue();

      await CornController.checkNDRExpiration();

      expect(cornService.checkNDRExpiration).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from cornService.checkNDRExpiration', async () => {
      const error = new Error('Service error');
      cornService.checkNDRExpiration.mockRejectedValue(error);

      await expect(CornController.checkNDRExpiration()).rejects.toThrow('Service error');
    });
  });

  describe('checkDailyDigestEmailNotification', () => {
    it('should call cornService.checkDailyDigestEmailNotification successfully', async () => {
      cornService.checkDailyDigestEmailNotification.mockResolvedValue();

      await CornController.checkDailyDigestEmailNotification();

      expect(cornService.checkDailyDigestEmailNotification).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from cornService.checkDailyDigestEmailNotification', async () => {
      const error = new Error('Service error');
      cornService.checkDailyDigestEmailNotification.mockRejectedValue(error);

      await expect(CornController.checkDailyDigestEmailNotification()).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('schedulerReportRequest', () => {
    it('should handle successful scheduler report request', async () => {
      const mockResponse = { data: 'test' };
      cornService.schedulerReportRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CornController.schedulerReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.schedulerReportRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from scheduler report request', async () => {
      const mockError = new Error('Service error');
      cornService.schedulerReportRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CornController.schedulerReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.schedulerReportRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in scheduler report request', async () => {
      const mockError = new Error('Exception error');
      cornService.schedulerReportRequest.mockRejectedValue(mockError);

      await CornController.schedulerReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.schedulerReportRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getSchedulerReportRequest', () => {
    it('should handle successful get scheduler report request', async () => {
      const mockResponse = { data: 'test' };
      cornService.getSchedulerReportRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CornController.getSchedulerReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.getSchedulerReportRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get scheduler report request', async () => {
      const mockError = new Error('Service error');
      cornService.getSchedulerReportRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CornController.getSchedulerReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.getSchedulerReportRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get scheduler report request', async () => {
      const mockError = new Error('Exception error');
      cornService.getSchedulerReportRequest.mockRejectedValue(mockError);

      await CornController.getSchedulerReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.getSchedulerReportRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getRerunReportRequest', () => {
    it('should handle successful get rerun report request', async () => {
      const mockResponse = { data: 'test' };
      cornService.getRerunReportRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CornController.getRerunReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.getRerunReportRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get rerun report request', async () => {
      const mockError = new Error('Service error');
      cornService.getRerunReportRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CornController.getRerunReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.getRerunReportRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get rerun report request', async () => {
      const mockError = new Error('Exception error');
      cornService.getRerunReportRequest.mockRejectedValue(mockError);

      await CornController.getRerunReportRequest(mockReq, mockRes, mockNext);

      expect(cornService.getRerunReportRequest).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getSchedulerTimelineNames', () => {
    it('should handle successful get scheduler timeline names', async () => {
      const mockResponse = { data: 'test' };
      cornService.getSchedulerTimelineNames.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CornController.getSchedulerTimelineNames(mockReq, mockRes, mockNext);

      expect(cornService.getSchedulerTimelineNames).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get scheduler timeline names', async () => {
      const mockError = new Error('Service error');
      cornService.getSchedulerTimelineNames.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CornController.getSchedulerTimelineNames(mockReq, mockRes, mockNext);

      expect(cornService.getSchedulerTimelineNames).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get scheduler timeline names', async () => {
      const mockError = new Error('Exception error');
      cornService.getSchedulerTimelineNames.mockRejectedValue(mockError);

      await CornController.getSchedulerTimelineNames(mockReq, mockRes, mockNext);

      expect(cornService.getSchedulerTimelineNames).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('runtimeScheduler', () => {
    it('should call cornService.runtimeScheduler successfully', async () => {
      cornService.runtimeScheduler.mockResolvedValue();

      await CornController.runtimeScheduler();

      expect(cornService.runtimeScheduler).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from cornService.runtimeScheduler and log them', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const error = new Error('Service error');
      cornService.runtimeScheduler.mockRejectedValue(error);

      await CornController.runtimeScheduler();

      expect(cornService.runtimeScheduler).toHaveBeenCalledTimes(1);
      expect(consoleSpy).toHaveBeenCalledWith('*************ERROR*****************', error);

      consoleSpy.mockRestore();
    });
  });
});
