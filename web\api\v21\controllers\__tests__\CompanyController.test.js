const status = require('http-status');
const CompanyController = require('../CompanyController');

// Mock the services
jest.mock('../../services', () => ({
  companyService: {
    getAll: jest.fn(),
    companyLogoUpload: jest.fn(),
    getDefinableWork: jest.fn(),
    getAllCompany: jest.fn(),
    addCompany: jest.fn(),
    deleteCompany: jest.fn(),
    editCompany: jest.fn(),
    checkExistCompany: jest.fn(),
  },
  projectService: {
    // Add any project service methods if needed
  },
}));

jest.mock('../../services/exportService', () => ({
  exportToExcel: jest.fn(),
}));

jest.mock('../../models', () => ({
  Company: {
    getAllCompany: jest.fn(),
  },
}));

const { companyService } = require('../../services');
const exportService = require('../../services/exportService');
const { Company } = require('../../models');

describe('CompanyController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    jest.clearAllMocks();

    mockReq = {
      body: {},
      params: {},
      query: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('getAllCompanies', () => {
    it('should get all companies successfully', async () => {
      // Arrange
      const mockResponse = {
        companyArray: [
          { id: 1, name: 'Company 1', isDeleted: false },
          { id: 2, name: 'Company 2', isDeleted: false },
        ],
        parentCompany: { id: 1, name: 'Parent Company' },
      };
      companyService.getAll.mockImplementation((req, callback) => callback(mockResponse, null));

      // Act
      await CompanyController.getAllCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.getAll).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: mockResponse.companyArray,
        parentCompany: mockResponse.parentCompany,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to get companies');
      companyService.getAll.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.getAllCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.getAll).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      companyService.getAll.mockImplementation(() => {
        throw error;
      });

      // Act
      await CompanyController.getAllCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('companyLogoUpload', () => {
    it('should upload company logo successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        logoUrl: 'https://example.com/logo.png',
        companyId: 123,
      };
      companyService.companyLogoUpload.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.companyLogoUpload(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.companyLogoUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company uploaded successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Logo upload failed');
      companyService.companyLogoUpload.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.companyLogoUpload(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.companyLogoUpload).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getDefinableWork', () => {
    it('should get definable work successfully', async () => {
      // Arrange
      const mockResponse = {
        defineRecord: [
          { id: 1, name: 'Work Type 1', description: 'Description 1' },
          { id: 2, name: 'Work Type 2', description: 'Description 2' },
        ],
      };
      companyService.getDefinableWork.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.getDefinableWork(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.getDefinableWork).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable work list.',
        data: mockResponse.defineRecord,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Failed to get definable work');
      companyService.getDefinableWork.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.getDefinableWork(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.getDefinableWork).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getCompanies', () => {
    it('should get companies with parent company successfully', async () => {
      // Arrange
      const mockResponse = {
        companyList: {
          rows: [
            { id: 1, companyName: 'Company A' },
            { id: 2, companyName: 'Company B' },
          ],
        },
        parentCompany: { id: 3, companyName: 'Parent Company' },
      };
      companyService.getAllCompany.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.getCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.getAllCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: [
          { id: 1, companyName: 'Company A' },
          { id: 2, companyName: 'Company B' },
          { id: 3, companyName: 'Parent Company' },
        ],
      });
    });

    it('should get companies without parent company', async () => {
      // Arrange
      const mockResponse = {
        companyList: {
          rows: [
            { id: 1, companyName: 'Company A' },
            { id: 2, companyName: 'Company B' },
          ],
        },
        parentCompany: null,
      };
      companyService.getAllCompany.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.getCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: [
          { id: 1, companyName: 'Company A' },
          { id: 2, companyName: 'Company B' },
        ],
      });
    });

    it('should handle duplicate parent company', async () => {
      // Arrange
      const mockResponse = {
        companyList: {
          rows: [
            { id: 1, companyName: 'Parent Company' },
            { id: 2, companyName: 'Company B' },
          ],
        },
        parentCompany: { id: 1, companyName: 'Parent Company' },
      };
      companyService.getAllCompany.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.getCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: [
          { id: 1, companyName: 'Parent Company' },
          { id: 2, companyName: 'Company B' },
        ],
      });
    });
  });

  describe('addCompany', () => {
    it('should add company successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        name: 'New Company',
        createdAt: '2023-01-01T00:00:00Z',
      };
      companyService.addCompany.mockImplementation((req, callback) => callback(mockResponse, null));

      // Act
      await CompanyController.addCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.addCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company Created Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Company creation failed');
      companyService.addCompany.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.addCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.addCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('deleteCompany', () => {
    it('should delete company successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        name: 'Deleted Company',
        isDeleted: true,
      };
      companyService.deleteCompany.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.deleteCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.deleteCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company Deleted Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Company deletion failed');
      companyService.deleteCompany.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.deleteCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.deleteCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('editCompany', () => {
    it('should edit company successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        name: 'Updated Company',
        updatedAt: '2023-01-01T00:00:00Z',
      };
      companyService.editCompany.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.editCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.editCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company Updated Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Company update failed');
      companyService.editCompany.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.editCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.editCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getAllCompaniesList', () => {
    it('should get all companies list with parent company filter', async () => {
      // Arrange
      const mockCompanyList = [
        { id: 1, name: 'Company 1', isParent: true },
        { id: 2, name: 'Company 2', isParent: true },
      ];

      mockReq.query = { parentCompanyId: '123' };
      Company.getAllCompany.mockResolvedValue(mockCompanyList);

      // Act
      await CompanyController.getAllCompaniesList(mockReq, mockRes, mockNext);

      // Assert
      expect(Company.getAllCompany).toHaveBeenCalledWith({
        isDeleted: false,
        ParentCompanyId: '123',
        isParent: true,
      });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Companies listed Successfully.',
        data: mockCompanyList,
      });
    });

    it('should get all companies list without parent company filter', async () => {
      // Arrange
      const mockCompanyList = [
        { id: 1, name: 'Company 1', isParent: true },
        { id: 2, name: 'Company 2', isParent: true },
      ];

      Company.getAllCompany.mockResolvedValue(mockCompanyList);

      // Act
      await CompanyController.getAllCompaniesList(mockReq, mockRes, mockNext);

      // Assert
      expect(Company.getAllCompany).toHaveBeenCalledWith({
        isDeleted: false,
        isParent: true,
      });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Companies listed Successfully.',
        data: mockCompanyList,
      });
    });

    it('should return empty array when no companies found', async () => {
      // Arrange
      Company.getAllCompany.mockResolvedValue(null);

      // Act
      await CompanyController.getAllCompaniesList(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Companies listed successfully.',
        data: [],
      });
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Database error');
      Company.getAllCompany.mockRejectedValue(error);

      // Act
      await CompanyController.getAllCompaniesList(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('checkExistCompany', () => {
    it('should return error when company exists in project', async () => {
      // Arrange
      const mockExistCompany = {
        existInProject: [{ id: 1, name: 'Existing Company' }],
        sameAsParentCompany: false,
      };
      companyService.checkExistCompany.mockResolvedValue(mockExistCompany);

      // Act
      await CompanyController.checkExistCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.checkExistCompany).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 422,
        message: 'Company name exist.',
        data: mockExistCompany,
      });
    });

    it('should return error when company same as parent company', async () => {
      // Arrange
      const mockExistCompany = {
        existInProject: [],
        sameAsParentCompany: true,
      };
      companyService.checkExistCompany.mockResolvedValue(mockExistCompany);

      // Act
      await CompanyController.checkExistCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 422,
        message: 'Company name exist.',
        data: mockExistCompany,
      });
    });

    it('should return success when company does not exist', async () => {
      // Arrange
      const mockExistCompany = {
        existInProject: [],
        sameAsParentCompany: false,
      };
      companyService.checkExistCompany.mockResolvedValue(mockExistCompany);

      // Act
      await CompanyController.checkExistCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Company name not exist.',
        data: mockExistCompany,
      });
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      companyService.checkExistCompany.mockRejectedValue(error);

      // Act
      await CompanyController.checkExistCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('sampleCompanyTemplate', () => {
    it('should export sample company template successfully', async () => {
      // Arrange
      const mockResponse = {
        filename: 'sample_company_template.xlsx',
        data: 'base64-encoded-data',
      };
      exportService.exportToExcel.mockResolvedValue(mockResponse);

      // Act
      await CompanyController.sampleCompanyTemplate(mockReq, mockRes, mockNext);

      // Assert
      expect(exportService.exportToExcel).toHaveBeenCalledWith('sampleCompanyTemplate');
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Sample Company Template Downloaded Successfully.',
        data: mockResponse,
      });
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Export error');
      exportService.exportToExcel.mockRejectedValue(error);

      // Act
      await CompanyController.sampleCompanyTemplate(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('createCompany', () => {
    it('should create company successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        name: 'New Company',
        createdAt: '2023-01-01T00:00:00Z',
      };
      companyService.addCompany.mockImplementation((req, callback) => callback(mockResponse, null));

      // Act
      await CompanyController.createCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.addCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company Created Successfully.',
        data: mockResponse,
      });
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Company creation failed');
      companyService.addCompany.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CompanyController.createCompany(mockReq, mockRes, mockNext);

      // Assert
      expect(companyService.addCompany).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('Edge Cases', () => {
    it('should handle null response from service', async () => {
      // Arrange
      companyService.getAll.mockImplementation((req, callback) => callback(null, null));

      // Act
      await CompanyController.getAllCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(null);
    });

    it('should handle empty company list', async () => {
      // Arrange
      const mockResponse = {
        companyList: { rows: [] },
        parentCompany: null,
      };
      companyService.getAllCompany.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.getCompanies(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company list.',
        data: [],
      });
    });

    it('should handle request with complex company data', async () => {
      // Arrange
      const mockReqWithData = {
        ...mockReq,
        body: {
          name: 'Complex Company',
          address: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          phone: '******-123-4567',
          email: '<EMAIL>',
          website: 'https://complexcompany.com',
          industry: 'Technology',
          employeeCount: 500,
          foundedYear: 2010,
        },
      };
      const mockResponse = {
        id: 1,
        ...mockReqWithData.body,
        createdAt: '2023-01-01T00:00:00Z',
      };
      companyService.addCompany.mockImplementation((req, callback) => callback(mockResponse, null));

      // Act
      await CompanyController.addCompany(mockReqWithData, mockRes, mockNext);

      // Assert
      expect(companyService.addCompany).toHaveBeenCalledWith(mockReqWithData, expect.any(Function));
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company Created Successfully.',
        data: mockResponse,
      });
    });

    it('should handle request with file upload', async () => {
      // Arrange
      const mockReqWithFile = {
        ...mockReq,
        files: {
          logo: {
            name: 'company-logo.png',
            size: 1024,
            mimetype: 'image/png',
          },
        },
        body: {
          name: 'Company with Logo',
        },
      };
      const mockResponse = {
        id: 1,
        name: 'Company with Logo',
        logoUrl: 'https://example.com/logo.png',
      };
      companyService.companyLogoUpload.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CompanyController.companyLogoUpload(mockReqWithFile, mockRes, mockNext);

      // Assert
      expect(companyService.companyLogoUpload).toHaveBeenCalledWith(
        mockReqWithFile,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Company uploaded successfully.',
        data: mockResponse,
      });
    });
  });
});
