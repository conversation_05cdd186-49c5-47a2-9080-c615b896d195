// Mock moment
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((input, format) => {
    if (input && format) {
      return actualMoment(input, format);
    }
    return actualMoment(input);
  });
  mockMoment.duration = actualMoment.duration;
  return mockMoment;
});

// Mock services
jest.mock('../../services', () => ({
  deliveryReportService: {
    listDeliveryRequest: jest.fn(),
    exportReport: jest.fn(),
    getTotalRequestList: jest.fn(),
    exportWeeklyCalendarReport: jest.fn(),
    createSavedReports: jest.fn(),
    heatMapListDeliveryRequest: jest.fn(),
    exportHeatMapReport: jest.fn(),
    deleteSchedulerReport: jest.fn(),
  },
  craneReportService: {
    listCraneRequest: jest.fn(),
    exportReport: jest.fn(),
  },
  concreteReportService: {
    listConcreteRequest: jest.fn(),
    exportReport: jest.fn(),
  },
  inspectionReportService: {
    listInspectionRequest: jest.fn(),
    exportReport: jest.fn(),
  },
}));

// Mock AWS config
jest.mock('../../middlewares/awsConfig', () => ({
  reportUpload: jest.fn(),
}));

// Mock puppeteer service
jest.mock('../../services/puppeteerService', () => ({
  generatePdfByURL: jest.fn(),
}));

const ReportController = require('../ReportController');
const { deliveryReportService, craneReportService, concreteReportService, inspectionReportService } = require('../../services');
const awsConfig = require('../../middlewares/awsConfig');
const puppeteerService = require('../../services/puppeteerService');

describe('ReportController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      query: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
      end: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('deliveryRequest', () => {
    it('should list delivery request successfully', async () => {
      const mockResponse = [
        { id: 1, deliveryDate: '2023-01-01', status: 'completed' },
        { id: 2, deliveryDate: '2023-01-02', status: 'pending' },
      ];
      deliveryReportService.listDeliveryRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.deliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.listDeliveryRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delivery request listing', async () => {
      const mockError = new Error('Service error');
      deliveryReportService.listDeliveryRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.deliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.listDeliveryRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delivery request', async () => {
      const mockError = new Error('Exception error');
      deliveryReportService.listDeliveryRequest.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.deliveryRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('craneRequest', () => {
    it('should list crane request successfully', async () => {
      const mockResponse = [
        { id: 1, craneType: 'Tower', status: 'completed' },
        { id: 2, craneType: 'Mobile', status: 'pending' },
      ];
      craneReportService.listCraneRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.craneRequest(mockReq, mockRes, mockNext);

      expect(craneReportService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane request listing', async () => {
      const mockError = new Error('Service error');
      craneReportService.listCraneRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.craneRequest(mockReq, mockRes, mockNext);

      expect(craneReportService.listCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in crane request', async () => {
      const mockError = new Error('Exception error');
      craneReportService.listCraneRequest.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.craneRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('concreteRequest', () => {
    it('should list concrete request successfully', async () => {
      const mockResponse = [
        { id: 1, concreteType: 'Standard', status: 'completed' },
        { id: 2, concreteType: 'High-strength', status: 'pending' },
      ];
      concreteReportService.listConcreteRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.concreteRequest(mockReq, mockRes, mockNext);

      expect(concreteReportService.listConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from concrete request listing', async () => {
      const mockError = new Error('Service error');
      concreteReportService.listConcreteRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.concreteRequest(mockReq, mockRes, mockNext);

      expect(concreteReportService.listConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in concrete request', async () => {
      const mockError = new Error('Exception error');
      concreteReportService.listConcreteRequest.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.concreteRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('inspectionRequest', () => {
    it('should list inspection request successfully', async () => {
      const mockResponse = [
        { id: 1, inspectionType: 'Safety', status: 'completed' },
        { id: 2, inspectionType: 'Quality', status: 'pending' },
      ];
      inspectionReportService.listInspectionRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.inspectionRequest(mockReq, mockRes, mockNext);

      expect(inspectionReportService.listInspectionRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from inspection request listing', async () => {
      const mockError = new Error('Service error');
      inspectionReportService.listInspectionRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.inspectionRequest(mockReq, mockRes, mockNext);

      expect(inspectionReportService.listInspectionRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in inspection request', async () => {
      const mockError = new Error('Exception error');
      inspectionReportService.listInspectionRequest.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.inspectionRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportDeliveryReport', () => {
    it('should export delivery report successfully for PDF/CSV', async () => {
      const mockResponse = { url: 'https://example.com/report.pdf' };
      mockReq.body = { exportType: 'PDF' };
      deliveryReportService.exportReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportDeliveryReport(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking exported Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should export delivery report successfully for EXCEL (not saved)', async () => {
      const mockResponse = { xlsx: { write: jest.fn() } };
      mockReq.body = { exportType: 'EXCEL', saved: false, reportName: 'test-report' };
      deliveryReportService.exportReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportDeliveryReport(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=test-report.xlsx');
      expect(mockResponse.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should export delivery report successfully for EXCEL (saved)', async () => {
      const mockResponse = { saved: true };
      mockReq.body = { exportType: 'EXCEL', saved: true };
      deliveryReportService.exportReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportDeliveryReport(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Report saved Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delivery report export', async () => {
      const mockError = new Error('Service error');
      deliveryReportService.exportReport.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.exportDeliveryReport(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in delivery report export', async () => {
      const mockError = new Error('Exception error');
      deliveryReportService.exportReport.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.exportDeliveryReport(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportCraneReport', () => {
    it('should export crane report successfully', async () => {
      const mockBuffer = Buffer.from('test pdf content');
      craneReportService.exportReport.mockImplementation((req, callback) => {
        callback(mockBuffer, null);
      });

      await ReportController.exportCraneReport(mockReq, mockRes, mockNext);

      expect(craneReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/pdf');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=crane_report.pdf');
      expect(mockRes.end).toHaveBeenCalledWith(mockBuffer);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane report export', async () => {
      const mockError = new Error('Service error');
      craneReportService.exportReport.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.exportCraneReport(mockReq, mockRes, mockNext);

      expect(craneReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in crane report export', async () => {
      const mockError = new Error('Exception error');
      craneReportService.exportReport.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.exportCraneReport(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportConcreteReport', () => {
    it('should export concrete report successfully', async () => {
      const mockBuffer = Buffer.from('test pdf content');
      concreteReportService.exportReport.mockImplementation((req, callback) => {
        callback(mockBuffer, null);
      });

      await ReportController.exportConcreteReport(mockReq, mockRes, mockNext);

      expect(concreteReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/pdf');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=concrete_report.pdf');
      expect(mockRes.end).toHaveBeenCalledWith(mockBuffer);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from concrete report export', async () => {
      const mockError = new Error('Service error');
      concreteReportService.exportReport.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.exportConcreteReport(mockReq, mockRes, mockNext);

      expect(concreteReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in concrete report export', async () => {
      const mockError = new Error('Exception error');
      concreteReportService.exportReport.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.exportConcreteReport(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportInspectionReport', () => {
    it('should export inspection report successfully', async () => {
      const mockBuffer = Buffer.from('test pdf content');
      inspectionReportService.exportReport.mockImplementation((req, callback) => {
        callback(mockBuffer, null);
      });

      await ReportController.exportInspectionReport(mockReq, mockRes, mockNext);

      expect(inspectionReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/pdf');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=inspection_report.pdf');
      expect(mockRes.end).toHaveBeenCalledWith(mockBuffer);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from inspection report export', async () => {
      const mockError = new Error('Service error');
      inspectionReportService.exportReport.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.exportInspectionReport(mockReq, mockRes, mockNext);

      expect(inspectionReportService.exportReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in inspection report export', async () => {
      const mockError = new Error('Exception error');
      inspectionReportService.exportReport.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.exportInspectionReport(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('weeklyCalendarRequest', () => {
    it('should get weekly calendar request successfully', async () => {
      const mockResponse = { total: 100, requests: [] };
      deliveryReportService.getTotalRequestList.mockResolvedValue(mockResponse);

      await ReportController.weeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.getTotalRequestList).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from weekly calendar request', async () => {
      const mockError = new Error('Service error');
      deliveryReportService.getTotalRequestList.mockRejectedValue(mockError);

      await ReportController.weeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.getTotalRequestList).toHaveBeenCalledWith(mockReq, mockNext);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportWeeklyCalendarRequest', () => {
    it('should export weekly calendar request successfully for EXCEL', async () => {
      const mockResponse = { xlsx: { write: jest.fn() } };
      mockReq.body = { exportType: 'EXCEL', saved: false, reportName: 'weekly-report' };
      deliveryReportService.exportWeeklyCalendarReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportWeeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportWeeklyCalendarReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=weekly-report.xlsx');
      expect(mockResponse.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should export weekly calendar request successfully for PDF with time validation', async () => {
      mockReq.body = {
        exportType: 'PDF',
        eventStartTime: '08:00:00',
        eventEndTime: '10:00:00',
        startDate: '2023-01-01',
        endDate: '2023-01-02',
        ParentCompanyId: 1,
        companyFilter: 'test',
        startTime: '08:00',
        endTime: '18:00',
        equipmentFilter: 'crane',
        gateFilter: 'gate1',
        memberFilter: 'member1',
        statusFilter: 'active',
        timezone: 'UTC',
        isDST: false,
        templateType: [{ id: 1 }],
        locationFilter: 'location1',
        reportName: 'test-report'
      };
      mockReq.params = { ProjectId: 1 };
      mockReq.user = { id: 1 };

      const mockBuffer = Buffer.from('test pdf content');
      puppeteerService.generatePdfByURL.mockResolvedValue(mockBuffer);
      awsConfig.reportUpload.mockImplementation((buffer, name, type, callback) => {
        callback({ Location: 'https://s3.amazonaws.com/bucket/report.pdf' }, null);
      });

      await ReportController.exportWeeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(puppeteerService.generatePdfByURL).toHaveBeenCalled();
      expect(awsConfig.reportUpload).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Weekly calendar exported Successfully.',
        data: { Location: 'https://s3.amazonaws.com/bucket/report.pdf' },
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle time difference validation error', async () => {
      mockReq.body = {
        exportType: 'PDF',
        eventStartTime: '08:00:00',
        eventEndTime: '22:00:00', // More than 12 hours difference
      };

      await ReportController.exportWeeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Currently, we are not supporting more than 12 hours time difference for PDF export. please change the start and end time in the filter section.',
        data: [],
      });
    });

    it('should handle error from weekly calendar request export', async () => {
      const mockError = new Error('Service error');
      mockReq.body = { exportType: 'EXCEL' };
      deliveryReportService.exportWeeklyCalendarReport.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.exportWeeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportWeeklyCalendarReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in weekly calendar request export', async () => {
      const mockError = new Error('Exception error');
      mockReq.body = { exportType: 'PDF' };
      puppeteerService.generatePdfByURL.mockRejectedValue(mockError);

      await ReportController.exportWeeklyCalendarRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('heatMapdeliveryRequest', () => {
    it('should list heat map delivery request successfully', async () => {
      const mockResponse = { heatMapData: [] };
      deliveryReportService.heatMapListDeliveryRequest.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.heatMapdeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.heatMapListDeliveryRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Success.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from heat map delivery request', async () => {
      const mockError = new Error('Service error');
      deliveryReportService.heatMapListDeliveryRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.heatMapdeliveryRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.heatMapListDeliveryRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in heat map delivery request', async () => {
      const mockError = new Error('Exception error');
      deliveryReportService.heatMapListDeliveryRequest.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.heatMapdeliveryRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportHeatMapRequest', () => {
    it('should export heat map request successfully for PDF', async () => {
      const mockResponse = { url: 'https://example.com/heatmap.pdf' };
      mockReq.body = { exportType: 'PDF' };
      deliveryReportService.exportHeatMapReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportHeatMapRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportHeatMapReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Heat map exported Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should export heat map request successfully for EXCEL', async () => {
      const mockResponse = { xlsx: { write: jest.fn() } };
      mockReq.body = { exportType: 'EXCEL', reportName: 'heatmap-report' };
      deliveryReportService.exportHeatMapReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportHeatMapRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportHeatMapReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=heatmap-report.xlsx');
      expect(mockResponse.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle no data found for PDF export', async () => {
      const mockResponse = { no_data: true };
      mockReq.body = { exportType: 'PDF' };
      deliveryReportService.exportHeatMapReport.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await ReportController.exportHeatMapRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportHeatMapReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'No data found.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from heat map request export', async () => {
      const mockError = new Error('Service error');
      deliveryReportService.exportHeatMapReport.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await ReportController.exportHeatMapRequest(mockReq, mockRes, mockNext);

      expect(deliveryReportService.exportHeatMapReport).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in heat map request export', async () => {
      const mockError = new Error('Exception error');
      deliveryReportService.exportHeatMapReport.mockImplementation(() => {
        throw mockError;
      });

      await ReportController.exportHeatMapRequest(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('deleteSchedulerReport', () => {
    it('should delete scheduler report successfully', async () => {
      const mockResponse = true;
      mockReq.query = { reportId: 1 };
      deliveryReportService.deleteSchedulerReport.mockResolvedValue(mockResponse);

      await ReportController.deleteSchedulerReport(mockReq, mockRes, mockNext);

      expect(deliveryReportService.deleteSchedulerReport).toHaveBeenCalledWith(mockReq.query);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Scheduled report deleted successfully.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delete scheduler report', async () => {
      const mockError = new Error('Service error');
      mockReq.query = { reportId: 1 };
      deliveryReportService.deleteSchedulerReport.mockRejectedValue(mockError);

      await ReportController.deleteSchedulerReport(mockReq, mockRes, mockNext);

      expect(deliveryReportService.deleteSchedulerReport).toHaveBeenCalledWith(mockReq.query);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
