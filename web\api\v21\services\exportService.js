const ExcelJS = require('exceljs');
const moment = require('moment');
const gateService = require('./gateService');
const equipmentService = require('./equipmentService');
const locationService = require('./locationService');
const companyService = require('./companyService');
const memberService = require('./memberService');
const timePicker = require('../helpers/timePicker');

exports.createWorkbook = async () => {
  const workbook = new ExcelJS.Workbook();
  workbook.views = [
    {
      x: 0,
      y: 0,
      width: 10000,
      height: 20000,
      firstSheet: 0,
      activeTab: 1,
      visibility: 'visible',
    },
  ];
  return workbook;
};

exports.exportSampleDocument = async () => {
  const workbook = await this.createWorkbook();
  const worksheet = workbook.addWorksheet('DFOW');
  const dfowArray = [
    {
      Specification: '12 23 45',
      DFOW: 'First',
    },
    {
      Specification: '56 45 89',
      DFOW: 'Second',
    },
    {
      Specification: '58 44 75',
      DFOW: 'Third',
    },
  ];
  /* Column headers */
  worksheet.getRow(1).values = ['id', 'Specification', 'DFOW'];
  worksheet.columns = [
    { key: 'id', width: 5 },
    { key: 'Specification', width: 32 },
    { key: 'DFOW', width: 32 },
  ];

  dfowArray.forEach(async (data, index) => {
    worksheet.addRow({
      id: index + 1,
      Specification: data.Specification,
      DFOW: data.DFOW,
    }).font = { size: '10px' };
  });
  return workbook;
};

exports.exportSampleLocationDocument = async () => {
  const workbook = await this.createWorkbook();
  const worksheet = workbook.addWorksheet('Location');
  worksheet.mergeCells('A1:P1');
  worksheet.getCell('A1').value = `
  Need to Remember
  1. Mandatory columns - Category.
  2. Skipping of Mandatory columns will not be eligible as Location and shows invalid.
  3. Tier Name without Sub Category & Category will not be eligible as Location and shows invalid
  4. Changing the sample template file name restricted to upload.`;
  worksheet.getCell('A1').alignment = { vertical: 'top', wrapText: true };
  worksheet.getCell('A1').font = { fontSize: '14px', bold: true, italic: true };
  worksheet.getRow(1).height = 100;
  /* Column headers */
  worksheet.getRow(2).values = ['Category', 'Sub Category', 'Tier Name', 'Location Path in Booking Dropdown'];
  worksheet.columns = [
    { width: 32 },
    { width: 32 },
    { width: 32 },
    { width: 32 }
  ];
  const data = [
    ['Building A', '', ''],
    ['Building A', 'Level 1', ''],
    ['Building A', 'Level 1', 'Room 1'],
    ['Building A', 'Level 1', 'Room 2'],
    ['Building A', 'Level 1', 'Room 3'],
    ['Building A', 'Level 2', ''],
    ['Building A', 'Level 2', 'Room 1'],
    ['Building A', 'Level 2', 'Room 2'],
    ['Building A', 'Level 2', 'Room 3'],
    ['Building B', '', ''],
    ['Building B', 'Level 1', ''],
    ['Building B', 'Level 1', 'Room 1'],
    ['Building B', 'Level 1', 'Room 2'],
    ['Building B', 'Level 1', 'Room 3'],
    ['Building B', 'Level 2', ''],
    ['Building B', 'Level 2', 'Room 1'],
    ['Building B', 'Level 2', 'Room 2'],
    ['Building B', 'Level 2', 'Room 3'],
  ];
  data.forEach(rowData => {
    worksheet.addRow(rowData);
  });
  for (let i = 3; i <= 500; i++) {
    const formula = `IF(AND(ISBLANK(A${i})=TRUE,ISBLANK(B${i})=TRUE,ISBLANK(C${i})=TRUE), "",IF(AND(ISBLANK(A${i})=FALSE,ISBLANK(B${i})=TRUE,ISBLANK(C${i})=TRUE), A${i}, IF(AND(ISBLANK(A${i})=FALSE,ISBLANK(B${i})=FALSE, ISBLANK(C${i})=TRUE), A${i}&">"&B${i}, IF(AND(ISBLANK(A${i})=FALSE,ISBLANK(B${i})=FALSE, ISBLANK(C${i})=FALSE), A${i}&">"&B${i}&">"&C${i}, "INVALID"))))`;
    worksheet.getCell(`D${i}`).value = {
      formula: formula,
    };
  }
  return workbook;
};

exports.sampleDeliveryRequestTemplate = async (req) => {
  const workbook = await this.createWorkbook();
  const worksheet1 = workbook.addWorksheet('Booking', { state: 'hidden' });
  const worksheet = workbook.addWorksheet('Delivery Booking');

  // Load data in hidden sheet for dropdown
  // Gates
  const gatelist = await gateService.gatesForBulkUploadDeliveryRequest(req);
  for (let i = 0; i < gatelist.length; i += 1) {
    worksheet1.getCell(`A${i + 1}`).value = gatelist[i].gateName;
  }
  // Equipments
  const equipmentList = await equipmentService.equipmentsForBulkUploadDeliveryRequest(req);
  for (let i = 0; i < equipmentList.length; i += 1) {
    worksheet1.getCell(`B${i + 1}`).value = equipmentList[i].equipmentName;
  }
  // Companies
  const responseData = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(req);
  const companiesData = responseData.newCompanyList;
  for (let i = 0; i < companiesData.length; i += 1) {
    worksheet1.getCell(`C${i + 1}`).value = companiesData[i].companyName;
  }
  // DFOW
  const dfowList = responseData.defineRecord;
  for (let i = 0; i < dfowList.length; i += 1) {
    worksheet1.getCell(`D${i + 1}`).value = dfowList[i].DFOW;
  }
  // Members
  const membersList = await memberService.membersForBulkUploadDeliveryRequest(req);
  for (let i = 0; i < membersList.length; i += 1) {
    worksheet1.getCell(`E${i + 1}`).value = membersList[i].User.email;
  }
  // Timepicker
  for (let index = 0; index < timePicker.length; index += 1) {
    worksheet1.getCell(`F${index + 1}`).value = timePicker[index];
  }
  // Escort
  const escortOptions = ['escort needed', 'escort not needed'];
  for (let index = 0; index < escortOptions.length; index += 1) {
    worksheet1.getCell(`G${index + 1}`).value = escortOptions[index];
  }
  // Location
  const locationList = await locationService.getDropdownValuesForLocation(req);
  for (let i = 0; i < locationList.length; i += 1) {
    worksheet1.getCell(`H${i + 1}`).value = locationList[i].locationPath;
  }
  // Define range values for a dropdown list
  const gateCellRange = `=Booking!$A$1:$A$${gatelist.length}`;
  const equipmentCellRange = `=Booking!$B$1:$B$${equipmentList.length}`;
  const companiesCellRange = `=Booking!$C$1:$C$${companiesData.length}`;
  const dfowCellRange = `=Booking!$D$1:$D$${dfowList.length}`;
  const membersCellRange = `=Booking!$E$1:$E$${membersList.length}`;
  const timePickerRange = `=Booking!$F$1:$F$${timePicker.length}`;
  const escortCellRange = `=Booking!$G$1:$G$${escortOptions.length}`;
  const locationCellRange = `=Booking!$H$1:$H$${locationList.length}`;
  /* Note */
  worksheet.getRow(1).values = [
    'id',
    'Description',
    'Responsible_Company',
    'Definable_Feature_of_Work',
    'Responsible_Person',
    'Is_Escort_Needed',
    'Date',
    'From_time',
    'To_time',
    'Gate',
    'Equipment',
    'Picking From',
    'Picking To',
    'Vehicle_Detail',
    'Additional_Notes',
    'Location',
  ];

  worksheet.mergeCells('A1:P1');

  worksheet.getCell('A1').value = `
  Need to Remember

  1. Mandatory columns - Description.
  2. Skipping of Mandatory columns will not be eligible as Delivery Booking.
  3. Changing the sample template file name restricted to upload.
  4. Date Format => (MMM-DD-YYYY) Eg: May-05-2023`;

  worksheet.getCell('A1').alignment = { vertical: 'top', wrapText: true };
  worksheet.getCell('A1').font = { fontSize: '14px', bold: true, italic: true };

  worksheet.getRow(1).height = 130;
  /* Column headers */
  worksheet.getRow(2).values = [
    'id',
    'Description',
    'Responsible_Company',
    'Definable_Feature_of_Work',
    'Responsible_Person',
    'Is_Escort_Needed',
    'Date - (MMM-DD-YYYY)',
    'From_time',
    'To_time',
    'Gate',
    'Equipment',
    'Picking_From',
    'Picking_To',
    'Vehicle_Detail',
    'Additional_Notes',
    'Location',
  ];
  worksheet.columns = [
    { key: 'id', width: 5 },
    { key: 'Description', width: 32 },
    { key: 'Responsible_Company', width: 32 },
    { key: 'Definable_Feature_of_Work', width: 32 },
    { key: 'Responsible_Person', width: 32 },
    { key: 'Is_Escort_Needed', width: 32 },
    { key: 'Date', width: 32 },
    { key: 'From_time', width: 32 },
    { key: 'To_time', width: 32 },
    { key: 'Gate', width: 32 },
    { key: 'Equipment', width: 32 },
    { key: 'Picking_From', width: 32 },
    { key: 'Picking_To', width: 32 },
    { key: 'Vehicle_Detail', width: 32 },
    { key: 'Additional_Notes', width: 32 },
    { key: 'Location', width: 32 },
  ];

  // Insert data into worksheet
  for (let i = 2; i <= 500; i += 1) {
    worksheet.addRow({
      id: i - 1,
    }).font = { size: '10px' };
    worksheet.getCell(`C${i + 1} `).dataValidation = {
      formulae: [companiesCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`D${i + 1} `).dataValidation = {
      formulae: [dfowCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`E${i + 1} `).dataValidation = {
      formulae: [membersCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`F${i + 1} `).dataValidation = {
      formulae: [escortCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`F${i + 1} `).value = 'escort not needed';
    worksheet.getCell(`G${i + 1} `).NumberFormat = 14;
    worksheet.getCell(`H${i + 1} `).dataValidation = {
      formulae: [timePickerRange],
      type: 'list',
      allowBlank: false,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`H${i + 1} `).value = '07:00:00';
    worksheet.getCell(`I${i + 1} `).dataValidation = {
      formulae: [timePickerRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`I${i + 1} `).value = '08:00:00';
    worksheet.getCell(`J${i + 1} `).dataValidation = {
      formulae: [gateCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`K${i + 1} `).dataValidation = {
      formulae: [equipmentCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
    worksheet.getCell(`P${i + 1} `).dataValidation = {
      formulae: [locationCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
  }
  return workbook;
};

exports.sampleCompanyTemplate = async (req) => {
  const workbook = await this.createWorkbook();
  const worksheet1 = workbook.addWorksheet('Booking', { state: 'hidden' });
  const worksheet = workbook.addWorksheet('Company');

  // Load data in hidden sheet for dropdown
  // DFOW
  const responseData = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(req);
  const dfowList = responseData.defineRecord;
  for (let i = 0; i < dfowList.length; i += 1) {
    worksheet1.getCell(`A${i + 1}`).value = dfowList[i].DFOW;
  }

  // Define range values for a dropdown list
  const dfowCellRange = `=Booking!$A$1:$A$${dfowList.length}`;
  /* Note */
  worksheet.getRow(1).values = [
    'id',
    'Company_Name',
    'Definable_Feature_of_Work',
    'Address_Line_1',
    'Address_Line_2',
    'Country',
    'State',
    'City',
    'Zipcode',
    'Website',
    'Additional_Notes',
  ];

  worksheet.mergeCells('A1:K1');

  worksheet.getCell('A1').value = `
  Need to Remember

  1. Mandatory column - Company Name.
  2. Skipping of Mandatory column will not be eligible.
  3. Changing the sample template file name restricted to upload.
  4. Select Definable Feature of Work from dropdown values`;
  worksheet.getCell('A1').alignment = { vertical: 'top', wrapText: true };
  worksheet.getCell('A1').font = { fontSize: '14px', bold: true, italic: true };

  worksheet.getRow(1).height = 130;
  /* Column headers */
  worksheet.getRow(2).values = [
    'id',
    'Company_Name',
    'Definable_Feature_of_Work',
    'Address_Line_1',
    'Address_Line_2',
    'Country',
    'State',
    'City',
    'Zipcode',
    'Website',
    'Additional_Notes',
  ];
  worksheet.columns = [
    { key: 'id', width: 5 },
    { key: 'Company_Name', width: 32 },
    { key: 'Definable_Feature_of_Work', width: 32 },
    { key: 'Address_Line_1', width: 32 },
    { key: 'Address_Line_2', width: 32 },
    { key: 'Country', width: 32 },
    { key: 'State', width: 32 },
    { key: 'City', width: 32 },
    { key: 'Zipcode', width: 32 },
    { key: 'Website', width: 32 },
    { key: 'Additional_Notes', width: 32 },
  ];

  // Insert data into worksheet
  for (let i = 2; i <= 500; i += 1) {
    worksheet.addRow({
      id: i,
    }).font = { size: '10px' };
    worksheet.getCell(`C${i + 1} `).dataValidation = {
      formulae: [dfowCellRange],
      type: 'list',
      allowBlank: true,
      showErrorMessage: true,
      errorStyle: 'error',
      showInputMessage: true,
      error: 'Please choose from dropdown',
    };
  }
  return workbook;
};
