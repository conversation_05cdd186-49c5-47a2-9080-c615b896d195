const csvDeliveryReportService = require('../csvDeliveryReportService');

// Mock all dependencies
jest.mock('export-to-csv', () => ({
  ExportToCsv: jest.fn().mockImplementation(() => ({
    generateCsv: jest.fn(),
  })),
}));

jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return jest.fn((date) => {
    if (date) {
      return actualMoment(date);
    }
    return actualMoment('2023-01-01T10:00:00Z');
  });
});

jest.mock('../../middlewares/awsConfig', () => ({
  reportUpload: jest.fn(),
}));

describe('CsvDeliveryReportService', () => {
  let mockDone;
  let mockExportToCsv;
  let mockAwsConfig;
  let mockMoment;

  beforeEach(() => {
    jest.clearAllMocks();

    mockDone = jest.fn();

    // Get mocked modules
    const { ExportToCsv } = require('export-to-csv');
    mockExportToCsv = ExportToCsv;
    mockAwsConfig = require('../../middlewares/awsConfig');
    mockMoment = require('moment');

    // Setup default mock implementations
    mockExportToCsv.mockImplementation(() => ({
      generateCsv: jest.fn().mockResolvedValue('mock,csv,data\n1,test,value'),
    }));

    mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
      callback('http://example.com/file.csv', null);
    });
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(csvDeliveryReportService).toBeDefined();
      expect(csvDeliveryReportService.exportDeliveryReportInCsvFormat).toBeDefined();
      expect(typeof csvDeliveryReportService.exportDeliveryReportInCsvFormat).toBe('function');
    });
  });

  describe('exportDeliveryReportInCsvFormat', () => {
    let mockData;
    let mockSelectedHeaders;
    let mockTimezoneOffset;
    let mockFileName;
    let mockExportType;

    beforeEach(() => {
      mockData = [
        {
          DeliveryId: 1,
          description: 'Test delivery',
          deliveryStart: '2023-01-01T10:00:00Z',
          status: 'Completed',
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: 'Doe'
            }
          },
          equipmentDetails: [
            {
              equipmentName: {
                Equipment: 'Crane A'
              }
            },
            {
              equipmentName: {
                Equipment: 'Crane B'
              }
            }
          ],
          defineWorkDetails: [
            {
              DFOW: {
                DeliverDefineWork: 'Foundation Work'
              }
            }
          ],
          gateDetails: [
            {
              Gate: {
                gateName: 'Gate 1'
              }
            }
          ],
          companyDetails: [
            {
              companyName: {
                Company: 'ABC Construction'
              }
            }
          ],
          memberDetails: [
            {
              User: {
                Member: 'Jane Smith'
              }
            }
          ],
          location: {
            locationPath: 'Building A / Floor 1'
          }
        }
      ];

      mockSelectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      mockTimezoneOffset = -300; // -5 hours
      mockFileName = 'delivery_report';
      mockExportType = 'csv';
    });

    it('should successfully export delivery report with all fields', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('mock,csv,data\n1,test,value')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      expect(mockExportToCsv).toHaveBeenCalledWith({
        showLabels: true,
        showTitle: false,
        useTextFile: false,
        useBom: false,
        useKeysAsHeaders: true,
      });

      expect(mockCsvInstance.generateCsv).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            Id: 1,
            Description: 'Test delivery',
            'Date & Time': expect.any(String),
            Status: 'Completed',
            'Approved By': 'John Doe',
            Equipment: 'Crane A, Crane B',
            'Definable Feature of Work': 'Foundation Work',
            Gate: 'Gate 1',
            'Responsible Company': 'ABC Construction',
            'Responsible Person': 'Jane Smith',
            Location: 'Building A / Floor 1'
          })
        ]),
        true
      );

      expect(mockAwsConfig.reportUpload).toHaveBeenCalledWith(
        expect.any(Buffer),
        mockFileName,
        mockExportType,
        expect.any(Function)
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle selective headers (only some fields active)', async () => {
      const selectiveHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: false },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: false }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('id,date\n1,Jan-01-2023 05:00 am')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        mockData,
        selectiveHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      expect(mockCsvInstance.generateCsv).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            Id: 1,
            'Date & Time': expect.any(String)
          })
        ]),
        true
      );

      // Should not include inactive fields
      const generatedData = mockCsvInstance.generateCsv.mock.calls[0][0][0];
      expect(generatedData).not.toHaveProperty('Description');
      expect(generatedData).not.toHaveProperty('Status');
    });

    it('should handle empty data array', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        [],
        mockSelectedHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      expect(mockCsvInstance.generateCsv).toHaveBeenCalledWith([], true);
    });

    it('should handle data with missing/null fields', async () => {
      const incompleteData = [
        {
          DeliveryId: 2,
          description: null,
          deliveryStart: '2023-01-02T15:30:00Z',
          status: undefined,
          approverDetails: null,
          equipmentDetails: [],
          defineWorkDetails: null,
          gateDetails: [],
          companyDetails: undefined,
          memberDetails: [],
          location: null
        }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('id,description\n2,')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        incompleteData,
        mockSelectedHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      const generatedData = mockCsvInstance.generateCsv.mock.calls[0][0][0];
      expect(generatedData).toEqual({
        Id: 2,
        Description: null,
        'Date & Time': expect.any(String),
        Status: undefined,
        'Approved By': '-',
        Equipment: '-',
        'Definable Feature of Work': '-',
        Gate: '-',
        'Responsible Company': '-',
        'Responsible Person': '',
        Location: '-'
      });
    });

    it('should handle AWS upload error', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('mock,csv,data')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      // Mock AWS upload to return error
      mockAwsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
        callback(null, { message: 'AWS upload failed' });
      });

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'cannot export document' });
    });

    it('should handle case when CSV generation returns null/empty', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue(null)
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      // Should not call AWS upload or done callback when CSV is null
      expect(mockAwsConfig.reportUpload).not.toHaveBeenCalled();
      expect(mockDone).not.toHaveBeenCalled();
    });

    it('should handle complex nested data structures', async () => {
      const complexData = [
        {
          DeliveryId: 3,
          description: 'Complex delivery',
          deliveryStart: '2023-01-03T08:15:00Z',
          status: 'In Progress',
          approverDetails: {
            User: {
              firstName: '',
              lastName: 'SingleName'
            }
          },
          equipmentDetails: [
            {
              equipmentName: {
                Equipment: 'Equipment 1'
              }
            },
            {
              equipmentName: null
            },
            {
              equipmentName: {
                Equipment: 'Equipment 2'
              }
            }
          ],
          defineWorkDetails: [
            {
              DFOW: null
            },
            {
              DFOW: {
                DeliverDefineWork: 'Work Type A'
              }
            }
          ],
          gateDetails: [
            {
              Gate: null
            }
          ],
          companyDetails: [
            {
              companyName: null
            },
            {
              companyName: {
                Company: 'Company A'
              }
            }
          ],
          memberDetails: [
            {
              User: null
            },
            {
              User: {
                Member: 'Member A'
              }
            }
          ],
          location: {
            locationPath: ''
          }
        }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('complex,data')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        complexData,
        mockSelectedHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      const generatedData = mockCsvInstance.generateCsv.mock.calls[0][0][0];
      expect(generatedData['Approved By']).toBe('SingleName');
      expect(generatedData.Equipment).toBe('Equipment 1, Equipment 2');
      expect(generatedData['Definable Feature of Work']).toBe('Work Type A');
      expect(generatedData.Gate).toBe('-');
      expect(generatedData['Responsible Company']).toBe('Company A');
      expect(generatedData['Responsible Person']).toBe('Member A');
      expect(generatedData.Location).toBe('');
    });

    it('should handle timezone offset correctly', async () => {
      const testData = [
        {
          DeliveryId: 4,
          deliveryStart: '2023-01-01T12:00:00Z',
          description: 'Timezone test'
        }
      ];

      const timezoneHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('id,date\n4,formatted_date')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      // Test with positive timezone offset
      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        testData,
        timezoneHeaders,
        120, // +2 hours
        mockFileName,
        mockExportType,
        mockDone
      );

      const generatedData = mockCsvInstance.generateCsv.mock.calls[0][0][0];
      expect(generatedData['Date & Time']).toMatch(/Jan-01-2023/);
    });

    it('should handle approver details edge cases', async () => {
      const approverTestData = [
        {
          DeliveryId: 5,
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: ''
            }
          }
        },
        {
          DeliveryId: 6,
          approverDetails: {
            User: {
              firstName: '',
              lastName: ''
            }
          }
        },
        {
          DeliveryId: 7,
          approverDetails: {
            User: null
          }
        },
        {
          DeliveryId: 8,
          approverDetails: null
        }
      ];

      const approverHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('id,approvedby\n5,John')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        approverTestData,
        approverHeaders,
        0,
        mockFileName,
        mockExportType,
        mockDone
      );

      const generatedDataArray = mockCsvInstance.generateCsv.mock.calls[0][0];
      expect(generatedDataArray[0]['Approved By']).toBe('John');
      expect(generatedDataArray[1]['Approved By']).toBe('-');
      expect(generatedDataArray[2]['Approved By']).toBe('-');
      expect(generatedDataArray[3]['Approved By']).toBe('-');
    });

    it('should handle empty selectedHeaders array', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        mockData,
        [],
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      const generatedData = mockCsvInstance.generateCsv.mock.calls[0][0][0];
      expect(generatedData).toEqual({});
    });

    it('should handle all headers inactive', async () => {
      const inactiveHeaders = mockSelectedHeaders.map(header => ({
        ...header,
        isActive: false
      }));

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
        mockData,
        inactiveHeaders,
        mockTimezoneOffset,
        mockFileName,
        mockExportType,
        mockDone
      );

      const generatedData = mockCsvInstance.generateCsv.mock.calls[0][0][0];
      expect(generatedData).toEqual({});
    });
  });
});
