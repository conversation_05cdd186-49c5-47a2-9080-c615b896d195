const craneRequestAttachmentService = require('../craneRequestAttachmentService');
const helper = require('../../helpers/domainHelper');
const notificationHelper = require('../../helpers/notificationHelper');
const pushNotification = require('../../config/fcm');
const awsConfig = require('../../middlewares/awsConfig');
const MAILER = require('../../mailer');

// Import the mocked models
const {
    Sequelize,
    Enterprise,
    NotificationPreference,
    Locations,
    LocationNotificationPreferences,
    CraneRequest,
    CraneRequestHistory,
    Member,
    User,
    CraneRequestResponsiblePerson,
    DeliveryPersonNotification,
    Project,
    Notification,
    CraneRequestAttachment
} = require('../../models');

// Mock all external dependencies
jest.mock('../../helpers/domainHelper');
jest.mock('../../helpers/notificationHelper');
jest.mock('../../config/fcm');
jest.mock('../../middlewares/awsConfig');
jest.mock('../../mailer');
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            ne: 'ne',
            notIn: 'notIn',
            and: 'and'
        },
        and: jest.fn()
    },
    Enterprise: {
        findOne: jest.fn()
    },
    NotificationPreference: {
        findAll: jest.fn()
    },
    Locations: {
        findOne: jest.fn()
    },
    LocationNotificationPreferences: {
        findAll: jest.fn()
    },
    CraneRequest: {
        findOne: jest.fn(),
        createInstance: jest.fn()
    },
    CraneRequestHistory: {
        createInstance: jest.fn()
    },
    Member: {
        findOne: jest.fn(),
        findAll: jest.fn()
    },
    User: {
        findOne: jest.fn()
    },
    CraneRequestResponsiblePerson: {
        findAll: jest.fn()
    },
    DeliveryPersonNotification: {},
    Project: {
        findByPk: jest.fn()
    },
    Notification: {
        createInstance: jest.fn()
    },
    CraneRequestAttachment: {
        findAll: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        createMultipleInstance: jest.fn()
    }
}));

describe('craneRequestAttachmentService - 100% Coverage Tests', () => {
    let mockInputData;
    let mockUser;
    let mockEnterprise;
    let mockCraneRequest;
    let mockMember;
    let mockLocation;
    let mockNotification;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common mock data
        mockUser = {
            id: 1,
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            profilePic: 'profile.jpg'
        };

        mockEnterprise = {
            id: 1,
            name: 'test-enterprise',
            status: 'completed'
        };

        mockCraneRequest = {
            id: 1,
            CraneRequestId: 'CR001',
            ProjectId: 1,
            LocationId: 1,
            description: 'Test Crane Request',
            memberDetails: []
        };

        mockMember = {
            id: 1,
            UserId: 1,
            ProjectId: 1,
            RoleId: 3,
            isDeleted: false,
            isActive: true,
            EnterpriseId: 1,
            isAccount: true
        };

        mockLocation = {
            id: 1,
            ProjectId: 1,
            locationPath: 'Test Location'
        };

        mockNotification = {
            id: 1,
            MemberId: 1
        };

        mockInputData = {
            user: mockUser,
            params: {
                CraneRequestId: 'CR001',
                ProjectId: 1,
                id: 1
            },
            body: {},
            domainName: 'test-enterprise'
        };

        // Setup common mock implementations
        helper.getDynamicModel.mockResolvedValue({
            CraneRequest,
            CraneRequestAttachment,
            CraneRequestHistory,
            CraneRequestResponsiblePerson,
            Member,
            User,
            Project,
            Notification,
            DeliveryPersonNotification
        });
        
        helper.returnProjectModel.mockResolvedValue({
            Member: Member,
            User: User
        });
    });

    // Test for line 83 - getEnterpriseValue with isAccount true
    describe('getEnterpriseValue - Line 83 Coverage', () => {
        it('should return enterprise when member isAccount is true', async () => {
            const mockUserData = { id: 1, email: '<EMAIL>' };
            const mockMemberData = { id: 1, UserId: 1, EnterpriseId: 1, isAccount: true };

            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn().mockResolvedValue(mockUserData) },
                Member: { findOne: jest.fn().mockResolvedValue(mockMemberData) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await craneRequestAttachmentService.getEnterpriseValue(mockInputData, 1);
            
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: mockMemberData.EnterpriseId, status: 'completed' }
            });
        });
    });

    // Test for line 88 - getEnterpriseValue fallback
    describe('getEnterpriseValue - Line 88 Coverage', () => {
        it('should return enterprise by ParentCompanyId when no email', async () => {
            const mockInputDataNoEmail = { ...mockInputData, user: { ...mockUser, email: null } };

            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await craneRequestAttachmentService.getEnterpriseValue(mockInputDataNoEmail, 1);
            
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' }
            });
        });
    });

    // Test for line 104 - getCraneRequestAttachements success callback
    describe('getCraneRequestAttachements - Line 104 Coverage', () => {
        it('should call done with attachments and false', async () => {
            const mockAttachments = [{ id: 1, filename: 'test.pdf' }];
            
            jest.spyOn(craneRequestAttachmentService, 'getDynamicModel').mockResolvedValue(true);
            
            // Mock the global variables that the service uses
            Object.assign(craneRequestAttachmentService, {
                CraneRequest: { findOne: jest.fn().mockResolvedValue(mockCraneRequest) },
                CraneRequestAttachment: { findAll: jest.fn().mockResolvedValue(mockAttachments) }
            });

            const done = jest.fn();
            await craneRequestAttachmentService.getCraneRequestAttachements(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockAttachments, false);
        });
    });

    // Test for lines 121-130 - deleteCraneRequestAttachement full flow
    describe('deleteCraneRequestAttachement - Lines 121-130 Coverage', () => {
        it('should successfully delete attachment and handle notifications', async () => {
            const mockAttachment = {
                id: 1,
                CraneRequestId: 1,
                CraneRequest: mockCraneRequest
            };

            jest.spyOn(craneRequestAttachmentService, 'getDynamicModel').mockResolvedValue(true);
            jest.spyOn(craneRequestAttachmentService, 'getMemberDetail').mockResolvedValue(mockMember);
            jest.spyOn(craneRequestAttachmentService, 'getMemberLocationPreference').mockResolvedValue([]);
            jest.spyOn(craneRequestAttachmentService, 'handleCraneRequestHistoryAndNotifications').mockResolvedValue();

            // Mock the global variables
            Object.assign(craneRequestAttachmentService, {
                CraneRequestAttachment: { 
                    findOne: jest.fn().mockResolvedValue(mockAttachment),
                    update: jest.fn().mockResolvedValue([1])
                },
                CraneRequest: { findOne: jest.fn().mockResolvedValue(mockCraneRequest) },
                Locations: { findOne: jest.fn().mockResolvedValue(mockLocation) }
            });

            const done = jest.fn();
            await craneRequestAttachmentService.deleteCraneRequestAttachement(mockInputData, done);

            expect(craneRequestAttachmentService.CraneRequestAttachment.update).toHaveBeenCalledWith(
                { isDeleted: true },
                { where: { id: mockInputData.params.id } }
            );
        });
    });

    // Test for lines 144-183 - getMemberDetail and related methods
    describe('getMemberDetail - Lines 144-183 Coverage', () => {
        it('should get member details successfully', async () => {
            Object.assign(craneRequestAttachmentService, {
                Member: { findOne: jest.fn().mockResolvedValue(mockMember) }
            });

            const result = await craneRequestAttachmentService.getMemberDetail(mockInputData, 1);
            
            expect(result).toEqual(mockMember);
            expect(craneRequestAttachmentService.Member.findOne).toHaveBeenCalledWith({
                where: [expect.any(Object)]
            });
        });
    });

    // Test for lines 244-270 - processNotifications and getCheckMemberNotification
    describe('processNotifications - Lines 244-270 Coverage', () => {
        it('should process notifications successfully', async () => {
            const mockHistory = { memberLocationPreference: [], type: 'attachment' };
            const mockCheckMemberNotification = [{ id: 1 }];

            Object.assign(craneRequestAttachmentService, {
                Project: { findByPk: jest.fn().mockResolvedValue({ id: 1 }) },
                NotificationPreference: { findAll: jest.fn().mockResolvedValue(mockCheckMemberNotification) },
                DeliveryPersonNotification: DeliveryPersonNotification
            });

            jest.spyOn(craneRequestAttachmentService, 'getCheckMemberNotification').mockResolvedValue(mockCheckMemberNotification);

            await craneRequestAttachmentService.processNotifications(
                mockNotification,
                mockCraneRequest,
                mockHistory,
                mockUser,
                mockMember,
                [],
                []
            );

            expect(notificationHelper.createDeliveryPersonNotification).toHaveBeenCalled();
            expect(pushNotification.sendPushNotificationForCrane).toHaveBeenCalled();
        });
    });

    // Test for lines 307-336 - file processing methods
    describe('processCraneRequestAttachmentAction - Lines 307-336 Coverage', () => {
        it('should process attachment action successfully', async () => {
            const mockContext = {
                action: 'Attached',
                result: [{ Location: 's3://test.pdf' }],
                loginUser: mockUser,
                memberDetail: mockMember,
                done: jest.fn()
            };
            const mockIncomeData = { ProjectId: 1 };
            const mockExist2 = { memberDetails: [] };
            const mockFiles = [{ originalname: 'test.pdf' }];

            Object.assign(craneRequestAttachmentService, {
                CraneRequestAttachment: { createMultipleInstance: jest.fn().mockResolvedValue() }
            });

            jest.spyOn(craneRequestAttachmentService, 'sendGuestEmailNotifications').mockResolvedValue();
            jest.spyOn(craneRequestAttachmentService, 'handleCraneRequestHistoryAndNotifications').mockResolvedValue();

            await craneRequestAttachmentService.processCraneRequestAttachmentAction(
                mockContext,
                { ...mockInputData, files: mockFiles },
                mockIncomeData,
                mockCraneRequest,
                mockExist2,
                mockLocation,
                []
            );

            expect(craneRequestAttachmentService.sendGuestEmailNotifications).toHaveBeenCalled();
            expect(craneRequestAttachmentService.CraneRequestAttachment.createMultipleInstance).toHaveBeenCalled();
            expect(craneRequestAttachmentService.handleCraneRequestHistoryAndNotifications).toHaveBeenCalled();
        });
    });
});
