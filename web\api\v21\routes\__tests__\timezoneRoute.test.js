const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  TimezoneController: {
    getTimezones: jest.fn(),
    setTimezone: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  timezoneValidation: {
    getTimezones: jest.fn(),
    setTimezone: jest.fn(),
  },
}));

describe('timezoneRoute', () => {
  let router;
  let timezoneRoute;
  let TimezoneController;
  let passportConfig;
  let timezoneValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    timezoneRoute = require('../timezoneRoute');
    const controllers = require('../../controllers');
    TimezoneController = controllers.TimezoneController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    timezoneValidation = validations.timezoneValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = timezoneRoute.router;
      
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Add specific route verifications based on actual routes
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = timezoneRoute.router;
      const result2 = timezoneRoute.router;
      
      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      timezoneRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];
      
      allCalls.forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof timezoneRoute).toBe('object');
      expect(timezoneRoute).toHaveProperty('router');
      
      const descriptor = Object.getOwnPropertyDescriptor(timezoneRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(timezoneRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});