module.exports = (sequelize, DataTypes) => {
    const CraneGate = sequelize.define(
        'CraneGate',
        {
            CraneRequestId: DataTypes.INTEGER,
            isDeleted: DataTypes.BOOLEAN,
            CraneRequestCode: DataTypes.INTEGER,
            publicSchemaId: {
                type: DataTypes.INTEGER,
            },
            GateId: DataTypes.INTEGER,
            ProjectId: DataTypes.INTEGER,
            isActive: DataTypes.BOOLEAN,
        },
        {},
    );
    CraneGate.associate = (models) => {
        // associations can be defined here
        CraneGate.belongsTo(models.CraneRequest, {
            as: 'Cranerequest',
            foreignKey: 'CraneRequestId',
        });
        CraneGate.belongsTo(models.Gates, {
            as: 'Gates',
            foreignKey: 'GateId',
        });
        CraneGate.belongsTo(models.Gates);
    };
    CraneGate.createInstance = async (paramData) => {
        const newCraneGate = await CraneGate.create(paramData);
        return newCraneGate;
    };
    return CraneGate;
};
