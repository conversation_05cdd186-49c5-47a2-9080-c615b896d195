const { Router } = require('express');
const multer = require('multer');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const mockMulter = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  return mockMulter;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  DefineController: {
    createDefinable: jest.fn(),
    getDefinable: jest.fn(),
    exportDefinable: jest.fn(),
    updateDefinable: jest.fn(),
    deleteDefinable: jest.fn(),
    addDefinable: jest.fn(),
    sampleExcelDownload: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  defineValidation: {
    createDefine: jest.fn(),
    getDefinable: jest.fn(),
    exportDefinable: jest.fn(),
    updateDefinable: jest.fn(),
    deleteDefinable: jest.fn(),
    addDefinable: jest.fn(),
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
}));

describe('defineRoute', () => {
  let router;
  let defineRoute;
  let DefineController;
  let passportConfig;
  let defineValidation;
  let checkAdmin;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    defineRoute = require('../defineRoute');
    const controllers = require('../../controllers');
    DefineController = controllers.DefineController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    defineValidation = validations.defineValidation;
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = defineRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer).toHaveBeenCalledWith({ dest: 'uploads/' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(6);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST routes with multer
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/create_definable/:ProjectId/?:ParentCompanyId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DefineController.createDefinable,
      );

      // Verify POST routes with validation only
      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/get_definable/:ProjectId/:pageSize/:pageNo/:sort/:export/:sortByField',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DefineController.getDefinable,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/update_definable/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DefineController.updateDefinable,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/delete_definable/',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DefineController.deleteDefinable,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/add_definable/',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DefineController.addDefinable,
      );

      // Verify POST route with admin check
      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/sample_dfow_excel_download/:ProjectId/',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        DefineController.sampleExcelDownload,
      );

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/export_definable/:ProjectId/:sort/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DefineController.exportDefinable,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(6);
      expect(validate).toHaveBeenCalledWith(
        defineValidation.createDefine,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        defineValidation.getDefinable,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        defineValidation.exportDefinable,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        defineValidation.updateDefinable,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        defineValidation.deleteDefinable,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        defineValidation.addDefinable,
        { keyByField: true },
        { abortEarly: false },
      );

      // Verify multer single method is called
      const uploadInstance = multer.mock.results[0].value;
      expect(uploadInstance.single).toHaveBeenCalledWith('definable');
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = defineRoute.router;
      const result2 = defineRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      defineRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure different middleware combinations correctly', () => {
      defineRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Route with multer + validation + auth
      expect(postCalls[0]).toHaveLength(5); // path + multer + validation + auth + controller
      expect(postCalls[0][1]).toBe('mocked-multer-middleware');
      expect(postCalls[0][2]).toBe('mocked-validate-middleware');

      // Routes with validation + auth only
      [postCalls[1], postCalls[2], postCalls[3], postCalls[4], getCalls[0]].forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // Route with auth + admin check only
      expect(postCalls[5]).toHaveLength(4); // path + auth + admin + controller
      expect(postCalls[5][1]).toBe(passportConfig.isAuthenticated);
      expect(postCalls[5][2]).toBe(checkAdmin.isProjectAdmin);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof defineRoute).toBe('object');
      expect(defineRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(defineRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(defineRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
