const crypto = require('crypto');

jest.mock('crypto');

describe('generatePassword', () => {
  beforeEach(() => {
    process.env.SERVICE_PASSWORDS = JSON.stringify({ genPass: '' });
    crypto.randomInt.mockImplementation((min, max) => min); // always pick first char
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should generate a password of length 10', () => {
    const { generatePassword } = require('../generatePassword');
    const password = generatePassword();
    expect(password.length).toBe(10);
  });
  it('should contain at least one alpha, int, and special char', () => {
    const { generatePassword } = require('../generatePassword');
    const password = generatePassword();
    expect(/[a-z]/.test(password)).toBe(true);
    expect(/[0-9]/.test(password)).toBe(true);
    expect(/[!@#_]/.test(password)).toBe(true);
  });
});
