const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  NotificationController: {
    listNotification: jest.fn(),
    deleteNotification: jest.fn(),
    setReadNotification: jest.fn(),
    getNotificationCount: jest.fn(),
    versionUpdate: jest.fn(),
    getVersion: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  notificationValidation: {
    listNotification: jest.fn(),
  },
}));

describe('notificationRoute', () => {
  let router;
  let notificationRoute;
  let NotificationController;
  let passportConfig;
  let notificationValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn(),
      get: jest.fn(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    notificationRoute = require('../notificationRoute');
    const controllers = require('../../controllers');
    NotificationController = controllers.NotificationController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    notificationValidation = validations.notificationValidation;
    validate = require('express-validation').validate;
  });

  describe('router', () => {
    it('should create a router instance', () => {
      const result = notificationRoute.router;
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);
    });

    it('should register POST /list_notification/:pageSize/:pageNo route with validation and authentication', () => {
      notificationRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/list_notification/:pageSize/:pageNo',
        'mocked-validate-middleware', // validate middleware
        passportConfig.isAuthenticated,
        NotificationController.listNotification,
      );

      expect(validate).toHaveBeenCalledWith(
        notificationValidation.listNotification,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should register GET /delete_notification route with authentication', () => {
      notificationRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/delete_notification',
        passportConfig.isAuthenticated,
        NotificationController.deleteNotification,
      );
    });

    it('should register GET /read_notification route with authentication', () => {
      notificationRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/read_notification',
        passportConfig.isAuthenticated,
        NotificationController.setReadNotification,
      );
    });

    it('should register GET /unread_count route with authentication', () => {
      notificationRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/unread_count',
        passportConfig.isAuthenticated,
        NotificationController.getNotificationCount,
      );
    });

    it('should register POST /version_update route with authentication', () => {
      notificationRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/version_update',
        passportConfig.isAuthenticated,
        NotificationController.versionUpdate,
      );
    });

    it('should register GET /current_version route with authentication', () => {
      notificationRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/current_version',
        passportConfig.isAuthenticated,
        NotificationController.getVersion,
      );
    });
  });
});
