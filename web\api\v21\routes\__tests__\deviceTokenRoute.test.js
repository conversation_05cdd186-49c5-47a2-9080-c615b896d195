const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  DeviceTokenController: {
    setDeviceToken: jest.fn(),
    clearDeviceToken: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  deviceTokenValidation: {
    setDeviceToken: jest.fn(),
  },
}));

describe('deviceTokenRoute', () => {
  let router;
  let deviceRoute;
  let DeviceTokenController;
  let passportConfig;
  let deviceTokenValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    deviceRoute = require('../deviceTokenRoute');
    const controllers = require('../../controllers');
    DeviceTokenController = controllers.DeviceTokenController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    deviceTokenValidation = validations.deviceTokenValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = deviceRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST route with validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/set_device_token',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeviceTokenController.setDeviceToken,
      );

      // Verify GET route without validation
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/clear_device_token',
        passportConfig.isAuthenticated,
        DeviceTokenController.clearDeviceToken,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(1);
      expect(validate).toHaveBeenCalledWith(
        deviceTokenValidation.setDeviceToken,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = deviceRoute.router;
      const result2 = deviceRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      deviceRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure different middleware combinations correctly', () => {
      deviceRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // POST route should have validation + auth (4 parameters)
      expect(postCalls[0]).toHaveLength(4); // path + validation + auth + controller
      expect(postCalls[0][1]).toBe('mocked-validate-middleware');

      // GET route should have auth only (3 parameters)
      expect(getCalls[0]).toHaveLength(3); // path + auth + controller
      expect(getCalls[0][1]).toBe(passportConfig.isAuthenticated);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof deviceRoute).toBe('object');
      expect(deviceRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(deviceRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(deviceRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});