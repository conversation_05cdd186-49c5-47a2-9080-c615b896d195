const moment = require('moment');
const { ProjectSettings, Member, Project, TimeZone } = require('../models');
const ProjectSettingsService = require('../projectSettingsService');

// Mock moment-timezone
jest.mock('moment', () => {
    const mockMoment = {
        tz: jest.fn(() => ({
            add: jest.fn(() => ({
                format: jest.fn(() => '2023-12-01 10:00:00+00:00'),
            })),
        })),
    };
    return mockMoment;
});

// Mock the models
jest.mock('../models', () => ({
    ProjectSettings: {
        findOne: jest.fn(),
        update: jest.fn(),
    },
    Member: {
        findAll: jest.fn(),
        update: jest.fn(),
    },
    Project: {},
    TimeZone: {},
}));

describe('ProjectSettingsService', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('getProjectSettings', () => {
        const mockProjectId = 1;
        const mockProjectSettings = {
            id: 1,
            ProjectId: mockProjectId,
            deliveryWindowTime: 2,
            deliveryWindowTimeUnit: 'hours',
            inspectionWindowTime: 1,
            inspectionWindowTimeUnit: 'days',
            craneWindowTime: 3,
            craneWindowTimeUnit: 'hours',
            concreteWindowTime: 4,
            concreteWindowTimeUnit: 'hours',
            isPdfUploaded: true,
            pdfOriginalName: 'test.pdf',
            convertedImageLinks: JSON.stringify(['link1', 'link2']),
            isDefaultColor: true,
            useTextColorAsLegend: true,
            deliveryCard: 'default',
            craneCard: 'default',
            concreteCard: 'default',
            inspectionCard: 'default',
            workingWindowStartHours: 9,
            workingWindowStartMinutes: 0,
            workingWindowEndHours: 17,
            workingWindowEndMinutes: 0,
        };

        const mockAutoApproveMembers = [
            {
                id: 1,
                firstName: 'John',
                isAutoApproveEnabled: true,
                memberId: 'M1',
                User: {
                    email: '<EMAIL>',
                    isAccount: true,
                    firstName: 'John',
                    lastName: 'Doe',
                    versionFlag: 1,
                },
            },
        ];

        it('should successfully get project settings with PDF', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockProjectSettings);
            Member.findAll.mockResolvedValue(mockAutoApproveMembers);

            const result = await ProjectSettingsService.getProjectSettings(mockProjectId);

            expect(ProjectSettings.findOne).toHaveBeenCalledWith({
                where: { ProjectId: mockProjectId },
                attributes: expect.any(Array),
                raw: true,
            });

            expect(Member.findAll).toHaveBeenCalledWith({
                where: {
                    ProjectId: mockProjectId,
                    isAutoApproveEnabled: true,
                },
                attributes: ['id', 'firstName', 'isAutoApproveEnabled', 'memberId'],
                include: expect.any(Array),
            });

            expect(result.projectSettings).toEqual({
                ...mockProjectSettings,
                fileExtension: 'pdf',
                pdfToImageLinks: ['link1', 'link2'],
            });
            expect(result.autoApproveMembers).toEqual(mockAutoApproveMembers);
        });

        it('should handle project settings without PDF', async () => {
            const settingsWithoutPdf = { ...mockProjectSettings, isPdfUploaded: false };
            ProjectSettings.findOne.mockResolvedValue(settingsWithoutPdf);
            Member.findAll.mockResolvedValue([]);

            const result = await ProjectSettingsService.getProjectSettings(mockProjectId);

            expect(result.projectSettings).toEqual({
                ...settingsWithoutPdf,
                fileExtension: null,
                pdfToImageLinks: [],
            });
            expect(result.autoApproveMembers).toEqual([]);
        });

        it('should handle empty converted image links', async () => {
            const settingsWithEmptyLinks = {
                ...mockProjectSettings,
                convertedImageLinks: null,
            };
            ProjectSettings.findOne.mockResolvedValue(settingsWithEmptyLinks);
            Member.findAll.mockResolvedValue([]);

            const result = await ProjectSettingsService.getProjectSettings(mockProjectId);

            expect(result.projectSettings.pdfToImageLinks).toEqual([]);
        });

        it('should handle database errors', async () => {
            ProjectSettings.findOne.mockRejectedValue(new Error('Database error'));

            await expect(ProjectSettingsService.getProjectSettings(mockProjectId)).rejects.toThrow(
                'Database error',
            );
        });
    });

    describe('updateProjectSettings', () => {
        const mockProjectId = 1;
        const mockPayload = {
            ProjectId: mockProjectId,
            deliveryWindowTime: 2,
            deliveryWindowTimeUnit: 'hours',
            enabledUser: [1, 2],
            disabledUser: [3, 4],
        };

        const mockExistingSettings = {
            id: 1,
            ProjectId: mockProjectId,
            defaultStatusColor: '#000000',
            defaultDeliveryCard: 'default',
            defaultCraneCard: 'default',
            defaultConcreteCard: 'default',
            defaultInspectionCard: 'default',
            save: jest.fn(),
        };

        it('should successfully update project settings', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);
            Member.update.mockResolvedValue([1]);

            const result = await ProjectSettingsService.updateProjectSettings(mockPayload);

            expect(ProjectSettings.findOne).toHaveBeenCalledWith({
                where: { ProjectId: mockProjectId },
            });
            expect(ProjectSettings.update).toHaveBeenCalledWith(
                expect.objectContaining({
                    deliveryWindowOpentime: expect.any(String),
                }),
                { where: { ProjectId: mockProjectId } },
            );
            expect(Member.update).toHaveBeenCalledTimes(2); // For enabled and disabled users
            expect(result).toEqual([1]);
        });

        it('should handle default color update', async () => {
            const payloadWithDefaultColor = {
                ...mockPayload,
                isDefaultColor: true,
            };

            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            await ProjectSettingsService.updateProjectSettings(payloadWithDefaultColor);

            expect(mockExistingSettings.save).toHaveBeenCalled();
            expect(mockExistingSettings.statusColorCode).toBe(mockExistingSettings.defaultStatusColor);
            expect(mockExistingSettings.isDefaultColor).toBe(true);
            expect(mockExistingSettings.useTextColorAsLegend).toBe(true);
        });

        it('should handle default card update', async () => {
            const payloadWithDefaultCard = {
                ...mockPayload,
                setDefaultCard: true,
            };

            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            await ProjectSettingsService.updateProjectSettings(payloadWithDefaultCard);

            expect(mockExistingSettings.save).toHaveBeenCalled();
            expect(mockExistingSettings.deliveryCard).toBe(mockExistingSettings.defaultDeliveryCard);
            expect(mockExistingSettings.craneCard).toBe(mockExistingSettings.defaultCraneCard);
            expect(mockExistingSettings.concreteCard).toBe(mockExistingSettings.defaultConcreteCard);
            expect(mockExistingSettings.inspectionCard).toBe(mockExistingSettings.defaultInspectionCard);
        });

        it('should handle window time updates', async () => {
            const payloadWithAllWindows = {
                ...mockPayload,
                deliveryWindowTime: 2,
                deliveryWindowTimeUnit: 'hours',
                craneWindowTime: 3,
                craneWindowTimeUnit: 'hours',
                concreteWindowTime: 4,
                concreteWindowTimeUnit: 'hours',
                inspectionWindowTime: 1,
                inspectionWindowTimeUnit: 'days',
            };

            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            await ProjectSettingsService.updateProjectSettings(payloadWithAllWindows);

            expect(ProjectSettings.update).toHaveBeenCalledWith(
                expect.objectContaining({
                    deliveryWindowOpentime: expect.any(String),
                    craneWindowOpentime: expect.any(String),
                    concreteWindowOpentime: expect.any(String),
                    inspectionWindowOpentime: expect.any(String),
                }),
                { where: { ProjectId: mockProjectId } },
            );
        });

        it('should handle database errors', async () => {
            ProjectSettings.findOne.mockRejectedValue(new Error('Database error'));

            const result = await ProjectSettingsService.updateProjectSettings(mockPayload);
            expect(result).toBeInstanceOf(Error);
            expect(result.message).toBe('Database error');
        });

        it('should handle empty enabled/disabled users', async () => {
            const payloadWithoutUsers = {
                ...mockPayload,
                enabledUser: [],
                disabledUser: [],
            };

            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            await ProjectSettingsService.updateProjectSettings(payloadWithoutUsers);

            expect(Member.update).not.toHaveBeenCalled();
        });

        // Additional comprehensive test cases for better coverage
        it('should handle null project settings data with isDefaultColor', async () => {
            ProjectSettings.findOne.mockResolvedValue(null);

            const result = await ProjectSettingsService.updateProjectSettings({
                ...mockPayload,
                isDefaultColor: true,
            });

            expect(result).toBeInstanceOf(Error);
            expect(result.message).toContain('Cannot read');
        });

        it('should handle null project settings data without flags', async () => {
            ProjectSettings.findOne.mockResolvedValue(null);
            ProjectSettings.update.mockResolvedValue([1]);

            const result = await ProjectSettingsService.updateProjectSettings({
                ProjectId: mockProjectId,
                deliveryWindowTime: 2,
                deliveryWindowTimeUnit: 'hours',
            });

            expect(result).toEqual([1]);
        });

        it('should handle save method failure', async () => {
            const mockSettingsWithFailingSave = {
                ...mockExistingSettings,
                save: jest.fn().mockRejectedValue(new Error('Save failed')),
            };

            ProjectSettings.findOne.mockResolvedValue(mockSettingsWithFailingSave);

            const result = await ProjectSettingsService.updateProjectSettings({
                ...mockPayload,
                isDefaultColor: true,
            });

            expect(result).toBeInstanceOf(Error);
            expect(result.message).toBe('Save failed');
        });

        it('should handle ProjectSettings.update failure', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockRejectedValue(new Error('Update failed'));

            const result = await ProjectSettingsService.updateProjectSettings(mockPayload);

            expect(result).toBeInstanceOf(Error);
            expect(result.message).toBe('Update failed');
        });

        it('should handle Member.update failure for enabled users', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);
            Member.update.mockRejectedValue(new Error('Member update failed'));

            const result = await ProjectSettingsService.updateProjectSettings({
                ...mockPayload,
                enabledUser: [1, 2],
            });

            expect(result).toBeInstanceOf(Error);
            expect(result.message).toBe('Member update failed');
        });

        it('should handle both isDefaultColor and setDefaultCard flags', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            await ProjectSettingsService.updateProjectSettings({
                ...mockPayload,
                isDefaultColor: true,
                setDefaultCard: true,
            });

            expect(mockExistingSettings.save).toHaveBeenCalledTimes(2);
            expect(mockExistingSettings.statusColorCode).toBe(mockExistingSettings.defaultStatusColor);
            expect(mockExistingSettings.deliveryCard).toBe(mockExistingSettings.defaultDeliveryCard);
        });

        it('should handle null window times', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            const payloadWithNullTimes = {
                ...mockPayload,
                deliveryWindowTime: null,
                craneWindowTime: null,
                concreteWindowTime: null,
                inspectionWindowTime: null,
            };

            await ProjectSettingsService.updateProjectSettings(payloadWithNullTimes);

            expect(ProjectSettings.update).toHaveBeenCalledWith(
                expect.not.objectContaining({
                    deliveryWindowOpentime: expect.any(String),
                    craneWindowOpentime: expect.any(String),
                    concreteWindowOpentime: expect.any(String),
                    inspectionWindowOpentime: expect.any(String),
                }),
                { where: { ProjectId: mockProjectId } },
            );
        });

        it('should handle undefined enabledUser and disabledUser', async () => {
            ProjectSettings.findOne.mockResolvedValue(mockExistingSettings);
            ProjectSettings.update.mockResolvedValue([1]);

            const payloadWithoutUserArrays = {
                ProjectId: mockProjectId,
                deliveryWindowTime: 2,
                deliveryWindowTimeUnit: 'hours',
            };

            await ProjectSettingsService.updateProjectSettings(payloadWithoutUserArrays);

            expect(Member.update).not.toHaveBeenCalled();
        });
    });

    // Additional test cases for getProjectSettings method
    describe('getProjectSettings - Additional Coverage', () => {
        const mockProjectId = 1;

        it('should handle null project settings', async () => {
            ProjectSettings.findOne.mockResolvedValue(null);
            Member.findAll.mockResolvedValue([]);

            await expect(ProjectSettingsService.getProjectSettings(mockProjectId)).rejects.toThrow();
        });

        it('should handle different file extensions', async () => {
            const mockSettingsWithDocx = {
                id: 1,
                ProjectId: mockProjectId,
                isPdfUploaded: true,
                pdfOriginalName: 'test.docx',
                convertedImageLinks: JSON.stringify(['link1']),
            };

            ProjectSettings.findOne.mockResolvedValue(mockSettingsWithDocx);
            Member.findAll.mockResolvedValue([]);

            const result = await ProjectSettingsService.getProjectSettings(mockProjectId);

            expect(result.projectSettings.fileExtension).toBe('docx');
            expect(result.projectSettings.pdfToImageLinks).toEqual(['link1']);
        });

        it('should handle missing file extension', async () => {
            const mockSettingsWithoutExtension = {
                id: 1,
                ProjectId: mockProjectId,
                isPdfUploaded: true,
                pdfOriginalName: 'testfile',
                convertedImageLinks: JSON.stringify(['link1']),
            };

            ProjectSettings.findOne.mockResolvedValue(mockSettingsWithoutExtension);
            Member.findAll.mockResolvedValue([]);

            const result = await ProjectSettingsService.getProjectSettings(mockProjectId);

            expect(result.projectSettings.fileExtension).toBe('testfile');
        });

        it('should handle invalid JSON in convertedImageLinks', async () => {
            const mockSettingsWithInvalidJson = {
                id: 1,
                ProjectId: mockProjectId,
                isPdfUploaded: true,
                pdfOriginalName: 'test.pdf',
                convertedImageLinks: 'invalid json',
            };

            ProjectSettings.findOne.mockResolvedValue(mockSettingsWithInvalidJson);
            Member.findAll.mockResolvedValue([]);

            await expect(ProjectSettingsService.getProjectSettings(mockProjectId)).rejects.toThrow();
        });

        it('should handle Member.findAll failure', async () => {
            const mockSettings = {
                id: 1,
                ProjectId: mockProjectId,
                isPdfUploaded: false,
            };

            ProjectSettings.findOne.mockResolvedValue(mockSettings);
            Member.findAll.mockRejectedValue(new Error('Member query failed'));

            await expect(ProjectSettingsService.getProjectSettings(mockProjectId)).rejects.toThrow(
                'Member query failed',
            );
        });

        it('should handle empty string convertedImageLinks', async () => {
            const mockSettingsWithEmptyString = {
                id: 1,
                ProjectId: mockProjectId,
                isPdfUploaded: true,
                pdfOriginalName: 'test.pdf',
                convertedImageLinks: '',
            };

            ProjectSettings.findOne.mockResolvedValue(mockSettingsWithEmptyString);
            Member.findAll.mockResolvedValue([]);

            const result = await ProjectSettingsService.getProjectSettings(mockProjectId);

            expect(result.projectSettings.pdfToImageLinks).toEqual([]);
        });
    });
});
