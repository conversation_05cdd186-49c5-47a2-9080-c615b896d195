const moment = require('moment');
const Moment = require('moment');
const status = require('http-status');
const MomentRange = require('moment-range');
const ApiError = require('../helpers/apiError');

const momentRange = MomentRange.extendMoment(Moment);
const {
  Company,
  User,
  Sequelize,
  Member,
  Role,
  DeliveryRequest,
  VoidList,
  DeliverCompany,
  DeliverGate,
  CalendarSetting,
  DeliveryPerson,
  CraneRequest,
  CraneRequestResponsiblePerson,
  ConcreteRequest,
  TimeZone,
} = require('../models');
const {
  CraneRequestDefinableFeatureOfWork,
  CraneRequestCompany,
  CraneRequestEquipment,
  CraneRequestHistory,
} = require('../models');
const {
  Gates,
  Locations,
  Equipments,
  DeliverDefineWork,
  Project,
  DeliverEquipment,
  DeliverDefine,
  DeliverHistory,
  Notification,
  LocationNotificationPreferences,
  NotificationPreference,
  DeliveryPersonNotification,
  DeliverAttachement,
  DeliverComment,
  CraneRequestAttachment,
  CraneRequestComment,
  ConcreteRequestAttachment,
  ConcreteRequestComment,
} = require('../models');
const { ConcreteRequestResponsiblePerson } = require('../models');
const {
  ConcreteRequestHistory,
  ConcreteRequestCompany,
  ConcreteLocation,
  ConcreteMixDesign,
  ConcretePumpSize,
  ConcreteRequestLocation,
  ConcreteRequestMixDesign,
  ConcreteRequestPumpSize,
  RequestRecurrenceSeries,
} = require('../models');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const concreteRequestService = require('./concreteRequestService');
const MAILER = require('../mailer');
const deliveryService = require('./deliveryService');
const craneRequestService = require('./craneRequestService');
const commentService = require('./commentService');
const awsConfig = require('../middlewares/awsConfig');
const craneRequestCommentService = require('./craneRequestCommentService');
const concreteRequestCommentService = require('./concreteRequestCommentService');
const { TableHints } = require('sequelize');

const { Op } = Sequelize;
const guestUserService = {
  async getCompanies(req) {
    const { ProjectId } = req.params;
    const { ParentCompanyId } = req.params;
    const companyList = await Company.findAll({
      where: {
        isDeleted: false,
        [Op.or]: [
          { ParentCompanyId, ProjectId, isParent: { [Op.not]: true } },
          { ParentCompanyId, isParent: true },
        ],
      },
      attributes: ['id', 'companyName', 'ParentCompanyId', 'isParent'],
    });
    return { companyList };
  },

  async createGuestUser(req) {
    const userData = req.body;
    const existUser = await User.findOne({
      where: { isDeleted: false, email: userData.email },
    });

    if (existUser) {
      return await this.handleExistingUser(existUser, userData);
    } else {
      return await this.handleNewUser(userData);
    }
  },

  async handleExistingUser(existUser, userData) {
    // Check if user is already an active member
    const activeMemberError = await this.checkActiveMember(existUser, userData);
    if (activeMemberError) return activeMemberError;

    // Check and update if user is an active guest
    const updatedGuest = await this.updateExistingGuest(existUser, userData);
    if (updatedGuest) return { existUser };

    // If guest doesn't exist, create one
    const newGuest = await this.createGuestMember(userData, existUser);
    if (newGuest) return { existUser };

    // Handle inactive members
    const reactivatedMember = await this.reactivateMember(existUser, userData);
    if (reactivatedMember) return { existUser };

    // Handle deleted members
    const recreatedMember = await this.recreateDeletedMember(existUser, userData);
    if (recreatedMember) return { existUser };

    return null;
  },

  async handleNewUser(userData) {
    const newUser = await this.createNewGuestUser(userData);
    if (newUser) {
      return { newUser };
    }
    return null;
  },

  async checkActiveMember(existUser, userData) {
    const condition = {
      isDeleted: false,
      UserId: existUser.id,
      ProjectId: userData.ProjectId,
      isActive: true,
      isGuestUser: false,
    };
    const activeMember = await Member.findOne({ where: condition });
    if (activeMember) {
      return { activeMember: 'You are already active Member in this project!' };
    }
    return null;
  },

  async updateExistingGuest(existUser, userData) {
    const condition = {
      isDeleted: false,
      UserId: existUser.id,
      ProjectId: userData.ProjectId,
      isActive: true,
      isGuestUser: true,
      ParentCompanyId: userData.ParentCompanyId,
    };
    const guestMember = await Member.findOne({ where: condition });
    if (guestMember) {
      await User.update(
        {
          phoneCode: userData.phoneCode,
          phoneNumber: userData.phoneNumber,
          firstName: userData.firstName,
          lastName: userData.lastName,
        },
        { where: { id: +existUser.id, email: existUser.email } }
      );

      await Member.update(
        {
          phoneCode: userData.phoneCode,
          phoneNumber: userData.phoneNumber,
          firstName: userData.firstName,
          CompanyId: userData.companyId,
        },
        { where: { UserId: +existUser.id, ProjectId: +userData.ProjectId } }
      );
      return true;
    }
    return false;
  },

  async reactivateMember(existUser, userData) {
    const condition = {
      isDeleted: false,
      UserId: existUser.id,
      ProjectId: userData.ProjectId,
      isActive: false,
      isGuestUser: false,
    };
    const inactiveMember = await Member.findOne({ where: condition });
    if (inactiveMember) {
      await Member.update(
        {
          phoneCode: userData.phoneCode,
          phoneNumber: userData.phoneNumber,
          firstName: userData.firstName,
          CompanyId: userData.companyId,
          isGuestUser: true,
          isActive: true,
          status: 'pending',
        },
        { where: { UserId: +existUser.id, ProjectId: userData.ProjectId } }
      );
      return true;
    }
    return false;
  },

  async recreateDeletedMember(existUser, userData) {
    const condition = {
      isDeleted: true,
      UserId: existUser.id,
      ProjectId: userData.ProjectId,
      isGuestUser: false,
    };
    const deletedMember = await Member.findOne({ where: condition });
    if (deletedMember) {
      return await this.createGuestMember(userData, existUser);
    }
    return false;
  },

  async alreadyVisited(req) {
    const { email, ProjectId } = req.body;

    const checkIsActive = await User.findOne({
      where: {
        isDeleted: false,
        email,
      },
      include: [
        {
          association: 'Members',
          where: {
            ProjectId,
            isActive: true,
            isDeleted: false,
            isGuestUser: false,
          },
        },
      ],
    });
    if (checkIsActive) {
      return { activeMember: 'You are already a Member in this project!' };
    }
    const oldUser = await User.findOne({
      where: {
        isDeleted: false,
        email,
      },
      include: [
        {
          association: 'Members',
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
          where: {
            ProjectId: req.body.ProjectId,
            isGuestUser: true,
          },
        },
      ],
    });
    const data = oldUser;
    if (oldUser) {
      return { data };
    }
    return { newUser: 'newUser' };
  },
  async guestUserDetail(req) {
    const { email, ProjectId } = req.body;
    const oldUser = await User.findOne({
      where: {
        isDeleted: false,
        email,
      },
    });
    if (oldUser) {
      const memberDetail = await Member.findOne({
        where: {
          isDeleted: false,
          UserId: oldUser.id,
          ProjectId,
          isGuestUser: true,
        },
      });
      const data = memberDetail;
      return { data };
    }
    return {};
  },
  async lastDeliveryId(req) {
    let data;
    const lastData = await DeliveryRequest.findOne({
      where: { ProjectId: req.body.ProjectId, isDeleted: false },
      order: [['DeliveryId', 'DESC']],
    });
    if (lastData) {
      data = lastData.DeliveryId + 1;
    } else {
      data = 1;
    }
    return { DeliveryId: data };
  },
  async listAllMember(req) {
    const { ProjectId } = req.params;

    const data = await Member.findAll({
      include: [
        {
          association: 'User',
          attributes: ['email', 'firstName', 'lastName'],
        },
      ],
      where: { ProjectId, isDeleted: false, isActive: true },
      order: [['id', 'DESC']],
    });
    return data;
  },
  async createNewGuestUser(data) {
    const userData = data;
    const newUser = await User.createInstance(userData);
    if (newUser) {
      userData.isGuestUser = true;
      const createMember = await this.createGuestMember(userData, newUser);
      if (createMember) {
        return newUser;
      }
    }
  },
  async createGuestMember(userData, newUser) {
    const roleDetails = await Role.getBy('Guest User');
    const memberData = {
      UserId: +newUser.id,
      ParentCompanyId: +userData.ParentCompanyId,
      firstName: newUser.firstName,
      phoneNumber: newUser.phoneNumber,
      phoneCode: newUser.phoneCode,
      CompanyId: +userData.companyId,
      ProjectId: +userData.ProjectId,
      status: 'pending',
      isGuestUser: true,
      RoleId: +roleDetails.id,
    };
    const updatedMemberData = await Member.createInstance(memberData);
    if (!updatedMemberData) {
      throw new Error('Guest Member Creation failed!!');
    } else {
      return updatedMemberData;
    }
  },
  async getEventNDR(inputData) {
    try {
      const { timezoneoffset } = inputData.headers;
      const order = 'DESC';
      const { params } = inputData;
      const incomeData = inputData.body;

      // Step 1: Get member details
      const memberDetails = await Member.findOne({
        where: {
          UserId: incomeData.id,
          ProjectId: params.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });

      // Step 2: Build conditions
      const { condition, searchCondition } = await this.buildConditions(
        incomeData,
        params,
        memberDetails,
        timezoneoffset
      );

      // Step 3: Get delivery list
      const roleId = memberDetails?.RoleId;
      const memberId = memberDetails?.id;
      const deliveryList = await DeliveryRequest.getCalendarData(
        condition,
        roleId,
        memberId,
        searchCondition,
        order
      );

      // Step 4: Apply final filters if needed
      return await this.filterDeliveryList(deliveryList, incomeData, memberDetails, timezoneoffset);

    } catch (e) {
      throw new Error(e);
    }
  },

  async buildConditions(incomeData, params, memberDetails, timezoneoffset) {
    let searchCondition = {};

    const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);

    const condition = {
      ProjectId: params.ProjectId,
      isQueued: false,
      deliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };

    // Add void delivery conditions if member exists
    if (memberDetails) {
      const voidDelivery = await this.getVoidDelivery(params.ProjectId);
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [
          params.void === '0' || params.void === 0
            ? { [Op.notIn]: voidDelivery }
            : { [Op.in]: voidDelivery },
        ],
      };
    }

    // Additional filters
    if (incomeData.descriptionFilter) {
      condition.description = { [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%` };
    }
    if (incomeData.pickFrom) {
      condition.cranePickUpLocation = { [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%` };
    }
    if (incomeData.pickTo) {
      condition.craneDropOffLocation = { [Sequelize.Op.iLike]: `%${incomeData.pickTo}%` };
    }
    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }
    if (incomeData.memberFilter > 0) {
      condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }
    if (incomeData.locationFilter) {
      condition['$location.locationPath$'] = incomeData.locationFilter;
    }
    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }

    // Search condition
    if (incomeData.search) {
      const searchDefault = [
        { '$approverDetails.User.firstName$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
        { '$equipmentDetails.Equipment.equipmentName$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
        { description: { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
        { cranePickUpLocation: { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
        { craneDropOffLocation: { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
        { '$location.locationPath$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
      ];
      searchCondition = Number.isNaN(+incomeData.search)
        ? { [Op.and]: [{ [Op.or]: searchDefault }] }
        : {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        DeliveryId: +incomeData.search,
                        isDeleted: false,
                        ProjectId: +params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
    }

    return { condition, searchCondition };
  },

  async getVoidDelivery(ProjectId) {
    const voidList = await VoidList.findAll({
      where: {
        ProjectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    return voidList.map(element => element.DeliveryRequestId);
  },

  async filterDeliveryList(deliveryList, incomeData, memberDetails, timezoneoffset) {
    if (!deliveryList || deliveryList.rows.length === 0) return deliveryList;

    const result = { count: 0, rows: [] };

    if (
      (incomeData.companyFilter && incomeData.companyFilter > 0) ||
      (incomeData.gateFilter && incomeData.gateFilter > 0) ||
      (incomeData.memberFilter && incomeData.memberFilter > 0) ||
      incomeData.dateFilter
    ) {
      return new Promise((resolve) => {
        this.getSearchData(
          incomeData,
          deliveryList.rows,
          0,
          [],
          memberDetails,
          timezoneoffset,
          async (checkResponse, checkError) => {
            if (!checkError) {
              result.rows = checkResponse;
              result.count = checkResponse.length;
              resolve(result);
            } else {
              resolve(deliveryList);
            }
          },
        );
      });
    } else {
      return deliveryList;
    }
  },


  async filterDeliveryElement(element, incomeData, timezoneoffset) {
    const status = { companyCondition: true, gateCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      const companyData = await DeliverCompany.findOne({
        where: {
          DeliveryId: element.id,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      if (!companyData) status.companyCondition = false;
    }

    if (incomeData.gateFilter > 0) {
      const gateData = await DeliverGate.findOne({
        where: {
          DeliveryId: element.id,
          GateId: +incomeData.gateFilter,
          isDeleted: false,
        },
      });
      if (!gateData) status.gateCondition = false;
    }

    if (status.companyCondition && status.gateCondition && status.memberCondition) {
      if (incomeData.dateFilter) {
        const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
          .startOf('day')
          .utcOffset(Number(timezoneoffset), true);
        const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
          .endOf('day')
          .utcOffset(Number(timezoneoffset), true);

        if (
          element &&
          moment(element.deliveryStart)
            .utcOffset(Number(timezoneoffset))
            .isBetween(startDateTime, endDateTime, undefined, '()')
        ) {
          return true;
        }
      } else {
        return true;
      }
    }

    return false;
  },

  async getSearchData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done
  ) {
    if (deliveryList.length === 0) {
      return done(deliveryList, false);
    }

    const elementValue = deliveryList[index];
    const element = JSON.parse(JSON.stringify(elementValue));

    const isValid = await this.filterDeliveryElement(element, incomeData, timezoneoffset);
    if (isValid) result.push(element);

    if (index < deliveryList.length - 1) {
      this.getSearchData(
        incomeData,
        deliveryList,
        index + 1,
        result,
        memberDetails,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },

  extractFilters(req) {
    const { ProjectId, ParentCompanyId, search, weeklyReportTest, start, end } = req.query;
    const {
      isApplicableToDelivery,
      isApplicableToCrane,
      isApplicableToConcrete,
      timezone,
      eventStartTime,
      eventEndTime,
      startDate,
      endDate,
      ...loginUser
    } = req.body;

    return {
      ProjectId,
      ParentCompanyId,
      search,
      weeklyReportTest,
      start,
      end,
      isApplicableToDelivery,
      isApplicableToCrane,
      isApplicableToConcrete,
      timezone,
      eventStartTime,
      eventEndTime,
      startDate,
      endDate,
      loginUser,
    };
  },

  async validateMemberAccess(ProjectId, ParentCompanyId, loginUser) {
    return await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId,
        isDeleted: false,
        ParentCompanyId,
      }),
    });
  },

  buildCondition(filters) {
    const condition = {
      ProjectId: filters.ProjectId,
      isDeleted: false,
    };

    if (filters.search) {
      condition.description = { [Op.iLike]: `%${filters.search}%` };
    }

    if (filters.isApplicableToDelivery) {
      condition.isApplicableToDelivery = true;
    }

    if (filters.isApplicableToCrane) {
      condition.isApplicableToCrane = true;
    }

    if (filters.isApplicableToConcrete) {
      condition.isApplicableToConcrete = true;
    }

    return condition;
  },

  filterEventsByDateAndTime(eventsArray, filters, mode) {
    const filtered = [];
    const { timezone, eventStartTime, eventEndTime, startDate, endDate, start, end } = filters;

    const startD = moment(startDate || start).format('YYYY-MM-DD');
    const endD = moment(endDate || end).format('YYYY-MM-DD');
    const nextDay = moment(startD).add(1, 'days').format('YYYY-MM-DD');

    for (const data of eventsArray) {
      const target = momenttz.utc(data.fromDate, 'YYYY-MM-DD HH:mm:ssZ').tz(timezone);
      const timeRange = target.format('HH:mm');
      const date = moment(target.format('YYYY-MM-DD'));

      const timeCondition =
        eventStartTime < eventEndTime
          ? timeRange >= eventStartTime && timeRange <= eventEndTime
          : (timeRange >= eventStartTime && timeRange <= '23:59:59') ||
            (date.isSame(nextDay) && timeRange <= eventEndTime);

      if (date.isBetween(startD, endD, null, '[]') && timeCondition) {
        filtered.push(data);
      }
    }

    return filtered;
  },

  async getAll(req, next) {
    try {
      const filters = this.extractFilters(req);

      const isMemberExists = await this.validateMemberAccess(
        filters.ProjectId,
        filters.ParentCompanyId,
        filters.loginUser
      );

      if (!isMemberExists) return [];

      const condition = this.buildCondition(filters);
      let events = filters.weeklyReportTest === 'weeklyReport'
        ? await CalendarSetting.getAllWeeklyReport({ ProjectId: +filters.ProjectId, isDeleted: false })
        : await CalendarSetting.getAll(condition);

      if (!events.length) return [];

      const eventsArray = await this.generateEventsArray(events, this.createRecurrenceObject);

      if (!eventsArray.length) return [];

      if (filters.weeklyReportTest === 'weeklyReport') {
        return this.filterEventsByDateAndTime(eventsArray, filters, 'weekly');
      } else {
        return this.filterEventsByDateAndTime(eventsArray, filters, 'default');
      }
    } catch (e) {
      next(new ApiError(e, status.BAD_REQUEST));
    }
  },

  buildRange(fromDate, toDate) {
    const range = momentRange.range(moment(fromDate), moment(toDate));
    return Array.from(range.by('day'));
  },

  async handleDoesNotRepeat(event, eventTimeZone, createRecurrenceObject, uniqueId) {
    const totalDays = this.buildRange(event.fromDate, event.toDate);
    const startDate = totalDays[0];
    const endDate = totalDays[totalDays.length - 1];

    return await Promise.all(
      totalDays.map((date, i) =>
        createRecurrenceObject(
          event,
          eventTimeZone,
          date.toDate(),
          startDate,
          endDate,
          uniqueId + i + 1,
          event.recurrence
        )
      )
    );
  },

  async handleDaily(event, eventTimeZone, createRecurrenceObject, uniqueId) {
    const totalDays = this.buildRange(event.fromDate, event.toDate);
    const startDate = totalDays[0];
    const endDate = totalDays[totalDays.length - 1];

    const instances = [];
    for (let i = 0; i < totalDays.length; i += +event.repeatEveryCount) {
      instances.push(
        await createRecurrenceObject(
          event,
          eventTimeZone,
          totalDays[i].toDate(),
          startDate,
          endDate,
          uniqueId + instances.length + 1,
          event.recurrence
        )
      );
    }
    return instances;
  },

  async handleWeekly(event, eventTimeZone, createRecurrenceObject, uniqueId) {
    const startWeek = moment(event.fromDate).startOf('week');
    const endWeek = moment(event.endDate).endOf('week');
    const totalDays = this.buildRange(startWeek, endWeek);
    const startDate = moment(event.fromDate);
    const endDate = moment(event.endDate);

    const increment = (+event.repeatEveryCount || 1) * 7;
    const instances = [];

    for (let i = 0; i < totalDays.length; i += increment) {
      for (let j = 0; j < 7 && (i + j) < totalDays.length; j++) {
        const date = totalDays[i + j];
        if (
          date &&
          !moment(date).isBefore(event.fromDate) &&
          !moment(date).isAfter(event.endDate)
        ) {
          const day = moment(date).format('dddd');
          if (event.days.includes(day)) {
            instances.push(
              await createRecurrenceObject(
                event,
                eventTimeZone,
                date.toDate(),
                startDate,
                endDate,
                uniqueId + instances.length + 1,
                event.recurrence
              )
            );
          }
        }
      }
    }
    return instances;
  },

  async handleMonthlyYearly(event, eventTimeZone, createRecurrenceObject, uniqueId) {
    const isMonthly = event.recurrence === 'Monthly';
    const incrementUnit = isMonthly ? 'month' : 'year';
    const incrementValue = isMonthly ? +event.repeatEveryCount : 1;

    let current = moment(event.fromDate).startOf('month');
    const end = moment(event.endDate).endOf('month');

    const instances = [];

    while (current.isBefore(end)) {
      const monthDates = Array.from(
        { length: current.daysInMonth() },
        (_, i) => current.clone().startOf('month').add(i, 'days')
      );

      let eventDate = null;

      if (event.chosenDateOfMonth) {
        eventDate = monthDates.find(
          d => d.format('DD') === event.dateOfMonth
        );
      } else if (event.monthlyRepeatType) {
        const [week, day] = event.monthlyRepeatType.split(' ');
        const targetDay = current.clone().startOf('month').day(day.toLowerCase());
        const weeklyDays = [];

        if (targetDay.date() > 7) targetDay.add(7, 'd');

        while (targetDay.month() === current.month()) {
          weeklyDays.push(targetDay.clone());
          targetDay.add(7, 'd');
        }

        const weekIndex = {
          first: 0,
          second: 1,
          third: 2,
          fourth: 3,
          last: weeklyDays.length - 1,
        }[week.toLowerCase()];

        eventDate = weeklyDays[weekIndex];
      }

      if (
        eventDate &&
        moment(eventDate).isBetween(event.fromDate, event.endDate, null, '[]')
      ) {
        instances.push(
          await createRecurrenceObject(
            event,
            eventTimeZone,
            eventDate.toDate(),
            moment(event.fromDate),
            moment(event.endDate),
            uniqueId + instances.length + 1,
            event.recurrence
          )
        );
      }

      current.add(incrementValue, incrementUnit);
    }

    return instances;
  },

  async handleRecurrence(event, eventTimeZone, createRecurrenceObject, uniqueId) {
    switch (event.recurrence) {
      case 'Does Not Repeat':
        return await this.handleDoesNotRepeat(event, eventTimeZone, createRecurrenceObject, uniqueId);
      case 'Daily':
        return await this.handleDaily(event, eventTimeZone, createRecurrenceObject, uniqueId);
      case 'Weekly':
        return await this.handleWeekly(event, eventTimeZone, createRecurrenceObject, uniqueId);
      case 'Monthly':
      case 'Yearly':
        return await this.handleMonthlyYearly(event, eventTimeZone, createRecurrenceObject, uniqueId);
      default:
        return [];
    }
  },

  async generateEventsArray(events, createRecurrenceObject) {
    const eventsArray = [];
    let uniqueNumber = 0;

    for (const eventObject of events) {
      const eventTimeZone = await TimeZone.findOne({
        where: { isDeleted: false, id: +eventObject.TimeZoneId },
        attributes: ['id', 'location', 'isDayLightSavingEnabled', 'timeZoneOffsetInMinutes', 'dayLightSavingTimeInMinutes', 'timezone'],
      });

      const recurrenceEvents = await this.handleRecurrence(
        eventObject,
        eventTimeZone,
        createRecurrenceObject,
        uniqueNumber
      );

      uniqueNumber += recurrenceEvents.length;
      eventsArray.push(...recurrenceEvents);
    }

    return eventsArray;
  },

  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
  async createRecurrenceObject(
    eventObject,
    eventTimeZone,
    data,
    startDate,
    endDate,
    uniqueNumber,
    recurrence,
  ) {
    const objectToAddEvents = {
      id: eventObject.id,
      description: eventObject.description,
      timeZoneLocation: eventObject.TimeZone.location,
      durationInMinutes: '',
      fromDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).startOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '00:00',
        )
        : await this.convertTimezoneToUtc(
          moment(data).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.startTime,
        ),
      toDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).endOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '23:59',
        )
        : await this.convertTimezoneToUtc(
          moment(data).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.endTime,
        ),
      startTime: await this.convertTimezoneToUtc(
        moment(startDate).format('MM/DD/YYYY'),
        eventTimeZone.timezone,
        eventObject.startTime,
      ),
      endTime: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).startOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '00:00',
        )
        : await this.convertTimezoneToUtc(
          moment(endDate).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.endTime,
        ),
      repeatEveryType: eventObject.repeatEveryType,
      repeatEveryCount: eventObject.repeatEveryCount,
      days: eventObject.days,
      isAllDay: eventObject.isAllDay,
      uniqueNumber,
      requestType: 'calendarEvent',
    };
    if (recurrence === 'Monthly' || recurrence === 'Yearly') {
      objectToAddEvents.chosenDateOfMonth = eventObject.chosenDateOfMonth;
      objectToAddEvents.dateOfMonth = eventObject.dateOfMonth;
      objectToAddEvents.monthlyRepeatType = eventObject.monthlyRepeatType;
    }
    objectToAddEvents.durationInMinutes = moment(objectToAddEvents.toDate)
      .diff(moment(objectToAddEvents.fromDate), 'minutes')
      .toString();
    return objectToAddEvents;
  },

  async getDeliveryRequestWithCrane(inputData) {
    try {
      const { timezoneoffset } = inputData.headers;
      const order = 'DESC';
      const { params } = inputData;
      const incomeData = inputData.body;
      const { sort, sortByField } = inputData.body;

      // Step 1: Get Member Details
      const memberDetails = await this.getMemberDetails(incomeData.id, params.ProjectId);
      if (!memberDetails) return [];

      // Step 2: Prepare Conditions
      const { DeliveryRequestCondition, craneDeliveryRequestCondition } = 
        await this.prepareRequestConditions(inputData, memberDetails, timezoneoffset);

      // Step 3: Fetch Data
      return await this.fetchCraneAndDeliveryData({
        inputData,
        memberDetails,
        DeliveryRequestCondition,
        craneDeliveryRequestCondition,
        order,
        sort,
        sortByField,
        timezoneoffset
    });

    } catch (e) {
      throw new Error(e);
    }
  },

  async getMemberDetails(userId, projectId) {
    return Member.findOne({
      where: Sequelize.and({
        UserId: userId,
        ProjectId: +projectId,
        isDeleted: false,
        isActive: true,
      }),
    });
  },

  async prepareRequestConditions(inputData, memberDetails, timezoneoffset) {
    const { params, body: incomeData } = inputData;
    const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);

    const DeliveryRequestCondition = {
      ProjectId: +params.ProjectId,
      isQueued: false,
      isAssociatedWithCraneRequest: true,
      deliveryStart: { [Op.between]: [moment(startDateTime), moment(endDateTime)] },
    };

    const craneDeliveryRequestCondition = {
      ProjectId: +params.ProjectId,
      craneDeliveryStart: { [Op.between]: [moment(startDateTime), moment(endDateTime)] },
    };

    // Handle void lists
    const voidDelivery = [];
    const voidCraneDelivery = [];
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: +params.ProjectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    voidList.forEach(element => voidDelivery.push(element.DeliveryRequestId));

    const voidCraneRequestList = await VoidList.findAll({
      where: {
        ProjectId: +params.ProjectId,
        isDeliveryRequest: false,
        CraneRequestId: { [Op.ne]: null },
      },
    });
    voidCraneRequestList.forEach(element => voidCraneDelivery.push(element.CraneRequestId));

    // Add void conditions
    const voidCondition = (params.void === '0' || params.void === 0)
      ? { [Op.notIn]: voidDelivery }
      : { [Op.in]: voidDelivery };

    const voidCraneCondition = (params.void === '0' || params.void === 0)
      ? { [Op.notIn]: voidCraneDelivery }
      : { [Op.in]: voidCraneDelivery };

    DeliveryRequestCondition['$DeliveryRequest.id$'] = { [Op.and]: [voidCondition] };
    craneDeliveryRequestCondition['$CraneRequest.id$'] = { [Op.and]: [voidCraneCondition] };

    return { DeliveryRequestCondition, craneDeliveryRequestCondition };
  },

  async fetchCraneAndDeliveryData(paramsData) {
    const {inputData, memberDetails, DeliveryRequestCondition, craneDeliveryRequestCondition, order, sort, sortByField, timezoneoffset} = paramsData;
    const incomeData = inputData.body;
    const { params } = inputData;
    const roleId = memberDetails.RoleId;
    const memberId = memberDetails.id;

    // Fetch crane requests
    let craneRequestList = [];
    if (!((incomeData.gateFilter && incomeData.gateFilter > 0) || (incomeData.statusFilter && incomeData.statusFilter === 'Delivered'))) {
      craneRequestList = await CraneRequest.getAll(
        inputData, roleId, memberId, craneDeliveryRequestCondition,
        incomeData.descriptionFilter, incomeData.startdate, incomeData.enddate,
        incomeData.companyFilter, incomeData.memberFilter, incomeData.equipmentFilter,
        incomeData.statusFilter, incomeData.idFilter, incomeData.pickFrom,
        incomeData.pickTo, incomeData.search, order, sort, sortByField,
        incomeData.dateFilter
      );
    }
    let craneRequestArray = craneRequestList.length > 0 ? craneRequestList : [];

    // Fetch delivery requests
    let deliveryList = [];
    if (!(incomeData.statusFilter && incomeData.statusFilter === 'Completed')) {
      deliveryList = await DeliveryRequest.getCraneAssociatedRequest(
        inputData, roleId, memberId, DeliveryRequestCondition,
        incomeData.descriptionFilter, incomeData.startdate, incomeData.enddate,
        incomeData.companyFilter, incomeData.memberFilter, incomeData.equipmentFilter,
        incomeData.statusFilter, incomeData.idFilter, incomeData.pickFrom,
        incomeData.pickTo, incomeData.search, incomeData.gateFilter, order,
        sort, sortByField, params.void, incomeData.dateFilter
      );
    }

    // Apply search filters
    if ((incomeData.companyFilter && incomeData.companyFilter > 0) ||
        (incomeData.gateFilter && incomeData.gateFilter > 0) ||
        (incomeData.memberFilter && incomeData.memberFilter > 0) ||
        incomeData.dateFilter) {
      return new Promise((resolve) => {
        this.getSearchCraneRequestCalendarData(
          incomeData, craneRequestArray, 0, [], memberDetails, timezoneoffset,
          async (checkResponse, checkError) => {
            if (checkError) return resolve({ message: 'Something went wrong' });
            craneRequestArray = checkResponse;
            this.getSearchData(
              incomeData, deliveryList, 0, [], memberDetails, timezoneoffset,
              async (checkResponse1, checkError1) => {
                if (!checkError1 && checkResponse1.length > 0) {
                  craneRequestArray.push(...checkResponse1);
                }
                resolve(craneRequestArray);
              }
            );
          }
        );
      });
    }

    if (deliveryList.length > 0) craneRequestArray.push(...deliveryList);
    return craneRequestArray;
  },


  async checkFilters(elementId, incomeData) {
    const status = { companyCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      const companyData = await CraneRequestCompany.findOne({
        where: {
          CraneRequestId: elementId,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      if (!companyData) {
        status.companyCondition = false;
      }
    }

    if (incomeData.memberFilter > 0) {
      const memberData = await CraneRequestResponsiblePerson.findOne({
        where: {
          CraneRequestId: elementId,
          MemberId: incomeData.memberFilter,
          isDeleted: false,
        },
      });
      if (!memberData) {
        status.memberCondition = false;
      }
    }

    return status;
  },

  async processDeliveryElement(incomeData, deliveryList, index, result) {
    const elementValue = deliveryList[index];
    const element = JSON.parse(JSON.stringify(elementValue));
    const status = await this.checkFilters(element.id, incomeData);

    if (status.companyCondition && status.memberCondition) {
      result.push(element);
    }
  },

  async getSearchCraneRequestCalendarData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (deliveryList.length === 0) {
      return done(deliveryList, false);
    }

    await this.processDeliveryElement(incomeData, deliveryList, index, result);

    if (index < deliveryList.length - 1) {
      this.getSearchCraneRequestCalendarData(
        incomeData,
        deliveryList,
        index + 1,
        result,
        memberDetails,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },

  async getConcreteRequest(inputData, done) {
    try {
      const { timezoneoffset } = inputData.body;
      const { params } = inputData;
      const incomeData = inputData.body;
      let order;
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: incomeData.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          const concreteCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            },
          };
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          } else {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
            };
          }
          const getConcreteRequest = await ConcreteRequest.getAll(
            inputData,
            concreteCondition,
            '',
            '',
            incomeData.descriptionFilter,
            incomeData.locationFilter,
            incomeData.concreteSupplierFilter,
            incomeData.orderNumberFilter,
            incomeData.statusFilter,
            incomeData.mixDesignFilter,
            '',
            '',
            incomeData.memberFilter,
            incomeData.search,
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          done(getConcreteRequest, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async listGates(inputData) {
    try {
      const { params } = inputData;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        condition.isActive = true;
      }
      let searchCondition = {};
      if (incomeData.search) {
        const searchDefault = [
          {
            gateName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        gateAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const gateData = await Gates.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      if (incomeData.isFilter) {
        if (gateData.rows) {
          if (gateData.rows.length > 0) {
            gateData.rows.sort((a, b) =>
              a.gateName.toLowerCase() > b.gateName.toLowerCase() ? 1 : -1,
            );
          }
        } else if (gateData.length > 0) {
          gateData.sort((a, b) => (a.gateName.toLowerCase() > b.gateName.toLowerCase() ? 1 : -1));
        }
      }

      return gateData;
    } catch (e) {
      throw new Error(e);
    }
  },
  async lastGate(inputData) {
    try {
      let data;
      const lastData = await Gates.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['gateAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.gateAutoId + 1;
      } else {
        data = 1;
      }
      return { id: data };
    } catch (e) {
      throw new Error(e);
    }
  },
  async buildConditionList(inputData) {
    const { body, params } = inputData;
    const condition = {
      ProjectId: params.ProjectId,
      isDeleted: false,
    };

    if (body.showActivatedAlone) {
      condition.isActive = true;
    }
    if (body.nameFilter) {
      condition.equipmentName = { [Sequelize.Op.iLike]: `%${body.nameFilter}%` };
    }
    if (body.companyNameFilter) {
      condition['$controllUserDetails.Company.companyName$'] = { [Sequelize.Op.iLike]: `%${body.companyNameFilter}%` };
    }
    if (body.idFilter) {
      condition.equipmentAutoId = body.idFilter;
    }
    if (body.memberFilter) {
      condition['$controllUserDetails.id$'] = body.memberFilter;
    }
    if (body.typeFilter) {
      condition['$PresetEquipmentType.equipmentType$'] = body.typeFilter;
    }

    return condition;
  },

  buildSearchCondition(incomeData, params) {
    let searchCondition = {};
    if (!incomeData.search) return searchCondition;

    const searchDefault = [
      { equipmentName: { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
      { '$controllUserDetails.User.email$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
      { '$controllUserDetails.Company.companyName$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
      { '$PresetEquipmentType.equipmentType$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
      { '$controllUserDetails.User.firstName$': { [Sequelize.Op.iLike]: `%${incomeData.search}%` } },
    ];

    if (!Number.isNaN(+incomeData.search)) {
      searchCondition = {
        [Op.and]: [
          {
            [Op.or]: [
              searchDefault,
              {
                [Op.and]: [
                  {
                    equipmentAutoId: incomeData.search,
                    isDeleted: false,
                    ProjectId: params.ProjectId,
                  },
                ],
              },
            ],
          },
        ],
      };
    } else {
      searchCondition = { [Op.and]: [{ [Op.or]: searchDefault }] };
    }

    return searchCondition;
  },

  async fetchEquipmentData(condition, pageSize, offset, searchCondition, sort, sortByField, incomeData) {
    const equipmentData = await Equipments.getAll(condition, pageSize, offset, searchCondition, sort, sortByField);

    if (incomeData?.isFilter) {
      const rows = equipmentData.rows || equipmentData;
      if (rows?.length > 0) {
        rows.sort((a, b) => a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1);
      }
    }

    return equipmentData;
  },

  async listEquipment(inputData) {
    try {
      const { params, body: incomeData } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;

      const condition = await this.buildConditionList(inputData);
      const searchCondition = this.buildSearchCondition(incomeData, params);
      const { sort, sortByField } = incomeData;

      return await this.fetchEquipmentData(condition, pageSize, offset, searchCondition, sort, sortByField, incomeData);
    } catch (e) {
      throw new Error(e);
    }
  },


  async lastEquipment(inputData) {
    try {
      let data;
      const lastData = await Equipments.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['equipmentAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.equipmentAutoId + 1;
      } else {
        data = 1;
      }
      return { id: data };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getAllCompany(inputData) {
    try {
      const { params } = inputData;
      const parentCompany = await Company.findOne({
        required: false,
        subQuery: false,
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: { isParent: true, ParentCompanyId: +params.ParentCompanyId, isDeleted: false },
      });
      const companyList = await Company.getAllCompany({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      });
      return { companyList, parentCompany };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getDefinableWork(inputData) {
    try {
      const { params } = inputData;
      const defineRecord = await DeliverDefineWork.findAll({
        where: Sequelize.and({ ProjectId: params.ProjectId, isDeleted: false }),
      });
      defineRecord.sort((a, b) => (a.DFOW.toLowerCase() > b.DFOW.toLowerCase() ? 1 : -1));
      return { defineRecord };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getLocations(req) {
    try {
      const { query } = req;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const condition = {
        ProjectId,
        ParentCompanyId,
        isDeleted: false,
        isActive: true,
      };
      const locations = await Locations.getLocations(condition);
      return locations;
    } catch (e) {
      throw new Error(e);
    }
  },
  async lastCraneRequest(inputData) {
    try {
      const { params } = inputData;
      let data;
      let data2;
      let lastData = {};
      lastData = await CraneRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['CraneRequestId', 'DESC']],
      });
      const deliveryRequestList = await DeliveryRequest.findOne({
        where: {
          ProjectId: params.ProjectId,
          isDeleted: false,
          isAssociatedWithCraneRequest: true,
        },
        order: [['CraneRequestId', 'DESC']],
      });
      if (deliveryRequestList) {
        if (lastData) {
          if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
            lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
          }
        } else {
          lastData = {};
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      }
      if (lastData) {
        data = lastData.CraneRequestId + 1;
      } else {
        data = 1;
      }
      const lastDeliveryId = await DeliveryRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['DeliveryId', 'DESC']],
      });
      if (lastDeliveryId) {
        data2 = lastDeliveryId.DeliveryId + 1;
      } else {
        data2 = 1;
      }
      return { CraneRequestId: data, DeliveryId: data2 };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getMemberDataMixPanel(req) {
    try {
      const memberData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          UserId: +memberData.id,
          ProjectId: +memberData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            association: 'Project',
            attributes: ['id', 'projectName'],
          },
          {
            association: 'Company',
            attributes: ['id', 'companyName'],
          },
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            association: 'Role',
            attributes: ['roleName'],
          },
        ],
      });
      return memberDetails;
    } catch (e) {
      throw new Error(e);
    }
  },
  async getMemberData(inputData) {
    try {
      const { params } = inputData;
      const condition = {
        UserId: params.id,
        ProjectId: params.ProjectId,
      };
      const memberData = await Member.getBy(condition);
      return memberData;
    } catch (e) {
      throw new Error(e);
    }
  },
  async searchMember(inputData) {
    try {
      const { params } = inputData;
      const condition = {
        [Op.and]: [
          {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            RoleId: { [Op.ne]: 1 },
            isActive: true,
            isGuestUser: false,
          },
        ],
      };
      const memberList = await Member.searchMemberNDR(condition, params);
      const finalList = [];
      let assignEmail;
      memberList.forEach((element) => {
        if (element.User.firstName && element.User.lastName) {
          assignEmail = `${element.User.firstName} ${element.User.lastName}(${element.User.email})`;
        } else {
          assignEmail = `(${element.User.email})`;
        }
        finalList.push({
          id: element.id,
          emails: element.User.email,
          firstName: element.firstName,
          lastName: element.User.lastName,
          email: assignEmail,
        });
      });
      return finalList;
    } catch (e) {
      throw new Error(e);
    }
  },
  async newRequest(inputData, done) {
    try {
      const { eventTimeZone, projectDetails } = await this.fetchInitialData(inputData);
      if (!eventTimeZone || !projectDetails) {
        return done(null, { message: 'Invalid timezone or project not found' });
      }

      const windowValidation = await this.validateDeliveryWindow(inputData, eventTimeZone, projectDetails);
      if (windowValidation) return done(null, windowValidation);

      const { eventsArray, id, craneId } = await this.generateDeliveryEvents(inputData, eventTimeZone, projectDetails);
      if (!eventsArray.length) {
        return done(null, { message: 'Bookings will not be created for the scheduled date/time' });
      }

      const { newDeliverData, memberDetails } = await this.checkAndCreateDeliveries(
        eventsArray, inputData, projectDetails, id, craneId
      );

      await this.handleNotificationsAndFinalize(
        inputData, projectDetails, newDeliverData, memberDetails, done
      );

    } catch (e) {
      return done(null, e);
    }
  },
  async fetchInitialData(inputData) {
    const eventTimeZone = await TimeZone.findOne({
      where: {
        isDeleted: false,
        id: +inputData.body.TimeZoneId,
      },
      attributes: [
        'id',
        'location',
        'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes',
        'dayLightSavingTimeInMinutes',
        'timezone',
      ],
    });

    if (!eventTimeZone) return { eventTimeZone: null };

    const userDetail = await User.findOne({
      where: { id: inputData.body.userId, isDeleted: false },
    });

    inputData.user = userDetail;

    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +inputData.body.ProjectId,
    });

    return { eventTimeZone, userDetail, projectDetails };
  },

  async validateDeliveryWindow(inputData, eventTimeZone, projectDetails) {
    const deliveryData = inputData.body;

    if (deliveryData.startPicker === deliveryData.endPicker) {
      return { message: 'Delivery Start time and End time should not be the same' };
    }

    if (deliveryData.startPicker > deliveryData.endPicker) {
      return { message: 'Please enter From Time lesser than To Time' };
    }

    if (!deliveryData.recurrence) return null;

    const startDate = await deliveryService.compareDeliveryDateWithDeliveryWindowDate(
      deliveryData.deliveryStart,
      deliveryData.startPicker,
      eventTimeZone.timezone,
      projectDetails.ProjectSettings.deliveryWindowTime,
      projectDetails.ProjectSettings.deliveryWindowTimeUnit,
    );

    const endDate = await deliveryService.compareDeliveryDateWithDeliveryWindowDate(
      deliveryData.deliveryEnd,
      deliveryData.endPicker,
      eventTimeZone.timezone,
      projectDetails.ProjectSettings.deliveryWindowTime,
      projectDetails.ProjectSettings.deliveryWindowTimeUnit,
    );

    if (startDate || endDate) {
      if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
        if (deliveryData.recurrence === 'Does Not Repeat') {
          return { message: 'Please enter Future Date/Time' };
        }
        return { message: 'Please enter Future Start or End Date/Time' };
      }
      return {
        message: `Bookings cannot be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
      };
    }

    return null;
  },

  async generateDeliveryEvents(inputData, eventTimeZone, projectDetails) {
    const deliveryData = inputData.body;
    const loginUser = inputData.user;

    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: deliveryData.ProjectId,
      isActive: true,
      isDeleted: false,
    });

    if (!memberDetails) return { eventsArray: [], id: 0, craneId: 0 };

    const lastDelivery = await DeliveryRequest.findOne({
      where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
      order: [['DeliveryId', 'DESC']],
    });

    let id = lastDelivery?.DeliveryId || 0;

    const lastCrane = await CraneRequest.findOne({
      where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
      order: [['CraneRequestId', 'DESC']],
    });

    let craneId = lastCrane?.CraneRequestId || 0;

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      deliveryData,
      loginUser,
      deliveryData.requestType,
      eventTimeZone.timezone,
    );

    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    const eventsArray = await this.generateRecurringEventsData({
      deliveryData,
      memberDetails,
      eventTimeZone,
      recurrenceId,
      id,
      craneId,
      projectDetails,
      roleDetails,
      accountRoleDetails,
    });

    return { eventsArray, id, craneId };
  },

  async checkAndCreateDeliveries(eventsArray, inputData, projectDetails, id, craneId) {
    const deliveryData = inputData.body;
    const loginUser = inputData.user;

    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: deliveryData.ProjectId,
      isActive: true,
      isDeleted: false,
    });

    const overlapCheck = await deliveryService.checkDoubleBookingAllowedOrNot(
      eventsArray,
      projectDetails,
      'add',
      deliveryData.GateId,
    );

    if (overlapCheck?.error) {
      throw new Error(overlapCheck.message);
    }

    let newDeliverData = {};

    for (const event of eventsArray) {
      const created = await DeliveryRequest.createInstance(event);

      await Promise.all([
        ...deliveryData.companies.map((CompanyId) =>
          DeliverCompany.createInstance({ ...event, DeliveryId: created.id, CompanyId })),
        ...deliveryData.GateId.map((GateId) =>
          DeliverGate.createInstance({ ...event, DeliveryId: created.id, GateId })),
        ...deliveryData.EquipmentId.map((EquipmentId) =>
          DeliverEquipment.createInstance({ ...event, DeliveryId: created.id, EquipmentId })),
        ...deliveryData.persons.map((MemberId) =>
          DeliveryPerson.createInstance({ ...event, DeliveryId: created.id, MemberId })),
        ...deliveryData.define.map((DeliverDefineWorkId) =>
          DeliverDefine.createInstance({ ...event, DeliveryId: created.id, DeliverDefineWorkId })),
      ]);

      await DeliverHistory.createInstance({
        DeliveryRequestId: created.id,
        DeliveryId: created.DeliveryId,
        MemberId: memberDetails.id,
        type: 'create',
        description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
      });

      newDeliverData = created; // only last one will remain for final steps
    }

    return { newDeliverData, memberDetails };
  },

  async handleNotificationsAndFinalize(inputData, projectDetails, newDeliverData, memberDetails, done) {
    const deliveryData = inputData.body;
    const loginUser = inputData.user;

    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: deliveryData.ProjectId,
        id: deliveryData.LocationId,
      },
    });

    const history = {
      DeliveryRequestId: newDeliverData.id,
      DeliveryId: newDeliverData.DeliveryId,
      MemberId: memberDetails.id,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
      locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}. Location: ${locationChosen.locationPath}.`,
      ProjectId: deliveryData.ProjectId,
      projectName: projectDetails.projectName,
      createdAt: new Date(),
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
    };

    const notification = {
      ...history,
      LocationId: deliveryData.LocationId,
      title: 'Delivery Booking Creation',
      recurrenceType: `${deliveryData.recurrence} From ${moment(deliveryData.deliveryStart).format('MM/DD/YYYY')} to ${moment(deliveryData.deliveryEnd).format('MM/DD/YYYY')}`,
      requestType: 'deliveryRequest',
    };

    await Notification.createInstance(notification);

    await pushNotification.sendDeviceToken(history, 3, deliveryData.ProjectId);
    await deliveryService.sendEmailNotificationToUser(history, memberDetails, loginUser, newDeliverData, deliveryData, []);

    return done(history, false);
  },
  async generateRecurringEventsData(params) {
    const {
      deliveryData,
      memberDetails,
      eventTimeZone,
      recurrenceId,
      startingId,
      startingCraneId,
      projectDetails,
      roleDetails,
      accountRoleDetails,
    } = params;

    const eventsArray = [];
    let id = startingId;
    let craneId = startingCraneId;

    // 1. Add Event Helper
    const addEvent = (date, startTime, endTime) => {
      const event = this.createEvent({
        date,
        startTime,
        endTime,
        eventTimeZone,
        deliveryData,
        memberDetails,
        recurrenceId,
        projectDetails,
        roleDetails,
        accountRoleDetails,
        id: ++id,
        craneId: ++craneId,
      });
      eventsArray.push(event);
    };

    // 2. Process Recurrence
    this.handleRecurrenceData({ deliveryData, addEvent });

    return eventsArray;
  },

  createEvent({
    date,
    startTime,
    endTime,
    eventTimeZone,
    deliveryData,
    memberDetails,
    recurrenceId,
    projectDetails,
    roleDetails,
    accountRoleDetails,
    id,
    craneId,
  }) {
    const formattedDate = moment(date).format('MM/DD/YYYY');
    const start = moment.tz(`${formattedDate} ${startTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone);
    const end = moment.tz(`${formattedDate} ${endTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone);

    const deliveryStart = start.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    const deliveryEnd = end.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

    const DeliverParam = {
      description: deliveryData.description,
      escort: deliveryData.escort,
      vehicleDetails: deliveryData.vehicleDetails,
      notes: deliveryData.notes,
      DeliveryId: id,
      deliveryStart,
      deliveryEnd,
      ProjectId: deliveryData.ProjectId,
      createdBy: memberDetails.id,
      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
      requestType: deliveryData.requestType,
      cranePickUpLocation: deliveryData.cranePickUpLocation,
      craneDropOffLocation: deliveryData.craneDropOffLocation,
      CraneRequestId: deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
      recurrenceId,
      LocationId: deliveryData.LocationId,
      isCreatedByGuestUser: true,
    };

    // Auto-approval conditions
    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      DeliverParam.status = 'Approved';
      DeliverParam.approvedBy = memberDetails.id;
      DeliverParam.approved_at = new Date();
    }

    return DeliverParam;
  },

  handleDailyRecurrenceNew({ deliveryData, addEvent, start, end, startTime, endTime }) {
    const range = momentRange.range(start, end);
    for (const day of range.by('day')) {
      addEvent(day, startTime, endTime);
      for (let i = 1; i < +deliveryData.repeatEveryCount; i++) day.add(1, 'day');
    }
  },

  handleWeeklyMonthlyYearlyNew({ deliveryData, addEvent, start, end, startTime, endTime }) {
    switch (deliveryData.recurrence) {
      case 'Weekly':
        this.handleWeeklyRecurrenceNew({ deliveryData, addEvent, start, end, startTime, endTime });
        break;

      case 'Monthly':
        this.handleMonthlyRecurrenceNew({ deliveryData, addEvent, start, end, startTime, endTime });
        break;

      case 'Yearly': {
        let current = start.clone();
        while (current.isSameOrBefore(end)) {
          if (deliveryData.chosenDateOfMonth) {
            const targetDay = current.clone().date(+deliveryData.dateOfMonth);
            if (targetDay.isBetween(start, end, null, '[]')) {
              addEvent(targetDay, startTime, endTime);
            }
          }
          current.add(1, 'year');
        }
        break;
      }
    }
  },

  handleWeeklyRecurrenceNew({ deliveryData, addEvent, start, end, startTime, endTime }) {
    const daysOfWeek = deliveryData.days;
    let current = start.clone();
    while (current.isSameOrBefore(end)) {
      if (daysOfWeek.includes(current.format('dddd'))) {
        addEvent(current, startTime, endTime);
      }
      current.add(1, 'day');
    }
  },

  handleMonthlyRecurrenceNew({ deliveryData, addEvent, start, end, startTime, endTime }) {
    const repeatBy = deliveryData.monthlyRepeatType;
    let current = start.clone().startOf('month');

    const addDateOfMonthEvent = (monthStart) => {
      const targetDay = monthStart.clone().date(+deliveryData.dateOfMonth);
      if (targetDay.isBetween(start, end, null, '[]')) {
        addEvent(targetDay, startTime, endTime);
      }
    };

    const addRepeatByEvent = (monthStart) => {
      const [weekName, dayName] = repeatBy.toLowerCase().split(' ');
      const allMatching = this.getMatchingDays(monthStart, dayName);
      const indexMap = { second: 1, third: 2, fourth: 3, last: allMatching.length - 1 };
      const chosenDate = allMatching[indexMap[weekName] || 0];
      if (chosenDate?.isBetween(start, end, null, '[]')) {
        addEvent(chosenDate, startTime, endTime);
      }
    };

    while (current.isSameOrBefore(end)) {
      if (deliveryData.chosenDateOfMonth) {
        addDateOfMonthEvent(current);
      } else if (repeatBy) {
        addRepeatByEvent(current);
      }
      current.add(+deliveryData.repeatEveryCount || 1, 'month');
    }
  },

  getMatchingDays(monthStart, dayName) {
    const allMatching = [];
    let firstDay = monthStart.clone().startOf('month').day(dayName);
    if (firstDay.date() > 7) firstDay.add(7, 'days');
    while (firstDay.month() === monthStart.month()) {
      allMatching.push(firstDay.clone());
      firstDay.add(7, 'days');
    }
    return allMatching;
  },



  handleRecurrenceData({ deliveryData, addEvent }) {
    const start = moment(deliveryData.deliveryStart);
    const end = moment(deliveryData.deliveryEnd);
    const startTime = deliveryData.startPicker;
    const endTime = deliveryData.endPicker;

    switch (deliveryData.recurrence) {
      case 'Daily':
        this.handleDailyRecurrenceNew({ deliveryData, addEvent, start, end, startTime, endTime });
        break;

      case 'Weekly':
      case 'Monthly':
      case 'Yearly':
        this.handleWeeklyMonthlyYearlyNew({ deliveryData, addEvent, start, end, startTime, endTime });
        break;

      case 'Does Not Repeat':
        this.addEvent(deliveryData.deliveryStart, startTime, endTime);
        break;

      default:
        break;
    }
  },


  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async checkInputDatas(inputData, done) {
    const deliveryData = inputData.body;
    const { companies, persons, define } = deliveryData;
    const gates = [deliveryData.GateId];
    const equipments = deliveryData.EquipmentId;
    const inputProjectId = deliveryData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: persons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const gateList = await Gates.count({
      where: { id: { [Op.in]: gates }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: { id: { [Op.in]: define }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +deliveryData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (deliveryData.persons && deliveryData.persons.length > 0 && memberList !== persons.length) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (deliveryData.GateId && gateList !== gates.length) {
      return done(null, { message: 'Mentioned Gate is not in the project' });
    }
    if ((deliveryData.EquipmentId && equipments[0] != 0) && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in this project' });
    }
    if (
      deliveryData.companies &&
      deliveryData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (deliveryData.define && deliveryData.define.length > 0 && defineList !== define.length) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async checkDeliveryConflictsWithAlreadyScheduled(requestsArray, type, gateId) {
    if (requestsArray && requestsArray.length > 0) {
      const deliveryStartDateArr = [];
      const deliveryEndDateArr = [];
      const requestIds = [];
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(new Date(data.deliveryStart));
        deliveryEndDateArr.push(new Date(data.deliveryEnd));
        if (type === 'edit' && data.id) {
          requestIds.push(data.id);
        }
      });
      let condition = {
        ProjectId: requestsArray[0].ProjectId,
        status: {
          [Op.notIn]: ['Delivered', 'Expired'],
        },
      };
      if (type === 'edit') {
        condition = {
          ...condition,
          id: {
            [Op.notIn]: requestIds,
          },
        };
      }
      const isDeliveryBookingOverlapping = await DeliveryRequest.findAll({
        where: {
          ...condition,
          [Op.or]: [
            {
              [Op.or]: deliveryStartDateArr.map((date) => ({
                deliveryStart: { [Op.lte]: date },
                deliveryEnd: { [Op.gte]: date },
              })),
            },
            {
              [Op.or]: deliveryEndDateArr.map((date) => ({
                deliveryStart: { [Op.lte]: date },
                deliveryEnd: { [Op.gte]: date },
              })),
            },
          ],
        },
        include: [
          {
            association: 'gateDetails',
            where: {
              isDeleted: false,
              isActive: true,
              GateId: { [Op.eq]: +gateId },
            },
          },
        ],
      });
      
      return isDeliveryBookingOverlapping && isDeliveryBookingOverlapping.length > 0;

    }
  },
  async craneListEquipment(inputData) {
    try {
      const conditionData = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        conditionData.isActive = true;
      }
      const equipmentData = await Equipments.findAndCountAll({
        where: conditionData,
        include: [
          {
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      if (equipmentData.rows) {
        if (equipmentData.rows.length > 0) {
          equipmentData.rows.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      } else if (equipmentData.length > 0) {
        equipmentData.sort((a, b) =>
          a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
        );
      }
      return equipmentData;
    } catch (e) {
      throw new Error(e);
    }
  },

  async fetchAndValidateInitialData(inputData) {
    const { TimeZoneId, userId, ProjectId } = inputData.body;

    const eventTimeZone = await TimeZone.findOne({
      where: { isDeleted: false, id: +TimeZoneId },
      attributes: ['id', 'location', 'isDayLightSavingEnabled', 'timeZoneOffsetInMinutes', 'dayLightSavingTimeInMinutes', 'timezone']
    });
    if (!eventTimeZone) throw new Error('Provide a valid timezone');

    const userDetail = await User.findOne({ where: { id: userId, isDeleted: false } });
    if (!userDetail) throw new Error('Invalid user');

    const projectDetails = await Project.getProjectAndSettings({ isDeleted: false, id: +ProjectId });
    if (!projectDetails) throw new Error('Project does not exist');

    inputData.user = userDetail;
    return { eventTimeZone, userDetail, projectDetails };
  },

  async validateCraneRequestWindow(craneRequestDetail, eventTimeZone, projectSettings) {
    const { craneDeliveryStart, craneDeliveryEnd, startPicker, endPicker, recurrence } = craneRequestDetail;

    const startDate = await this.compareDeliveryDateWithDeliveryWindowDate(craneDeliveryStart, startPicker, eventTimeZone.timezone, projectSettings.deliveryWindowTime, projectSettings.deliveryWindowTimeUnit);
    const endDate = await this.compareDeliveryDateWithDeliveryWindowDate(craneDeliveryEnd, endPicker, eventTimeZone.timezone, projectSettings.deliveryWindowTime, projectSettings.deliveryWindowTimeUnit);

    if (startPicker === endPicker) throw new Error('Delivery Start time and End time should not be the same');
    if (startPicker > endPicker) throw new Error('Please enter From Time lesser than To Time');
    if ((startDate || endDate) && projectSettings.deliveryWindowTime === 0 && recurrence === 'Does Not Repeat') {
      throw new Error('Please enter Future Date/Time');
    }
    if (startDate || endDate) {
      throw new Error(`Bookings can not be submitted within ${projectSettings.deliveryWindowTime} ${projectSettings.deliveryWindowTimeUnit} prior to the event`);
    }
  },

  async generateRecurringEvents(craneRequestDetail, projectDetails, memberDetails, eventTimeZone, recurrenceId, initialId) {
    const recurrenceType = craneRequestDetail.recurrence;
    const eventGeneratorMap = {
      Daily: this.generateDailyEvents,
      Weekly: this.generateWeeklyEvents,
      Monthly: this.generateMonthlyEvents,
      Yearly: this.generateYearlyEvents,
      'Does Not Repeat': this.generateSingleEvent
    };

    if (!eventGeneratorMap[recurrenceType]) throw new Error('Unsupported recurrence type');

    return await eventGeneratorMap[recurrenceType].call(
      this,
      craneRequestDetail,
      projectDetails,
      memberDetails,
      eventTimeZone,
      recurrenceId,
      initialId
    );
  },

  async checkBookingConflicts(eventsArray, projectDetails) {
    if (!eventsArray.length) return;
    const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, 'add');
    if (isOverlapping?.error) throw new Error(isOverlapping.message);
  },

  async createAndNotifyCraneRequests(eventsArray, craneRequestDetail, inputData, memberDetails, loginUser, projectDetails, done) {
    for (const event of eventsArray) {
      const newRequest = await CraneRequest.createInstance(event);
      await this.linkCraneAssociations(newRequest, craneRequestDetail);
      await this.recordHistoryAndNotify(newRequest, craneRequestDetail, inputData, memberDetails, loginUser, projectDetails);
    }
  },

  async newCraneRequest(inputData, done) {
    try {
      const craneRequestDetail = inputData.body;
      const { eventTimeZone, projectDetails } = await this.fetchAndValidateInitialData(inputData);
      const loginUser = inputData.user;
      await this.validateCraneRequestWindow(craneRequestDetail, eventTimeZone, projectDetails.ProjectSettings);

      this.checkCraneInputDatas(inputData, async (checkResponse, checkError) => {
        if (checkError) return done(null, checkError);

        const memberDetails = await this.getCraneMemberDetails(craneRequestDetail, loginUser);
        const lastId = await this.getLatestCraneRequestId(craneRequestDetail.ProjectId);
        const recurrenceId = await this.insertRecurrence(craneRequestDetail, inputData.user, eventTimeZone);
        const eventsArray = await this.generateRecurringEvents(craneRequestDetail, projectDetails, memberDetails, eventTimeZone, recurrenceId, lastId);

        await this.checkBookingConflicts(eventsArray, projectDetails);
        await this.createAndNotifyCraneRequests(eventsArray, craneRequestDetail, inputData, memberDetails, loginUser, projectDetails, done);
      });

    } catch (error) {
      return done(null, { message: error.message || 'Internal Server Error' });
    }
  },

  async generateDailyEvents(details, project, member, tz, recurrenceId, lastId) {
    const { craneDeliveryStart, craneDeliveryEnd, startPicker, endPicker, repeatEveryCount } = details;
    const range = momentRange.range(moment(craneDeliveryStart), moment(craneDeliveryEnd));
    const days = Array.from(range.by('day'));
    const events = [];
    let id = lastId;

    for (let i = 0; i < days.length; i += +repeatEveryCount) {
      const date = moment(days[i]).format('MM/DD/YYYY');
      const start = moment.tz(`${date} ${startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
      const end = moment.tz(`${date} ${endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

      events.push(await this.buildCraneEvent(details, project, member, start, end, ++id, recurrenceId));
    }

    return events;
  },

  async generateWeeklyEvents(details, project, member, tz, recurrenceId, lastId) {
    const { craneDeliveryStart, craneDeliveryEnd, startPicker, endPicker, days } = details;
    const startOfWeek = moment(craneDeliveryStart).startOf('week');
    const endOfWeek = moment(craneDeliveryEnd).endOf('week');
    const range = momentRange.range(startOfWeek, endOfWeek);
    const allDays = Array.from(range.by('day'));

    const events = [];
    let id = lastId;

    for (const date of allDays) {
      const dayName = moment(date).format('dddd');
      if (days.includes(dayName) && moment(date).isBetween(craneDeliveryStart, craneDeliveryEnd, null, '[]')) {
        const dateStr = moment(date).format('MM/DD/YYYY');
        const start = moment.tz(`${dateStr} ${startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
        const end = moment.tz(`${dateStr} ${endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

        events.push(await this.buildCraneEvent(details, project, member, start, end, ++id, recurrenceId));
      }

    }

    return events;
  },

  async generateMonthlyEvents(details, project, member, tz, recurrenceId, lastId) {
    const { craneDeliveryStart, craneDeliveryEnd, dateOfMonth, chosenDateOfMonth, startPicker, endPicker, repeatEveryCount, monthlyRepeatType } = details;
    let current = moment(craneDeliveryStart).clone().startOf('month');
    const end = moment(craneDeliveryEnd).clone().endOf('month');

    const events = [];
    let id = lastId;

    while (current.isSameOrBefore(end)) {
      const yearMonth = current.format('YYYY-MM');

      if (chosenDateOfMonth) {
        const matchDate = moment(`${yearMonth}-${dateOfMonth}`, 'YYYY-MM-DD');
        if (matchDate.isValid() && matchDate.isBetween(craneDeliveryStart, craneDeliveryEnd, null, '[]')) {
          const dateStr = matchDate.format('MM/DD/YYYY');
          const start = moment.tz(`${dateStr} ${startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
          const endT = moment.tz(`${dateStr} ${endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

          events.push(await this.buildCraneEvent(details, project, member, start, endT, ++id, recurrenceId));
        }
      } else {
        const [week, day] = monthlyRepeatType.split(' ');
        const days = this.getMonthlyDayOccurrences(yearMonth, day.toLowerCase());
        let target = days[this.weekToIndex(week.toLowerCase())];
        if (target && moment(target).isBetween(craneDeliveryStart, craneDeliveryEnd, null, '[]')) {
          const dateStr = moment(target).format('MM/DD/YYYY');
          const start = moment.tz(`${dateStr} ${startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
          const endT = moment.tz(`${dateStr} ${endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

          events.push(await this.buildCraneEvent(details, project, member, start, endT, ++id, recurrenceId));
        }
      }

      current = current.add(repeatEveryCount, 'months');
    }

    return events;
  },

  async generateYearlyEvents(details, project, member, tz, recurrenceId, lastId) {
    const { craneDeliveryStart, craneDeliveryEnd, startPicker, endPicker, dateOfMonth, chosenDateOfMonth, repeatEveryCount, monthlyRepeatType } = details;

    let current = moment(craneDeliveryStart).clone().startOf('year');
    const end = moment(craneDeliveryEnd).clone().endOf('year');

    const events = [];
    let id = lastId;

    while (current.isSameOrBefore(end)) {
      const yearMonth = current.format('YYYY-MM');
      const dateStr = chosenDateOfMonth
        ? `${yearMonth}-${dateOfMonth}`
        : this.getMonthlyDayOccurrences(yearMonth, monthlyRepeatType.split(' ')[1].toLowerCase())[this.weekToIndex(monthlyRepeatType.split(' ')[0].toLowerCase())];

      const target = moment(dateStr, 'YYYY-MM-DD');
      if (target.isValid() && target.isBetween(craneDeliveryStart, craneDeliveryEnd, null, '[]')) {
        const formattedDate = target.format('MM/DD/YYYY');
        const start = moment.tz(`${formattedDate} ${startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
        const endT = moment.tz(`${formattedDate} ${endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

        events.push(await this.buildCraneEvent(details, project, member, start, endT, ++id, recurrenceId));
      }

      current = current.add(repeatEveryCount, 'years');
    }

    return events;
  },

  async generateSingleEvent(details, project, member, tz, recurrenceId, lastId) {
      const { craneDeliveryStart, craneDeliveryEnd, startPicker, endPicker } = details;

      const start = moment.tz(`${craneDeliveryStart} ${startPicker}`, 'YYYY-MM-DD HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
      const end = moment.tz(`${craneDeliveryEnd} ${endPicker}`, 'YYYY-MM-DD HH:mm', tz.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

      return [await this.buildCraneEvent(details, project, member, start, end, lastId + 1, recurrenceId)];
    },

    weekToIndex(week) {
    return { first: 0, second: 1, third: 2, fourth: 3, last: -1 }[week] ?? 0;
  },

  getMonthlyDayOccurrences(yearMonth, dayName) {
    const allDays = [];
    const base = moment(yearMonth, 'YYYY-MM').startOf('month').day(dayName);
    if (base.date() > 7) base.add(7, 'd');
    const month = base.month();
    while (base.month() === month) {
      allDays.push(base.clone());
      base.add(7, 'd');
    }
    return allDays;
  },

  async buildCraneEvent(details, project, member, start, end, id, recurrenceId) {
    const base = {
      description: details.description,
      isEscortNeeded: details.isEscortNeeded,
      additionalNotes: details.additionalNotes,
      CraneRequestId: id,
      craneDeliveryStart: start,
      craneDeliveryEnd: end,
      ProjectId: details.ProjectId,
      createdBy: member.id,
      isAssociatedWithDeliveryRequest: details.isAssociatedWithDeliveryRequest,
      pickUpLocation: details.pickUpLocation,
      dropOffLocation: details.dropOffLocation,
      recurrenceId,
      LocationId: details.LocationId,
      isCreatedByGuestUser: true
    };

    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    if (
      member.RoleId === roleDetails.id ||
      member.RoleId === accountRoleDetails.id ||
      member.isAutoApproveEnabled ||
      project.ProjectSettings.isAutoApprovalEnabled
    ) {
      base.status = 'Approved';
      base.approvedBy = member.id;
      base.approved_at = new Date();
    }

    return base;
  },


  async checkCraneInputDatas(inputData, done) {
    const craneRequestData = inputData.body;
    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
    const equipments = craneRequestData.EquipmentId;
    const inputProjectId = craneRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: {
        id: { [Op.in]: definableFeatureOfWorks },
        ProjectId: inputProjectId,
        isDeleted: false,
      },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +craneRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      craneRequestData.responsiblePersons &&
      craneRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (craneRequestData.EquipmentId && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in the project' });
    }
    if (
      craneRequestData.companies &&
      craneRequestData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (
      craneRequestData.definableFeatureOfWorks &&
      craneRequestData.definableFeatureOfWorks.length > 0 &&
      defineList !== definableFeatureOfWorks.length
    ) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async getConcreteDropdownDetail(req) {
    const { ProjectId } = req.query;
    const locationDetailsDropdown = await ConcreteLocation.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const mixDesignDropdown = await ConcreteMixDesign.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const pumpSizeDropdown = await ConcretePumpSize.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const parentCompany = await Company.findOne({
      required: false,
      subQuery: false,
      attributes: [
        'id',
        'companyName',
        'website',
        'address',
        'secondAddress',
        'country',
        'city',
        'companyAutoId',
        'state',
        'zipCode',
        'scope',
        'logo',
      ],
      where: { isParent: true, ParentCompanyId: +req.query.ParentCompanyId, isDeleted: false },
    });
    const companyList = await Company.getAllCompany({
      ProjectId,
      isDeleted: false,
      isParent: { [Op.not]: true },
    });
    const newCompanyList = [];
    await companyList.rows.forEach((element) => {
      newCompanyList.push({
        id: element.id,
        companyName: element.companyName,
      });
    });
    if (parentCompany) {
      const index = newCompanyList.findIndex(
        (item) =>
          item.id === parentCompany.id ||
          item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
      );
      if (index === -1) {
        newCompanyList.push({
          id: parentCompany.id,
          companyName: parentCompany.companyName,
        });
      }
    }
    newCompanyList.sort((a, b) =>
      a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
    );
    const condition = {
      ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const locationDropdown = await Locations.getLocations(condition);

    let data;
    let lastData = {};
    lastData = await ConcreteRequest.findOne({
      where: { ProjectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });
    if (lastData) {
      data = lastData.ConcreteRequestId + 1;
    } else {
      data = 1;
    }
    const dropdownData = {
      locationDetailsDropdown,
      locationDropdown,
      concreteSupplierDropdown: newCompanyList,
      mixDesignDropdown,
      pumpSizeDropdown,
      ConcreteRequestId: data,
    };
    return { status: 200, data: dropdownData };
  },

  async newConcreteRequest(inputData, done) {
    try {
      const {
        eventTimeZone,
        loginUser,
        projectDetails,
        concreteRequestDetail,
        memberDetails,
      } = await this.validateAndSetupRequest(inputData, done);
      if (!eventTimeZone || !projectDetails || !memberDetails) return;

      const { startDate } =
        await this.checkTimeConstraints(concreteRequestDetail, projectDetails, memberDetails, eventTimeZone, done);
      if (startDate === false) return;

      const eventsArray = await this.generateConcreteEventSeries(
        concreteRequestDetail,
        loginUser,
        projectDetails,
        memberDetails,
        eventTimeZone,
        done
      );
      if (!eventsArray || eventsArray.length === 0) return done(null, { message: 'Bookings will not be created for the scheduled date/time' });

      const createdRequest = await this.persistConcreteEvents(
        eventsArray,
        concreteRequestDetail,
        loginUser,
        memberDetails
      );

      await this.createConcreteRequestHistoryAndNotifications(
        createdRequest,
        concreteRequestDetail,
        loginUser,
        memberDetails,
        projectDetails,
        done
      );
    } catch (e) {
      done(null, e);
    }
  },


  async validateAndSetupRequest(inputData, done) {
    const concreteRequestDetail = inputData.body;

    const eventTimeZone = await TimeZone.findOne({
      where: { isDeleted: false, id: +concreteRequestDetail.TimeZoneId },
      attributes: [
        'id', 'location', 'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes', 'dayLightSavingTimeInMinutes', 'timezone',
      ],
    });
    if (!eventTimeZone) return done(null, { message: 'Provide a valid timezone' });

    const userDetail = await User.findOne({
      where: { id: concreteRequestDetail.userId, isDeleted: false },
    });
    inputData.user = userDetail;

    const loginUser = userDetail;
    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +concreteRequestDetail.ProjectId,
    });

    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: concreteRequestDetail.ProjectId,
      isActive: true,
      isDeleted: false,
    });

    if (!projectDetails) return done(null, { message: 'Project does not exist.' });

    return {
      eventTimeZone,
      userDetail,
      loginUser,
      projectDetails,
      concreteRequestDetail,
      memberDetails,
    };
  },

  async validatePlacementTimeConstraints(detail, project, member, tz, done) {
    let startDate, endDate;

    if (detail.recurrence) {
      startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        detail.concretePlacementStart,
        detail.startPicker,
        tz.timezone,
        project.ProjectSettings.deliveryWindowTime,
        project.ProjectSettings.deliveryWindowTimeUnit,
      );
      endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        detail.concretePlacementEnd,
        detail.endPicker,
        tz.timezone,
        project.ProjectSettings.deliveryWindowTime,
        project.ProjectSettings.deliveryWindowTimeUnit,
      );
    }

    if (detail.startPicker === detail.endPicker) {
      done(null, { message: 'Placement Start Time and Anticipated Completion Time should not be the same' });
      return false;
    }

    if (detail.startPicker > detail.endPicker) {
      done(null, { message: 'Please enter Placement Start Time lesser than Anticipated Completion Time' });
      return false;
    }

    if (+member.RoleId === 4 && (startDate || endDate)) {
      if (project.ProjectSettings.deliveryWindowTime === 0) {
        if (detail.recurrence === 'Does Not Repeat') {
          done(null, { message: 'Please enter Future Concrete Placement Date/Time' });
          return false;
        }
        done(null, { message: 'Please enter Future Concrete Placement or Recurrence End Date/Time' });
        return false;
      }
      done(null, {
        message: `Bookings cannot be submitted within ${project.ProjectSettings.deliveryWindowTime} ${project.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
      });
      return false;
    }

    return { startDate, endDate };
  },

  async validatePumpTimeConstraints(detail, project, member, tz, done) {
    let pumpStartDate, pumpEndDate;

    if (!detail.isPumpRequired) return { pumpStartDate, pumpEndDate };

    if (detail.pumpWorkStart === detail.pumpWorkEnd) {
      done(null, { message: 'Pump Show up Time and Completion Time should not be the same' });
      return false;
    }

    if (detail.pumpWorkStart > detail.pumpWorkEnd) {
      done(null, { message: 'Please enter Pump Show up Time lesser than Pump Completion Time' });
      return false;
    }

    pumpStartDate = await this.compareDeliveryDateWithDeliveryWindowDate(
      detail.pumpOrderedDate,
      detail.pumpWorkStart,
      tz.timezone,
      project.ProjectSettings.deliveryWindowTime,
      project.ProjectSettings.deliveryWindowTimeUnit,
    );

    pumpEndDate = await this.compareDeliveryDateWithDeliveryWindowDate(
      detail.pumpOrderedDate,
      detail.pumpWorkEnd,
      tz.timezone,
      project.ProjectSettings.deliveryWindowTime,
      project.ProjectSettings.deliveryWindowTimeUnit,
    );

    if (+member.RoleId === 4 && (pumpStartDate || pumpEndDate)) {
      done(null, { message: 'Please enter Future Pump Ordered Date/Time' });
      return false;
    }

    return { pumpStartDate, pumpEndDate };
  },

  async checkTimeConstraints(detail, project, member, tz, done) {
    const placementValidation = await this.validatePlacementTimeConstraints(detail, project, member, tz, done);
    if (!placementValidation) return false;

    const pumpValidation = await this.validatePumpTimeConstraints(detail, project, member, tz, done);
    if (!pumpValidation) return false;

    return { ...placementValidation, ...pumpValidation };
  },


  async generateConcreteEventSeries(detail, loginUser, project, member, tz, done) {
    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      detail, loginUser, 'concreteRequest', tz.timezone
    );

    const handler = {
      'Daily': this.handleDailyRecurrence,
      'Weekly': this.handleWeeklyRecurrence,
      'Monthly': this.handleMonthlyRecurrence,
      'Yearly': this.handleYearlyRecurrence,
      'Does Not Repeat': this.handleSingleRequest,
    };

    const generatorFn = handler[detail.recurrence];
    if (!generatorFn) return [];

    const events = await generatorFn.call(this, {
      detail,
      loginUser,
      project,
      member,
      tz,
      recurrenceId,
    });

    if (events && events.length > 0) {
      const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
        events,
        project,
        'add',
      );
      if (isOverlapping?.error) {
          done(null, { message: isOverlapping.message });
          return false;
      }

    }

    return events;
  },

  async persistConcreteEvents(events, detail, loginUser, member) {
    let lastCreatedRequest = {};

    for (let req of events) {
      const request = await ConcreteRequest.createInstance(req);

      const updateParam = {
        ConcreteRequestId: request.id,
        ConcreteRequestCode: request.ConcreteRequestId,
        ProjectId: detail.ProjectId,
      };

      // Location
      const locationData = await ConcreteLocation.createConcreteLocation({
        location: detail.location,
        ProjectId: detail.ProjectId,
        isDeleted: false,
        createdBy: loginUser.id,
      });
      updateParam.ConcreteLocationId = locationData.id;
      await ConcreteRequestLocation.createInstance(updateParam);

      // Concrete Suppliers
      for (let companyId of detail.concreteSupplier) {
        await ConcreteRequestCompany.createInstance({ ...updateParam, CompanyId: companyId });
      }

      // Pump Sizes
      for (let pump of detail.pumpSize) {
        const pumpId = await this.resolveOrCreatePumpSize(pump, detail.ProjectId, loginUser.id);
        await ConcreteRequestPumpSize.createInstance({ ...updateParam, ConcretePumpSizeId: pumpId });
      }

      // Mix Designs
      for (let mix of detail.mixDesign) {
        const mixId = await this.resolveOrCreateMixDesign(mix, detail.ProjectId, loginUser.id);
        await ConcreteRequestMixDesign.createInstance({ ...updateParam, ConcreteMixDesignId: mixId });
      }

      // Responsible Persons
      for (let personId of detail.responsiblePersons) {
        await ConcreteRequestResponsiblePerson.createInstance({ ...updateParam, MemberId: personId });
      }

      lastCreatedRequest = request;
    }

    return lastCreatedRequest;
  },

  async createConcreteRequestHistoryAndNotifications(
    request,
    detail,
    loginUser,
    member,
    project,
    done
  ) {
    const locationChosen = await Locations.findOne({
      where: { ProjectId: detail.ProjectId, id: detail.LocationId },
    });

    const history = {
      ConcreteRequestId: request.id,
      MemberId: member.id,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${detail.description}.`,
      locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${detail.description}. Location: ${locationChosen.locationPath}.`,
      ProjectId: detail.ProjectId,
      projectName: project.projectName,
      memberData: [],
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
      createdAt: new Date(),
    };

    const notification = {
      ...history,
      LocationId: detail.LocationId,
      title: 'Concrete Booking Creation',
      isDeliveryRequest: false,
      requestType: 'concreteRequest',
      recurrenceType: `${detail.recurrence} From ${moment(detail.concretePlacementStart).format('MM/DD/YYYY')} to ${moment(detail.concretePlacementEnd).format('MM/DD/YYYY')}`,
    };

    await ConcreteRequestHistory.createInstance(history);
    const notif = await Notification.createInstance(notification);

    await notificationHelper.createDeliveryPersonNotification(
      [], [], project, notif, DeliveryPersonNotification,
      member, loginUser, 3, 'created a', 'Concrete Request',
      `concrete Booking (${request.ConcreteRequestId} - ${request.description})`,
      request.ConcreteRequestId,
    );

    await pushNotification.sendPushNotificationForConcrete(history, 3, detail.ProjectId);

    return done(history, false);
  },

  async handleDailyRecurrence({ detail, loginUser, project, member, tz, recurrenceId }) {
    const range = momentRange.range(
      moment(detail.concretePlacementStart),
      moment(detail.concretePlacementEnd)
    );
    const totalDays = Array.from(range.by('day'));
    const eventsArray = [];
    let id = await this.getNextConcreteRequestId(member.ProjectId);

    const { startPicker: startTime, endPicker: endTime } = detail;

    for (let i = 0; i < totalDays.length; i += +detail.repeatEveryCount) {
      const date = moment(totalDays[i]).format('MM/DD/YYYY');
      const concreteStart = moment.tz(`${date} ${startTime}`, 'MM/DD/YYYY HH:mm', tz.timezone);
      const concreteEnd = moment.tz(`${date} ${endTime}`, 'MM/DD/YYYY HH:mm', tz.timezone);

      const event = this.buildConcreteRequestData(
        id++, detail, member, recurrenceId,
        concreteStart, concreteEnd, tz
      );
      eventsArray.push(event);
    }

    return eventsArray;
  },

  async handleWeeklyRecurrence({ detail, loginUser, project, member, tz, recurrenceId }) {
    const startWeek = moment(detail.concretePlacementStart).startOf('week');
    const endWeek = moment(detail.concretePlacementEnd).endOf('week');
    const range = momentRange.range(startWeek, endWeek);
    const allDays = Array.from(range.by('day'));
    const eventsArray = [];
    let id = await this.getNextConcreteRequestId(member.ProjectId);
    const daysToInclude = detail.days || [];

    const { startPicker: startTime, endPicker: endTime } = detail;

    for (const dayStr of allDays) {
      const day = moment(dayStr);
      const weekday = day.format('dddd');
      if (daysToInclude.includes(weekday)) {
        const date = day.format('MM/DD/YYYY');
        const concreteStart = moment.tz(`${date} ${startTime}`, 'MM/DD/YYYY HH:mm', tz.timezone);
        const concreteEnd = moment.tz(`${date} ${endTime}`, 'MM/DD/YYYY HH:mm', tz.timezone);
        const event = this.buildConcreteRequestData(
          id++, detail, member, recurrenceId,
          concreteStart, concreteEnd, tz
        );
        eventsArray.push(event);
      }
    }

    return eventsArray;
  },

  async handleMonthlyRecurrence({ detail, loginUser, project, member, tz, recurrenceId }) {
    const startDate = moment(detail.concretePlacementStart);
    const endDate = moment(detail.concretePlacementEnd);
    const eventsArray = [];
    let id = await this.getNextConcreteRequestId(member.ProjectId);

    let current = startDate.clone().startOf('month');

    while (current.isSameOrBefore(endDate)) {
      let chosenDay;

      if (detail.chosenDateOfMonth) {
        chosenDay = current.clone().date(+detail.dateOfMonth);
      } else {
        const parts = detail.monthlyRepeatType.split(' ');
        const week = parts[0].toLowerCase();
        const weekday = parts[1].toLowerCase();

        const days = [];
        let d = current.clone().startOf('month').day(weekday);
        if (d.date() > 7) d.add(7, 'd');

        while (d.month() === current.month()) {
          days.push(d.clone());
          d.add(7, 'd');
        }

        const weekIndex = {
          'first': 0, 'second': 1, 'third': 2, 'fourth': 3, 'last': days.length - 1,
        }[week];
        chosenDay = days[weekIndex];
      }

      if (chosenDay?.isBetween(startDate, endDate, null, '[]')) {
        const date = chosenDay.format('MM/DD/YYYY');
        const concreteStart = moment.tz(`${date} ${detail.startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone);
        const concreteEnd = moment.tz(`${date} ${detail.endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone);

        const event = this.buildConcreteRequestData(
          id++, detail, member, recurrenceId,
          concreteStart, concreteEnd, tz
        );
        eventsArray.push(event);
      }

      current.add(+detail.repeatEveryCount, 'months');
    }

    return eventsArray;
  },

  async handleYearlyRecurrence({ detail, loginUser, project, member, tz, recurrenceId }) {
    const startDate = moment(detail.concretePlacementStart);
    const endDate = moment(detail.concretePlacementEnd);
    const eventsArray = [];
    let id = await this.getNextConcreteRequestId(member.ProjectId);
    let current = startDate.clone();

    while (current.isSameOrBefore(endDate)) {
      const date = current.format('MM/DD/YYYY');
      const concreteStart = moment.tz(`${date} ${detail.startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone);
      const concreteEnd = moment.tz(`${date} ${detail.endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone);

      const event = this.buildConcreteRequestData(
        id++, detail, member, recurrenceId,
        concreteStart, concreteEnd, tz
      );
      eventsArray.push(event);

      current.add(+detail.repeatEveryCount, 'years');
    }

    return eventsArray;
  },

  async handleSingleRequest({ detail, loginUser, project, member, tz, recurrenceId }) {
    const eventsArray = [];
    let id = await this.getNextConcreteRequestId(member.ProjectId);

    const date = moment(detail.concretePlacementStart).format('MM/DD/YYYY');
    const concreteStart = moment.tz(`${date} ${detail.startPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone);
    const concreteEnd = moment.tz(`${date} ${detail.endPicker}`, 'MM/DD/YYYY HH:mm', tz.timezone);

    const event = this.buildConcreteRequestData(
      id, detail, member, recurrenceId, concreteStart, concreteEnd, tz
    );
    eventsArray.push(event);

    return eventsArray;
  },

  buildConcreteRequestData(id, detail, member, recurrenceId, concreteStart, concreteEnd, tz) {
    return {
      description: detail.description,
      ProjectId: detail.ProjectId,
      notes: detail.notes,
      concretePlacementStart: concreteStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ'),
      concretePlacementEnd: concreteEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ'),
      isPumpConfirmed: detail.isPumpConfirmed,
      isPumpRequired: detail.isPumpRequired,
      isConcreteConfirmed: detail.isConcreteConfirmed,
      ParentCompanyId: detail.ParentCompanyId,
      concreteOrderNumber: detail.concreteOrderNumber,
      truckSpacingHours: detail.truckSpacingHours,
      slump: detail.slump,
      concreteQuantityOrdered: detail.concreteQuantityOrdered,
      concreteConfirmedOn: detail.concreteConfirmedOn || null,
      pumpLocation: detail.pumpLocation,
      pumpOrderedDate: null,
      pumpWorkStart: null,
      pumpWorkEnd: null,
      pumpConfirmedOn: detail.pumpConfirmedOn || null,
      cubicYardsTotal: detail.cubicYardsTotal,
      hoursToCompletePlacement: detail.hoursToCompletePlacement,
      minutesToCompletePlacement: detail.minutesToCompletePlacement,
      ConcreteRequestId: id,
      requestType: 'concreteRequest',
      status: 'Tentative',
      primerForPump: detail.primerForPump,
      createdBy: member.id,
      recurrenceId,
      LocationId: detail.LocationId,
      isCreatedByGuestUser: true,
    };
  },

  async getNextConcreteRequestId(projectId) {
    const last = await ConcreteRequest.findOne({
      where: { ProjectId: +projectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });
    return last ? last.ConcreteRequestId + 1 : 1;
  },

  async resolveOrCreatePumpSize(pump, projectId, userId) {
    if (pump.chosenFromDropdown) return pump.id;

    const existing = await ConcretePumpSize.findAll({
      where: { ProjectId: projectId, isDeleted: false },
    });
    const found = existing.find(p =>
      p.pumpSize.toLowerCase().trim() === pump.pumpSize.toLowerCase().trim());

    if (found) return found.id;

    const created = await ConcretePumpSize.createConcretePumpSize({
      pumpSize: pump.pumpSize,
      ProjectId: projectId,
      isDeleted: false,
      createdBy: userId,
    });
    return created.id;
  },

  async resolveOrCreateMixDesign(mix, projectId, userId) {
    if (mix.chosenFromDropdown) return mix.id;

    const existing = await ConcreteMixDesign.findAll({
      where: { ProjectId: projectId, isDeleted: false },
    });
    const found = existing.find(m =>
      m.mixDesign.toLowerCase().trim() === mix.mixDesign.toLowerCase().trim());

    if (found) return found.id;

    const created = await ConcreteMixDesign.createConcreteMixDesign({
      mixDesign: mix.mixDesign,
      ProjectId: projectId,
      isDeleted: false,
      createdBy: userId,
    });
    return created.id;
  },



  async concreteCheckInputDatas(inputData, done) {
    const concreteRequestData = inputData.body;
    const { concreteSupplier } = concreteRequestData;
    const { responsiblePersons } = concreteRequestData;
    const inputProjectId = +concreteRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: concreteSupplier },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: concreteSupplier,
            },
            isParent: true,
            ParentCompanyId: +concreteRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      concreteRequestData.responsiblePersons &&
      concreteRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (
      concreteRequestData.concreteSupplier &&
      concreteRequestData.concreteSupplier.length > 0 &&
      companyList !== concreteSupplier.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }

    if (concreteRequestData.mixDesign && concreteRequestData.mixDesign.length > 0) {
      const mixDesignTempArray = [];
      concreteRequestData.mixDesign.forEach((element) => {
        if (element.chosenFromDropdown) {
          mixDesignTempArray.push(element.id);
        }
      });
      const concreteMixDesignCount = await ConcreteMixDesign.count({
        where: { id: { [Op.in]: mixDesignTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concreteMixDesignCount !== mixDesignTempArray.length) {
        return done(null, { message: 'Some Mix Design is not in the project' });
      }
    }
    if (concreteRequestData.pumpSize && concreteRequestData.pumpSize.length > 0) {
      const pumpSizeTempArray = [];
      concreteRequestData.pumpSize.forEach((element) => {
        if (element.chosenFromDropdown) {
          pumpSizeTempArray.push(element.id);
        }
      });
      const concretePumpSizeCount = await ConcretePumpSize.count({
        where: { id: { [Op.in]: pumpSizeTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concretePumpSizeCount !== pumpSizeTempArray.length) {
        return done(null, { message: 'Some Pump Size is not in the project' });
      }
    }
    return done(true, false);
  },
  async getNDRData(inputData, done) {
    try {
      const { params } = inputData;
      const condition = {
        id: params.DeliveryRequestId,
      };
      const deliveryList = await DeliveryRequest.guestGetNDRData(condition);
      return deliveryList;
    } catch (e) {
      throw new Error(e);
    }
  },
  async getSingleCraneRequest(inputData) {
    try {
      const { params } = inputData;
      const getCraneRequest = await CraneRequest.findOne({
        where: { CraneRequestId: params.CraneRequestId, ProjectId: params.ProjectId },
      });
      const craneRequest = await CraneRequest.guestSingleCraneRequestData({
        id: +getCraneRequest.id,
      });
      return craneRequest;
    } catch (e) {
      throw new Error(e);
    }
  },
  async getSingleConcreteRequest(inputData) {
    try {
      const { params } = inputData;
      const getConcreteRequest = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: params.ConcreteRequestId,
          ProjectId: params.ProjectId,
          isDeleted: false,
        },
      });
      const concreteRequest = await ConcreteRequest.guestSingleConcreteRequestData({
        id: +getConcreteRequest.id,
      });
      return concreteRequest;
    } catch (e) {
      throw new Error(e);
    }
  },
  async editRequest(inputData, done) {
    try {
      const context = await this.initializeRequestData(inputData);

      const overlapError = await this.handleOverlapValidation(context);
      if (overlapError) return done(null, { message: overlapError });

      const recurrenceContext = await this.handleRecurrenceUpdates(context);

      const editSeriesRequests = recurrenceContext.editSeriesRequests;
      if (!editSeriesRequests || editSeriesRequests.length === 0)
        return done(null, { message: 'No matching delivery requests found to edit.' });

      await this.updateDeliveryRequests(editSeriesRequests, context, recurrenceContext);

      const finalResult = await this.finalizeAndNotify(editSeriesRequests, context, recurrenceContext);
      return done(finalResult, false);

    } catch (e) {
      return done(null, e);
    }
  },

  async initializeRequestData(inputData) {
    const deliveryData = inputData.body;
    const user = await User.findOne({ where: { id: deliveryData.userId, isDeleted: false } });
    inputData.user = user;
    const projectSettingDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +deliveryData.ProjectId,
    });

    return {
      inputData,
      deliveryData,
      loginUser: user,
      projectSettingDetails
    };
  },

  async handleRecurrenceUpdates({ deliveryData }) {
    let editSeriesRequests = [];
    let previousRecordInSeries = null;
    let newRecurrenceId = null;

    const recurrenceId = deliveryData.recurrenceId;

    if (deliveryData.seriesOption === 1) {
      editSeriesRequests = await DeliveryRequest.findAll({
        where: [Sequelize.and({ id: deliveryData.id })],
      });
    }

    if (deliveryData.seriesOption === 2) {
      editSeriesRequests = await DeliveryRequest.findAll({
        where: [Sequelize.and({
          recurrenceId,
          id: { [Op.gte]: deliveryData.id },
        })],
      });

      previousRecordInSeries = await DeliveryRequest.findOne({
        where: [Sequelize.and({
          recurrenceId,
          id: { [Op.lt]: deliveryData.id },
        })],
        order: [['id', 'DESC']],
      });
    }

    if (deliveryData.seriesOption === 3) {
      editSeriesRequests = await DeliveryRequest.findAll({
        where: [Sequelize.and({
          recurrenceId,
          deliveryStart: {
            [Op.gte]: moment.tz(deliveryData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
          },
        })],
      });
    }

    return {
      editSeriesRequests,
      previousRecordInSeries,
      newRecurrenceId,
    };
  },

  async updateDeliveryRequests(requests, context, recurrenceContext) {
    const { deliveryData, loginUser, inputData, projectSettingDetails } = context;
    const { newRecurrenceId } = recurrenceContext;

    for (const request of requests) {
      const existsRequest = await DeliveryRequest.getSingleDeliveryRequestData({ id: request.id });
      if (!existsRequest) continue;

      await this.checkInputDatas(inputData, async (checkResponse, checkError) => {
        if (checkError) throw new Error(checkError.message || 'Validation failed');

        const memberData = await Member.getBy({
          UserId: loginUser.id,
          ProjectId: deliveryData.ProjectId,
        });

        const deliveryParams = {
          description: deliveryData.description,
          escort: deliveryData.escort,
          vehicleDetails: deliveryData.vehicleDetails,
          notes: deliveryData.notes,
          isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
          requestType: deliveryData.requestType,
          cranePickUpLocation: deliveryData.cranePickUpLocation,
          craneDropOffLocation: deliveryData.craneDropOffLocation,
          recurrenceId: deliveryData.seriesOption !== 1 ? newRecurrenceId : null,
          LocationId: deliveryData.LocationId,
        };

        if (deliveryData.seriesOption === 1) {
          deliveryParams.deliveryStart = deliveryData.deliveryStart;
          deliveryParams.deliveryEnd = deliveryData.deliveryEnd;
        } else {
          const localStart = moment.utc(request.deliveryStart).tz(deliveryData.timezone);
          const localEnd = moment.utc(request.deliveryEnd).tz(deliveryData.timezone);

          deliveryParams.deliveryStart = await this.convertTimezoneToUtc(
            localStart.format('MM/DD/YYYY'),
            deliveryData.timezone,
            deliveryData.deliveryStartTime,
          );
          deliveryParams.deliveryEnd = await this.convertTimezoneToUtc(
            localEnd.format('MM/DD/YYYY'),
            deliveryData.timezone,
            deliveryData.deliveryEndTime,
          );
        }

        const isAutoApprove = (
          memberData.RoleId === 1 || memberData.RoleId === 2 ||
          memberData.isAutoApproveEnabled ||
          (projectSettingDetails.ProjectSettings?.isAutoApprovalEnabled)
        );

        if (isAutoApprove && request.status === 'Approved') {
          deliveryParams.status = 'Approved';
          deliveryParams.approvedBy = memberData.id;
          deliveryParams.approved_at = new Date();
        }

        await DeliveryRequest.update(deliveryParams, {
          where: { id: request.id },
        });
      });
    }
  },

  async finalizeAndNotify(requests, context, recurrenceContext) {
    await this.updateRequestStatuses(requests, context, recurrenceContext);
    await this.sendNotifications(requests, context, recurrenceContext);
    await this.recordEditHistory(requests, context, recurrenceContext);

    return { message: 'Delivery requests updated, notifications sent, and history recorded.' };
  },

  async hasRequestChanged(updatedReq, originalReq) {
    const compareFields = [
      'description', 'vehicleDetails', 'notes',
      'LocationId', 'escort', 'requestType',
      'cranePickUpLocation', 'craneDropOffLocation',
    ];

    for (const field of compareFields) {
      if (updatedReq[field] !== originalReq[field]) return true;
    }

    const deliveryTimeChanged =
      moment(updatedReq.deliveryStart).format('h:mm a') !== moment(originalReq.deliveryStart).format('h:mm a') ||
      moment(updatedReq.deliveryEnd).format('h:mm a') !== moment(originalReq.deliveryEnd).format('h:mm a');

    return deliveryTimeChanged;
  },

  async updateStatusForRequest(reqId, status, shouldApprove, memberData, fieldsChanged, deliveryTimeChanged) {
    if (['Approved', 'Delivered'].includes(status)) {
      if ((fieldsChanged || deliveryTimeChanged) && !shouldApprove) {
        await DeliveryRequest.update({ status: 'Pending' }, { where: { id: reqId } });
      } else if (shouldApprove) {
        await DeliveryRequest.update(
          { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
          { where: { id: reqId } }
        );
      }
    }

    if (['Expired', 'Declined', 'Pending'].includes(status) && shouldApprove) {
      await DeliveryRequest.update(
        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
        { where: { id: reqId } }
      );
    }
  },

  async updateRequestStatuses(requests, context, recurrenceContext) {
    const { deliveryData, loginUser, projectSettingDetails } = context;
    const memberData = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: deliveryData.ProjectId,
    });

    const updatedRequests = [];

    for (const req of requests) {
      const updatedReq = await DeliveryRequest.getSingleDeliveryRequestData({ id: req.id });
      const originalReq = await DeliveryRequest.findOne({ where: { id: req.id } });

      if (!updatedReq || !originalReq) continue;

      const fieldsChanged = await this.hasRequestChanged(updatedReq, originalReq);
      const deliveryTimeChanged = false; // handled within hasRequestChanged (but we could separate if needed)

      const shouldApprove =
        memberData.RoleId === 2 ||
        memberData.isAutoApproveEnabled ||
        projectSettingDetails?.ProjectSettings?.isAutoApprovalEnabled;

      await this.updateStatusForRequest(
        req.id,
        updatedReq.status,
        shouldApprove,
        memberData,
        fieldsChanged,
        deliveryTimeChanged
      );

      updatedRequests.push(req.id);
    }

    return updatedRequests;
  },

  async sendNotifications(requests, context, recurrenceContext) {
    const { deliveryData, loginUser } = context;
    const deliveryId = requests[0].id;

    const notification = {
      title: `Delivery Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`,
      description: deliveryData.description,
      requestType: 'deliveryRequest',
      DeliveryRequestId: deliveryId,
      ProjectId: deliveryData.ProjectId,
    };

    const locationFollowers = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: deliveryData.ProjectId,
        LocationId: deliveryData.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    const followerIds = locationFollowers.map((pref) => pref.Member.id);

    if (followerIds.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        locationFollowers,
        deliveryData.DeliveryRequestId,
        notification.description,
        deliveryData.requestType,
        deliveryData.ProjectId,
        deliveryId,
        5, // NotificationPreferenceItemId for "edited"
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        deliveryData.ProjectId,
        deliveryId,
        locationFollowers,
        5
      );
    }

    return true;
  },

  async recordEditHistory(requests, context, recurrenceContext) {
    const { deliveryData, loginUser } = context;
    const firstRequest = await DeliveryRequest.findOne({ where: { id: requests[0].id } });

    const memberData = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: deliveryData.ProjectId,
    });

    const location = await Locations.findOne({
      where: { ProjectId: deliveryData.ProjectId, id: deliveryData.LocationId },
    });

    const history = {
      DeliveryRequestId: firstRequest.id,
      DeliveryId: firstRequest.DeliveryId,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} edited this delivery booking.`,
      locationFollowDescription: `Location: ${location.locationPath}`,
      ProjectId: deliveryData.ProjectId,
      createdAt: new Date(),
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
    };

    await DeliverHistory.createInstance(history);
  },

  async validateSingleEventOverlap(deliveryData, projectSettingDetails) {
    const requestArray = [{
      ProjectId: deliveryData.ProjectId,
      deliveryStart: deliveryData.deliveryStart,
      deliveryEnd: deliveryData.deliveryEnd,
      id: deliveryData.id,
    }];

    const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
      requestArray,
      projectSettingDetails,
      'edit',
      deliveryData.GateId
    );

    return isOverlapping?.error ? isOverlapping.message : null;
  },

  async validateRecurringEventsOverlap(deliveryData, projectSettingDetails) {
    const recurrenceId = deliveryData.recurrenceId;
    let requestSeries = [];

    if (deliveryData.seriesOption === 2) {
      requestSeries = await DeliveryRequest.findAll({
        where: [
          Sequelize.and({
            recurrenceId,
            id: { [Op.gte]: deliveryData.id },
          }),
        ],
      });
    }

    if (deliveryData.seriesOption === 3) {
      requestSeries = await DeliveryRequest.findAll({
        where: [
          Sequelize.and({
            recurrenceId,
            deliveryStart: {
              [Op.gte]: moment.tz(deliveryData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
            },
          }),
        ],
      });
    }

    const requestArray = [];

    for (const req of requestSeries) {
      const deliveryStartDate = await this.convertTimezoneToUtc(
        moment.utc(req.deliveryStart).tz(deliveryData.timezone).format('MM/DD/YYYY'),
        deliveryData.timezone,
        deliveryData.deliveryStartTime,
      );

      const deliveryEndDate = await this.convertTimezoneToUtc(
        moment.utc(req.deliveryEnd).tz(deliveryData.timezone).format('MM/DD/YYYY'),
        deliveryData.timezone,
        deliveryData.deliveryEndTime,
      );

      requestArray.push({
        ProjectId: deliveryData.ProjectId,
        deliveryStart: !moment(deliveryStartDate).isSame(moment(req.deliveryStart)) ? deliveryStartDate : req.deliveryStart,
        deliveryEnd: !moment(deliveryEndDate).isSame(moment(req.deliveryEnd)) ? deliveryEndDate : req.deliveryEnd,
        id: req.id,
      });
    }

    const existingRecurrenceEndDate = moment(deliveryData.recurrence.recurrenceEndDate)
      .tz(deliveryData.timezone)
      .format('YYYY-MM-DD');

    const newRecurrenceEndDate = deliveryData.recurrenceEndDate;

    // Extend recurrence range if needed
    if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
      const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
      const endDate = moment(newRecurrenceEndDate);

      for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
        requestArray.push({
          ProjectId: deliveryData.ProjectId,
          deliveryStart: await this.convertTimezoneToUtc(
            moment(date).format('MM/DD/YYYY'),
            deliveryData.timezone,
            deliveryData.deliveryStartTime,
          ),
          deliveryEnd: await this.convertTimezoneToUtc(
            moment(date).format('MM/DD/YYYY'),
            deliveryData.timezone,
            deliveryData.deliveryEndTime,
          ),
        });
      }
    }

    if (requestArray.length > 0) {
      const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
        requestArray,
        projectSettingDetails,
        'edit',
        deliveryData.GateId,
      );

      return isOverlapping?.error ? isOverlapping.message : null;
    }

    return null;
  },

  async handleOverlapValidation({ deliveryData, projectSettingDetails }) {
    const recurrenceId = deliveryData.recurrenceId;

    if (!projectSettingDetails || !recurrenceId) return null;

    if (deliveryData.seriesOption === 1) {
      return await this.validateSingleEventOverlap(deliveryData, projectSettingDetails);
    }

    if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
      return await this.validateRecurringEventsOverlap(deliveryData, projectSettingDetails);
    }

    return null;
  },

  async editCraneRequest(inputData, done) {
    try {
      const craneRequestData = inputData.body;
      const loginUser = await this.fetchUserDetails(inputData);
      const projectSettingDetails = await this.fetchProjectSettings(craneRequestData.ProjectId);
      
      await this.validateNoBookingConflicts(craneRequestData, projectSettingDetails);
      
      const {
        editSeriesRequests,
        newRecurrenceId
      } = await this.handleSeriesTypeAndSetup(craneRequestData, loginUser);

      await this.processEachCraneRequest(
        inputData,
        craneRequestData,
        editSeriesRequests,
        loginUser,
        projectSettingDetails,
        newRecurrenceId,
        done
      );
    } catch (e) {
      done(null, e);
    }
  },

  async fetchUserDetails(inputData) {
    const userDetail = await User.findOne({
      where: { id: inputData.body.userId, isDeleted: false },
    });
    inputData.user = userDetail;
    return inputData.user;
  },

  async fetchProjectSettings(projectId) {
    return await Project.getProjectAndSettings({
      isDeleted: false,
      id: +projectId,
    });
  },

  async validateNoBookingConflicts(craneRequestData, projectSettingDetails) {
    if (!projectSettingDetails) return;

    const requestData = await CraneRequest.getSingleCraneRequestData({ id: craneRequestData.id });

    const requestArray = await this.prepareBookingArrayForConflictCheck(craneRequestData, requestData);

    if (requestArray.length > 0) {
      const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
        requestArray,
        projectSettingDetails,
        'edit'
      );
      if (isOverlapping?.error) {
        throw new Error(isOverlapping.message);
      }
    }
  },

  async prepareBookingArrayForConflictCheck(craneRequestData, requestData) {
    const requestArray = [];
    const recurrenceId = craneRequestData.recurrenceId;
    const timezone = craneRequestData.timezone;

    if (craneRequestData.seriesOption === 1) {
      requestArray.push({
        ProjectId: craneRequestData.ProjectId,
        craneDeliveryStart: craneRequestData.craneDeliveryStart,
        craneDeliveryEnd: craneRequestData.craneDeliveryEnd,
        id: craneRequestData.id,
      });
    } else if ([2, 3].includes(craneRequestData.seriesOption)) {
      let requestSeries = [];

      if (craneRequestData.seriesOption === 2) {
        requestSeries = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: craneRequestData.id,
              },
            }),
          ],
        });
      } else {
        requestSeries = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              craneDeliveryStart: {
                [Op.gte]: moment.tz(timezone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }

      for (let item of requestSeries) {
        const deliveryStart = await this.convertTimezoneToUtc(
          moment.utc(item.craneDeliveryStart).tz(timezone).format('MM/DD/YYYY'),
          timezone,
          craneRequestData.deliveryStartTime
        );
        const deliveryEnd = await this.convertTimezoneToUtc(
          moment.utc(item.craneDeliveryEnd).tz(timezone).format('MM/DD/YYYY'),
          timezone,
          craneRequestData.deliveryEndTime
        );

        requestArray.push({
          ProjectId: craneRequestData.ProjectId,
          craneDeliveryStart: deliveryStart,
          craneDeliveryEnd: deliveryEnd,
          id: item.id,
        });
      }

      // Check for added days if recurrence end date changed
      const existingEnd = moment(requestData.recurrence.recurrenceEndDate).tz(timezone).format('YYYY-MM-DD');
      const newEnd = craneRequestData.recurrenceEndDate;

      if (!moment(existingEnd).isSame(moment(newEnd))) {
        const startDate = moment(existingEnd).add(1, 'day');
        const endDate = moment(newEnd);
        for (let date = startDate.clone(); date.isSameOrBefore(endDate); date.add(1, 'day')) {
          requestArray.push({
            ProjectId: craneRequestData.ProjectId,
            craneDeliveryStart: await this.convertTimezoneToUtc(
              date.format('MM/DD/YYYY'),
              timezone,
              craneRequestData.deliveryStartTime
            ),
            craneDeliveryEnd: await this.convertTimezoneToUtc(
              date.format('MM/DD/YYYY'),
              timezone,
              craneRequestData.deliveryEndTime
            ),
          });
        }
      }
    }

    return requestArray;
  },

  async handleSeriesTypeAndSetup(craneRequestData, loginUser) {
    let editSeriesRequests = [];
    let newRecurrenceId = null;
    let previousRecordInSeries = null;

    if (craneRequestData.seriesOption === 1) {
      editSeriesRequests = await this.handleThisEventSeries(craneRequestData);
    }

    if (craneRequestData.seriesOption === 2) {
      ({ editSeriesRequests, previousRecordInSeries } = await this.handleThisAndFollowingEvents(craneRequestData));
    }

    if (craneRequestData.seriesOption === 3) {
      editSeriesRequests = await this.handleAllEvents(craneRequestData);
    }

    newRecurrenceId = await this.maybeInsertRecurrenceSeriesIfNeeded(
      craneRequestData,
      editSeriesRequests,
      previousRecordInSeries,
      loginUser
    );

    return { editSeriesRequests, newRecurrenceId, previousRecordInSeries };
  },

  async handleThisEventSeries(craneRequestData) {
    return await CraneRequest.findAll({
      where: [
        Sequelize.and({ id: craneRequestData.id }),
      ],
    });
  },

  async handleThisAndFollowingEvents(craneRequestData) {
    const editSeriesRequests = await CraneRequest.findAll({
      where: [
        Sequelize.and({
          recurrenceId: craneRequestData.recurrenceId,
          id: {
            [Op.gte]: craneRequestData.id,
          },
        }),
      ],
    });

    const previousRecordInSeries = await CraneRequest.findOne({
      where: [
        Sequelize.and({
          recurrenceId: craneRequestData.recurrenceId,
          id: {
            [Op.lt]: craneRequestData.id,
          },
        }),
      ],
      order: [['id', 'DESC']],
    });

    return { editSeriesRequests, previousRecordInSeries };
  },

  async handleAllEvents(craneRequestData) {
    return await CraneRequest.findAll({
      where: [
        Sequelize.and({
          recurrenceId: craneRequestData.recurrenceId,
          craneDeliveryStart: {
            [Op.gte]: moment.tz(craneRequestData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
          },
        }),
      ],
    });
  },

  async maybeInsertRecurrenceSeriesIfNeeded(craneRequestData, editSeriesRequests, previousRecordInSeries, loginUser) {
    if (
      craneRequestData.seriesOption === 2 &&
      previousRecordInSeries &&
      editSeriesRequests.length > 0
    ) {
      const requestData = await CraneRequest.getSingleCraneRequestData({
        id: editSeriesRequests[0].id,
      });

      if (requestData?.recurrence) {
        requestData.recurrence.ParentCompanyId = craneRequestData.ParentCompanyId;
        requestData.recurrence.ProjectId = craneRequestData.ProjectId;
        requestData.recurrence.craneDeliveryStart = craneRequestData.recurrenceSeriesStartDate;
        requestData.recurrence.craneDeliveryEnd = craneRequestData.recurrenceEndDate;

        return await concreteRequestService.insertRecurrenceSeries(
          requestData.recurrence,
          loginUser,
          requestData.requestType,
          craneRequestData.timezone
        );
      }
    }
    return null;
  },

  async processEachCraneRequest(
    inputData,
    craneRequestData,
    editSeriesRequests,
    loginUser,
    projectSettingDetails,
    newRecurrenceId,
    done
  ) {
    for (const seriesData of editSeriesRequests) {
      await this.processSingleCraneRequest(
        inputData,
        craneRequestData,
        seriesData,
        loginUser,
        projectSettingDetails,
        newRecurrenceId,
        done
      );
    }
  },

  async processSingleCraneRequest(
    inputData,
    craneRequestData,
    seriesData,
    loginUser,
    projectSettingDetails,
    newRecurrenceId,
    done
  ) {
    const isCraneRequestExists = await CraneRequest.findOne({ where: { id: seriesData.id } });
    if (!isCraneRequestExists) return done(null, { message: 'Crane Booking id is not available' });

    const existsCraneRequest = await CraneRequest.getSingleCraneRequestData({ id: +seriesData.id });

    await this.checkCraneInputDatas(inputData, async (checkResponse, checkError) => {
      if (checkError) return done(null, checkError);

      const memberData = await this.fetchMemberData(loginUser.id, craneRequestData.ProjectId);

      const updatedParams = await this.buildCraneRequestParams(
        craneRequestData,
        seriesData,
        isCraneRequestExists,
        memberData,
        loginUser,
        newRecurrenceId
      );

      await CraneRequest.update(updatedParams, { where: { id: isCraneRequestExists.id } });

      await this.updateAssociatedEntitiesData(craneRequestData, isCraneRequestExists.id);

      const updatedCraneRequest = await CraneRequest.getSingleCraneRequestData({
        id: +isCraneRequestExists.id,
      });

      const history = await this.logHistoryAndNotifications(
        craneRequestData,
        updatedCraneRequest,
        existsCraneRequest,
        loginUser,
        memberData,
        projectSettingDetails
      );

      await this.handleStatusTransitions(
        existsCraneRequest,
        updatedCraneRequest,
        craneRequestData,
        memberData,
        loginUser,
        projectSettingDetails
      );

      await this.sendReapprovalEmailIfNeeded(
        updatedCraneRequest,
        craneRequestData,
        loginUser,
        memberData,
        history
      );

      return done(history, false);
    });
  },

  async fetchMemberData(userId, projectId) {
    return await Member.getBy({ UserId: userId, ProjectId: projectId });
  },

  async buildCraneRequestParams(
    craneRequestData,
    seriesData,
    isCraneRequestExists,
    memberData,
    loginUser,
    newRecurrenceId
  ) {
    const param = {
      description: craneRequestData.description,
      isEscortNeeded: craneRequestData.isEscortNeeded,
      additionalNotes: craneRequestData.additionalNotes,
      isAssociatedWithDeliveryRequest: craneRequestData.isAssociatedWithDeliveryRequest,
      pickUpLocation: craneRequestData.pickUpLocation,
      dropOffLocation: craneRequestData.dropOffLocation,
      recurrenceId: craneRequestData.seriesOption !== 1 ? newRecurrenceId : null,
      LocationId: craneRequestData.LocationId,
    };

    if (craneRequestData.seriesOption === 1) {
      param.craneDeliveryStart = craneRequestData.craneDeliveryStart;
      param.craneDeliveryEnd = craneRequestData.craneDeliveryEnd;
    } else {
      const timezone = craneRequestData.timezone;
      const localStart = moment.utc(isCraneRequestExists.craneDeliveryStart).tz(timezone);
      const localEnd = moment.utc(isCraneRequestExists.craneDeliveryEnd).tz(timezone);

      param.craneDeliveryStart = await this.convertTimezoneToUtc(
        moment(localStart).format('MM/DD/YYYY'),
        timezone,
        craneRequestData.deliveryStartTime
      );
      param.craneDeliveryEnd = await this.convertTimezoneToUtc(
        moment(localEnd).format('MM/DD/YYYY'),
        timezone,
        craneRequestData.deliveryEndTime
      );
    }

    if (
      ((memberData.RoleId === 2 || memberData.RoleId === 1) && isCraneRequestExists.status === 'Approved') ||
      memberData.isAutoApproveEnabled
    ) {
      param.status = 'Approved';
      param.approvedBy = memberData.id;
      param.approved_at = new Date();
    }

    return param;
  },

  async updateAssociatedEntitiesData(craneRequestData, craneRequestId) {
    const condition = {
      ProjectId: craneRequestData.ProjectId,
      CraneRequestId: craneRequestId,
    };

    const updateParam = {
      CraneRequestId: craneRequestId,
      CraneRequestCode: craneRequestId,
      ProjectId: craneRequestData.ProjectId,
      isDeleted: false,
    };

    await craneRequestService.updateValues(condition, async (response, error) => {
      if (error) throw error;

      await this.upsertAssociations(CraneRequestCompany, craneRequestData.companies, 'CompanyId', updateParam);
      await this.upsertAssociations(CraneRequestEquipment, craneRequestData.EquipmentId, 'EquipmentId', updateParam);
      await this.upsertAssociations(CraneRequestResponsiblePerson, craneRequestData.responsiblePersons, 'MemberId', updateParam);
      await this.upsertAssociations(CraneRequestDefinableFeatureOfWork, craneRequestData.definableFeatureOfWorks, 'DeliverDefineWorkId', updateParam);
    });
  },

  async upsertAssociations(model, ids, key, baseParam) {
    const existing = await model.findAll({ where: { CraneRequestId: baseParam.CraneRequestId } });

    for (let id of ids) {
      const found = existing.find((e) => e[key] === id);
      const param = { ...baseParam, [key]: id };
      if (found) {
        await model.update(param, { where: { id: found.id } });
      } else {
        await model.createInstance(param);
      }
    }
  },

  async logHistoryAndNotifications(
    craneRequestData,
    updatedCraneRequest,
    existsCraneRequest,
    loginUser,
    memberData,
    projectSettingDetails
  ) {
    const history = {
      CraneRequestId: updatedCraneRequest.id,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} Edited this Crane Booking.`,
      createdAt: new Date(),
      ProjectId: craneRequestData.ProjectId,
    };

    // Example notification logic placeholder
    await Notification.createInstance({
      title: `Crane Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`,
      ProjectId: updatedCraneRequest.ProjectId,
      requestType: 'craneRequest',
      isDeliveryRequest: false,
    });

    return history;
  },

  checkIfFieldsChanged(updated, existing) {
    const basicFields = [
      "description",
      "CraneRequestId",
      "LocationId",
      "requestType",
      "additionalNotes",
      "isAssociatedWithDeliveryRequest",
      "isEscortNeeded",
      "dropOffLocation",
      "pickUpLocation",
    ];

    // Check basic fields
    const basicFieldChanges = basicFields.some(
      (field) => updated[field] !== existing[field]
    );

    // Helper function to check array differences
    const arraysHaveChanges = (arr1, arr2, keyPath) => {
      if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
      const getKey = (obj) => keyPath.reduce((val, k) => val?.[k], obj);

      return (
        arr1.some((el) => !arr2.some((e) => getKey(e) === getKey(el))) ||
        arr2.some((el) => !arr1.some((e) => getKey(e) === getKey(el)))
      );
    };

    // Compare all array details
    const tagsUpdated =
      arraysHaveChanges(updated.defineWorkDetails, existing.defineWorkDetails, ["id"]) ||
      arraysHaveChanges(updated.equipmentDetails, existing.equipmentDetails, ["Equipment", "id"]) ||
      arraysHaveChanges(updated.companyDetails, existing.companyDetails, ["Company", "id"]) ||
      arraysHaveChanges(updated.memberDetails, existing.memberDetails, ["Member", "id"]);

    return basicFieldChanges || tagsUpdated;
  },

  async handleStatusTransitions(
    existsCraneRequest,
    updatedCraneRequest,
    craneRequestData,
    memberData,
    loginUser,
    projectSettingDetails
  ) {
    const status = updatedCraneRequest.status;
    const fieldsChanged = this.checkIfFieldsChanged(updatedCraneRequest, existsCraneRequest);

    const shouldBePending =
      fieldsChanged &&
      memberData.RoleId !== 2 &&
      !memberData.isAutoApproveEnabled &&
      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled;

    const shouldBeApproved =
      fieldsChanged &&
      (memberData.RoleId === 2 ||
        memberData.isAutoApproveEnabled ||
        projectSettingDetails.ProjectSettings.isAutoApprovalEnabled);

    if (status === 'Completed' || status === 'Declined' || status === 'Expired') {
      if (shouldBePending) {
        await CraneRequest.update({ status: 'Pending' }, { where: { id: updatedCraneRequest.id } });
      } else if (shouldBeApproved) {
        await CraneRequest.update({ status: 'Approved' }, { where: { id: updatedCraneRequest.id } });
      }
    } else if (status === 'Pending' && shouldBeApproved) {
      await CraneRequest.update({ status: 'Approved' }, { where: { id: updatedCraneRequest.id } });
    }
  },

  async  sendReapprovalEmailIfNeeded(
    updatedCraneRequest,
    craneRequestData,
    loginUser,
    memberData,
    history
  ) {
    if ([3, 4].includes(memberData.RoleId)) {
      const userEmails = await craneRequestService.getMemberDetailData(history);
      const role = await Role.findOne({ where: { id: memberData.RoleId } });

      for (let member of userEmails) {
        const name = member.firstName ? `${member.firstName} ${member.lastName}` : 'user';

        const mailPayload = {
          name,
          email: member.email,
          content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} - ${role.roleName} has updated a crane booking ${updatedCraneRequest.CraneRequestId} and waiting for your approval.`,
        };

        await MAILER.sendMail(
          mailPayload,
          'notifyPAForReApproval',
          `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${role.roleName}`,
          `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${role.roleName}`
        );
      }
    }
  },



  async getInitialDataAndProjectSettings(inputData) {
    const concreteRequestData = inputData.body;
    const userDetail = await User.findOne({
      where: { id: concreteRequestData.userId, isDeleted: false },
    });
    inputData.user = userDetail;

    const projectSettingDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +concreteRequestData.ProjectId,
    });

    return { userDetail, projectSettingDetails };
  },

  async validateBookingOverlap(concreteRequestData, projectSettingDetails) {
    const { recurrenceId, seriesOption, ProjectId, timezone } = concreteRequestData;

    if (seriesOption === 1) {
      const requestArray = [{
        ProjectId,
        concretePlacementStart: concreteRequestData.concretePlacementStart,
        concretePlacementEnd: concreteRequestData.concretePlacementEnd,
        id: concreteRequestData.id,
      }];
      return await concreteRequestService.checkDoubleBookingAllowedOrNot(requestArray, projectSettingDetails, 'add');
    }

    if (seriesOption === 2 || seriesOption === 3) {
      let requestSeries = [];
      const whereCondition = seriesOption === 2
        ? { recurrenceId, id: { [Op.gte]: concreteRequestData.id } }
        : {
            recurrenceId,
            concretePlacementStart: {
              [Op.gte]: moment.tz(timezone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
            },
          };

      requestSeries = await ConcreteRequest.findAll({ where: [Sequelize.and(whereCondition)] });

      const requestArray = await Promise.all(
        requestSeries.map(async (request) => {
          const deliveryStartDate = await this.convertTimezoneToUtc(
            moment.utc(request.concretePlacementStart).tz(timezone).format('MM/DD/YYYY'),
            timezone,
            concreteRequestData.deliveryStartTime,
          );
          const deliveryEndDate = await this.convertTimezoneToUtc(
            moment.utc(request.concretePlacementEnd).tz(timezone).format('MM/DD/YYYY'),
            timezone,
            concreteRequestData.deliveryEndTime,
          );
          return {
            ProjectId,
            concretePlacementStart: deliveryStartDate,
            concretePlacementEnd: deliveryEndDate,
            id: request.id,
          };
        })
      );

      return await concreteRequestService.checkDoubleBookingAllowedOrNot(requestArray, projectSettingDetails, 'edit');
    }

    return null;
  },

  async handleRecurrenceAdjustments(concreteRequestData, recurrenceId, loginUser) {
    let editSeriesRequests = [];
    let previousRecordInSeries = null;
    let newRecurrenceId = null;

    const whereClause =
      concreteRequestData.seriesOption === 2
        ? { recurrenceId, id: { [Op.gte]: concreteRequestData.id } }
        : {
            recurrenceId,
            concretePlacementStart: {
              [Op.gte]: moment.tz(concreteRequestData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
            },
          };

    editSeriesRequests = await ConcreteRequest.findAll({ where: [Sequelize.and(whereClause)] });

    if (concreteRequestData.seriesOption === 2) {
      previousRecordInSeries = await ConcreteRequest.findOne({
        where: [Sequelize.and({ recurrenceId, id: { [Op.lt]: concreteRequestData.id } })],
        order: [['id', 'DESC']],
      });

      if (previousRecordInSeries) {
        const recurrence = await ConcreteRequest.getSingleConcreteRequestData({ id: editSeriesRequests[0].id });
        newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
          recurrence.recurrence,
          loginUser,
          recurrence.requestType,
          concreteRequestData.timezone
        );
      }
    }

    return { editSeriesRequests, newRecurrenceId, previousRecordInSeries };
  },

  async updateConcreteRequestSeries(
    editSeriesRequests,
    concreteRequestData,
    newRecurrenceId,
    loginUser,
    projectSettingDetails,
    previousRecordInSeries,
    done
  ) {
    for (let request of editSeriesRequests) {
      const isExists = await ConcreteRequest.findOne({
        where: [Sequelize.and({ id: request.id, isDeleted: false })],
      });
      if (!isExists) return done(null, { message: 'Concrete Booking id is not available' });

      // Call internal update function to refactor per-record logic (defined below)
      await this.updateConcreteRequestRecord(
        request,
        concreteRequestData,
        loginUser,
        newRecurrenceId,
        projectSettingDetails,
        previousRecordInSeries,
        done
      );
    }
  },

    async editConcreteRequest(inputData, done) {
    try {
      const { userDetail, projectSettingDetails } = await this.getInitialDataAndProjectSettings(inputData);
      const concreteRequestData = inputData.body;
      const loginUser = userDetail;
      inputData.user = loginUser;

      const overlapCheck = await this.validateBookingOverlap(this, concreteRequestData, projectSettingDetails);
      if (overlapCheck?.error) return done(null, { message: overlapCheck.message });

      const { editSeriesRequests, newRecurrenceId, previousRecordInSeries } =
        await this.handleRecurrenceAdjustments(concreteRequestData, concreteRequestData.recurrenceId, loginUser);

      await this.updateConcreteRequestSeries(
        editSeriesRequests,
        concreteRequestData,
        newRecurrenceId,
        loginUser,
        projectSettingDetails,
        previousRecordInSeries,
        done
      );
    } catch (err) {
      done(null, err);
    }
  },

  async validateMemberPermissionsAndDates(memberData, concreteRequestData, done) {
    const startDate = new Date(concreteRequestData.concretePlacementStart).getTime();
    const endDate = new Date(concreteRequestData.concretePlacementEnd).getTime();
    const now = Date.now();

    if (memberData.RoleId === 4 && startDate < now && endDate < now) {
      return done(null, { message: 'Please enter Future start or end date.' });
    }
  },

  async prepareAndUpdateRequestData({
    existsConcreteRequest,
    concreteRequestData,
    loginUser,
    memberData,
    newRecurrenceId,
    projectSettingDetails,
  }) {
    const params = {
      ...commonFieldsFromRequest(concreteRequestData),
      recurrenceId: concreteRequestData.seriesOption !== 1 ? newRecurrenceId : null,
    };

    if (concreteRequestData.seriesOption === 1) {
      params.concretePlacementStart = concreteRequestData.concretePlacementStart;
      params.concretePlacementEnd = concreteRequestData.concretePlacementEnd;
    } else {
      params.concretePlacementStart = await this.convertTimezoneToUtc(
        moment(existsConcreteRequest.concretePlacementStart).format('MM/DD/YYYY'),
        concreteRequestData.timezone,
        concreteRequestData.deliveryStartTime
      );
      params.concretePlacementEnd = await this.convertTimezoneToUtc(
        moment(existsConcreteRequest.concretePlacementEnd).format('MM/DD/YYYY'),
        concreteRequestData.timezone,
        concreteRequestData.deliveryEndTime
      );
    }

    if (
      (memberData.RoleId === 1 || memberData.RoleId === 2) ||
      memberData.isAutoApproveEnabled ||
      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      params.status = 'Approved';
      params.approvedBy = memberData.id;
      params.approved_at = new Date();
    }

    await ConcreteRequest.update(params, { where: { id: existsConcreteRequest.id } });
  },

  async updateAssociatedEntities({
      existsConcreteRequest,
      concreteRequestData,
      loginUser,
    }) {
      const condition = Sequelize.and({
        ProjectId: concreteRequestData.ProjectId,
        ConcreteRequestId: existsConcreteRequest.id,
      });

      const updateParam = {
        ConcreteRequestId: existsConcreteRequest.id,
        ProjectId: concreteRequestData.ProjectId,
        isDeleted: false,
        ConcreteRequestCode: existsConcreteRequest.ConcreteRequestId,
      };

      await concreteRequestService.updateValues(condition, async () => {
        await this.updateResponsiblePersons(concreteRequestData, updateParam, condition);
        await this.updatePumpSizes(concreteRequestData, updateParam, condition, loginUser);
        await this.updateMixDesigns(concreteRequestData, updateParam, condition, loginUser);
        await this.updateSuppliers(concreteRequestData, updateParam, condition);
        await this.updateLocationIfNeeded(concreteRequestData, existsConcreteRequest);
      });
    },


  async handleApprovalsAndStatusUpdate({
    existsConcreteRequest,
    updatedConcreteRequest,
    memberData,
    loginUser,
    concreteRequestData,
    projectSettingDetails,
  }) {
    const shouldAutoApprove =
      memberData.RoleId === 2 ||
      memberData.isAutoApproveEnabled ||
      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled;

    if (existsConcreteRequest.status === 'Declined') {
      await ConcreteRequest.update(
        { status: shouldAutoApprove ? 'Approved' : 'Tentative', approvedBy: shouldAutoApprove ? memberData.id : null },
        { where: { id: updatedConcreteRequest.id } }
      );
    }

    if (existsConcreteRequest.status === 'Tentative' && shouldAutoApprove) {
      await ConcreteRequest.update(
        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
        { where: { id: updatedConcreteRequest.id } }
      );
    }

    if (existsConcreteRequest.status === 'Approved' && memberData.RoleId === 4 && !shouldAutoApprove) {
      await ConcreteRequest.update({ status: 'Tentative' }, { where: { id: updatedConcreteRequest.id } });
    }
  },

  async buildAndSendNotifications({
    existsConcreteRequest,
    updatedConcreteRequest,
    concreteRequestData,
    loginUser,
    memberData,
    projectSettingDetails,
  }) {
    const history = {
      description: `${loginUser.firstName} ${loginUser.lastName} updated the Concrete Booking, ${concreteRequestData.description}`,
      type: 'edit',
      MemberId: memberData.id,
      ProjectId: concreteRequestData.ProjectId,
      ConcreteRequestId: existsConcreteRequest.id,
    };

    const notification = {
      title: `Concrete Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`,
      requestType: 'concreteRequest',
      isDeliveryRequest: false,
      ProjectId: concreteRequestData.ProjectId,
    };

    const newNotification = await Notification.createInstance(notification);
    await pushNotification.sendPushNotificationForConcrete(history, 5, concreteRequestData.ProjectId);

    return { history, notification: newNotification };
  },

  async updateConcreteRequestRecord(
    request,
    concreteRequestData,
    loginUser,
    newRecurrenceId,
    projectSettingDetails,
    previousRecordInSeries,
    done
  ) {
    const existsConcreteRequest = await ConcreteRequest.getSingleConcreteRequestData({ id: +request.id });
    const memberData = await Member.getBy({ UserId: loginUser.id, ProjectId: concreteRequestData.ProjectId });

    await this.validateMemberPermissionsAndDates(memberData, concreteRequestData, done);

    await this.prepareAndUpdateRequestData({
      existsConcreteRequest,
      concreteRequestData,
      loginUser,
      memberData,
      newRecurrenceId,
      projectSettingDetails
    });

    await this.updateAssociatedEntities({ existsConcreteRequest, concreteRequestData, loginUser });

    const updatedConcreteRequest = await ConcreteRequest.getSingleConcreteRequestData({ id: +existsConcreteRequest.id });

    await this.handleApprovalsAndStatusUpdate({
      existsConcreteRequest,
      updatedConcreteRequest,
      memberData,
      loginUser,
      concreteRequestData,
      projectSettingDetails,
    });

    await this.buildAndSendNotifications({
      existsConcreteRequest,
      updatedConcreteRequest,
      concreteRequestData,
      loginUser,
      memberData,
      projectSettingDetails,
    });

    return done({ message: 'Concrete request updated successfully' });
  },

  async updateResponsiblePersons(concreteRequestData, updateParam, condition) {
    const existing = await ConcreteRequestResponsiblePerson.findAll({ where: condition });
    const responsiblePersons = concreteRequestData.responsiblePersons;

    for (const memberId of responsiblePersons) {
      const index = existing.findIndex((item) => item.MemberId === memberId);
      const memberParam = { ...updateParam, MemberId: memberId };

      if (index !== -1) {
        await ConcreteRequestResponsiblePerson.update(memberParam, {
          where: { id: existing[index].id },
        });
      } else {
        await ConcreteRequestResponsiblePerson.createInstance(memberParam);
      }
    }
  },

  async updatePumpSizes(concreteRequestData, updateParam, condition, loginUser) {
    const existing = await ConcreteRequestPumpSize.findAll({ where: condition });
    const pumpSizes = concreteRequestData.pumpSize;

    for (const pump of pumpSizes) {
      const index = existing.findIndex((item) => item.ConcretePumpSizeId === pump.id);
      const pumpSizeParam = { ...updateParam };

      if (index !== -1) {
        pumpSizeParam.ConcretePumpSizeId = pump.id;
        await ConcreteRequestPumpSize.update(pumpSizeParam, {
          where: { id: existing[index].id },
        });
      } else {
        if (!pump.chosenFromDropdown) {
          const newPump = await ConcretePumpSize.createConcretePumpSize({
            pumpSize: pump.pumpSize,
            ProjectId: updateParam.ProjectId,
            isDeleted: false,
            createdBy: loginUser.id,
          });
          pumpSizeParam.ConcretePumpSizeId = newPump.id;
        } else {
          pumpSizeParam.ConcretePumpSizeId = pump.id;
        }
        await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
      }
    }
  },

  async updateSuppliers(concreteRequestData, updateParam, condition) {
    const existing = await ConcreteRequestCompany.findAll({ where: condition });
    const suppliers = concreteRequestData.concreteSupplier;

    for (const companyId of suppliers) {
      const index = existing.findIndex((item) => item.CompanyId === companyId);
      const companyParam = { ...updateParam, CompanyId: companyId };

      if (index !== -1) {
        await ConcreteRequestCompany.update(companyParam, {
          where: { id: existing[index].id },
        });
      } else {
        await ConcreteRequestCompany.createInstance(companyParam);
      }
    }
  },

  async updateLocationIfNeeded(concreteRequestData, existsConcreteRequest) {
    if (
      existsConcreteRequest?.locationDetails?.[0]?.ConcreteLocation?.id
    ) {
      const locationId = existsConcreteRequest.locationDetails[0].ConcreteLocation.id;
      const locationRecord = await ConcreteLocation.findOne({ where: { id: locationId } });

      if (locationRecord) {
        await ConcreteLocation.update(
          { location: concreteRequestData.location },
          { where: { id: locationId } }
        );
      }
    }
  },


  async createAttachement(inputData, done) {
    try {
      const incomeData = inputData.params;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await DeliveryRequest.findOne({ where: { id: incomeData.DeliveryRequestId } });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: exist.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberDetail.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachement: element.Location,
                filename: fileName,
                extension,
                DeliveryRequestId: +incomeData.DeliveryRequestId,
                DeliveryId: exist.DeliveryId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await DeliverAttachement.createMultipleInstance(bulkData);
              const history = {
                DeliveryRequestId: incomeData.DeliveryRequestId,
                DeliveryId: exist.DeliveryId,
                MemberId: memberDetail.id,
                type: 'attachement',
                description: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in ${exist.description}`,
                locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              const notification = history;
              notification.ProjectId = exist.ProjectId;
              notification.title = 'Delivery Booking Attachment';
              DeliverHistory.createInstance(history);
              const personData = await DeliveryPerson.findAll({
                where: { DeliveryId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: exist.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = exist.ProjectId;
              const projectDetails = await Project.findByPk(exist.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  exist.DeliveryRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  1,
                );
                // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  1,
                );
              }
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in',
                'Delivery Request',
                `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
                incomeData.DeliveryRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: exist.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await pushNotification.sendDeviceToken(history, 1, exist.ProjectId);
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createComment(inputData, done) {
    try {
      let resultedArray;
      const incomeData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const exist = await DeliveryRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: { id: incomeData.DeliveryRequestId },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: exist.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        incomeData.ProjectId = exist.ProjectId;
        const history = {
          DeliveryRequestId: incomeData.DeliveryRequestId,
          DeliveryId: exist.DeliveryId,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Delivery Booking Comment';
        const previousComments = await DeliverComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            DeliveryRequestId: incomeData.DeliveryRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        await DeliverComment.createInstance(inputData.body);
        await DeliverHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.requestType = 'deliveryRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await DeliveryPerson.findAll({
          where: { DeliveryId: history.DeliveryRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });
        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id', 'RoleId'],
        });
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await pushNotification.sendMemberLocationPreferencePushNotification(
            memberLocationPreference,
            exist.DeliveryRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Delivery Request',
          `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
          incomeData.DeliveryRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await pushNotification.sendDeviceToken(history, 4, exist.ProjectId);
        const userEmails = await commentService.getMemberDetailData(
          history,
          memberLocationPreference,
        );
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.deliveryStart).format('MM-DD-YYYY');
            const mailPayload = {
              deliveryId: exist.DeliveryId,
              deliveryDescription: exist.description,
              deliveryStart: time,
              deliveryEnd: exist.deliveryEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification?.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'commentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Delivery ID ${exist.DeliveryId}`,
                  'Comments added against a Delivery Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification?.dailyDigest) {
                await commentService.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Delivery Request',
                  `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
                  incomeData.DeliveryRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createCraneRequestAttachement(inputData, done) {
    try {
      const incomeData = inputData.params;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await CraneRequest.findOne({
        where: { CraneRequestId: incomeData.CraneRequestId, ProjectId: incomeData.ProjectId },
      });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: exist.ProjectId,
                id: exist.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: exist.ProjectId,
                LocationId: exist.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberDetail.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachement: element.Location,
                filename: fileName,
                extension,
                CraneRequestId: +exist.id,
                ProjectId: +incomeData.ProjectId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await CraneRequestAttachment.createMultipleInstance(bulkData);
              const history = {
                CraneRequestId: +exist.id,
                MemberId: memberDetail.id,
                type: 'attachement',
                description: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in ${exist.description}`,
                locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in the Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              history.ProjectId = incomeData.ProjectId;
              const notification = history;
              notification.ProjectId = incomeData.ProjectId;
              notification.title = 'Crane Booking Attachment';
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    {
                      ProjectId: incomeData.ProjectId,
                      isDeleted: false,
                      id: { [Op.notIn]: locationFollowMembers },
                    },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              CraneRequestHistory.createInstance(history);
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = incomeData.ProjectId;
              const projectDetails = await Project.findByPk(incomeData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  exist.CraneRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  2,
                );
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  2,
                );
              }
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in a',
                'Crane Request',
                `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
                incomeData.CraneRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: incomeData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await pushNotification.sendPushNotificationForCrane(history, 1, incomeData.ProjectId);
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createCraneRequestComment(inputData, done) {
    try {
      let resultedArray;
      const incomeData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const exist = await CraneRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: { CraneRequestId: +incomeData.CraneRequestId, ProjectId: +incomeData.ProjectId },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        const history = {
          CraneRequestId: +exist.id,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Crane Booking Comment';
        const previousComments = await CraneRequestComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            CraneRequestId: incomeData.CraneRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        const addCraneRequestCommentObject = {
          ProjectId: inputData.body.ProjectId,
          MemberId: memberData.id,
          CraneRequestId: exist.id,
          isDeleted: false,
          comment: inputData.body.comment,
        };
        await CraneRequestComment.createInstance(addCraneRequestCommentObject);
        await CraneRequestHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.isDeliveryRequest = false;
        notification.requestType = 'craneRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await CraneRequestResponsiblePerson.findAll({
          where: { CraneRequestId: history.CraneRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });

        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id'],
        });
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
            memberLocationPreference,
            exist.CraneRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Crane Request',
          `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
          incomeData.CraneRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await pushNotification.sendPushNotificationForCrane(history, 4, exist.ProjectId);
        const userEmails = await craneRequestCommentService.getMemberDetailData(
          history,
          memberLocationPreference,
        );
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.craneDeliveryStart).format('MM-DD-YYYY');
            const mailPayload = {
              craneId: exist.CraneRequestId,
              craneDescription: exist.description,
              craneDeliveryStart: time,
              craneDeliveryEnd: exist.craneDeliveryEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification?.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'cranecommentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Crane ID ${exist.CraneRequestId}`,
                  'Comments added against a Crane Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification?.dailyDigest) {
                await craneRequestCommentService.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Crane Request',
                  `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
                  exist.CraneRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Crane Booking does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createConcreteRequestAttachment(inputData, done) {
    try {
      const incomeData = inputData.params;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: incomeData.ConcreteRequestId,
          ProjectId: incomeData.ProjectId,
          isDeleted: false,
        },
      });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: exist.ProjectId,
                id: exist.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: exist.ProjectId,
                LocationId: exist.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberDetail.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachment: element.Location,
                filename: fileName,
                extension,
                ConcreteRequestId: +exist.id,
                ProjectId: +incomeData.ProjectId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await ConcreteRequestAttachment.createMultipleInstance(bulkData);
              const history = {
                ConcreteRequestId: +exist.id,
                MemberId: memberDetail.id,
                type: 'attachment',
                description: `${loginUser.firstName} ${loginUser.lastName} attached the file in ${exist.description}`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} attached the file in the Booking ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              history.ProjectId = incomeData.ProjectId;
              const notification = history;
              notification.ProjectId = incomeData.ProjectId;
              notification.title = 'Concrete Booking Attachment';
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    {
                      ProjectId: incomeData.ProjectId,
                      isDeleted: false,
                      id: { [Op.notIn]: locationFollowMembers },
                    },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              ConcreteRequestHistory.createInstance(history);
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = incomeData.ProjectId;
              const projectDetails = await Project.findByPk(incomeData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  exist.ConcreteRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  2,
                );
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  2,
                );
              }
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in a',
                'Concrete Request',
                `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
                incomeData.ConcreteRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: incomeData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                1,
                incomeData.ProjectId,
              );
              history.ConcreteRequestId = exist.ConcreteRequestId;
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createConcreteRequestComment(inputData, done) {
    try {
      let resultedArray;
      const incomeData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const exist = await ConcreteRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: {
          ConcreteRequestId: incomeData.ConcreteRequestId,
          ProjectId: incomeData.ProjectId,
          isDeleted: false,
        },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        const history = {
          ConcreteRequestId: +exist.id,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Concrete Booking Comment';
        const previousComments = await ConcreteRequestComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            ConcreteRequestId: incomeData.ConcreteRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        const toAddCommentObject = inputData.body;
        // eslint-disable-next-line no-param-reassign
        delete inputData.body.ConcreteRequestId;
        toAddCommentObject.ConcreteRequestId = exist.id;
        await ConcreteRequestComment.createInstance(toAddCommentObject);
        await ConcreteRequestHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.isDeliveryRequest = false;
        notification.requestType = 'concreteRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await ConcreteRequestResponsiblePerson.findAll({
          where: { ConcreteRequestId: history.ConcreteRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });

        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id'],
        });
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
            memberLocationPreference,
            exist.ConcreteRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Concrete Request',
          `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
          incomeData.ConcreteRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        history.ConcreteRequestId = exist.ConcreteRequestId;
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await pushNotification.sendPushNotificationForConcrete(history, 4, exist.ProjectId);
        const userEmails = await concreteRequestCommentService.getMemberDetailData(
          history,
          memberLocationPreference,
        );
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.concretePlacementStart).format('MM-DD-YYYY');
            const mailPayload = {
              concreteId: exist.ConcreteRequestId,
              concreteDescription: exist.description,
              concreteStart: time,
              concreteEnd: exist.concretePlacementEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification?.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'concretecommentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Concrete Booking ID ${exist.ConcreteRequestId}`,
                  'Comments added against a Concrete Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification?.dailyDigest) {
                await concreteRequestCommentService.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Concrete Request',
                  `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
                  exist.ConcreteRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Concrete Booking does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async isRequestToMember(req) {
    const { userId, ProjectId } = req.body;
    const memberDetail = await Member.findOne({
      where: {
        isDeleted: false,
        isActive: true,
        UserId: userId,
        ProjectId,
      },
    });
    const data = memberDetail;
    return { data };
  },
  async updateGuestMember(req) {
    try {
      const { userId, ProjectId } = req.body;
      await Member.update(
        { isRequestedToBeAMember: true },
        {
          where: {
            isDeleted: false,
            isActive: true,
            UserId: +userId,
            ProjectId,
          },
        },
      );
      const memberDetail = await Member.findOne({
        where: {
          isDeleted: false,
          isActive: true,
          UserId: +userId,
          ProjectId,
        },
      });
      const data = memberDetail;

      // send email to all PA for guest request to be a member
      const projectAdminLists = await Member.findAll({
        include: [
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        where: {
          ProjectId,
          RoleId: 2,
          status: 'completed',
        },
      });
      const getProject = await Project.findOne({
        where: {
          isDeleted: false,
          id: +ProjectId,
        },
      });
      for (const admin of projectAdminLists) {
        const mailData = {
          email: admin.User.email,
          adminFirstName: admin.User.firstName,
          adminLastName: admin.User.lastName,
          guestFirstName: memberDetail.firstName,
          guestLastName: memberDetail.lastName,
          projectName: getProject.projectName,
        };

        await MAILER.sendMail(
          mailData,
          'guestRequested',
          'Guest Requested',
          'Guest Requested',
          (info, err) => {
            if (err) {
              throw new Error(err);
            } else {
              return { data };
            }
          },
        );
      }

      return { data };
    } catch (error) {
      console.log(error);
      throw new Error(error);
    }
  },
};

module.exports = guestUserService;
