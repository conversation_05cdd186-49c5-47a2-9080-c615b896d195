const status = require('http-status');
const CommentController = require('../CommentController');

// Mock the services
jest.mock('../../services', () => ({
  commentService: {
    createComment: jest.fn(),
    createInspectionComment: jest.fn(),
    getComment: jest.fn(),
    getInspectionComment: jest.fn(),
  },
}));

const { commentService } = require('../../services');

describe('CommentController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock request, response, and next function
    mockReq = {
      body: {},
      params: {},
      query: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('createComment', () => {
    it('should create comment successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        content: 'This is a test comment',
        userId: 123,
        createdAt: '2023-01-01T00:00:00Z',
      };
      commentService.createComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.createComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Comment creation failed');
      commentService.createComment.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CommentController.createComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.createComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      commentService.createComment.mockImplementation(() => {
        throw error;
      });

      // Act
      await CommentController.createComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle request with comment data', async () => {
      // Arrange
      const mockReqWithData = {
        ...mockReq,
        body: {
          content: 'This is a detailed comment',
          entityType: 'delivery',
          entityId: 456,
          userId: 789,
        },
      };
      const mockResponse = {
        id: 2,
        content: 'This is a detailed comment',
        entityType: 'delivery',
        entityId: 456,
        userId: 789,
      };
      commentService.createComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createComment(mockReqWithData, mockRes, mockNext);

      // Assert
      expect(commentService.createComment).toHaveBeenCalledWith(
        mockReqWithData,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
    });
  });

  describe('createInspectionComment', () => {
    it('should create inspection comment successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 3,
        content: 'This is an inspection comment',
        inspectionId: 101,
        userId: 456,
        createdAt: '2023-01-02T00:00:00Z',
      };
      commentService.createInspectionComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createInspectionComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.createInspectionComment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Inspection comment creation failed');
      commentService.createInspectionComment.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await CommentController.createInspectionComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.createInspectionComment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      commentService.createInspectionComment.mockImplementation(() => {
        throw error;
      });

      // Act
      await CommentController.createInspectionComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle request with inspection comment data', async () => {
      // Arrange
      const mockReqWithData = {
        ...mockReq,
        body: {
          content: 'Inspection finding: Safety issue detected',
          inspectionId: 202,
          severity: 'high',
          userId: 789,
        },
      };
      const mockResponse = {
        id: 4,
        content: 'Inspection finding: Safety issue detected',
        inspectionId: 202,
        severity: 'high',
        userId: 789,
      };
      commentService.createInspectionComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createInspectionComment(mockReqWithData, mockRes, mockNext);

      // Assert
      expect(commentService.createInspectionComment).toHaveBeenCalledWith(
        mockReqWithData,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
    });
  });

  describe('getComment', () => {
    it('should get comment successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        content: 'This is a test comment',
        userId: 123,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      };
      commentService.getComment.mockImplementation((req, callback) => callback(mockResponse, null));

      // Act
      await CommentController.getComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.getComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Comment retrieval failed');
      commentService.getComment.mockImplementation((req, callback) => callback(null, error));

      // Act
      await CommentController.getComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.getComment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      commentService.getComment.mockImplementation(() => {
        throw error;
      });

      // Act
      await CommentController.getComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle request with query parameters', async () => {
      // Arrange
      const mockReqWithQuery = {
        ...mockReq,
        query: {
          entityType: 'delivery',
          entityId: '456',
          page: '1',
          limit: '10',
        },
      };
      const mockResponse = [
        { id: 1, content: 'Comment 1' },
        { id: 2, content: 'Comment 2' },
      ];
      commentService.getComment.mockImplementation((req, callback) => callback(mockResponse, null));

      // Act
      await CommentController.getComment(mockReqWithQuery, mockRes, mockNext);

      // Assert
      expect(commentService.getComment).toHaveBeenCalledWith(
        mockReqWithQuery,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: mockResponse,
      });
    });
  });

  describe('getInspectionComment', () => {
    it('should get inspection comment successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 3,
        content: 'This is an inspection comment',
        inspectionId: 101,
        userId: 456,
        createdAt: '2023-01-02T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
      };
      commentService.getInspectionComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.getInspectionComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.getInspectionComment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Inspection comment retrieval failed');
      commentService.getInspectionComment.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await CommentController.getInspectionComment(mockReq, mockRes, mockNext);

      // Assert
      expect(commentService.getInspectionComment).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      commentService.getInspectionComment.mockImplementation(() => {
        throw error;
      });

      // Act
      await CommentController.getInspectionComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle request with inspection parameters', async () => {
      // Arrange
      const mockReqWithParams = {
        ...mockReq,
        params: { inspectionId: '202' },
        query: {
          page: '1',
          limit: '5',
          sortBy: 'createdAt',
          sortOrder: 'desc',
        },
      };
      const mockResponse = [
        { id: 5, content: 'Inspection comment 1', inspectionId: 202 },
        { id: 6, content: 'Inspection comment 2', inspectionId: 202 },
      ];
      commentService.getInspectionComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.getInspectionComment(mockReqWithParams, mockRes, mockNext);

      // Assert
      expect(commentService.getInspectionComment).toHaveBeenCalledWith(
        mockReqWithParams,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: mockResponse,
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle null response from service', async () => {
      // Arrange
      commentService.getComment.mockImplementation((req, callback) => callback(null, null));

      // Act
      await CommentController.getComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: null,
      });
    });

    it('should handle undefined response from service', async () => {
      // Arrange
      commentService.getComment.mockImplementation((req, callback) => callback(undefined, null));

      // Act
      await CommentController.getComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: undefined,
      });
    });

    it('should handle empty array response from service', async () => {
      // Arrange
      commentService.getComment.mockImplementation((req, callback) => callback([], null));

      // Act
      await CommentController.getComment(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment Viewed Successfully.',
        data: [],
      });
    });

    it('should handle request with file attachments', async () => {
      // Arrange
      const mockReqWithFiles = {
        ...mockReq,
        body: {
          content: 'Comment with attachment',
          entityType: 'delivery',
          entityId: 456,
        },
        files: {
          attachment: {
            name: 'document.pdf',
            size: 1024,
            mimetype: 'application/pdf',
          },
        },
      };
      const mockResponse = {
        id: 7,
        content: 'Comment with attachment',
        entityType: 'delivery',
        entityId: 456,
        attachment: 'document.pdf',
      };
      commentService.createComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createComment(mockReqWithFiles, mockRes, mockNext);

      // Assert
      expect(commentService.createComment).toHaveBeenCalledWith(
        mockReqWithFiles,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
    });

    it('should handle request with user context', async () => {
      // Arrange
      const mockReqWithUser = {
        ...mockReq,
        body: {
          content: 'User comment',
        },
        user: {
          id: 123,
          email: '<EMAIL>',
          role: 'member',
        },
      };
      const mockResponse = {
        id: 8,
        content: 'User comment',
        userId: 123,
        userEmail: '<EMAIL>',
      };
      commentService.createComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createComment(mockReqWithUser, mockRes, mockNext);

      // Assert
      expect(commentService.createComment).toHaveBeenCalledWith(
        mockReqWithUser,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
    });

    it('should handle request with complex comment data', async () => {
      // Arrange
      const mockReqWithComplexData = {
        ...mockReq,
        body: {
          content: 'Complex comment with @mentions and #tags',
          entityType: 'delivery',
          entityId: 789,
          mentions: ['@user1', '@user2'],
          tags: ['#urgent', '#review'],
          priority: 'high',
          visibility: 'public',
        },
      };
      const mockResponse = {
        id: 9,
        content: 'Complex comment with @mentions and #tags',
        entityType: 'delivery',
        entityId: 789,
        mentions: ['@user1', '@user2'],
        tags: ['#urgent', '#review'],
        priority: 'high',
        visibility: 'public',
      };
      commentService.createComment.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await CommentController.createComment(mockReqWithComplexData, mockRes, mockNext);

      // Assert
      expect(commentService.createComment).toHaveBeenCalledWith(
        mockReqWithComplexData,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Comment added successfully.',
        data: mockResponse,
      });
    });
  });
});
