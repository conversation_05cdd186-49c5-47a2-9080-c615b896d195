const { Router } = require('express');
const multer = require('multer');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(),
}));

jest.mock('../../controllers/carbonEmissionController', () => ({
  fileUpload: jest.fn(),
  dashboardData: jest.fn(),
}));

jest.mock('multer', () => {
  const mockMulter = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  mockMulter.memoryStorage = jest.fn(() => 'mocked-storage');
  return mockMulter;
});

describe('carbonEmissionRoute', () => {
  let router;
  let carbonEmissionRoute;
  let CarbonEmissionController;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    carbonEmissionRoute = require('../carbonEmissionRoute');
    CarbonEmissionController = require('../../controllers/carbonEmissionController');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = carbonEmissionRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer.memoryStorage).toHaveBeenCalled();
      expect(multer).toHaveBeenCalledWith({ dest: 'uploads/' });
      expect(multer).toHaveBeenCalledWith({ storage: 'mocked-storage' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST route with multer middleware
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/upload_utilities/:zipcode/:ProjectId',
        'mocked-multer-middleware',
        CarbonEmissionController.fileUpload,
      );

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_dashboard_data/:ProjectId',
        CarbonEmissionController.dashboardData,
      );

      // Verify multer single method is called
      const uploadInstance = multer.mock.results[1].value; // Second call (with storage)
      expect(uploadInstance.single).toHaveBeenCalledWith('utilities');
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = carbonEmissionRoute.router;
      const result2 = carbonEmissionRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure different multer instances', () => {
      carbonEmissionRoute.router;

      // Verify multer setup is called for memory storage
      expect(multer.memoryStorage).toHaveBeenCalled();

      // Verify multer instances are created
      expect(multer).toHaveBeenCalledTimes(2);
    });

    it('should configure routes in the correct order', () => {
      carbonEmissionRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      expect(postCalls[0][0]).toBe('/upload_utilities/:zipcode/:ProjectId');
      expect(getCalls[0][0]).toBe('/get_dashboard_data/:ProjectId');
    });

    it('should use multer middleware for upload route only', () => {
      carbonEmissionRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // POST route should have multer middleware
      expect(postCalls[0]).toHaveLength(3); // path + multer + controller
      expect(postCalls[0][1]).toBe('mocked-multer-middleware');

      // GET route should not have multer middleware
      expect(getCalls[0]).toHaveLength(2); // path + controller
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof carbonEmissionRoute).toBe('object');
      expect(carbonEmissionRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(carbonEmissionRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(carbonEmissionRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
