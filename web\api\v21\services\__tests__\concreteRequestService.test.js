// Mock all external dependencies BEFORE importing the service (deliveryService pattern)
const moment = require('moment');

// Mock moment
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return actualMoment;
});

// Mock sequelize
const mockSequelize = {
  DataTypes: {
    STRING: 'STRING',
    INTEGER: 'INTEGER',
    BOOLEAN: 'BOOLEAN',
    DATE: 'DATE',
    TEXT: 'TEXT',
    DECIMAL: 'DECIMAL'
  },
  and: jest.fn(() => ({ and: 'condition' })),
  or: jest.fn(() => ({ or: 'condition' })),
  literal: jest.fn(() => 'LITERAL_RESULT'),
  Op: {
    and: Symbol('and'),
    or: Symbol('or'),
    eq: Symbol('eq'),
    ne: Symbol('ne'),
    in: Symbol('in'),
    notIn: Symbol('notIn'),
    like: Symbol('like'),
    notLike: Symbol('notLike'),
    gt: Symbol('gt'),
    gte: Symbol('gte'),
    lt: Symbol('lt'),
    lte: Symbol('lte')
  }
};

jest.mock('sequelize', () => mockSequelize);

// Mock the mailer
jest.mock('../../mailer', () => ({
  sendMail: jest.fn().mockResolvedValue(true)
}));

// Mock all models
const mockModels = {
  Sequelize: {
    Op: {
      and: 'and',
      or: 'or',
      in: 'in',
      notIn: 'notIn',
      between: 'between',
      gte: 'gte',
      lte: 'lte',
      ne: 'ne',
      like: 'like'
    }
  },
  Enterprise: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  TimeZone: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getProjectAndSettings: jest.fn()
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
    count: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  ConcreteRequest: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    getAll: jest.fn(),
    getConcreteRequestData: jest.fn(),
    getSingleConcreteRequestData: jest.fn()
  },
  ConcreteMixDesign: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  ConcretePumpSize: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  ConcreteLocation: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  ConcreteRequestResponsiblePerson: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  ConcreteRequestCompany: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  ConcreteRequestHistory: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn()
  },
  Company: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  Role: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  RequestRecurrenceSeries: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn()
  },
  ProjectSettings: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Notification: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  }
};

// Mock external services
jest.mock('../../models', () => mockModels);

jest.mock('../../helpers/domainHelper', () => ({
  returnProjectModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  }),
  getDynamicModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  })
}));

jest.mock('../../helpers/notificationHelper', () => ({
  sendNotification: jest.fn()
}));

jest.mock('../../config/fcm', () => ({
  sendPushNotification: jest.fn()
}));

jest.mock('../voidService', () => ({
  checkVoidStatus: jest.fn()
}));

// Import the actual service AFTER all mocks are set up (deliveryService pattern)
const concreteRequestService = require('../concreteRequestService');

describe('ConcreteRequestService Comprehensive Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateTimezone', () => {
    it('should validate timezone successfully', async () => {
      const mockTimeZone = {
        id: 1,
        location: 'America/New_York',
        isDayLightSavingEnabled: true,
        timeZoneOffsetInMinutes: -300,
        dayLightSavingTimeInMinutes: -240,
        timezone: 'America/New_York'
      };
      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);

      const result = await concreteRequestService.validateTimezone(1);

      expect(result).toEqual(mockTimeZone);
    });

    it('should return null for invalid timezone', async () => {
      mockModels.TimeZone.findOne.mockResolvedValue(null);

      const result = await concreteRequestService.validateTimezone(999);

      expect(result).toBeNull();
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully', async () => {
      const inputData = {
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockModelData = { Member: mockModels.Member, User: mockModels.User };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelData);

      await concreteRequestService.getDynamicModel(inputData);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalled();
      expect(require('../../helpers/domainHelper').getDynamicModel).toHaveBeenCalled();
    });
  });

  describe('checkInputDatas', () => {
    it('should validate input data successfully', async () => {
      const inputData = {
        body: {
          companies: [1, 2],
          responsiblePersons: [1, 2],
          mixDesignId: 1,
          pumpSizeId: 1,
          locationId: 1,
          ProjectId: 1,
          volume: 10.5
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.ConcreteMixDesign.count.mockResolvedValue(1);
      mockModels.ConcretePumpSize.count.mockResolvedValue(1);
      mockModels.ConcreteLocation.count.mockResolvedValue(1);
      mockModels.Company.count.mockResolvedValue(2);

      const done = jest.fn();
      await concreteRequestService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should handle validation errors', async () => {
      const inputData = {
        body: {
          companies: [1, 2, 3],
          responsiblePersons: [1, 2],
          mixDesignId: 999, // Invalid mix design
          ProjectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.ConcreteMixDesign.count.mockResolvedValue(0); // Invalid mix design
      mockModels.Company.count.mockResolvedValue(2);

      const done = jest.fn();
      await concreteRequestService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('newConcreteRequest', () => {
    it('should create new concrete request successfully', async () => {
      const inputData = {
        body: {
          description: 'Test concrete request',
          concreteDeliveryStart: '2024-08-01',
          concreteDeliveryEnd: '2024-08-01',
          startPicker: '08:00',
          endPicker: '10:00',
          recurrence: 'Does Not Repeat',
          TimeZoneId: 1,
          ProjectId: 1,
          ParentCompanyId: null,
          mixDesignId: 1,
          pumpSizeId: 1,
          locationId: 1,
          volume: 15.0,
          companies: [1],
          responsiblePersons: [1]
        },
        params: { ParentCompanyId: null },
        user: {
          id: 1,
          domainName: 'example.com',
          firstName: 'John',
          lastName: 'Doe'
        }
      };

      // Setup comprehensive mocks
      const mockTimeZone = { id: 1, timezone: 'America/New_York' };
      const mockProject = {
        id: 1,
        ProjectSettings: {
          isAutoApprovalEnabled: false,
          concreteWindowTime: 48,
          concreteWindowTimeUnit: 'hours'
        }
      };
      const mockMember = {
        id: 1,
        ProjectId: 1,
        RoleId: 2,
        User: { id: 1, firstName: 'John', lastName: 'Doe' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);
      mockModels.Project.getProjectAndSettings.mockResolvedValue(mockProject);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(1);
      mockModels.ConcreteMixDesign.count.mockResolvedValue(1);
      mockModels.ConcretePumpSize.count.mockResolvedValue(1);
      mockModels.ConcreteLocation.count.mockResolvedValue(1);
      mockModels.Company.count.mockResolvedValue(1);
      mockModels.ConcreteRequest.findOne.mockResolvedValue({ ConcreteRequestId: 10 });
      mockModels.ConcreteRequest.createInstance.mockResolvedValue({ id: 1 });

      const done = jest.fn();
      await concreteRequestService.newConcreteRequest(inputData, done);

      expect(done).toHaveBeenCalled();
    });
  });

  describe('listConcreteRequest', () => {
    it('should list concrete requests successfully', async () => {
      const inputData = {
        params: {
          ProjectId: 1,
          ParentCompanyId: null,
          pageSize: 10,
          pageNumber: 1,
          void: 0
        },
        body: {
          ParentCompanyId: null
        },
        user: {
          id: 1,
          domainName: 'example.com'
        }
      };

      const mockConcreteRequests = [
        { id: 1, description: 'Concrete 1', status: 'Approved', volume: 10.5 },
        { id: 2, description: 'Concrete 2', status: 'Pending', volume: 15.0 }
      ];
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockMember = { id: 1, ProjectId: 1, RoleId: 2 };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.ConcreteRequest.getAll.mockResolvedValue(mockConcreteRequests);

      const done = jest.fn();
      await concreteRequestService.listConcreteRequest(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array)
      }), false);
    });
  });
});