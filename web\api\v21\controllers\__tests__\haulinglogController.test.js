// Mock Sequelize constructor
const mockSequelize = {
  authenticate: jest.fn(),
  close: jest.fn(),
  define: jest.fn(),
  sync: jest.fn(),
};

jest.mock('sequelize', () => {
  return {
    __esModule: true,
    default: jest.fn(() => mockSequelize),
    Sequelize: jest.fn(() => mockSequelize),
    DataTypes: {
      STRING: 'STRING',
      INTEGER: 'INTEGER',
      BOOLEAN: 'BOOLEAN',
      DATE: 'DATE',
      TEXT: 'TEXT',
    },
  };
});

// Mock models directory
jest.mock('../../models', () => ({
  sequelize: mockSequelize,
  Sequelize: jest.fn(() => mockSequelize),
}));

// Mock haulingLogService
jest.mock('../../services/haulingLogService', () => ({
  addHaulingLog: jest.fn(),
  listHaulingLog: jest.fn(),
}));

const haulingLogController = require('../haulingLogController');
const haulingLogService = require('../../services/haulingLogService');

describe('haulingLogController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('addHaulingLog', () => {
    it('should add hauling log successfully', async () => {
      const mockHaulingLog = {
        id: 1,
        vehicleId: 123,
        driverId: 456,
        tripType: 'delivery',
        timestamp: '2023-01-01T10:00:00Z',
      };

      haulingLogService.addHaulingLog.mockImplementation((req, callback) => {
        callback(mockHaulingLog, null);
      });

      await haulingLogController.addHaulingLog(mockReq, mockRes, mockNext);

      expect(haulingLogService.addHaulingLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'HaulingLog added successfully.',
        data: mockHaulingLog,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from add hauling log', async () => {
      const mockError = new Error('Service error');
      haulingLogService.addHaulingLog.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await haulingLogController.addHaulingLog(mockReq, mockRes, mockNext);

      expect(haulingLogService.addHaulingLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listHaulingLog', () => {
    it('should list hauling logs successfully', async () => {
      const mockHaulingLogDetail = [
        { id: 1, vehicleId: 123, driverId: 456, tripType: 'delivery' },
        { id: 2, vehicleId: 123, driverId: 456, tripType: 'pickup' },
      ];

      haulingLogService.listHaulingLog.mockImplementation((req, callback) => {
        callback(mockHaulingLogDetail, null);
      });

      await haulingLogController.listHaulingLog(mockReq, mockRes, mockNext);

      expect(haulingLogService.listHaulingLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Hauling Log Listed successfully.',
        data: mockHaulingLogDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list hauling log', async () => {
      const mockError = new Error('Service error');
      haulingLogService.listHaulingLog.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await haulingLogController.listHaulingLog(mockReq, mockRes, mockNext);

      expect(haulingLogService.listHaulingLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});