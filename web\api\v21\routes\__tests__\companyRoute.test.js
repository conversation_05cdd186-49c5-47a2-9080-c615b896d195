const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    get: jest.fn().mockReturnThis(),
    post: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  multer.memoryStorage = jest.fn(() => 'mocked-storage');
  return multer;
});

jest.mock('../../controllers', () => ({
  CompanyController: {
    getAllCompaniesList: jest.fn(),
    getAllCompanies: jest.fn(),
    getCompanies: jest.fn(),
    addCompany: jest.fn(),
    companyLogoUpload: jest.fn(),
    editCompany: jest.fn(),
    deleteCompany: jest.fn(),
    getDefinableWork: jest.fn(),
    checkExistCompany: jest.fn(),
    sampleCompanyTemplate: jest.fn(),
    createCompany: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAdmin: jest.fn(),
  isProjectAdmin: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  companyValidation: {
    listCompany: jest.fn(),
    addCompany: jest.fn(),
    editCompany: jest.fn(),
    deleteCompany: jest.fn(),
    getDefinableWork: jest.fn(),
    sampleCompanyTemplate: jest.fn(),
    createCompany: jest.fn(),
  },
}));

describe('companyRoute', () => {
  let router;
  let companyRoute;
  let CompanyController;
  let passportConfig;
  let checkAdmin;
  let companyValidation;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      get: jest.fn().mockReturnThis(),
      post: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    companyRoute = require('../companyRoute');
    const controllers = require('../../controllers');
    CompanyController = controllers.CompanyController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    const validations = require('../../middlewares/validations');
    companyValidation = validations.companyValidation;
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = companyRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer.memoryStorage).toHaveBeenCalled();
      expect(multer).toHaveBeenCalledWith({ dest: 'uploads/' });
      expect(multer).toHaveBeenCalledWith({ storage: 'mocked-storage' });

      // Verify all routes are registered with correct parameters
      expect(router.get).toHaveBeenCalledTimes(3);
      expect(router.post).toHaveBeenCalledTimes(8);

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/all_companies',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        CompanyController.getAllCompaniesList,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_newcompanies/:ProjectId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        CompanyController.getCompanies,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/get_definable_work/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CompanyController.getDefinableWork,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(8);
      expect(validate).toHaveBeenCalledWith(
        companyValidation.listCompany,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        companyValidation.addCompany,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = companyRoute.router;
      const result2 = companyRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes with proper middleware chains', () => {
      companyRoute.router;

      // Verify routes with admin middleware
      const getAllCompaniesCall = router.get.mock.calls[0];
      expect(getAllCompaniesCall).toContain(checkAdmin.isAdmin);

      // Verify routes with project admin middleware
      const postCalls = router.post.mock.calls;
      const routesWithProjectAdmin = postCalls.filter(call =>
        call.includes(checkAdmin.isProjectAdmin)
      );
      expect(routesWithProjectAdmin.length).toBeGreaterThan(0);
    });

    it('should use authentication middleware for all routes', () => {
      companyRoute.router;

      const getCalls = router.get.mock.calls;
      const postCalls = router.post.mock.calls;

      // All routes should have authentication
      [...getCalls, ...postCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure multer middleware for file upload routes', () => {
      companyRoute.router;

      const postCalls = router.post.mock.calls;

      // Find routes with multer middleware
      const multerRoutes = postCalls.filter(call =>
        call.includes('mocked-multer-middleware')
      );
      expect(multerRoutes.length).toBe(2); // upload_logo and create_company routes
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof companyRoute).toBe('object');
      expect(companyRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(companyRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(companyRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
