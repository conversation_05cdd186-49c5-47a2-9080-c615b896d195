const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const { locationValidation } = require('../middlewares/validations');
const { LocationController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');
const upload = multer({ dest: 'uploads/' }); // NOSONAR
const locationRoute = {
  get router() {
    const router = Router();
    router.post(
      '/',
      validate(locationValidation.addLocation, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdminOnly,
      LocationController.addLocation,
    );
    router.put(
      '/',
      validate(locationValidation.addLocation, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdminOnly,
      LocationController.editLocation,
    );
    router.get(
      '/',
      validate(locationValidation.listLocation, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdminOnly,
      LocationController.listLocation,
    );
    router.delete(
      '/',
      validate(locationValidation.deleteLocation, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdminOnly,
      LocationController.deleteLocation,
    );
    router.get(
      '/get_location',
      validate(locationValidation.getLocation, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdminOnly,
      LocationController.getLocation,
    );
    router.post(
      '/sample_location_excel_download',
      validate(
        locationValidation.sampleExcelFileDownload,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      LocationController.sampleExcelDownload,
    );
    router.get(
      '/get_locations',
      validate(locationValidation.getLocations, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      LocationController.getLocations,
    );
    router.post(
      '/import_location',
      validate(locationValidation.importLocation, { keyByField: true }, { abortEarly: false }),
      upload.single('location'),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdminOnly,
      LocationController.importLocation,
    );
    router.get(
      '/locations',
      validate(locationValidation.listLocation, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      LocationController.listLocations,
    );
    router.get(
      '/set_location_notification_preference',
      LocationController.setLocationNotificationPreference,
    );
    router.put(
      '/member_location_preference',
      validate(
        locationValidation.memberLocationPreference,
        { keyByField: true },
        { abortEarly: false },
      ),
      LocationController.updateMemberLocationPreference,
    );
    router.get(
      '/create_default_location_path_existing_project',
      LocationController.createDefaultLocationPathForExistingProject,
    );
    router.get(
      '/set_default_location_id_existing_bookings',
      LocationController.createDefaultLocationIDForExistingBookings,
    );
    router.post(
      '/available_timeslots',
      validate(locationValidation.findAvailableTimeSlot, { keyByField: true }, { abortEarly: false }),
      LocationController.findAvailableSlots,
    );
    return router;
  },
};
module.exports = locationRoute;
