// Mock all required dependencies first
jest.mock('moment', () => {
    const actualMoment = jest.requireActual('moment');
    const mockMoment = jest.fn(() => actualMoment());
    mockMoment.tz = jest.fn().mockImplementation(() => {
        const mockMomentInstance = {
            clone: jest.fn().mockReturnThis(),
            tz: jest.fn().mockReturnThis(),
            format: jest.fn().mockReturnValue('2024-01-15 15:00:00+00:00')
        };
        return mockMomentInstance;
    });
    mockMoment.utc = jest.fn();
    return mockMoment;
});

jest.mock('moment-timezone', () => ({
    tz: jest.fn(),
    utc: jest.fn().mockImplementation(() => ({
        tz: jest.fn().mockReturnValue({
            format: jest.fn((format) => {
                if (format === 'HH:mm') return '10:00';
                if (format === 'YYYY-MM-DD') return '2024-01-15';
                return '2024-01-15 10:00:00';
            })
        })
    })),
}));

jest.mock('moment-range', () => ({
    extendMoment: jest.fn(() => {
        return {
            range: jest.fn().mockReturnValue({
                by: jest.fn().mockReturnValue([
                    { toDate: () => new Date('2024-01-15') },
                    { toDate: () => new Date('2024-01-16') }
                ])
            })
        };
    })
}));

const moment = require('moment');
const momenttz = require('moment-timezone');
const MomentRange = require('moment-range');
const Moment = require('moment');
const momentRange = MomentRange.extendMoment(Moment);

jest.mock('../models', () => ({
    Sequelize: {
        Op: {
            iLike: 'iLike',
            ne: 'ne',
            and: jest.fn(),
            between: 'between',
            notIn: 'notIn',
            in: 'in',
        },
        and: jest.fn(),
    },
    Enterprise: {
        findOne: jest.fn(),
    },
    Project: {},
    TimeZone: {
        findOne: jest.fn(),
    },
    CalendarSetting: {
        getAll: jest.fn(),
        getAllWeeklyReport: jest.fn(),
        createInstance: jest.fn(),
        updateInstance: jest.fn(),
        isExits: jest.fn(),
        getOne: jest.fn(),
    },
    Member: {
        findOne: jest.fn(),
    },
    User: {
        findOne: jest.fn(),
    },
}));

jest.mock('../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn().mockResolvedValue({
        User: { findOne: jest.fn() },
        Member: { findOne: jest.fn() }
    }),
    getDynamicModel: jest.fn().mockResolvedValue({
        Member: { findOne: jest.fn() },
        CalendarSetting: {
            getAll: jest.fn(),
            getAllWeeklyReport: jest.fn(),
            createInstance: jest.fn(),
            updateInstance: jest.fn(),
            isExits: jest.fn(),
            getOne: jest.fn()
        },
        User: { findOne: jest.fn() }
    }),
}));

jest.mock('../helpers/apiError', () => {
    return jest.fn().mockImplementation((message, status) => ({
        message,
        status,
    }));
});

const calendarSettingsService = require('../calendarSettingsService');

describe('CalendarSettingsService', () => {
    let mockReq;
    let mockNext;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup default mock request
        mockReq = {
            user: {
                id: 1,
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
            query: {
                ProjectId: '123',
                ParentCompanyId: '456',
                start: '2024-01-01',
                end: '2024-01-31',
            },
            body: {
                isApplicableToDelivery: true,
                isApplicableToCrane: false,
                isApplicableToConcrete: false,
                isApplicableToInspection: false,
            },
        };

        mockNext = jest.fn();
    });

    describe('findDomainEnterprise', () => {
        it('should return domain name when enterprise exists', async () => {
            const mockEnterprise = { name: 'testdomain' };
            require('../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await calendarSettingsService.findDomainEnterprise('testdomain');
            expect(result).toBe('testdomain');
        });

        it('should return empty string when enterprise does not exist', async () => {
            require('../models').Enterprise.findOne.mockResolvedValue(null);

            const result = await calendarSettingsService.findDomainEnterprise('nonexistent');
            expect(result).toBe('');
        });

        it('should return null when domain name is not provided', async () => {
            const result = await calendarSettingsService.findDomainEnterprise(null);
            expect(result).toBeNull();
        });
    });

    describe('findUserByEmail', () => {
        beforeEach(async () => {
            // Set up publicUser by calling returnProjectModel first
            await calendarSettingsService.returnProjectModel();
        });

        it('should return user when email exists', async () => {
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockPublicUser = { findOne: jest.fn().mockResolvedValue(mockUser) };
            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: { findOne: jest.fn() }
            });
            await calendarSettingsService.returnProjectModel();

            const result = await calendarSettingsService.findUserByEmail('<EMAIL>');
            expect(result).toEqual(mockUser);
        });

        it('should return null when email does not exist', async () => {
            const mockPublicUser = { findOne: jest.fn().mockResolvedValue(null) };
            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: { findOne: jest.fn() }
            });
            await calendarSettingsService.returnProjectModel();

            const result = await calendarSettingsService.findUserByEmail('<EMAIL>');
            expect(result).toBeNull();
        });

        it('should return null when email is not provided', async () => {
            const result = await calendarSettingsService.findUserByEmail(null);
            expect(result).toBeNull();
        });

        it('should return null when email is undefined', async () => {
            const result = await calendarSettingsService.findUserByEmail(undefined);
            expect(result).toBeNull();
        });

        it('should return null when email is empty string', async () => {
            const result = await calendarSettingsService.findUserByEmail('');
            expect(result).toBeNull();
        });
    });

    describe('returnProjectModel', () => {
        it('should call helper.returnProjectModel and set public variables', async () => {
            const mockModelData = {
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            };
            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue(mockModelData);

            await calendarSettingsService.returnProjectModel();
            expect(require('../helpers/domainHelper').returnProjectModel).toHaveBeenCalled();
        });

        it('should handle errors in returnProjectModel', async () => {
            const error = new Error('Model error');
            require('../helpers/domainHelper').returnProjectModel.mockRejectedValue(error);

            await expect(calendarSettingsService.returnProjectModel()).rejects.toThrow('Model error');
        });
    });

    describe('findMemberByUser', () => {
        it('should return member when userData is valid', async () => {
            const mockMember = { id: 1, UserId: 1, RoleId: 2 };
            const userData = { id: 1 };

            const mockPublicMember = { findOne: jest.fn().mockResolvedValue(mockMember) };
            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: mockPublicMember
            });
            await calendarSettingsService.returnProjectModel();

            const result = await calendarSettingsService.findMemberByUser(userData);
            expect(result).toEqual(mockMember);
        });

        it('should return null when userData is null', async () => {
            const result = await calendarSettingsService.findMemberByUser(null);
            expect(result).toBeNull();
        });

        it('should return null when userData is undefined', async () => {
            const result = await calendarSettingsService.findMemberByUser(undefined);
            expect(result).toBeNull();
        });

        it('should return null when userData has no id', async () => {
            const result = await calendarSettingsService.findMemberByUser({});
            expect(result).toBeNull();
        });
    });

    describe('findEnterpriseByMember', () => {
        it('should return enterprise name when member is account', async () => {
            const memberData = { isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'TestEnterprise' };
            require('../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await calendarSettingsService.findEnterpriseByMember(memberData, '456');
            expect(result).toBe('testenterprise');
        });

        it('should return null when member is account but enterprise not found', async () => {
            const memberData = { isAccount: true, EnterpriseId: 1 };
            require('../models').Enterprise.findOne.mockResolvedValue(null);

            const result = await calendarSettingsService.findEnterpriseByMember(memberData, '456');
            expect(result).toBeNull();
        });

        it('should return enterprise name when member is not account', async () => {
            const memberData = { isAccount: false };
            const mockEnterprise = { name: 'TestEnterprise' };
            require('../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await calendarSettingsService.findEnterpriseByMember(memberData, '456');
            expect(result).toBe('testenterprise');
        });

        it('should return null when memberData is null', async () => {
            const result = await calendarSettingsService.findEnterpriseByMember(null, '456');
            expect(result).toBeNull();
        });
    });

    describe('validateMember', () => {
        it('should return member when valid', async () => {
            const mockMember = { id: 1, UserId: 1, ProjectId: '123' };
            require('../models').Member.findOne.mockResolvedValue(mockMember);

            const result = await calendarSettingsService.validateMember(
                { id: 1 },
                '123',
                '456'
            );
            expect(result).toEqual(mockMember);
        });

        it('should return null when member does not exist', async () => {
            require('../models').Member.findOne.mockResolvedValue(null);

            const result = await calendarSettingsService.validateMember(
                { id: 1 },
                '123',
                '456'
            );
            expect(result).toBeNull();
        });

        it('should handle database errors', async () => {
            const error = new Error('Database error');
            require('../models').Member.findOne.mockRejectedValue(error);

            await expect(calendarSettingsService.validateMember(
                { id: 1 },
                '123',
                '456'
            )).rejects.toThrow('Database error');
        });
    });

    describe('getDynamicModel', () => {
        it('should handle request with domain name', async () => {
            const mockReq = {
                user: { domainName: 'testdomain', email: '<EMAIL>' },
                query: { ProjectId: '123' },
                body: { ParentCompanyId: '456' }
            };

            const mockEnterprise = { name: 'testdomain' };
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockModelObj = {
                Member: { findOne: jest.fn() },
                CalendarSetting: { getAll: jest.fn() },
                User: { findOne: jest.fn().mockResolvedValue(mockUser) }
            };

            require('../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);
            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            // Mock the findDomainEnterprise method to return the domain name
            calendarSettingsService.findDomainEnterprise = jest.fn().mockResolvedValue('testdomain');

            const result = await calendarSettingsService.getDynamicModel(mockReq);
            expect(result).toBeUndefined(); // The method returns ProjectId but it's not defined in the actual code
            expect(require('../helpers/domainHelper').getDynamicModel).toHaveBeenCalledWith('testdomain');
        });

        it('should handle request without domain name', async () => {
            const mockReq = {
                user: { email: '<EMAIL>' },
                query: { ProjectId: '123' },
                body: { ParentCompanyId: '456' }
            };

            const mockModelObj = {
                Member: { findOne: jest.fn() },
                CalendarSetting: { getAll: jest.fn() },
                User: { findOne: jest.fn() }
            };

            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            calendarSettingsService.findDomainEnterprise = jest.fn().mockResolvedValue('');

            const result = await calendarSettingsService.getDynamicModel(mockReq);
            expect(result).toBeUndefined();
            expect(require('../helpers/domainHelper').getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle request with non-existent domain', async () => {
            const mockReq = {
                user: { domainName: 'nonexistent', email: '<EMAIL>' },
                query: { ProjectId: '123' }
            };

            require('../models').Enterprise.findOne.mockResolvedValue(null);
            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                Member: { findOne: jest.fn() },
                CalendarSetting: { getAll: jest.fn() },
                User: { findOne: jest.fn() }
            });
            calendarSettingsService.findDomainEnterprise = jest.fn().mockResolvedValue('');

            const result = await calendarSettingsService.getDynamicModel(mockReq);
            expect(result).toBeUndefined();
            expect(require('../helpers/domainHelper').getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle ProjectId from params', async () => {
            const mockReq = {
                user: { email: '<EMAIL>' },
                params: { ProjectId: '789' }
            };

            require('../helpers/domainHelper').returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn() },
                Member: { findOne: jest.fn() }
            });
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue({
                Member: { findOne: jest.fn() },
                CalendarSetting: { getAll: jest.fn() },
                User: { findOne: jest.fn() }
            });
            calendarSettingsService.findDomainEnterprise = jest.fn().mockResolvedValue('');

            const result = await calendarSettingsService.getDynamicModel(mockReq);
            expect(result).toBeUndefined();
        });
    });

    describe('buildSearchCondition', () => {
        it('should build basic condition with ProjectId', () => {
            const result = calendarSettingsService.buildSearchCondition('123');
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false
            });
        });

        it('should add search condition when search term provided', () => {
            const result = calendarSettingsService.buildSearchCondition('123', 'test event');
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false,
                description: { iLike: '%test event%' }
            });
        });

        it('should add delivery condition when isApplicableToDelivery is true', () => {
            const result = calendarSettingsService.buildSearchCondition('123', null, true);
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false,
                isApplicableToDelivery: true
            });
        });

        it('should add crane condition when isApplicableToCrane is true', () => {
            const result = calendarSettingsService.buildSearchCondition('123', null, false, true);
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false,
                isApplicableToCrane: true
            });
        });

        it('should add concrete condition when isApplicableToConcrete is true', () => {
            const result = calendarSettingsService.buildSearchCondition('123', null, false, false, true);
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false,
                isApplicableToConcrete: true
            });
        });

        it('should add inspection condition when isApplicableToInspection is true', () => {
            const result = calendarSettingsService.buildSearchCondition('123', null, false, false, false, true);
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false,
                isApplicableToInspection: true
            });
        });

        it('should add all conditions when all flags are true', () => {
            const result = calendarSettingsService.buildSearchCondition('123', 'search', true, true, true, true);
            expect(result).toEqual({
                ProjectId: '123',
                isDeleted: false,
                description: { iLike: '%search%' },
                isApplicableToDelivery: true,
                isApplicableToCrane: true,
                isApplicableToConcrete: true,
                isApplicableToInspection: true
            });
        });
    });

    describe('fetchEvents', () => {
        beforeEach(async () => {
            // Set up dynamic models
            const mockModelObj = {
                Member: { findOne: jest.fn() },
                CalendarSetting: {
                    getAll: jest.fn(),
                    getAllWeeklyReport: jest.fn(),
                    createInstance: jest.fn(),
                    updateInstance: jest.fn(),
                    isExits: jest.fn(),
                    getOne: jest.fn()
                },
                User: { findOne: jest.fn() }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            await calendarSettingsService.getDynamicModel({ user: { email: '<EMAIL>' } });
        });

        it('should call getAllWeeklyReport for weekly report', async () => {
            const mockReq = {
                query: { weeklyReportTest: 'weeklyReport', ProjectId: '123' }
            };
            const mockEvents = [{ id: 1, description: 'Test Event' }];

            // Mock the dynamic CalendarSetting model
            const mockModelObj = {
                CalendarSetting: {
                    getAllWeeklyReport: jest.fn().mockResolvedValue(mockEvents)
                }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            await calendarSettingsService.getDynamicModel({ user: { email: '<EMAIL>' } });

            const result = await calendarSettingsService.fetchEvents(mockReq, {});
            expect(result).toEqual(mockEvents);
        });

        it('should call getAll for regular events', async () => {
            const mockReq = {
                query: { ProjectId: '123' }
            };
            const condition = { ProjectId: '123', isDeleted: false };
            const mockEvents = [{ id: 1, description: 'Test Event' }];

            // Mock the dynamic CalendarSetting model
            const mockModelObj = {
                CalendarSetting: {
                    getAll: jest.fn().mockResolvedValue(mockEvents)
                }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            await calendarSettingsService.getDynamicModel({ user: { email: '<EMAIL>' } });

            const result = await calendarSettingsService.fetchEvents(mockReq, condition);
            expect(result).toEqual(mockEvents);
        });
    });

    describe('getAll', () => {
        beforeEach(async () => {
            // Set up dynamic models properly
            const mockModelObj = {
                Member: { findOne: jest.fn() },
                CalendarSetting: {
                    getAll: jest.fn(),
                    getAllWeeklyReport: jest.fn(),
                    createInstance: jest.fn(),
                    updateInstance: jest.fn(),
                    isExits: jest.fn(),
                    getOne: jest.fn()
                },
                User: { findOne: jest.fn() }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
        });

        it('should return empty array when no events exist', async () => {
            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue({ id: 1 }) },
                CalendarSetting: { getAll: jest.fn().mockResolvedValue([]) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.getAll(mockReq, mockNext);
            expect(result).toEqual([]);
        });

        it('should return filtered events for regular calendar view', async () => {
            const mockEvents = [{
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15 10:00:00',
                toDate: '2024-01-15 11:00:00',
                TimeZoneId: 1,
                recurrence: 'Does Not Repeat',
                TimeZone: { location: 'UTC' }
            }];

            const mockTimeZone = {
                id: 1,
                location: 'UTC',
                timezone: 'UTC',
                isDayLightSavingEnabled: false,
                timeZoneOffsetInMinutes: 0,
                dayLightSavingTimeInMinutes: 0,
            };

            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue({ id: 1 }) },
                CalendarSetting: { getAll: jest.fn().mockResolvedValue(mockEvents) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            require('../models').TimeZone.findOne.mockResolvedValue(mockTimeZone);

            // Mock createRecurrenceObject
            calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15 10:00:00',
                toDate: '2024-01-15 11:00:00'
            });

            const result = await calendarSettingsService.getAll(mockReq, mockNext);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should return filtered events for weekly report', async () => {
            mockReq.query.weeklyReportTest = 'weeklyReport';
            mockReq.body.timezone = 'UTC';
            mockReq.body.eventStartTime = '09:00';
            mockReq.body.eventEndTime = '17:00';

            const mockEvents = [{
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15 10:00:00',
                toDate: '2024-01-15 11:00:00',
                TimeZoneId: 1,
                recurrence: 'Does Not Repeat',
                TimeZone: { location: 'UTC' }
            }];

            const mockTimeZone = {
                id: 1,
                location: 'UTC',
                timezone: 'UTC',
                isDayLightSavingEnabled: false,
                timeZoneOffsetInMinutes: 0,
                dayLightSavingTimeInMinutes: 0,
            };

            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue({ id: 1 }) },
                CalendarSetting: { getAllWeeklyReport: jest.fn().mockResolvedValue(mockEvents) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            require('../models').TimeZone.findOne.mockResolvedValue(mockTimeZone);

            // Mock createRecurrenceObject
            calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15 10:00:00+00:00',
                toDate: '2024-01-15 11:00:00+00:00'
            });

            const result = await calendarSettingsService.getAll(mockReq, mockNext);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should handle errors and call next with ApiError', async () => {
            const error = new Error('Test error');
            const mockModelObj = {
                Member: { findOne: jest.fn().mockRejectedValue(error) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            await calendarSettingsService.getAll(mockReq, mockNext);
            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                message: error,
                status: 400,
            }));
        });

        it('should return empty array when member validation fails', async () => {
            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue(null) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.getAll(mockReq, mockNext);
            expect(result).toEqual([]);
        });

        it('should handle search parameter', async () => {
            const searchReq = {
                ...mockReq,
                query: { ...mockReq.query, search: 'test event' }
            };

            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue({ id: 1 }) },
                CalendarSetting: { getAll: jest.fn().mockResolvedValue([]) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.getAll(searchReq, mockNext);
            expect(result).toEqual([]);
        });

        it('should handle all application type filters', async () => {
            const filterReq = {
                ...mockReq,
                body: {
                    isApplicableToDelivery: true,
                    isApplicableToCrane: true,
                    isApplicableToConcrete: true,
                    isApplicableToInspection: true
                }
            };

            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue({ id: 1 }) },
                CalendarSetting: { getAll: jest.fn().mockResolvedValue([]) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.getAll(filterReq, mockNext);
            expect(result).toEqual([]);
        });
    });

    describe('filterWeeklyReportEvents', () => {
        const mockEvents = [
            {
                id: 1,
                fromDate: '2024-01-15 10:00:00+00:00',
                description: 'Morning Event'
            },
            {
                id: 2,
                fromDate: '2024-01-15 14:00:00+00:00',
                description: 'Afternoon Event'
            },
            {
                id: 3,
                fromDate: '2024-01-16 02:00:00+00:00',
                description: 'Night Event'
            }
        ];

        beforeEach(() => {
            momenttz.utc.mockImplementation(() => ({
                tz: jest.fn().mockReturnValue({
                    format: jest.fn((format) => {
                        if (format === 'HH:mm') return '10:00';
                        if (format === 'YYYY-MM-DD') return '2024-01-15';
                        return '2024-01-15 10:00:00';
                    })
                })
            }));
        });

        it('should filter events within time range (normal case)', () => {
            const req = {
                query: { start: '2024-01-15', end: '2024-01-16' },
                body: {
                    timezone: 'UTC',
                    eventStartTime: '09:00',
                    eventEndTime: '17:00'
                }
            };

            const result = calendarSettingsService.filterWeeklyReportEvents(mockEvents, req);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should filter events with overnight time range', () => {
            const req = {
                query: { start: '2024-01-15', end: '2024-01-16' },
                body: {
                    timezone: 'UTC',
                    eventStartTime: '22:00',
                    eventEndTime: '06:00'
                }
            };

            const result = calendarSettingsService.filterWeeklyReportEvents(mockEvents, req);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should use body dates when provided', () => {
            const req = {
                query: { start: '2024-01-01', end: '2024-01-31' },
                body: {
                    startDate: '2024-01-15',
                    endDate: '2024-01-16',
                    timezone: 'UTC',
                    eventStartTime: '09:00',
                    eventEndTime: '17:00'
                }
            };

            const result = calendarSettingsService.filterWeeklyReportEvents(mockEvents, req);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('filterRegularEvents', () => {
        const mockEvents = [
            {
                id: 1,
                fromDate: '2024-01-15 10:00:00',
                description: 'Event 1'
            },
            {
                id: 2,
                fromDate: '2024-01-20 14:00:00',
                description: 'Event 2'
            },
            {
                id: 3,
                fromDate: '2024-02-01 10:00:00',
                description: 'Event 3'
            }
        ];

        it('should filter events within date range with buffer', () => {
            const req = {
                query: { start: '2024-01-15', end: '2024-01-25' }
            };

            const result = calendarSettingsService.filterRegularEvents(mockEvents, req);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should handle edge case dates', () => {
            const req = {
                query: { start: '2024-01-01', end: '2024-12-31' }
            };

            const result = calendarSettingsService.filterRegularEvents(mockEvents, req);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('addEvent', () => {
        it('should successfully create a new event', async () => {
            const mockEvent = {
                id: 1,
                description: 'New Event',
                EquipmentId: [1, 2],
            };

            const mockModelObj = {
                CalendarSetting: { createInstance: jest.fn().mockResolvedValue(mockEvent) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.addEvent({
                ...mockReq,
                body: mockEvent,
            });

            expect(result).toEqual(mockEvent);
        });

        it('should handle errors during event creation', async () => {
            const error = new Error('Creation failed');
            const mockModelObj = {
                CalendarSetting: { createInstance: jest.fn().mockRejectedValue(error) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const consoleSpy = jest.spyOn(console, 'log');
            await calendarSettingsService.addEvent(mockReq);
            expect(consoleSpy).toHaveBeenCalledWith(error);
        });
    });

    describe('updateEvent', () => {
        it('should successfully update an existing event', async () => {
            const mockEvent = {
                id: 1,
                description: 'Updated Event',
                EquipmentId: [1, 2],
            };

            const mockModelObj = {
                CalendarSetting: {
                    isExits: jest.fn().mockResolvedValue(true),
                    updateInstance: jest.fn().mockResolvedValue(mockEvent)
                }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.updateEvent({
                ...mockReq,
                params: { id: 1 },
                body: mockEvent,
            }, mockNext);

            expect(result).toEqual(mockEvent);
        });

        it('should call next with ApiError when event does not exist', async () => {
            const mockModelObj = {
                CalendarSetting: { isExits: jest.fn().mockResolvedValue(false) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            await calendarSettingsService.updateEvent({
                ...mockReq,
                params: { id: 999 },
            }, mockNext);

            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                message: 'No event found',
                status: 404,
            }));
        });

        it('should handle errors during event update', async () => {
            const error = new Error('Update failed');
            const mockModelObj = {
                CalendarSetting: {
                    isExits: jest.fn().mockResolvedValue(true),
                    updateInstance: jest.fn().mockRejectedValue(error)
                }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const consoleSpy = jest.spyOn(console, 'log');
            await calendarSettingsService.updateEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);
            expect(consoleSpy).toHaveBeenCalledWith(error);
        });
    });

    describe('getCalendarEvent', () => {
        it('should return event details with timezone conversion', async () => {
            const mockEvent = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15 10:00:00',
                toDate: '2024-01-15 11:00:00',
                endDate: '2024-01-15 11:00:00',
                startTime: '10:00',
                endTime: '11:00',
                TimeZoneId: 1,
            };

            const mockTimeZone = {
                id: 1,
                location: 'UTC',
                timezone: 'UTC',
                isDayLightSavingEnabled: false,
                timeZoneOffsetInMinutes: 0,
                dayLightSavingTimeInMinutes: 0,
            };

            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue({ id: 1 }) },
                CalendarSetting: { getOne: jest.fn().mockResolvedValue(mockEvent) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);
            require('../models').TimeZone.findOne.mockResolvedValue(mockTimeZone);

            const result = await calendarSettingsService.getCalendarEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);

            expect(result).toBeDefined();
            expect(result.startTime).toBeDefined();
            expect(result.endTime).toBeDefined();
        });

        it('should return empty object when member does not exist', async () => {
            const mockModelObj = {
                Member: { findOne: jest.fn().mockResolvedValue(null) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            const result = await calendarSettingsService.getCalendarEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);

            expect(result).toBeUndefined();
        });

        it('should handle errors and call next with ApiError', async () => {
            const error = new Error('Test error');
            const mockModelObj = {
                Member: { findOne: jest.fn().mockRejectedValue(error) }
            };
            require('../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelObj);

            await calendarSettingsService.getCalendarEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);

            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                message: error,
                status: 400,
            }));
        });
    });

    describe('processEvent', () => {
        beforeEach(() => {
            // Mock createRecurrenceObject method
            calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-15'
            });
        });

        it('should process non-repeating event', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-15',
                recurrence: 'Does Not Repeat',
                startTime: '10:00',
                endTime: '11:00',
                isAllDay: false,
            };

            const eventTimeZone = {
                timezone: 'UTC',
            };

            const result = await calendarSettingsService.processEvent(eventObject, eventTimeZone, 0);
            expect(result.events).toBeDefined();
            expect(result.uniqueNumber).toBeGreaterThan(0);
        });

        it('should process daily repeating event', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-20',
                recurrence: 'Daily',
                repeatEveryCount: 1,
                startTime: '10:00',
                endTime: '11:00',
                isAllDay: false,
            };

            const eventTimeZone = {
                timezone: 'UTC',
            };

            const result = await calendarSettingsService.processEvent(eventObject, eventTimeZone, 0);
            expect(result.events).toBeDefined();
            expect(result.uniqueNumber).toBeGreaterThan(0);
        });

        it('should process weekly repeating event', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                endDate: '2024-01-20',
                recurrence: 'Weekly',
                repeatEveryCount: 1,
                days: ['Monday', 'Wednesday', 'Friday'],
                startTime: '10:00',
                endTime: '11:00',
                isAllDay: false,
                TimeZone: { location: 'UTC' }
            };

            const eventTimeZone = {
                timezone: 'UTC',
            };

            const result = await calendarSettingsService.processEvent(eventObject, eventTimeZone, 0);
            expect(result.events).toBeDefined();
            expect(result.uniqueNumber).toBeGreaterThan(0);
        });

        it('should process monthly repeating event', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-12-31',
                recurrence: 'Monthly',
                repeatEveryCount: 1,
                chosenDateOfMonth: true,
                dateOfMonth: '15',
                startTime: '10:00',
                endTime: '11:00',
                isAllDay: false,
            };

            const eventTimeZone = {
                timezone: 'UTC',
            };

            const result = await calendarSettingsService.processEvent(eventObject, eventTimeZone, 0);
            expect(result.events).toBeDefined();
            expect(result.uniqueNumber).toBeGreaterThan(0);
        });

        it('should process yearly repeating event', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                endDate: '2026-01-15',
                recurrence: 'Yearly',
                repeatEveryCount: 1,
                chosenDateOfMonth: true,
                dateOfMonth: '15',
                startTime: '10:00',
                endTime: '11:00',
                isAllDay: false,
                TimeZone: { location: 'UTC' }
            };

            const eventTimeZone = {
                timezone: 'UTC',
            };

            const result = await calendarSettingsService.processEvent(eventObject, eventTimeZone, 0);
            expect(result.events).toBeDefined();
            expect(result.uniqueNumber).toBeGreaterThan(0);
        });

        it('should handle unknown recurrence type', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-15',
                recurrence: 'Unknown',
                startTime: '10:00',
                endTime: '11:00',
                isAllDay: false,
            };

            const eventTimeZone = {
                timezone: 'UTC',
            };

            const result = await calendarSettingsService.processEvent(eventObject, eventTimeZone, 0);
            expect(result.events).toEqual([]);
            expect(result.uniqueNumber).toBe(0);
        });
    });

    describe('processNonRepeatingEvent', () => {
        beforeEach(() => {
            calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-15'
            });
        });

        it('should process non-repeating event correctly', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                recurrence: 'Does Not Repeat',
                TimeZone: { location: 'UTC' }
            };
            const eventTimeZone = { timezone: 'UTC' };
            const totalDays = [moment('2024-01-15'), moment('2024-01-16')];
            const uniqueNumber = 0;

            const result = await calendarSettingsService.processNonRepeatingEvent(eventObject, eventTimeZone, totalDays, uniqueNumber);
            expect(result.events).toBeDefined();
            expect(result.events.length).toBe(2);
            expect(result.uniqueNumber).toBe(2);
        });
    });

    describe('processDailyEvent', () => {
        beforeEach(() => {
            calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-15'
            });
        });

        it('should process daily event correctly', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                recurrence: 'Daily',
                repeatEveryCount: 1,
                TimeZone: { location: 'UTC' }
            };
            const eventTimeZone = { timezone: 'UTC' };
            const totalDays = [moment('2024-01-15'), moment('2024-01-16'), moment('2024-01-17')];
            const uniqueNumber = 0;

            const result = await calendarSettingsService.processDailyEvent(eventObject, eventTimeZone, totalDays, uniqueNumber);
            expect(result.events).toBeDefined();
            expect(result.events.length).toBe(3);
            expect(result.uniqueNumber).toBe(3);
        });

        it('should process daily event with repeat count > 1', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                recurrence: 'Daily',
                repeatEveryCount: 2,
                TimeZone: { location: 'UTC' }
            };
            const eventTimeZone = { timezone: 'UTC' };
            const totalDays = [moment('2024-01-15'), moment('2024-01-16'), moment('2024-01-17'), moment('2024-01-18')];
            const uniqueNumber = 0;

            const result = await calendarSettingsService.processDailyEvent(eventObject, eventTimeZone, totalDays, uniqueNumber);
            expect(result.events).toBeDefined();
            expect(result.events.length).toBe(2);
            expect(result.uniqueNumber).toBe(2);
        });
    });

    describe('processMonthEvent', () => {
        beforeEach(() => {
            calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15',
                toDate: '2024-01-15'
            });
        });

        it('should process month event with chosen date of month', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-01',
                endDate: '2024-12-31',
                chosenDateOfMonth: true,
                dateOfMonth: '15',
                recurrence: 'Monthly',
                TimeZone: { location: 'UTC' }
            };
            const eventTimeZone = { timezone: 'UTC' };
            const month = '2024-01';
            const uniqueNumber = 0;

            const result = await calendarSettingsService.processMonthEvent(month, eventObject, eventTimeZone, uniqueNumber);
            expect(result).toBeDefined();
        });

        it('should process month event with monthly repeat type', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-01',
                endDate: '2024-12-31',
                chosenDateOfMonth: false,
                monthlyRepeatType: 'first monday',
                recurrence: 'Monthly',
                TimeZone: { location: 'UTC' }
            };
            const eventTimeZone = { timezone: 'UTC' };
            const month = '2024-01';
            const uniqueNumber = 0;

            calendarSettingsService.getFinalDayForMonth = jest.fn().mockReturnValue('2024-01-01');
            calendarSettingsService.isDateInRange = jest.fn().mockReturnValue(true);

            const result = await calendarSettingsService.processMonthEvent(month, eventObject, eventTimeZone, uniqueNumber);
            expect(result).toBeDefined();
        });

        it('should return null when date is not in range', async () => {
            const eventObject = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-01',
                endDate: '2024-01-31',
                chosenDateOfMonth: true,
                dateOfMonth: '15',
                recurrence: 'Monthly',
                TimeZone: { location: 'UTC' }
            };
            const eventTimeZone = { timezone: 'UTC' };
            const month = '2024-02';
            const uniqueNumber = 0;

            const result = await calendarSettingsService.processMonthEvent(month, eventObject, eventTimeZone, uniqueNumber);
            expect(result).toBeNull();
        });
    });

    describe('generateMonthRanges', () => {
        it('should generate monthly ranges', () => {
            const startDate = moment('2024-01-15');
            const endDate = moment('2024-06-15');
            const recurrence = 'Monthly';
            const repeatEveryCount = 1;

            const result = calendarSettingsService.generateMonthRanges(startDate, endDate, recurrence, repeatEveryCount);
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBeGreaterThan(0);
        });

        it('should generate yearly ranges', () => {
            const startDate = moment('2024-01-15');
            const endDate = moment('2026-01-15');
            const recurrence = 'Yearly';
            const repeatEveryCount = 1;

            const result = calendarSettingsService.generateMonthRanges(startDate, endDate, recurrence, repeatEveryCount);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should handle monthly with repeat count > 1', () => {
            const startDate = moment('2024-01-15');
            const endDate = moment('2024-12-15');
            const recurrence = 'Monthly';
            const repeatEveryCount = 2;

            const result = calendarSettingsService.generateMonthRanges(startDate, endDate, recurrence, repeatEveryCount);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should handle edge case with same start and end date', () => {
            const startDate = moment('2024-01-15');
            const endDate = moment('2024-01-15');
            const recurrence = 'Monthly';
            const repeatEveryCount = 1;

            const result = calendarSettingsService.generateMonthRanges(startDate, endDate, recurrence, repeatEveryCount);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('convertTimezoneToUtc', () => {
        beforeEach(() => {
            // Mock moment.tz
            const mockMoment = {
                clone: jest.fn().mockReturnThis(),
                tz: jest.fn().mockReturnThis(),
                format: jest.fn().mockReturnValue('2024-01-15 15:00:00+00:00')
            };
            moment.tz = jest.fn().mockReturnValue(mockMoment);
        });

        it('should convert timezone to UTC correctly', async () => {
            const date = '01/15/2024';
            const timezone = 'America/New_York';
            const time = '10:00';

            const result = await calendarSettingsService.convertTimezoneToUtc(date, timezone, time);
            expect(result).toBe('2024-01-15 15:00:00+00:00');
            expect(moment.tz).toHaveBeenCalledWith('01/15/2024 10:00', 'MM/DD/YYYY HH:mm', 'America/New_York');
        });

        it('should handle different date formats', async () => {
            const date = '12/31/2023';
            const timezone = 'UTC';
            const time = '23:59';

            const result = await calendarSettingsService.convertTimezoneToUtc(date, timezone, time);
            expect(result).toBe('2024-01-15 15:00:00+00:00');
        });

        it('should handle different timezones', async () => {
            const date = '06/15/2024';
            const timezone = 'Europe/London';
            const time = '14:30';

            const result = await calendarSettingsService.convertTimezoneToUtc(date, timezone, time);
            expect(result).toBe('2024-01-15 15:00:00+00:00');
        });
    });

    describe('isDateInRange', () => {
        it('should return true when date is within range', () => {
            const date = '2024-01-15';
            const eventObject = {
                fromDate: '2024-01-01',
                endDate: '2024-01-31',
            };

            const result = calendarSettingsService.isDateInRange(date, eventObject);
            expect(result).toBe(true);
        });

        it('should return false when date is outside range', () => {
            const date = '2024-03-01';
            const eventObject = {
                fromDate: '2024-01-01',
                endDate: '2024-01-31'
            };

            const result = calendarSettingsService.isDateInRange(date, eventObject);
            expect(result).toBe(false);
        });
    });

    describe('getFinalDayForMonth', () => {
        it('should return correct date for first week', () => {
            const monthNew = '2024-01';
            const eventObject = {
                monthlyRepeatType: 'first monday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });

        it('should return correct date for last week', () => {
            const monthNew = '2024-01';
            const eventObject = {
                monthlyRepeatType: 'last friday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });

        it('should return correct date for second week', () => {
            const monthNew = '2024-01';
            const eventObject = {
                monthlyRepeatType: 'second tuesday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });

        it('should return correct date for third week', () => {
            const monthNew = '2024-01';
            const eventObject = {
                monthlyRepeatType: 'third wednesday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });

        it('should return correct date for fourth week', () => {
            const monthNew = '2024-01';
            const eventObject = {
                monthlyRepeatType: 'fourth thursday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });

        it('should handle edge case with different months', () => {
            const monthNew = '2024-02';
            const eventObject = {
                monthlyRepeatType: 'last sunday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });

        it('should handle leap year February', () => {
            const monthNew = '2024-02';
            const eventObject = {
                monthlyRepeatType: 'last saturday',
            };

            const result = calendarSettingsService.getFinalDayForMonth(monthNew, eventObject);
            expect(result).toBeDefined();
        });
    });

    describe('addEvent - additional edge cases', () => {
        it('should handle event with empty EquipmentId array', async () => {
            const mockEvent = {
                id: 1,
                description: 'New Event',
                EquipmentId: [],
            };

            require('../models').CalendarSetting.createInstance.mockResolvedValue(mockEvent);

            const result = await calendarSettingsService.addEvent({
                ...mockReq,
                body: mockEvent,
            });

            expect(result).toEqual(mockEvent);
        });

        it('should handle event without EquipmentId', async () => {
            const mockEvent = {
                id: 1,
                description: 'New Event',
            };

            require('../models').CalendarSetting.createInstance.mockResolvedValue(mockEvent);

            const result = await calendarSettingsService.addEvent({
                ...mockReq,
                body: mockEvent,
            });

            expect(result).toEqual(mockEvent);
        });

        it('should handle null response from createInstance', async () => {
            require('../models').CalendarSetting.createInstance.mockResolvedValue(null);

            const result = await calendarSettingsService.addEvent(mockReq);
            expect(result).toBeNull();
        });
    });

    describe('updateEvent - additional edge cases', () => {
        it('should handle event with null EquipmentId', async () => {
            const mockEvent = {
                id: 1,
                description: 'Updated Event',
                EquipmentId: null,
            };

            require('../models').CalendarSetting.isExits.mockResolvedValue(true);
            require('../models').CalendarSetting.updateInstance.mockResolvedValue(mockEvent);

            const result = await calendarSettingsService.updateEvent({
                ...mockReq,
                params: { id: 1 },
                body: mockEvent,
            }, mockNext);

            expect(result).toEqual(mockEvent);
        });

        it('should handle database error in isExits check', async () => {
            const error = new Error('Database connection failed');
            require('../models').CalendarSetting.isExits.mockRejectedValue(error);

            const consoleSpy = jest.spyOn(console, 'log');
            await calendarSettingsService.updateEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);
            expect(consoleSpy).toHaveBeenCalledWith(error);
        });

        it('should handle null response from updateInstance', async () => {
            require('../models').CalendarSetting.isExits.mockResolvedValue(true);
            require('../models').CalendarSetting.updateInstance.mockResolvedValue(null);

            const result = await calendarSettingsService.updateEvent({
                ...mockReq,
                params: { id: 1 },
                body: { description: 'Updated Event' },
            }, mockNext);

            expect(result).toBeNull();
        });
    });

    describe('getCalendarEvent - additional edge cases', () => {
        it('should handle event without TimeZoneId', async () => {
            const mockEvent = {
                id: 1,
                description: 'Test Event',
                fromDate: '2024-01-15 10:00:00',
                toDate: '2024-01-15 11:00:00',
                endDate: '2024-01-15 11:00:00',
                startTime: '10:00',
                endTime: '11:00',
            };

            require('../models').Member.findOne.mockResolvedValue({ id: 1 });
            require('../models').CalendarSetting.getOne.mockResolvedValue(mockEvent);
            require('../models').TimeZone.findOne.mockResolvedValue(null);

            const result = await calendarSettingsService.getCalendarEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);

            expect(result).toBeDefined();
        });

        it('should handle null event from getOne', async () => {
            require('../models').Member.findOne.mockResolvedValue({ id: 1 });
            require('../models').CalendarSetting.getOne.mockResolvedValue(null);

            const result = await calendarSettingsService.getCalendarEvent({
                ...mockReq,
                params: { id: 999 },
            }, mockNext);

            expect(result).toBeUndefined();
        });

        it('should handle database error in getOne', async () => {
            const error = new Error('Database error');
            require('../models').Member.findOne.mockResolvedValue({ id: 1 });
            require('../models').CalendarSetting.getOne.mockRejectedValue(error);

            await calendarSettingsService.getCalendarEvent({
                ...mockReq,
                params: { id: 1 },
            }, mockNext);

            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                message: error,
                status: 400,
            }));
        });
    });

    describe('Edge Cases and Error Handling', () => {
        describe('isDateInRange edge cases', () => {
            it('should handle null dates', () => {
                const result = calendarSettingsService.isDateInRange(null, {
                    fromDate: '2024-01-01',
                    endDate: '2024-01-31'
                });
                expect(typeof result).toBe('boolean');
            });

            it('should handle invalid date formats', () => {
                const result = calendarSettingsService.isDateInRange('invalid-date', {
                    fromDate: '2024-01-01',
                    endDate: '2024-01-31'
                });
                expect(typeof result).toBe('boolean');
            });

            it('should handle same start and end dates', () => {
                const result = calendarSettingsService.isDateInRange('2024-01-15', {
                    fromDate: '2024-01-15',
                    endDate: '2024-01-15'
                });
                expect(result).toBe(true);
            });
        });

        describe('findDomainEnterprise edge cases', () => {
            it('should handle empty string domain name', async () => {
                const result = await calendarSettingsService.findDomainEnterprise('');
                expect(result).toBeNull();
            });

            it('should handle whitespace-only domain name', async () => {
                const mockEnterprise = { name: '   ' };
                require('../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

                const result = await calendarSettingsService.findDomainEnterprise('   ');
                expect(result).toBe('   ');
            });

            it('should handle database error', async () => {
                const error = new Error('Database connection failed');
                require('../models').Enterprise.findOne.mockRejectedValue(error);

                await expect(calendarSettingsService.findDomainEnterprise('testdomain'))
                    .rejects.toThrow('Database connection failed');
            });

            it('should handle case sensitivity', async () => {
                const mockEnterprise = { name: 'testdomain' };
                require('../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

                const result = await calendarSettingsService.findDomainEnterprise('TESTDOMAIN');
                expect(result).toBe('TESTDOMAIN');
                expect(require('../models').Enterprise.findOne).toHaveBeenCalledWith({
                    where: { name: 'testdomain' }
                });
            });
        });

        describe('processEvent with complex scenarios', () => {
            beforeEach(() => {
                calendarSettingsService.createRecurrenceObject = jest.fn().mockResolvedValue({
                    id: 1,
                    description: 'Test Event'
                });
                calendarSettingsService.processNonRepeatingEvent = jest.fn().mockResolvedValue({
                    events: [{ id: 1 }],
                    uniqueNumber: 1
                });
                calendarSettingsService.processDailyEvent = jest.fn().mockResolvedValue({
                    events: [{ id: 1 }],
                    uniqueNumber: 1
                });
                calendarSettingsService.processWeeklyEvent = jest.fn().mockResolvedValue({
                    events: [{ id: 1 }],
                    uniqueNumber: 1
                });
                calendarSettingsService.processMonthlyYearlyEvent = jest.fn().mockResolvedValue({
                    events: [{ id: 1 }],
                    uniqueNumber: 1
                });
            });

            it('should handle event with missing recurrence field', async () => {
                const eventObject = {
                    id: 1,
                    fromDate: '2024-01-15',
                    toDate: '2024-01-15'
                };

                const result = await calendarSettingsService.processEvent(eventObject, {}, 0);
                expect(result.events).toEqual([]);
                expect(result.uniqueNumber).toBe(0);
            });

            it('should handle event with null dates', async () => {
                const eventObject = {
                    id: 1,
                    fromDate: null,
                    toDate: null,
                    recurrence: 'Does Not Repeat'
                };

                // This should handle the error gracefully
                await expect(calendarSettingsService.processEvent(eventObject, {}, 0))
                    .resolves.toBeDefined();
            });
        });

        describe('Memory and performance edge cases', () => {
            it('should handle large date ranges efficiently', async () => {
                const eventObject = {
                    id: 1,
                    fromDate: '2024-01-01',
                    toDate: '2024-12-31',
                    recurrence: 'Daily',
                    repeatEveryCount: 1
                };

                const result = await calendarSettingsService.processEvent(eventObject, { timezone: 'UTC' }, 0);
                expect(result).toBeDefined();
            });

            it('should handle empty events array', async () => {
                require('../models').Member.findOne.mockResolvedValue({ id: 1 });
                require('../models').CalendarSetting.getAll.mockResolvedValue([]);

                const result = await calendarSettingsService.getAll(mockReq, mockNext);
                expect(result).toEqual([]);
            });
        });
    });
});