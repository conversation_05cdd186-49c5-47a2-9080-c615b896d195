// Mock all dependencies before requiring the module
const mockParentPort = {
  on: jest.fn(),
  postMessage: jest.fn(),
};

const mockFromBuffer = jest.fn();
const mockPDFDocument = {
  load: jest.fn(),
};
const mockAxios = {
  get: jest.fn(),
};
const mockFs = {
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  rm: jest.fn(),
  readFileSync: jest.fn(),
};
const mockPuppeteerService = {
  saveImageToS3Bucket: jest.fn(),
};

jest.mock('worker_threads', () => ({
  parentPort: mockParentPort,
}));

jest.mock('pdf2pic', () => ({
  fromBuffer: mockFromBuffer,
}));

jest.mock('pdf-lib', () => ({
  PDFDocument: mockPDFDocument,
}));

jest.mock('axios', () => mockAxios);

jest.mock('fs', () => mockFs);

jest.mock('../puppeteerService', () => mockPuppeteerService);

// Mock console methods to avoid noise in tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

describe('ImageConversionProcess Worker', () => {
  let messageHandler;
  let mockConverter;
  let mockPdfDoc;

  beforeEach(() => {
    jest.clearAllMocks();

    // Suppress console output during tests
    console.log = jest.fn();
    console.error = jest.fn();

    // Setup mock implementations
    mockConverter = jest.fn();
    mockFromBuffer.mockReturnValue(mockConverter);

    mockPdfDoc = {
      getPageCount: jest.fn().mockReturnValue(3),
    };
    mockPDFDocument.load.mockResolvedValue(mockPdfDoc);

    mockAxios.get.mockResolvedValue({
      data: Buffer.from('mock-pdf-data'),
    });

    mockFs.existsSync.mockReturnValue(false);
    mockFs.mkdirSync.mockImplementation(() => { });
    mockFs.rm.mockImplementation((_path, _options, callback) => {
      if (callback) callback(null);
    });

    mockPuppeteerService.saveImageToS3Bucket.mockResolvedValue({
      success: true,
      data: [{ fileLink: 'https://example.com/image.png' }],
    });

    // Clear the module cache and require the module fresh
    delete require.cache[require.resolve('../imageConversionProcess')];
    require('../imageConversionProcess');

    // Get the message handler that was registered
    const messageCalls = mockParentPort.on.mock.calls.filter(call => call[0] === 'message');
    if (messageCalls.length > 0) {
      messageHandler = messageCalls[messageCalls.length - 1][1];
    }
  });

  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  describe('Worker Setup', () => {
    it('should register message event listener on parentPort', () => {
      expect(mockParentPort.on).toHaveBeenCalledWith('message', expect.any(Function));
    });

    it('should have a message handler function', () => {
      const messageCalls = mockParentPort.on.mock.calls.filter(call => call[0] === 'message');
      expect(messageCalls.length).toBeGreaterThan(0);
      expect(typeof messageCalls[0][1]).toBe('function');
    });
  });

  describe('Message Processing', () => {
    const validMessage = JSON.stringify({
      pdfFileLink: 'https://example.com/test.pdf',
      fileName: 'test-file',
    });

    it('should process valid message successfully', async () => {
      // Setup successful conversion
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      expect(JSON.parse).not.toThrow();
      expect(mockAxios.get).toHaveBeenCalledWith(
        'https://example.com/test.pdf',
        { responseType: 'arraybuffer' }
      );
      expect(mockPDFDocument.load).toHaveBeenCalledWith(Buffer.from('mock-pdf-data'));
    });

    it('should handle invalid JSON message', async () => {
      const invalidMessage = 'invalid-json';

      await expect(messageHandler(invalidMessage)).rejects.toThrow();
    });

    it('should create directory if it does not exist', async () => {
      mockFs.existsSync.mockReturnValue(false);
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      expect(mockFs.existsSync).toHaveBeenCalledWith('./uploads/test-file');
      expect(mockFs.mkdirSync).toHaveBeenCalledWith('./uploads/test-file');
    });

    it('should not create directory if it already exists', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      expect(mockFs.existsSync).toHaveBeenCalledWith('./uploads/test-file');
      expect(mockFs.mkdirSync).not.toHaveBeenCalled();
    });
  });

  describe('PDF Processing', () => {
    const validMessage = JSON.stringify({
      pdfFileLink: 'https://example.com/test.pdf',
      fileName: 'test-file',
    });

    it('should download PDF from provided URL', async () => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      expect(mockAxios.get).toHaveBeenCalledWith(
        'https://example.com/test.pdf',
        { responseType: 'arraybuffer' }
      );
    });

    it('should load PDF document and get page count', async () => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      expect(mockPDFDocument.load).toHaveBeenCalledWith(Buffer.from('mock-pdf-data'));
      expect(mockPdfDoc.getPageCount).toHaveBeenCalled();
    });

    it('should handle axios download error', async () => {
      const downloadError = new Error('Network error');
      mockAxios.get.mockRejectedValue(downloadError);

      await expect(messageHandler(validMessage)).rejects.toThrow('Network error');
    });

    it('should handle PDF loading error', async () => {
      const pdfError = new Error('Invalid PDF');
      mockPDFDocument.load.mockRejectedValue(pdfError);

      await expect(messageHandler(validMessage)).rejects.toThrow('Invalid PDF');
    });

    it('should configure pdf2pic converter with correct options', async () => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      expect(mockFromBuffer).toHaveBeenCalledWith(
        Buffer.from('mock-pdf-data'),
        expect.objectContaining({
          density: 400,
          saveFilename: 'test-file',
          savePath: './uploads/test-file',
          format: 'png',
          width: 1600,
          height: 900,
          quality: 100,
        })
      );
    });
  });

  describe('Image Conversion', () => {
    const validMessage = JSON.stringify({
      pdfFileLink: 'https://example.com/test.pdf',
      fileName: 'test-file',
    });

    it('should convert all pages to images', async () => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      await messageHandler(validMessage);

      // Should call converter for each page (3 pages in mock)
      expect(mockConverter).toHaveBeenCalledTimes(3);
      expect(mockConverter).toHaveBeenCalledWith(1, { responseType: 'image' });
      expect(mockConverter).toHaveBeenCalledWith(2, { responseType: 'image' });
      expect(mockConverter).toHaveBeenCalledWith(3, { responseType: 'image' });
    });

    it('should handle single page conversion error', async () => {
      mockConverter.mockImplementation((pageNum) => {
        if (pageNum === 2) {
          return Promise.reject(new Error('Conversion failed for page 2'));
        }
        return Promise.resolve({ success: true, page: pageNum });
      });

      await messageHandler(validMessage);

      expect(mockParentPort.postMessage).toHaveBeenCalledWith({ success: false });
      expect(console.error).toHaveBeenCalledWith(
        'Error converting page 2:',
        expect.any(Error)
      );
    });

    it('should handle multiple page conversion errors', async () => {
      mockConverter.mockRejectedValue(new Error('Conversion failed'));

      await messageHandler(validMessage);

      expect(mockParentPort.postMessage).toHaveBeenCalledWith({ success: false });
    });
  });

  describe('S3 Upload Process', () => {
    const validMessage = JSON.stringify({
      pdfFileLink: 'https://example.com/test.pdf',
      fileName: 'test-file',
    });

    beforeEach(() => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );
    });

    it('should upload all converted images to S3', async () => {
      await messageHandler(validMessage);

      // Should call saveImageToS3Bucket for each page (3 pages)
      expect(mockPuppeteerService.saveImageToS3Bucket).toHaveBeenCalledTimes(3);
      expect(mockPuppeteerService.saveImageToS3Bucket).toHaveBeenCalledWith(
        './uploads/test-file/test-file.1.png',
        'test-file'
      );
      expect(mockPuppeteerService.saveImageToS3Bucket).toHaveBeenCalledWith(
        './uploads/test-file/test-file.2.png',
        'test-file'
      );
      expect(mockPuppeteerService.saveImageToS3Bucket).toHaveBeenCalledWith(
        './uploads/test-file/test-file.3.png',
        'test-file'
      );
    });

    it('should handle S3 upload success and return image links', async () => {
      mockPuppeteerService.saveImageToS3Bucket.mockResolvedValue({
        success: true,
        data: [{ fileLink: 'https://cdn.example.com/image.png' }],
      });

      await messageHandler(validMessage);

      expect(mockParentPort.postMessage).toHaveBeenCalledWith({
        success: true,
        result: [
          'https://cdn.example.com/image.png',
          'https://cdn.example.com/image.png',
          'https://cdn.example.com/image.png',
        ],
      });
    });

    it('should handle S3 upload error', async () => {
      mockPuppeteerService.saveImageToS3Bucket.mockRejectedValue(
        new Error('S3 upload failed')
      );

      await messageHandler(validMessage);

      expect(mockParentPort.postMessage).toHaveBeenCalledWith({ success: false });
      expect(console.error).toHaveBeenCalledWith(
        'An error occurred during conversion or uploading:',
        expect.any(Error)
      );
    });

    it('should handle mixed S3 upload results', async () => {
      mockPuppeteerService.saveImageToS3Bucket
        .mockResolvedValueOnce({
          success: true,
          data: [{ fileLink: 'https://cdn.example.com/image1.png' }],
        })
        .mockResolvedValueOnce({
          success: true,
          data: [{ fileLink: 'https://cdn.example.com/image2.png' }],
        })
        .mockRejectedValueOnce(new Error('Upload failed for page 3'));

      await messageHandler(validMessage);

      expect(mockParentPort.postMessage).toHaveBeenCalledWith({ success: false });
    });
  });

  describe('Directory Cleanup', () => {
    const validMessage = JSON.stringify({
      pdfFileLink: 'https://example.com/test.pdf',
      fileName: 'test-file',
    });

    beforeEach(() => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );
    });

    it('should clean up directory after successful upload', async () => {
      await messageHandler(validMessage);

      expect(mockFs.rm).toHaveBeenCalledWith(
        './uploads/test-file',
        { recursive: true },
        expect.any(Function)
      );
    });

    it('should handle directory cleanup error after success', async () => {
      mockFs.rm.mockImplementation((_path, _options, callback) => {
        callback(new Error('Failed to remove directory'));
      });

      await messageHandler(validMessage);

      expect(console.error).toHaveBeenCalledWith(
        'Error removing directory:',
        expect.any(Error)
      );
    });

    it('should clean up directory after conversion error', async () => {
      const conversionError = new Error('Conversion failed');
      mockConverter.mockRejectedValue(conversionError);

      await messageHandler(validMessage);

      expect(mockFs.rm).toHaveBeenCalledWith(
        './uploads/test-file',
        { recursive: true },
        expect.any(Function)
      );
    });

    it('should handle directory cleanup error after conversion failure', async () => {
      const conversionError = new Error('Conversion failed');
      mockConverter.mockRejectedValue(conversionError);

      mockFs.rm.mockImplementation((_path, _options, callback) => {
        callback(new Error('Failed to remove directory'));
      });

      await messageHandler(validMessage);

      expect(console.error).toHaveBeenCalledWith(
        'Error removing directory:',
        expect.any(Error)
      );
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle message with missing pdfFileLink', async () => {
      const invalidMessage = JSON.stringify({
        fileName: 'test-file',
      });

      await expect(messageHandler(invalidMessage)).rejects.toThrow();
    });

    it('should handle message with missing fileName', async () => {
      const invalidMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
      });

      await expect(messageHandler(invalidMessage)).rejects.toThrow();
    });

    it('should handle empty message object', async () => {
      const emptyMessage = JSON.stringify({});

      await expect(messageHandler(emptyMessage)).rejects.toThrow();
    });

    it('should handle PDF with zero pages', async () => {
      mockPdfDoc.getPageCount.mockReturnValue(0);
      const validMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
        fileName: 'test-file',
      });

      await messageHandler(validMessage);

      expect(mockConverter).not.toHaveBeenCalled();
      expect(mockPuppeteerService.saveImageToS3Bucket).not.toHaveBeenCalled();
    });

    it('should handle PDF with many pages', async () => {
      mockPdfDoc.getPageCount.mockReturnValue(100);
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      const validMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
        fileName: 'test-file',
      });

      await messageHandler(validMessage);

      expect(mockConverter).toHaveBeenCalledTimes(100);
      expect(mockPuppeteerService.saveImageToS3Bucket).toHaveBeenCalledTimes(100);
    });

    it('should handle network timeout error', async () => {
      const timeoutError = new Error('ETIMEDOUT');
      timeoutError.code = 'ETIMEDOUT';
      mockAxios.get.mockRejectedValue(timeoutError);

      const validMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
        fileName: 'test-file',
      });

      await expect(messageHandler(validMessage)).rejects.toThrow('ETIMEDOUT');
    });

    it('should handle invalid PDF file format', async () => {
      const formatError = new Error('Invalid PDF format');
      mockPDFDocument.load.mockRejectedValue(formatError);

      const validMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
        fileName: 'test-file',
      });

      await expect(messageHandler(validMessage)).rejects.toThrow('Invalid PDF format');
    });

    it('should handle file system permission errors', async () => {
      const permissionError = new Error('EACCES: permission denied');
      permissionError.code = 'EACCES';
      mockFs.mkdirSync.mockImplementation(() => {
        throw permissionError;
      });

      const validMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
        fileName: 'test-file',
      });

      await expect(messageHandler(validMessage)).rejects.toThrow('EACCES: permission denied');
    });

    it('should handle special characters in fileName', async () => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );

      const specialCharMessage = JSON.stringify({
        pdfFileLink: 'https://example.com/test.pdf',
        fileName: 'test-file-with-special-chars-@#$%',
      });

      await messageHandler(specialCharMessage);

      expect(mockFs.existsSync).toHaveBeenCalledWith('./uploads/test-file-with-special-chars-@#$%');
      expect(mockFromBuffer).toHaveBeenCalledWith(
        expect.any(Buffer),
        expect.objectContaining({
          saveFilename: 'test-file-with-special-chars-@#$%',
          savePath: './uploads/test-file-with-special-chars-@#$%',
        })
      );
    });
  });

  describe('Console Logging', () => {
    const validMessage = JSON.stringify({
      pdfFileLink: 'https://example.com/test.pdf',
      fileName: 'test-file',
    });

    beforeEach(() => {
      mockConverter.mockImplementation((pageNum) =>
        Promise.resolve({ success: true, page: pageNum })
      );
    });

    it('should log process start message', async () => {
      await messageHandler(validMessage);

      expect(console.log).toHaveBeenCalledWith(
        '======================Image Conversion process started=========================='
      );
    });

    it('should log pdfFileLink and fileName', async () => {
      await messageHandler(validMessage);

      expect(console.log).toHaveBeenCalledWith('pdfFileLink', 'https://example.com/test.pdf');
      expect(console.log).toHaveBeenCalledWith('fileName', 'test-file');
    });

    it('should log page conversion success', async () => {
      await messageHandler(validMessage);

      expect(console.log).toHaveBeenCalledWith('Page 1 is now converted as image');
      expect(console.log).toHaveBeenCalledWith('Page 2 is now converted as image');
      expect(console.log).toHaveBeenCalledWith('Page 3 is now converted as image');
    });

    it('should log all pages converted message', async () => {
      await messageHandler(validMessage);

      expect(console.log).toHaveBeenCalledWith('All pages converted!');
    });

    it('should log directory removal success', async () => {
      await messageHandler(validMessage);

      expect(console.log).toHaveBeenCalledWith('Directory removed successfully.');
    });
  });
});
