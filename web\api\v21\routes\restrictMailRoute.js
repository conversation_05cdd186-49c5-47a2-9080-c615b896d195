const { Router } = require('express');
const { validate } = require('express-validation');
const { restrictMailValidation } = require('../middlewares/validations');
const { RestrictMailController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const restrictMailRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_restrictmail',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(restrictMailValidation.addRestrictMail, { keyByField: true }, { abortEarly: false }),
      RestrictMailController.addRestrictMail,
    );
    router.get(
      '/list_resetrictmail/:pageSize/:pageNo',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(
        restrictMailValidation.restrictMailList,
        { keyByField: true },
        { abortEarly: false },
      ),
      RestrictMailController.restrictMailList,
    );
    router.post(
      '/update_restrictmail',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(
        restrictMailValidation.updateRestrictMail,
        { keyByField: true },
        { abortEarly: false },
      ),
      RestrictMailController.updateRestrictMail,
    );
    router.post(
      '/delete_restrictmail',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(
        restrictMailValidation.deleteRestrictMail,
        { keyByField: true },
        { abortEarly: false },
      ),
      RestrictMailController.deleteRestrictMail,
    );
    return router;
  },
};
module.exports = restrictMailRoute;
