// Mock Sequelize constructor
const mockSequelize = {
  authenticate: jest.fn(),
  close: jest.fn(),
  define: jest.fn(),
  sync: jest.fn(),
};

jest.mock('sequelize', () => {
  return {
    __esModule: true,
    default: jest.fn(() => mockSequelize),
    Sequelize: jest.fn(() => mockSequelize),
    DataTypes: {
      STRING: 'STRING',
      INTEGER: 'INTEGER',
      BOOLEAN: 'BOOLEAN',
      DATE: 'DATE',
      TEXT: 'TEXT',
    },
  };
});

// Mock models directory
jest.mock('../../models', () => ({
  sequelize: mockSequelize,
  Sequelize: jest.fn(() => mockSequelize),
}));

// Mock wasteLogService
jest.mock('../../services/wasteLogService', () => ({
  addWasteLog: jest.fn(),
  listWasteLog: jest.fn(),
}));

const wasteLogController = require('../wasteLogController');
const wasteLogService = require('../../services/wasteLogService');

describe('wasteLogController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();

    // Mock console.log to avoid output during tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log
    console.log.mockRestore();
  });

  describe('addWasteLog', () => {
    it('should add waste log successfully', async () => {
      const mockWasteLog = {
        id: 1,
        projectId: 123,
        wasteType: 'concrete',
        quantity: 5.5,
        unit: 'cubic_yards',
        status: 'pending',
      };

      wasteLogService.addWasteLog.mockImplementation((req, callback) => {
        callback(mockWasteLog, null);
      });

      await wasteLogController.addWasteLog(mockReq, mockRes, mockNext);

      expect(wasteLogService.addWasteLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'WasteLog added successfully.',
        data: mockWasteLog,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from add waste log', async () => {
      const mockError = new Error('Service error');
      wasteLogService.addWasteLog.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await wasteLogController.addWasteLog(mockReq, mockRes, mockNext);

      expect(wasteLogService.addWasteLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(console.log).toHaveBeenCalledWith(mockError, "error======");
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listWasteLog', () => {
    it('should list waste logs successfully', async () => {
      const mockWasteLogDetail = [
        { id: 1, projectId: 123, wasteType: 'concrete', quantity: 5.5 },
        { id: 2, projectId: 124, wasteType: 'steel', quantity: 2.0 },
      ];

      wasteLogService.listWasteLog.mockImplementation((req, callback) => {
        callback(mockWasteLogDetail, null);
      });

      await wasteLogController.listWasteLog(mockReq, mockRes, mockNext);

      expect(wasteLogService.listWasteLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Waste Log Listed successfully.',
        data: mockWasteLogDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list waste log', async () => {
      const mockError = new Error('Service error');
      wasteLogService.listWasteLog.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await wasteLogController.listWasteLog(mockReq, mockRes, mockNext);

      expect(wasteLogService.listWasteLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
