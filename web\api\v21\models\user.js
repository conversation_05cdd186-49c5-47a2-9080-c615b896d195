const Sequelize = require('sequelize');
const { Role } = require('.');
const { bcryptPassword } = require('../services/password');

const { Op } = Sequelize;

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    firstName: DataTypes.STRING,
    email: {
      type: DataTypes.STRING,
      unique: true,
      validate: {
        isEmail: {
          msg: 'Email format is Invalid.',
        },
      },
    },
    stripeCustomerId: DataTypes.STRING,
    userType: {
      type: DataTypes.ENUM,
      values: ['user', 'super admin', 'folloit admin'],
    },
    phoneCode: DataTypes.STRING,
    phoneNumber: DataTypes.STRING,
    password: DataTypes.STRING,
    profilePic: DataTypes.STRING,
    lastName: DataTypes.STRING,
    publicSchemaId: {
      type: DataTypes.INTEGER,
    },
    isEnterprise: DataTypes.BOOLEAN,
    isDeleted: DataTypes.BOOLEAN,
    isAccount: DataTypes.BOOLEAN,
    otpCode: DataTypes.STRING,
    secret: DataTypes.STRING,
    loginDateTime: DataTypes.DATE,
    resetPasswordToken: DataTypes.STRING,
    isActive: DataTypes.BOOLEAN,
    versionFlag: DataTypes.BOOLEAN,
  });

  User.getAll = async () => {
    const user = await User.findAll({
      where: { isDeleted: false },
    });
    return user;
  };
  User.associate = (models) => {
    User.hasMany(models.Member);
    User.hasOne(models.Company, {
      as: 'Company',
      foreignKey: 'createdBy',
    });
    User.hasMany(models.SchedulerReport, {
      as: 'SchedulerReport',
      foreignKey: 'createdBy',
    });
    return User;
  };
  User.getBy = async (attr) => {
    const user = await User.findOne({ where: { ...attr } });
    return user;
  };

  User.createInstance = async (userData) => {
    const user = userData;
    if (user?.password) {
      await bcryptPassword(user.password, (encPassword) => {
        user.password = encPassword;
      });
    }
    const newUser = await User.create(user);
    return newUser;
  };
  User.getUserWithRole = async (attr) => {
    const user = await User.findOne({
      where: Sequelize.and({ ...attr }, Sequelize.or({ roleId: [1, 2, 3] })),
    });

    return user;
  };

  User.prototype.updateInstance = async (id, args) => {
    const user = await User.update(args, { where: { id } });

    return user;
  };

  User.bulkCreateUsers = async (users) => {
    const count = users.length;
    const roleCount = await Role.count();
    if (count > roleCount) {
      await User.bulkCreate(users);
    }
  };
  User.getAllMembers = async ({
    limit,
    offset,
    searchText,
    sortByField = 'id',
    sortByType = 'DESC',
    nameFilter,
    companyFilter,
    roleFilter,
    statusFilter,
  }) => {
    let commonSearch = {
      [Op.and]: {
        isDeleted: false,
        userType: 'user',
      },
    };
    let roleFilterCondition;
    if (searchText) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { firstName: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { lastName: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { email: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { phoneNumber: { [Sequelize.Op.iLike]: `%${searchText}%` } },
            ],
          },
        ],
      };
    }
    if (nameFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ firstName: { [Sequelize.Op.iLike]: `%${nameFilter}%` } }],
          },
        ],
      };
    }
    if (companyFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$Company.companyName$': {
                  [Sequelize.Op.iLike]: `%${companyFilter}%`,
                },
              },
            ],
          },
        ],
      };
    }
    if (roleFilter) {
      roleFilterCondition = { RoleId: +roleFilter };
    }
    if (statusFilter) {
      let statusType;
      if (statusFilter.toLowerCase() === 'active') {
        statusType = true;
      } else {
        statusType = false;
      }
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ isActive: statusType }],
          },
        ],
      };
    }
    const member = await User.findAll({
      where: commonSearch,
      offset,
      limit,
      include: [
        {
          association: 'Members',
          attributes: ['id', 'RoleId', 'ParentCompanyId'],
          where: roleFilterCondition,
          include: [{ association: 'Role', attributes: ['roleName'] }],
        },
        {
          association: 'Company',
          attributes: ['id', 'companyName', 'website', 'address', 'secondAddress'],
        },
      ],
      order: [[`${sortByField}`, `${sortByType}`]],
      attributes: [
        'id',
        'email',
        'firstName',
        'lastName',
        'phoneNumber',
        'phoneCode',
        'isActive',
        'profilePic',
      ],
    });
    return member;
  };

  User.getMemberDetail = async (attr) => {
    const member = await User.findOne({
      where: { ...attr, isDeleted: false },
      include: [
        {
          association: 'Company',
          attributes: ['id', 'companyName', 'website', 'address', 'secondAddress'],
        },
      ],
      attributes: [
        'id',
        'email',
        'firstName',
        'lastName',
        'phoneNumber',
        'phoneCode',
        'isActive',
        'profilePic',
      ],
    });

    return member;
  };

  return User;
};
