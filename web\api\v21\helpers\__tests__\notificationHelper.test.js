const moment = require('moment');
const Cryptr = require('cryptr');

// Mock dependencies before requiring the module
jest.mock('moment', () => {
  const mockMoment = jest.fn(() => ({
    utc: jest.fn(() => ({
      format: jest.fn((format) => {
        if (format === 'MMMM DD') return 'January 01';
        if (format === 'hh:mm A zz') return '12:00 PM UTC';
        return 'January 01';
      })
    }))
  }));
  return mockMoment;
});

jest.mock('cryptr');

jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      and: 'and',
      or: 'or',
      eq: 'eq',
      ne: 'ne',
      in: 'in',
      notIn: 'notIn'
    }
  },
  DigestNotification: {
    create: jest.fn()
  },
  NotificationPreference: {
    findOne: jest.fn()
  }
}));

// Mock Cryptr implementation
Cryptr.mockImplementation(() => ({
  encrypt: (v) => `enc-${v}`
}));

const notificationHelper = require('../notificationHelper');
const models = require('../../models');

describe('notificationHelper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.BASE_URL = 'https://base.url';
    console.log = jest.fn(); // Mock console.log
  });

  describe('createDailyDigestData', () => {
    it('should create digest notification for Delivery Request', async () => {
      const data = { ProjectId: 1 };
      const memberId = 2;
      const memberDetails = { ParentCompanyId: 3 };
      const loginUser = { firstName: 'John', lastName: 'Doe' };
      const dailyDigestMessage = 'created a new delivery request';
      const requestType = 'Delivery Request';
      const messages = 'View Details';
      const requestId = 5;

      await notificationHelper.createDailyDigestData({
        data,
        memberId,
        memberDetails,
        loginUser,
        dailyDigestMessage,
        requestType,
        messages,
        requestId,
      });

      expect(models.DigestNotification.create).toHaveBeenCalled();
      const arg = models.DigestNotification.create.mock.calls[0][0];
      expect(arg.MemberId).toBe(2);
      expect(arg.ProjectId).toBe(1);
      expect(arg.ParentCompanyId).toBe(3);
      expect(arg.isSent).toBe(false);
      expect(arg.isDeleted).toBe(false);
      expect(arg.description).toContain('John  Doe');
      expect(arg.description).toContain('created a new delivery request');
      expect(arg.description).toContain('View Details');
      expect(arg.description).toContain('enc-5');
      expect(arg.description).toContain('enc-2');
      expect(arg.description).toContain('delivery-request');
      expect(arg.description).toContain('height:18px;');
      expect(arg.description).toContain('January 01');
      expect(arg.description).toContain('12:00 PM UTC');
    });

    it('should create digest notification for Crane Request', async () => {
      const data = { ProjectId: 1 };
      const memberId = 2;
      const memberDetails = { ParentCompanyId: 3 };
      const loginUser = { firstName: 'Jane', lastName: 'Smith' };
      const dailyDigestMessage = 'updated a crane request';
      const requestType = 'Crane Request';
      const messages = 'View Details';
      const requestId = 10;

      await notificationHelper.createDailyDigestData({
        data,
        memberId,
        memberDetails,
        loginUser,
        dailyDigestMessage,
        requestType,
        messages,
        requestId,
      });

      expect(models.DigestNotification.create).toHaveBeenCalled();
      const arg = models.DigestNotification.create.mock.calls[0][0];
      expect(arg.description).toContain('Jane  Smith');
      expect(arg.description).toContain('updated a crane request');
      expect(arg.description).toContain('crane-request');
      expect(arg.description).toContain('height:32px;');
      expect(arg.description).toContain('enc-10');
    });

    it('should create digest notification for Concrete Request', async () => {
      const data = { ProjectId: 1 };
      const memberId = 2;
      const memberDetails = { ParentCompanyId: 3 };
      const loginUser = { firstName: 'Bob', lastName: 'Johnson' };
      const dailyDigestMessage = 'created a concrete request';
      const requestType = 'Concrete Request';
      const messages = 'View Details';
      const requestId = 15;

      await notificationHelper.createDailyDigestData({
        data,
        memberId,
        memberDetails,
        loginUser,
        dailyDigestMessage,
        requestType,
        messages,
        requestId,
      });

      expect(models.DigestNotification.create).toHaveBeenCalled();
      const arg = models.DigestNotification.create.mock.calls[0][0];
      expect(arg.description).toContain('Bob  Johnson');
      expect(arg.description).toContain('created a concrete request');
      expect(arg.description).toContain('concrete-request');
      expect(arg.description).toContain('height:18px;');
      expect(arg.description).toContain('enc-15');
    });

    it('should handle unknown request type', async () => {
      const data = { ProjectId: 1 };
      const memberId = 2;
      const memberDetails = { ParentCompanyId: 3 };
      const loginUser = { firstName: 'Alice', lastName: 'Brown' };
      const dailyDigestMessage = 'created an unknown request';
      const requestType = 'Unknown Request';
      const messages = 'View Details';
      const requestId = 20;

      await notificationHelper.createDailyDigestData({
        data,
        memberId,
        memberDetails,
        loginUser,
        dailyDigestMessage,
        requestType,
        messages,
        requestId,
      });

      expect(models.DigestNotification.create).toHaveBeenCalled();
      const arg = models.DigestNotification.create.mock.calls[0][0];
      expect(arg.description).toContain('Alice  Brown');
      expect(arg.description).toContain('created an unknown request');
      expect(arg.description).toContain('undefined'); // imageUrl, link, height will be undefined
    });
  });

  describe('checkMemberNotificationPreference', () => {
    it('should return notification preference when found', async () => {
      const mockPreference = {
        id: 1,
        MemberId: 2,
        ProjectId: 1,
        ParentCompanyId: 3,
        NotificationPreferenceItemId: 4,
        instant: true,
        dailyDigest: false,
        NotificationPreferenceItem: {
          id: 4,
          description: 'Test notification',
          inappNotification: true,
          emailNotification: false
        }
      };

      models.NotificationPreference.findOne.mockResolvedValue(mockPreference);

      const data = { ProjectId: 1 };
      const MemberId = 2;
      const NotificationPreferenceItemId = 4;

      const result = await notificationHelper.checkMemberNotificationPreference(
        data,
        MemberId,
        NotificationPreferenceItemId,
      );

      expect(models.NotificationPreference.findOne).toHaveBeenCalledWith({
        where: {
          MemberId: 2,
          isDeleted: false,
          ProjectId: 1,
        },
        attributes: [
          'id',
          'MemberId',
          'ProjectId',
          'ParentCompanyId',
          'NotificationPreferenceItemId',
          'instant',
          'dailyDigest',
        ],
        include: [
          {
            association: 'NotificationPreferenceItem',
            where: {
              id: 4,
              isDeleted: false,
            },
            attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
          },
        ],
      });

      expect(result).toEqual(mockPreference);
    });

    it('should return null when notification preference not found', async () => {
      models.NotificationPreference.findOne.mockResolvedValue(null);

      const data = { ProjectId: 1 };
      const MemberId = 2;
      const NotificationPreferenceItemId = 4;

      const result = await notificationHelper.checkMemberNotificationPreference(
        data,
        MemberId,
        NotificationPreferenceItemId,
      );

      expect(result).toBeNull();
    });
  });

  describe('createDeliveryPersonNotification', () => {
    let mockDeliveryPersonNotification;
    let mockNotificationPreference;

    beforeEach(() => {
      mockDeliveryPersonNotification = {
        create: jest.fn().mockResolvedValue({ id: 1 })
      };

      mockNotificationPreference = {
        id: 1,
        instant: true,
        NotificationPreferenceItem: {
          inappNotification: true
        }
      };
    });

    it('should create notifications for admin data when conditions are met', async () => {
      // Mock the checkMemberNotificationPreference method
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockNotificationPreference);

      const adminData = [
        { id: 1 },
        { id: 2 }
      ];
      const personData = [];
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 }; // Different from admin data ids
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(2);
      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledTimes(2);

      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledWith({
        ProjectId: 10,
        NotificationId: 20,
        MemberId: 1,
      });

      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledWith({
        ProjectId: 10,
        NotificationId: 20,
        MemberId: 2,
      });
    });

    it('should create notifications for person data when conditions are met', async () => {
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockNotificationPreference);

      const adminData = [];
      const personData = [
        { Member: { id: 1 } },
        { Member: { id: 2 } }
      ];
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 }; // Different from person data ids
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(2);
      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledTimes(2);
    });

    it('should skip notification creation when member is same as current member', async () => {
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockNotificationPreference);

      const adminData = [{ id: 3 }]; // Same as memberData.id
      const personData = [{ Member: { id: 3 } }]; // Same as memberData.id
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 };
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(notificationHelper.checkMemberNotificationPreference).not.toHaveBeenCalled();
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should skip notification creation when preference not found', async () => {
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(null);

      const adminData = [{ id: 1 }];
      const personData = [];
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 };
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should skip notification creation when inappNotification is false', async () => {
      const mockPreferenceNoInApp = {
        id: 1,
        instant: true,
        NotificationPreferenceItem: {
          inappNotification: false
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockPreferenceNoInApp);

      const adminData = [{ id: 1 }];
      const personData = [];
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 };
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should skip notification creation when instant is false', async () => {
      const mockPreferenceNoInstant = {
        id: 1,
        instant: false,
        NotificationPreferenceItem: {
          inappNotification: true
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockPreferenceNoInstant);

      const adminData = [{ id: 1 }];
      const personData = [];
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 };
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should handle errors and return error', async () => {
      const error = new Error('Test error');
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockRejectedValue(error);

      const adminData = [{ id: 1 }];
      const personData = [];
      const projectDetails = { id: 10 };
      const newNotification = { id: 20 };
      const memberData = { id: 3 };
      const NotificationPreferenceItemId = 5;

      const result = await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        mockDeliveryPersonNotification,
        memberData,
        NotificationPreferenceItemId
      );

      expect(console.log).toHaveBeenCalledWith('error', error);
      expect(result).toBe(error);
    });
  });

  describe('memberNotificationCreation', () => {
    it('should create notifications for all existing notifications in project', async () => {
      const mockNotifications = [
        { id: 1 },
        { id: 2 },
        { id: 3 }
      ];

      const mockNotification = {
        findAll: jest.fn().mockResolvedValue(mockNotifications)
      };

      const mockDeliveryPersonNotification = {
        create: jest.fn().mockResolvedValue({ id: 1 })
      };

      const memberData = {
        id: 5,
        ProjectId: 10
      };

      await notificationHelper.memberNotificationCreation(
        memberData,
        mockDeliveryPersonNotification,
        mockNotification
      );

      expect(mockNotification.findAll).toHaveBeenCalledWith({
        where: { ProjectId: 10 }
      });

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledTimes(3);

      // Check that create was called with correct data structure
      const createCalls = mockDeliveryPersonNotification.create.mock.calls;
      expect(createCalls[0][0]).toEqual({
        notData: {
          ProjectId: 10,
          NotificationId: 1,
          MemberId: 5
        }
      });
      expect(createCalls[1][0]).toEqual({
        notData: {
          ProjectId: 10,
          NotificationId: 2,
          MemberId: 5
        }
      });
      expect(createCalls[2][0]).toEqual({
        notData: {
          ProjectId: 10,
          NotificationId: 3,
          MemberId: 5
        }
      });
    });

    it('should handle empty notification list', async () => {
      const mockNotification = {
        findAll: jest.fn().mockResolvedValue([])
      };

      const mockDeliveryPersonNotification = {
        create: jest.fn().mockResolvedValue({ id: 1 })
      };

      const memberData = {
        id: 5,
        ProjectId: 10
      };

      await notificationHelper.memberNotificationCreation(
        memberData,
        mockDeliveryPersonNotification,
        mockNotification
      );

      expect(mockNotification.findAll).toHaveBeenCalledWith({
        where: { ProjectId: 10 }
      });

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });
  });

  describe('createMemberDeliveryLocationInAppNotification', () => {
    let mockDeliveryPersonNotification;

    beforeEach(() => {
      mockDeliveryPersonNotification = {
        create: jest.fn().mockResolvedValue({ id: 1 })
      };
    });

    it('should create notifications for members who follow the location', async () => {
      const mockNotificationPreference = {
        id: 1,
        instant: true,
        NotificationPreferenceItem: {
          inappNotification: true
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockNotificationPreference);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        },
        {
          follow: true,
          Member: { id: 2 }
        },
        {
          follow: false,
          Member: { id: 3 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        mockDeliveryPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(2);
      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledTimes(2);

      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledWith({
        ProjectId: 10,
        NotificationId: 20,
        MemberId: 1,
        isLocationFollowNotification: true
      });

      expect(mockDeliveryPersonNotification.create).toHaveBeenCalledWith({
        ProjectId: 10,
        NotificationId: 20,
        MemberId: 2,
        isLocationFollowNotification: true
      });
    });

    it('should skip members who do not follow the location', async () => {
      const memberLocationPreference = [
        {
          follow: false,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        mockDeliveryPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should skip notification when preference not found', async () => {
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(null);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        mockDeliveryPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should skip notification when inappNotification is false', async () => {
      const mockPreference = {
        id: 1,
        instant: true,
        NotificationPreferenceItem: {
          inappNotification: false
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockPreference);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        mockDeliveryPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });

    it('should skip notification when instant is false', async () => {
      const mockPreference = {
        id: 1,
        instant: false,
        NotificationPreferenceItem: {
          inappNotification: true
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockPreference);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        mockDeliveryPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(mockDeliveryPersonNotification.create).not.toHaveBeenCalled();
    });
  });

  describe('createMemberInspectionLocationInAppNotification', () => {
    let mockInspectionPersonNotification;

    beforeEach(() => {
      mockInspectionPersonNotification = {
        create: jest.fn().mockResolvedValue({ id: 1 })
      };
    });

    it('should check notification preferences for members who follow the location', async () => {
      const mockNotificationPreference = {
        id: 1,
        instant: true,
        NotificationPreferenceItem: {
          inappNotification: true
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockNotificationPreference);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        },
        {
          follow: true,
          Member: { id: 2 }
        },
        {
          follow: false,
          Member: { id: 3 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberInspectionLocationInAppNotification(
        mockInspectionPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(2);

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledWith(
        { ProjectId: 10 },
        1,
        5
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledWith(
        { ProjectId: 10 },
        2,
        5
      );

      // Verify console.log was called for members with valid preferences
      expect(console.log).toHaveBeenCalledWith('getMemberNotificationPreference', mockNotificationPreference);
    });

    it('should skip members who do not follow the location', async () => {
      const memberLocationPreference = [
        {
          follow: false,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberInspectionLocationInAppNotification(
        mockInspectionPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).not.toHaveBeenCalled();
      expect(console.log).not.toHaveBeenCalledWith(expect.stringContaining('getMemberNotificationPreference'));
    });

    it('should skip notification when preference not found', async () => {
      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(null);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberInspectionLocationInAppNotification(
        mockInspectionPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(console.log).not.toHaveBeenCalledWith(expect.stringContaining('getMemberNotificationPreference'));
    });

    it('should skip notification when inappNotification is false', async () => {
      const mockPreference = {
        id: 1,
        instant: true,
        NotificationPreferenceItem: {
          inappNotification: false
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockPreference);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberInspectionLocationInAppNotification(
        mockInspectionPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(console.log).not.toHaveBeenCalledWith(expect.stringContaining('getMemberNotificationPreference'));
    });

    it('should skip notification when instant is false', async () => {
      const mockPreference = {
        id: 1,
        instant: false,
        NotificationPreferenceItem: {
          inappNotification: true
        }
      };

      notificationHelper.checkMemberNotificationPreference = jest.fn().mockResolvedValue(mockPreference);

      const memberLocationPreference = [
        {
          follow: true,
          Member: { id: 1 }
        }
      ];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberInspectionLocationInAppNotification(
        mockInspectionPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).toHaveBeenCalledTimes(1);
      expect(console.log).not.toHaveBeenCalledWith(expect.stringContaining('getMemberNotificationPreference'));
    });

    it('should handle empty member location preference array', async () => {
      const memberLocationPreference = [];

      const ProjectId = 10;
      const NotificationId = 20;
      const NotificationPreferenceItemId = 5;

      await notificationHelper.createMemberInspectionLocationInAppNotification(
        mockInspectionPersonNotification,
        ProjectId,
        NotificationId,
        memberLocationPreference,
        NotificationPreferenceItemId
      );

      expect(notificationHelper.checkMemberNotificationPreference).not.toHaveBeenCalled();
      expect(console.log).not.toHaveBeenCalledWith(expect.stringContaining('getMemberNotificationPreference'));
    });
  });
});
