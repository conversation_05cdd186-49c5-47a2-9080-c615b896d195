const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
    put: jest.fn(),
    route: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('body-parser', () => ({}));

jest.mock('../../controllers', () => ({
  StripeController: {
    createPlan: jest.fn(),
    editPlan: jest.fn(),
    listAllPlans: jest.fn(),
    upgradePlanList: jest.fn(),
    addCard: jest.fn(),
    listPlans: jest.fn(),
    updatePlanDetail: jest.fn(),
    cancelSubscription: jest.fn(),
    holdSubscription: jest.fn(),
    stripePortalSession: jest.fn(),
    wehook: jest.fn(),
    checkout: jest.fn(),
    createCheckoutSession: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  stripeValidation: {
    createSubscription: jest.fn(),
    editSubscription: jest.fn(),
    cancelSubscription: jest.fn(),
    holdSubscription: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAdmin: jest.fn(),
  isProjectAdmin: jest.fn(),
}));

describe('stripeRoute', () => {
  let router;
  let stripeRoute;
  let StripeController;
  let passportConfig;
  let checkAdmin;
  let stripeValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn(),
      get: jest.fn(),
      put: jest.fn(),
      route: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    stripeRoute = require('../stripeRoute');
    const controllers = require('../../controllers');
    StripeController = controllers.StripeController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    const validations = require('../../middlewares/validations');
    stripeValidation = validations.stripeValidation;
    validate = require('express-validation').validate;
  });

  describe('router', () => {
    it('should create a router instance', () => {
      const result = stripeRoute.router;
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);
    });

    it('should register POST /create_plan route with authentication, admin check, and validation', () => {
      stripeRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/create_plan',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        'mocked-validate-middleware', // validate middleware
        StripeController.createPlan,
      );

      expect(validate).toHaveBeenCalledWith(
        stripeValidation.createSubscription,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should register POST /edit_plan route with authentication, admin check, and validation', () => {
      stripeRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/edit_plan',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        'mocked-validate-middleware', // validate middleware
        StripeController.editPlan,
      );
    });

    it('should register GET /list_all_plans/:interval route without authentication', () => {
      stripeRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/list_all_plans/:interval',
        StripeController.listAllPlans,
      );
    });

    it('should register GET /list_upgrade_plans/:interval route without authentication', () => {
      stripeRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/list_upgrade_plans/:interval',
        StripeController.upgradePlanList,
      );
    });

    it('should register POST /add_card route with authentication only', () => {
      stripeRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/add_card',
        passportConfig.isAuthenticated,
        StripeController.addCard,
      );
    });

    it('should register GET /list_plans route with authentication and admin check', () => {
      stripeRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/list_plans',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        StripeController.listPlans,
      );
    });

    it('should register PUT /update_plan_detail/:id route with authentication and admin check', () => {
      stripeRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/update_plan_detail/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        StripeController.updatePlanDetail,
      );
    });

    it('should register route for /cancel_subscription/:ProjectId with validation and project admin check', () => {
      stripeRoute.router;

      expect(router.route).toHaveBeenCalledWith('/cancel_subscription/:ProjectId');
      expect(router.get).toHaveBeenCalledWith(
        passportConfig.isAuthenticated,
        'mocked-validate-middleware', // validate middleware
        checkAdmin.isProjectAdmin,
        StripeController.cancelSubscription,
      );
    });

    it('should register route for /hold_resume_subscription/:ProjectId with validation and project admin check', () => {
      stripeRoute.router;

      expect(router.route).toHaveBeenCalledWith('/hold_resume_subscription/:ProjectId');
      expect(router.get).toHaveBeenCalledWith(
        passportConfig.isAuthenticated,
        'mocked-validate-middleware', // validate middleware
        checkAdmin.isProjectAdmin,
        StripeController.holdSubscription,
      );
    });

    it('should register GET /get_session_url route with authentication', () => {
      stripeRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_session_url',
        passportConfig.isAuthenticated,
        StripeController.stripePortalSession,
      );
    });

    it('should register POST /webhook route without authentication', () => {
      stripeRoute.router;

      expect(router.post).toHaveBeenCalledWith('/webhook', StripeController.wehook);
    });

    it('should register POST /checkout route with authentication', () => {
      stripeRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/checkout',
        passportConfig.isAuthenticated,
        StripeController.checkout,
      );
    });

    it('should register POST /create_checkout_session route without authentication', () => {
      stripeRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/create_checkout_session',
        StripeController.createCheckoutSession,
      );
    });
  });
});
