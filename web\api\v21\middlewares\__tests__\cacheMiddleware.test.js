const cacheMiddleware = require('../cacheMiddleware');
const cacheService = require('../../utils/cacheService');
const { CACHE_TTL } = require('../../config/cache');

// Mock dependencies
jest.mock('../../utils/cacheService');
jest.mock('../../config/cache', () => ({
  CACHE_TTL: {
    DEFAULT: 14400,
    SHORT: 1800,
    MEDIUM: 3600,
    LONG: 7200,
    EXTENDED: 14400,
    DAY: 86400
  }
}));

describe('cacheMiddleware', () => {
  let mockReq, mockRes, mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      query: {},
      body: {},
      user: { id: 'user123' },
      method: 'GET'
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      statusCode: 200
    };
    mockNext = jest.fn();
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    cacheService.get = jest.fn();
    cacheService.set = jest.fn();
    cacheService.generateCacheKey = jest.fn().mockReturnValue('mocked-cache-key');
    cacheService.generateHash = jest.fn().mockReturnValue('mocked-hash');
    cacheService.invalidateProjectCache = jest.fn().mockResolvedValue();
    cacheService.invalidateEventCache = jest.fn().mockResolvedValue();
    cacheService.delPattern = jest.fn().mockResolvedValue();
  });

  describe('cacheGetRequests', () => {
    it('should call next when no keyGenerator is provided', async () => {
      const middleware = cacheMiddleware.cacheGetRequests();
      await middleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(cacheService.get).not.toHaveBeenCalled();
    });

    it('should call next when keyGenerator returns null', async () => {
      const keyGenerator = jest.fn().mockReturnValue(null);
      const middleware = cacheMiddleware.cacheGetRequests({ keyGenerator });
      await middleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(cacheService.get).not.toHaveBeenCalled();
    });

    it('should return cached data when cache hit', async () => {
      const cachedData = { data: 'cached' };
      const keyGenerator = jest.fn().mockReturnValue('test-key');
      cacheService.get.mockReturnValue(cachedData);
      
      const middleware = cacheMiddleware.cacheGetRequests({ keyGenerator });
      await middleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.json).toHaveBeenCalledWith(cachedData);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should cache response when cache miss and successful response', async () => {
      const keyGenerator = jest.fn().mockReturnValue('test-key');
      const responseData = { data: 'response' };
      cacheService.get.mockReturnValue(null);
      cacheService.set.mockReturnValue(true);
      
      const middleware = cacheMiddleware.cacheGetRequests({ keyGenerator });
      await middleware(mockReq, mockRes, mockNext);
      
      // Simulate response
      mockRes.json(responseData);
      
      expect(cacheService.set).toHaveBeenCalledWith('test-key', responseData, CACHE_TTL.DEFAULT);
    });

    it('should handle cache service errors gracefully', async () => {
      const keyGenerator = jest.fn().mockReturnValue('test-key');
      cacheService.get.mockImplementation(() => {
        throw new Error('Cache error');
      });
      
      const middleware = cacheMiddleware.cacheGetRequests({ keyGenerator });
      await middleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
    });

    it('should use custom TTL when provided', async () => {
      const keyGenerator = jest.fn().mockReturnValue('test-key');
      const customTTL = 3600;
      cacheService.get.mockReturnValue(null);
      
      const middleware = cacheMiddleware.cacheGetRequests({ keyGenerator, ttl: customTTL });
      await middleware(mockReq, mockRes, mockNext);
      
      // Simulate response
      mockRes.json({ data: 'test' });
      
      expect(cacheService.set).toHaveBeenCalledWith('test-key', { data: 'test' }, customTTL);
    });

    it('should not cache non-successful responses', async () => {
      const keyGenerator = jest.fn().mockReturnValue('test-key');
      cacheService.get.mockReturnValue(null);
      
      const middleware = cacheMiddleware.cacheGetRequests({ keyGenerator });
      await middleware(mockReq, mockRes, mockNext);
      
      // Set non-successful status
      mockRes.statusCode = 400;
      mockRes.json({ error: 'Bad Request' });
      
      expect(cacheService.set).not.toHaveBeenCalled();
    });
  });

  describe('invalidateCacheAfterMutation', () => {
    it('should call invalidation function for successful responses', async () => {
      const invalidationFunction = jest.fn().mockResolvedValue();
      
      const middleware = cacheMiddleware.invalidateCacheAfterMutation({ invalidationFunction });
      await middleware(mockReq, mockRes, mockNext);
      
      // Simulate successful response
      mockRes.json({ success: true });
      
      expect(invalidationFunction).toHaveBeenCalledWith(mockReq);
    });

    it('should not call invalidation function for non-successful responses', async () => {
      const invalidationFunction = jest.fn();
      
      const middleware = cacheMiddleware.invalidateCacheAfterMutation({ invalidationFunction });
      await middleware(mockReq, mockRes, mockNext);
      
      // Set non-successful status
      mockRes.statusCode = 400;
      mockRes.json({ error: 'Bad Request' });
      
      expect(invalidationFunction).not.toHaveBeenCalled();
    });

    it('should handle invalidation errors gracefully', async () => {
      const invalidationFunction = jest.fn().mockRejectedValue(new Error('Invalidation error'));
      
      const middleware = cacheMiddleware.invalidateCacheAfterMutation({ invalidationFunction });
      await middleware(mockReq, mockRes, mockNext);
      
      // Simulate successful response
      mockRes.json({ success: true });
      
      expect(invalidationFunction).toHaveBeenCalled();
    });
  });

  describe('Key Generation Functions', () => {
    describe('generateCalendarEventsKey', () => {
      it('should generate key with all parameters', () => {
        mockReq.params = { ProjectId: 'proj123' };
        mockReq.query = {
          ParentCompanyId: 'comp123',
          search: 'test',
          isApplicableToDelivery: true,
          isApplicableToCrane: false,
          isApplicableToConcrete: true,
          isApplicableToInspection: false,
          weeklyReportTest: 'weekly',
          start: '2023-01-01',
          end: '2023-01-31',
          startDate: '2023-01-01',
          endDate: '2023-01-31',
          eventStartTime: '09:00',
          eventEndTime: '17:00',
          timezone: 'UTC'
        };
        
        const result = cacheMiddleware.generateCalendarEventsKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('calendar_events', expect.any(Object));
      });
    });

    describe('generateCalendarEventKey', () => {
      it('should generate key for single event', () => {
        mockReq.params = { id: 'event123', ProjectId: 'proj123' };
        mockReq.query = { ParentCompanyId: 'comp123' };
        
        const result = cacheMiddleware.generateCalendarEventKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('calendar_event', expect.any(Object));
      });
    });

    describe('generateProjectsCompanyKey', () => {
      it('should generate key for projects company', () => {
        const result = cacheMiddleware.generateProjectsCompanyKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('projects_company', expect.any(Object));
      });
    });

    describe('generateAccountProjectsKey', () => {
      it('should generate key for account projects', () => {
        mockReq.query = { companyId: 'comp123' };
        
        const result = cacheMiddleware.generateAccountProjectsKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('account_projects', expect.any(Object));
      });
    });

    describe('generateMemberListKey', () => {
      it('should generate key for member list', () => {
        mockReq.params = { 
          ProjectId: 'proj123', 
          ParentCompanyId: 'comp123',
          pageSize: 10,
          pageNo: 1
        };
        
        const result = cacheMiddleware.generateMemberListKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('member_list_paginated', expect.any(Object));
      });
    });

    describe('generateAllMemberListKey', () => {
      it('should generate key for all member list', () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        const result = cacheMiddleware.generateAllMemberListKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('member_list_all', expect.any(Object));
      });
    });

    describe('generateGateListKey', () => {
      it('should generate key for gate list', () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        const result = cacheMiddleware.generateGateListKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('gate_list', expect.any(Object));
      });
    });

    describe('generateEquipmentListKey', () => {
      it('should generate key for equipment list', () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        const result = cacheMiddleware.generateEquipmentListKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('equipment_list', expect.any(Object));
      });
    });

    describe('generateAllCalendarKey', () => {
      it('should generate key for all calendar', () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        mockReq.body = { filter: 'test' };
        
        const result = cacheMiddleware.generateAllCalendarKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('all_calendar', expect.any(Object));
      });
    });

    describe('generateAttachmentsKey', () => {
      it('should generate key for attachments', () => {
        mockReq.params = { 
          DeliveryRequestId: 'del123',
          InspectionRequestId: 'insp123',
          ConcreteRequestId: 'conc123',
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateAttachmentsKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('attachments', expect.any(Object));
      });
    });

    describe('generateCommentsKey', () => {
      it('should generate key for comments', () => {
        mockReq.params = { 
          DeliveryRequestId: 'del123',
          InspectionRequestId: 'insp123',
          ConcreteRequestId: 'conc123',
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateCommentsKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('comments', expect.any(Object));
      });
    });

    describe('generateHistoryKey', () => {
      it('should generate key for history', () => {
        mockReq.params = { 
          DeliveryRequestId: 'del123',
          InspectionRequestId: 'insp123',
          ConcreteRequestId: 'conc123',
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateHistoryKey(mockReq);
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('history', expect.any(Object));
      });
    });

    describe('generateSingleNDRKey', () => {
      it('should generate key for delivery NDR', () => {
        mockReq.params = { 
          DeliveryRequestId: 'del123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateSingleNDRKey(mockReq, 'delivery');
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('delivery_single_ndr', expect.any(Object));
      });

      it('should generate key for crane NDR', () => {
        mockReq.params = { 
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateSingleNDRKey(mockReq, 'crane');
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('crane_single_ndr', expect.any(Object));
      });
    });

    describe('generateNDRListKey', () => {
      it('should generate key for NDR list', () => {
        mockReq.params = { 
          ProjectId: 'proj123',
          pageSize: 10,
          pageNo: 1,
          void: false,
          ParentCompanyId: 'comp123'
        };
        mockReq.body = { filter: 'test' };
        
        const result = cacheMiddleware.generateNDRListKey(mockReq, 'delivery');
        expect(result).toBeDefined();
        expect(cacheService.generateCacheKey).toHaveBeenCalledWith('delivery_ndr_list', expect.any(Object));
      });
    });
  });

  describe('Invalidation Functions', () => {
    beforeEach(() => {
      cacheService.invalidateProjectCache = jest.fn().mockResolvedValue();
      cacheService.invalidateEventCache = jest.fn().mockResolvedValue();
      cacheService.delPattern = jest.fn().mockResolvedValue();
    });

    describe('invalidateProjectCache', () => {
      it('should invalidate project cache', async () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateProjectCache(mockReq);
        
        expect(cacheService.invalidateProjectCache).toHaveBeenCalledWith('proj123', 'comp123');
      });
    });

    describe('invalidateEventCache', () => {
      it('should invalidate event cache', async () => {
        mockReq.params = { id: 'event123', ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateEventCache(mockReq);
        
        expect(cacheService.invalidateEventCache).toHaveBeenCalledWith('event123', 'proj123', 'comp123');
      });
    });

    describe('invalidateCompanyCache', () => {
      it('should invalidate company cache', async () => {
        mockReq.params = { companyId: 'comp123' };
        
        await cacheMiddleware.invalidateCompanyCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('account_projects:*companyId*comp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('projects_company:*');
      });
    });

    describe('invalidateMemberCache', () => {
      it('should invalidate member cache with ProjectId', async () => {
        mockReq.params = { ProjectId: 'proj123' };
        
        await cacheMiddleware.invalidateMemberCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('member_list_paginated:*ProjectId*proj123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('member_list_all:*ProjectId*proj123*');
      });

      it('should invalidate member cache with ParentCompanyId', async () => {
        mockReq.params = { ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateMemberCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('member_list_paginated:*ParentCompanyId*comp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('member_list_all:*ParentCompanyId*comp123*');
      });
    });

    describe('invalidateGateCache', () => {
      it('should invalidate gate cache', async () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateGateCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('gate_list:*ProjectId*proj123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('gate_list:*ParentCompanyId*comp123*');
      });
    });

    describe('invalidateEquipmentCache', () => {
      it('should invalidate equipment cache', async () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateEquipmentCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('equipment_list:*ProjectId*proj123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('equipment_list:*ParentCompanyId*comp123*');
      });
    });

    describe('invalidateAllCalendarCache', () => {
      it('should invalidate all calendar cache', async () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateAllCalendarCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('all_calendar:*ProjectId*proj123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('all_calendar:*ParentCompanyId*comp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('all_calendar:*');
      });
    });

    describe('invalidateAttachmentsCache', () => {
      it('should invalidate attachments cache', async () => {
        mockReq.params = { 
          DeliveryRequestId: 'del123',
          InspectionRequestId: 'insp123',
          ConcreteRequestId: 'conc123',
          CraneRequestId: 'crane123'
        };
        
        await cacheMiddleware.invalidateAttachmentsCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('attachments:*DeliveryRequestId*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('attachments:*InspectionRequestId*insp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('attachments:*ConcreteRequestId*conc123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('attachments:*CraneRequestId*crane123*');
      });
    });

    describe('invalidateCommentsCache', () => {
      it('should invalidate comments cache', async () => {
        mockReq.params = { DeliveryRequestId: 'del123' };
        
        await cacheMiddleware.invalidateCommentsCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('comments:*DeliveryRequestId*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('comments:*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('comments:*');
      });
    });

    describe('invalidateHistoryCache', () => {
      it('should invalidate history cache', async () => {
        mockReq.params = { DeliveryRequestId: 'del123' };
        
        await cacheMiddleware.invalidateHistoryCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('history:*DeliveryRequestId*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('history:*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('history:*');
      });
    });

    describe('invalidateSingleNDRCache', () => {
      it('should invalidate single NDR cache', async () => {
        mockReq.params = { 
          DeliveryRequestId: 'del123',
          inspectionRequestId: 'insp123',
          ConcreteRequestId: 'conc123',
          CraneRequestId: 'crane123'
        };
        
        await cacheMiddleware.invalidateSingleNDRCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*DeliveryRequestId*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*inspectionRequestId*insp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*ConcreteRequestId*conc123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*CraneRequestId*crane123*');
      });
    });

    describe('invalidateCraneRequestCache', () => {
      it('should invalidate crane request cache', async () => {
        mockReq.params = { CraneRequestId: 'crane123' };
        
        await cacheMiddleware.invalidateCraneRequestCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('crane_attachments:*CraneRequestId*crane123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('crane_comments:*CraneRequestId*crane123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('crane_history:*CraneRequestId*crane123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*CraneRequestId*crane123*');
      });
    });

    describe('invalidateConcreteRequestCache', () => {
      it('should invalidate concrete request cache', async () => {
        mockReq.params = { ConcreteRequestId: 'conc123' };
        
        await cacheMiddleware.invalidateConcreteRequestCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('concrete_attachments:*ConcreteRequestId*conc123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('concrete_comments:*ConcreteRequestId*conc123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('concrete_history:*ConcreteRequestId*conc123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*ConcreteRequestId*conc123*');
      });
    });

    describe('invalidateInspectionHistoryCache', () => {
      it('should invalidate inspection history cache', async () => {
        mockReq.params = { InspectionRequestId: 'insp123' };
        
        await cacheMiddleware.invalidateInspectionHistoryCache(mockReq);
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('inspection_history:*InspectionRequestId*insp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('single_ndr:*InspectionRequestId*insp123*');
      });
    });
  });

  describe('Convenience Methods', () => {
    describe('cacheCalendarEvents', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCalendarEvents();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheCalendarEvent', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCalendarEvent();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheProjectsCompany', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheProjectsCompany();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheAccountProjects', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheAccountProjects();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheMemberList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheMemberList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheAllMemberList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheAllMemberList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheGateList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheGateList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheEquipmentList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheEquipmentList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheAllCalendar', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheAllCalendar();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheAttachments', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheAttachments();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheComments', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheComments();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheHistory', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheHistory();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheSingleNDR', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheSingleNDR();
        expect(typeof middleware).toBe('function');
      });
    });
  });

  describe('Invalidation Convenience Methods', () => {
    describe('invalidateAfterAddEvent', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterAddEvent();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterUpdateEvent', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterUpdateEvent();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterDeleteEvent', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterDeleteEvent();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterProjectMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterProjectMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterEventMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterEventMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterCompanyMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterCompanyMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterMemberMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterMemberMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterGateMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterGateMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterEquipmentMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterEquipmentMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterAllCalendarMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterAllCalendarMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterAttachmentMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterAttachmentMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterCommentMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterCommentMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterHistoryMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterHistoryMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterSingleNDRMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterSingleNDRMutation();
        expect(typeof middleware).toBe('function');
      });
    });
  });

  describe('Specialized Key Generation Methods', () => {
    describe('generateCraneRequestAttachmentsKey', () => {
      it('should generate key for crane request attachments', () => {
        mockReq.params = { 
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateCraneRequestAttachmentsKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('crane_attachments:');
      });
    });

    describe('generateCraneRequestCommentsKey', () => {
      it('should generate key for crane request comments', () => {
        mockReq.params = { 
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateCraneRequestCommentsKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('crane_comments:');
      });
    });

    describe('generateCraneRequestHistoryKey', () => {
      it('should generate key for crane request history', () => {
        mockReq.params = { 
          CraneRequestId: 'crane123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateCraneRequestHistoryKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('crane_history:');
      });
    });

    describe('generateConcreteRequestAttachmentsKey', () => {
      it('should generate key for concrete request attachments', () => {
        mockReq.params = { 
          ConcreteRequestId: 'conc123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateConcreteRequestAttachmentsKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('concrete_attachments:');
      });
    });

    describe('generateConcreteRequestCommentsKey', () => {
      it('should generate key for concrete request comments', () => {
        mockReq.params = { 
          ConcreteRequestId: 'conc123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateConcreteRequestCommentsKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('concrete_comments:');
      });
    });

    describe('generateConcreteRequestHistoryKey', () => {
      it('should generate key for concrete request history', () => {
        mockReq.params = { 
          ConcreteRequestId: 'conc123',
          ParentCompanyId: 'comp123',
          ProjectId: 'proj123'
        };
        
        const result = cacheMiddleware.generateConcreteRequestHistoryKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('concrete_history:');
      });
    });

    describe('generateInspectionHistoryKey', () => {
      it('should generate key for inspection history', () => {
        mockReq.params = { 
          InspectionRequestId: 'insp123',
          ParentCompanyId: 'comp123'
        };
        
        const result = cacheMiddleware.generateInspectionHistoryKey(mockReq);
        expect(result).toBeDefined();
        expect(result).toContain('inspection_history:');
      });
    });
  });

  describe('Specialized Convenience Methods', () => {
    describe('cacheCraneRequestAttachments', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCraneRequestAttachments();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheCraneRequestComments', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCraneRequestComments();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheCraneRequestHistory', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCraneRequestHistory();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterCraneRequestMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterCraneRequestMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheConcreteRequestAttachments', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheConcreteRequestAttachments();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheConcreteRequestComments', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheConcreteRequestComments();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheConcreteRequestHistory', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheConcreteRequestHistory();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterConcreteRequestMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterConcreteRequestMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheInspectionHistory', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheInspectionHistory();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterInspectionMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterInspectionMutation();
        expect(typeof middleware).toBe('function');
      });
    });
  });

  describe('NDR List Methods', () => {
    describe('cacheNDRList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheNDRList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterNDRListMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterNDRListMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheDeliveryNDRList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheDeliveryNDRList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheCraneNDRList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCraneNDRList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheConcreteNDRList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheConcreteNDRList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheInspectionNDRList', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheInspectionNDRList();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateNDRListCacheByType', () => {
      it('should invalidate NDR list cache by type', async () => {
        mockReq.params = { ProjectId: 'proj123', ParentCompanyId: 'comp123' };
        
        await cacheMiddleware.invalidateNDRListCacheByType(mockReq, 'delivery');
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('delivery_ndr_list:*ProjectId*proj123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('delivery_ndr_list:*ParentCompanyId*comp123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('delivery_ndr_list:*');
      });
    });

    describe('invalidateAfterDeliveryNDRListMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterDeliveryNDRListMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterCraneNDRListMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterCraneNDRListMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterConcreteNDRListMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterConcreteNDRListMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterInspectionNDRListMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterInspectionNDRListMutation();
        expect(typeof middleware).toBe('function');
      });
    });
  });

  describe('Single NDR Methods', () => {
    describe('cacheDeliverySingleNDR', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheDeliverySingleNDR();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheCraneSingleNDR', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheCraneSingleNDR();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheConcreteSingleNDR', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheConcreteSingleNDR();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('cacheInspectionSingleNDR', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.cacheInspectionSingleNDR();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateSingleNDRCacheByType', () => {
      it('should invalidate single NDR cache by type', async () => {
        mockReq.params = { DeliveryRequestId: 'del123' };
        
        await cacheMiddleware.invalidateSingleNDRCacheByType(mockReq, 'delivery');
        
        expect(cacheService.delPattern).toHaveBeenCalledWith('delivery_single_ndr:*DeliveryRequestId*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('delivery_single_ndr:*del123*');
        expect(cacheService.delPattern).toHaveBeenCalledWith('delivery_single_ndr:*');
      });
    });

    describe('invalidateAfterDeliverySingleNDRMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterDeliverySingleNDRMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterCraneSingleNDRMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterCraneSingleNDRMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterConcreteSingleNDRMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterConcreteSingleNDRMutation();
        expect(typeof middleware).toBe('function');
      });
    });

    describe('invalidateAfterInspectionSingleNDRMutation', () => {
      it('should return middleware with correct configuration', () => {
        const middleware = cacheMiddleware.invalidateAfterInspectionSingleNDRMutation();
        expect(typeof middleware).toBe('function');
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing user object', () => {
      mockReq.user = null;
      
      const result = cacheMiddleware.generateProjectsCompanyKey(mockReq);
      expect(result).toBeDefined();
    });

    it('should handle empty parameters', () => {
      mockReq.params = {};
      mockReq.query = {};
      mockReq.body = {};
      
      const result = cacheMiddleware.generateCalendarEventsKey(mockReq);
      expect(result).toBeDefined();
    });

    it('should handle cache service errors in invalidation', async () => {
      cacheService.delPattern.mockRejectedValue(new Error('Cache error'));
      mockReq.params = { ProjectId: 'proj123' };
      
      await expect(cacheMiddleware.invalidateGateCache(mockReq)).rejects.toThrow('Cache error');
    });

    it('should handle missing parameters in invalidation', async () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      await expect(cacheMiddleware.invalidateProjectCache(mockReq)).resolves.not.toThrow();
    });

    it('should handle missing ProjectId in invalidation functions', async () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      await expect(cacheMiddleware.invalidateMemberCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateGateCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateEquipmentCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateAllCalendarCache(mockReq)).resolves.not.toThrow();
    });

    it('should handle missing ParentCompanyId in invalidation functions', async () => {
      mockReq.params = { ProjectId: 'proj123' };
      mockReq.body = {};
      mockReq.query = {};
      
      await expect(cacheMiddleware.invalidateMemberCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateGateCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateEquipmentCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateAllCalendarCache(mockReq)).resolves.not.toThrow();
    });

    it('should handle missing request IDs in attachment invalidation', async () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      await expect(cacheMiddleware.invalidateAttachmentsCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateCommentsCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateHistoryCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateSingleNDRCache(mockReq)).resolves.not.toThrow();
    });

    it('should handle missing request IDs in specialized invalidation', async () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      await expect(cacheMiddleware.invalidateCraneRequestCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateConcreteRequestCache(mockReq)).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateInspectionHistoryCache(mockReq)).resolves.not.toThrow();
    });

    it('should handle missing parameters in NDR list invalidation', async () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      await expect(cacheMiddleware.invalidateNDRListCacheByType(mockReq, 'delivery')).resolves.not.toThrow();
      await expect(cacheMiddleware.invalidateSingleNDRCacheByType(mockReq, 'delivery')).resolves.not.toThrow();
    });

    it('should handle missing user in specialized key generation', () => {
      mockReq.user = null;
      mockReq.params = { 
        CraneRequestId: 'crane123',
        ParentCompanyId: 'comp123',
        ProjectId: 'proj123'
      };
      
      const result = cacheMiddleware.generateCraneRequestAttachmentsKey(mockReq);
      expect(result).toBeDefined();
      expect(result).toContain('crane_attachments:');
    });

    it('should handle missing user in inspection key generation', () => {
      mockReq.user = null;
      mockReq.params = { 
        InspectionRequestId: 'insp123',
        ParentCompanyId: 'comp123'
      };
      
      const result = cacheMiddleware.generateInspectionHistoryKey(mockReq);
      expect(result).toBeDefined();
      expect(result).toContain('inspection_history:');
    });

    it('should handle missing user in concrete key generation', () => {
      mockReq.user = null;
      mockReq.params = { 
        ConcreteRequestId: 'conc123',
        ParentCompanyId: 'comp123',
        ProjectId: 'proj123'
      };
      
      const result = cacheMiddleware.generateConcreteRequestAttachmentsKey(mockReq);
      expect(result).toBeDefined();
      expect(result).toContain('concrete_attachments:');
    });

    it('should handle missing parameters in key generation with fallbacks', () => {
      mockReq.params = {};
      mockReq.query = {};
      mockReq.body = {};
      
      // Test key generation functions that use fallback logic
      const result1 = cacheMiddleware.generateCalendarEventsKey(mockReq);
      const result2 = cacheMiddleware.generateCalendarEventKey(mockReq);
      const result3 = cacheMiddleware.generateAttachmentsKey(mockReq);
      const result4 = cacheMiddleware.generateCommentsKey(mockReq);
      const result5 = cacheMiddleware.generateHistoryKey(mockReq);
      const result6 = cacheMiddleware.generateSingleNDRKey(mockReq, 'delivery');
      
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      expect(result3).toBeDefined();
      expect(result4).toBeDefined();
      expect(result5).toBeDefined();
      expect(result6).toBeDefined();
    });

    it('should handle missing parameters in NDR list key generation', () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      const result = cacheMiddleware.generateNDRListKey(mockReq, 'delivery');
      expect(result).toBeDefined();
    });

    it('should handle missing parameters in single NDR key generation', () => {
      mockReq.params = {};
      mockReq.body = {};
      mockReq.query = {};
      
      const result = cacheMiddleware.generateSingleNDRKey(mockReq, 'delivery');
      expect(result).toBeDefined();
    });

    it('should handle missing parameters in account projects key generation', () => {
      mockReq.params = {};
      mockReq.query = {};
      mockReq.body = {};
      
      const result = cacheMiddleware.generateAccountProjectsKey(mockReq);
      expect(result).toBeDefined();
    });

    it('should handle missing parameters in member list key generation', () => {
      mockReq.params = {};
      mockReq.query = {};
      mockReq.body = {};
      
      const result1 = cacheMiddleware.generateMemberListKey(mockReq);
      const result2 = cacheMiddleware.generateAllMemberListKey(mockReq);
      
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
    });

    it('should handle missing parameters in gate and equipment key generation', () => {
      mockReq.params = {};
      mockReq.query = {};
      mockReq.body = {};
      
      const result1 = cacheMiddleware.generateGateListKey(mockReq);
      const result2 = cacheMiddleware.generateEquipmentListKey(mockReq);
      
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
    });

    it('should handle missing parameters in all calendar key generation', () => {
      mockReq.params = {};
      mockReq.query = {};
      mockReq.body = {};
      
      const result = cacheMiddleware.generateAllCalendarKey(mockReq);
      expect(result).toBeDefined();
    });
  });
});