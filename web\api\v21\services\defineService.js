const fs = require('fs');
const status = require('http-status');
const xlstojson = require('xls-to-json-lc');
const csv = require('csv-parser');
const ExcelJS = require('exceljs');
const {
  Sequelize,
  Enterprise,
  DeliverDefine,
  CraneRequestDefinableFeatureOfWork,
  CompanyDefine,
  Company,
  DeliveryRequest,
  CraneRequest,
  DeliverHistory,
  CraneRequestHistory,
} = require('../models');
let { DeliverDefineWork, Member } = require('../models');
const helper = require('../helpers/domainHelper');
const ApiError = require('../helpers/apiError');

let publicUser;
let publicMember;

const { Op } = Sequelize;

const defineService = {
  async getDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const { ProjectId } = params;
      const reqData = inputData.body;
      const offset = (+params.pageNo - 1) * +params.pageSize;
      const limit = +params.pageSize;
      const sortByColumnType = params.sortByField || 'DFOW';

      const condition = reqData.search
        ? Sequelize.and({ isDeleted: false, ProjectId, DFOW: { [Sequelize.Op.iLike]: `%${reqData.search}%` } })
        : Sequelize.and({ isDeleted: false, ProjectId });

      const defineWork = await DeliverDefineWork.findAndCountAll({
        where: condition,
        order: [[sortByColumnType, params.sort]],
        limit,
        offset,
      });

      if (sortByColumnType === 'DFOW') {
        defineWork.rows.sort((a, b) => (a.DFOW.toLowerCase() > b.DFOW.toLowerCase() ? 1 : -1));
      }

      done(defineWork, false);
    } catch (e) {
      done(null, e);
    }
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }

    if (!domainName && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      const userData = email ? await publicUser.findOne({ where: { email } }) : null;
      let enterpriseQuery = { ParentCompanyId, status: 'completed' };
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData?.isAccount) {
          enterpriseQuery = { id: memberData.EnterpriseId, status: 'completed' };
        }
      }
      const enterpriseValue = await Enterprise.findOne({ where: enterpriseQuery });
      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliverDefineWork = modelObj.DeliverDefineWork;
    Member = modelObj.Member;
    return true;
  },

  async exportFile(defineWork) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'User';
    workbook.lastModifiedBy = 'User';
    workbook.views = [{
      x: 0, y: 0, width: 10000, height: 20000, firstSheet: 0, activeTab: 1, visibility: 'visible',
    }];
    const worksheet = workbook.addWorksheet('DFOW');
    worksheet.columns = [
      { header: 'id', key: 'id', width: 5 },
      { header: 'DFOW', key: 'DFOW', width: 32 },
      { header: 'Specification', key: 'Specification', width: 32 },
    ];
    defineWork.forEach(data => worksheet.addRow({
      id: data.autoId, DFOW: data.DFOW, Specification: data.Specification,
    }));
    return workbook;
  },

  async updateDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { editData } = inputData.body;
      const { params } = inputData;
      const existData = [];
      const addData = [];
      
      const processElement = async (element, i) => {
        const condition = element.id !== undefined && element.id !== null;
        let exist = null;
              
        if (condition) {
          exist = await DeliverDefineWork.findOne({ where: Sequelize.and({ id: element.id }) });
        }
              
        if (exist) {
          await this.processExistingElement(exist, element, editData, existData, params);
        } else {
          addData.push({ dfow: element.DFOW, specification: element.Specification || '' });
        }
         
        if (editData.length - 1 === i) {
          this.finalizeUpdate(editData, addData, existData, inputData, done);
        }
      };
      
      await Promise.all(editData.map(processElement));
    } catch (e) {
      done(null, e);
    }
  },

  async processExistingElement(exist, element, editData, existData, params) {
    const existDFOW = editData.filter(
      e => e.id !== element.id && e.DFOW === element.DFOW && e.Specification === element.Specification
    );
  
    if (existDFOW.length === 0) {
      const newExist = await DeliverDefineWork.findAll({
        where: Sequelize.and(
          { id: { [Op.not]: element.id } },
          { DFOW: element.DFOW },
          { Specification: element.Specification },
          { ProjectId: params.ProjectId },
        ),
      });
      if (newExist.length === 0) {
        await DeliverDefineWork.update(
          { DFOW: element.DFOW, Specification: element.Specification },
          { where: { id: element.id } },
        );
      } else {
        this.collectExistData(newExist, existData);
      }
    } else {
      this.collectExistData(existDFOW, existData);
    }
  },

  collectExistData(collection, existData) {
    collection.forEach(newElement => {
      if (!existData.includes(newElement)) {
        existData.push(newElement);
      }
    });
  },

  finalizeUpdate(editData, addData, existData, inputData, done) {
    if (addData.length > 0) {
      this.insertDefine(addData, inputData, (response, error) => {
        if (error) {
          done(null, error);
        } else if (existData.length > 0) {
          done(null, { message: 'Duplication not allowed', data: existData });
        } else {
          done({ message: 'Updated Successfully.' }, false);
        }
      });
    } else if (existData.length > 0) {
      done(null, { message: 'Duplication not allowed', data: existData });
    } else {
      done({ message: 'Updated Successfully.' }, false);
    }
  },

  async addDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const dfowData = inputData.body;
      const newInput = inputData;
      const { addData } = dfowData;
      const existDFOW = await DeliverDefineWork.findOne({
        where: Sequelize.and({ ProjectId: dfowData.ProjectId }, { DFOW: addData[0].DFOW }),
      });
      if (!existDFOW) {
        newInput.params.ProjectId = dfowData.ProjectId;
        addData[0].dfow = addData[0].DFOW;
        this.insertDefine(addData, newInput, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done({ message: 'Created Successfully.' }, false);
          }
        });
      } else {
        done(null, { message: 'DFOW Already exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async deleteDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { deleteData } = inputData.body;
      const reqData = inputData.body;
      const condition = { ProjectId: reqData.ProjectId, isDeleted: false };
      if (!reqData.isSelectAll) {
        condition.id = { [Op.in]: deleteData };
      }
      const getDfow = await DeliverDefineWork.findAll({ where: condition });
      if (getDfow && getDfow.length > 0) {
        for (let i = 0; i < getDfow.length; i++) {
          const item = getDfow[i];
          await this.deleteDFOW(item, reqData.ProjectId, done);
          if (i === getDfow.length - 1) {
            done('success', false);
          }
        }
      }
    } catch (e) {
      done(null, e);
    }
  },

  async deleteDFOW(item, ProjectId, done) {
    const isDfowMappedToDeliveryRequest = await this.isDfowMapped(DeliverDefine, item.id, ProjectId);
    const isDfowMappedToCraneRequest = await this.isDfowMapped(CraneRequestDefinableFeatureOfWork, item.id, ProjectId);
    const isDfowMappedToCompany = await this.isDfowMapped(CompanyDefine, item.id, ProjectId);

    if (isDfowMappedToDeliveryRequest) {
      return done(null, {
        message: `Definable Feature of Work ID ${item.autoId} cannot be deleted. Mapped to a delivery booking`,
      });
    }
    if (isDfowMappedToCraneRequest) {
      return done(null, {
        message: `Definable Feature of Work ID ${item.autoId} cannot be deleted. Mapped to a crane booking`,
      });
    }
    if (isDfowMappedToCompany) {
      return done(null, {
        message: `Definable Feature of Work ID ${item.autoId} cannot be deleted. Mapped to a company`,
      });
    }
    await DeliverDefineWork.update(
      { isDeleted: true },
      { where: { id: item.id, ProjectId, isDeleted: false } },
    );
  },

  async isDfowMapped(model, id, ProjectId) {
    return await model.findOne({
      where: { DeliverDefineWorkId: id, isDeleted: false, ProjectId },
    });
  },

  async createDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { file } = inputData;
      const ProjectId = +inputData.params.ProjectId;
      const memberDetail = await Member.findOne({ where: this.getMemberCondition(inputData, ProjectId) });

      if (memberDetail) {
        if (file?.originalname) {
          const extension = file.originalname.split('.').pop();
          await this.processFileByExtension(extension, file, inputData, done);
        } else {
          done(null, { message: 'Please select a file.' });
        }
      } else {
        done(null, { message: 'Project Does not exist or you are not a valid member.' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  getMemberCondition(inputData, ProjectId) {
    return [
      Sequelize.and(
        { UserId: inputData.user.id, ProjectId, isDeleted: false },
        Sequelize.or({ RoleId: [1, 2, 3] }),
      ),
    ];
  },

  async processFileByExtension(extension, file, inputData, done) {
    if (extension === 'xlsx') {
      await this.processXLSX(file, inputData, done);
    } else if (extension === 'csv') {
      this.processCSV(file, inputData, done);
    } else if (extension === 'xls') {
      this.processXLS(file, inputData, done);
    } else {
      done(null, { message: 'Please choose valid file' });
    }
  },

  async processXLSX(file, inputData, done) {
    const newWorkbook = new ExcelJS.Workbook();
    await newWorkbook.xlsx.readFile(file.path);
    const worksheet = newWorkbook.getWorksheet('data');
    const dfowArray = worksheet.getSheetValues().slice(2).map((singleRowData) => ({
      dfow: singleRowData[3],
      id: singleRowData[1].toString(),
      specification: singleRowData[2],
    }));
    this.insertDefine(dfowArray, inputData, (resValue, error) => {
      if (!error) {
        done(resValue, false);
      } else {
        done(null, error);
      }
    });
  },

  processCSV(file, inputData, done) {
    const result = [];
    const stream = fs.createReadStream(file.path).pipe(csv()).on('data', (row) => {
      stream.pause();
      result.push({ id: row.id, dfow: row.DFOW, specification: row.Specification });
      stream.resume();
    }).on('end', () => {
      this.insertDefine(result, inputData, (resValue, error) => {
        if (!error) {
          done(resValue, false);
        } else {
          done(null, error);
        }
      });
    });
  },

  processXLS(file, inputData, done) {
    xlstojson({ input: file.path, output: null, lowerCaseHeaders: true }, (err, result) => {
      if (err) {
        done(null, err);
      } else {
        this.insertDefine(result, inputData, (resValue, error) => {
          if (!error) {
            done(resValue, false);
          } else {
            done(null, error);
          }
        });
      }
    });
  },

  async insertDefine(result, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const ProjectId = +inputData.params.ProjectId;
      if (this.checkFileFormat(result, inputData) && result.length > 0) {
        const lastIdValue = await DeliverDefineWork.findOne({
          where: { ProjectId, isDeleted: false },
          order: [['autoId', 'DESC']],
        });
        let id = lastIdValue ? lastIdValue.autoId : 0;
        for (let i = 0; i < result.length; i++) {
          const element = result[i];
          if (element.dfow) {
            const findDFOWByAttributes = this.getDFOWAttributes(ProjectId, element);
            const existValue = await DeliverDefineWork.findOne({
              where: { [Op.and]: findDFOWByAttributes },
            });
            await this.processInsertElement(existValue, inputData, element, id++);
          }
          if (result.length - 1 === i) {
            done({ message: 'created' }, false);
          }
        }
      } else {
        done(null, { message: 'Invalid File Format' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  checkFileFormat(result, inputData) {
    if (inputData.file) {
      fs.unlinkSync(inputData.file.path);
      const keys = ['id', 'dfow', 'specification'];
      const inputKeys = Object.keys(result[0]);
      const keyArray = inputKeys.filter(e => keys.includes(e));
      return inputKeys.length >= 2 && keyArray.length >= 2;
    }
    return true;
  },

  async processInsertElement(existValue, inputData, element, id) {
    const ProjectId = +inputData.params.ProjectId;
    const data = { DFOW: element.dfow, Specification: element.specification, autoId: id, ProjectId, isDeleted: false };
    if (!existValue) {
      await DeliverDefineWork.createInstance(data);
    } else if (existValue.isDeleted) {
      await DeliverDefineWork.update({ autoId: id, isDeleted: false }, { where: { id: existValue.id } });
    } else {
      await this.handleDuplicationWhenInsert(element, existValue, [result, existValue]);
    }
  },

  async handleDuplicationWhenInsert(element, existValue, duplicationRecords) {
    if (result.length === 1) {
      done(null, { message: 'Duplication not allowed', data: duplicationRecords });
    }
  },

  getDFOWAttributes(ProjectId, element) {
    const attributes = [{ ProjectId }, { DFOW: element.dfow }];
    if (element.specification) {
      attributes.push({ Specification: element.specification.toString() });
    }
    return attributes;
  },

  async lastDefineId(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const lastData = await DeliverDefineWork.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['autoId', 'DESC']],
      });
      done({ id: lastData ? lastData.autoId + 1 : 1 }, false);
    } catch (e) {
      done(null, e);
    }
  },
};

module.exports = defineService;