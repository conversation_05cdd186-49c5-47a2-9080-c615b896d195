const cacheService = require('../utils/cacheService');
const { CACHE_TTL } = require('../config/cache');

/**
 * Enhanced middleware to handle cache operations gracefully
 * If cache is down, the API should still work by falling back to database
 */
const cacheMiddleware = {
  /**
   * Middleware to check cache before processing request and cache response after
   * @param {Object} options - Configuration options
   * @param {Function} options.keyGenerator - Function to generate cache key from request
   * @param {number} options.ttl - Time to live in seconds
   * @param {string} options.logPrefix - Prefix for log messages
   */
  cacheGetRequests: (options = {}) => {
    const { keyGenerator, ttl = CACHE_TTL.DEFAULT, logPrefix = 'Cache' } = options;

    return async (req, res, next) => {
      try {
        // Generate cache key based on request
        const cacheKey = keyGenerator ? keyGenerator(req) : null;

        if (!cacheKey) {
          return next();
        }

        // Try to get data from cache
        const cachedData = cacheService.get(cacheKey);

        if (cachedData) {
          console.log(`${logPrefix}: Cache hit for key: ${cacheKey.substring(0, 50)}...`);
          return res.status(200).json(cachedData);
        }

        console.log(`${logPrefix}: Cache miss for key: ${cacheKey.substring(0, 50)}...`);

        // Store original json method
        const originalJson = res.json;

        // Override json method to cache successful responses
        res.json = async function (data) {
          try {
            // Only cache successful responses (2xx status codes)
            if (res.statusCode >= 200 && res.statusCode < 300 && data) {
              cacheService.set(cacheKey, data, ttl);
              console.log(`${logPrefix}: Response cached successfully`);
            }
          } catch (error) {
            console.error(`${logPrefix}: Cache set error:`, error);
            // Continue without caching if cache is down
          }

          // Call original json method
          return originalJson.call(this, data);
        };

        next();
      } catch (error) {
        console.error(`${logPrefix}: Cache middleware error:`, error);
        // Continue without cache if cache is down
        next();
      }
    };
  },

  /**
   * Middleware to invalidate cache after mutations (POST, PUT, DELETE)
   * @param {Object} options - Configuration options
   * @param {Function} options.invalidationFunction - Function to handle cache invalidation
   * @param {string} options.logPrefix - Prefix for log messages
   */
  invalidateCacheAfterMutation: (options = {}) => {
    const { invalidationFunction, logPrefix = 'Cache' } = options;

    return async (req, res, next) => {
      // Store original json method
      const originalJson = res.json;

      // Override json method to invalidate cache after successful response
      res.json = async function (data) {
        try {
          // Only invalidate cache for successful responses (2xx status codes)
          if (res.statusCode >= 200 && res.statusCode < 300 && invalidationFunction) {
            await invalidationFunction(req);
            console.log(`${logPrefix}: Cache invalidated successfully after ${req.method} operation`);
          }
        } catch (error) {
          console.error(`${logPrefix}: Cache invalidation error:`, error);
          // Continue without cache invalidation if cache is down
        }

        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    };
  },

  // ===== KEY GENERATION FUNCTIONS =====

  /**
   * Generate cache key for calendar events list
   */
  generateCalendarEventsKey: (req) => {
    const cacheParams = {
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      search: req.query.search || '',
      isApplicableToDelivery: req.query.isApplicableToDelivery || false,
      isApplicableToCrane: req.query.isApplicableToCrane || false,
      isApplicableToConcrete: req.query.isApplicableToConcrete || false,
      isApplicableToInspection: req.query.isApplicableToInspection || false,
      weeklyReportTest: req.query.weeklyReportTest || '',
      start: req.query.start || '',
      end: req.query.end || '',
      startDate: req.query.startDate || '',
      endDate: req.query.endDate || '',
      eventStartTime: req.query.eventStartTime || '',
      eventEndTime: req.query.eventEndTime || '',
      timezone: req.query.timezone || '',
      UserId: req.user ? req.user.id : '',
    };
    return cacheService.generateCacheKey('calendar_events', cacheParams);
  },

  /**
   * Generate cache key for single calendar event
   */
  generateCalendarEventKey: (req) => {
    const cacheParams = {
      id: req.params.id,
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      UserId: req.user ? req.user.id : '',
    };
    return cacheService.generateCacheKey('calendar_event', cacheParams);
  },

  /**
   * Generate cache key for projects by company
   */
  generateProjectsCompanyKey: (req) => {
    const cacheParams = {
      UserId: req.user ? req.user.id : '',
    };
    return cacheService.generateCacheKey('projects_company', cacheParams);
  },

  /**
   * Generate cache key for account projects
   */
  generateAccountProjectsKey: (req) => {
    const cacheParams = {
      companyId: req.query.companyId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('account_projects', cacheParams);
  },

  /**
   * Generate cache key for member list (paginated with lastId)
   */
  generateMemberListKey: (req) => {
    const cacheParams = {
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      pageSize: req.params.pageSize,
      pageNo: req.params.pageNo,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('member_list_paginated', cacheParams);
  },

  /**
   * Generate cache key for all member list (without pagination)
   */
  generateAllMemberListKey: (req) => {
    const cacheParams = {
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('member_list_all', cacheParams);
  },

  /**
   * Generate cache key for gate list
   */
  generateGateListKey: (req) => {
    const cacheParams = {
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('gate_list', cacheParams);
  },

  /**
   * Generate cache key for equipment list
   */
  generateEquipmentListKey: (req) => {
    const cacheParams = {
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('equipment_list', cacheParams);
  },

  /**
   * Generate cache key for all calendar
   */
  generateAllCalendarKey: (req) => {
    const cacheParams = {
      ProjectId: req.params.ProjectId || req.query.ProjectId,
      ParentCompanyId: req.params.ParentCompanyId || req.query.ParentCompanyId,
      UserId: req.user ? req.user.id : '',
      ...req.body,
    };
    return cacheService.generateCacheKey('all_calendar', cacheParams);
  },

  /**
   * Generate cache key for attachments
   */
  generateAttachmentsKey: (req) => {
    const cacheParams = {
      DeliveryRequestId: req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id,
      InspectionRequestId: req.params.InspectionRequestId || req.body.InspectionRequestId,
      ConcreteRequestId: req.params.ConcreteRequestId || req.body.ConcreteRequestId,
      CraneRequestId: req.params.CraneRequestId || req.body.CraneRequestId,
      ParentCompanyId: req.params.ParentCompanyId || req.body.ParentCompanyId,
      ProjectId: req.params.ProjectId || req.body.ProjectId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('attachments', cacheParams);
  },

  /**
   * Generate cache key for comments
   */
  generateCommentsKey: (req) => {
    const cacheParams = {
      DeliveryRequestId: req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id,
      InspectionRequestId: req.params.InspectionRequestId || req.body.InspectionRequestId,
      ConcreteRequestId: req.params.ConcreteRequestId || req.body.ConcreteRequestId,
      CraneRequestId: req.params.CraneRequestId || req.body.CraneRequestId,
      ParentCompanyId: req.params.ParentCompanyId || req.body.ParentCompanyId,
      ProjectId: req.params.ProjectId || req.body.ProjectId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('comments', cacheParams);
  },

  /**
   * Generate cache key for history
   */
  generateHistoryKey: (req) => {
    const cacheParams = {
      DeliveryRequestId: req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id,
      InspectionRequestId: req.params.InspectionRequestId || req.body.InspectionRequestId,
      ConcreteRequestId: req.params.ConcreteRequestId || req.body.ConcreteRequestId,
      CraneRequestId: req.params.CraneRequestId || req.body.CraneRequestId,
      ParentCompanyId: req.params.ParentCompanyId || req.body.ParentCompanyId,
      ProjectId: req.params.ProjectId || req.body.ProjectId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey('history', cacheParams);
  },

  /**
   * Generate cache key for single NDR (delivery/inspection/crane/concrete)
   * @param {Request} req
   * @param {string} type - One of 'delivery', 'crane', 'concrete', 'inspection'
   */
  generateSingleNDRKey: (req, type = 'delivery') => {
    const cacheParams = {
      DeliveryRequestId: req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id,
      inspectionRequestId: req.params.inspectionRequestId || req.body.inspectionRequestId || req.body.id,
      ConcreteRequestId: req.params.ConcreteRequestId || req.body.ConcreteRequestId || req.body.id,
      CraneRequestId: req.params.CraneRequestId || req.body.CraneRequestId || req.body.id,
      ParentCompanyId: req.params.ParentCompanyId || req.body.ParentCompanyId,
      ProjectId: req.params.ProjectId || req.body.ProjectId,
      UserId: req.user ? req.user.id : '',
      ...req.query,
    };
    return cacheService.generateCacheKey(`${type}_single_ndr`, cacheParams);
  },

  /**
   * Generate cache key for NDR list (delivery requests)
   */
  generateNDRListKey: (req, type = 'delivery') => {
    const cacheParams = {
      ProjectId: req.params.ProjectId,
      pageSize: req.params.pageSize,
      pageNo: req.params.pageNo,
      void: req.params.void,
      ParentCompanyId: req.params.ParentCompanyId,
      UserId: req.user ? req.user.id : '',
      ...req.body, // Include request body for filtering parameters
    };
    return cacheService.generateCacheKey(`${type}_ndr_list`, cacheParams);
  },

  // ===== INVALIDATION FUNCTIONS =====

  /**
   * Invalidate project cache
   */
  invalidateProjectCache: async (req) => {
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;
    await cacheService.invalidateProjectCache(ProjectId, ParentCompanyId);
  },

  /**
   * Invalidate event cache
   */
  invalidateEventCache: async (req) => {
    const { id } = req.params;
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;
    await cacheService.invalidateEventCache(id, ProjectId, ParentCompanyId);
  },

  /**
   * Invalidate company cache
   */
  invalidateCompanyCache: async (req) => {
    const { companyId } = req.params;
    await cacheService.delPattern(`account_projects:*companyId*${companyId}*`);
    await cacheService.delPattern('projects_company:*');
  },

  /**
   * Invalidate member cache
   */
  invalidateMemberCache: async (req) => {
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;

    if (ProjectId) {
      // Invalidate both paginated and all member list caches
      await cacheService.delPattern(`member_list_paginated:*ProjectId*${ProjectId}*`);
      await cacheService.delPattern(`member_list_all:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      // Invalidate both paginated and all member list caches
      await cacheService.delPattern(`member_list_paginated:*ParentCompanyId*${ParentCompanyId}*`);
      await cacheService.delPattern(`member_list_all:*ParentCompanyId*${ParentCompanyId}*`);
    }
  },

  /**
   * Invalidate gate cache
   */
  invalidateGateCache: async (req) => {
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;

    if (ProjectId) {
      await cacheService.delPattern(`gate_list:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      await cacheService.delPattern(`gate_list:*ParentCompanyId*${ParentCompanyId}*`);
    }
  },

  /**
   * Invalidate equipment cache
   */
  invalidateEquipmentCache: async (req) => {
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;

    if (ProjectId) {
      await cacheService.delPattern(`equipment_list:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      await cacheService.delPattern(`equipment_list:*ParentCompanyId*${ParentCompanyId}*`);
    }
  },

  /**
   * Invalidate all calendar cache
   */
  invalidateAllCalendarCache: async (req) => {
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;

    if (ProjectId) {
      await cacheService.delPattern(`all_calendar:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      await cacheService.delPattern(`all_calendar:*ParentCompanyId*${ParentCompanyId}*`);
    }
    await cacheService.delPattern('all_calendar:*');
  },


  /**
   * Invalidate attachments cache for a request
   */
  invalidateAttachmentsCache: async (req) => {
    const DeliveryRequestId = req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id;
    const InspectionRequestId = req.params.InspectionRequestId || req.body.InspectionRequestId || req.body.id;
    const ConcreteRequestId = req.params.ConcreteRequestId || req.body.ConcreteRequestId || req.body.id;
    const CraneRequestId = req.params.CraneRequestId || req.body.CraneRequestId || req.body.id;

    if (DeliveryRequestId) {
      await cacheService.delPattern(`attachments:*DeliveryRequestId*${DeliveryRequestId}*`);
    }
    if (InspectionRequestId) {
      await cacheService.delPattern(`attachments:*InspectionRequestId*${InspectionRequestId}*`);
    }
    if (ConcreteRequestId) {
      await cacheService.delPattern(`attachments:*ConcreteRequestId*${ConcreteRequestId}*`);
    }
    if (CraneRequestId) {
      await cacheService.delPattern(`attachments:*CraneRequestId*${CraneRequestId}*`);
    }
  },

  /**
 * Invalidate comments cache for a request
 */
  invalidateCommentsCache: async (req) => {
    const DeliveryRequestId = req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id;
    const InspectionRequestId = req.params.InspectionRequestId || req.body.InspectionRequestId || req.body.id;
    const ConcreteRequestId = req.params.ConcreteRequestId || req.body.ConcreteRequestId || req.body.id;
    const CraneRequestId = req.params.CraneRequestId || req.body.CraneRequestId || req.body.id;

    if (DeliveryRequestId) {
      const patterns = [
        `comments:*DeliveryRequestId*${DeliveryRequestId}*`,
        `comments:*${DeliveryRequestId}*`,
        `comments:*`
      ];

      for (const pattern of patterns) {
        await cacheService.delPattern(pattern);
      }
    }
    if (InspectionRequestId) {
      const pattern = `comments:*InspectionRequestId*${InspectionRequestId}*`;
      await cacheService.delPattern(pattern);
    }
    if (ConcreteRequestId) {
      const pattern = `comments:*ConcreteRequestId*${ConcreteRequestId}*`;
      await cacheService.delPattern(pattern);
    }
    if (CraneRequestId) {
      const pattern = `comments:*CraneRequestId*${CraneRequestId}*`;
      await cacheService.delPattern(pattern);
    }
  },

  /**
 * Invalidate history cache for a request
 */
  invalidateHistoryCache: async (req) => {
    const DeliveryRequestId = req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id;
    const InspectionRequestId = req.params.InspectionRequestId || req.body.InspectionRequestId || req.body.id;
    const ConcreteRequestId = req.params.ConcreteRequestId || req.body.ConcreteRequestId || req.body.id;
    const CraneRequestId = req.params.CraneRequestId || req.body.CraneRequestId || req.body.id;

    if (DeliveryRequestId) {
      const patterns = [
        `history:*DeliveryRequestId*${DeliveryRequestId}*`,
        `history:*${DeliveryRequestId}*`,
        `history:*`
      ];

      for (const pattern of patterns) {
        await cacheService.delPattern(pattern);
      }
    }
    if (InspectionRequestId) {
      const pattern = `history:*InspectionRequestId*${InspectionRequestId}*`;
      await cacheService.delPattern(pattern);
    }
    if (ConcreteRequestId) {
      const pattern = `history:*ConcreteRequestId*${ConcreteRequestId}*`;
      await cacheService.delPattern(pattern);
    }
    if (CraneRequestId) {
      const pattern = `history:*CraneRequestId*${CraneRequestId}*`;
      await cacheService.delPattern(pattern);
    }
  },

  /**
   * Invalidate single NDR cache for a request
   */
  invalidateSingleNDRCache: async (req) => {
    const DeliveryRequestId = req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id;
    const inspectionRequestId = req.params.inspectionRequestId || req.body.inspectionRequestId || req.body.id;
    const ConcreteRequestId = req.params.ConcreteRequestId || req.body.ConcreteRequestId || req.body.id;
    const CraneRequestId = req.params.CraneRequestId || req.body.CraneRequestId || req.body.id;

    if (DeliveryRequestId) {
      await cacheService.delPattern(`single_ndr:*DeliveryRequestId*${DeliveryRequestId}*`);
    }
    if (inspectionRequestId) {
      await cacheService.delPattern(`single_ndr:*inspectionRequestId*${inspectionRequestId}*`);
    }
    if (ConcreteRequestId) {
      await cacheService.delPattern(`single_ndr:*ConcreteRequestId*${ConcreteRequestId}*`);
    }
    if (CraneRequestId) {
      await cacheService.delPattern(`single_ndr:*CraneRequestId*${CraneRequestId}*`);
    }
  },

  /**
   * Invalidate crane request cache
   */
  invalidateCraneRequestCache: async (req) => {
    const CraneRequestId = req.params.CraneRequestId || req.body.CraneRequestId;

    if (CraneRequestId) {
      await cacheService.delPattern(`crane_attachments:*CraneRequestId*${CraneRequestId}*`);
      await cacheService.delPattern(`crane_comments:*CraneRequestId*${CraneRequestId}*`);
      await cacheService.delPattern(`crane_history:*CraneRequestId*${CraneRequestId}*`);
      await cacheService.delPattern(`single_ndr:*CraneRequestId*${CraneRequestId}*`);
    }
  },

  /**
   * Invalidate concrete request cache
   */
  invalidateConcreteRequestCache: async (req) => {
    const ConcreteRequestId = req.params.ConcreteRequestId || req.body.ConcreteRequestId;

    if (ConcreteRequestId) {
      await cacheService.delPattern(`concrete_attachments:*ConcreteRequestId*${ConcreteRequestId}*`);
      await cacheService.delPattern(`concrete_comments:*ConcreteRequestId*${ConcreteRequestId}*`);
      await cacheService.delPattern(`concrete_history:*ConcreteRequestId*${ConcreteRequestId}*`);
      await cacheService.delPattern(`single_ndr:*ConcreteRequestId*${ConcreteRequestId}*`);
    }
  },

  /**
   * Invalidate inspection history cache
   */
  invalidateInspectionHistoryCache: async (req) => {
    const InspectionRequestId = req.params.InspectionRequestId || req.body.InspectionRequestId;

    if (InspectionRequestId) {
      await cacheService.delPattern(`inspection_history:*InspectionRequestId*${InspectionRequestId}*`);
      await cacheService.delPattern(`single_ndr:*InspectionRequestId*${InspectionRequestId}*`);
    }
  },

  // ===== CONVENIENCE METHODS =====

  /**
   * Pre-configured middleware for calendar events list caching
   */
  cacheCalendarEvents: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateCalendarEventsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CalendarEvents'
    });
  },

  /**
   * Pre-configured middleware for single calendar event caching
   */
  cacheCalendarEvent: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateCalendarEventKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CalendarEvent'
    });
  },

  /**
   * Pre-configured middleware for projects company caching
   */
  cacheProjectsCompany: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateProjectsCompanyKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'ProjectsCompany'
    });
  },

  /**
   * Pre-configured middleware for account projects caching
   */
  cacheAccountProjects: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateAccountProjectsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'AccountProjects'
    });
  },

  /**
   * Pre-configured middleware for member list caching (paginated with lastId)
   */
  cacheMemberList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateMemberListKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'MemberListPaginated'
    });
  },

  /**
   * Pre-configured middleware for all member list caching (without pagination)
   */
  cacheAllMemberList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateAllMemberListKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'MemberListAll'
    });
  },

  /**
   * Pre-configured middleware for gate list caching
   */
  cacheGateList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateGateListKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'GateList'
    });
  },

  /**
   * Pre-configured middleware for equipment list caching
   */
  cacheEquipmentList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateEquipmentListKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'EquipmentList'
    });
  },

  /**
   * Pre-configured middleware for all calendar caching
   */
  cacheAllCalendar: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateAllCalendarKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'AllCalendar'
    });
  },

  /**
   * Pre-configured middleware for attachments caching
   */
  cacheAttachments: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateAttachmentsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'Attachments'
    });
  },

  /**
   * Pre-configured middleware for comments caching
   */
  cacheComments: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateCommentsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'Comments'
    });
  },

  /**
   * Pre-configured middleware for history caching
   */
  cacheHistory: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateHistoryKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'History'
    });
  },

  /**
   * Pre-configured middleware for single NDR caching
   */
  cacheSingleNDR: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateSingleNDRKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'SingleNDR'
    });
  },

  // ===== INVALIDATION CONVENIENCE METHODS =====

  /**
   * Pre-configured middleware for invalidating cache after adding events
   */
  invalidateAfterAddEvent: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateAllCalendarCache,
      logPrefix: 'AddEvent'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after updating events
   */
  invalidateAfterUpdateEvent: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateAllCalendarCache,
      logPrefix: 'UpdateEvent'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after deleting events
   */
  invalidateAfterDeleteEvent: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateAllCalendarCache,
      logPrefix: 'DeleteEvent'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after project mutations
   */
  invalidateAfterProjectMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateProjectCache,
      logPrefix: 'ProjectMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after event mutations
   */
  invalidateAfterEventMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateEventCache,
      logPrefix: 'EventMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after company mutations
   */
  invalidateAfterCompanyMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateCompanyCache,
      logPrefix: 'CompanyMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after member mutations
   */
  invalidateAfterMemberMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateMemberCache,
      logPrefix: 'MemberMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after gate mutations
   */
  invalidateAfterGateMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateGateCache,
      logPrefix: 'GateMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after equipment mutations
   */
  invalidateAfterEquipmentMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateEquipmentCache,
      logPrefix: 'EquipmentMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after all calendar mutations
   */
  invalidateAfterAllCalendarMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateAllCalendarCache,
      logPrefix: 'AllCalendarMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after attachment mutations
   */
  invalidateAfterAttachmentMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateAttachmentsCache,
      logPrefix: 'AttachmentMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after comment mutations
   */
  invalidateAfterCommentMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateCommentsCache,
      logPrefix: 'CommentMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after history mutations
   */
  invalidateAfterHistoryMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateHistoryCache,
      logPrefix: 'HistoryMutation'
    });
  },

  // ===== KEY GENERATION METHODS FOR SPECIFIC ENTITIES =====

  /**
   * Generate cache key for crane request attachments
   */
  generateCraneRequestAttachmentsKey: (req) => {
    const { CraneRequestId, ParentCompanyId, ProjectId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      CraneRequestId,
      ParentCompanyId,
      ProjectId,
      userId
    };

    return `crane_attachments:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Generate cache key for crane request comments
   */
  generateCraneRequestCommentsKey: (req) => {
    const { CraneRequestId, ParentCompanyId, ProjectId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      CraneRequestId,
      ParentCompanyId,
      ProjectId,
      userId
    };

    return `crane_comments:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Generate cache key for crane request history
   */
  generateCraneRequestHistoryKey: (req) => {
    const { CraneRequestId, ParentCompanyId, ProjectId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      CraneRequestId,
      ParentCompanyId,
      ProjectId,
      userId
    };

    return `crane_history:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Generate cache key for concrete request attachments
   */
  generateConcreteRequestAttachmentsKey: (req) => {
    const { ConcreteRequestId, ParentCompanyId, ProjectId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      ConcreteRequestId,
      ParentCompanyId,
      ProjectId,
      userId
    };

    return `concrete_attachments:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Generate cache key for concrete request comments
   */
  generateConcreteRequestCommentsKey: (req) => {
    const { ConcreteRequestId, ParentCompanyId, ProjectId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      ConcreteRequestId,
      ParentCompanyId,
      ProjectId,
      userId
    };

    return `concrete_comments:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Generate cache key for concrete request history
   */
  generateConcreteRequestHistoryKey: (req) => {
    const { ConcreteRequestId, ParentCompanyId, ProjectId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      ConcreteRequestId,
      ParentCompanyId,
      ProjectId,
      userId
    };

    return `concrete_history:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Generate cache key for inspection history
   */
  generateInspectionHistoryKey: (req) => {
    const { InspectionRequestId, ParentCompanyId } = req.params;
    const { user } = req;
    const userId = user?.id || 'anonymous';

    const keyData = {
      InspectionRequestId,
      ParentCompanyId,
      userId
    };

    return `inspection_history:${cacheService.generateHash(keyData)}`;
  },

  /**
   * Pre-configured middleware for invalidating cache after single NDR mutations
   */
  invalidateAfterSingleNDRMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateSingleNDRCache,
      logPrefix: 'SingleNDRMutation'
    });
  },

  // ===== CRANE REQUEST-RELATED CONVENIENCE METHODS =====

  /**
   * Pre-configured middleware for caching crane request attachments
   */
  cacheCraneRequestAttachments: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateCraneRequestAttachmentsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CraneAttachments'
    });
  },

  /**
   * Pre-configured middleware for caching crane request comments
   */
  cacheCraneRequestComments: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateCraneRequestCommentsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CraneComments'
    });
  },

  /**
   * Pre-configured middleware for caching crane request history
   */
  cacheCraneRequestHistory: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateCraneRequestHistoryKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CraneHistory'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after crane request mutations
   */
  invalidateAfterCraneRequestMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateCraneRequestCache,
      logPrefix: 'CraneRequestMutation'
    });
  },

  // ===== CONCRETE REQUEST-RELATED CONVENIENCE METHODS =====

  /**
   * Pre-configured middleware for caching concrete request attachments
   */
  cacheConcreteRequestAttachments: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateConcreteRequestAttachmentsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'ConcreteAttachments'
    });
  },

  /**
   * Pre-configured middleware for caching concrete request comments
   */
  cacheConcreteRequestComments: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateConcreteRequestCommentsKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'ConcreteComments'
    });
  },

  /**
   * Pre-configured middleware for caching concrete request history
   */
  cacheConcreteRequestHistory: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateConcreteRequestHistoryKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'ConcreteHistory'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after concrete request mutations
   */
  invalidateAfterConcreteRequestMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateConcreteRequestCache,
      logPrefix: 'ConcreteRequestMutation'
    });
  },

  // ===== INSPECTION-RELATED CONVENIENCE METHODS =====

  /**
   * Pre-configured middleware for caching inspection history
   */
  cacheInspectionHistory: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateInspectionHistoryKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'InspectionHistory'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after inspection mutations
   */
  invalidateAfterInspectionMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateInspectionHistoryCache,
      logPrefix: 'InspectionMutation'
    });
  },

  // ===== NDR LIST-RELATED CONVENIENCE METHODS =====

  /**
   * Pre-configured middleware for caching NDR list
   */
  cacheNDRList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: cacheMiddleware.generateNDRListKey,
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'NDRList'
    });
  },

  /**
   * Pre-configured middleware for invalidating cache after NDR list mutations
   */
  invalidateAfterNDRListMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: cacheMiddleware.invalidateNDRListCache,
      logPrefix: 'NDRListMutation'
    });
  },

  /**
   * Pre-configured middleware for caching Delivery NDR list
   */
  cacheDeliveryNDRList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateNDRListKey(req, 'delivery'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'DeliveryNDRList'
    });
  },

  /**
   * Pre-configured middleware for caching Crane NDR list
   */
  cacheCraneNDRList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateNDRListKey(req, 'crane'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CraneNDRList'
    });
  },

  /**
   * Pre-configured middleware for caching Concrete NDR list
   */
  cacheConcreteNDRList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateNDRListKey(req, 'concrete'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'ConcreteNDRList'
    });
  },

  /**
   * Pre-configured middleware for caching Inspection NDR list
   */
  cacheInspectionNDRList: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateNDRListKey(req, 'inspection'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'InspectionNDRList'
    });
  },

  /**
   * Invalidate NDR list cache for a specific module
   * @param {Request} req
   * @param {string} type - One of 'delivery', 'crane', 'concrete', 'inspection'
   */
  invalidateNDRListCacheByType: async (req, type = 'delivery') => {
    const ProjectId = req.params.ProjectId || req.body.ProjectId || req.query.ProjectId;
    const ParentCompanyId = req.params.ParentCompanyId || req.body.ParentCompanyId || req.query.ParentCompanyId;
    if (ProjectId) {
      await cacheService.delPattern(`${type}_ndr_list:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      await cacheService.delPattern(`${type}_ndr_list:*ParentCompanyId*${ParentCompanyId}*`);
    }
    await cacheService.delPattern(`${type}_ndr_list:*`);
  },

  /**
   * Pre-configured middleware for invalidating Delivery NDR list cache
   */
  invalidateAfterDeliveryNDRListMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateNDRListCacheByType(req, 'delivery'),
      logPrefix: 'DeliveryNDRListMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating Crane NDR list cache
   */
  invalidateAfterCraneNDRListMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateNDRListCacheByType(req, 'crane'),
      logPrefix: 'CraneNDRListMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating Concrete NDR list cache
   */
  invalidateAfterConcreteNDRListMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateNDRListCacheByType(req, 'concrete'),
      logPrefix: 'ConcreteNDRListMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating Inspection NDR list cache
   */
  invalidateAfterInspectionNDRListMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateNDRListCacheByType(req, 'inspection'),
      logPrefix: 'InspectionNDRListMutation'
    });
  },

  /**
   * Pre-configured middleware for caching Delivery single NDR
   */
  cacheDeliverySingleNDR: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateSingleNDRKey(req, 'delivery'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'DeliverySingleNDR'
    });
  },

  /**
   * Pre-configured middleware for caching Crane single NDR
   */
  cacheCraneSingleNDR: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateSingleNDRKey(req, 'crane'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'CraneSingleNDR'
    });
  },

  /**
   * Pre-configured middleware for caching Concrete single NDR
   */
  cacheConcreteSingleNDR: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateSingleNDRKey(req, 'concrete'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'ConcreteSingleNDR'
    });
  },

  /**
   * Pre-configured middleware for caching Inspection single NDR
   */
  cacheInspectionSingleNDR: () => {
    return cacheMiddleware.cacheGetRequests({
      keyGenerator: (req) => cacheMiddleware.generateSingleNDRKey(req, 'inspection'),
      ttl: CACHE_TTL.DEFAULT,
      logPrefix: 'InspectionSingleNDR'
    });
  },

  /**
 * Invalidate single NDR cache for a specific module
 * @param {Request} req
 * @param {string} type - One of 'delivery', 'crane', 'concrete', 'inspection'
 */
  invalidateSingleNDRCacheByType: async (req, type = 'delivery') => {
    const DeliveryRequestId = req.params.DeliveryRequestId || req.body.DeliveryRequestId || req.body.id;
    const inspectionRequestId = req.params.inspectionRequestId || req.body.inspectionRequestId;
    const ConcreteRequestId = req.params.ConcreteRequestId || req.body.ConcreteRequestId;
    const CraneRequestId = req.params.CraneRequestId || req.body.CraneRequestId;

    if (DeliveryRequestId) {
      // Try multiple patterns to ensure we catch the cache key
      const patterns = [
        `${type}_single_ndr:*DeliveryRequestId*${DeliveryRequestId}*`,
        `${type}_single_ndr:*${DeliveryRequestId}*`,
        `${type}_single_ndr:*`
      ];

      for (const pattern of patterns) {
        await cacheService.delPattern(pattern);
      }
    }
    if (inspectionRequestId) {
      const pattern = `${type}_single_ndr:*inspectionRequestId*${inspectionRequestId}*`;
      await cacheService.delPattern(pattern);
    }
    if (ConcreteRequestId) {
      const pattern = `${type}_single_ndr:*ConcreteRequestId*${ConcreteRequestId}*`;
      await cacheService.delPattern(pattern);
    }
    if (CraneRequestId) {
      const pattern = `${type}_single_ndr:*CraneRequestId*${CraneRequestId}*`;
      await cacheService.delPattern(pattern);
    }
  },

  /**
   * Pre-configured middleware for invalidating Delivery single NDR cache
   */
  invalidateAfterDeliverySingleNDRMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateSingleNDRCacheByType(req, 'delivery'),
      logPrefix: 'DeliverySingleNDRMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating Crane single NDR cache
   */
  invalidateAfterCraneSingleNDRMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateSingleNDRCacheByType(req, 'crane'),
      logPrefix: 'CraneSingleNDRMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating Concrete single NDR cache
   */
  invalidateAfterConcreteSingleNDRMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateSingleNDRCacheByType(req, 'concrete'),
      logPrefix: 'ConcreteSingleNDRMutation'
    });
  },

  /**
   * Pre-configured middleware for invalidating Inspection single NDR cache
   */
  invalidateAfterInspectionSingleNDRMutation: () => {
    return cacheMiddleware.invalidateCacheAfterMutation({
      invalidationFunction: (req) => cacheMiddleware.invalidateSingleNDRCacheByType(req, 'inspection'),
      logPrefix: 'InspectionSingleNDRMutation'
    });
  },
};

module.exports = cacheMiddleware;
