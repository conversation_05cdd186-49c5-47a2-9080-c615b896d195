const DefineController = require('../DefineController');
const { defineService } = require('../../services');
const exportService = require('../../services/exportService');

// Mock dependencies
jest.mock('../../services', () => ({
  defineService: {
    createDefinable: jest.fn(),
    getDefinable: jest.fn(),
    updateDefinable: jest.fn(),
    deleteDefinable: jest.fn(),
    addDefinable: jest.fn(),
    lastDefineId: jest.fn(),
  },
}));

jest.mock('../../services/exportService', () => ({
  exportSampleDocument: jest.fn(),
}));

describe('DefineController', () => {
  let mockReq, mockRes, mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
      end: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createDefinable', () => {
    it('should create definable successfully', async () => {
      const mockResponse = { id: 1, data: 'test' };
      defineService.createDefinable.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DefineController.createDefinable(mockReq, mockRes, mockNext);

      expect(defineService.createDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable Feature of Work Created Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from definable creation', async () => {
      const mockError = new Error('Service error');
      defineService.createDefinable.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.createDefinable(mockReq, mockRes, mockNext);

      expect(defineService.createDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in definable creation', async () => {
      const mockError = new Error('Exception error');
      defineService.createDefinable.mockRejectedValue(mockError);

      await DefineController.createDefinable(mockReq, mockRes, mockNext);

      expect(defineService.createDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getDefinable', () => {
    it('should get definable successfully', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockLastDetail = { id: 1 };
      
      defineService.getDefinable.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });
      
      defineService.lastDefineId.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await DefineController.getDefinable(mockReq, mockRes, mockNext);

      expect(defineService.getDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(defineService.lastDefineId).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable Feature of Work Listed Successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from definable retrieval', async () => {
      const mockError = new Error('Service error');
      defineService.getDefinable.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.getDefinable(mockReq, mockRes, mockNext);

      expect(defineService.getDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from last define id', async () => {
      const mockResponse = [{ id: 1, data: 'test' }];
      const mockError = new Error('Last ID error');
      
      defineService.getDefinable.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });
      
      defineService.lastDefineId.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.getDefinable(mockReq, mockRes, mockNext);

      expect(defineService.getDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(defineService.lastDefineId).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in definable retrieval', async () => {
      const mockError = new Error('Exception error');
      defineService.getDefinable.mockRejectedValue(mockError);

      await DefineController.getDefinable(mockReq, mockRes, mockNext);

      expect(defineService.getDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateDefinable', () => {
    it('should update definable successfully', async () => {
      const mockResponse = { id: 1, data: 'updated' };
      defineService.updateDefinable.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DefineController.updateDefinable(mockReq, mockRes, mockNext);

      expect(defineService.updateDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable Feature of Work Updated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from definable update', async () => {
      const mockError = new Error('Service error');
      defineService.updateDefinable.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.updateDefinable(mockReq, mockRes, mockNext);

      expect(defineService.updateDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(mockError);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in definable update', async () => {
      const mockError = new Error('Exception error');
      defineService.updateDefinable.mockRejectedValue(mockError);

      await DefineController.updateDefinable(mockReq, mockRes, mockNext);

      expect(defineService.updateDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('exportDefinable', () => {
    it('should export definable successfully', async () => {
      const mockWorkbook = {
        xlsx: {
          write: jest.fn().mockResolvedValue(),
        },
      };
      
      defineService.getDefinable.mockImplementation((req, callback) => {
        callback(mockWorkbook, null);
      });

      await DefineController.exportDefinable(mockReq, mockRes, mockNext);

      expect(mockReq.params.export).toBe(1);
      expect(defineService.getDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/excel');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=Definable Feature of Work.xlsx');
      expect(mockWorkbook.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from definable export', async () => {
      const mockError = new Error('Service error');
      defineService.getDefinable.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.exportDefinable(mockReq, mockRes, mockNext);

      expect(mockReq.params.export).toBe(1);
      expect(defineService.getDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.setHeader).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
  });

  describe('deleteDefinable', () => {
    it('should delete definable successfully', async () => {
      const mockResponse = { id: 1, deleted: true };
      defineService.deleteDefinable.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DefineController.deleteDefinable(mockReq, mockRes, mockNext);

      expect(defineService.deleteDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable Feature of deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from definable deletion', async () => {
      const mockError = new Error('Service error');
      defineService.deleteDefinable.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.deleteDefinable(mockReq, mockRes, mockNext);

      expect(defineService.deleteDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in definable deletion', async () => {
      const mockError = new Error('Exception error');
      defineService.deleteDefinable.mockRejectedValue(mockError);

      await DefineController.deleteDefinable(mockReq, mockRes, mockNext);

      expect(defineService.deleteDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('addDefinable', () => {
    it('should add definable successfully', async () => {
      const mockResponse = { id: 1, data: 'added' };
      defineService.addDefinable.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DefineController.addDefinable(mockReq, mockRes, mockNext);

      expect(defineService.addDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Definable Feature of Work Added Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from definable addition', async () => {
      const mockError = new Error('Service error');
      defineService.addDefinable.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DefineController.addDefinable(mockReq, mockRes, mockNext);

      expect(defineService.addDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in definable addition', async () => {
      const mockError = new Error('Exception error');
      defineService.addDefinable.mockRejectedValue(mockError);

      await DefineController.addDefinable(mockReq, mockRes, mockNext);

      expect(defineService.addDefinable).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('sampleExcelDownload', () => {
    it('should download sample excel successfully', async () => {
      const mockWorkbook = {
        xlsx: {
          write: jest.fn().mockResolvedValue(),
        },
      };
      exportService.exportSampleDocument.mockResolvedValue(mockWorkbook);

      await DefineController.sampleExcelDownload(mockReq, mockRes, mockNext);

      expect(exportService.exportSampleDocument).toHaveBeenCalled();
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/excel');
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        'attachment; filename=Definable feature of work sample document.xlsx',
      );
      expect(mockWorkbook.xlsx.write).toHaveBeenCalledWith(mockRes);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle null workbook from export service', async () => {
      exportService.exportSampleDocument.mockResolvedValue(null);

      await DefineController.sampleExcelDownload(mockReq, mockRes, mockNext);

      expect(exportService.exportSampleDocument).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'cannot export document', status: 422 });
      expect(mockRes.setHeader).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle exception in sample excel download', async () => {
      const mockError = new Error('Exception error');
      exportService.exportSampleDocument.mockRejectedValue(mockError);

      await DefineController.sampleExcelDownload(mockReq, mockRes, mockNext);

      expect(exportService.exportSampleDocument).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
}); 