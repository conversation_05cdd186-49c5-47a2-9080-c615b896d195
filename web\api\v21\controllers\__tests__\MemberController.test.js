const status = require('http-status');

// Define mocks inside jest.mock factories to avoid out-of-scope errors
const mockMemberService = {};
jest.mock('../../services', () => ({ memberService: mockMemberService }));
const mockRole = { getAll: jest.fn() };
jest.mock('../../models', () => ({ Role: mockRole }));

const MemberController = require('../MemberController');

const mockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('MemberController', () => {
  let req, res, next;

  beforeEach(() => {
    req = { body: {}, query: {}, params: {} };
    res = mockRes();
    next = jest.fn();
    Object.keys(mockMemberService).forEach(k => delete mockMemberService[k]);
    mockRole.getAll.mockReset();
  });

  const testCallbackController = (method, serviceMethod, successResp, errorResp) => {
    it(`${method} - success`, async () => {
      mockMemberService[serviceMethod] = jest.fn((req, cb) => cb(successResp, null));
      await MemberController[method](req, res, next);
      expect(res.status).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalled();
      expect(next).not.toHaveBeenCalled();
    });
    it(`${method} - error callback`, async () => {
      mockMemberService[serviceMethod] = jest.fn((req, cb) => cb(null, 'error'));
      await MemberController[method](req, res, next);
      expect(next).toHaveBeenCalledWith('error');
    });
    it(`${method} - exception`, async () => {
      mockMemberService[serviceMethod] = jest.fn(() => { throw new Error('fail'); });
      await MemberController[method](req, res, next);
      expect(next).toHaveBeenCalled();
    });
  };

  testCallbackController('checkExistMember', 'checkExistMember', { ok: true }, { error: true });
  testCallbackController('inviteMembers', 'inviteMembers', { ok: true }, { error: true });
  testCallbackController('resendInviteLink', 'resendInviteLink', { ok: true }, { error: true });
  testCallbackController('getOverViewDetail', 'getOverViewDetail', { ok: true }, { error: true });
  testCallbackController('updateUserProfile', 'updateUserProfile', { ok: true }, { error: true });
  testCallbackController('editMember', 'editMemberDetail', { ok: true }, { error: true });
  testCallbackController('updateInviteMember', 'updateInviteMember', { ok: true }, { error: true });
  testCallbackController('deleteMember', 'deleteMember', { ok: true }, { error: true });
  testCallbackController('searchMember', 'searchMember', { ok: true }, { error: true });
  testCallbackController('searchAutoApproveMember', 'searchAutoApproveMember', { ok: true }, { error: true });
  testCallbackController('searchAllMember', 'searchAllMember', { ok: true }, { error: true });
  testCallbackController('getUserDetail', 'getUserDetail', { ok: true }, { error: true });
  testCallbackController('listAllMember', 'listAllMember', { ok: true }, { error: true });
  testCallbackController('deactivateMember', 'deactivateMember', { ok: true }, { error: true });
  testCallbackController('activateMember', 'activateMember', { ok: true }, { error: true });
  testCallbackController('getMappedRequests', 'getMappedRequests', { ok: true }, { error: true });
  testCallbackController('getOnboardingInviteLink', 'getOnboardingInviteLink', { ok: true }, { error: true });
  testCallbackController('getMemberData', 'getMemberData', { ok: true }, { error: true });
  testCallbackController('listRegisteredMembers', 'listRegisteredMembers', { ok: true }, { error: true });
  testCallbackController('listGuestMembers', 'listGuestMembers', { ok: true }, { error: true });

  describe('listMember', () => {
    it('success with lastDetail', async () => {
      mockMemberService.listMember = jest.fn((req, cb) => cb('resp', null));
      mockMemberService.lastMember = jest.fn((req, cb) => cb('last', null));
      await MemberController.listMember(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String), data: 'resp', lastId: 'last' });
    });
    it('lastMember error', async () => {
      mockMemberService.listMember = jest.fn((req, cb) => cb('resp', null));
      mockMemberService.lastMember = jest.fn((req, cb) => cb(null, 'err'));
      await MemberController.listMember(req, res, next);
      expect(next).toHaveBeenCalledWith('err');
    });
    it('listMember error', async () => {
      mockMemberService.listMember = jest.fn((req, cb) => cb(null, 'err'));
      await MemberController.listMember(req, res, next);
      expect(next).toHaveBeenCalledWith('err');
    });
    it('exception', async () => {
      mockMemberService.listMember = jest.fn(() => { throw new Error('fail'); });
      await MemberController.listMember(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('getRoles', () => {
    it('success', async () => {
      mockRole.getAll.mockResolvedValue([
        { roleName: 'B' },
        { roleName: 'a' },
      ]);
      await MemberController.getRoles(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'Role list.', data: [ { roleName: 'a' }, { roleName: 'B' } ] });
    });
    it('exception', async () => {
      mockRole.getAll.mockRejectedValue(new Error('fail'));
      await MemberController.getRoles(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('getAllMemberLists', () => {
    it('members found', async () => {
      mockMemberService.getAllMemberLists = jest.fn().mockResolvedValue({ memberLists: [1], count: 1 });
      await MemberController.getAllMemberLists(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ status: 200, message: expect.any(String), data: [1], count: 1 });
    });
    it('no members', async () => {
      mockMemberService.getAllMemberLists = jest.fn().mockResolvedValue(null);
      await MemberController.getAllMemberLists(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ status: 200, message: expect.any(String), data: [], count: 0 });
    });
    it('exception', async () => {
      mockMemberService.getAllMemberLists = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.getAllMemberLists(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('getMemberDetail', () => {
    it('detail found', async () => {
      mockMemberService.getMemberDetail = jest.fn().mockResolvedValue({ id: 1 });
      await MemberController.getMemberDetail(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String), data: { id: 1 } });
    });
    it('no detail', async () => {
      mockMemberService.getMemberDetail = jest.fn().mockResolvedValue(null);
      await MemberController.getMemberDetail(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String), data: {} });
    });
    it('exception', async () => {
      mockMemberService.getMemberDetail = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.getMemberDetail(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('getMemberProjects', () => {
    it('projects found', async () => {
      mockMemberService.getMemberProjects = jest.fn().mockResolvedValue([1, 2]);
      await MemberController.getMemberProjects(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String), data: [1, 2], count: 2 });
    });
    it('no projects', async () => {
      mockMemberService.getMemberProjects = jest.fn().mockResolvedValue(null);
      await MemberController.getMemberProjects(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String), data: [], count: 0 });
    });
    it('exception', async () => {
      mockMemberService.getMemberProjects = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.getMemberProjects(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('changeMemberPassword', () => {
    it('updated', async () => {
      mockMemberService.changeMemberPassword = jest.fn().mockResolvedValue(true);
      await MemberController.changeMemberPassword(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String) });
    });
    it('not updated', async () => {
      mockMemberService.changeMemberPassword = jest.fn().mockResolvedValue(false);
      await MemberController.changeMemberPassword(req, res, next);
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String) });
    });
    it('exception', async () => {
      mockMemberService.changeMemberPassword = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.changeMemberPassword(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('updateMemberProfile', () => {
    it('updated', async () => {
      mockMemberService.updateMemberProfile = jest.fn().mockResolvedValue({});
      await MemberController.updateMemberProfile(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ status: 200, message: expect.any(String) });
    });
    it('not updated', async () => {
      mockMemberService.updateMemberProfile = jest.fn().mockResolvedValue({ error: true, message: 'fail' });
      await MemberController.updateMemberProfile(req, res, next);
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({ status: 422, message: 'fail' });
    });
    it('not updated, no message', async () => {
      mockMemberService.updateMemberProfile = jest.fn().mockResolvedValue({ error: true });
      await MemberController.updateMemberProfile(req, res, next);
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({ status: 422, message: 'Update failed' });
    });
    it('exception', async () => {
      mockMemberService.updateMemberProfile = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.updateMemberProfile(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('updateMemberProjectStatus', () => {
    it('userStatus true, updated', async () => {
      req.body.userStatus = true;
      mockMemberService.updateMemberProjectStatus = jest.fn().mockResolvedValue(true);
      await MemberController.updateMemberProjectStatus(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ status: 200, message: expect.any(String) });
    });
    it('userStatus true, not updated', async () => {
      req.body.userStatus = true;
      mockMemberService.updateMemberProjectStatus = jest.fn().mockResolvedValue(false);
      await MemberController.updateMemberProjectStatus(req, res, next);
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({ status: 422, message: expect.any(String) });
    });
    it('userStatus false', async () => {
      req.body.userStatus = false;
      mockMemberService.updateMemberProjectStatus = jest.fn().mockResolvedValue(true);
      await MemberController.updateMemberProjectStatus(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ status: 200, message: expect.any(String) });
    });
    it('no userStatus', async () => {
      req.body = {};
      mockMemberService.updateMemberProjectStatus = jest.fn().mockResolvedValue(true);
      await MemberController.updateMemberProjectStatus(req, res, next);
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({ status: 422, message: expect.any(String) });
    });
    it('exception', async () => {
      mockMemberService.updateMemberProjectStatus = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.updateMemberProjectStatus(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('getAllMemberListsForAssignProject', () => {
    it('members found', async () => {
      mockMemberService.getAllMemberListsForAssignProject = jest.fn().mockResolvedValue([1, 2]);
      await MemberController.getAllMemberListsForAssignProject(req, res, next);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ status: 200, message: expect.any(String), data: [1, 2] });
    });
    it('no members', async () => {
      mockMemberService.getAllMemberListsForAssignProject = jest.fn().mockResolvedValue(null);
      await MemberController.getAllMemberListsForAssignProject(req, res, next);
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({ status: 422, message: expect.any(String) });
    });
    it('exception', async () => {
      mockMemberService.getAllMemberListsForAssignProject = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.getAllMemberListsForAssignProject(req, res, next);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('listRetoolMembers', () => {
    it('success', async () => {
      mockMemberService.listRetoolMembers = jest.fn().mockResolvedValue([1, 2]);
      await MemberController.listRetoolMembers(req, res);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: expect.any(String), data: [1, 2] });
    });
    it('exception', async () => {
      mockMemberService.listRetoolMembers = jest.fn().mockRejectedValue(new Error('fail'));
      await MemberController.listRetoolMembers(req, res);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ error: 'fail' });
    });
  });

  describe('addGuestAsMember', () => {
    it('success', async () => {
      mockMemberService.addGuestAsMember = jest.fn().mockResolvedValue({ id: 1 });
      await MemberController.addGuestAsMember(req, res);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ id: 1 });
    });
    it('exception', async () => {
      mockMemberService.addGuestAsMember = jest.fn().mockRejectedValue('fail');
      await MemberController.addGuestAsMember(req, res);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: 'Internal server error', error: 'fail' });
    });
  });

  describe('rejectGuestRequest', () => {
    it('success', async () => {
      mockMemberService.rejectGuestRequest = jest.fn().mockResolvedValue({ id: 1 });
      await MemberController.rejectGuestRequest(req, res);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ id: 1 });
    });
    it('exception', async () => {
      mockMemberService.rejectGuestRequest = jest.fn().mockRejectedValue('fail');
      await MemberController.rejectGuestRequest(req, res);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: 'Internal server error', error: 'fail' });
    });
  });
});
