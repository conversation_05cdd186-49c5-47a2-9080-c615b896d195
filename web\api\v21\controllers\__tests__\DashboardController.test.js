const DashboardController = require('../DashboardController');
const { dashboardService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  dashboardService: {
    getDashboardData: jest.fn(),
    getGraphDelivery: jest.fn(),
    getCraneGraphData: jest.fn(),
    getConcreteGraphData: jest.fn(),
    upcomingDelivery: jest.fn(),
    getReleasenoteVersion: jest.fn(),
    getInspectionGraphData: jest.fn(),
  },
}));

describe('DashboardController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {};
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('getDashboardData', () => {
    it('should get dashboard data successfully', async () => {
      const mockResponse = { data: 'dashboard data' };
      dashboardService.getDashboardData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getDashboardData(mockReq, mockRes, mockNext);

      expect(dashboardService.getDashboardData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard data Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from dashboard data', async () => {
      const mockError = new Error('Service error');
      dashboardService.getDashboardData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getDashboardData(mockReq, mockRes, mockNext);

      expect(dashboardService.getDashboardData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in dashboard data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getDashboardData.mockRejectedValue(mockError);

      await DashboardController.getDashboardData(mockReq, mockRes, mockNext);

      expect(dashboardService.getDashboardData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getPADashboardData', () => {
    it('should get PA dashboard data successfully', async () => {
      const mockResponse = { data: 'PA dashboard data' };
      dashboardService.getDashboardData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getPADashboardData(mockReq, mockRes, mockNext);

      expect(mockReq.PAadmin).toBe(true);
      expect(dashboardService.getDashboardData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard data Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from PA dashboard data', async () => {
      const mockError = new Error('Service error');
      dashboardService.getDashboardData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getPADashboardData(mockReq, mockRes, mockNext);

      expect(mockReq.PAadmin).toBe(true);
      expect(dashboardService.getDashboardData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in PA dashboard data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getDashboardData.mockRejectedValue(mockError);

      await DashboardController.getPADashboardData(mockReq, mockRes, mockNext);

      expect(mockReq.PAadmin).toBe(true);
      expect(dashboardService.getDashboardData).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getGraphDelivery', () => {
    it('should get graph delivery data successfully', async () => {
      const mockResponse = { data: 'graph delivery data' };
      dashboardService.getGraphDelivery.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getGraphDelivery(mockReq, mockRes, mockNext);

      expect(dashboardService.getGraphDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard count Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from graph delivery data', async () => {
      const mockError = new Error('Service error');
      dashboardService.getGraphDelivery.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getGraphDelivery(mockReq, mockRes, mockNext);

      expect(dashboardService.getGraphDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in graph delivery data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getGraphDelivery.mockRejectedValue(mockError);

      await DashboardController.getGraphDelivery(mockReq, mockRes, mockNext);

      expect(dashboardService.getGraphDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getCraneGraphData', () => {
    it('should get crane graph data successfully', async () => {
      const mockResponse = { data: 'crane graph data' };
      dashboardService.getCraneGraphData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getCraneGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getCraneGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard count Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from crane graph data', async () => {
      const mockError = new Error('Service error');
      dashboardService.getCraneGraphData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getCraneGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getCraneGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in crane graph data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getCraneGraphData.mockRejectedValue(mockError);

      await DashboardController.getCraneGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getCraneGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getConcreteGraphData', () => {
    it('should get concrete graph data successfully', async () => {
      const mockResponse = { data: 'concrete graph data' };
      dashboardService.getConcreteGraphData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getConcreteGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getConcreteGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard count Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from concrete graph data', async () => {
      const mockError = new Error('Service error');
      dashboardService.getConcreteGraphData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getConcreteGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getConcreteGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in concrete graph data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getConcreteGraphData.mockRejectedValue(mockError);

      await DashboardController.getConcreteGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getConcreteGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('upcomingDelivery', () => {
    it('should get upcoming delivery data successfully', async () => {
      const mockResponse = { data: 'upcoming delivery data' };
      dashboardService.upcomingDelivery.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.upcomingDelivery(mockReq, mockRes, mockNext);

      expect(dashboardService.upcomingDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from upcoming delivery data', async () => {
      const mockError = new Error('Service error');
      dashboardService.upcomingDelivery.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.upcomingDelivery(mockReq, mockRes, mockNext);

      expect(dashboardService.upcomingDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in upcoming delivery data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.upcomingDelivery.mockRejectedValue(mockError);

      await DashboardController.upcomingDelivery(mockReq, mockRes, mockNext);

      expect(dashboardService.upcomingDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getReleasenoteVersion', () => {
    it('should get release note version successfully', async () => {
      const mockResponse = { data: 'release note version' };
      dashboardService.getReleasenoteVersion.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getReleasenoteVersion(mockReq, mockRes, mockNext);

      expect(dashboardService.getReleasenoteVersion).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'release notes seen',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from release note version', async () => {
      const mockError = new Error('Service error');
      dashboardService.getReleasenoteVersion.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getReleasenoteVersion(mockReq, mockRes, mockNext);

      expect(dashboardService.getReleasenoteVersion).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in release note version', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getReleasenoteVersion.mockRejectedValue(mockError);

      await DashboardController.getReleasenoteVersion(mockReq, mockRes, mockNext);

      expect(dashboardService.getReleasenoteVersion).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getInspectionGraphData', () => {
    it('should get inspection graph data successfully', async () => {
      const mockResponse = { data: 'inspection graph data' };
      dashboardService.getInspectionGraphData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await DashboardController.getInspectionGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getInspectionGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Dashboard count Listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from inspection graph data', async () => {
      const mockError = new Error('Service error');
      dashboardService.getInspectionGraphData.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await DashboardController.getInspectionGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getInspectionGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in inspection graph data', async () => {
      const mockError = new Error('Exception error');
      dashboardService.getInspectionGraphData.mockRejectedValue(mockError);

      await DashboardController.getInspectionGraphData(mockReq, mockRes, mockNext);

      expect(dashboardService.getInspectionGraphData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
