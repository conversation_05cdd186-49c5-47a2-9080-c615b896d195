module.exports = (sequelize, DataTypes) => {
  const DeliverDefine = sequelize.define(
    'DeliverDefine',
    {
      DeliveryId: DataTypes.INTEGER,
      InspectionId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      InspectionCode: DataTypes.INTEGER,
      DeliveryCode: DataTypes.INTEGER,
      DeliverDefineWorkId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
    },
    {},
  );
  DeliverDefine.associate = (models) => {
    // associations can be defined here
    DeliverDefine.belongsTo(models.DeliveryRequest, {
      as: 'deliveryrequest',
      foreignKey: 'DeliveryId',
    });
    DeliverDefine.belongsTo(models.InspectionRequest, {
      as: 'InspectionRequest',
      foreignKey: 'InspectionId',
    });
    DeliverDefine.belongsTo(models.DeliverDefineWork, {
      as: 'dfow',
      foreignKey: 'DeliverDefineWorkId',
    });
    DeliverDefine.belongsTo(models.DeliverDefineWork);
  };
  DeliverDefine.createInstance = async (paramData) => {
    const newDeliverDefine = await DeliverDefine.create(paramData);
    return newDeliverDefine;
  };
  return DeliverDefine;
};
