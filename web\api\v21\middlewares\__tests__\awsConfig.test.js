const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Mock dependencies
jest.mock('aws-sdk');
jest.mock('uuid');

describe('awsConfig', () => {
  let mockS3Client;
  let mockUpload;
  let awsConfig;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock environment variables
    process.env.CLOUD_FRONT_URL = 'https://cloudfront.example.com/';
    process.env.REGION = 'us-east-1';
    process.env.BUCKET = 'test-bucket';

    // Mock UUID
    uuidv4.mockReturnValue('test-uuid-123');

    // Mock S3 client
    mockUpload = jest.fn();
    mockS3Client = {
      upload: mockUpload,
    };

    // Mock AWS.S3 constructor
    AWS.S3.mockImplementation(() => mockS3Client);

    // Mock AWS.config.getCredentials
    AWS.config.getCredentials = jest.fn((callback) => {
      callback(null);
    });

    // Re-require the module after setting up mocks
    jest.resetModules();
    awsConfig = require('../awsConfig');
  });

  describe('upload', () => {
    it('should upload multiple files successfully', async () => {
      const fileList = [
        {
          originalname: 'test1.jpg',
          buffer: Buffer.from('test1'),
        },
      ];

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
      };

      // Mock the upload method to return an object with promise method
      mockUpload.mockReturnValue({
        promise: jest.fn().mockResolvedValue(mockS3Response),
      });

      const done = jest.fn();

      await awsConfig.upload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
        Body: Buffer.from('test1'),
        ContentDisposition: 'attachment; filename=test1.jpg',
      });

      expect(done).toHaveBeenCalledWith(
        [
          {
            ...mockS3Response,
            Location: 'https://cloudfront.example.com/test-uuid-123.jpg',
          },
        ],
        false,
      );
    });

    it('should handle files with only name property', async () => {
      const fileList = [
        {
          name: 'test1.jpg',
          buffer: Buffer.from('test1'),
        },
      ];

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
      };

      mockUpload.mockReturnValue({
        promise: jest.fn().mockResolvedValue(mockS3Response),
      });

      const done = jest.fn();

      await awsConfig.upload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
        Body: Buffer.from('test1'),
        ContentDisposition: 'attachment; filename=test1.jpg',
      });
    });

    it('should handle upload errors', async () => {
      const fileList = [
        {
          originalname: 'test1.jpg',
          buffer: Buffer.from('test1'),
        },
      ];

      const uploadError = new Error('Upload failed');
      mockUpload.mockReturnValue({
        promise: jest.fn().mockRejectedValue(uploadError),
      });

      const done = jest.fn();

      await awsConfig.upload(fileList, done);

      expect(done).toHaveBeenCalledWith(null, uploadError);
    });

    it('should handle empty file list', async () => {
      const fileList = [];

      const done = jest.fn();

      await awsConfig.upload(fileList, done);

      expect(done).toHaveBeenCalledWith([], false);
    });
  });

  describe('singleUpload', () => {
    it('should upload single file with file property', async () => {
      const fileList = {
        file: {
          originalname: 'test.jpg',
          buffer: Buffer.from('test'),
        },
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.singleUpload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'test-uuid-123.jpg',
          Body: Buffer.from('test'),
          ContentDisposition: 'attachment; filename=test.jpg',
        },
        expect.any(Function),
      );

      expect(done).toHaveBeenCalledWith(
        [
          {
            ...mockS3Response,
            Location: 'https://cloudfront.example.com/test-uuid-123.jpg',
          },
        ],
        false,
      );
    });

    it('should upload single file with files property', async () => {
      const fileList = {
        files: {
          name: 'test.jpg',
          buffer: Buffer.from('test'),
        },
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.singleUpload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'test-uuid-123.jpg',
          Body: Buffer.from('test'),
          ContentDisposition: 'attachment; filename=test.jpg',
        },
        expect.any(Function),
      );
    });

    it('should handle upload error in singleUpload', async () => {
      const fileList = {
        file: {
          originalname: 'test.jpg',
          buffer: Buffer.from('test'),
        },
      };

      const uploadError = new Error('Upload failed');
      mockUpload.mockImplementation((params, callback) => {
        callback(uploadError, null);
      });

      const done = jest.fn();

      await awsConfig.singleUpload(fileList, done);

      expect(done).toHaveBeenCalledWith(null, uploadError);
    });
  });

  describe('carbonEmissionFileUpload', () => {
    it('should upload carbon emission file with file property', async () => {
      const fileList = {
        file: {
          originalname: 'emission.pdf',
          buffer: Buffer.from('emission data'),
        },
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/folloit-development/invoice-extracts/test-uuid-123.pdf',
        Bucket: 'folloit-development/invoice-extracts',
        Key: 'test-uuid-123.pdf',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.carbonEmissionFileUpload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'folloit-development/invoice-extracts',
          Key: 'test-uuid-123.pdf',
          Body: Buffer.from('emission data'),
          ContentDisposition: 'attachment; filename=emission.pdf',
        },
        expect.any(Function),
      );

      expect(done).toHaveBeenCalledWith(
        {
          ...mockS3Response,
          Location: 'https://cloudfront.example.com/test-uuid-123.pdf',
          S3URI: 's3://folloit-development/invoice-extracts/test-uuid-123.pdf',
        },
        false,
      );
    });

    it('should handle upload error in carbonEmissionFileUpload', async () => {
      const fileList = {
        file: {
          originalname: 'emission.pdf',
          buffer: Buffer.from('emission data'),
        },
      };

      const uploadError = new Error('Upload failed');
      mockUpload.mockImplementation((params, callback) => {
        callback(uploadError, null);
      });

      const done = jest.fn();

      await awsConfig.carbonEmissionFileUpload(fileList, done);

      expect(done).toHaveBeenCalledWith(null, uploadError);
    });
  });

  describe('reportUpload', () => {
    it('should upload report successfully', async () => {
      const buffer = Buffer.from('report data');
      const name = 'test-report';
      const extension = 'PDF';

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.pdf',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.pdf',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.reportUpload(buffer, name, extension, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'test-uuid-123.pdf',
          Body: buffer,
          ContentDisposition: 'attachment; filename=test-report.pdf',
        },
        expect.any(Function),
      );

      expect(done).toHaveBeenCalledWith('https://cloudfront.example.com/test-uuid-123.pdf', false);
    });

    it('should handle upload error in reportUpload', async () => {
      const buffer = Buffer.from('report data');
      const name = 'test-report';
      const extension = 'PDF';

      const uploadError = new Error('Upload failed');
      mockUpload.mockImplementation((params, callback) => {
        callback(uploadError, null);
      });

      const done = jest.fn();

      await awsConfig.reportUpload(buffer, name, extension, done);

      expect(done).toHaveBeenCalledWith(null, uploadError);
    });
  });

  describe('uploadToS3', () => {
    it('should upload to S3 successfully', async () => {
      const buffer = Buffer.from('schedule data');
      const name = 'schedule-report';
      const extension = 'PDF';

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/schedule_reports/schedule-report.pdf',
        Bucket: 'test-bucket',
        Key: 'schedule_reports/schedule-report.pdf',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const result = await awsConfig.uploadToS3(buffer, name, extension);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'schedule_reports/schedule-report.pdf',
          Body: buffer,
        },
        expect.any(Function),
      );

      expect(result).toBe('https://cloudfront.example.com/schedule_reports/schedule-report.pdf');
    });

    it('should handle upload error in uploadToS3', async () => {
      const buffer = Buffer.from('schedule data');
      const name = 'schedule-report';
      const extension = 'PDF';

      const uploadError = new Error('Upload failed');
      mockUpload.mockImplementation((params, callback) => {
        callback(uploadError, null);
      });

      await expect(awsConfig.uploadToS3(buffer, name, extension)).rejects.toThrow('Upload failed');
    });
  });

  describe('logisticPlanUpload', () => {
    it('should upload logistic plan with file property', async () => {
      const fileList = {
        file: {
          originalname: 'siteplan.jpg',
          buffer: Buffer.from('site plan data'),
        },
        params: {
          ProjectId: '12345',
        },
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/12345-siteplan-test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: '12345-siteplan-test-uuid-123.jpg',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.logisticPlanUpload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: '12345-siteplan-test-uuid-123.jpg',
          Body: Buffer.from('site plan data'),
          ContentDisposition: 'inline; filename=siteplan.jpg',
          ContentType: 'image/jpeg',
        },
        expect.any(Function),
      );

      expect(done).toHaveBeenCalledWith(
        [
          {
            ...mockS3Response,
            fileLink: 'https://cloudfront.example.com/12345-siteplan-test-uuid-123.jpg',
            fileName: 'siteplan.jpg',
          },
        ],
        false,
      );
    });

    it('should handle PDF content type', async () => {
      const fileList = {
        file: {
          originalname: 'siteplan.pdf',
          buffer: Buffer.from('site plan data'),
        },
        params: {
          ProjectId: '12345',
        },
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/12345-siteplan-test-uuid-123.pdf',
        Bucket: 'test-bucket',
        Key: '12345-siteplan-test-uuid-123.pdf',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.logisticPlanUpload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: '12345-siteplan-test-uuid-123.pdf',
          Body: Buffer.from('site plan data'),
          ContentDisposition: 'inline; filename=siteplan.pdf',
          ContentType: 'application/pdf',
        },
        expect.any(Function),
      );
    });

    it('should handle upload error in logisticPlanUpload', async () => {
      const fileList = {
        file: {
          originalname: 'siteplan.jpg',
          buffer: Buffer.from('site plan data'),
        },
        params: {
          ProjectId: '12345',
        },
      };

      const uploadError = new Error('Upload failed');
      mockUpload.mockImplementation((params, callback) => {
        callback(uploadError, null);
      });

      const done = jest.fn();

      await awsConfig.logisticPlanUpload(fileList, done);

      expect(done).toHaveBeenCalledWith(null, uploadError);
    });
  });

  describe('sitePlanUpload', () => {
    it('should upload site plan successfully', async () => {
      const file = {
        originalname: 'siteplan.jpg',
        buffer: Buffer.from('site plan data'),
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.sitePlanUpload(file, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'test-uuid-123.jpg',
          Body: Buffer.from('site plan data'),
          ContentDisposition: 'inline; filename=siteplan.jpg',
          ContentType: 'image/jpeg',
        },
        expect.any(Function),
      );

      expect(done).toHaveBeenCalledWith(
        [
          {
            ...mockS3Response,
            fileLink: 'https://cloudfront.example.com/test-uuid-123.jpg',
            fileName: 'siteplan.jpg',
          },
        ],
        false,
      );
    });

    it('should handle file with name property only', async () => {
      const file = {
        name: 'siteplan.png',
        buffer: Buffer.from('site plan data'),
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.png',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.png',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.sitePlanUpload(file, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'test-uuid-123.png',
          Body: Buffer.from('site plan data'),
          ContentDisposition: 'inline; filename=siteplan.png',
          ContentType: 'image/png',
        },
        expect.any(Function),
      );
    });

    it('should handle upload error in sitePlanUpload', async () => {
      const file = {
        originalname: 'siteplan.jpg',
        buffer: Buffer.from('site plan data'),
      };

      const uploadError = new Error('Upload failed');
      mockUpload.mockImplementation((params, callback) => {
        callback(uploadError, null);
      });

      const done = jest.fn();

      await awsConfig.sitePlanUpload(file, done);

      expect(done).toHaveBeenCalledWith(null, uploadError);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle files with no extension', async () => {
      const fileList = [
        {
          originalname: 'testfile',
          buffer: Buffer.from('test'),
        },
      ];

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.',
      };

      mockUpload.mockReturnValue({
        promise: jest.fn().mockResolvedValue(mockS3Response),
      });

      const done = jest.fn();

      await awsConfig.upload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.',
        Body: Buffer.from('test'),
        ContentDisposition: 'attachment; filename=testfile',
      });
    });

    it('should handle files with multiple dots in name', async () => {
      const fileList = [
        {
          originalname: 'test.file.name.jpg',
          buffer: Buffer.from('test'),
        },
      ];

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.jpg',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
      };

      mockUpload.mockReturnValue({
        promise: jest.fn().mockResolvedValue(mockS3Response),
      });

      const done = jest.fn();

      await awsConfig.upload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.jpg',
        Body: Buffer.from('test'),
        ContentDisposition: 'attachment; filename=test.file.name.jpg',
      });
    });

    it('should handle unknown file extension in logisticPlanUpload', async () => {
      const fileList = {
        file: {
          originalname: 'siteplan.txt',
          buffer: Buffer.from('site plan data'),
        },
        params: {
          ProjectId: '12345',
        },
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/12345-siteplan-test-uuid-123.txt',
        Bucket: 'test-bucket',
        Key: '12345-siteplan-test-uuid-123.txt',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.logisticPlanUpload(fileList, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: '12345-siteplan-test-uuid-123.txt',
          Body: Buffer.from('site plan data'),
          ContentDisposition: 'inline; filename=siteplan.txt',
          ContentType: undefined,
        },
        expect.any(Function),
      );
    });

    it('should handle unknown file extension in sitePlanUpload', async () => {
      const file = {
        originalname: 'siteplan.txt',
        buffer: Buffer.from('site plan data'),
      };

      const mockS3Response = {
        Location: 'https://s3.amazonaws.com/test-bucket/test-uuid-123.txt',
        Bucket: 'test-bucket',
        Key: 'test-uuid-123.txt',
      };

      mockUpload.mockImplementation((params, callback) => {
        callback(null, mockS3Response);
      });

      const done = jest.fn();

      await awsConfig.sitePlanUpload(file, done);

      expect(mockUpload).toHaveBeenCalledWith(
        {
          Bucket: 'test-bucket',
          Key: 'test-uuid-123.txt',
          Body: Buffer.from('site plan data'),
          ContentDisposition: 'inline; filename=siteplan.txt',
          ContentType: undefined,
        },
        expect.any(Function),
      );
    });
  });
});
