const excelInspectionReportService = require('../excelInspectionReportService');
const moment = require('moment');

// Mock moment
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((date) => {
    if (date) {
      return actualMoment(date);
    }
    return actualMoment();
  });

  // Copy all moment methods
  Object.setPrototypeOf(mockMoment, actualMoment);
  Object.assign(mockMoment, actualMoment);

  return mockMoment;
});

describe('ExcelInspectionReportService', () => {
  let mockWorkbook;
  let mockWorksheet;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock worksheet
    mockWorksheet = {
      getRow: jest.fn().mockReturnValue({
        values: null
      }),
      addRow: jest.fn(),
      getCell: jest.fn().mockReturnValue({
        value: null
      }),
      columns: null
    };

    // Mock workbook
    mockWorkbook = {
      addWorksheet: jest.fn().mockReturnValue(mockWorksheet)
    };
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(excelInspectionReportService).toBeDefined();
    });

    it('should have inspectionReport method', () => {
      expect(typeof excelInspectionReportService.inspectionReport).toBe('function');
    });
  });

  describe('inspectionReport method', () => {
    let responseData;
    let selectedHeaders;
    let timezoneoffset;

    beforeEach(() => {
      responseData = [
        {
          InspectionId: 1,
          description: 'Test inspection',
          deliveryStart: '2023-01-01T10:00:00Z',
          status: 'Delivered',
          inspectionStatus: 'Completed',
          inspectionType: 'Safety',
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: 'Doe'
            }
          },
          equipmentDetails: [
            {
              Equipment: {
                equipmentName: 'Crane A'
              }
            },
            {
              Equipment: {
                equipmentName: 'Crane B'
              }
            }
          ],
          defineWorkDetails: [
            {
              DeliverDefineWork: {
                DFOW: 'Work Item 1'
              }
            }
          ],
          gateDetails: [
            {
              Gate: {
                gateName: 'Gate 1'
              }
            }
          ],
          companyDetails: [
            {
              Company: {
                companyName: 'Company A'
              }
            }
          ],
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'Jane',
                  lastName: 'Smith'
                }
              }
            }
          ],
          location: {
            locationPath: 'Building A/Floor 1'
          }
        }
      ];

      selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
        { key: 'inspectionType', title: 'Inspection Type', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'DFOW', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Company', isActive: true },
        { key: 'name', title: 'Name', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      timezoneoffset = 300; // 5 hours
    });

    it('should create worksheet with correct name', async () => {
      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Inspection Report');
    });

    it('should set row values from active headers', async () => {
      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      const expectedRowValues = selectedHeaders.filter(h => h.isActive).map(h => h.title);
      expect(mockWorksheet.getRow(1).values).toEqual(expectedRowValues);
    });

    it('should set columns with correct widths', async () => {
      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      const expectedColumns = selectedHeaders.filter(h => h.isActive).map(h => ({
        key: h.key,
        width: h.key === 'id' ? 5 : 32
      }));
      expect(mockWorksheet.columns).toEqual(expectedColumns);
    });

    it('should add rows for each data item', async () => {
      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(responseData.length);
    });

    it('should return the workbook', async () => {
      const result = await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Column Mapping Formatters - Integration Tests', () => {
    // Test formatters by verifying the cell values set during inspectionReport execution
    let capturedCellValues;

    beforeEach(() => {
      capturedCellValues = [];
      mockWorksheet.getCell.mockImplementation((cellRef) => {
        const mockCell = {
          set value(val) {
            capturedCellValues.push({ cell: cellRef, value: val });
          },
          get value() {
            return this._value;
          },
          _value: null
        };
        return mockCell;
      });
    });

    it('should format all column types correctly', async () => {
      const responseData = [
        {
          InspectionId: 123,
          description: 'Test description',
          deliveryStart: '2023-01-01T10:00:00Z',
          status: 'Delivered',
          inspectionStatus: 'Completed',
          inspectionType: 'Safety',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          equipmentDetails: [
            { Equipment: { equipmentName: 'Crane A' } },
            { Equipment: { equipmentName: 'Crane B' } }
          ],
          defineWorkDetails: [
            { DeliverDefineWork: { DFOW: 'Work Item 1' } }
          ],
          gateDetails: [
            { Gate: { gateName: 'Gate 1' } }
          ],
          companyDetails: [
            { Company: { companyName: 'Company A' } }
          ],
          memberDetails: [
            { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
          ],
          location: { locationPath: 'Building A/Floor 1' }
        }
      ];

      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
        { key: 'inspectionType', title: 'Inspection Type', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'DFOW', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Company', isActive: true },
        { key: 'name', title: 'Name', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 300);

      // Verify that all formatters were called and produced expected results
      expect(capturedCellValues).toHaveLength(13); // 13 active headers

      // Check specific formatted values
      const idValue = capturedCellValues.find(cv => cv.cell === 'A2');
      expect(idValue.value).toBe(123);

      const statusValue = capturedCellValues.find(cv => cv.cell === 'D2');
      expect(statusValue.value).toBe('Completed'); // 'Delivered' -> 'Completed'

      const approvedByValue = capturedCellValues.find(cv => cv.cell === 'G2');
      expect(approvedByValue.value).toBe('John Doe');

      const equipmentValue = capturedCellValues.find(cv => cv.cell === 'H2');
      expect(equipmentValue.value).toBe('Crane A, Crane B');
    });

    it('should handle null and undefined values in formatters', async () => {
      const responseData = [
        {
          InspectionId: null,
          description: null,
          deliveryStart: '2023-01-01T10:00:00Z',
          status: 'Pending',
          inspectionStatus: null,
          inspectionType: undefined,
          approverDetails: null,
          equipmentDetails: null,
          defineWorkDetails: 'not an array',
          gateDetails: [],
          companyDetails: undefined,
          memberDetails: 'not an array',
          location: null
        }
      ];

      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
        { key: 'inspectionType', title: 'Inspection Type', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'DFOW', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Company', isActive: true },
        { key: 'name', title: 'Name', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 0);

      // Check that null/undefined values are handled correctly
      const inspectionStatusValue = capturedCellValues.find(cv => cv.cell === 'D2');
      expect(inspectionStatusValue.value).toBe('-');

      const approvedByValue = capturedCellValues.find(cv => cv.cell === 'F2');
      expect(approvedByValue.value).toBe('-');

      const equipmentValue = capturedCellValues.find(cv => cv.cell === 'G2');
      expect(equipmentValue.value).toBe('-');

      const dfowValue = capturedCellValues.find(cv => cv.cell === 'H2');
      expect(dfowValue.value).toBe('-');

      const gateValue = capturedCellValues.find(cv => cv.cell === 'I2');
      expect(gateValue.value).toBe('-');

      const locationValue = capturedCellValues.find(cv => cv.cell === 'L2');
      expect(locationValue.value).toBe('-');
    });

    it('should handle edge cases in approvedby formatter', async () => {
      const responseData = [
        {
          InspectionId: 1,
          approverDetails: { User: { lastName: 'Doe' } } // Missing firstName
        }
      ];

      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 0);

      const approvedByValue = capturedCellValues.find(cv => cv.cell === 'B2');
      expect(approvedByValue.value).toBe('-');
    });

    it('should handle gate formatter edge cases', async () => {
      const responseData = [
        {
          InspectionId: 1,
          gateDetails: [{}] // Empty gate object
        }
      ];

      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 0);

      const gateValue = capturedCellValues.find(cv => cv.cell === 'B2');
      expect(gateValue.value).toBe('-');
    });
  });

  describe('Edge Cases and Integration Tests', () => {
    it('should handle empty responseData array', async () => {
      const emptyData = [];
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true }
      ];

      const result = await excelInspectionReportService.inspectionReport(mockWorkbook, emptyData, selectedHeaders, 0);

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Inspection Report');
      expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should handle selectedHeaders with inactive headers', async () => {
      const responseData = [{ InspectionId: 1 }];
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: false },
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 0);

      const expectedRowValues = ['ID', 'Date & Time'];
      expect(mockWorksheet.getRow(1).values).toEqual(expectedRowValues);
    });

    it('should handle all headers inactive', async () => {
      const responseData = [{ InspectionId: 1 }];
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: false },
        { key: 'description', title: 'Description', isActive: false }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 0);

      expect(mockWorksheet.getRow(1).values).toEqual([]);
      expect(mockWorksheet.columns).toEqual([]);
    });

    it('should set correct cell values for all column types', async () => {
      const responseData = [
        {
          InspectionId: 123,
          description: 'Test description',
          deliveryStart: '2023-01-01T10:00:00Z',
          status: 'Delivered',
          inspectionStatus: 'Completed',
          inspectionType: 'Safety',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          equipmentDetails: [{ Equipment: { equipmentName: 'Crane A' } }],
          defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Work Item 1' } }],
          gateDetails: [{ Gate: { gateName: 'Gate 1' } }],
          companyDetails: [{ Company: { companyName: 'Company A' } }],
          memberDetails: [{ Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }],
          location: { locationPath: 'Building A/Floor 1' }
        }
      ];

      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'status', title: 'Status', isActive: true }
      ];

      const mockCell = { value: null };
      mockWorksheet.getCell.mockReturnValue(mockCell);

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 300);

      expect(mockWorksheet.getCell).toHaveBeenCalledWith('A2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('B2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('C2');
    });

    it('should handle multiple data rows', async () => {
      const responseData = [
        { InspectionId: 1, description: 'First' },
        { InspectionId: 2, description: 'Second' },
        { InspectionId: 3, description: 'Third' }
      ];

      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true }
      ];

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 0);

      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(3);
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('A2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('A3');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('A4');
    });

    it('should handle timezone offset correctly', async () => {
      const responseData = [
        { deliveryStart: '2023-01-01T10:00:00Z' }
      ];

      const selectedHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      const mockCell = { value: null };
      mockWorksheet.getCell.mockReturnValue(mockCell);

      await excelInspectionReportService.inspectionReport(mockWorkbook, responseData, selectedHeaders, 300);

      expect(mockWorksheet.getCell).toHaveBeenCalledWith('A2');
    });
  });
});
