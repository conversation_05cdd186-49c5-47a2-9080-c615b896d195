// Mock bcrypt first to avoid module loading issues
jest.mock('bcrypt', () => ({
  compare: jest.fn(),
}));

// Mock models
jest.mock('../../models', () => ({
  User: {
    getBy: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
  },
  Role: {
    findOne: jest.fn(),
  },
  Sequelize: {
    and: jest.fn(),
    where: jest.fn(),
    fn: jest.fn(),
    col: jest.fn(),
    Op: {
      and: Symbol('and'),
    },
  },
}));

// Mock services
jest.mock('../../services', () => ({
  memberService: {
    uploadProfile: jest.fn(),
  },
}));

// Mock password service
jest.mock('../../services/password', () => ({
  bcryptPassword: jest.fn(),
}));

// Mock helpers
jest.mock('../helpers/enterpriseCheckHelper', () => ({
  checkEnterPrise: jest.fn(),
}));

jest.mock('../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
}));

// Mock serializers
jest.mock('../../serializers', () => ({
  UserSerializer: {
    serialize: jest.fn(),
  },
}));

const UserController = require('../UserController');
const bcrypt = require('bcrypt');
const { User, Member, Role, Sequelize } = require('../../models');
const { memberService } = require('../../services');
const { bcryptPassword } = require('../../services/password');
const enterpriseCheck = require('../helpers/enterpriseCheckHelper');
const helper = require('../helpers/domainHelper');
const { UserSerializer } = require('../../serializers');

describe('UserController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      user: { id: 1 },
      params: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('show', () => {
    it('should show user successfully', async () => {
      const mockUser = { id: 1, email: '<EMAIL>', name: 'Test User' };
      const mockSerializedUser = { id: 1, email: '<EMAIL>' };

      User.getBy.mockResolvedValue(mockUser);
      UserSerializer.serialize.mockReturnValue(mockSerializedUser);

      await UserController.show(mockReq, mockRes, mockNext);

      expect(User.getBy).toHaveBeenCalledWith({});
      expect(UserSerializer.serialize).toHaveBeenCalledWith(mockUser);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockSerializedUser);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error in show', async () => {
      const mockError = new Error('Database error');
      User.getBy.mockRejectedValue(mockError);

      await UserController.show(mockReq, mockRes, mockNext);

      expect(User.getBy).toHaveBeenCalledWith({});
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('userLists', () => {
    it('should get user lists successfully', async () => {
      const mockUsers = [
        { id: 1, email: '<EMAIL>' },
        { id: 2, email: '<EMAIL>' },
      ];

      User.findAll.mockResolvedValue(mockUsers);

      await UserController.userLists(mockReq, mockRes);

      expect(User.findAll).toHaveBeenCalledWith({
        where: {
          [Sequelize.Op.and]: {
            isDeleted: false,
            userType: 'user',
          },
        },
        attributes: ['id', 'email'],
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockUsers });
    });
  });

  describe('checkUser', () => {
    it('should check user successfully when user exists and is member', async () => {
      mockReq.body = { email: '<EMAIL>', ProjectId: 1 };
      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockExistUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, ProjectId: 1 };

      User.getBy.mockResolvedValue(mockUser);
      User.findOne.mockResolvedValue(mockExistUser);
      Member.findOne.mockResolvedValue(mockMember);
      Sequelize.and.mockReturnValue({});
      Sequelize.where.mockReturnValue({});
      Sequelize.fn.mockReturnValue({});
      Sequelize.col.mockReturnValue({});

      await UserController.checkUser(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(User.findOne).toHaveBeenCalled();
      expect(Member.findOne).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        response: mockUser,
        isMemberExists: true,
      });
    });

    it('should check user successfully when user exists but is not member', async () => {
      mockReq.body = { email: '<EMAIL>', ProjectId: 1 };
      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockExistUser = { id: 1, email: '<EMAIL>' };

      User.getBy.mockResolvedValue(mockUser);
      User.findOne.mockResolvedValue(mockExistUser);
      Member.findOne.mockResolvedValue(null);
      Sequelize.and.mockReturnValue({});
      Sequelize.where.mockReturnValue({});
      Sequelize.fn.mockReturnValue({});
      Sequelize.col.mockReturnValue({});

      await UserController.checkUser(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(User.findOne).toHaveBeenCalled();
      expect(Member.findOne).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        response: mockUser,
        isMemberExists: false,
      });
    });

    it('should check user successfully when user does not exist', async () => {
      mockReq.body = { email: '<EMAIL>', ProjectId: 1 };

      User.getBy.mockResolvedValue(null);
      User.findOne.mockResolvedValue(null);
      Sequelize.and.mockReturnValue({});
      Sequelize.where.mockReturnValue({});
      Sequelize.fn.mockReturnValue({});
      Sequelize.col.mockReturnValue({});

      await UserController.checkUser(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(User.findOne).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        response: {},
        isMemberExists: false,
      });
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      mockReq.body = { name: 'Updated Name', phone: '1234567890' };
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        update: jest.fn(),
      };
      const mockUpdatedUser = { id: 1, email: '<EMAIL>', name: 'Updated Name' };
      const mockSerializedUser = { id: 1, email: '<EMAIL>' };

      User.getBy.mockResolvedValue(mockUser);
      mockUser.update.mockResolvedValue(mockUpdatedUser);
      UserSerializer.serialize.mockReturnValue(mockSerializedUser);

      await UserController.update(mockReq, mockRes, mockNext);

      expect(User.getBy).toHaveBeenCalledWith({ id: 1 });
      expect(mockUser.update).toHaveBeenCalledWith(mockReq.body);
      expect(UserSerializer.serialize).toHaveBeenCalledWith(mockUpdatedUser);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ user: mockSerializedUser });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error in update', async () => {
      const mockError = new Error('Update error');
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        update: jest.fn(),
      };

      User.getBy.mockResolvedValue(mockUser);
      mockUser.update.mockRejectedValue(mockError);

      await UserController.update(mockReq, mockRes, mockNext);

      expect(User.getBy).toHaveBeenCalledWith({ id: 1 });
      expect(mockUser.update).toHaveBeenCalledWith(mockReq.body);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('uploadProfile', () => {
    it('should upload profile successfully', async () => {
      const mockResponse = { url: 'https://example.com/profile.jpg' };
      memberService.uploadProfile.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await UserController.uploadProfile(mockReq, mockRes, mockNext);

      expect(memberService.uploadProfile).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from upload profile', async () => {
      const mockError = new Error('Upload error');
      memberService.uploadProfile.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await UserController.uploadProfile(mockReq, mockRes, mockNext);

      expect(memberService.uploadProfile).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in upload profile', async () => {
      const mockError = new Error('Exception error');
      memberService.uploadProfile.mockImplementation(() => {
        throw mockError;
      });

      await UserController.uploadProfile(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('changePassword', () => {
    beforeEach(() => {
      mockReq.body = {
        oldPassword: 'oldPassword123',
        newPassword: 'newPassword123',
      };
      enterpriseCheck.checkEnterPrise.mockResolvedValue('domain');
      helper.getDynamicModel.mockResolvedValue({ User });
    });

    it('should change password successfully', async () => {
      const mockUser = {
        id: 1,
        password: 'hashedOldPassword',
        userType: 'user',
        update: jest.fn(),
      };
      const mockUpdatedUser = { id: 1, email: '<EMAIL>' };
      const mockSerializedUser = { id: 1, email: '<EMAIL>' };

      User.getBy.mockResolvedValue(mockUser);
      bcrypt.compare
        .mockResolvedValueOnce(true) // oldPassword verification
        .mockResolvedValueOnce(false); // newPassword != current password
      bcryptPassword.mockImplementation((password, callback) => {
        callback('hashedNewPassword');
      });
      mockUser.update.mockResolvedValue(mockUpdatedUser);
      UserSerializer.serialize.mockReturnValue(mockSerializedUser);

      await UserController.changePassword(mockReq, mockRes, mockNext);

      expect(enterpriseCheck.checkEnterPrise).toHaveBeenCalledWith(mockReq);
      expect(helper.getDynamicModel).toHaveBeenCalledWith('domain');
      expect(User.getBy).toHaveBeenCalledWith({ id: 1 });
      expect(bcrypt.compare).toHaveBeenCalledWith('oldPassword123', 'hashedOldPassword');
      expect(bcrypt.compare).toHaveBeenCalledWith('newPassword123', 'hashedOldPassword');
      expect(bcryptPassword).toHaveBeenCalledWith('newPassword123', expect.any(Function));
      expect(mockUser.update).toHaveBeenCalledWith({ password: 'hashedNewPassword' });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Password Updated Successfully.',
        user: mockSerializedUser,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle incorrect old password', async () => {
      const mockUser = {
        id: 1,
        password: 'hashedOldPassword',
        userType: 'user',
      };

      User.getBy.mockResolvedValue(mockUser);
      bcrypt.compare
        .mockResolvedValueOnce(false) // oldPassword verification fails
        .mockResolvedValueOnce(false);

      await UserController.changePassword(mockReq, mockRes, mockNext);

      expect(User.getBy).toHaveBeenCalledWith({ id: 1 });
      expect(bcrypt.compare).toHaveBeenCalledWith('oldPassword123', 'hashedOldPassword');
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should handle super admin with same current and new password', async () => {
      const mockUser = {
        id: 1,
        password: 'hashedPassword',
        userType: 'super admin',
      };

      User.getBy.mockResolvedValue(mockUser);
      bcrypt.compare
        .mockResolvedValueOnce(true) // oldPassword verification
        .mockResolvedValueOnce(true); // newPassword == current password

      await UserController.changePassword(mockReq, mockRes, mockNext);

      expect(User.getBy).toHaveBeenCalledWith({ id: 1 });
      expect(bcrypt.compare).toHaveBeenCalledWith('oldPassword123', 'hashedPassword');
      expect(bcrypt.compare).toHaveBeenCalledWith('newPassword123', 'hashedPassword');
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Current & New password should not be same',
      });
    });

    it('should handle error in change password', async () => {
      const mockError = new Error('Database error');
      User.getBy.mockRejectedValue(mockError);

      await UserController.changePassword(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('isAuthenticatedUser', () => {
    it('should return authenticated user successfully', async () => {
      const mockUserData = { id: 1, UserId: 1, RoleId: 2 };
      const mockSerializedUser = { id: 1, email: '<EMAIL>', roleId: null };

      Member.findOne.mockResolvedValue(mockUserData);
      UserSerializer.serialize.mockReturnValue(mockSerializedUser);

      await UserController.isAuthenticatedUser(mockReq, mockRes, mockNext);

      expect(Member.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, isDeleted: false },
      });
      expect(UserSerializer.serialize).toHaveBeenCalledWith(mockReq.user);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        id: 1,
        email: '<EMAIL>',
        roleId: 2,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return authenticated user without roleId update', async () => {
      const mockUserData = { id: 1, UserId: 1, RoleId: 2 };
      const mockSerializedUser = { id: 1, email: '<EMAIL>' };

      Member.findOne.mockResolvedValue(mockUserData);
      UserSerializer.serialize.mockReturnValue(mockSerializedUser);

      await UserController.isAuthenticatedUser(mockReq, mockRes, mockNext);

      expect(Member.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, isDeleted: false },
      });
      expect(UserSerializer.serialize).toHaveBeenCalledWith(mockReq.user);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockSerializedUser);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle unauthenticated user', async () => {
      mockReq.user = null;

      await UserController.isAuthenticatedUser(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('superAdminDetails', () => {
    it('should return super admin details successfully', async () => {
      const mockRole = { id: 1, roleName: 'Super Admin' };

      Role.findOne.mockResolvedValue(mockRole);

      await UserController.superAdminDetails(mockReq, mockRes, mockNext);

      expect(Role.findOne).toHaveBeenCalledWith({
        where: { roleName: 'Super Admin' },
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        user: mockReq.user,
        role: mockRole,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle unauthenticated user for super admin details', async () => {
      mockReq.user = null;

      await UserController.superAdminDetails(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('updateAdminProfile', () => {
    it('should update admin profile successfully', async () => {
      mockReq.body = {
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        phoneCode: '+1',
      };

      User.update.mockResolvedValue([1]);

      await UserController.updateAdminProfile(mockReq, mockRes, mockNext);

      expect(User.update).toHaveBeenCalledWith(
        {
          firstName: 'John',
          phoneNumber: '1234567890',
          phoneCode: '+1',
          lastName: 'Doe',
        },
        {
          where: {
            id: 1,
          },
        },
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Profile updated successfully.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle unauthenticated user for admin profile update', async () => {
      mockReq.user = null;

      await UserController.updateAdminProfile(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});


