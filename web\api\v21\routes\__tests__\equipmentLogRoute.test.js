const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  equipmentValidation: {
    // Add validation objects if needed
  },
}));

jest.mock('../../controllers/equipmentLogController', () => ({
  addEquipmentLog: jest.fn(),
  listEquipmentLog: jest.fn(),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAdmin: jest.fn(),
  isProjectAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
}));

describe('equipmentLogRoute', () => {
  let router;
  let equipmentLogRoute;
  let EquipmentLogController;
  let passportConfig;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    equipmentLogRoute = require('../equipmentLogRoute');
    EquipmentLogController = require('../../controllers/equipmentLogController');
    passportConfig = require('../../config/passport');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = equipmentLogRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST route without validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/add_equipmentlog',
        passportConfig.isAuthenticated,
        EquipmentLogController.addEquipmentLog,
      );

      // Verify GET route without validation
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/equipmentlog_list/:pageSize/:pageNo',
        passportConfig.isAuthenticated,
        EquipmentLogController.listEquipmentLog,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = equipmentLogRoute.router;
      const result2 = equipmentLogRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      equipmentLogRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure routes with auth only (no validation)', () => {
      equipmentLogRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have auth only (3 parameters)
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toHaveLength(3); // path + auth + controller
        expect(call[1]).toBe(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof equipmentLogRoute).toBe('object');
      expect(equipmentLogRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(equipmentLogRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(equipmentLogRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
