/* eslint-disable no-await-in-loop */
const moment = require('moment');
const crypto = require('crypto');
const status = require('http-status');
const bcrypt = require('bcrypt');
const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});

const creditCardType = require('credit-card-type');
const helper = require('../helpers/domainHelper');
const ApiError = require('../helpers/apiError');
const { generatePassword } = require('../helpers/generatePassword');
const MAILER = require('../mailer');
const {
  StripeSubscription,
  Sequelize,
  RestrictEmail,
  Enterprise,
  StripePlan,
  TimeZone,
  NotificationPreference,
  NotificationPreferenceItem,
  ProjectSettings,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let { User, Role, Project, Member, ParentCompany, Company } = require('../models');
const jwtGenerator = require('../middlewares/jwtGenerator');
const { bcryptPassword } = require('./password');
const stripeService = require('./stripeService');
const db = require('../models');
const projectService = require('./projectService');

const { Op } = Sequelize;
let publicUser;
let domainName;
let publicMember;

const authService = {
  async findUserByEmailOrToken(inputData) {
    const email = inputData.params.email ? inputData.params.email : inputData.body.email;
    if (email) {
      return await publicUser.findOne({
        where: {
          [Op.and]: [
            {
              isDeleted: false,
              [Op.and]: Sequelize.and(
                Sequelize.where(
                  Sequelize.fn('lower', Sequelize.col('email')),
                  Sequelize.fn('lower', email),
                ),
              ),
            },
          ],
        },
      });
    }

    const resetToken = inputData.params.resetToken || inputData.params.reset_password_token;
    if (resetToken) {
      return await publicUser.findOne({
        where: { resetPasswordToken: resetToken },
      });
    }

    return null;
  },

  async getDomainNameFromMember(userData) {
    if (!userData) return null;

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });

    if (!memberData?.isAccount) return null;

    const enterpriseValue = await Enterprise.findOne({
      where: { id: memberData.EnterpriseId, status: 'completed' },
    });

    return enterpriseValue ? enterpriseValue.name.toLowerCase() : null;
  },

  async resolveDomainName(inputData) {
    if (inputData.domainName) {
      return inputData.domainName;
    }
    const userData = await this.findUserByEmailOrToken(inputData);
    return await this.getDomainNameFromMember(userData);
  },

  async assignModels(modelObj) {
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    Role = modelObj.Role;
    ParentCompany = modelObj.ParentCompany;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const domainName = await this.resolveDomainName(inputData);
    const modelObj = await helper.getDynamicModel(domainName);
    await this.assignModels(modelObj);
    return User;
  },

  async getPublicModel() {
    const newDomainName = '';
    const modelObj = await helper.getDynamicModel(newDomainName);
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    Role = modelObj.Role;
    ParentCompany = modelObj.ParentCompany;
    return User;
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },

  async createUser(user, done) {
    try {
      await this.getPublicModel();
      const userData = user;
      const firstSplit = userData.basicDetails.email.split('@')[1];
      const secondSplit = firstSplit.split('.');
      let emailDomainName;
      if (secondSplit.length === 2) {
        emailDomainName = firstSplit;
      } else if (secondSplit.length > 2) {
        const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
        emailDomainName = str;
      }
      const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
      if (restrict) {
        return done(null, { message: 'Please use your work email address to register' });
      }
      userData.emailDomainName = emailDomainName;
      if (userData.planData.Plan.planType.toLowerCase() === 'project plan') {
        this.registerProjectPlanUser(userData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      } else {
        this.registerUser(userData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async registerProjectPlanUser(userInput, done) {
    let result = {};
    // stripeService.addNewCard(userInput, async (result, err) => {
    // if (err) {
    // done(null, err);
    // } else {

    const subscription = await stripe.subscriptions.list({
      customer: userInput.stripeCustomerId,
      limit: 1,
    });
    const latestSubscriptionObject = subscription.data[0];

    result = latestSubscriptionObject;
    result.stripeCustomerId = userInput.stripeCustomerId;
    this.commonRegister(userInput, result, async (response, error) => {
      if (error) {
        done(null, error);
      } else {
        done(response, null);
      }
    });

  },

  async createStripeSubscription(newUser, userInput) {
    const customer = await stripe.customers.create({
      email: newUser.email,
      name: `${newUser.firstName} ${newUser.lastName}`,
      phone: `${userInput.basicDetails.phoneCode} ${userInput.basicDetails.phoneNumber}`,
      address: {
        city: userInput.companyDetails.city,
        country: userInput.companyDetails.country,
        line1: userInput.companyDetails.address,
        postal_code: userInput.companyDetails.zipCode,
        state: userInput.companyDetails.state,
      },
    });

    await newUser.updateInstance(newUser.id, {
      stripeCustomerId: customer.id,
    });

    const plan = await StripePlan.getBy({ stripePlanId: userInput.planData.stripePlanId });
    const subscribedToTrialPlan = await stripe.subscriptions.create({
      customer: customer.id,
      collection_method: 'send_invoice',
      trial_period_days: 14,
      days_until_due: 0,
      items: [{ plan: plan.stripePlanId }],
    });

    delete subscribedToTrialPlan.status;
    subscribedToTrialPlan.status = 'active';
    return await StripeSubscription.createInstance(newUser, subscribedToTrialPlan);
  },

  async createProjectWithSettings(userInput, newUser, existParentCompany, newStripeSubscription) {
    const { projectName, projectLocation, projectLocationLatitude, projectLocationLongitude } = userInput.projectDetails;
    const selectedPlanName = userInput.planData.Plan.planType.toLowerCase();
    const projectPlanPeriod = userInput.planData.stripePlanName.toLowerCase();

    const timezoneList = await TimeZone.getAll();
    const getUserTimeZone = timezoneList.filter(
      (object) => object.timezone === userInput.timezone,
    );

    const projectParam = {
      projectName,
      projectLocation,
      createdBy: newUser.id,
      StripeSubscriptionId: newStripeSubscription ? newStripeSubscription.id : null,
      ParentCompanyId: existParentCompany.id,
      subscribedOn: new Date(),
      PlanId: userInput.planData.id,
      projectLocationLatitude,
      projectLocationLongitude,
      TimeZoneId: getUserTimeZone?.[0]?.id || null,
    };

    let startDate = moment().format('YYYY-MM-DD');
    let endDate;
    if (selectedPlanName === 'trial plan') {
      endDate = moment().add(14, 'days').format('YYYY-MM-DD');
    } else if (selectedPlanName === 'project plan') {
      endDate = moment(startDate, 'YYYY-MM-DD')
        .add(projectPlanPeriod === 'monthly' ? 1 : 12, 'M')
        .format('YYYY-MM-DD');
    }

    projectParam.startDate = startDate;
    projectParam.endDate = endDate;

    const newProject = await Project.createInstance(projectParam);

    await ProjectSettings.create({
      deliveryWindowTime: 0,
      deliveryWindowTimeUnit: 'minutes',
      inspectionWindowTime: 0,
      inspectionWindowTimeUnit: 'minutes',
      ProjectId: +newProject.id,
    });

    await projectService.generatePublicUrlForCurrentProject({
      ProjectId: +newProject.id,
      projectName,
    });

    return newProject;
  },

  async createCompanyAndMember(userInput, newUser, existParentCompany, newProject) {
    const roleDetails = await Role.getBy('Project Admin');
    const companyParam = { ...userInput.companyDetails };
    companyParam.createdBy = newUser.id;
    companyParam.ProjectId = newProject.id;
    companyParam.ParentCompanyId = existParentCompany.id;
    companyParam.isParent = !(await Company.findOne({
      where: { ParentCompanyId: +existParentCompany.id, isParent: true },
    }));
    companyParam.companyAutoId = 1;
    delete companyParam.fullName;
    delete companyParam.lastName;

    let newCompany = {};
    if (companyParam.companyId) {
      newCompany.id = +companyParam.companyId;
    } else {
      newCompany = await Company.createInstance(companyParam);
    }

    const memberParam = {
      UserId: newUser.id,
      firstName: newUser.firstName,
      password: userInput.basicDetails.password,
      CompanyId: newCompany.id,
      phoneNumber: userInput.basicDetails.phoneNumber,
      phoneCode: userInput.basicDetails.phoneCode,
      ProjectId: newProject.id,
      memberId: 1,
      ParentCompanyId: existParentCompany.id,
      RoleId: roleDetails.id,
      createdBy: newUser.id,
      status: 'completed',
    };

    const addedMember = await Member.createInstance(memberParam);
    await this.createMemberNotificationPreference(addedMember);
    await this.createMemberLocationFollowPreference(
      +newProject.id,
      +existParentCompany.id,
      +addedMember.id,
      newProject.projectName,
      +newUser.id,
    );

    return { newCompany, addedMember };
  },

  async commonRegister(userInput, newResult, done) {
    try {
      const result = newResult;
      const userData = userInput.basicDetails;
      const { emailDomainName } = userInput;
      userData.firstName = userInput.companyDetails.fullName;
      userData.lastName = userInput.companyDetails.lastName;
      userData.password = generatePassword();
      userData.email = userData.email.toLowerCase();
      const generatedPassword = userData.password;

      const newUser = await User.createInstance(userData);
      let newStripeSubscription;

      if (result) {
        await newUser.updateInstance(newUser.id, {
          stripeCustomerId: result.stripeCustomerId,
        });
        delete result.stripeCustomerId;
        newStripeSubscription = await StripeSubscription.createInstance(newUser, result);
      } else {
        newStripeSubscription = await this.createStripeSubscription(newUser, userInput);
      }

      let existParentCompany = await ParentCompany.getBy({ emailDomainName });
      if (!existParentCompany) {
        existParentCompany = await ParentCompany.createInstance({
          UserId: newUser.id,
          emailDomainName,
        });
      }

      const newProject = await this.createProjectWithSettings(
        userInput,
        newUser,
        existParentCompany,
        newStripeSubscription
      );

      await this.createCompanyAndMember(userInput, newUser, existParentCompany, newProject);

      const userDetail = newUser.dataValues ? newUser : newUser.attributes;
      userData.generatedPassword = generatedPassword;

      this.sendMail(userData, (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          done(null, newError);
        } else {
          done(userDetail, false);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },

  async registerUser(userInput, done) {
    this.commonRegister(userInput, null, async (response, error) => {
      if (error) {
        done(null, error);
      } else {
        done(response, null);
      }
    });
  },

  async sendMail(userData, done) {
    MAILER.sendMail(userData, 'register', 'Registration', 'Sign Up', (info, err) => {
      if (err) {
        const newError = new ApiError(err.message, status.BAD_REQUEST);
        done(null, newError);
      } else {
        done(userData, false);
      }
    });
  },

  async findUserByEmail(email, domainName) {
    let condition = { isDeleted: false };
    if (!domainName) {
      condition = { ...condition, userType: 'user' };
    } else if (domainName !== 'public') {
      await db.syncToSchema(domainName);
    }

    return await User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', email),
        ),
        { ...condition },
      ),
    });
  },

  async validateUserAndPassword(user, password) {
    if (!user?.isActive) {
      throw new ApiError(
        'Your account was deactivated. Please contact Super Admin.!',
        status.NOT_FOUND,
      );
    }

    const onboardingusers = await Member.findOne({
      where: { UserId: user.id, isDeleted: false, status: 'completed' },
    });

    if (!user?.password || !onboardingusers) {
      throw new ApiError(
        'User not yet onboarded, kindly click the onboarding link sent to your email and complete the onboarding process',
        status.NOT_FOUND,
      );
    }

    const verifyPassword = await bcrypt.compare(password, user.password);
    if (!verifyPassword) {
      throw new ApiError('Incorrect Password.', status.UNAUTHORIZED);
    }

    return onboardingusers;
  },

  async getUserProjects(user) {
    const getUserProjects = await Member.findOne({
      where: { UserId: user.id, isDeleted: false, status: 'completed' },
    });

    if (!getUserProjects) {
      throw new ApiError(
        'You need to be a part of a project to access Follo',
        status.NOT_FOUND,
      );
    }

    return getUserProjects;
  },

  async login(inputData, done) {
    try {
      const userData = inputData.body;
      await this.getDynamicModel(inputData);
      const { email, password } = userData;

      const user = await this.findUserByEmail(email, domainName);
      if (!user) {
        throw new ApiError('User not found', status.NOT_FOUND);
      }

      await this.validateUserAndPassword(user, password);
      await this.getUserProjects(user);

      const userParams = { loginDateTime: new Date() };
      await user.updateInstance(user.id, userParams);

      user.domainName = domainName;
      const token = jwtGenerator.token(user);
      user.token = token;

      done(user, false);
    } catch (e) {
      done(null, e);
    }
  },

  async forgotPassword(inputData, userObj, done) {
    await this.returnProjectModel();
    const user = userObj;
    const params = {};
    if (!user?.isActive) {
      const error = new ApiError('User is not verified', status.BAD_REQUEST);
      done(null, error);
    } else {
      params.resetPasswordToken = crypto.randomBytes(64).toString('hex');
      const userData = await user.updateInstance(user.id, params);
      const publicData = await publicUser.findOne({
        where: Sequelize.and(
          Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('email')),
            Sequelize.fn('lower', inputData.body.email),
          ),
          { isDeleted: false },
        ),
      });
      if (publicData.id !== userData.id) {
        publicUser.update(params, { where: { id: publicData.id } });
      }
      user.resetPasswordToken = params.resetPasswordToken;
      user.origin = inputData.get('origin');
      user.requestType = inputData.body.requestType;
      MAILER.sendMail(user, 'forgotPassword', 'Forgot Password', 'Forgot Password', (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          done(null, newError);
        } else {
          done(userData, false);
        }
      });
    }
  },

  async resetPassword(inputData, user, params, done) {
    try {
      await this.getDynamicModel(inputData);
      let { password } = params;
      const encPassword = await bcryptPassword(password);

      const userParams = {
        password: encPassword,
        resetPasswordToken: null,
        otpCode: null,
        secret: null,
      };

      await user.updateInstance(user.id, userParams);
      done(true, false);
    } catch (error) {
      done(true, error);
    }
  },

  async createMemberNotificationPreference(memberData) {
    const memberDetail = await Member.findOne({
      where: { id: memberData.id, isDeleted: false },
    });
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberDetail.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    if (projectObject?.TimeZone) {
      attr.TimeZoneId = projectObject.TimeZone.id;
    } else {
      attr.TimeZoneId = 3;
    }
    await Member.update(attr, { where: { id: memberData.id } });

    for (const item of getNotificationPreferenceItemsList) {
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    }
  },

  async createMemberLocationFollowPreference(
    ProjectId,
    ParentCompanyId,
    MemberId,
    projectName,
    createdBy,
  ) {
    const locationObject = {
      ProjectId,
      ParentCompanyId,
      notes: null,
      MemberId,
      createdBy,
      platform: 'web',
      locationName: projectName,
      locationPath: projectName,
      isDefault: true,
    };
    const location = await Locations.create(locationObject);
    const object = {
      MemberId,
      ProjectId,
      LocationId: location.id,
      follow: true,
      ParentCompanyId,
      isDeleted: false,
    };
    await LocationNotificationPreferences.createInstance(object);
  },
};

module.exports = authService;
