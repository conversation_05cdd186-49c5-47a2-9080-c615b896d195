const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../middlewares/awsConfig');

const csvDeliveryReportService = {
  async exportDeliveryReportInCsvFormat(data, selectedHeaders, timezoneoffset, fileName, exportType, done) {
    const { selectedFlags } = this.extractDeliveryHeaders(selectedHeaders);
    const values = this.constructDeliveryCsvRows(data, selectedFlags, timezoneoffset);
    const csvFile = await this.generateCsvFile(values);

    if (csvFile) {
      const buffer = Buffer.from(csvFile, 'utf-8');
      awsConfig.reportUpload(buffer, fileName, exportType, async (result, error1) => {
        if (!error1) {
          return done(result, false);
        }
        return done(null, { message: 'cannot export document' });
      });
    }
  },
  extractDeliveryHeaders(selectedHeaders) {
    const rowValues = [];
    const columns = [];
    const selectedFlags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isEquipmentSelected: false,
      isDfowSelected: false,
      isGateSelected: false,
      isCompanySelected: false,
      isPersonSelected: false,
      isLocationSelected: false,
    };

    const keyMap = {
      id: 'isIdSelected',
      description: 'isDescriptionSelected',
      date: 'isDateSelected',
      status: 'isStatusSelected',
      approvedby: 'isApprovedBySelected',
      equipment: 'isEquipmentSelected',
      dfow: 'isDfowSelected',
      gate: 'isGateSelected',
      company: 'isCompanySelected',
      name: 'isPersonSelected',
      location: 'isLocationSelected',
    };

    selectedHeaders.forEach((object) => {
      if (object.isActive) {
        rowValues.push(object.title);
        columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });
        const flag = keyMap[object.key];
        if (flag) selectedFlags[flag] = true;
      }
    });

    return { rowValues, columns, selectedFlags };
  },
  constructDeliveryCsvRows(data, flags, timezoneoffset) {
    return data.map(item => this.buildDeliveryRow(item, flags, timezoneoffset));
  },

  buildDeliveryRow(item, flags, timezoneoffset) {
    const row = {};
    
    this.addIdToRow(row, item, flags);
    this.addDescriptionToRow(row, item, flags);
    this.addDateTimeToRow(row, item, flags, timezoneoffset);
    this.addStatusToRow(row, item, flags);
    this.addApprovedByToRow(row, item, flags);
    this.addEquipmentToRow(row, item, flags);
    this.addDfowToRow(row, item, flags);
    this.addGateToRow(row, item, flags);
    this.addCompanyToRow(row, item, flags);
    this.addPersonToRow(row, item, flags);
    this.addLocationToRow(row, item, flags);
    
    return row;
  },

  addIdToRow(row, item, flags) {
    if (flags.isIdSelected) {
      row.Id = item.DeliveryId;
    }
  },

  addDescriptionToRow(row, item, flags) {
    if (flags.isDescriptionSelected) {
      row.Description = item.description;
    }
  },

  addDateTimeToRow(row, item, flags, timezoneoffset) {
    if (flags.isDateSelected) {
      const start = moment(item.deliveryStart).add(Number(timezoneoffset), 'minutes');
      const end = moment(item.deliveryEnd).add(Number(timezoneoffset), 'minutes');
      row['Date & Time'] = `${start.format('MMM-DD-YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
    }
  },

  addStatusToRow(row, item, flags) {
    if (flags.isStatusSelected) {
      row.Status = item.status;
    }
  },

  addApprovedByToRow(row, item, flags) {
    if (flags.isApprovedBySelected) {
      const approver = item.approverDetails?.User;
      row['Approved By'] = approver?.firstName
        ? `${approver.firstName} ${approver.lastName}`
        : '-';
    }
  },

  addEquipmentToRow(row, item, flags) {
    if (flags.isEquipmentSelected) {
      const equipment = this.extractEquipmentNames(item.equipmentDetails);
      row.Equipment = equipment.length ? equipment.join(', ') : '-';
    }
  },

  addDfowToRow(row, item, flags) {
    if (flags.isDfowSelected) {
      const dfows = this.extractDfowNames(item.defineWorkDetails);
      row['Definable Feature of Work'] = dfows.length ? dfows.join(', ') : '-';
    }
  },

  addGateToRow(row, item, flags) {
    if (flags.isGateSelected) {
      row.Gate = item.gateDetails?.[0]?.Gate?.gateName || '-';
    }
  },

  addCompanyToRow(row, item, flags) {
    if (flags.isCompanySelected) {
      const companies = this.extractCompanyNames(item.companyDetails);
      row['Responsible Company'] = companies.length ? companies.join(', ') : '-';
    }
  },

  addPersonToRow(row, item, flags) {
    if (flags.isPersonSelected) {
      const members = this.extractMemberNames(item.memberDetails);
      row['Responsible Person'] = members.length ? members.join(', ') : '-';
    }
  },

  addLocationToRow(row, item, flags) {
    if (flags.isLocationSelected) {
      row.Location = item.location?.locationPath || '-';
    }
  },

  extractEquipmentNames(equipmentDetails) {
    return equipmentDetails?.map(e => e.Equipment?.equipmentName).filter(Boolean) || [];
  },

  extractDfowNames(defineWorkDetails) {
    return defineWorkDetails?.map(d => d.DeliverDefineWork?.DFOW).filter(Boolean) || [];
  },

  extractCompanyNames(companyDetails) {
    return companyDetails?.map(c => c.Company?.companyName).filter(Boolean) || [];
  },

  extractMemberNames(memberDetails) {
    return memberDetails
      ?.map(m => `${m.Member?.User?.firstName} ${m.Member?.User?.lastName}`.trim())
      .filter(Boolean) || [];
  },
  async generateCsvFile(values) {
    const options = {
      showLabels: true,
      showTitle: false,
      useTextFile: false,
      useBom: false,
      useKeysAsHeaders: true,
    };

    const csvExporter = new ExportToCsv(options);
    return await csvExporter.generateCsv(values, true);
  }

};
module.exports = csvDeliveryReportService;
