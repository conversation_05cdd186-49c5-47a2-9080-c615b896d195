const status = require('http-status');
const {
    Sequelize,
    EquipmentLog
} = require('../models');

const equipmentLogService = {
    async addEquipmentLog(equData, done) {
        try {
            const inputData = equData.body;
            const newEquipment = await EquipmentLog.createEquipmentLog(inputData);
            done(newEquipment, false);
        } catch (e) {
            done(null, e);
        }
    },
    async listEquipmentLog(inputData, done) {
        try {
            const { params } = inputData;
            const incomeData = inputData.body;
            let searchCondition = {};
            const pageNumber = +params.pageNo;
            const pageSize = +params.pageSize;
            const { sort } = inputData.body;
            const { sortByField } = inputData.body;
            const offset = (pageNumber - 1) * pageSize;
            const condition = {
                isDeleted: false,
                projectId: inputData.query.projectId,
            };
            const equipmentData = await EquipmentLog.getAll(
                condition,
                pageSize,
                offset,
                searchCondition,
                sort,
                sortByField,
            );
            if (incomeData?.isFilter) {
                if (equipmentData.rows) {
                    if (equipmentData.rows.length > 0) {
                        equipmentData.rows.sort((a, b) =>
                            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
                        );
                    }
                } else if (equipmentData.length > 0) {
                    equipmentData.sort((a, b) =>
                        a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
                    );
                }
            }

            done(equipmentData, false);
        } catch (e) {
            done(null, e);
        }
    },
};
module.exports = equipmentLogService;