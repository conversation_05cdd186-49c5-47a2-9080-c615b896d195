const historyService = require('../historyService');
const helper = require('../../helpers/domainHelper');

// Mock all dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      in: Symbol('in'),
      ne: Symbol('ne'),
      and: Symbol('and'),
      or: Symbol('or'),
      between: Symbol('between'),
      notIn: Symbol('notIn'),
    },
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  DeliverHistory: {
    findAll: jest.fn(),
  },
  DeliveryRequest: {
    findOne: jest.fn(),
  },
  InspectionHistory: {
    findAll: jest.fn(),
  },
  InspectionRequest: {
    findOne: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

describe('HistoryService', () => {
  let mockInputData;
  let mockDone;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    mockModels = require('../../models');

    mockInputData = {
      user: {
        id: 1,
        email: '<EMAIL>',
        domainName: 'testdomain',
        firstName: 'John',
        lastName: 'Doe',
      },
      body: {
        ProjectId: 1,
        ParentCompanyId: 1,
      },
      params: {
        ProjectId: 1,
        DeliveryRequestId: 1,
        InspectionRequestId: 1,
        pageNo: '1',
        pageSize: '10',
      },
    };

    mockDone = jest.fn();

    // Setup default mock implementations
    helper.getDynamicModel.mockResolvedValue({
      DeliverHistory: mockModels.DeliverHistory,
      DeliveryRequest: mockModels.DeliveryRequest,
      InspectionHistory: mockModels.InspectionHistory,
      InspectionRequest: mockModels.InspectionRequest,
      User: mockModels.User,
      Member: mockModels.Member,
    });

    helper.returnProjectModel.mockResolvedValue({
      User: mockModels.User,
      Member: mockModels.Member,
    });

    mockModels.Enterprise.findOne.mockResolvedValue({
      name: 'testdomain',
      id: 1,
    });
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(historyService).toBeDefined();
    });

    it('should have all required methods', () => {
      expect(typeof historyService.getHistory).toBe('function');
      expect(typeof historyService.getInspectionHistory).toBe('function');
      expect(typeof historyService.returnProjectModel).toBe('function');
      expect(typeof historyService.getDomainEnterpriseValue).toBe('function');
      expect(typeof historyService.getEnterpriseValueByMemberData).toBe('function');
      expect(typeof historyService.getEnterpriseValueWithoutMemberData).toBe('function');
      expect(typeof historyService.getUserData).toBe('function');
      expect(typeof historyService.getMemberData).toBe('function');
      expect(typeof historyService.getDynamicModel).toBe('function');
    });
  });

  describe('getHistory', () => {
    it('should successfully get delivery history when delivery request exists', async () => {
      const mockDeliveryRequest = { id: 1, description: 'Test delivery' };
      const mockHistoryList = [
        {
          id: 1,
          type: 'edit',
          description: 'Request edited',
          Member: {
            User: { firstName: 'John', lastName: 'Doe', profilePic: 'pic.jpg' }
          }
        }
      ];

      mockModels.DeliveryRequest.findOne.mockResolvedValue(mockDeliveryRequest);
      mockModels.DeliverHistory.findAll.mockResolvedValue(mockHistoryList);

      await historyService.getHistory(mockInputData, mockDone);

      expect(mockModels.DeliveryRequest.findOne).toHaveBeenCalledWith({
        where: { id: mockInputData.params.DeliveryRequestId }
      });
      expect(mockModels.DeliverHistory.findAll).toHaveBeenCalledWith({
        include: [{
          association: 'Member',
          include: [{ association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] }]
        }],
        where: { DeliveryRequestId: mockInputData.params.DeliveryRequestId },
        order: [['id', 'DESC']]
      });
      expect(mockDone).toHaveBeenCalledWith(mockHistoryList, false);
    });

    it('should return error message when delivery request does not exist', async () => {
      mockModels.DeliveryRequest.findOne.mockResolvedValue(null);

      await historyService.getHistory(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
    });

    it('should handle errors during getHistory execution', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await historyService.getHistory(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('getInspectionHistory', () => {
    it('should successfully get inspection history when inspection request exists', async () => {
      const mockInspectionRequest = { id: 1, description: 'Test inspection' };
      const mockHistoryList = [
        {
          id: 1,
          type: 'approved',
          description: 'Request approved',
          Member: {
            User: { firstName: 'Jane', lastName: 'Smith', profilePic: 'pic2.jpg' }
          }
        }
      ];

      mockModels.InspectionRequest.findOne.mockResolvedValue(mockInspectionRequest);
      mockModels.InspectionHistory.findAll.mockResolvedValue(mockHistoryList);

      await historyService.getInspectionHistory(mockInputData, mockDone);

      expect(mockModels.InspectionRequest.findOne).toHaveBeenCalledWith({
        where: { id: mockInputData.params.InspectionRequestId }
      });
      expect(mockModels.InspectionHistory.findAll).toHaveBeenCalledWith({
        include: [{
          association: 'Member',
          include: [{ association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] }]
        }],
        where: { InspectionRequestId: mockInputData.params.InspectionRequestId },
        order: [['id', 'DESC']]
      });
      expect(mockDone).toHaveBeenCalledWith(mockHistoryList, false);
    });

    it('should return error message when inspection request does not exist', async () => {
      mockModels.InspectionRequest.findOne.mockResolvedValue(null);

      await historyService.getInspectionHistory(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'Inspection Booking id does not exist' });
    });

    it('should handle errors during getInspectionHistory execution', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await historyService.getInspectionHistory(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('returnProjectModel', () => {
    it('should call helper.returnProjectModel and set public variables', async () => {
      const mockModelData = {
        User: mockModels.User,
        Member: mockModels.Member,
      };
      helper.returnProjectModel.mockResolvedValue(mockModelData);

      await historyService.returnProjectModel();

      expect(helper.returnProjectModel).toHaveBeenCalled();
    });
  });

  describe('getDomainEnterpriseValue', () => {
    it('should return enterprise when domain name exists', async () => {
      const mockEnterprise = { id: 1, name: 'testdomain' };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await historyService.getDomainEnterpriseValue('testdomain');

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' }
      });
      expect(result).toEqual(mockEnterprise);
    });

    it('should return null when domain name is not provided', async () => {
      const result = await historyService.getDomainEnterpriseValue(null);
      expect(result).toBeNull();
    });

    it('should return null when enterprise is not found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await historyService.getDomainEnterpriseValue('nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('getEnterpriseValueByMemberData', () => {
    it('should get enterprise by EnterpriseId when member is account', async () => {
      const memberData = { isAccount: true, EnterpriseId: 1 };
      const mockEnterprise = { id: 1, name: 'enterprise1' };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await historyService.getEnterpriseValueByMemberData(memberData, 2);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { id: 1, status: 'completed' }
      });
      expect(result).toEqual(mockEnterprise);
    });

    it('should get enterprise by ParentCompanyId when member is not account', async () => {
      const memberData = { isAccount: false };
      const mockEnterprise = { id: 2, name: 'enterprise2' };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await historyService.getEnterpriseValueByMemberData(memberData, 2);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 2, status: 'completed' }
      });
      expect(result).toEqual(mockEnterprise);
    });
  });

  describe('getEnterpriseValueWithoutMemberData', () => {
    it('should get enterprise by ParentCompanyId', async () => {
      const mockEnterprise = { id: 3, name: 'enterprise3' };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await historyService.getEnterpriseValueWithoutMemberData(3);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 3, status: 'completed' }
      });
      expect(result).toEqual(mockEnterprise);
    });
  });

  describe('getUserData', () => {
    it('should return user when email is provided', async () => {
      const mockUser = { id: 1, email: '<EMAIL>' };
      mockModels.User.findOne.mockResolvedValue(mockUser);

      const result = await historyService.getUserData('<EMAIL>');

      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' }
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null when email is not provided', async () => {
      const result = await historyService.getUserData(null);
      expect(result).toBeNull();
    });
  });

  describe('getMemberData', () => {
    it('should return member when userData is provided', async () => {
      const userData = { id: 1 };
      const mockMember = { id: 1, UserId: 1, RoleId: 2 };
      mockModels.Member.findOne.mockResolvedValue(mockMember);

      const result = await historyService.getMemberData(userData);

      expect(mockModels.Member.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, RoleId: { [mockModels.Sequelize.Op.ne]: 4 }, isDeleted: false }
      });
      expect(result).toEqual(mockMember);
    });

    it('should return null when userData is not provided', async () => {
      const result = await historyService.getMemberData(null);
      expect(result).toBeNull();
    });
  });

  describe('getDynamicModel', () => {
    beforeEach(() => {
      // Reset mocks for each test
      jest.clearAllMocks();

      // Setup default mock implementations
      helper.returnProjectModel.mockResolvedValue({
        User: mockModels.User,
        Member: mockModels.Member,
      });

      helper.getDynamicModel.mockResolvedValue({
        DeliverHistory: mockModels.DeliverHistory,
        DeliveryRequest: mockModels.DeliveryRequest,
        User: mockModels.User,
      });
    });

    it('should successfully get dynamic model with domain name', async () => {
      const mockEnterprise = { id: 1, name: 'testdomain' };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

      const result = await historyService.getDynamicModel(mockInputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' }
      });
      expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
      expect(result).toBe(true);
    });

    it('should handle case when domain enterprise is not found and ParentCompanyId exists', async () => {
      const inputDataWithParent = {
        ...mockInputData,
        user: { ...mockInputData.user, domainName: 'nonexistent' },
        body: { ParentCompanyId: 1 }
      };

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: false };
      const mockEnterprise = { id: 2, name: 'parentcompany' };
      const mockNewUser = { id: 1, email: '<EMAIL>', domainName: 'parentcompany' };

      // First call for domain enterprise (returns null)
      mockModels.Enterprise.findOne
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockEnterprise);

      mockModels.User.findOne
        .mockResolvedValueOnce(mockUser)
        .mockResolvedValueOnce(mockNewUser);

      mockModels.Member.findOne.mockResolvedValue(mockMember);

      const result = await historyService.getDynamicModel(inputDataWithParent);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'nonexistent' }
      });
      expect(mockModels.User.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' }
      });
      expect(mockModels.Member.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, RoleId: { [mockModels.Sequelize.Op.ne]: 4 }, isDeleted: false }
      });
      expect(helper.getDynamicModel).toHaveBeenCalledWith('parentcompany');
      expect(result).toBe(true);
    });

    it('should handle case when member is account type', async () => {
      const inputDataWithParent = {
        ...mockInputData,
        user: { ...mockInputData.user, domainName: 'nonexistent' },
        body: { ParentCompanyId: 1 }
      };

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: true, EnterpriseId: 5 };
      const mockEnterprise = { id: 5, name: 'accountenterprise' };

      mockModels.Enterprise.findOne
        .mockResolvedValueOnce(null) // domain enterprise not found
        .mockResolvedValueOnce(mockEnterprise); // enterprise by member data

      mockModels.User.findOne.mockResolvedValue(mockUser);
      mockModels.Member.findOne.mockResolvedValue(mockMember);

      const result = await historyService.getDynamicModel(inputDataWithParent);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { id: 5, status: 'completed' }
      });
      expect(helper.getDynamicModel).toHaveBeenCalledWith('accountenterprise');
      expect(result).toBe(true);
    });

    it('should handle case when no member data exists', async () => {
      const inputDataWithParent = {
        ...mockInputData,
        user: { ...mockInputData.user, domainName: 'nonexistent' },
        body: { ParentCompanyId: 1 }
      };

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockEnterprise = { id: 3, name: 'nomemberenterprise' };

      mockModels.Enterprise.findOne
        .mockResolvedValueOnce(null) // domain enterprise not found
        .mockResolvedValueOnce(mockEnterprise); // enterprise without member data

      mockModels.User.findOne.mockResolvedValue(mockUser);
      mockModels.Member.findOne.mockResolvedValue(null);

      const result = await historyService.getDynamicModel(inputDataWithParent);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' }
      });
      expect(helper.getDynamicModel).toHaveBeenCalledWith('nomemberenterprise');
      expect(result).toBe(true);
    });

    it('should handle case when ParentCompanyId is undefined', async () => {
      const inputDataNoParent = {
        ...mockInputData,
        user: { ...mockInputData.user, domainName: null },
        body: {},
        params: {}
      };

      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await historyService.getDynamicModel(inputDataNoParent);

      expect(helper.getDynamicModel).toHaveBeenCalledWith(null);
      expect(result).toBe(true);
    });

    it('should handle case when ParentCompanyId is string "undefined"', async () => {
      const inputDataStringUndefined = {
        ...mockInputData,
        user: { ...mockInputData.user, domainName: null },
        body: { ParentCompanyId: 'undefined' }
      };

      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await historyService.getDynamicModel(inputDataStringUndefined);

      expect(helper.getDynamicModel).toHaveBeenCalledWith(null);
      expect(result).toBe(true);
    });

    it('should handle case when no enterprise value is found after member lookup', async () => {
      const inputDataWithParent = {
        ...mockInputData,
        user: { ...mockInputData.user, domainName: 'nonexistent' },
        body: { ParentCompanyId: 1 }
      };

      const mockUser = { id: 1, email: '<EMAIL>' };
      const mockMember = { id: 1, UserId: 1, isAccount: false };

      // First call for domain enterprise (returns null)
      // Second call for enterprise by member data (also returns null)
      mockModels.Enterprise.findOne
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(null);

      mockModels.User.findOne.mockResolvedValue(mockUser);
      mockModels.Member.findOne.mockResolvedValue(mockMember);

      const result = await historyService.getDynamicModel(inputDataWithParent);

      expect(helper.getDynamicModel).toHaveBeenCalledWith('nonexistent');
      expect(result).toBe(true);
    });
  });
});
