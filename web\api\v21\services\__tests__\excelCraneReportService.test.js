const excelCraneReportService = require('../excelCraneReportService');
const moment = require('moment');

// Mock moment to control date formatting
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((date) => actualMoment(date));

  // Copy all static methods from actual moment
  Object.setPrototypeOf(mockMoment, actualMoment);
  Object.assign(mockMoment, actualMoment);

  return mockMoment;
});

describe('ExcelCraneReportService', () => {
  let mockWorkbook;
  let mockWorksheet;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock worksheet methods
    mockWorksheet = {
      addRow: jest.fn(),
      getRow: jest.fn(() => ({ values: null })),
      getCell: jest.fn(() => ({ value: null })),
      columns: null,
    };

    // Mock workbook methods
    mockWorkbook = {
      addWorksheet: jest.fn(() => mockWorksheet),
    };
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(excelCraneReportService).toBeDefined();
    });

    it('should have craneReport method', () => {
      expect(typeof excelCraneReportService.craneReport).toBe('function');
    });
  });

  describe('craneReport method', () => {
    it('should create worksheet with correct name', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true }
      ];
      const responseData = [];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Crane Report');
    });

    it('should process active headers correctly', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: false },
        { key: 'status', title: 'Status', isActive: true }
      ];
      const responseData = [];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.getRow).toHaveBeenCalledWith(1);
      expect(mockWorksheet.columns).toBeDefined();
    });

    it('should set correct column width for id field', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true }
      ];
      const responseData = [];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      // The columns should be set with width 5 for id
      expect(mockWorksheet.columns).toEqual([{ key: 'id', width: 5 }]);
    });

    it('should set correct column width for non-id fields', async () => {
      const selectedHeaders = [
        { key: 'description', title: 'Description', isActive: true }
      ];
      const responseData = [];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      // The columns should be set with width 32 for non-id fields
      expect(mockWorksheet.columns).toEqual([{ key: 'description', width: 32 }]);
    });
  });

  describe('formatMapping functions', () => {
    it('should format id correctly', async () => {
      const selectedHeaders = [{ key: 'id', title: 'ID', isActive: true }];
      const responseData = [{ CraneRequestId: 123 }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format description correctly', async () => {
      const selectedHeaders = [{ key: 'description', title: 'Description', isActive: true }];
      const responseData = [{ description: 'Test description' }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format status correctly', async () => {
      const selectedHeaders = [{ key: 'status', title: 'Status', isActive: true }];
      const responseData = [{ status: 'Approved' }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format approvedby with user details', async () => {
      const selectedHeaders = [{ key: 'approvedby', title: 'Approved By', isActive: true }];
      const responseData = [{
        approverDetails: {
          User: {
            firstName: 'John',
            lastName: 'Doe'
          }
        }
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format approvedby without user details', async () => {
      const selectedHeaders = [{ key: 'approvedby', title: 'Approved By', isActive: true }];
      const responseData = [{ approverDetails: null }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('equipment formatting', () => {
    it('should format equipment with crane type', async () => {
      const selectedHeaders = [{ key: 'equipment', title: 'Equipment', isActive: true }];
      const responseData = [{
        equipmentDetails: [{
          Equipment: {
            equipmentName: 'Crane 1',
            PresetEquipmentType: {
              isCraneType: true
            }
          }
        }]
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format equipment without crane type', async () => {
      const selectedHeaders = [{ key: 'equipment', title: 'Equipment', isActive: true }];
      const responseData = [{
        equipmentDetails: [{
          Equipment: {
            equipmentName: 'Truck 1',
            PresetEquipmentType: {
              isCraneType: false
            }
          }
        }]
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format equipment with empty details', async () => {
      const selectedHeaders = [{ key: 'equipment', title: 'Equipment', isActive: true }];
      const responseData = [{ equipmentDetails: [] }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format equipment with null details', async () => {
      const selectedHeaders = [{ key: 'equipment', title: 'Equipment', isActive: true }];
      const responseData = [{ equipmentDetails: null }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('dfow formatting', () => {
    it('should format dfow with details', async () => {
      const selectedHeaders = [{ key: 'dfow', title: 'DFOW', isActive: true }];
      const responseData = [{
        defineWorkDetails: [{
          DeliverDefineWork: {
            DFOW: 'Test DFOW'
          }
        }]
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format dfow with empty details', async () => {
      const selectedHeaders = [{ key: 'dfow', title: 'DFOW', isActive: true }];
      const responseData = [{ defineWorkDetails: [] }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('gate formatting', () => {
    it('should format gate with details', async () => {
      const selectedHeaders = [{ key: 'gate', title: 'Gate', isActive: true }];
      const responseData = [{
        gateDetails: [{
          Gate: {
            gateName: 'Gate A'
          }
        }]
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format gate with null details', async () => {
      const selectedHeaders = [{ key: 'gate', title: 'Gate', isActive: true }];
      const responseData = [{ gateDetails: null }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('company formatting', () => {
    it('should format company with details', async () => {
      const selectedHeaders = [{ key: 'company', title: 'Company', isActive: true }];
      const responseData = [{
        companyDetails: [{
          Company: {
            companyName: 'Test Company'
          }
        }]
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format company with empty details', async () => {
      const selectedHeaders = [{ key: 'company', title: 'Company', isActive: true }];
      const responseData = [{ companyDetails: [] }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('name formatting', () => {
    it('should format name with member details', async () => {
      const selectedHeaders = [{ key: 'name', title: 'Name', isActive: true }];
      const responseData = [{
        memberDetails: [{
          Member: {
            User: {
              firstName: 'John',
              lastName: 'Doe'
            }
          }
        }]
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format name with empty member details', async () => {
      const selectedHeaders = [{ key: 'name', title: 'Name', isActive: true }];
      const responseData = [{ memberDetails: [] }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('date formatting', () => {
    it('should format date for craneRequest type', async () => {
      const selectedHeaders = [{ key: 'date', title: 'Date', isActive: true }];
      const responseData = [{
        requestType: 'craneRequest',
        craneDeliveryStart: '2023-12-01T10:00:00Z'
      }];
      const timezoneoffset = 60;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format date for non-craneRequest type', async () => {
      const selectedHeaders = [{ key: 'date', title: 'Date', isActive: true }];
      const responseData = [{
        requestType: 'deliveryRequest',
        deliveryStart: '2023-12-01T10:00:00Z'
      }];
      const timezoneoffset = -120;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('picking location formatting', () => {
    it('should format pickingFrom for craneRequest type', async () => {
      const selectedHeaders = [{ key: 'pickingFrom', title: 'Picking From', isActive: true }];
      const responseData = [{
        requestType: 'craneRequest',
        pickUpLocation: 'Location A'
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format pickingTo for craneRequest type', async () => {
      const selectedHeaders = [{ key: 'pickingTo', title: 'Picking To', isActive: true }];
      const responseData = [{
        requestType: 'craneRequest',
        dropOffLocation: 'Location B'
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format pickingFrom for deliveryRequestWithCrane type', async () => {
      const selectedHeaders = [{ key: 'pickingFrom', title: 'Picking From', isActive: true }];
      const responseData = [{
        requestType: 'deliveryRequestWithCrane',
        cranePickUpLocation: 'Crane Location A'
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('more picking location tests', () => {
    it('should format pickingTo for deliveryRequestWithCrane type', async () => {
      const selectedHeaders = [{ key: 'pickingTo', title: 'Picking To', isActive: true }];
      const responseData = [{
        requestType: 'deliveryRequestWithCrane',
        craneDropOffLocation: 'Crane Location B'
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format picking location for other request types', async () => {
      const selectedHeaders = [{ key: 'pickingFrom', title: 'Picking From', isActive: true }];
      const responseData = [{
        requestType: 'otherType'
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('location formatting', () => {
    it('should format location with locationPath', async () => {
      const selectedHeaders = [{ key: 'location', title: 'Location', isActive: true }];
      const responseData = [{
        location: {
          locationPath: 'Building A > Floor 1 > Room 101'
        }
      }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should format location without locationPath', async () => {
      const selectedHeaders = [{ key: 'location', title: 'Location', isActive: true }];
      const responseData = [{ location: null }];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('multiple data items', () => {
    it('should process multiple data items correctly', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true }
      ];
      const responseData = [
        { CraneRequestId: 1, description: 'First item' },
        { CraneRequestId: 2, description: 'Second item' },
        { CraneRequestId: 3, description: 'Third item' }
      ];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(3);
      expect(mockWorksheet.getCell).toHaveBeenCalledTimes(6); // 3 items * 2 fields each
    });

    it('should handle mixed active and inactive headers', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: false },
        { key: 'status', title: 'Status', isActive: true }
      ];
      const responseData = [
        { CraneRequestId: 1, description: 'Test', status: 'Active' }
      ];
      const timezoneoffset = 0;

      await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
      expect(mockWorksheet.getCell).toHaveBeenCalledTimes(2); // Only active headers
    });
  });

  describe('edge cases', () => {
    it('should handle empty responseData', async () => {
      const selectedHeaders = [{ key: 'id', title: 'ID', isActive: true }];
      const responseData = [];
      const timezoneoffset = 0;

      const result = await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(result).toBe(mockWorkbook);
      expect(mockWorksheet.addRow).not.toHaveBeenCalled();
    });

    it('should handle empty selectedHeaders', async () => {
      const selectedHeaders = [];
      const responseData = [{ CraneRequestId: 1 }];
      const timezoneoffset = 0;

      const result = await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(result).toBe(mockWorkbook);
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
      expect(mockWorksheet.getCell).not.toHaveBeenCalled();
    });

    it('should handle all inactive headers', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: false },
        { key: 'description', title: 'Description', isActive: false }
      ];
      const responseData = [{ CraneRequestId: 1, description: 'Test' }];
      const timezoneoffset = 0;

      const result = await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(result).toBe(mockWorkbook);
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
      expect(mockWorksheet.getCell).not.toHaveBeenCalled();
    });
  });

  describe('complex scenarios', () => {
    it('should handle complex data with all fields', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'DFOW', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Company', isActive: true },
        { key: 'name', title: 'Name', isActive: true },
        { key: 'pickingFrom', title: 'Picking From', isActive: true },
        { key: 'pickingTo', title: 'Picking To', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];
      const responseData = [{
        CraneRequestId: 123,
        description: 'Complex crane request',
        requestType: 'craneRequest',
        craneDeliveryStart: '2023-12-01T10:00:00Z',
        status: 'Approved',
        approverDetails: {
          User: { firstName: 'John', lastName: 'Doe' }
        },
        equipmentDetails: [{
          Equipment: {
            equipmentName: 'Crane 1',
            PresetEquipmentType: { isCraneType: true }
          }
        }],
        defineWorkDetails: [{
          DeliverDefineWork: { DFOW: 'Test DFOW' }
        }],
        gateDetails: [{
          Gate: { gateName: 'Gate A' }
        }],
        companyDetails: [{
          Company: { companyName: 'Test Company' }
        }],
        memberDetails: [{
          Member: {
            User: { firstName: 'Jane', lastName: 'Smith' }
          }
        }],
        pickUpLocation: 'Location A',
        dropOffLocation: 'Location B',
        location: {
          locationPath: 'Building A > Floor 1'
        }
      }];
      const timezoneoffset = 60;

      const result = await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(result).toBe(mockWorkbook);
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
      expect(mockWorksheet.getCell).toHaveBeenCalledTimes(13); // All 13 fields
    });

    it('should return the workbook', async () => {
      const selectedHeaders = [{ key: 'id', title: 'ID', isActive: true }];
      const responseData = [{ CraneRequestId: 1 }];
      const timezoneoffset = 0;

      const result = await excelCraneReportService.craneReport(mockWorkbook, responseData, selectedHeaders, timezoneoffset);

      expect(result).toBe(mockWorkbook);
    });
  });
});
