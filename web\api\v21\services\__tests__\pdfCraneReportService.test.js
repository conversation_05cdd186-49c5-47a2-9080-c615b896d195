// Mock dependencies first before requiring modules
jest.mock('fs');
jest.mock('../../middlewares/awsConfig');
jest.mock('puppeteer', () => ({
  launch: jest.fn(),
}));
jest.mock('../puppeteerService');
jest.mock('../../models', () => ({
  Project: {
    findOne: jest.fn(),
  },
  Company: {
    findOne: jest.fn(),
  },
}));
jest.mock('moment', () => {
  const mockMomentInstance = {
    format: jest.fn().mockReturnValue('01/15/2024 10:00 AM'),
    add: jest.fn().mockReturnThis(),
  };
  return jest.fn(() => mockMomentInstance);
});

const fs = require('fs');
const moment = require('moment');
const pdfCraneReportService = require('../pdfCraneReportService');
const puppeteerService = require('../puppeteerService');
const awsConfig = require('../../middlewares/awsConfig');
const { Project, Company } = require('../../models');

describe('pdfCraneReportService', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock fs.readFileSync
    fs.readFileSync.mockReturnValue(
      '$projectName $companyName $generatedDate $generatedBy $reportType $header $data'
    );

    // Mock puppeteerService
    puppeteerService.generatePdfBuffer.mockResolvedValue(Buffer.from('mock-pdf'));

    // Mock awsConfig
    awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
      callback('success-url', null);
    });
  });

  describe('extractCraneHeaderSelection', () => {
    it('should extract headers and flags correctly for all active headers', () => {
      const req = {
        body: {
          selectedHeaders: [
            { key: 'id', title: 'ID', isActive: true },
            { key: 'description', title: 'Description', isActive: true },
            { key: 'date', title: 'Date', isActive: true },
            { key: 'status', title: 'Status', isActive: true },
            { key: 'approvedBy', title: 'Approved By', isActive: true },
            { key: 'equipment', title: 'Equipment', isActive: true },
            { key: 'dfow', title: 'DFOW', isActive: true },
            { key: 'gate', title: 'Gate', isActive: true },
            { key: 'company', title: 'Company', isActive: true },
            { key: 'name', title: 'Person', isActive: true },
            { key: 'pickingFrom', title: 'Picking From', isActive: true },
            { key: 'pickingTo', title: 'Picking To', isActive: true },
            { key: 'location', title: 'Location', isActive: true }
          ]
        }
      };

      const result = pdfCraneReportService.extractCraneHeaderSelection(req);

      expect(result.flags.isIdSelected).toBe(true);
      expect(result.flags.isDescriptionSelected).toBe(true);
      expect(result.flags.isDateSelected).toBe(true);
      expect(result.flags.isStatusSelected).toBe(true);
      expect(result.flags.isApprovedBySelected).toBe(true);
      expect(result.flags.isEquipmentSelected).toBe(true);
      expect(result.flags.isDfowSelected).toBe(true);
      expect(result.flags.isGateSelected).toBe(true);
      expect(result.flags.isCompanySelected).toBe(true);
      expect(result.flags.isPersonSelected).toBe(true);
      expect(result.flags.isPickingFromSelected).toBe(true);
      expect(result.flags.isPickingToSelected).toBe(true);
      expect(result.flags.isLocationSelected).toBe(true);
      expect(result.header).toHaveLength(13);
      expect(result.header[0]).toContain('ID');
      expect(result.header[9]).toContain('Person'); // name key maps to Person
    });

    it('should handle inactive headers correctly', () => {
      const req = {
        body: {
          selectedHeaders: [
            { key: 'id', title: 'ID', isActive: true },
            { key: 'description', title: 'Description', isActive: false },
            { key: 'date', title: 'Date', isActive: true }
          ]
        }
      };

      const result = pdfCraneReportService.extractCraneHeaderSelection(req);

      expect(result.flags.isIdSelected).toBe(true);
      expect(result.flags.isDescriptionSelected).toBe(false);
      expect(result.flags.isDateSelected).toBe(true);
      expect(result.header).toHaveLength(2);
    });

    it('should handle empty selectedHeaders array', () => {
      const req = {
        body: {
          selectedHeaders: []
        }
      };

      const result = pdfCraneReportService.extractCraneHeaderSelection(req);

      expect(Object.values(result.flags).every(flag => flag === false)).toBe(true);
      expect(result.header).toHaveLength(0);
    });

    it('should handle special key mappings correctly', () => {
      const req = {
        body: {
          selectedHeaders: [
            { key: 'name', title: 'Person Name', isActive: true }
          ]
        }
      };

      const result = pdfCraneReportService.extractCraneHeaderSelection(req);

      expect(result.flags.isPersonSelected).toBe(true);
      expect(result.header[0]).toContain('Person Name');
    });
  });

  describe('buildCraneRows', () => {
    const mockData = [
      {
        CraneRequestId: 1,
        description: 'Test crane request',
        requestType: 'craneRequest',
        craneDeliveryStart: '2024-01-15T10:00:00Z',
        craneDeliveryEnd: '2024-01-15T12:00:00Z',
        status: 'Approved',
        pickUpLocation: 'Location A',
        dropOffLocation: 'Location B',
        approverDetails: {
          User: { firstName: 'John', lastName: 'Doe' }
        },
        equipmentDetails: [
          {
            Equipment: {
              equipmentName: 'Crane 1',
              PresetEquipmentType: { isCraneType: true }
            }
          }
        ],
        defineWorkDetails: [
          { DeliverDefineWork: { DFOW: 'Work Type 1' } }
        ],
        gateDetails: [
          { Gate: { gateName: 'Gate A' } }
        ],
        companyDetails: [
          { Company: { companyName: 'Company A' } }
        ],
        memberDetails: [
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ],
        location: { locationPath: 'Path/To/Location' }
      }
    ];

    const mockFlags = {
      isIdSelected: true,
      isDescriptionSelected: true,
      isDateSelected: true,
      isStatusSelected: true,
      isApprovedBySelected: true,
      isEquipmentSelected: true,
      isDfowSelected: true,
      isGateSelected: true,
      isCompanySelected: true,
      isPersonSelected: true,
      isPickingFromSelected: true,
      isPickingToSelected: true,
      isLocationSelected: true
    };

    it('should build rows correctly with selected flags', () => {
      const result = pdfCraneReportService.buildCraneRows(mockData, mockFlags, 0);

      expect(result).toHaveLength(1);
      expect(result[0]).toContain('<tr style="border-bottom: 1px solid #e0e0e0; font-size: 12px">');
      expect(result[0]).toContain('Test crane request');
      expect(result[0]).toContain('Approved');
      expect(result[0]).toContain('John Doe');
    });

    it('should handle empty data array', () => {
      const result = pdfCraneReportService.buildCraneRows([], mockFlags, 0);

      expect(result).toHaveLength(0);
    });

    it('should handle timezone offset correctly', () => {
      const timezoneOffset = -300; // 5 hours behind UTC
      pdfCraneReportService.buildCraneRows(mockData, mockFlags, timezoneOffset);

      expect(moment).toHaveBeenCalledWith(mockData[0].craneDeliveryStart);
      expect(moment().add).toHaveBeenCalledWith(-300, 'minutes');
    });

    it('should handle missing optional fields', () => {
      const dataWithMissingFields = [{
        CraneRequestId: 1,
        description: 'Test crane request',
        requestType: 'craneRequest',
        craneDeliveryStart: '2024-01-15T10:00:00Z',
        craneDeliveryEnd: '2024-01-15T12:00:00Z',
        status: 'Approved'
        // Missing optional fields
      }];

      const result = pdfCraneReportService.buildCraneRows(dataWithMissingFields, mockFlags, 0);

      expect(result).toHaveLength(1);
      expect(result[0]).toContain('-'); // Should contain dash for missing fields
    });

    it('should handle different request types', () => {
      const deliveryRequestData = [{
        CraneRequestId: 1,
        description: 'Test delivery request',
        requestType: 'deliveryRequest',
        deliveryStart: '2024-01-15T10:00:00Z',
        deliveryEnd: '2024-01-15T12:00:00Z',
        status: 'Approved',
        cranePickUpLocation: 'Crane Location A',
        craneDropOffLocation: 'Crane Location B'
      }];

      const result = pdfCraneReportService.buildCraneRows(deliveryRequestData, mockFlags, 0);

      expect(result).toHaveLength(1);
      expect(moment).toHaveBeenCalledWith(deliveryRequestData[0].deliveryStart);
    });
  });

  describe('generateCranePdfTemplate', () => {
    const mockTemplateData = {
      templatePath: '/path/to/template.html',
      projectData: { projectName: 'Test Project' },
      companyData: { companyName: 'Test Company' },
      loginUser: { firstName: 'John', lastName: 'Doe' },
      req: { body: { generatedDate: '2024-01-15' } },
      header: ['<th>Header 1</th>', '<th>Header 2</th>'],
      content: ['<tr>Row 1</tr>', '<tr>Row 2</tr>']
    };

    it('should replace all template placeholders correctly', () => {
      const result = pdfCraneReportService.generateCranePdfTemplate(
        mockTemplateData.templatePath,
        mockTemplateData.projectData,
        mockTemplateData.companyData,
        mockTemplateData.loginUser,
        mockTemplateData.req,
        mockTemplateData.header,
        mockTemplateData.content
      );

      expect(fs.readFileSync).toHaveBeenCalledWith(mockTemplateData.templatePath, 'utf-8');
      expect(result).toContain('Test Project');
      expect(result).toContain('Test Company');
      expect(result).toContain('2024-01-15');
      expect(result).toContain('John Doe');
      expect(result).toContain('Crane');
      expect(result).toContain('<th>Header 1</th><th>Header 2</th>');
      expect(result).toContain('<tr>Row 1</tr><tr>Row 2</tr>');
      expect(result).not.toContain(','); // Commas should be removed
    });

    it('should handle empty header and content arrays', () => {
      const result = pdfCraneReportService.generateCranePdfTemplate(
        mockTemplateData.templatePath,
        mockTemplateData.projectData,
        mockTemplateData.companyData,
        mockTemplateData.loginUser,
        mockTemplateData.req,
        [],
        []
      );

      expect(result).toContain('Test Project');
      expect(result).toContain('Test Company');
      expect(result).not.toContain('<th>');
      expect(result).not.toContain('<tr>');
    });
  });

  describe('pdfFormatOfCraneRequest', () => {
    const mockParams = { ProjectId: '1' };
    const mockLoginUser = { firstName: 'John', lastName: 'Doe' };
    const mockData = [
      {
        CraneRequestId: 1,
        description: 'Test crane request',
        requestType: 'craneRequest',
        craneDeliveryStart: '2024-01-15T10:00:00Z',
        craneDeliveryEnd: '2024-01-15T12:00:00Z',
        status: 'Approved'
      }
    ];
    const mockReq = {
      headers: { timezoneoffset: '0' },
      body: {
        selectedHeaders: [
          { key: 'id', title: 'ID', isActive: true }
        ],
        ParentCompanyId: '1',
        reportName: 'test-report.pdf',
        exportType: 'pdf'
      }
    };
    const mockDone = jest.fn();

    beforeEach(() => {
      Project.findOne.mockResolvedValue({ projectName: 'Test Project' });
      Company.findOne.mockResolvedValue({ companyName: 'Test Company' });
      puppeteerService.generatePdfBuffer.mockResolvedValue(Buffer.from('pdf content'));
      awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
        callback({ success: true }, null);
      });
    });

    it('should generate PDF successfully', async () => {
      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(Project.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, id: 1 },
        attributes: ['projectName']
      });
      expect(Company.findOne).toHaveBeenCalledWith({
        where: { isDeleted: false, ParentCompanyId: 1, isParent: true },
        attributes: ['companyName']
      });
      expect(puppeteerService.generatePdfBuffer).toHaveBeenCalled();
      expect(awsConfig.reportUpload).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith({ success: true }, false);
    });

    it('should handle project not found', async () => {
      Project.findOne.mockResolvedValue(null);

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(mockDone).toHaveBeenCalledWith(false, expect.objectContaining({
        message: 'Unexpected error during PDF generation'
      }));
    });

    it('should handle company not found', async () => {
      Company.findOne.mockResolvedValue(null);

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(mockDone).toHaveBeenCalledWith(false, expect.objectContaining({
        message: 'Unexpected error during PDF generation'
      }));
    });

    it('should handle PDF generation failure', async () => {
      puppeteerService.generatePdfBuffer.mockResolvedValue(null);

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Cannot export the document' });
    });

    it('should handle AWS upload failure', async () => {
      awsConfig.reportUpload.mockImplementation((_buffer, _name, _type, callback) => {
        callback(null, { error: 'Upload failed' });
      });

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(mockDone).toHaveBeenCalledWith(false, { message: 'Upload failed' });
    });

    it('should handle database errors', async () => {
      Project.findOne.mockRejectedValue(new Error('Database connection failed'));

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(mockDone).toHaveBeenCalledWith(false, expect.objectContaining({
        message: 'Unexpected error during PDF generation',
        error: 'Database connection failed'
      }));
    });

    it('should handle puppeteer service errors', async () => {
      puppeteerService.generatePdfBuffer.mockRejectedValue(new Error('Puppeteer failed'));

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, mockReq, mockDone);

      expect(mockDone).toHaveBeenCalledWith(false, expect.objectContaining({
        message: 'Unexpected error during PDF generation',
        error: 'Puppeteer failed'
      }));
    });

    it('should handle empty data array', async () => {
      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, [], mockReq, mockDone);

      expect(puppeteerService.generatePdfBuffer).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith({ success: true }, false);
    });

    it('should handle different timezone offsets', async () => {
      const reqWithTimezone = {
        ...mockReq,
        headers: { timezoneoffset: '-300' }
      };

      await pdfCraneReportService.pdfFormatOfCraneRequest(mockParams, mockLoginUser, mockData, reqWithTimezone, mockDone);

      expect(mockDone).toHaveBeenCalledWith({ success: true }, false);
    });
  });
});