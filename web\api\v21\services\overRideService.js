const Sequelize = require('sequelize');
const status = require('http-status');
const { Member, OverRide, Project } = require('../models');
const ApiError = require('../helpers/apiError');
const MAILER = require('../mailer');

const overRideService = {
  async applyOverRide(inputData, done) {
    try {
      const overRideData = inputData.body;
      const loginUser = inputData.user;
      const planDetails = await Project.findByPk(overRideData.ProjectId);
      const member = await Member.findOne({
        where: Sequelize.and({
          RoleId: 2,
          UserId: loginUser.id,
          ProjectId: overRideData.ProjectId,
          isDeleted: false,
        }),
      });
      if (member) {
        const existOverRide = await OverRide.findOne({
          where: Sequelize.and({
            MemberId: member.id,
            status: 'Pending',
            StripePlanId: planDetails.PlanId,
          }),
        });
        if (existOverRide) {
          done(null, { message: 'Your Booking Send Already , waiting for admin approval.' });
        } else {
          const overRideParam = {
            MemberId: member.id,
            StripePlanId: planDetails.PlanId,
            UserId: loginUser.id,
            ProjectId: overRideData.ProjectId,
            expiryDate: overRideData.expiryDate,
            comment: overRideData.comment,
          };
          const newOverRide = await OverRide.createInstance(overRideParam);
          done(newOverRide, false);
        }
      } else {
        done(null, { message: "You are not a member / You don't have rights to apply that." });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async adminAction(input, done) {
    try {
      const inputData = input.body;
      const overRideData = await OverRide.getBy({ id: inputData.id });
      if (overRideData) {
        const updateData = await OverRide.update(
          { status: inputData.status },
          { where: { id: inputData.id } },
        );
        if (updateData) {
          this.sendMail(
            overRideData,
            'adminoverride',
            'Admin Over Ride Status',
            'Admin Over Ride Status',
            (response, error) => {
              if (!error) {
                done(response, false);
              } else {
                done(null, error);
              }
            },
          );
        } else {
          done(null, { message: 'Something went wrong' });
        }
      } else {
        done(null, { message: 'Something went wrong' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async sendMail(overRideData, type, subject, tagName, done) {
    await MAILER.sendMail(overRideData, type, subject, tagName, (info, err) => {
      if (err) {
        const newError = new ApiError(err.message, status.BAD_REQUEST);
        done(null, newError);
      } else {
        done(overRideData, false);
      }
    });
  },
};
module.exports = overRideService;
