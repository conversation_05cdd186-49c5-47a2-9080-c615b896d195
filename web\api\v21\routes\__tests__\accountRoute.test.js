const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('../../controllers', () => ({
  AccountCornController: {
    getEnterpriseAccounts: jest.fn(),
    getNonEnterpriseAccounts: jest.fn(),
    getNonEnterpriseAccountProjects: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAdmin: jest.fn(),
}));

describe('accountRoute', () => {
  let router;
  let accountRoute;
  let AccountCornController;
  let passportConfig;
  let checkAdmin;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    accountRoute = require('../accountRoute');
    const controllers = require('../../controllers');
    AccountCornController = controllers.AccountCornController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = accountRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.get).toHaveBeenCalledTimes(3);

      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_enterprise_accounts',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AccountCornController.getEnterpriseAccounts,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_non_enterprise_accounts',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AccountCornController.getNonEnterpriseAccounts,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/get_non_enterprise_account_projects/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AccountCornController.getNonEnterpriseAccountProjects,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = accountRoute.router;
      const result2 = accountRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2); // Called once for each access
    });

    it('should configure routes in the correct order', () => {
      accountRoute.router;

      const calls = router.get.mock.calls;
      expect(calls[0][0]).toBe('/get_enterprise_accounts');
      expect(calls[1][0]).toBe('/get_non_enterprise_accounts');
      expect(calls[2][0]).toBe('/get_non_enterprise_account_projects/:id');
    });

    it('should use the correct middleware chain for each route', () => {
      accountRoute.router;

      // Check that each route has the correct middleware chain
      const calls = router.get.mock.calls;

      // All routes should have authentication and admin check
      calls.forEach(call => {
        expect(call[1]).toBe(passportConfig.isAuthenticated);
        expect(call[2]).toBe(checkAdmin.isAdmin);
      });

      // Check controller methods
      expect(calls[0][3]).toBe(AccountCornController.getEnterpriseAccounts);
      expect(calls[1][3]).toBe(AccountCornController.getNonEnterpriseAccounts);
      expect(calls[2][3]).toBe(AccountCornController.getNonEnterpriseAccountProjects);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof accountRoute).toBe('object');
      expect(accountRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(accountRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(accountRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
