module.exports = (sequelize, DataTypes) => {
  const State = sequelize.define(
    'State',
    {
      stateName: DataTypes.STRING,
      CountryId: DataTypes.INTEGER,
    },
    {},
  );
  State.associate = (models) => {
    State.belongsTo(models.Country);
    return State;
  };
  State.getAll = async (attr) => {
    const state = await State.findAll({
      include: ['Country'],
      where: { ...attr },
    });
    return state;
  };
  State.createInstance = async (paramData) => {
    const state = await State.create(paramData);
    return state;
  };
  return State;
};
