const Joi = require('joi');

const defineValidation = {
  createDefine: {
    body: Joi.object({
      definable: Joi.optional().allow(''),
    }),
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  updateDefinable: {
    body: Joi.object({
      editData: Joi.array().required(),
      ParentCompanyId: Joi.any(),
    }),
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
  },
  addDefinable: {
    body: Joi.object({
      addData: Joi.array().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  deleteDefinable: {
    body: Joi.object({
      deleteData: Joi.array().required(),
      ProjectId: Joi.number().required(),
      isSelectAll: Joi.boolean().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getDefinable: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      export: Joi.number().required(),
      sort: Joi.string().required(),
      sortByField: Joi.any().optional().allow('', null),
    }),
    body: Joi.object({
      search: Joi.optional().allow(''),
      ParentCompanyId: Joi.any(),
    }),
  },
  exportDefinable: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      sort: Joi.string().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = defineValidation;
