const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  craneRequestHistoryController: {
    getCraneRequestHistories: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  craneRequestHistoryValidation: {
    getCraneRequestHistories: jest.fn(),
  },
}));

describe('craneRequestHistoryRoute', () => {
  let router;
  let craneRequestHistoryRoute;
  let craneRequestHistoryController;
  let passportConfig;
  let craneRequestHistoryValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    craneRequestHistoryRoute = require('../craneRequestHistoryRoute');
    const controllers = require('../../controllers');
    craneRequestHistoryController = controllers.craneRequestHistoryController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    craneRequestHistoryValidation = validations.craneRequestHistoryValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = craneRequestHistoryRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_crane_request_histories/:CraneRequestId/?:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        craneRequestHistoryController.getCraneRequestHistories,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(1);
      expect(validate).toHaveBeenCalledWith(
        craneRequestHistoryValidation.getCraneRequestHistories,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = craneRequestHistoryRoute.router;
      const result2 = craneRequestHistoryRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication and validation middleware for all routes', () => {
      craneRequestHistoryRoute.router;

      const getCalls = router.get.mock.calls;

      // All routes should have validation and authentication
      getCalls.forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toHaveLength(4); // path + validation + auth + controller
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof craneRequestHistoryRoute).toBe('object');
      expect(craneRequestHistoryRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestHistoryRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestHistoryRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
