const moment = require('moment');

const excelCraneReportService = {
  async craneReport(workbook, responseData, selectedHeaders, timezoneoffset) {
    const worksheet = workbook.addWorksheet('Crane Report');

    const {
      rowValues,
      columns,
      selectedFlags
    } = this.prepareCraneWorksheetMetadata(selectedHeaders);

    worksheet.getRow(1).values = rowValues;
    worksheet.columns = columns;

    const cellRange = this.getCellRange();
    this.populateCraneWorksheetRows(worksheet, rowValues, responseData, selectedFlags, cellRange, timezoneoffset);

    return workbook;
  },

  prepareCraneWorksheetMetadata(selectedHeaders) {
    const rowValues = [];
    const columns = [];
    const selectedFlags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isEquipmentSelected: false,
      isDfowSelected: false,
      isGateSelected: false,
      isCompanySelected: false,
      isPersonSelected: false,
      isPickingFromSelected: false,
      isPickingToSelected: false,
      isLocationSelected: false
    };

    selectedHeaders.forEach((object) => {
      if (!object.isActive) return;

      rowValues.push(object.title);
      columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });

      switch (object.key) {
        case 'id': selectedFlags.isIdSelected = true; break;
        case 'description': selectedFlags.isDescriptionSelected = true; break;
        case 'date': selectedFlags.isDateSelected = true; break;
        case 'status': selectedFlags.isStatusSelected = true; break;
        case 'approvedby': selectedFlags.isApprovedBySelected = true; break;
        case 'equipment': selectedFlags.isEquipmentSelected = true; break;
        case 'dfow': selectedFlags.isDfowSelected = true; break;
        case 'gate': selectedFlags.isGateSelected = true; break;
        case 'company': selectedFlags.isCompanySelected = true; break;
        case 'name': selectedFlags.isPersonSelected = true; break;
        case 'pickingFrom': selectedFlags.isPickingFromSelected = true; break;
        case 'pickingTo': selectedFlags.isPickingToSelected = true; break;
        case 'location': selectedFlags.isLocationSelected = true; break;
      }
    });

    return { rowValues, columns, selectedFlags };
  },

  getCellRange() {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const range = {};
    for (let i = 0; i < alphabet.length; i++) {
      range[i] = alphabet[i];
    }
    return range;
  },

  populateCraneWorksheetRows(worksheet, rowValues, responseData, flags, cellRange, timezoneoffset) {
    for (let index = 1; index <= responseData.length; index++) {
      const data = responseData[index - 1];
      worksheet.addRow();
      const cell = (title) => cellRange[rowValues.indexOf(title)] + (index + 1);

      this.populateCraneBasicFields(worksheet, cell, data, flags, timezoneoffset);
      this.populateCraneMultiValueFields(worksheet, cell, data, flags);
      this.populateCraneNestedFields(worksheet, cell, data, flags);
      this.populateCraneSpecialFields(worksheet, cell, data, flags);
    }
  },

  populateCraneBasicFields(worksheet, cell, data, flags, timezoneoffset) {
    if (flags.isIdSelected) worksheet.getCell(cell('Id')).value = data.CraneRequestId;
    if (flags.isDescriptionSelected) worksheet.getCell(cell('Description')).value = data.description;

    if (flags.isDateSelected) {
      const start = data.requestType === 'craneRequest'
        ? moment(data.craneDeliveryStart).add(Number(timezoneoffset), 'minutes')
        : moment(data.deliveryStart).add(Number(timezoneoffset), 'minutes');

      const end = data.requestType === 'craneRequest'
        ? moment(data.craneDeliveryEnd).add(Number(timezoneoffset), 'minutes')
        : moment(data.deliveryEnd).add(Number(timezoneoffset), 'minutes');

      worksheet.getCell(cell('Date & Time')).value = `${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
    }

    if (flags.isStatusSelected) {
      worksheet.getCell(cell('Status')).value = data.status;
    }
  },
  populateCraneMultiValueFields(worksheet, cell, data, flags) {
    if (flags.isEquipmentSelected) {
      const equipment = (data.equipmentDetails || [])
        .filter(e => e?.Equipment?.PresetEquipmentType?.isCraneType)
        .map(e => e.Equipment.equipmentName)
        .join(', ') || '-';
      worksheet.getCell(cell('Equipment')).value = equipment;
    }

    if (flags.isDfowSelected) {
      const dfow = (data.defineWorkDetails || [])
        .map(d => d?.DeliverDefineWork?.DFOW)
        .filter(Boolean)
        .join(', ') || '-';
      worksheet.getCell(cell('Definable Feature of Work')).value = dfow;
    }

    if (flags.isCompanySelected) {
      const companies = (data.companyDetails || [])
        .map(c => c?.Company?.companyName)
        .filter(Boolean)
        .join(', ') || '-';
      worksheet.getCell(cell('Responsible Company')).value = companies;
    }
  },

  populateCraneNestedFields(worksheet, cell, data, flags) {
    if (flags.isApprovedBySelected) {
      const user = data.approverDetails?.User;
      worksheet.getCell(cell('Approved By')).value = user
        ? `${user.firstName} ${user.lastName}`
        : '-';
    }

    if (flags.isGateSelected) {
      worksheet.getCell(cell('Gate')).value = data.gateDetails?.[0]?.Gate?.gateName || '-';
    }

    if (flags.isPersonSelected) {
      const members = (data.memberDetails || [])
        .map(m => m?.Member?.User ? `${m.Member.User.firstName} ${m.Member.User.lastName}` : null)
        .filter(Boolean)
        .join(', ') || '-';
      worksheet.getCell(cell('Responsible Person')).value = members;
    }

    if (flags.isLocationSelected) {
      worksheet.getCell(cell('Location')).value = data.location?.locationPath || '-';
    }
  },

  populateCraneSpecialFields(worksheet, cell, data, flags) {
    if (flags.isPickingFromSelected) {
      const from = data.requestType === 'craneRequest'
        ? data.pickUpLocation
        : data.cranePickUpLocation || '-';
      worksheet.getCell(cell('Picking From')).value = from;
    }

    if (flags.isPickingToSelected) {
      const to = data.requestType === 'craneRequest'
        ? data.dropOffLocation
        : data.craneDropOffLocation || '-';
      worksheet.getCell(cell('Picking To')).value = to;
    }
  }


};
module.exports = excelCraneReportService;
