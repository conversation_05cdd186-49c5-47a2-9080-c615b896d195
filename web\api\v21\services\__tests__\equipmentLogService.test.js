const equipmentLogService = require('../equipmentLogService');

// Mock all dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      in: jest.fn(),
      ne: jest.fn(),
      and: jest.fn(),
      or: jest.fn(),
      between: jest.fn(),
      notIn: jest.fn(),
    },
  },
  EquipmentLog: {
    createEquipmentLog: jest.fn(),
    getAll: jest.fn(),
  },
}));

describe('EquipmentLogService', () => {
  let mockDone;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    mockModels = require('../../models');
    mockDone = jest.fn();
  });

  describe('addEquipmentLog', () => {
    it('should successfully add equipment log', async () => {
      const mockEquipmentData = {
        EquipmentName: 'Test Equipment',
        EquipmentManufacturer: 'Test Manufacturer',
        EquipmentModel: 'Test Model',
        fuelType: 'Diesel',
        projectId: 1,
      };

      const mockInputData = {
        body: mockEquipmentData,
      };

      const mockCreatedEquipment = {
        id: 1,
        ...mockEquipmentData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockModels.EquipmentLog.createEquipmentLog.mockResolvedValue(mockCreatedEquipment);

      await equipmentLogService.addEquipmentLog(mockInputData, mockDone);

      expect(mockModels.EquipmentLog.createEquipmentLog).toHaveBeenCalledWith(mockEquipmentData);
      expect(mockDone).toHaveBeenCalledWith(mockCreatedEquipment, false);
    });

    it('should handle error when adding equipment log fails', async () => {
      const mockInputData = {
        body: {
          EquipmentName: 'Test Equipment',
        },
      };

      const mockError = new Error('Database error');
      mockModels.EquipmentLog.createEquipmentLog.mockRejectedValue(mockError);

      await equipmentLogService.addEquipmentLog(mockInputData, mockDone);

      expect(mockModels.EquipmentLog.createEquipmentLog).toHaveBeenCalledWith(mockInputData.body);
      expect(mockDone).toHaveBeenCalledWith(null, mockError);
    });
  });

  describe('listEquipmentLog', () => {
    it('should successfully list equipment logs without filter', async () => {
      const mockInputData = {
        params: {
          pageNo: '2',
          pageSize: '5',
        },
        body: {
          sort: 'ASC',
          sortByField: 'equipmentName',
        },
        query: {
          projectId: 1,
        },
      };

      const mockEquipmentData = {
        rows: [
          {
            id: 1,
            equipmentName: 'Equipment A',
            EquipmentManufacturer: 'Manufacturer A',
          },
          {
            id: 2,
            equipmentName: 'Equipment B',
            EquipmentManufacturer: 'Manufacturer B',
          },
        ],
        count: 2,
      };

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      expect(mockModels.EquipmentLog.getAll).toHaveBeenCalledWith(
        {
          isDeleted: false,
          projectId: 1,
        },
        5, // pageSize
        5, // offset (pageNumber - 1) * pageSize = (2 - 1) * 5 = 5
        {}, // searchCondition
        'ASC', // sort
        'equipmentName' // sortByField
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should successfully list equipment logs with filter and rows data', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {
          isFilter: true,
          sort: 'DESC',
          sortByField: 'id',
        },
        query: {
          projectId: 2,
        },
      };

      const mockEquipmentData = {
        rows: [
          {
            id: 2,
            equipmentName: 'zebra equipment',
            EquipmentManufacturer: 'Manufacturer Z',
          },
          {
            id: 1,
            equipmentName: 'alpha equipment',
            EquipmentManufacturer: 'Manufacturer A',
          },
        ],
        count: 2,
      };

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      // Verify the data was sorted alphabetically by equipmentName
      const expectedSortedData = {
        rows: [
          {
            id: 1,
            equipmentName: 'alpha equipment',
            EquipmentManufacturer: 'Manufacturer A',
          },
          {
            id: 2,
            equipmentName: 'zebra equipment',
            EquipmentManufacturer: 'Manufacturer Z',
          },
        ],
        count: 2,
      };

      expect(mockModels.EquipmentLog.getAll).toHaveBeenCalledWith(
        {
          isDeleted: false,
          projectId: 2,
        },
        10, // pageSize
        0, // offset (pageNumber - 1) * pageSize = (1 - 1) * 10 = 0
        {}, // searchCondition
        'DESC', // sort
        'id' // sortByField
      );
      expect(mockDone).toHaveBeenCalledWith(expectedSortedData, false);
    });
    it('should successfully list equipment logs with filter and array data (no rows property)', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {
          isFilter: true,
        },
        query: {
          projectId: 3,
        },
      };

      const mockEquipmentData = [
        {
          id: 3,
          equipmentName: 'zebra equipment',
          EquipmentManufacturer: 'Manufacturer Z',
        },
        {
          id: 1,
          equipmentName: 'alpha equipment',
          EquipmentManufacturer: 'Manufacturer A',
        },
      ];

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      // Verify the data was sorted alphabetically by equipmentName
      const expectedSortedData = [
        {
          id: 1,
          equipmentName: 'alpha equipment',
          EquipmentManufacturer: 'Manufacturer A',
        },
        {
          id: 3,
          equipmentName: 'zebra equipment',
          EquipmentManufacturer: 'Manufacturer Z',
        },
      ];

      expect(mockDone).toHaveBeenCalledWith(expectedSortedData, false);
    });

    it('should handle filter with empty rows array', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {
          isFilter: true,
        },
        query: {
          projectId: 4,
        },
      };

      const mockEquipmentData = {
        rows: [],
        count: 0,
      };

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should handle filter with empty array (no rows property)', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {
          isFilter: true,
        },
        query: {
          projectId: 5,
        },
      };

      const mockEquipmentData = [];

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should handle error when listing equipment logs fails', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {},
        query: {
          projectId: 1,
        },
      };

      const mockError = new Error('Database connection failed');
      mockModels.EquipmentLog.getAll.mockRejectedValue(mockError);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, mockError);
    });

    it('should handle missing sort and sortByField in body', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {}, // No sort or sortByField
        query: {
          projectId: 1,
        },
      };

      const mockEquipmentData = {
        rows: [],
        count: 0,
      };

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      expect(mockModels.EquipmentLog.getAll).toHaveBeenCalledWith(
        {
          isDeleted: false,
          projectId: 1,
        },
        10, // pageSize
        0, // offset
        {}, // searchCondition
        undefined, // sort
        undefined // sortByField
      );
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should test both branches of sorting comparison for rows data', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {
          isFilter: true,
        },
        query: {
          projectId: 1,
        },
      };

      const mockEquipmentData = {
        rows: [
          {
            id: 1,
            equipmentName: 'Alpha Equipment',
            EquipmentManufacturer: 'Manufacturer A',
          },
          {
            id: 2,
            equipmentName: 'Beta Equipment',
            EquipmentManufacturer: 'Manufacturer B',
          },
        ],
        count: 2,
      };

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      // The data should remain in the same order since it's already sorted
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });

    it('should test both branches of sorting comparison for array data', async () => {
      const mockInputData = {
        params: {
          pageNo: '1',
          pageSize: '10',
        },
        body: {
          isFilter: true,
        },
        query: {
          projectId: 1,
        },
      };

      const mockEquipmentData = [
        {
          id: 1,
          equipmentName: 'Alpha Equipment',
          EquipmentManufacturer: 'Manufacturer A',
        },
        {
          id: 2,
          equipmentName: 'Beta Equipment',
          EquipmentManufacturer: 'Manufacturer B',
        },
      ];

      mockModels.EquipmentLog.getAll.mockResolvedValue(mockEquipmentData);

      await equipmentLogService.listEquipmentLog(mockInputData, mockDone);

      // The data should remain in the same order since it's already sorted
      expect(mockDone).toHaveBeenCalledWith(mockEquipmentData, false);
    });
  });
});
