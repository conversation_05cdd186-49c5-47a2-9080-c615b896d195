// Mock all service modules before importing the index
jest.mock('../authService', () => ({ name: 'authService', mock: true }));
jest.mock('../stripeService', () => ({ name: 'stripeService', mock: true }));
jest.mock('../projectService', () => ({ name: 'projectService', mock: true }));
jest.mock('../equipmentService', () => ({ name: 'equipmentService', mock: true }));
jest.mock('../gateService', () => ({ name: 'gateService', mock: true }));
jest.mock('../companyService', () => ({ name: 'companyService', mock: true }));
jest.mock('../memberService', () => ({ name: 'memberService', mock: true }));
jest.mock('../deliveryService', () => ({ name: 'deliveryService', mock: true }));
jest.mock('../inspectionService', () => ({ name: 'inspectionService', mock: true }));
jest.mock('../overRideService', () => ({ name: 'overRideService', mock: true }));
jest.mock('../restrictMailService', () => ({ name: 'restrictMailService', mock: true }));
jest.mock('../addressService', () => ({ name: 'addressService', mock: true }));
jest.mock('../defineService', () => ({ name: 'defineService', mock: true }));
jest.mock('../voidService', () => ({ name: 'voidService', mock: true }));
jest.mock('../commentService', () => ({ name: 'commentService', mock: true }));
jest.mock('../attachementService', () => ({ name: 'attachementService', mock: true }));
jest.mock('../historyService', () => ({ name: 'historyService', mock: true }));
jest.mock('../cornService', () => ({ name: 'cornService', mock: true }));
jest.mock('../calendarService', () => ({ name: 'calendarService', mock: true }));
jest.mock('../notificationService', () => ({ name: 'notificationService', mock: true }));
jest.mock('../adminService', () => ({ name: 'adminService', mock: true }));
jest.mock('../accountCornService', () => ({ name: 'accountCornService', mock: true }));
jest.mock('../dashboardService', () => ({ name: 'dashboardService', mock: true }));
jest.mock('../deviceTokenService', () => ({ name: 'deviceTokenService', mock: true }));
jest.mock('../billingService', () => ({ name: 'billingService', mock: true }));
jest.mock('../accountService', () => ({ name: 'accountService', mock: true }));
jest.mock('../craneRequestService', () => ({ name: 'craneRequestService', mock: true }));
jest.mock('../craneRequestAttachmentService', () => ({ name: 'craneRequestAttachmentService', mock: true }));
jest.mock('../craneRequestCommentService', () => ({ name: 'craneRequestCommentService', mock: true }));
jest.mock('../craneRequestHistoryService', () => ({ name: 'craneRequestHistoryService', mock: true }));
jest.mock('../calendarSettingsService', () => ({ name: 'calendarSettingsService', mock: true }));
jest.mock('../notificationPreferenceService', () => ({ name: 'notificationPreferenceService', mock: true }));
jest.mock('../concreteRequestService', () => ({ name: 'concreteRequestService', mock: true }));
jest.mock('../concreteRequestAttachmentService', () => ({ name: 'concreteRequestAttachmentService', mock: true }));
jest.mock('../concreteRequestCommentService', () => ({ name: 'concreteRequestCommentService', mock: true }));
jest.mock('../concreteRequestHistoryService', () => ({ name: 'concreteRequestHistoryService', mock: true }));
jest.mock('../deliveryreportService', () => ({ name: 'deliveryReportService', mock: true }));
jest.mock('../craneReportService', () => ({ name: 'craneReportService', mock: true }));
jest.mock('../concreteReportService', () => ({ name: 'concreteReportService', mock: true }));
jest.mock('../pdfDeliveryReportService', () => ({ name: 'pdfDeliveryReportService', mock: true }));
jest.mock('../pdfCraneReportService', () => ({ name: 'pdfCraneReportService', mock: true }));
jest.mock('../pdfConcreteReportService', () => ({ name: 'pdfConcreteReportService', mock: true }));
jest.mock('../csvDeliveryReportService', () => ({ name: 'csvDeliveryReportService', mock: true }));
jest.mock('../csvCraneReportService', () => ({ name: 'csvCraneReportService', mock: true }));
jest.mock('../csvConcreteReportService', () => ({ name: 'csvConcreteReportService', mock: true }));
jest.mock('../excelDeliveryReportService', () => ({ name: 'excelDeliveryReportService', mock: true }));
jest.mock('../excelCraneReportService', () => ({ name: 'excelCraneReportService', mock: true }));
jest.mock('../excelConcreteReportService', () => ({ name: 'excelConcreteReportService', mock: true }));
jest.mock('../excelWeeklyCalendarService', () => ({ name: 'excelWeeklyCalendarService', mock: true }));
jest.mock('../projectSettingsService', () => ({ name: 'projectSettingsService', mock: true }));
jest.mock('../locationService', () => ({ name: 'locationService', mock: true }));
jest.mock('../puppeteerService', () => ({ name: 'puppeteerService', mock: true }));
jest.mock('../bookingTemplatesService', () => ({ name: 'bookingTemplatesService', mock: true }));
jest.mock('../inspectionReportService', () => ({ name: 'inspectionReportService', mock: true }));
jest.mock('../pdfInspectionReportService', () => ({ name: 'pdfInspectionReportService', mock: true }));
jest.mock('../excelInspectionReportService', () => ({ name: 'excelInspectionReportService', mock: true }));
jest.mock('../csvInspectionReportService', () => ({ name: 'csvInspectionReportService', mock: true }));

// Expected services for validation
const expectedServices = [
  'authService', 'stripeService', 'projectService', 'equipmentService', 'gateService',
  'companyService', 'memberService', 'deliveryService', 'inspectionService', 'overRideService',
  'restrictMailService', 'addressService', 'defineService', 'voidService', 'commentService',
  'attachementService', 'historyService', 'cornService', 'calendarService', 'notificationService',
  'adminService', 'accountCornService', 'dashboardService', 'deviceTokenService', 'billingService',
  'accountService', 'craneRequestService', 'craneRequestAttachmentService', 'craneRequestCommentService',
  'craneRequestHistoryService', 'calendarSettingsService', 'notificationPreferenceService',
  'concreteRequestService', 'concreteRequestAttachmentService', 'concreteRequestCommentService',
  'concreteRequestHistoryService', 'deliveryReportService', 'craneReportService', 'concreteReportService',
  'pdfDeliveryReportService', 'pdfCraneReportService', 'pdfConcreteReportService',
  'csvDeliveryReportService', 'csvCraneReportService', 'csvConcreteReportService',
  'excelDeliveryReportService', 'excelCraneReportService', 'excelConcreteReportService',
  'excelWeeklyCalendarService', 'projectSettingsService', 'locationService', 'puppeteerService',
  'bookingTemplatesService', 'inspectionReportService', 'pdfInspectionReportService',
  'excelInspectionReportService', 'csvInspectionReportService'
];

describe('Services Index Module', () => {
  let servicesIndex;

  beforeEach(() => {
    // Clear module cache to ensure fresh imports
    jest.resetModules();
    // Import the index module after mocking
    servicesIndex = require('../index');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Module Structure', () => {
    test('should export an object', () => {
      expect(servicesIndex).toBeDefined();
      expect(typeof servicesIndex).toBe('object');
      expect(servicesIndex).not.toBeNull();
    });

    test('should export all expected services', () => {
      expectedServices.forEach(serviceName => {
        expect(servicesIndex).toHaveProperty(serviceName);
      });
    });

    test('should have exactly the expected number of exported services', () => {
      const exportedKeys = Object.keys(servicesIndex);
      expect(exportedKeys).toHaveLength(expectedServices.length);
    });
  });

  describe('Service Exports Integrity', () => {
    test('should export correct authService', () => {
      expect(servicesIndex.authService).toBeDefined();
      expect(servicesIndex.authService.name).toBe('authService');
      expect(servicesIndex.authService.mock).toBe(true);
    });

    test('should export correct stripeService', () => {
      expect(servicesIndex.stripeService).toBeDefined();
      expect(servicesIndex.stripeService.name).toBe('stripeService');
      expect(servicesIndex.stripeService.mock).toBe(true);
    });

    test('should export correct projectService', () => {
      expect(servicesIndex.projectService).toBeDefined();
      expect(servicesIndex.projectService.name).toBe('projectService');
      expect(servicesIndex.projectService.mock).toBe(true);
    });

    test('should export correct equipmentService', () => {
      expect(servicesIndex.equipmentService).toBeDefined();
      expect(servicesIndex.equipmentService.name).toBe('equipmentService');
      expect(servicesIndex.equipmentService.mock).toBe(true);
    });

    test('should export correct gateService', () => {
      expect(servicesIndex.gateService).toBeDefined();
      expect(servicesIndex.gateService.name).toBe('gateService');
      expect(servicesIndex.gateService.mock).toBe(true);
    });

    test('should export correct companyService', () => {
      expect(servicesIndex.companyService).toBeDefined();
      expect(servicesIndex.companyService.name).toBe('companyService');
      expect(servicesIndex.companyService.mock).toBe(true);
    });

    test('should export correct memberService', () => {
      expect(servicesIndex.memberService).toBeDefined();
      expect(servicesIndex.memberService.name).toBe('memberService');
      expect(servicesIndex.memberService.mock).toBe(true);
    });

    test('should export correct deliveryService', () => {
      expect(servicesIndex.deliveryService).toBeDefined();
      expect(servicesIndex.deliveryService.name).toBe('deliveryService');
      expect(servicesIndex.deliveryService.mock).toBe(true);
    });

    test('should export correct inspectionService', () => {
      expect(servicesIndex.inspectionService).toBeDefined();
      expect(servicesIndex.inspectionService.name).toBe('inspectionService');
      expect(servicesIndex.inspectionService.mock).toBe(true);
    });

    test('should export correct overRideService', () => {
      expect(servicesIndex.overRideService).toBeDefined();
      expect(servicesIndex.overRideService.name).toBe('overRideService');
      expect(servicesIndex.overRideService.mock).toBe(true);
    });

    test('should export all remaining core services correctly', () => {
      const coreServices = [
        'restrictMailService', 'addressService', 'defineService', 'voidService',
        'commentService', 'attachementService', 'historyService', 'cornService',
        'calendarService', 'notificationService', 'adminService', 'accountCornService',
        'dashboardService', 'deviceTokenService', 'billingService', 'accountService'
      ];

      coreServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export all crane request services correctly', () => {
      const craneServices = [
        'craneRequestService', 'craneRequestAttachmentService',
        'craneRequestCommentService', 'craneRequestHistoryService'
      ];

      craneServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export all concrete request services correctly', () => {
      const concreteServices = [
        'concreteRequestService', 'concreteRequestAttachmentService',
        'concreteRequestCommentService', 'concreteRequestHistoryService'
      ];

      concreteServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export all settings services correctly', () => {
      const settingsServices = [
        'calendarSettingsService', 'notificationPreferenceService'
      ];

      settingsServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export all report services correctly', () => {
      const reportServices = [
        'deliveryReportService', 'craneReportService', 'concreteReportService'
      ];

      reportServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export correct PDF report services', () => {
      const pdfServices = [
        'pdfDeliveryReportService', 'pdfCraneReportService',
        'pdfConcreteReportService', 'pdfInspectionReportService'
      ];

      pdfServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export correct CSV report services', () => {
      const csvServices = [
        'csvDeliveryReportService', 'csvCraneReportService',
        'csvConcreteReportService', 'csvInspectionReportService'
      ];

      csvServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export correct Excel report services', () => {
      const excelServices = [
        'excelDeliveryReportService', 'excelCraneReportService',
        'excelConcreteReportService', 'excelWeeklyCalendarService',
        'excelInspectionReportService'
      ];

      excelServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });

    test('should export correct remaining services', () => {
      const remainingServices = [
        'projectSettingsService', 'locationService', 'puppeteerService',
        'bookingTemplatesService', 'inspectionReportService'
      ];

      remainingServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toBeDefined();
        expect(servicesIndex[serviceName].name).toBe(serviceName);
        expect(servicesIndex[serviceName].mock).toBe(true);
      });
    });
  });

  describe('Module Properties', () => {
    test('should not have undefined exports', () => {
      Object.keys(servicesIndex).forEach(key => {
        expect(servicesIndex[key]).toBeDefined();
        expect(servicesIndex[key]).not.toBeNull();
      });
    });

    test('should have all services as objects', () => {
      Object.keys(servicesIndex).forEach(key => {
        expect(typeof servicesIndex[key]).toBe('object');
      });
    });

    test('should maintain service identity', () => {
      // Test that each service maintains its mock identity
      expectedServices.forEach(serviceName => {
        expect(servicesIndex[serviceName]).toHaveProperty('mock', true);
        expect(servicesIndex[serviceName]).toHaveProperty('name', serviceName);
      });
    });
  });

  describe('Module Completeness', () => {
    test('should not export any extra properties', () => {
      const exportedKeys = Object.keys(servicesIndex);

      exportedKeys.forEach(key => {
        expect(expectedServices).toContain(key);
      });
    });

    test('should not miss any expected services', () => {
      const exportedKeys = Object.keys(servicesIndex);

      expectedServices.forEach(key => {
        expect(exportedKeys).toContain(key);
      });
    });

    test('should have consistent service naming', () => {
      Object.keys(servicesIndex).forEach(key => {
        expect(key).toMatch(/Service$/);
      });
    });
  });

  describe('Import Behavior', () => {
    test('should handle module re-import correctly', () => {
      const firstImport = require('../index');
      const secondImport = require('../index');

      expect(firstImport).toBe(secondImport);
      expect(Object.keys(firstImport)).toEqual(Object.keys(secondImport));
    });

    test('should maintain service references across imports', () => {
      const firstImport = require('../index');
      const secondImport = require('../index');

      expectedServices.forEach(serviceName => {
        expect(firstImport[serviceName]).toBe(secondImport[serviceName]);
      });
    });
  });

  describe('Service Categories', () => {
    test('should export core services', () => {
      const coreServices = ['authService', 'projectService', 'memberService', 'companyService'];
      coreServices.forEach(service => {
        expect(servicesIndex).toHaveProperty(service);
        expect(servicesIndex[service]).toBeDefined();
      });
    });

    test('should export request services', () => {
      const requestServices = ['deliveryService', 'craneRequestService', 'concreteRequestService', 'inspectionService'];
      requestServices.forEach(service => {
        expect(servicesIndex).toHaveProperty(service);
        expect(servicesIndex[service]).toBeDefined();
      });
    });

    test('should export report services', () => {
      const reportServices = ['deliveryReportService', 'craneReportService', 'concreteReportService', 'inspectionReportService'];
      reportServices.forEach(service => {
        expect(servicesIndex).toHaveProperty(service);
        expect(servicesIndex[service]).toBeDefined();
      });
    });

    test('should export utility services', () => {
      const utilityServices = ['notificationService', 'calendarService', 'locationService', 'puppeteerService'];
      utilityServices.forEach(service => {
        expect(servicesIndex).toHaveProperty(service);
        expect(servicesIndex[service]).toBeDefined();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle property access gracefully', () => {
      expect(() => {
        const service = servicesIndex.authService;
        const name = service.name;
        const mock = service.mock;
      }).not.toThrow();
    });

    test('should handle iteration over services', () => {
      expect(() => {
        for (const serviceName in servicesIndex) {
          const service = servicesIndex[serviceName];
          expect(service).toBeDefined();
        }
      }).not.toThrow();
    });

    test('should handle Object.keys() operation', () => {
      expect(() => {
        const keys = Object.keys(servicesIndex);
        expect(keys.length).toBeGreaterThan(0);
      }).not.toThrow();
    });

    test('should handle Object.values() operation', () => {
      expect(() => {
        const values = Object.values(servicesIndex);
        expect(values.length).toBeGreaterThan(0);
        values.forEach(service => {
          expect(service).toBeDefined();
        });
      }).not.toThrow();
    });

    test('should handle destructuring assignment', () => {
      expect(() => {
        const { authService, projectService, memberService } = servicesIndex;
        expect(authService).toBeDefined();
        expect(projectService).toBeDefined();
        expect(memberService).toBeDefined();
      }).not.toThrow();
    });

    test('should handle JSON serialization', () => {
      expect(() => {
        const serialized = JSON.stringify(servicesIndex);
        expect(serialized).toBeDefined();
        expect(typeof serialized).toBe('string');
      }).not.toThrow();
    });
  });

  describe('Service Validation', () => {
    test('should validate all services have expected structure', () => {
      Object.keys(servicesIndex).forEach(serviceName => {
        const service = servicesIndex[serviceName];
        expect(service).toHaveProperty('name');
        expect(service).toHaveProperty('mock');
        expect(service.name).toBe(serviceName);
        expect(service.mock).toBe(true);
      });
    });

    test('should validate service count matches expected', () => {
      const expectedCount = 53; // Total number of services in the index
      expect(Object.keys(servicesIndex)).toHaveLength(expectedCount);
    });

    test('should validate no circular references', () => {
      expect(() => {
        JSON.stringify(servicesIndex);
      }).not.toThrow();
    });

    test('should validate all services are truthy', () => {
      Object.values(servicesIndex).forEach(service => {
        expect(service).toBeTruthy();
      });
    });
  });

  describe('Performance and Memory', () => {
    test('should not create excessive object references', () => {
      const keys = Object.keys(servicesIndex);
      const values = Object.values(servicesIndex);

      expect(keys.length).toBe(values.length);
      expect(keys.length).toBeLessThan(100); // Reasonable upper bound
    });

    test('should maintain consistent object identity', () => {
      const firstAccess = servicesIndex.authService;
      const secondAccess = servicesIndex.authService;

      expect(firstAccess).toBe(secondAccess);
    });
  });
});
