const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {


  const InspectionRequest = sequelize.define(
    'InspectionRequest',
    {
      description: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      CompanyId: DataTypes.INTEGER,
      escort: DataTypes.BOOLEAN,
      notes: DataTypes.STRING,
      InspectionId: DataTypes.INTEGER,
      publicSchemaId: DataTypes.INTEGER,
      status: {
        type: DataTypes.ENUM,
        values: ['Pending', 'Approved', 'Declined', 'Delivered', 'Expired'],
      },
      approved_at: DataTypes.DATE,
      approvedBy: DataTypes.INTEGER,
      inspectionStart: DataTypes.DATE,
      inspectionEnd: DataTypes.DATE,
      ProjectId: DataTypes.INTEGER,
      vehicleDetails: DataTypes.STRING,
      createdBy: DataTypes.INTEGER,
      isQueued: DataTypes.BOOLEAN,
      isAllDetailsFilled: DataTypes.BOOLEAN,
      cranePickUpLocation: DataTypes.STRING,
      craneDropOffLocation: DataTypes.STRING,
      isAssociatedWithCraneRequest: DataTypes.BOOLEAN,
      CraneRequestId: DataTypes.INTEGER,
      requestType: DataTypes.STRING,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      isCreatedByGuestUser: DataTypes.BOOLEAN,
      inspectionType: DataTypes.STRING,
      OriginationAddress: DataTypes.TEXT,
      vehicleType: DataTypes.TEXT,
      inspectionStatus: DataTypes.STRING,
    },
    {},
  );

  InspectionRequest.associate = (models) => {
    InspectionRequest.hasMany(models.InspectionPerson, {
      as: 'memberDetails',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.InspectionPerson, {
      as: 'memberEditData',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    InspectionRequest.belongsTo(models.Project);
    InspectionRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    InspectionRequest.hasMany(models.InspectionCompany, {
      as: 'companyDetails',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.InspectionCompany, {
      as: 'companyEditData',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.InspectionGate, {
      as: 'gateDetails',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.InspectionEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.DeliverDefine, {
      as: 'defineWorkDetails',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.DeliverDefine, {
      as: 'defineEditData',
      foreignKey: 'InspectionId',
    });
    InspectionRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'InspectionRequestId',
    });
    InspectionRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    InspectionRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
    InspectionRequest.hasMany(models.InspectionComment, {
      as: 'comments',
      foreignKey: 'InspectionRequestId',
    });
    InspectionRequest.hasMany(models.InspectionHistory, {
      as: 'history',
      foreignKey: 'InspectionRequestId',
    });
  };

  InspectionRequest.createInstance = async (paramData) => {
    const newInspectionRequest = await InspectionRequest.create(paramData);
    return newInspectionRequest;
  };
  InspectionRequest.getAll = async ({
    attr,
    roleId,
    memberId,
    limit,
    offset,
    searchCondition,
    order,
    sort,
    sortColumn
  }) => {
    const orderType = order || sort || 'DESC';
    let orderQuery = [['id', 'DESC']];
    if (sortColumn === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${orderType}`]];
    }
    if (sortColumn === 'gate') {
      orderQuery = [['gateDetails', 'Gate', 'gateName', `${orderType}`]];
    }
    if (sortColumn === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${orderType}`]];
    }
    if (sortColumn === 'company') {
      orderQuery = [['companyDetails', 'Company', 'companyName', `${orderType}`]];
    }

    if (sortColumn === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', `${orderType}`]];
    }
    if (
      sortColumn === 'description' ||
      sortColumn === 'inspectionStart' ||
      sortColumn === 'id' ||
      sortColumn === 'status' ||
      sortColumn === 'inspectionStatus' ||
      sortColumn === 'inspectionType'
    ) {
      orderQuery = [[`${sortColumn}`, `${orderType}`]];
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newInspectionRequest = await InspectionRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'InspectionRequestId'],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'status',
        'notes',
        'InspectionId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        'inspectionStatus',
        'inspectionType',
      ],
      order: orderQuery,
    });
    return newInspectionRequest;
  };
  InspectionRequest.getCalendarData = async (
    attr,
    roleId,
    memberId,
    searchCondition,
    order,
    sort,
    sortColumn,
  ) => {
    const orderType = order || sort || 'DESC';
    let orderQuery = [['id', 'DESC']];
    if (sortColumn === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${orderType}`]];
    }
    if (sortColumn === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${orderType}`]];
    }
    if (
      sortColumn === 'description' ||
      sortColumn === 'inspectionStart' ||
      sortColumn === 'id' ||
      sortColumn === 'status' ||
      sortColumn === 'inspectionStatus' ||
      sortColumn === 'inspectionType'
    ) {
      orderQuery = [[`${sortColumn}`, `${orderType}`]];
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newInspectionRequest = await InspectionRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'InspectionRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'status',
        'InspectionId',
        'notes',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        'inspectionStatus',
        'inspectionType',
      ],
      order: orderQuery,
    });
    return newInspectionRequest;
  };

  InspectionRequest.getWeeklyCalendarData = async ({
    req,
    attr,
    roleId,
    dateRange: { start, end, startTime, endTime, startDate, endDate },
    typeFormat,
    timezone,
    eventTime: { eventStartTime, eventEndTime }
  }) => {
    let requiredCondition = roleId !== 2;

    let startDate1 = start;
    let endDate1 = end;
    if (req.body.startDate && req.body.endDate) {
      startDate1 = req.body.startDate;
      endDate1 = req.body.endDate;
    }

    const { commonSearch: updatedSearch } = buildTimeBasedSearch(
      startDate1,
      endDate1,
      eventStartTime,
      eventEndTime,
      timezone,
      typeFormat
    );

    const newInspectionRequest = await InspectionRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: buildIncludes(requiredCondition),
      where: { ...attr, ...updatedSearch, isDeleted: false },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'status',
        'InspectionId',
        'notes',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        'inspectionStatus',
        'inspectionType',
      ],
    });
    return newInspectionRequest;
  };

  function buildTimeBasedSearch(startDate1, endDate1, eventStartTime, eventEndTime, timezone, typeFormat) {
    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);
    const nextDay = moment(startDate1).add(1, 'days');
    const queryStartDate = nextDay.format('YYYY-MM-DD');

    if (typeFormat && startDate1) {
      return buildTypeFormatSearch(startDate1, endDate1, eventStartTime, eventEndTime, timezone, finalFromTimeSeconds <= finalToTimeSeconds, queryStartDate);
    }
    return buildDefaultSearch(startDate1, endDate1, eventStartTime, eventEndTime, timezone, finalFromTimeSeconds <= finalToTimeSeconds, queryStartDate);
  }

  function buildTypeFormatSearch(startDate1, endDate1, eventStartTime, eventEndTime, timezone, singleQuery, queryStartDate) {
    const startDateTime = moment(startDate1, 'YYYY-MM-DD').format('YYYY-MM-DD');
    const endDateTime = moment(endDate1, 'YYYY-MM-DD').format('YYYY-MM-DD');

    if (singleQuery) {
      return {
        commonSearch: {
          [Op.and]: [
            sequelize.literal(`(DATE_TRUNC('day', "InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                              AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                              AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
          ],
        }
      };
    }

    return {
      commonSearch: {
        [Op.or]: [
          sequelize.literal(`(DATE_TRUNC('day', "InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
          sequelize.literal(`(DATE_TRUNC('day', "InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
        ],
      }
    };
  }

  function buildDefaultSearch(startDate1, endDate1, eventStartTime, eventEndTime, timezone, singleQuery, queryStartDate) {
    if (singleQuery) {
      return {
        commonSearch: {
          [Op.and]: [
            sequelize.literal(`(DATE_TRUNC('day', "InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                              AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                              AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
          ],
        }
      };
    }

    return {
      commonSearch: {
        [Op.or]: [
          sequelize.literal(`(DATE_TRUNC('day', "InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
          sequelize.literal(`(DATE_TRUNC('day', "InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate1}'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                            AND ("InspectionRequest"."inspectionStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
        ],
      }
    };
  }

  function timeToSeconds(timeString) {
    const [hours, minutes, seconds] = timeString.split(':');
    return +hours * 60 * 60 + +minutes * 60 + +seconds;
  }

  InspectionRequest.getNDRData = async (attr) => {
    const newInspectionRequest = await InspectionRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'voidList',
          required: false,
          attributes: ['id', 'MemberId', 'ProjectId', 'InspectionRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'status',
        'notes',
        'InspectionId',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        'inspectionType',
        'OriginationAddress',
        'vehicleType',
        'inspectionStatus',
      ],
    });
    return newInspectionRequest;
  };
  InspectionRequest.updateInstance = async (id, args) => {
    const updatedInspectionRequest = await InspectionRequest.update(args, { where: { id } });
    return updatedInspectionRequest;
  };
  InspectionRequest.getCraneAssociatedRequest = async ({
    req,
    roleId,
    attr,
    filters: {
      descriptionFilter,
      startdate,
      enddate,
      companyFilter,
      memberFilter,
      equipmentFilter,
      statusFilter,
      idFilter,
      pickFrom,
      pickTo,
      search,
      gateFilter,
      order,
      voidType,
      dateFilter
    }
  }) => {
    const locationFilter = req.body.locationFilter;
    const baseSearch = buildBaseSearch(attr, voidType);
    const searchConditions = buildSearchConditions({
      descriptionFilter,
      pickFrom,
      pickTo,
      memberFilter,
      equipmentFilter,
      locationFilter,
      companyFilter,
      dateFilter,
      req,
      statusFilter,
      idFilter,
      startdate,
      enddate,
      gateFilter,
      search
    });

    let requiredCondition = roleId !== 2;

    const newInspectionRequest = await InspectionRequest.findAll({
      subQuery: false,
      distinct: true,
      required: false,
      include: buildIncludes(requiredCondition),
      where: { ...baseSearch, ...searchConditions },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'status',
        'notes',
        'InspectionId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newInspectionRequest;
  };

  function buildIncludes(requiredCondition) {
    return [
      {
        required: requiredCondition,
        association: 'memberDetails',
        where: { isDeleted: false, isActive: true },
        attributes: ['id'],
        include: [
          {
            association: 'Member',
            attributes: ['id', 'UserId', 'isGuestUser'],
            include: [
              {
                association: 'User',
                attributes: ['id', 'email', 'firstName', 'lastName'],
              },
            ],
          },
        ],
      },
      {
        association: 'companyDetails',
        required: false,
        where: { isDeleted: false },
        attributes: ['id'],
        include: [
          {
            association: 'Company',
            attributes: ['companyName', 'id'],
          },
        ],
      },
      {
        association: 'Project',
        attributes: ['projectName'],
      },
      {
        association: 'createdUserDetails',
        required: false,
        attributes: ['id', 'RoleId'],
        include: [
          {
            association: 'User',
            attributes: ['email', 'id', 'firstName', 'lastName'],
          },
        ],
      },
      {
        association: 'approverDetails',
        attributes: ['id'],
        include: [
          {
            association: 'User',
            attributes: ['email', 'firstName', 'lastName'],
          },
        ],
      },
      {
        association: 'gateDetails',
        where: { isDeleted: false, isActive: true },
        required: false,
        attributes: ['id'],
        include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
      },
      {
        association: 'equipmentDetails',
        where: { isDeleted: false, isActive: true },
        attributes: ['id'],
        include: [
          {
            include: [
              {
                required: false,
                where: { isDeleted: false, isActive: true, isCraneType: true },
                association: 'PresetEquipmentType',
                attributes: ['id', 'equipmentType', 'isCraneType'],
              },
            ],
            association: 'Equipment',
            attributes: ['equipmentName', 'id'],
          },
        ],
      },
      {
        association: 'voidList',
        attributes: ['id', 'MemberId', 'ProjectId', 'InspectionRequestId'],
      },
      {
        association: 'recurrence',
        required: false,
        attributes: [
          'id',
          'recurrence',
          'recurrenceStartDate',
          'recurrenceEndDate',
          'dateOfMonth',
          'monthlyRepeatType',
          'repeatEveryCount',
          'days',
          'requestType',
          'repeatEveryType',
          'chosenDateOfMonth',
          'createdBy',
          'chosenDateOfMonthValue',
        ],
      },
      {
        association: 'location',
        required: false,
      },
    ];
  }

  InspectionRequest.getSingleInspectionRequestData = async (attr) => {
    const newInspectionRequest = await InspectionRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'InspectionId',
        'status',
        'notes',
        'CraneRequestId',
        'ProjectId',
        'CraneRequestId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newInspectionRequest;
  };
  InspectionRequest.upcomingInspectionRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      inspectionStart: { [Op.gt]: new Date() },
      requestType: 'inspectionRequestWithCrane',
      isAssociatedWithCraneRequest: true,
      ...condition,
    };
    const InspectionRequestData = await InspectionRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'InspectionId',
        'status',
        'notes',
        'CraneRequestId',
        'ProjectId',
        'CraneRequestId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        'OriginationAddress',
        'vehicleType',
      ],
      order: [['inspectionStart', 'ASC']],
    });
    return InspectionRequestData;
  };
  InspectionRequest.guestGetNDRData = async (attr) => {
    const newInspectionRequest = await InspectionRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'voidList',
          required: false,
          attributes: ['id', 'MemberId', 'ProjectId', 'InspectionRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
        },
        {
          association: 'comments',
          required: false,
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
        {
          association: 'history',
          required: false,
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'inspectionStart',
        'inspectionEnd',
        'status',
        'notes',
        'InspectionId',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newInspectionRequest;
  };
  return InspectionRequest;
};

function buildBaseSearch(attr, voidType) {
  const baseSearch = {
    ...attr,
    isDeleted: false,
    isQueued: false,
  };
  if (+voidType === 0) {
    baseSearch.isAssociatedWithCraneRequest = true;
  }
  return baseSearch;
}

function buildSearchConditions({
  descriptionFilter,
  pickFrom,
  pickTo,
  memberFilter,
  equipmentFilter,
  locationFilter,
  companyFilter,
  dateFilter,
  req,
  statusFilter,
  idFilter,
  startdate,
  enddate,
  gateFilter,
  search
}) {
  const conditions = {};

  if (descriptionFilter) {
    conditions.description = { [Sequelize.Op.iLike]: `%${descriptionFilter}%` };
  }

  if (pickFrom) {
    conditions.cranePickUpLocation = { [Sequelize.Op.iLike]: `%${pickFrom}%` };
  }

  if (pickTo) {
    conditions.craneDropOffLocation = { [Sequelize.Op.iLike]: `%${pickTo}%` };
  }

  if (memberFilter > 0) {
    conditions['$memberDetails.Member.id$'] = +memberFilter;
  }

  if (equipmentFilter) {
    conditions['$equipmentDetails.Equipment.equipmentName$'] = { [Sequelize.Op.iLike]: equipmentFilter };
  }

  if (locationFilter) {
    conditions['$location.locationPath$'] = { [Sequelize.Op.iLike]: locationFilter };
  }

  if (typeof companyFilter === 'string' && companyFilter !== '') {
    conditions['$companyDetails.Company.companyName$'] = { [Sequelize.Op.iLike]: companyFilter };
  }

  if (dateFilter) {
    const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(req.headers.timezoneoffset), true);
    const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(req.headers.timezoneoffset), true);
    conditions.inspectionStart = { [Op.between]: [moment(startDateTime), moment(endDateTime)] };
  }

  if (statusFilter) {
    conditions.status = statusFilter;
  }

  if (idFilter) {
    conditions.CraneRequestId = idFilter;
  }

  if (startdate) {
    const startDateTime = moment(startdate, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(req.headers.timezoneoffset), true);
    const endDateTime = moment(enddate, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(req.headers.timezoneoffset), true);
    conditions.inspectionStart = { [Op.between]: [moment(startDateTime), moment(endDateTime)] };
  }

  if (gateFilter > 0) {
    conditions['$gateDetails.Gate.id$'] = +gateFilter;
  }

  if (search) {
    conditions[Op.or] = [
      { description: { [Sequelize.Op.iLike]: `%${search}%` } },
      { cranePickUpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
      { craneDropOffLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
      { '$equipmentDetails.Equipment.equipmentName$': { [Sequelize.Op.iLike]: `%${search}%` } },
      { '$location.locationPath$': { [Sequelize.Op.iLike]: `%${search}%` } }
    ];
  }

  return Object.keys(conditions).length > 0 ? { [Op.and]: [{ ...conditions }] } : {};
}
