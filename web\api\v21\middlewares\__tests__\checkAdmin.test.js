const Sequelize = require('sequelize');
const { isAdmin, isProjectAdmin, isAccountAdmin, isProjectAdminOnly } = require('../checkAdmin');
const ApiError = require('../../helpers/apiError');
const helper = require('../../helpers/domainHelper');
const enterpriseHelper = require('../../helpers/enterpriseCheckHelper');

jest.mock('../../helpers/domainHelper');
jest.mock('../../helpers/enterpriseCheckHelper');
// Mock Sequelize at the module level
jest.mock('sequelize', () => {
  const actualSequelize = jest.requireActual('sequelize');
  return {
    ...actualSequelize,
    and: jest.fn((...args) => args),
    or: jest.fn((...args) => args),
    where: jest.fn((...args) => args),
    fn: jest.fn((...args) => args),
    col: jest.fn((...args) => args),
  };
});

jest.mock('../../models', () => ({
  User: {
    findOne: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  Sequelize: {
    and: jest.fn((...args) => args),
    or: jest.fn((...args) => args),
    where: jest.fn((...args) => args),
    fn: jest.fn((...args) => args),
    col: jest.fn((...args) => args),
    Op: {
      and: Symbol('and'),
      ne: Symbol('ne'),
    },
  },
  sequelize: {
    and: jest.fn((...args) => args),
  },
}));

const { User, Member, sequelize } = require('../../models');

// Helper to reset mocks
function resetMocks() {
  User.findOne.mockReset();
  Member.findOne.mockReset();
  helper.getDynamicModel.mockReset();
  enterpriseHelper.checkEnterPrise.mockReset();
  sequelize.and.mockReset();
}

describe('checkAdmin middleware', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    req = {
      user: { id: 1, email: '<EMAIL>', domainName: 'testdomain' },
      body: {},
      params: {},
      query: {},
    };
    res = {};
    next = jest.fn();
    resetMocks();
    helper.getDynamicModel.mockResolvedValue({ User, Member });
    enterpriseHelper.checkEnterPrise.mockResolvedValue('testdomain');

    // Mock the User.findOne call in getDynamicModel
    User.findOne.mockResolvedValue({
      id: 1,
      email: '<EMAIL>',
      isDeleted: false,
      userType: 'user'
    });
  });

  describe('isAdmin', () => {
    it('should call next if user is super admin', async () => {
      // Mock the second User.findOne call (the admin check)
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      }).mockResolvedValueOnce({ id: 1, userType: 'super admin' });

      await isAdmin(req, res, next);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next if user is folloit admin', async () => {
      // Mock the second User.findOne call (the admin check)
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      }).mockResolvedValueOnce({ id: 1, userType: 'folloit admin' });

      await isAdmin(req, res, next);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next with error if user is not admin', async () => {
      // Mock the second User.findOne call (the admin check)
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      }).mockResolvedValueOnce(null);

      await isAdmin(req, res, next);
      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      expect(next.mock.calls[0][0].message).toBe('Access Forbidden');
    });
  });

  describe('isAccountAdmin', () => {
    it('should call next if user exists', async () => {
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      }).mockResolvedValueOnce({ id: 1, isDeleted: false });

      await isAccountAdmin(req, res, next);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next with error if user does not exist', async () => {
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      }).mockResolvedValueOnce(null);

      await isAccountAdmin(req, res, next);
      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      expect(next.mock.calls[0][0].message).toBe('Access Forbidden');
    });
  });

  describe('isProjectAdminOnly', () => {
    it('should call next if user is project admin (RoleId 2)', async () => {
      req.query = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 2 });

      await isProjectAdminOnly(req, res, next);
      // next() is called inside callback
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next with error if user is not project admin', async () => {
      req.query = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 5 });

      await isProjectAdminOnly(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      expect(next.mock.calls[0][0].message).toBe('Access Forbidden');
    });

    it('should call next with error if member not found', async () => {
      req.query = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce(null);

      await isProjectAdminOnly(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      expect(next.mock.calls[0][0].message).toBe('Access Forbidden');
    });

    it('should not call checkProjectAdmin if query is undefined', async () => {
      req.query = undefined;
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });

      await isProjectAdminOnly(req, res, next);
      expect(Member.findOne).not.toHaveBeenCalled();
    });

    it('should not call checkProjectAdmin if ProjectId is undefined', async () => {
      req.query = { ProjectId: undefined };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });

      await isProjectAdminOnly(req, res, next);
      expect(Member.findOne).not.toHaveBeenCalled();
    });

    it('should not call checkProjectAdmin if ProjectId is null', async () => {
      req.query = { ProjectId: null };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });

      await isProjectAdminOnly(req, res, next);
      expect(Member.findOne).not.toHaveBeenCalled();
    });
  });

  describe('isProjectAdmin', () => {
    it('should call next if user is project admin (params, RoleId 1)', async () => {
      req.params = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 1 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next if user is project admin (params, RoleId 2)', async () => {
      req.params = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 2 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next if user is project admin (params, RoleId 3)', async () => {
      req.params = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 3 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next if user is project admin (body)', async () => {
      req.params = {};
      req.body = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 3 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next if user is project admin (params undefined, body)', async () => {
      req.params = undefined;
      req.body = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 1 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next with error if user is not project admin', async () => {
      req.params = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 5 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      expect(next.mock.calls[0][0].message).toBe('Access Forbidden');
    });

    it('should call next with error if member not found', async () => {
      req.params = { ProjectId: 123 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce(null);

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      expect(next.mock.calls[0][0].message).toBe('Access Forbidden');
    });

    it('should handle params.ProjectId undefined', async () => {
      req.params = { ProjectId: undefined };
      req.body = { ProjectId: 456 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 2 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });

    it('should handle params.ProjectId null', async () => {
      req.params = { ProjectId: null };
      req.body = { ProjectId: 789 };
      // Mock both User.findOne calls
      User.findOne.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        isDeleted: false,
        userType: 'user'
      });
      Member.findOne.mockResolvedValueOnce({ RoleId: 1 });

      await isProjectAdmin(req, res, next);
      await new Promise(setImmediate);
      expect(next).toHaveBeenCalledWith();
    });
  });
});
