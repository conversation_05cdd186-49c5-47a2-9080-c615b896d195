const commentService = require('../craneRequestHistoryService');
const { Enterprise, Sequelize } = require('../../models');
const helper = require('../../helpers/domainHelper');

// Mock the models and helper
jest.mock('../../models', () => ({
    Enterprise: {
        findOne: jest.fn(),
    },
    Sequelize: {
        Op: {
            ne: 'ne',
        },
    },
    CraneRequestHistory: {
        findAll: jest.fn(),
    },
    CraneRequest: {
        findOne: jest.fn(),
    },
    User: {
        findOne: jest.fn(),
    },
}));

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn(),
}));

describe('CraneRequestHistoryService', () => {
    let mockModels;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup default mock models
        mockModels = {
            CraneRequestHistory: {
                findAll: jest.fn(),
            },
            CraneRequest: {
                findOne: jest.fn(),
            },
            User: {
                findOne: jest.fn(),
            },
            Member: {
                findOne: jest.fn(),
            },
        };

        // Setup default helper mock responses
        helper.returnProjectModel.mockResolvedValue({
            Member: mockModels.Member,
            User: mockModels.User,
        });

        helper.getDynamicModel.mockResolvedValue(mockModels);
    });

    describe('getCraneRequestHistories', () => {
        const mockInputData = {
            params: {
                CraneRequestId: '123',
                ProjectId: '456',
            },
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
            body: {
                ParentCompanyId: 789,
            },
        };

        it('should successfully fetch crane request histories', async () => {
            // Mock successful crane request existence
            mockModels.CraneRequest.findOne.mockResolvedValue({ id: 123 });

            // Mock successful history fetch
            const mockHistories = [
                {
                    id: 1,
                    Member: {
                        User: {
                            firstName: 'John',
                            lastName: 'Doe',
                            profilePic: 'pic.jpg',
                        },
                    },
                },
            ];
            mockModels.CraneRequestHistory.findAll.mockResolvedValue(mockHistories);

            const done = jest.fn();
            await commentService.getCraneRequestHistories(mockInputData, done);

            expect(done).toHaveBeenCalledWith(mockHistories, false);
        });

        it('should handle non-existent crane request', async () => {
            mockModels.CraneRequest.findOne.mockResolvedValue(null);

            const done = jest.fn();
            await commentService.getCraneRequestHistories(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Crane Booking id does not exist' });
        });

        it('should handle errors during history fetch', async () => {
            mockModels.CraneRequest.findOne.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();
            await commentService.getCraneRequestHistories(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle errors during getDynamicModel', async () => {
            helper.returnProjectModel.mockRejectedValue(new Error('Helper error'));

            const done = jest.fn();
            await commentService.getCraneRequestHistories(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('getDynamicModel', () => {
        const mockInputData = {
            user: {
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
            body: {
                ParentCompanyId: 789,
            },
        };

        it('should use domain name when available', async () => {
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });

            await commentService.getDynamicModel(mockInputData);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
        });

        it('should fallback to email when domain is not available', async () => {
            const mockInputDataNoDomain = {
                user: {
                    email: '<EMAIL>',
                    domainName: '',
                },
                body: {
                    ParentCompanyId: 789,
                },
            };

            Enterprise.findOne.mockResolvedValue(null);
            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>',
            });
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                isAccount: true,
                EnterpriseId: 123,
            });
            Enterprise.findOne.mockResolvedValueOnce(null).mockResolvedValueOnce({
                id: 123,
                name: 'testenterprise',
                status: 'completed',
            });

            await commentService.getDynamicModel(mockInputDataNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('testenterprise');
        });

        it('should handle parent company fallback when no enterprise found', async () => {
            const mockInputDataNoUser = {
                user: {
                    email: '<EMAIL>',
                },
                body: {
                    ParentCompanyId: 789,
                },
            };

            Enterprise.findOne.mockResolvedValue(null);
            mockModels.User.findOne.mockResolvedValue(null);
            Enterprise.findOne.mockResolvedValueOnce(null).mockResolvedValueOnce({
                id: 789,
                name: 'parentcompany',
                status: 'completed',
            });

            await commentService.getDynamicModel(mockInputDataNoUser);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('parentcompany');
        });

        it('should handle case when domain enterprise is not found', async () => {
            const mockInputDataWithDomain = {
                user: {
                    email: '<EMAIL>',
                    domainName: 'nonexistentdomain',
                },
                body: {
                    ParentCompanyId: 789,
                },
            };

            Enterprise.findOne.mockResolvedValueOnce(null); // Domain enterprise not found
            mockModels.User.findOne.mockResolvedValue({
                id: 1,
                email: '<EMAIL>',
            });
            mockModels.Member.findOne.mockResolvedValue({
                id: 1,
                isAccount: true,
                EnterpriseId: 123,
            });
            Enterprise.findOne.mockResolvedValueOnce({
                id: 123,
                name: 'testenterprise',
                status: 'completed',
            });

            await commentService.getDynamicModel(mockInputDataWithDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('testenterprise');
        });

        it('should handle case when no email is provided', async () => {
            const mockInputDataNoEmail = {
                user: {},
                body: {
                    ParentCompanyId: 789,
                },
            };

            Enterprise.findOne.mockResolvedValueOnce({
                id: 789,
                name: 'parentcompany',
                status: 'completed',
            });

            await commentService.getDynamicModel(mockInputDataNoEmail);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('parentcompany');
        });
    });

    describe('determineEnterpriseValue', () => {
        it('should return enterprise for account member', async () => {
            const mockUser = { id: 1 };
            const mockMember = {
                isAccount: true,
                EnterpriseId: 123,
            };
            const mockEnterprise = {
                id: 123,
                name: 'testenterprise',
                status: 'completed',
            };

            mockModels.Member.findOne.mockResolvedValue(mockMember);
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.determineEnterpriseValue(mockUser, 789);

            expect(result).toEqual(mockEnterprise);
            expect(mockModels.Member.findOne).toHaveBeenCalledWith({
                where: { UserId: 1, RoleId: { ne: 4 }, isDeleted: false },
            });
        });

        it('should check parent company when member is not an account', async () => {
            const mockUser = { id: 1 };
            const mockMember = {
                isAccount: false,
                EnterpriseId: 123,
            };
            const mockEnterprise = {
                id: 789,
                name: 'parentcompany',
                status: 'completed',
            };

            mockModels.Member.findOne.mockResolvedValue(mockMember);
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.determineEnterpriseValue(mockUser, 789);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 789, status: 'completed' },
            });
        });

        it('should check parent company when no user data provided', async () => {
            const mockEnterprise = {
                id: 789,
                name: 'parentcompany',
                status: 'completed',
            };

            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.determineEnterpriseValue(null, 789);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 789, status: 'completed' },
            });
        });

        it('should check parent company when no member data found', async () => {
            const mockUser = { id: 1 };
            const mockEnterprise = {
                id: 789,
                name: 'parentcompany',
                status: 'completed',
            };

            mockModels.Member.findOne.mockResolvedValue(null);
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.determineEnterpriseValue(mockUser, 789);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 789, status: 'completed' },
            });
        });
    });

    describe('updateIncomeUser', () => {
        it('should update user in input data', async () => {
            const mockInputData = {
                user: { email: '<EMAIL>' },
            };
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
            };

            // Mock the User model from the models module, not the dynamic one
            const { User } = require('../../models');
            User.findOne.mockResolvedValue(mockUser);

            await commentService.updateIncomeUser(mockInputData, '<EMAIL>');

            expect(mockInputData.user).toEqual(mockUser);
            expect(User.findOne).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
        });

        it('should handle case when user is not found', async () => {
            const mockInputData = {
                user: { email: '<EMAIL>' },
            };

            const { User } = require('../../models');
            User.findOne.mockResolvedValue(null);

            await commentService.updateIncomeUser(mockInputData, '<EMAIL>');

            expect(mockInputData.user).toBeNull();
        });
    });

    describe('setModels', () => {
        it('should set models correctly', () => {
            const mockModelObj = {
                CraneRequestHistory: { test: 'craneHistory' },
                CraneRequest: { test: 'craneRequest' },
                User: { test: 'user' },
            };

            commentService.setModels(mockModelObj);

            // Since setModels modifies module-level variables, we can't directly test them
            // Instead, we verify the method was called without errors
            expect(() => commentService.setModels(mockModelObj)).not.toThrow();
        });
    });

    describe('checkCraneRequestExistence', () => {
        it('should check if crane request exists', async () => {
            const mockInputData = {
                params: {
                    CraneRequestId: '123',
                    ProjectId: '456',
                },
            };
            const mockCraneRequest = { id: 123, CraneRequestId: '123' };

            mockModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);

            const result = await commentService.checkCraneRequestExistence(mockInputData);

            expect(result).toEqual(mockCraneRequest);
            expect(mockModels.CraneRequest.findOne).toHaveBeenCalledWith({
                where: {
                    CraneRequestId: '123',
                    ProjectId: 456,
                },
            });
        });

        it('should return null when crane request does not exist', async () => {
            const mockInputData = {
                params: {
                    CraneRequestId: '999',
                    ProjectId: '456',
                },
            };

            mockModels.CraneRequest.findOne.mockResolvedValue(null);

            const result = await commentService.checkCraneRequestExistence(mockInputData);

            expect(result).toBeNull();
        });

        it('should handle database errors', async () => {
            const mockInputData = {
                params: {
                    CraneRequestId: '123',
                    ProjectId: '456',
                },
            };

            mockModels.CraneRequest.findOne.mockRejectedValue(new Error('Database error'));

            await expect(commentService.checkCraneRequestExistence(mockInputData)).rejects.toThrow('Database error');
        });
    });

    describe('fetchCraneRequestHistories', () => {
        it('should fetch crane request histories with proper includes', async () => {
            const craneRequestId = 123;
            const mockInputData = {
                params: {
                    ProjectId: '456',
                },
            };
            const mockHistories = [
                {
                    id: 1,
                    CraneRequestId: 123,
                    Member: {
                        User: {
                            firstName: 'John',
                            lastName: 'Doe',
                            profilePic: 'pic.jpg',
                        },
                    },
                },
            ];

            mockModels.CraneRequestHistory.findAll.mockResolvedValue(mockHistories);

            const result = await commentService.fetchCraneRequestHistories(craneRequestId, mockInputData);

            expect(result).toEqual(mockHistories);
            expect(mockModels.CraneRequestHistory.findAll).toHaveBeenCalledWith({
                include: [
                    {
                        association: 'Member',
                        include: [
                            { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
                        ],
                    },
                ],
                where: {
                    CraneRequestId: 123,
                    ProjectId: 456,
                },
                order: [['id', 'DESC']],
            });
        });

        it('should return empty array when no histories found', async () => {
            const craneRequestId = 123;
            const mockInputData = {
                params: {
                    ProjectId: '456',
                },
            };

            mockModels.CraneRequestHistory.findAll.mockResolvedValue([]);

            const result = await commentService.fetchCraneRequestHistories(craneRequestId, mockInputData);

            expect(result).toEqual([]);
        });

        it('should handle database errors during history fetch', async () => {
            const craneRequestId = 123;
            const mockInputData = {
                params: {
                    ProjectId: '456',
                },
            };

            mockModels.CraneRequestHistory.findAll.mockRejectedValue(new Error('Database error'));

            await expect(commentService.fetchCraneRequestHistories(craneRequestId, mockInputData)).rejects.toThrow('Database error');
        });
    });

    describe('returnProjectModel', () => {
        it('should return project model and set public variables', async () => {
            const mockModelData = {
                Member: { test: 'member' },
                User: { test: 'user' },
            };

            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await commentService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle errors from helper', async () => {
            helper.returnProjectModel.mockRejectedValue(new Error('Helper error'));

            await expect(commentService.returnProjectModel()).rejects.toThrow('Helper error');
        });
    });

    describe('getDomainEnterprise', () => {
        it('should find enterprise by domain name', async () => {
            const domainName = 'testdomain';
            const mockEnterprise = { id: 1, name: 'testdomain' };

            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.getDomainEnterprise(domainName);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'testdomain' },
            });
        });

        it('should return null when domain name is not provided', async () => {
            const result = await commentService.getDomainEnterprise(null);

            expect(result).toBeNull();
        });

        it('should return null when domain name is empty string', async () => {
            const result = await commentService.getDomainEnterprise('');

            expect(result).toBeNull();
        });

        it('should handle case insensitive domain names', async () => {
            const domainName = 'TestDomain';
            const mockEnterprise = { id: 1, name: 'testdomain' };

            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.getDomainEnterprise(domainName);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'testdomain' },
            });
        });
    });

    describe('getParentCompanyId', () => {
        it('should return ParentCompanyId from body', () => {
            const mockInputData = {
                body: {
                    ParentCompanyId: 123,
                },
                params: {
                    ParentCompanyId: 456,
                },
            };

            const result = commentService.getParentCompanyId(mockInputData);

            expect(result).toBe(123);
        });

        it('should return ParentCompanyId from params when body is not available', () => {
            const mockInputData = {
                body: {},
                params: {
                    ParentCompanyId: 456,
                },
            };

            const result = commentService.getParentCompanyId(mockInputData);

            expect(result).toBe(456);
        });

        it('should return undefined when ParentCompanyId is not in body or params', () => {
            const mockInputData = {
                body: {},
                params: {},
            };

            const result = commentService.getParentCompanyId(mockInputData);

            expect(result).toBeUndefined();
        });
    });

    describe('getUserData', () => {
        it('should find user by email', async () => {
            const email = '<EMAIL>';
            const mockUser = { id: 1, email: '<EMAIL>' };

            mockModels.User.findOne.mockResolvedValue(mockUser);

            const result = await commentService.getUserData(email);

            expect(result).toEqual(mockUser);
            expect(mockModels.User.findOne).toHaveBeenCalledWith({ where: { email } });
        });

        it('should return null when email is not provided', async () => {
            const result = await commentService.getUserData(null);

            expect(result).toBeNull();
        });

        it('should return null when email is empty string', async () => {
            const result = await commentService.getUserData('');

            expect(result).toBeNull();
        });

        it('should return null when user is not found', async () => {
            const email = '<EMAIL>';

            mockModels.User.findOne.mockResolvedValue(null);

            const result = await commentService.getUserData(email);

            expect(result).toBeNull();
        });
    });

    describe('getMemberData', () => {
        it('should find member by user id with proper conditions', async () => {
            const userId = 1;
            const mockMember = { id: 1, UserId: 1, isAccount: true };

            mockModels.Member.findOne.mockResolvedValue(mockMember);

            const result = await commentService.getMemberData(userId);

            expect(result).toEqual(mockMember);
            expect(mockModels.Member.findOne).toHaveBeenCalledWith({
                where: { UserId: userId, RoleId: { ne: 4 }, isDeleted: false },
            });
        });

        it('should return null when member is not found', async () => {
            const userId = 999;

            mockModels.Member.findOne.mockResolvedValue(null);

            const result = await commentService.getMemberData(userId);

            expect(result).toBeNull();
        });

        it('should handle database errors', async () => {
            const userId = 1;

            mockModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await expect(commentService.getMemberData(userId)).rejects.toThrow('Database error');
        });
    });

    describe('checkEnterpriseByParentCompany', () => {
        it('should find enterprise by parent company id', async () => {
            const parentCompanyId = 123;
            const mockEnterprise = { id: 456, ParentCompanyId: 123, status: 'completed' };

            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.checkEnterpriseByParentCompany(parentCompanyId);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: parentCompanyId, status: 'completed' },
            });
        });

        it('should return null when enterprise is not found', async () => {
            const parentCompanyId = 999;

            Enterprise.findOne.mockResolvedValue(null);

            const result = await commentService.checkEnterpriseByParentCompany(parentCompanyId);

            expect(result).toBeNull();
        });
    });

    describe('fetchEnterprise', () => {
        it('should find enterprise with given where clause', async () => {
            const whereClause = { id: 123, status: 'completed' };
            const mockEnterprise = { id: 123, name: 'test', status: 'completed' };

            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await commentService.fetchEnterprise(whereClause);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({ where: whereClause });
        });

        it('should return null when enterprise is not found', async () => {
            const whereClause = { id: 999 };

            Enterprise.findOne.mockResolvedValue(null);

            const result = await commentService.fetchEnterprise(whereClause);

            expect(result).toBeNull();
        });

        it('should handle database errors', async () => {
            const whereClause = { id: 123 };

            Enterprise.findOne.mockRejectedValue(new Error('Database error'));

            await expect(commentService.fetchEnterprise(whereClause)).rejects.toThrow('Database error');
        });
    });
});