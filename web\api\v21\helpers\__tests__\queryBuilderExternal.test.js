const queryBuilder = require('../queryBuilderExternal');

describe('queryBuilderExternal', () => {
  describe('queryBuilderExternal function', () => {
    const baseInputData = {
      headers: {
        timezoneoffset: '-300'
      }
    };

    const baseIncomeData = {
      timezone: 'UTC',
      startDate: '2023-01-01',
      endDate: '2023-01-02',
      startTime: '08:00:00',
      endTime: '17:00:00',
      startdate: '2023-01-01',
      enddate: '2023-01-02'
    };

    const baseParams = {
      sortOrder: 'ASC',
      ProjectId: '1',
      pageNo: '1',
      pageSize: '10'
    };

    it('should build query for single delivery template type', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }]
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('WITH time_slots AS');
      expect(result).toContain('DeliveryRequests');
      expect(result).toContain('ORDER BY');
      expect(result).toContain('ASC');
    });

    it('should build query for single crane template type', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 1 }]
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('WITH time_slots AS');
      expect(result).toContain('CraneRequests');
      expect(result).toContain('ORDER BY');
    });

    it('should build query for single concrete template type', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 2 }]
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('WITH time_slots AS');
      expect(result).toContain('ConcreteRequests');
      expect(result).toContain('ORDER BY');
    });

    it('should build query for single inspection template type', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 3 }]
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('WITH time_slots AS');
      expect(result).toContain('InspectionRequests');
      expect(result).toContain('ORDER BY');
    });

    it('should throw error for invalid single template type', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 99 }]
      };

      await expect(queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams))
        .rejects.toThrow('please send valid template type');
    });

    it('should build query for multiple template types with union', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }, { id: 1 }],
        gateFilter: 0,
        equipmentFilter: 0,
        defineFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('WITH time_slots AS');
      expect(result).toContain('union all');
      expect(result).toContain('DeliveryRequests');
      expect(result).toContain('CraneRequests');
    });

    it('should skip crane requests when gateFilter is not 0', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }, { id: 1 }],
        gateFilter: 1,
        equipmentFilter: 0,
        defineFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('DeliveryRequests');
      expect(result).not.toContain('union all');
      expect(result).not.toContain('CraneRequests');
    });

    it('should skip concrete requests when filters are not 0', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }, { id: 2 }],
        gateFilter: 1,
        equipmentFilter: 0,
        defineFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('DeliveryRequests');
      expect(result).not.toContain('union all');
      expect(result).not.toContain('ConcreteRequests');
    });

    it('should handle multiple template types with last item filtered out', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }, { id: 1 }],
        gateFilter: 1,
        equipmentFilter: 0,
        defineFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('DeliveryRequests');
      expect(result).not.toContain('union all');
    });

    it('should handle multiple template types with second-to-last item filtered out', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 1 }, { id: 0 }],
        gateFilter: 1,
        equipmentFilter: 0,
        defineFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('DeliveryRequests');
      expect(result).not.toContain('union all');
    });

    it('should throw error for invalid template type length', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: []
      };

      await expect(queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams))
        .rejects.toThrow('please send valid template type or please check template type length');
    });

    it('should throw error for too many template types', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }, { id: 1 }, { id: 2 }, { id: 3 }, { id: 0 }]
      };

      await expect(queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams))
        .rejects.toThrow('please send valid template type or please check template type length');
    });

    it('should handle time range that spans multiple days (singleQuery false)', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }],
        startTime: '22:00:00',
        endTime: '06:00:00'
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('DeliveryRequests');
      expect(result).toContain('23:59:59');
      expect(result).toContain('00:00:00');
    });

    it('should include member joins when memberFilter is set', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }],
        memberFilter: 1
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('JOIN "DeliveryPeople"');
      expect(result).toContain('memberDetails');
    });

    it('should include company joins when companyFilter is set', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }],
        companyFilter: 1
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('JOIN "DeliverCompanies"');
      expect(result).toContain('companyDetails');
    });

    it('should test crane request joins with member and company filters', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 1 }],
        memberFilter: 1,
        companyFilter: 1,
        gateFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('JOIN "CraneRequestResponsiblePeople"');
      expect(result).toContain('JOIN "CraneRequestCompanies"');
    });

    it('should test concrete request joins with member and company filters', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 2 }],
        memberFilter: 1,
        companyFilter: 1,
        gateFilter: 0,
        equipmentFilter: 0,
        defineFilter: 0
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('JOIN ("ConcreteRequestResponsiblePeople"');
      expect(result).toContain('JOIN "ConcreteRequestCompanies"');
    });

    it('should test inspection request joins with member and company filters', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 3 }],
        memberFilter: 1,
        companyFilter: 1
      };

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('JOIN "InspectionPeople"');
      expect(result).toContain('JOIN "InspectionCompanies"');
    });

    it('should handle case where startDate and endDate are not provided in incomeData', async () => {
      const incomeData = {
        ...baseIncomeData,
        templateType: [{ id: 0 }]
      };
      delete incomeData.startDate;
      delete incomeData.endDate;

      const result = await queryBuilder.queryBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result).toContain('DeliveryRequests');
      expect(result).not.toContain('BETWEEN');
    });
  });

  describe('replacementsBuilderExternal function', () => {
    const baseInputData = {
      headers: {
        timezoneoffset: '-300'
      }
    };

    const baseParams = {
      ProjectId: '123',
      pageNo: '2',
      pageSize: '20'
    };

    it('should build basic replacements without filters', async () => {
      const incomeData = {};

      const result = await queryBuilder.replacementsBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result.project_id).toBe(123);
      expect(result.offset).toBe(20); // (2-1) * 20
      expect(result.limit).toBe(20);
    });

    it('should build replacements with date filters', async () => {
      const incomeData = {
        startdate: '2023-01-01',
        enddate: '2023-01-02'
      };

      const result = await queryBuilder.replacementsBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result.project_id).toBe(123);
      expect(result.start_date).toBeDefined();
      expect(result.end_date).toBeDefined();
    });

    it('should build replacements with all filters', async () => {
      const incomeData = {
        startdate: '2023-01-01',
        enddate: '2023-01-02',
        statusFilter: 'Delivered',
        gateFilter: 1,
        equipmentFilter: 2,
        defineFilter: 3,
        memberFilter: 4,
        companyFilter: 5,
        locationFilter: 6
      };

      const result = await queryBuilder.replacementsBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result.project_id).toBe(123);
      expect(result.status).toBe('Delivered');
      expect(result.status_fix).toBe('Completed');
      expect(result.gate).toBe(1);
      expect(result.equipment).toBe(2);
      expect(result.define).toBe(3);
      expect(result.member).toBe(4);
      expect(result.company).toBe(5);
      expect(result.location).toBe(6);
    });

    it('should handle statusFilter with non-Delivered value', async () => {
      const incomeData = {
        statusFilter: 'Pending'
      };

      const result = await queryBuilder.replacementsBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result.status).toBe('Pending');
      expect(result.status_fix).toBe('Pending');
    });

    it('should ignore filters with zero values', async () => {
      const incomeData = {
        statusFilter: '',
        gateFilter: 0,
        equipmentFilter: 0,
        defineFilter: 0,
        memberFilter: 0,
        companyFilter: 0,
        locationFilter: 0
      };

      const result = await queryBuilder.replacementsBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result.status).toBeUndefined();
      expect(result.gate).toBeUndefined();
      expect(result.equipment).toBeUndefined();
      expect(result.define).toBeUndefined();
      expect(result.member).toBeUndefined();
      expect(result.company).toBeUndefined();
      expect(result.location).toBeUndefined();
    });

    it('should handle missing date fields', async () => {
      const incomeData = {
        statusFilter: 'Active'
      };

      const result = await queryBuilder.replacementsBuilderExternal(baseInputData, incomeData, baseParams);

      expect(result.start_date).toBeUndefined();
      expect(result.end_date).toBeUndefined();
    });
  });

  describe('defaultTimeSlots function', () => {
    it('should return object with all time slots initialized to 0', () => {
      const result = queryBuilder.defaultTimeSlots();

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
      expect(result['01 AM']).toBe(0);
      expect(result['02 AM']).toBe(0);
      expect(result['12 PM']).toBe(0);
      expect(result['01 PM']).toBe(0);
      expect(result['11 PM']).toBe(0);
      expect(result['12 AM']).toBe(0);

      // Check that all 24 time slots are present
      const expectedSlots = [
        '01 AM', '02 AM', '03 AM', '04 AM', '05 AM', '06 AM',
        '07 AM', '08 AM', '09 AM', '10 AM', '11 AM', '12 PM',
        '01 PM', '02 PM', '03 PM', '04 PM', '05 PM', '06 PM',
        '07 PM', '08 PM', '09 PM', '10 PM', '11 PM', '12 AM'
      ];

      expectedSlots.forEach(slot => {
        expect(result[slot]).toBe(0);
      });

      expect(Object.keys(result)).toHaveLength(24);
    });
  });

  describe('convertToCron function', () => {
    it('should convert datetime to cron format', () => {
      const dateTime = '2023-01-15 14:30:00';
      const result = queryBuilder.convertToCron(dateTime);

      expect(result).toMatch(/^0 \d+ \d+ \d+ \d+ \*$/);
      expect(result).toContain('0'); // seconds
      expect(result).toContain('*'); // year
    });

    it('should handle different datetime formats', () => {
      const dateTime = '2023-12-25 09:15:45';
      const result = queryBuilder.convertToCron(dateTime);

      expect(result).toMatch(/^0 \d+ \d+ \d+ \d+ \*$/);
    });
  });

  describe('convertToCronYearly function', () => {
    it('should convert yearly recurrence with first occurrence', () => {
      const result = queryBuilder.convertToCronYearly('first', 'MON', 3, 10, 30);

      expect(result).toBe('30 10 * 3 1#1');
    });

    it('should convert yearly recurrence with last occurrence', () => {
      const result = queryBuilder.convertToCronYearly('last', 'FRI', 12, 15, 45);

      expect(result).toBe('45 15 * 12 5L');
    });

    it('should handle all order types', () => {
      expect(queryBuilder.convertToCronYearly('first', 'SUN', 1, 0, 0)).toBe('0 0 * 1 0#1');
      expect(queryBuilder.convertToCronYearly('second', 'TUE', 6, 12, 30)).toBe('30 12 * 6 2#2');
      expect(queryBuilder.convertToCronYearly('third', 'WED', 9, 18, 15)).toBe('15 18 * 9 3#3');
      expect(queryBuilder.convertToCronYearly('fourth', 'THU', 11, 8, 45)).toBe('45 8 * 11 4#4');
    });

    it('should handle all day types', () => {
      expect(queryBuilder.convertToCronYearly('first', 'SUN', 1, 0, 0)).toBe('0 0 * 1 0#1');
      expect(queryBuilder.convertToCronYearly('first', 'MON', 1, 0, 0)).toBe('0 0 * 1 1#1');
      expect(queryBuilder.convertToCronYearly('first', 'TUE', 1, 0, 0)).toBe('0 0 * 1 2#1');
      expect(queryBuilder.convertToCronYearly('first', 'WED', 1, 0, 0)).toBe('0 0 * 1 3#1');
      expect(queryBuilder.convertToCronYearly('first', 'THU', 1, 0, 0)).toBe('0 0 * 1 4#1');
      expect(queryBuilder.convertToCronYearly('first', 'FRI', 1, 0, 0)).toBe('0 0 * 1 5#1');
      expect(queryBuilder.convertToCronYearly('first', 'SAT', 1, 0, 0)).toBe('0 0 * 1 6#1');
    });

    it('should throw error for invalid order', () => {
      expect(() => queryBuilder.convertToCronYearly('invalid', 'MON', 1, 0, 0))
        .toThrow('Order is wrong for montly');
    });

    it('should throw error for invalid day', () => {
      expect(() => queryBuilder.convertToCronYearly('first', 'INVALID', 1, 0, 0))
        .toThrow('Order is wrong for montly');
    });
  });

  describe('convertToCronMonthly function', () => {
    it('should convert monthly recurrence with first occurrence', () => {
      const result = queryBuilder.convertToCronMonthly('first', 'MON', 2, 10, 30);

      expect(result).toBe('30 10 * */2 1#1');
    });

    it('should convert monthly recurrence with last occurrence', () => {
      const result = queryBuilder.convertToCronMonthly('last', 'FRI', 3, 15, 45);

      expect(result).toBe('45 15 * */3 5L');
    });

    it('should handle all order types for monthly', () => {
      expect(queryBuilder.convertToCronMonthly('first', 'SUN', 1, 0, 0)).toBe('0 0 * */1 0#1');
      expect(queryBuilder.convertToCronMonthly('second', 'TUE', 2, 12, 30)).toBe('30 12 * */2 2#2');
      expect(queryBuilder.convertToCronMonthly('third', 'WED', 3, 18, 15)).toBe('15 18 * */3 3#3');
      expect(queryBuilder.convertToCronMonthly('fourth', 'THU', 4, 8, 45)).toBe('45 8 * */4 4#4');
    });

    it('should handle all day types for monthly', () => {
      expect(queryBuilder.convertToCronMonthly('first', 'SUN', 1, 0, 0)).toBe('0 0 * */1 0#1');
      expect(queryBuilder.convertToCronMonthly('first', 'MON', 1, 0, 0)).toBe('0 0 * */1 1#1');
      expect(queryBuilder.convertToCronMonthly('first', 'TUE', 1, 0, 0)).toBe('0 0 * */1 2#1');
      expect(queryBuilder.convertToCronMonthly('first', 'WED', 1, 0, 0)).toBe('0 0 * */1 3#1');
      expect(queryBuilder.convertToCronMonthly('first', 'THU', 1, 0, 0)).toBe('0 0 * */1 4#1');
      expect(queryBuilder.convertToCronMonthly('first', 'FRI', 1, 0, 0)).toBe('0 0 * */1 5#1');
      expect(queryBuilder.convertToCronMonthly('first', 'SAT', 1, 0, 0)).toBe('0 0 * */1 6#1');
    });

    it('should throw error for invalid order in monthly', () => {
      expect(() => queryBuilder.convertToCronMonthly('invalid', 'MON', 1, 0, 0))
        .toThrow('Order is wrong for montly');
    });

    it('should throw error for invalid day in monthly', () => {
      expect(() => queryBuilder.convertToCronMonthly('first', 'INVALID', 1, 0, 0))
        .toThrow('Order is wrong for montly');
    });
  });
});
