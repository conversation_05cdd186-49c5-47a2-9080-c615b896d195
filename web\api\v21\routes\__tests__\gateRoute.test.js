const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  gateValidation: {
    addGates: jest.fn(),
    updateGates: jest.fn(),
    gateDetail: jest.fn(),
    deleteGates: jest.fn(),
  },
}));

jest.mock('../../controllers', () => ({
  GateController: {
    addGates: jest.fn(),
    updateGates: jest.fn(),
    listGates: jest.fn(),
    deleteGates: jest.fn(),
    getMappedRequests: jest.fn(),
    deactivateGate: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isAdmin: jest.fn(),
}));

describe('gateRoute', () => {
  let router;
  let gateRoute;
  let GateController;
  let passportConfig;
  let checkAdmin;
  let gateValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    gateRoute = require('../gateRoute');
    const controllers = require('../../controllers');
    GateController = controllers.GateController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    const validations = require('../../middlewares/validations');
    gateValidation = validations.gateValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = gateRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered
      expect(router.post).toHaveBeenCalledWith(
        '/add_gates',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        GateController.addGates,
      );
      expect(router.post).toHaveBeenCalledWith(
        '/update_gates',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        GateController.updateGates,
      );
      expect(router.post).toHaveBeenCalledWith(
        '/gate_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        GateController.listGates,
      );
      expect(router.post).toHaveBeenCalledWith(
        '/delete_gates',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        GateController.deleteGates,
        checkAdmin.isProjectAdmin,
      );
      expect(router.post).toHaveBeenCalledWith(
        '/get_mapped_requests',
        passportConfig.isAuthenticated,
        GateController.getMappedRequests,
      );
      expect(router.post).toHaveBeenCalledWith(
        '/deactivate_gate',
        passportConfig.isAuthenticated,
        GateController.deactivateGate,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = gateRoute.router;
      const result2 = gateRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      gateRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];

      allCalls.forEach((call) => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof gateRoute).toBe('object');
      expect(gateRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(gateRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(gateRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });

  describe('routes', () => {
    it('should register POST /add_gates route with validation, authentication, and project admin check', () => {
      gateRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/add_gates',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        GateController.addGates,
      );
    });

    it('should register POST /update_gates route with validation, authentication, and project admin check', () => {
      gateRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/update_gates',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        GateController.updateGates,
      );
    });

    it('should register POST /gate_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId route with validation and authentication', () => {
      gateRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/gate_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        GateController.listGates,
      );
    });

    it('should register POST /delete_gates route with validation, authentication, and project admin check', () => {
      gateRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/delete_gates',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        GateController.deleteGates,
        checkAdmin.isProjectAdmin,
      );
    });

    it('should register POST /get_mapped_requests route with authentication only', () => {
      gateRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/get_mapped_requests',
        passportConfig.isAuthenticated,
        GateController.getMappedRequests,
      );
    });

    it('should register POST /deactivate_gate route with authentication only', () => {
      gateRoute.router;

      expect(router.post).toHaveBeenCalledWith(
        '/deactivate_gate',
        passportConfig.isAuthenticated,
        GateController.deactivateGate,
      );
    });
  });
});
