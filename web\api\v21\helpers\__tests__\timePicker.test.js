const timePicker = require('../timePicker');

describe('timePicker', () => {
  it('should export an array of 24 time slots', () => {
    expect(Array.isArray(timePicker)).toBe(true);
    expect(timePicker.length).toBe(24);
    expect(timePicker[0]).toBe('00:00:00');
    expect(timePicker[23]).toBe('23:00:00');
  });
  it('should contain all hour slots in HH:00:00 format', () => {
    for (let i = 0; i < 24; i++) {
      const hour = i.toString().padStart(2, '0');
      expect(timePicker[i]).toBe(`${hour}:00:00`);
    }
  });
});
