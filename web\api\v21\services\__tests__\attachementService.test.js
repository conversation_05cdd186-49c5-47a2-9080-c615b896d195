const attachementService = require('../attachementService');
const helper = require('../../helpers/domainHelper');
const notificationHelper = require('../../helpers/notificationHelper');
const pushNotification = require('../../config/fcm');
const awsConfig = require('../../middlewares/awsConfig');
const MAILER = require('../../mailer');

// Mock all dependencies
jest.mock('../../helpers/domainHelper');
jest.mock('../../helpers/notificationHelper');
jest.mock('../../config/fcm', () => ({
    sendDeviceToken: jest.fn(),
    sendMemberLocationPreferencePushNotification: jest.fn()
}));
jest.mock('../../middlewares/awsConfig');
jest.mock('../../mailer');

// Mock FCM module to prevent initialization errors
jest.mock('fcm-push', () => {
    return jest.fn().mockImplementation(() => ({
        send: jest.fn()
    }));
});

// Mock environment variables
process.env.DEVICE_SERVER_KEY = 'test-server-key';
process.env.FIRE_BASE_JSON_PATH = 'test-path';
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            ne: 'ne',
            notIn: 'notIn',
            and: 'and'
        },
        and: jest.fn()
    },
    Enterprise: {
        findOne: jest.fn()
    },
    NotificationPreference: {
        findAll: jest.fn()
    },
    Locations: {
        findOne: jest.fn()
    },
    LocationNotificationPreferences: {
        findAll: jest.fn()
    },
    DeliveryRequest: {
        findOne: jest.fn(),
        createInstance: jest.fn()
    },
    InspectionRequest: {
        findOne: jest.fn(),
        createInstance: jest.fn()
    },
    DeliverAttachement: {
        findOne: jest.fn(),
        findAll: jest.fn(),
        update: jest.fn(),
        createMultipleInstance: jest.fn()
    },
    InspectionAttachement: {
        findOne: jest.fn(),
        findAll: jest.fn(),
        update: jest.fn(),
        createMultipleInstance: jest.fn()
    },
    DeliverHistory: {
        createInstance: jest.fn()
    },
    InspectionHistory: {
        createInstance: jest.fn()
    },
    Member: {
        findOne: jest.fn(),
        findAll: jest.fn()
    },
    User: {
        findOne: jest.fn()
    },
    DeliveryPerson: {
        findAll: jest.fn()
    },
    InspectionPerson: {
        findAll: jest.fn()
    },
    DeliveryPersonNotification: {
        createInstance: jest.fn()
    },
    Project: {
        findByPk: jest.fn()
    },
    Notification: {
        createInstance: jest.fn()
    }
}));

describe('attachementService', () => {
    let mockInputData;
    let mockDone;
    let mockUser;
    let mockProject;
    let mockMember;
    let mockDeliveryRequest;
    let mockInspectionRequest;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common mock data
        mockUser = {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            profilePic: 'profile.jpg',
            domainName: 'test.com'
        };

        mockProject = {
            id: 1,
            projectName: 'Test Project'
        };

        mockMember = {
            id: 1,
            UserId: 1,
            ProjectId: 1,
            isDeleted: false
        };

        mockDeliveryRequest = {
            id: 1,
            DeliveryId: 'DEL-001',
            ProjectId: 1,
            LocationId: 1,
            description: 'Test Delivery',
            requestType: 'deliveryRequest'
        };

        mockInspectionRequest = {
            id: 1,
            InspectionId: 'INS-001',
            ProjectId: 1,
            LocationId: 1,
            description: 'Test Inspection',
            requestType: 'inspectionRequest'
        };

        mockInputData = {
            user: mockUser,
            params: {
                DeliveryRequestId: 1,
                InspectionRequestId: 1,
                id: 1,
                ParentCompanyId: 1
            },
            body: {
                ParentCompanyId: 1
            },
            files: [
                {
                    originalname: 'test.pdf',
                    name: 'test.pdf'
                }
            ]
        };

        mockDone = jest.fn();

        // Setup common mock implementations
        helper.getDynamicModel.mockResolvedValue({
            DeliveryRequest: require('../../models').DeliveryRequest,
            InspectionRequest: require('../../models').InspectionRequest,
            DeliverAttachement: require('../../models').DeliverAttachement,
            InspectionAttachement: require('../../models').InspectionAttachement,
            DeliverHistory: require('../../models').DeliverHistory,
            InspectionHistory: require('../../models').InspectionHistory,
            Member: require('../../models').Member,
            User: require('../../models').User,
            DeliveryPerson: require('../../models').DeliveryPerson,
            InspectionPerson: require('../../models').InspectionPerson,
            DeliveryPersonNotification: require('../../models').DeliveryPersonNotification,
            Project: require('../../models').Project,
            Notification: require('../../models').Notification
        });
        helper.returnProjectModel.mockResolvedValue({
            Member: require('../../models').Member,
            User: require('../../models').User
        });
    });

    describe('resolveDomainName', () => {
        it('should return domain name when enterprise exists', async () => {
            const mockEnterprise = { name: 'test.com' };
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.resolveDomainName('test.com', 1, mockUser);
            expect(result).toBe('test.com');
        });

        it('should return empty string when enterprise does not exist', async () => {
            require('../../models').Enterprise.findOne.mockResolvedValue(null);

            const result = await attachementService.resolveDomainName('nonexistent.com', 1, mockUser);
            expect(result).toBe('');
        });

        it('should return empty string when domain name is not provided', async () => {
            const result = await attachementService.resolveDomainName(null, 1, mockUser);
            expect(result).toBe('');
        });
    });

    describe('resolveEnterpriseValue', () => {
        it('should return null when ParentCompanyId is undefined', async () => {
            const result = await attachementService.resolveEnterpriseValue(undefined, mockUser);
            expect(result).toBeNull();
        });

        it('should return null when user data is not found', async () => {
            // First call returnProjectModel to set up publicUser
            await attachementService.returnProjectModel();
            require('../../models').User.findOne.mockResolvedValue(null);
            const result = await attachementService.resolveEnterpriseValue(1, mockUser);
            expect(result).toBeNull();
        });

        it('should return enterprise when member data is not found', async () => {
            // First call returnProjectModel to set up publicUser and publicMember
            await attachementService.returnProjectModel();
            const mockEnterprise = { id: 1, ParentCompanyId: 1, status: 'completed' };
            require('../../models').User.findOne.mockResolvedValue(mockUser);
            require('../../models').Member.findOne.mockResolvedValue(null);
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.resolveEnterpriseValue(1, mockUser);
            expect(result).toEqual(mockEnterprise);
        });
    });

    describe('getAttachement', () => {
        it('should return attachment list when delivery request exists', async () => {
            const mockAttachments = [{ id: 1, filename: 'test.pdf' }];
            require('../../models').DeliveryRequest.findOne.mockResolvedValue(mockDeliveryRequest);
            require('../../models').DeliverAttachement.findAll.mockResolvedValue(mockAttachments);

            await attachementService.getAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(mockAttachments, false);
        });

        it('should return error when delivery request does not exist', async () => {
            require('../../models').DeliveryRequest.findOne.mockResolvedValue(null);

            await attachementService.getAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
        });

        it('should handle errors gracefully', async () => {
            const mockError = new Error('Test error');
            require('../../models').DeliveryRequest.findOne.mockRejectedValue(mockError);

            await attachementService.getAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('getInspectionAttachement', () => {
        it('should return inspection attachment list when inspection request exists', async () => {
            const mockAttachments = [{ id: 1, filename: 'test.pdf' }];
            require('../../models').InspectionRequest.findOne.mockResolvedValue(mockInspectionRequest);
            require('../../models').InspectionAttachement.findAll.mockResolvedValue(mockAttachments);

            await attachementService.getInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(mockAttachments, false);
        });

        it('should return error when inspection request does not exist', async () => {
            require('../../models').InspectionRequest.findOne.mockResolvedValue(null);

            await attachementService.getInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Inspection Booking id does not exist' });
        });

        it('should handle errors gracefully', async () => {
            const mockError = new Error('Test error');
            require('../../models').InspectionRequest.findOne.mockRejectedValue(mockError);

            await attachementService.getInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('deleteAttachement', () => {
        it('should successfully delete attachment and send notifications', async () => {
            const mockAttachment = {
                id: 1,
                DeliveryRequestId: 1,
                DeliveryRequest: mockDeliveryRequest
            };
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberLocationPreference = [{ Member: { id: 2 } }];
            const mockDeliveryPersons = [{ id: 1, Member: { id: 3, User: { id: 1, firstName: 'Test', lastName: 'User' } } }];
            const mockAdminData = [{ id: 4, User: { id: 2, firstName: 'Admin', lastName: 'User' } }];
            const mockNotificationPreference = [{ id: 1, instant: true, dailyDigest: false }];

            require('../../models').DeliverAttachement.findOne.mockResolvedValue(mockAttachment);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue(mockAdminData);
            require('../../models').DeliveryRequest.findOne.mockResolvedValue(mockDeliveryRequest);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberLocationPreference);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').DeliveryPerson.findAll.mockResolvedValue(mockDeliveryPersons);
            require('../../models').NotificationPreference.findAll.mockResolvedValue(mockNotificationPreference);
            require('../../models').DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').DeliverAttachement.update.mockResolvedValue([1]);

            // Mock Sequelize.and function
            require('../../models').Sequelize.and.mockReturnValue({});

            // Mock notification helper functions
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);
            notificationHelper.createMemberDeliveryLocationInAppNotification.mockResolvedValue(true);

            await attachementService.deleteAttachement(mockInputData, mockDone);
            expect(require('../../models').DeliverAttachement.update).toHaveBeenCalledWith(
                { isDeleted: true },
                { where: { id: 1 } }
            );
            expect(pushNotification.sendDeviceToken).toHaveBeenCalled();
        });

        it('should handle errors during deletion', async () => {
            const mockError = new Error('Test error');
            require('../../models').DeliverAttachement.findOne.mockRejectedValue(mockError);

            await attachementService.deleteAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle missing attachment', async () => {
            require('../../models').DeliverAttachement.findOne.mockResolvedValue(null);

            await attachementService.deleteAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing member detail', async () => {
            const mockAttachment = {
                id: 1,
                DeliveryRequestId: 1,
                DeliveryRequest: mockDeliveryRequest
            };
            require('../../models').DeliverAttachement.findOne.mockResolvedValue(mockAttachment);
            require('../../models').Member.findOne.mockResolvedValue(null);

            await attachementService.deleteAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle notification creation failure', async () => {
            const mockAttachment = {
                id: 1,
                DeliveryRequestId: 1,
                DeliveryRequest: mockDeliveryRequest
            };
            const mockLocation = { locationPath: 'Test Location' };

            require('../../models').DeliverAttachement.findOne.mockResolvedValue(mockAttachment);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockRejectedValue(new Error('Notification failed'));

            await attachementService.deleteAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('deleteInspectionAttachement', () => {
        it('should successfully delete inspection attachment and send notifications', async () => {
            const mockAttachment = {
                id: 1,
                InspectionRequestId: 1,
                InspectionRequest: mockInspectionRequest
            };
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberLocationPreference = [{ Member: { id: 2 } }];
            const mockInspectionPersons = [{ id: 1, Member: { id: 3, User: { id: 1, firstName: 'Test', lastName: 'User' } } }];
            const mockAdminData = [{ id: 4, User: { id: 2, firstName: 'Admin', lastName: 'User' } }];
            const mockNotificationPreference = [{ id: 1, instant: true, dailyDigest: false }];

            require('../../models').InspectionAttachement.findOne.mockResolvedValue(mockAttachment);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue(mockAdminData);
            require('../../models').InspectionRequest.findOne.mockResolvedValue(mockInspectionRequest);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberLocationPreference);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').InspectionPerson.findAll.mockResolvedValue(mockInspectionPersons);
            require('../../models').NotificationPreference.findAll.mockResolvedValue(mockNotificationPreference);
            require('../../models').InspectionHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').InspectionAttachement.update.mockResolvedValue([1]);

            // Mock Sequelize.and function
            require('../../models').Sequelize.and.mockReturnValue({});

            // Mock notification helper functions
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);
            notificationHelper.createMemberInspectionLocationInAppNotification.mockResolvedValue(true);

            await attachementService.deleteInspectionAttachement(mockInputData, mockDone);
            expect(require('../../models').InspectionAttachement.update).toHaveBeenCalledWith(
                { isDeleted: true },
                { where: { id: 1 } }
            );
            expect(pushNotification.sendDeviceToken).toHaveBeenCalled();
        });

        it('should handle errors during deletion', async () => {
            const mockError = new Error('Test error');
            require('../../models').InspectionAttachement.findOne.mockRejectedValue(mockError);

            await attachementService.deleteInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('createAttachement', () => {
        it('should successfully create attachment and send notifications', async () => {
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberLocationPreference = [{ Member: { id: 2 } }];
            const mockDeliveryPersons = [{ id: 1, Member: { id: 3, User: { id: 1, firstName: 'Test', lastName: 'User' } } }];
            const mockAdminData = [{ id: 4, User: { id: 2, firstName: 'Admin', lastName: 'User' } }];
            const mockNotificationPreference = [{ id: 1, instant: true, dailyDigest: false }];

            const mockDeliveryRequestWithMembers = {
                ...mockDeliveryRequest,
                memberDetails: [
                    {
                        id: 1,
                        Member: {
                            id: 1,
                            isGuestUser: false,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'Test',
                                lastName: 'User'
                            }
                        }
                    }
                ]
            };

            require('../../models').DeliveryRequest.findOne
                .mockResolvedValueOnce(mockDeliveryRequest)  // First call in getDynamicModel
                .mockResolvedValueOnce(mockDeliveryRequestWithMembers);  // Second call in createAttachement
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue(mockAdminData);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberLocationPreference);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').DeliveryPerson.findAll.mockResolvedValue(mockDeliveryPersons);
            require('../../models').NotificationPreference.findAll.mockResolvedValue(mockNotificationPreference);
            require('../../models').DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').DeliverAttachement.createMultipleInstance.mockResolvedValue([{ id: 1 }]);

            // Mock Sequelize.and function
            require('../../models').Sequelize.and.mockReturnValue({});

            // Mock notification helper functions
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);
            notificationHelper.createMemberDeliveryLocationInAppNotification.mockResolvedValue(true);

            awsConfig.upload.mockImplementation((files, callback) => callback(mockUploadResult, null));

            await attachementService.createAttachement(mockInputData, mockDone);
            expect(require('../../models').DeliverAttachement.createMultipleInstance).toHaveBeenCalled();
        });

        it('should handle AWS upload errors', async () => {
            const mockError = new Error('AWS upload failed');
            awsConfig.upload.mockImplementation((files, callback) => callback(null, mockError));

            await attachementService.createAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle errors during creation', async () => {
            const mockError = new Error('Test error');
            require('../../models').DeliveryRequest.findOne.mockRejectedValue(mockError);

            await attachementService.createAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle guest user email notifications', async () => {
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberLocationPreference = [{ Member: { id: 2 } }];
            const mockDeliveryPersons = [{ id: 1, Member: { id: 3, User: { id: 1, firstName: 'Test', lastName: 'User' } } }];
            const mockAdminData = [{ id: 4, User: { id: 2, firstName: 'Admin', lastName: 'User' } }];
            const mockNotificationPreference = [{ id: 1, instant: true, dailyDigest: false }];

            const mockDeliveryRequestWithGuestMembers = {
                ...mockDeliveryRequest,
                memberDetails: [
                    {
                        id: 1,
                        Member: {
                            id: 1,
                            isGuestUser: true,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'Guest',
                                lastName: 'User'
                            }
                        }
                    }
                ]
            };

            require('../../models').DeliveryRequest.findOne
                .mockResolvedValueOnce(mockDeliveryRequest)
                .mockResolvedValueOnce(mockDeliveryRequestWithGuestMembers);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue(mockAdminData);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberLocationPreference);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').DeliveryPerson.findAll.mockResolvedValue(mockDeliveryPersons);
            require('../../models').NotificationPreference.findAll.mockResolvedValue(mockNotificationPreference);
            require('../../models').DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').DeliverAttachement.createMultipleInstance.mockResolvedValue([{ id: 1 }]);

            require('../../models').Sequelize.and.mockReturnValue({});
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);
            notificationHelper.createMemberDeliveryLocationInAppNotification.mockResolvedValue(true);
            MAILER.sendMail.mockImplementation((payload, template, subject, title, callback) => callback('success', null));

            awsConfig.upload.mockImplementation((files, callback) => callback(mockUploadResult, null));

            await attachementService.createAttachement(mockInputData, mockDone);
            expect(MAILER.sendMail).toHaveBeenCalled();
            expect(require('../../models').DeliverAttachement.createMultipleInstance).toHaveBeenCalled();
        });

        it('should handle delivery request not found', async () => {
            require('../../models').DeliveryRequest.findOne
                .mockResolvedValueOnce(null);  // First call returns null

            await attachementService.createAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking id does not exist' });
        });
    });

    describe('createInspectionAttachement', () => {
        it('should successfully create inspection attachment and send notifications', async () => {
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberLocationPreference = [{ Member: { id: 2 } }];
            const mockInspectionPersons = [{ id: 1, Member: { id: 3, User: { id: 1, firstName: 'Test', lastName: 'User' } } }];
            const mockAdminData = [{ id: 4, User: { id: 2, firstName: 'Admin', lastName: 'User' } }];
            const mockNotificationPreference = [{ id: 1, instant: true, dailyDigest: false }];

            const mockInspectionRequestWithMembers = {
                ...mockInspectionRequest,
                memberDetails: [
                    {
                        id: 1,
                        Member: {
                            id: 1,
                            isGuestUser: false,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'Test',
                                lastName: 'User'
                            }
                        }
                    }
                ]
            };

            require('../../models').InspectionRequest.findOne
                .mockResolvedValueOnce(mockInspectionRequest)  // First call in getDynamicModel
                .mockResolvedValueOnce(mockInspectionRequestWithMembers);  // Second call in createInspectionAttachement
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue(mockAdminData);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberLocationPreference);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').InspectionPerson.findAll.mockResolvedValue(mockInspectionPersons);
            require('../../models').NotificationPreference.findAll.mockResolvedValue(mockNotificationPreference);
            require('../../models').InspectionHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').InspectionAttachement.createMultipleInstance.mockResolvedValue([{ id: 1 }]);

            // Mock Sequelize.and function
            require('../../models').Sequelize.and.mockReturnValue({});

            // Mock notification helper functions
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);
            notificationHelper.createMemberInspectionLocationInAppNotification.mockResolvedValue(true);

            awsConfig.upload.mockImplementation((files, callback) => callback(mockUploadResult, null));

            await attachementService.createInspectionAttachement(mockInputData, mockDone);
            expect(require('../../models').InspectionAttachement.createMultipleInstance).toHaveBeenCalled();
        });

        it('should handle AWS upload errors', async () => {
            const mockError = new Error('AWS upload failed');
            awsConfig.upload.mockImplementation((files, callback) => callback(null, mockError));

            await attachementService.createInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle errors during creation', async () => {
            const mockError = new Error('Test error');
            require('../../models').InspectionRequest.findOne.mockRejectedValue(mockError);

            await attachementService.createInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle guest user email notifications', async () => {
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberLocationPreference = [{ Member: { id: 2 } }];
            const mockInspectionPersons = [{ id: 1, Member: { id: 3, User: { id: 1, firstName: 'Test', lastName: 'User' } } }];
            const mockAdminData = [{ id: 4, User: { id: 2, firstName: 'Admin', lastName: 'User' } }];
            const mockNotificationPreference = [{ id: 1, instant: true, dailyDigest: false }];

            const mockInspectionRequestWithGuestMembers = {
                ...mockInspectionRequest,
                memberDetails: [
                    {
                        id: 1,
                        Member: {
                            id: 1,
                            isGuestUser: true,
                            User: {
                                email: '<EMAIL>',
                                firstName: 'Guest',
                                lastName: 'User'
                            }
                        }
                    }
                ]
            };

            require('../../models').InspectionRequest.findOne
                .mockResolvedValueOnce(mockInspectionRequest)
                .mockResolvedValueOnce(mockInspectionRequestWithGuestMembers);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue(mockAdminData);
            require('../../models').Locations.findOne.mockResolvedValue(mockLocation);
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberLocationPreference);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').InspectionPerson.findAll.mockResolvedValue(mockInspectionPersons);
            require('../../models').NotificationPreference.findAll.mockResolvedValue(mockNotificationPreference);
            require('../../models').InspectionHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').InspectionAttachement.createMultipleInstance.mockResolvedValue([{ id: 1 }]);

            require('../../models').Sequelize.and.mockReturnValue({});
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);
            notificationHelper.createMemberInspectionLocationInAppNotification.mockResolvedValue(true);
            MAILER.sendMail.mockImplementation(() => Promise.resolve());

            awsConfig.upload.mockImplementation((files, callback) => callback(mockUploadResult, null));

            await attachementService.createInspectionAttachement(mockInputData, mockDone);
            expect(MAILER.sendMail).toHaveBeenCalled();
            expect(require('../../models').InspectionAttachement.createMultipleInstance).toHaveBeenCalled();
        });

        it('should handle inspection request not found', async () => {
            require('../../models').InspectionRequest.findOne
                .mockResolvedValueOnce(null);  // First call returns null

            await attachementService.createInspectionAttachement(mockInputData, mockDone);
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Inspection Booking id does not exist' });
        });
    });

    describe('getDynamicModel', () => {
        it('should successfully get dynamic model with domain name', async () => {
            const mockEnterprise = { name: 'test.com' };
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.getDynamicModel(mockInputData);
            expect(result).toBe(true);
            expect(helper.getDynamicModel).toHaveBeenCalledWith('test.com');
        });

        it('should get dynamic model with enterprise value when no domain name', async () => {
            const mockUserWithoutDomain = { ...mockUser, domainName: null };
            const mockInputDataWithoutDomain = { ...mockInputData, user: mockUserWithoutDomain };
            const mockEnterprise = { id: 1, name: 'enterprise.com', status: 'completed' };

            // First call returnProjectModel to set up publicUser and publicMember
            await attachementService.returnProjectModel();
            require('../../models').User.findOne.mockResolvedValue(mockUserWithoutDomain);
            require('../../models').Member.findOne.mockResolvedValue(null);
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.getDynamicModel(mockInputDataWithoutDomain);
            expect(result).toBe(true);
            expect(helper.getDynamicModel).toHaveBeenCalledWith('enterprise.com');
        });

        it('should handle empty domain name and no enterprise value', async () => {
            const mockUserWithoutDomain = { ...mockUser, domainName: null };
            const mockInputDataWithoutDomain = { ...mockInputData, user: mockUserWithoutDomain };

            // First call returnProjectModel to set up publicUser and publicMember
            await attachementService.returnProjectModel();
            require('../../models').User.findOne.mockResolvedValue(null);

            const result = await attachementService.getDynamicModel(mockInputDataWithoutDomain);
            expect(result).toBe(true);
            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });
    });

    describe('returnProjectModel', () => {
        it('should successfully return project model', async () => {
            const mockModelData = {
                Member: require('../../models').Member,
                User: require('../../models').User
            };
            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await attachementService.returnProjectModel();
            expect(helper.returnProjectModel).toHaveBeenCalled();
        });
    });

    describe('resolveEnterpriseValue - additional cases', () => {
        beforeEach(async () => {
            // Set up publicUser and publicMember
            await attachementService.returnProjectModel();
        });

        it('should return enterprise when member has isAccount true', async () => {
            const mockMemberWithAccount = { id: 1, isAccount: true, EnterpriseId: 2 };
            const mockEnterprise = { id: 2, status: 'completed' };

            require('../../models').User.findOne.mockResolvedValue(mockUser);
            require('../../models').Member.findOne.mockResolvedValue(mockMemberWithAccount);
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.resolveEnterpriseValue(1, mockUser);
            expect(result).toEqual(mockEnterprise);
        });

        it('should return enterprise when member has isAccount false', async () => {
            const mockMemberWithoutAccount = { id: 1, isAccount: false };
            const mockEnterprise = { id: 1, ParentCompanyId: 1, status: 'completed' };

            require('../../models').User.findOne.mockResolvedValue(mockUser);
            require('../../models').Member.findOne.mockResolvedValue(mockMemberWithoutAccount);
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.resolveEnterpriseValue(1, mockUser);
            expect(result).toEqual(mockEnterprise);
        });

        it('should handle ParentCompanyId as string "undefined"', async () => {
            const result = await attachementService.resolveEnterpriseValue('undefined', mockUser);
            expect(result).toBeNull();
        });
    });

    describe('resolveDomainName - additional cases', () => {
        it('should handle case insensitive domain name', async () => {
            const mockEnterprise = { name: 'TEST.COM' };
            require('../../models').Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await attachementService.resolveDomainName('TEST.COM', 1, mockUser);
            expect(result).toBe('TEST.COM');
            expect(require('../../models').Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'test.com' }
            });
        });

        it('should handle undefined domain name', async () => {
            const result = await attachementService.resolveDomainName(undefined, 1, mockUser);
            expect(result).toBe('');
        });

        it('should handle empty string domain name', async () => {
            const result = await attachementService.resolveDomainName('', 1, mockUser);
            expect(result).toBe('');
        });
    });

    describe('Edge cases and error scenarios', () => {
        it('should handle files with different naming conventions', async () => {
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockInputDataWithDifferentFiles = {
                ...mockInputData,
                files: [
                    {
                        name: 'test-file.pdf'  // Using 'name' instead of 'originalname'
                    }
                ]
            };

            const mockDeliveryRequestWithMembers = {
                ...mockDeliveryRequest,
                memberDetails: []
            };

            require('../../models').DeliveryRequest.findOne
                .mockResolvedValueOnce(mockDeliveryRequest)
                .mockResolvedValueOnce(mockDeliveryRequestWithMembers);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Member.findAll.mockResolvedValue([]);
            require('../../models').Locations.findOne.mockResolvedValue({ locationPath: 'Test Location' });
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').DeliveryPerson.findAll.mockResolvedValue([]);
            require('../../models').NotificationPreference.findAll.mockResolvedValue([]);
            require('../../models').DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').DeliverAttachement.createMultipleInstance.mockResolvedValue([{ id: 1 }]);

            require('../../models').Sequelize.and.mockReturnValue({});
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);

            awsConfig.upload.mockImplementation((files, callback) => callback(mockUploadResult, null));

            await attachementService.createAttachement(mockInputDataWithDifferentFiles, mockDone);
            expect(require('../../models').DeliverAttachement.createMultipleInstance).toHaveBeenCalled();
        });

        it('should handle empty bulk data in createAttachement', async () => {
            const mockUploadResult = [];  // Empty result
            const mockDeliveryRequestWithMembers = {
                ...mockDeliveryRequest,
                memberDetails: []
            };

            require('../../models').DeliveryRequest.findOne
                .mockResolvedValueOnce(mockDeliveryRequest)
                .mockResolvedValueOnce(mockDeliveryRequestWithMembers);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').Locations.findOne.mockResolvedValue({ locationPath: 'Test Location' });
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);

            awsConfig.upload.mockImplementation((files, callback) => callback(mockUploadResult, null));

            await attachementService.createAttachement(mockInputData, mockDone);
            // Should not call createMultipleInstance when bulkData is empty
            expect(require('../../models').DeliverAttachement.createMultipleInstance).not.toHaveBeenCalled();
        });

        it('should handle missing location in delete operations', async () => {
            const mockAttachment = {
                id: 1,
                DeliveryRequestId: 1,
                DeliveryRequest: mockDeliveryRequest
            };

            require('../../models').DeliverAttachement.findOne.mockResolvedValue(mockAttachment);
            require('../../models').Member.findOne.mockResolvedValue(mockMember);
            require('../../models').DeliveryRequest.findOne.mockResolvedValue(mockDeliveryRequest);
            require('../../models').Locations.findOne.mockResolvedValue(null);  // No location found
            require('../../models').LocationNotificationPreferences.findAll.mockResolvedValue([]);
            require('../../models').Project.findByPk.mockResolvedValue(mockProject);
            require('../../models').Notification.createInstance.mockResolvedValue({ id: 1, MemberId: 1 });
            require('../../models').DeliveryPerson.findAll.mockResolvedValue([]);
            require('../../models').Member.findAll.mockResolvedValue([]);
            require('../../models').NotificationPreference.findAll.mockResolvedValue([]);
            require('../../models').DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
            require('../../models').DeliverAttachement.update.mockResolvedValue([1]);

            require('../../models').Sequelize.and.mockReturnValue({});
            notificationHelper.createDeliveryPersonNotification.mockResolvedValue(true);

            await attachementService.deleteAttachement(mockInputData, mockDone);
            expect(require('../../models').DeliverAttachement.update).toHaveBeenCalled();
        });
    });
});