const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  Locations,
  LocationNotificationPreferences,
} = require('../models');

let {
  ConcreteRequest,
  ConcreteRequestHistory,
  Member,
  User,
  ConcreteRequestResponsiblePerson,
  DeliveryPersonNotification,
  Notification,
  ConcreteRequestAttachment,
  Project,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const awsConfig = require('../middlewares/awsConfig');
const MAILER = require('../mailer');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const concreteRequestAttachmentService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const { domainName: initialDomainName, email, ParentCompanyId } = this.extractDomainInfo(inputData);

    let { domainName, enterpriseValue } = await this.getDomainEnterpriseValue(initialDomainName);

    domainName = domainName || await this.handleUndefinedDomain(ParentCompanyId, email);

    const modelObj = await helper.getDynamicModel(domainName);
    this.assignDynamicModels(modelObj);

    if (enterpriseValue) {
      await this.updateIncomeUser(inputData);
    }

    return true;
  },

  extractDomainInfo(inputData) {
    const domainName = inputData.user.domainName ? inputData.user.domainName.toLowerCase() : '';
    const email = inputData.user.email;
    const ParentCompanyId = inputData.body?.ParentCompanyId || inputData.params?.ParentCompanyId;
    return { domainName, email, ParentCompanyId };
  },

  async getDomainEnterpriseValue(domainName) {
    let enterpriseValue;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({ where: { name: domainName } });
      if (!domainEnterpriseValue) {
        domainName = '';
      } else {
        enterpriseValue = domainEnterpriseValue.name.toLowerCase();
      }
    }
    return { domainName, enterpriseValue };
  },

  async handleUndefinedDomain(ParentCompanyId, email) {
    let enterpriseValue;
    const userData = await publicUser.findOne({ where: { email } });
    if (userData) {
      const memberData = await publicMember.findOne({
        where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
      });
      enterpriseValue = await this.getEnterpriseValue(memberData, ParentCompanyId);
    }
    return enterpriseValue || '';
  },

  async getEnterpriseValue(memberData, ParentCompanyId) {
    let enterpriseValue;
    if (memberData?.isAccount) {
      enterpriseValue = await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' },
      });
    } else {
      enterpriseValue = await Enterprise.findOne({
        where: { ParentCompanyId, status: 'completed' },
      });
    }
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  assignDynamicModels(modelObj) {
    ConcreteRequest = modelObj.ConcreteRequest;
    ConcreteRequestAttachment = modelObj.ConcreteRequestAttachment;
    ConcreteRequestHistory = modelObj.ConcreteRequestHistory;
    ConcreteRequestResponsiblePerson = modelObj.ConcreteRequestResponsiblePerson;
    Member = modelObj.Member;
    User = modelObj.User;
    Notification = modelObj.Notification;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Project = modelObj.Project;
  },

  async updateIncomeUser(inputData) {
    const newUser = await User.findOne({ where: { email: inputData.user.email } });
    inputData.user = newUser;
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getConcreteRequestAttachments(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: inputData.params.ConcreteRequestId,
          ProjectId: +inputData.params.ProjectId,
          isDeleted: false,
        },
      });
      if (exist) {
        const attachmentList = await ConcreteRequestAttachment.findAll({
          where: {
            ConcreteRequestId: exist.id,
            isDeleted: false,
          },
          order: [['id', 'DESC']],
        });
        done({ attachmentList, exist }, false);
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async deleteConcreteRequestAttachment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const { params } = inputData;

      const attachment = await this.findAttachment(params);
      const { memberDetail, exist } = await this.getMemberAndRequestDetails(params, attachment);

      await ConcreteRequestAttachment.update(
        { isDeleted: true },
        {
          where: {
            id: params.id,
          },
        }
      );

      if (exist) {
        const locationChosen = await this.getLocation(exist);
        const memberLocationPreference = await this.getMemberLocationPreferences(exist);
        const locationFollowMembers = await this.getLocationFollowMembers(memberLocationPreference);

        const history = this.constructHistoryObject(exist, loginUser, locationChosen, memberDetail);
        const notification = this.constructNotificationObject(history, params);

        await this.createHistoryAndNotificationObjects(history, notification);
        const adminData = await this.getAdminData(params, locationFollowMembers);
        const personData = await this.getPersonData(attachment, locationFollowMembers);

        const checkMemberNotification = await this.getNotificationPreferences(params);
        history.notificationPreference = checkMemberNotification;

        await this.sendNotifications(history, memberLocationPreference, exist, adminData, personData);

        done(history, false);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async findAttachment(params) {
    return await ConcreteRequestAttachment.findOne({
      where: { id: params.id, ProjectId: +params.ProjectId },
      include: [{ association: 'ConcreteRequest' }],
    });
  },

  async getMemberAndRequestDetails(params, attachment) {
    const ProjectId = +params.ProjectId;
    const memberDetail = await Member.findOne({
      where: [
        Sequelize.and({
          UserId: params.user.id,
          ProjectId,
          isDeleted: false,
        }),
      ],
    });
    const exist = await ConcreteRequest.findOne({
      where: {
        id: attachment?.ConcreteRequestId,
        ProjectId,
        isDeleted: false,
      },
    });
    return { memberDetail, exist };
  },

  async getLocation(exist) {
    return await Locations.findOne({
      where: {
        ProjectId: exist?.ProjectId,
        id: exist?.LocationId,
      },
    });
  },

  async getMemberLocationPreferences(exist) {
    return await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: exist?.ProjectId,
        LocationId: exist?.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  },

  async getLocationFollowMembers(memberLocationPreference) {
    const locationFollowMembers = [];
    if (memberLocationPreference && Array.isArray(memberLocationPreference)) {
      for (const element of memberLocationPreference) {
        locationFollowMembers.push(element.Member?.id);
      }
    }
    return locationFollowMembers;
  },

  constructHistoryObject(exist, loginUser, locationChosen, memberDetail) {
    return {
      ConcreteRequestId: exist?.id,
      MemberId: memberDetail?.id,
      type: 'attachment',
      description: `${loginUser.firstName} ${loginUser.lastName} removed the file in ${exist?.description}`,
      locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} removed the file in the Booking ${exist?.description}. Location: ${locationChosen?.locationPath}.`,
      ProjectId: exist?.ProjectId,
      firstName: loginUser.firstName,
      profilePic: loginUser?.profilePic,
      createdAt: new Date(),
      projectName: '',
    };
  },

  constructNotificationObject(history, params) {
    return {
      ...history,
      title: 'Concrete Booking Attachment',
      isDeliveryRequest: false,
      requestType: 'concreteRequest',
      ProjectId: params.ProjectId,
    };
  },

  async createHistoryAndNotificationObjects(history, notification) {
    history.projectName = (await Project.findByPk(history.ProjectId))?.projectName;
    await ConcreteRequestHistory.createInstance(history);
    await Notification.createInstance(notification);
  },

  async getAdminData(params, locationFollowMembers) {
    return await Member.findAll({
      where: {
        ProjectId: params.ProjectId,
        isDeleted: false,
        id: { [Op.notIn]: locationFollowMembers },
      },
      include: [
        {
          association: 'User',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
    });
  },

  async getPersonData(attachment, locationFollowMembers) {
    return await ConcreteRequestResponsiblePerson.findAll({
      where: { ConcreteRequestId: attachment?.ConcreteRequestId, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
          where: {
            RoleId: { [Op.notIn]: [1, 2] },
            id: { [Op.notIn]: locationFollowMembers },
          },
        },
      ],
    });
  },

  async getNotificationPreferences(params) {
    return await NotificationPreference.findAll({
      where: {
        ProjectId: params.ProjectId,
        isDeleted: false,
      },
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: { id: 2, isDeleted: false },
        },
      ],
    });
  },

  async sendNotifications(history, memberLocationPreference, exist, adminData, personData) {
    await this.sendPushNotification(memberLocationPreference, exist, history);
    await this.createLocationInAppNotification(memberLocationPreference, exist);
    await this.createDeliveryNotification(adminData, personData, exist, history);
    await this.sendConcretePushNotification(history);
  },

  async sendPushNotification(memberLocationPreference, exist, history) {
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
        memberLocationPreference,
        exist?.ConcreteRequestId,
        history.locationFollowDescription,
        exist?.requestType,
        exist?.ProjectId,
        exist?.id,
        2
      );
    }
  },

  async createLocationInAppNotification(memberLocationPreference, exist) {
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        exist?.ProjectId,
        2,
        memberLocationPreference
      );
    }
  },

  async createDeliveryNotification(adminData, personData, exist, history) {
    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      await Project.findByPk(history.ProjectId),
      await Notification.createInstance(history),
      DeliveryPersonNotification,
      history.MemberId,
      history,
      2,
      'deleted an attachment in a',
      'Concrete Request',
      `concrete Booking (${exist?.ConcreteRequestId} - ${exist?.description})`,
      history.ConcreteRequestId
    );
  },

  async sendConcretePushNotification(history) {
    await pushNotification.sendPushNotificationForConcrete(history, 2, history.ProjectId);
  },

  async createConcreteRequestAttachment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params, user } = inputData;

      const exist = await this.findExistingConcreteRequest(params);
      const memberDetail = await this.findMemberDetail(params, user);

      if (exist) {
        await this.uploadFilesAndNotify(inputData, params, user, exist, memberDetail, done);
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async findExistingConcreteRequest(params) {
    return await ConcreteRequest.findOne({
      where: {
        ConcreteRequestId: +params.ConcreteRequestId,
        ProjectId: +params.ProjectId,
        isDeleted: false,
      },
    });
  },

  async findMemberDetail(params, user) {
    return await Member.findOne({
      where: {
        UserId: user.id,
        ProjectId: params.ProjectId,
        isDeleted: false,
      },
    });
  },

  async uploadFilesAndNotify(inputData, params, user, exist, memberDetail, done) {
    awsConfig.upload(inputData.files, async (result, err) => {
      if (!err) {
        await this.processUploadedFiles(result, inputData, params, user, exist, memberDetail, done);
      } else {
        done(null, err);
      }
    });
  },

  async processUploadedFiles(result, inputData, params, user, exist, memberDetail, done) {
    const userDataMail = await this.getUserDataMail(inputData, params);
    for (const userData of userDataMail) {
      if (userData.Member?.isGuestUser) {
        const guestMailPayload = this.constructGuestMailPayload(userData, user);
        await MAILER.sendMail(
          guestMailPayload,
          'notifyGuestOnEdit',
          `Attachment Added by ${user.firstName} `,
          'Attachment Added against Concrete Booking',
          console.log
        );
      }
    }

    const locationChosen = await this.getLocation(exist);
    const memberLocationPreference = await this.getMemberLocationPreferences(exist);
    const locationFollowMembers = await this.getLocationFollowMembers(memberLocationPreference);

    const bulkData = this.constructBulkData(result, inputData, exist);
    await ConcreteRequestAttachment.createMultipleInstance(bulkData);

    const history = this.constructHistoryObject(exist, user, locationChosen, memberDetail);
    const notification = this.constructNotificationObject(history, params);

    const personData = await this.getPersonData(exist, locationFollowMembers);
    const adminData = await this.getAdminData(params, locationFollowMembers);

    await this.createHistoryAndNotificationObjects(history, notification);
    await this.sendNotifications(history, memberLocationPreference, exist, adminData, personData);
    done(history, false);
  },

  async getUserDataMail(inputData, params) {
    const exist2 = await ConcreteRequest.findOne({
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
      ],
      where: {
        ConcreteRequestId: +params.ConcreteRequestId,
        ProjectId: +params.ProjectId,
        isDeleted: false,
      },
    });
    return exist2?.memberDetails;
  },

  constructGuestMailPayload(userData, user) {
    return {
      email: userData.Member?.User?.email,
      guestName: userData.Member?.User?.firstName,
      content: `We would like to inform you that ${user.firstName} ${user.lastName} Attached the file in Booking.`,
    };
  },

  constructBulkData(result, inputData, exist) {
    return result.map((element, i) => {
      const fileData = inputData.files[i];
      const { fileName, extension } = this.getFileNameAndExtension(fileData);
      return {
        attachment: element.Location,
        filename: fileName,
        extension,
        ConcreteRequestId: +exist.id,
        ProjectId: +exist.ProjectId,
        isDeleted: false,
      };
    });
  },

  getFileNameAndExtension(fileData) {
    let fileName, extension;
    if (fileData.originalname !== undefined) {
      fileName = fileData.originalname;
      extension = fileName.split('.').pop();
    } else {
      fileName = fileData.name;
      extension = fileName.split('.').pop();
    }
    return { fileName, extension };
  },
};

module.exports = concreteRequestAttachmentService;
