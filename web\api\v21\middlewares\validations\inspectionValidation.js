const Joi = require('joi');

const inspectionValidation = {
    newRequest: {
        body: Joi.object({
            description: Joi.string().min(3).required(),
            companies: Joi.array().required(),
            escort: Joi.boolean().required(),
            ProjectId: Joi.number().required(),
            GateId: Joi.number().required(),
            notes: Joi.optional().allow(''),
            EquipmentId: Joi.array().required(),
            LocationId: Joi.number().required(),
            define: Joi.array().required(),
            inspectionStart: Joi.date().required(),
            inspectionEnd: Joi.date().required(),
            ParentCompanyId: Joi.any(),
            persons: Joi.array().min(1).required(),
            isAssociatedWithCraneRequest: Joi.boolean().required(),
            requestType: Joi.string().required(),
            cranePickUpLocation: Joi.string().optional().allow('', null),
            craneDropOffLocation: Joi.string().optional().allow('', null),
            CraneRequestId: Joi.number().optional().allow('', null),
            recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
            repeatEveryCount: Joi.when('recurrence', {
                is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
                then: Joi.string().min(1).required(),
                otherwise: Joi.string().allow('', null),
            }),
            repeatEveryType: Joi.when('recurrence', {
                is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
                then: Joi.string().min(1).required(),
                otherwise: Joi.string().allow('', null),
            }),
            days: Joi.when('recurrence', {
                is: Joi.string().valid('Daily', 'Weekly'),
                then: Joi.array().min(1).required(),
                otherwise: Joi.array().allow('', null),
            }),
            chosenDateOfMonth: Joi.when('recurrence', {
                is: Joi.string().valid('Monthly', 'Yearly'),
                then: Joi.boolean().required(),
                otherwise: Joi.boolean().allow('', null),
            }),
            dateOfMonth: Joi.when('recurrence', {
                is: Joi.string().valid('Monthly', 'Yearly'),
                then: Joi.when('chosenDateOfMonth', {
                    is: Joi.boolean().valid(true),
                    then: Joi.string().min(1).required(),
                    otherwise: Joi.string().allow('', null),
                }),
                otherwise: Joi.string().allow('', null),
            }),
            monthlyRepeatType: Joi.when('recurrence', {
                is: Joi.string().valid('Monthly', 'Yearly'),
                then: Joi.when('chosenDateOfMonth', {
                    is: Joi.string().valid(false),
                    then: Joi.string().min(1).required(),
                    otherwise: Joi.string().allow('', null),
                }),
                otherwise: Joi.string().allow('', null),
            }),
            TimeZoneId: Joi.number().required(),
            endPicker: Joi.string().allow('', null),
            startPicker: Joi.string().allow('', null),
            inspectionType: Joi.string().required(),
            originationAddress: Joi.string().optional().allow('', null),
            vehicleType: Joi.string().optional().allow('', null),
            chosenDateOfMonthValue: Joi.any().optional().allow('', null),
        }),
    },
    listNDR: {
        params: Joi.object({
            ProjectId: Joi.number().required(),
            pageSize: Joi.number().required(),
            pageNo: Joi.number().required(),
            void: Joi.number().required(),
        }),
        body: Joi.object({
            companyFilter: Joi.number(),
            queuedNdr: Joi.boolean().required(),
            descriptionFilter: Joi.optional().allow(''),
            memberFilter: Joi.number(),
            gateFilter: Joi.number(),
            ParentCompanyId: Joi.number(),
            upcoming: Joi.boolean(),
            equipmentFilter: Joi.number().optional().allow(null),
            assignedFilter: Joi.boolean(),
            dateFilter: Joi.optional().allow(''),
            statusFilter: Joi.optional().allow(''),
            locationFilter: Joi.string().optional().allow('', null),
            search: Joi.optional().allow(''),
            sort: Joi.any().optional().allow('', null),
            sortByField: Joi.any().optional().allow('', null),
            pickFrom: Joi.optional().allow('', null),
            pickTo: Joi.optional().allow('', null),
            inspectionStatusFilter: Joi.optional().allow('', null),
            inspectionTypeFilter: Joi.optional().allow('', null),
        }),
    },
    getNDRData: {
        params: Joi.object({
            inspectionRequestId: Joi.number().required(),
            ParentCompanyId: Joi.any(),
        }),
    },
    getMemberData: {
        params: Joi.object({
            ProjectId: Joi.number().required(),
            ParentCompanyId: Joi.any(),
        }),
    },
    editRequest: {
        body: Joi.object({
            id: Joi.number().required(),
            description: Joi.string().min(3).required(),
            inspectionId: Joi.number(),
            companies: Joi.array().required(),
            escort: Joi.boolean().required(),
            ProjectId: Joi.number().required(),
            GateId: Joi.number().required(),
            notes: Joi.optional().allow(''),
            EquipmentId: Joi.array().required(),
            LocationId: Joi.number().required(),
            vehicleDetails: Joi.string().optional().allow('', null),
            define: Joi.array().required(),
            inspectionStart: Joi.date().required(),
            inspectionEnd: Joi.date().required(),
            persons: Joi.array().required(),
            ParentCompanyId: Joi.any(),
            isAssociatedWithCraneRequest: Joi.boolean().required(),
            requestType: Joi.string().required(),
            cranePickUpLocation: Joi.string().optional().allow('', null),
            craneDropOffLocation: Joi.string().optional().allow('', null),
            CraneRequestId: Joi.number().optional().allow('', null),
            recurrenceId: Joi.number().optional().allow('', null),
            recurrenceEndDate: Joi.date().optional().allow('', null),
            previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
            nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
            recurrenceSeriesStartDate: Joi.date().required().allow('', null),
            recurrenceSeriesEndDate: Joi.date().required().allow('', null),
            seriesOption: Joi.number().required(),
            inspectionStartTime: Joi.string().required(),
            inspectionEndTime: Joi.string().required(),
            timezone: Joi.string().required(),
            inspectionType: Joi.string().required(),
            originationAddress: Joi.string().optional().allow('', null),
            vehicleType: Joi.string().optional().allow('', null),
            recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
            repeatEveryCount: Joi.when('recurrence', {
                is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
                then: Joi.string().min(1).required(),
                otherwise: Joi.string().allow('', null),
            }),
            repeatEveryType: Joi.when('recurrence', {
                is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
                then: Joi.string().min(1).required(),
                otherwise: Joi.string().allow('', null),
            }),
            days: Joi.when('recurrence', {
                is: Joi.string().valid('Daily', 'Weekly'),
                then: Joi.array().min(1).required(),
                otherwise: Joi.array().allow('', null),
            }),
            chosenDateOfMonth: Joi.when('recurrence', {
                is: Joi.string().valid('Monthly', 'Yearly'),
                then: Joi.boolean().required(),
                otherwise: Joi.boolean().allow('', null),
            }),
            dateOfMonth: Joi.when('recurrence', {
                is: Joi.string().valid('Monthly', 'Yearly'),
                then: Joi.when('chosenDateOfMonth', {
                    is: Joi.boolean().valid(true),
                    then: Joi.string().min(1).required(),
                    otherwise: Joi.string().allow('', null),
                }),
                otherwise: Joi.string().allow('', null),
            }),
            monthlyRepeatType: Joi.when('recurrence', {
                is: Joi.string().valid('Monthly', 'Yearly'),
                then: Joi.when('chosenDateOfMonth', {
                    is: Joi.string().valid(false),
                    then: Joi.string().min(1).required(),
                    otherwise: Joi.string().allow('', null),
                }),
                otherwise: Joi.string().allow('', null),
            }),
            chosenDateOfMonthValue: Joi.any().optional().allow('', null),
            recurrenceEdited: Joi.boolean().optional().allow('', null),
        }),
    },
    editQueuedRequest: {
        body: Joi.object({
            id: Joi.number().required(),
            description: Joi.string().min(3).optional().allow(null, ''),
            inspectionId: Joi.number(),
            companies: Joi.array().optional().allow(null, ''),
            escort: Joi.boolean().optional(),
            ProjectId: Joi.number().required(),
            GateId: Joi.number().optional().allow(null, ''),
            LocationId: Joi.number().optional().allow(null, ''),
            notes: Joi.optional().allow('').allow(null, ''),
            EquipmentId: Joi.number().optional().allow(null, ''),
            vehicleDetails: Joi.string().optional().allow('', null),
            define: Joi.array().optional().allow(null, ''),
            inspectionStart: Joi.date().optional().allow(null, ''),
            inspectionEnd: Joi.date().optional().allow(null, ''),
            persons: Joi.array().optional().allow(null, ''),
            ParentCompanyId: Joi.any(),
            isAssociatedWithCraneRequest: Joi.boolean().required(),
            requestType: Joi.string().required(),
            cranePickUpLocation: Joi.string().optional().allow('', null),
            craneDropOffLocation: Joi.string().optional().allow('', null),
            CraneRequestId: Joi.number().optional().allow('', null),
            recurrenceId: Joi.number().optional().allow('', null),
            recurrenceEndDate: Joi.date().optional().allow('', null),
            previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
            nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
            recurrenceSeriesStartDate: Joi.date().required().allow('', null),
            recurrenceSeriesEndDate: Joi.date().required().allow('', null),
            seriesOption: Joi.number().required(),
            inspectionStartTime: Joi.string().required(),
            inspectionEndTime: Joi.string().required(),
            timezone: Joi.string().required(),
            ndrStatus: Joi.string().required(),
        }),
    },
    updateQueuedRequest: {
        body: Joi.object({
            id: Joi.number().required(),
            updateQueuedRequest: Joi.number().required(),
            description: Joi.string().min(3).required(),
            inspectionId: Joi.number(),
            companies: Joi.array().required(),
            escort: Joi.boolean().required(),
            ProjectId: Joi.number().required(),
            GateId: Joi.number().required(),
            LocationId: Joi.number().required(),
            notes: Joi.optional().allow(''),
            EquipmentId: Joi.number().required(),
            vehicleDetails: Joi.string().optional().allow('', null),
            define: Joi.array().required(),
            inspectionStart: Joi.date().required(),
            inspectionEnd: Joi.date().required(),
            persons: Joi.array().required(),
            ParentCompanyId: Joi.any(),
            isAssociatedWithCraneRequest: Joi.boolean().required(),
            requestType: Joi.string().required(),
            cranePickUpLocation: Joi.string().optional().allow('', null),
            craneDropOffLocation: Joi.string().optional().allow('', null),
            CraneRequestId: Joi.number().optional().allow('', null),
            recurrenceId: Joi.number().optional().allow('', null),
            recurrenceEndDate: Joi.date().optional().allow('', null),
            previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
            nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
            recurrenceSeriesStartDate: Joi.date().required().allow('', null),
            recurrenceSeriesEndDate: Joi.date().required().allow('', null),
            seriesOption: Joi.number().required(),
            inspectionStartTime: Joi.string().required(),
            inspectionEndTime: Joi.string().required(),
            timezone: Joi.string().required(),
            ndrStatus: Joi.string().required(),
            originationAddress: Joi.string().optional().allow('', null),
            vehicleType: Joi.string().optional().allow('', null),
        }),
    },
    updateNDRStatus: {
        body: Joi.object({
            id: Joi.number().required(),
            status: Joi.string(),
            ParentCompanyId: Joi.any(),
            statuschange: Joi.string(),
            inspectionStatus: Joi.string(),
        }),
    },
    uploadBulkNDRTemplate: {
        params: Joi.object({
            ProjectId: Joi.number().required(),
            ParentCompanyId: Joi.any(),
        }),
    },
    bulkUploadinspectionRequest: {
        body: Joi.object({
            inspection_request: Joi.optional().allow(''),
        }),
    },
};
module.exports = inspectionValidation;
