const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const { UserController } = require('../controllers');
const passportConfig = require('../config/passport');
const { userValidation } = require('../middlewares/validations');

const storage = multer.memoryStorage();

const upload = multer({ storage }); // NOSONAR

const userRoute = {
  get router() {
    const router = Router();
    router
      .get('/', UserController.show)
      .get('/user_lists', UserController.userLists)
      .put('/', passportConfig.isAuthenticated, UserController.update);
    router.get(
      '/authenticated_user',
      passportConfig.isAuthenticated,
      UserController.isAuthenticatedUser,
    );
    router.get('/user_details', passportConfig.isAuthenticated, UserController.superAdminDetails);
    router.put(
      '/update_admin_profile',
      passportConfig.isAuthenticated,
      UserController.updateAdminProfile,
    );
    router.post(
      '/change_password',
      validate(userValidation.changePassword, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      UserController.changePassword,
    );
    router.post(
      '/upload_profile',
      upload.single('profile'),
      passportConfig.isAuthenticated,
      UserController.uploadProfile,
    );
    return router;
  },
};

module.exports = userRoute;
