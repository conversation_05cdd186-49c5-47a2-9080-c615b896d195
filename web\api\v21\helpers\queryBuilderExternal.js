const moment = require('moment');
const _ = require('lodash');

const timeToSeconds = (timeString) => {
  const [hours, minutes, seconds] = timeString.split(':');
  return +hours * 60 * 60 + +minutes * 60 + +seconds;
};

const buildDateRangeClause = (options) => {
  const { timezone, startDate, endDate, startTime, endTime, queryStartDate, singleQuery, tableName } = options;

  if (singleQuery) {
    return ` and (DATE_TRUNC('day', "${tableName}"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
      AND("${tableName}"."deliveryStart" AT TIME ZONE '${timezone}'):: time >= '${startTime}'
      AND("${tableName}"."deliveryStart" AT TIME ZONE '${timezone}'):: time <= '${endTime}')`;
  }

  return ` and (DATE_TRUNC('day', "${tableName}"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
    AND ("${tableName}"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${startTime}'
    AND ("${tableName}"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59') or 
    (DATE_TRUNC('day', "${tableName}"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate}'
    AND ("${tableName}"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
    AND ("${tableName}"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '${endTime}')`;
};

const buildBaseQuery = (timezone, tableName) => `
  select
  (date_trunc('hour', "${tableName}"."deliveryStart" AT TIME ZONE '${timezone}' )::timestamp) AS "hour_start"
  FROM
  public."${tableName}"`;

const buildDeliveryJoins = (incomeData) => {
  let joins = '';

  if (incomeData.memberFilter && incomeData.memberFilter != 0) {
    joins += ` JOIN "DeliveryPeople" AS "memberDetails" ON "DeliveryRequests"."id" = "memberDetails"."DeliveryId" 
      AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true 
      JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" 
      JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id"`;
  }

  if (incomeData.companyFilter && incomeData.companyFilter != 0) {
    joins += ` JOIN "DeliverCompanies" AS "companyDetails" ON "DeliveryRequests"."id" = "companyDetails"."DeliveryId" 
      AND "companyDetails"."isDeleted" = false 
      JOIN "Companies" AS "companyDetails->Company" ON "companyDetails"."CompanyId" = "companyDetails->Company"."id"`;
  }

  return joins;
};

const buildDeliveryWhereClause = (options) => {
  const { incomeData, timezone, startDate, endDate, startTime, endTime, queryStartDate, singleQuery } = options;
  let whereClause = `
    WHERE "DeliveryRequests"."ProjectId" = :project_id 
    AND "DeliveryRequests"."isQueued" = false 
    AND "DeliveryRequests"."isDeleted" = false 
    AND "DeliveryRequests"."id" not in (select "VoidLists"."DeliveryRequestId" from "VoidLists" WHERE "VoidLists"."DeliveryRequestId" is not NULL)`;

  if (incomeData.startDate && incomeData.endDate) {
    whereClause += buildDateRangeClause({
      timezone,
      startDate,
      endDate,
      startTime,
      endTime,
      queryStartDate,
      singleQuery,
      tableName: 'DeliveryRequests'
    });
  }

  return whereClause;
};

const buildCraneJoins = (incomeData) => {
  let joins = '';

  if (incomeData.memberFilter && incomeData.memberFilter != 0) {
    joins += ` JOIN "CraneRequestResponsiblePeople" AS "memberDetails" ON "CraneRequests"."id" = "memberDetails"."CraneRequestId" 
      AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true 
      JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" 
      JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id"`;
  }

  if (incomeData.companyFilter && incomeData.companyFilter != 0) {
    joins += ` JOIN "CraneRequestCompanies" AS "companyDetails" ON "CraneRequests"."id" = "companyDetails"."CraneRequestId" 
      AND "companyDetails"."isDeleted" = false 
      JOIN "Companies" AS "companyDetails->Company" ON "companyDetails"."CompanyId" = "companyDetails->Company"."id"`;
  }

  return joins;
};

const buildCraneWhereClause = (options) => {
  const { incomeData, timezone, startDate, endDate, startTime, endTime, queryStartDate, singleQuery } = options;
  let whereClause = `
    WHERE "CraneRequests"."ProjectId" = :project_id 
    AND "CraneRequests"."isDeleted" = false 
    AND "CraneRequests"."id" NOT IN (SELECT "VoidLists"."CraneRequestId" FROM "VoidLists" WHERE "VoidLists"."CraneRequestId" IS NOT NULL)`;

  if (incomeData.startDate && incomeData.endDate) {
    whereClause += buildDateRangeClause({
      timezone,
      startDate,
      endDate,
      startTime,
      endTime,
      queryStartDate,
      singleQuery,
      tableName: 'CraneRequests'
    });
  }

  return whereClause;
};

const buildConcreteJoins = (incomeData) => {
  let joins = '';

  if (incomeData.memberFilter && incomeData.memberFilter != 0) {
    joins += ` JOIN ("ConcreteRequestResponsiblePeople" AS "memberDetails" 
      INNER JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" 
      AND "memberDetails->Member"."isDeleted" = false 
      INNER JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id" 
      AND "memberDetails->Member->User"."isDeleted" = false) 
      ON "ConcreteRequests"."id" = "memberDetails"."ConcreteRequestId" 
      AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true`;
  }

  if (incomeData.companyFilter && incomeData.companyFilter != 0) {
    joins += ` JOIN "ConcreteRequestCompanies" AS "concreteSupplierDetails" ON "ConcreteRequests"."id" = "concreteSupplierDetails"."ConcreteRequestId" 
      AND "concreteSupplierDetails"."isDeleted" = false 
      JOIN "Companies" AS "concreteSupplierDetails->Company" ON "concreteSupplierDetails"."CompanyId" = "concreteSupplierDetails->Company"."id"`;
  }

  return joins;
};

const buildConcreteWhereClause = (options) => {
  const { incomeData, timezone, startDate, endDate, startTime, endTime, queryStartDate, singleQuery } = options;
  let whereClause = `
    WHERE "ConcreteRequests"."ProjectId" = :project_id 
    AND "ConcreteRequests"."isDeleted" = false 
    AND "ConcreteRequests"."id" NOT IN (SELECT "VoidLists"."ConcreteRequestId" FROM "VoidLists" WHERE "VoidLists"."ConcreteRequestId" IS NOT NULL)`;

  if (incomeData.startDate && incomeData.endDate) {
    whereClause += buildDateRangeClause({
      timezone,
      startDate,
      endDate,
      startTime,
      endTime,
      queryStartDate,
      singleQuery,
      tableName: 'ConcreteRequests'
    });
  }

  return whereClause;
};

const buildInspectionJoins = (incomeData) => {
  let joins = '';

  if (incomeData.memberFilter && incomeData.memberFilter != 0) {
    joins += ` JOIN "InspectionPeople" AS "memberDetails" ON "InspectionRequests"."id" = "memberDetails"."InspectionId" 
      AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true 
      JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" 
      JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id"`;
  }

  if (incomeData.companyFilter && incomeData.companyFilter != 0) {
    joins += ` JOIN "InspectionCompanies" AS "companyDetails" ON "InspectionRequests"."id" = "companyDetails"."InspectionId" 
      AND "companyDetails"."isDeleted" = false 
      JOIN "Companies" AS "companyDetails->Company" ON "companyDetails"."CompanyId" = "companyDetails->Company"."id"`;
  }

  return joins;
};

const buildInspectionWhereClause = (options) => {
  const { incomeData, timezone, startDate, endDate, startTime, endTime, queryStartDate, singleQuery } = options;
  let whereClause = `
    WHERE "InspectionRequests"."ProjectId" = :project_id 
    AND "InspectionRequests"."isQueued" = false 
    AND "InspectionRequests"."isDeleted" = false 
    AND "InspectionRequests"."id" not in (select "VoidLists"."InspectionRequestId" from "VoidLists" WHERE "VoidLists"."InspectionRequestId" is not NULL)`;

  if (incomeData.startDate && incomeData.endDate) {
    whereClause += buildDateRangeClause({
      timezone,
      startDate,
      endDate,
      startTime,
      endTime,
      queryStartDate,
      singleQuery,
      tableName: 'InspectionRequests'
    });
  }

  return whereClause;
};

const mainQueryuilder = (qType, incomeData, deliveryInCrane = false) => {
  const { timezone, startDate, endDate, startTime, endTime } = incomeData;
  const nextDay = moment(startDate).add(1, 'days');
  const queryStartDate = nextDay.format('YYYY-MM-DD');
  const finalFromTimeSeconds = timeToSeconds(startTime);
  const finalToTimeSeconds = timeToSeconds(endTime);
  const singleQuery = finalFromTimeSeconds <= finalToTimeSeconds;

  let mainQuery = '';
  let whereClause = '';

  const options = {
    incomeData,
    timezone,
    startDate,
    endDate,
    startTime,
    endTime,
    queryStartDate,
    singleQuery
  };

  switch (qType) {
    case 0:
      mainQuery = buildBaseQuery(timezone, 'DeliveryRequests');
      mainQuery += buildDeliveryJoins(incomeData);
      whereClause = buildDeliveryWhereClause(options);
      break;
    case 1:
      mainQuery = buildBaseQuery(timezone, 'CraneRequests');
      mainQuery += buildCraneJoins(incomeData);
      whereClause = buildCraneWhereClause(options);
      break;
    case 2:
      mainQuery = buildBaseQuery(timezone, 'ConcreteRequests');
      mainQuery += buildConcreteJoins(incomeData);
      whereClause = buildConcreteWhereClause(options);
      break;
    case 3:
      mainQuery = buildBaseQuery(timezone, 'InspectionRequests');
      mainQuery += buildInspectionJoins(incomeData);
      whereClause = buildInspectionWhereClause(options);
      break;
    default:
      throw new Error('please send valid template type');
  }

  return mainQuery.concat(whereClause);
};

const queryBuilderExternal = async (inputData, incomeData, params) => {
  let mainQuery = ``;
  const whereClause = ``;
  const baseQuery = ` WITH time_slots AS ( `;
  const orderGropuQuery = `)
                            select
                            to_char("hour_start", 'Month DD,YYYY') as "Date", to_char("hour_start", 'HH12 PM') AS "Time", count(to_char(hour_start, 'HH12 PM')) as "Count"
                            FROM
                            time_slots group by "Date", "Time"    ORDER BY "Date", "Time" ${params.sortOrder};`;

  if (incomeData.templateType.length === 1) {
    // 0 for Delivery Request Heat map Only
    if (incomeData.templateType.find((ele) => ele.id == 0)) {
      mainQuery = mainQueryuilder(0, incomeData);
      // 1 for Crane Request Heat map Only
    } else if (incomeData.templateType.find((ele) => ele.id == 1)) {
      mainQuery = mainQueryuilder(1, incomeData);
      // 2 for Concrete Request Heat map Only
    } else if (incomeData.templateType.find((ele) => ele.id == 2)) {
      mainQuery = mainQueryuilder(2, incomeData);
      // 3 for Inspection Request Heat map Only
    } else if (incomeData.templateType.find((ele) => ele.id == 3)) {
      mainQuery = mainQueryuilder(3, incomeData);
    } else {
      throw new Error('please send valid template type');
    }
  } else if (incomeData.templateType.length > 1 && incomeData.templateType.length < 5) {
    _.forEach(incomeData.templateType, (d, i) => {
      if (i !== incomeData.templateType.length - 1) {
        if (
          (d.id == 1 && incomeData.gateFilter != 0) ||
          (d.id == 2 &&
            (incomeData.gateFilter != 0 ||
              incomeData.equipmentFilter != 0 ||
              incomeData.defineFilter != 0))
        ) {
          return;
        }
        if (
          i == incomeData.templateType.length - 2 &&
          ((incomeData.templateType[incomeData.templateType.length - 1].id == 1 &&
            incomeData.gateFilter != 0) ||
            (incomeData.templateType[incomeData.templateType.length - 1].id == 2 &&
              (incomeData.gateFilter != 0 ||
                incomeData.equipmentFilter != 0 ||
                incomeData.defineFilter != 0)))
        ) {
          mainQuery += `${mainQueryuilder(d.id, incomeData)} `;
          return;
        }
        mainQuery += `${mainQueryuilder(d.id, incomeData)} union all `;
      } else {
        if (
          (d.id == 1 && incomeData.gateFilter != 0) ||
          (d.id == 2 &&
            (incomeData.gateFilter != 0 ||
              incomeData.equipmentFilter != 0 ||
              incomeData.defineFilter != 0))
        ) {
          return;
        }
        mainQuery += mainQueryuilder(d.id, incomeData);
      }
    });
  } else {
    throw new Error('please send valid template type or please check template type length');
  }

  const OutQuery = baseQuery.concat(mainQuery).concat(whereClause).concat(orderGropuQuery);

  return OutQuery;
};

const convertToCron = (dateTime) => {
  const date = moment(dateTime);
  return `${0} ${date.minute()} ${date.hour()} ${date.date()} ${date.month() + 1} ` + `*`;
};

const convertToCronYearly = (order, day, month, hour, minute) => {
  let orderValue;
  let dayValue;
  switch (order) {
    case 'first':
      orderValue = 1;
      break;
    case 'second':
      orderValue = 2;
      break;
    case 'third':
      orderValue = 3;
      break;
    case 'fourth':
      orderValue = 4;
      break;
    case 'last':
      orderValue = 'L';
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  switch (day) {
    case 'SUN':
      dayValue = 0;
      break;
    case 'MON':
      dayValue = 1;
      break;
    case 'TUE':
      dayValue = 2;
      break;
    case 'WED':
      dayValue = 3;
      break;
    case 'THU':
      dayValue = 4;
      break;
    case 'FRI':
      dayValue = 5;
      break;
    case 'SAT':
      dayValue = 6;
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  return order == 'last'
    ? `${minute} ${hour} * ${month} ${dayValue}${orderValue}`
    : `${minute} ${hour} * ${month} ${dayValue}#${orderValue}`;
};

const convertToCronMonthly = (order, day, every, hour, minute) => {
  let orderValue;
  let dayValue;
  switch (order) {
    case 'first':
      orderValue = 1;
      break;
    case 'second':
      orderValue = 2;
      break;
    case 'third':
      orderValue = 3;
      break;
    case 'fourth':
      orderValue = 4;
      break;
    case 'last':
      orderValue = 'L';
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  switch (day) {
    case 'SUN':
      dayValue = 0;
      break;
    case 'MON':
      dayValue = 1;
      break;
    case 'TUE':
      dayValue = 2;
      break;
    case 'WED':
      dayValue = 3;
      break;
    case 'THU':
      dayValue = 4;
      break;
    case 'FRI':
      dayValue = 5;
      break;
    case 'SAT':
      dayValue = 6;
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  return order == 'last'
    ? `${minute} ${hour} * */${every} ${dayValue}${orderValue}`
    : `${minute} ${hour} * */${every} ${dayValue}#${orderValue}`;
};

const buildDateReplacements = (incomeData, inputData) => {
  if (!incomeData.startdate || !incomeData.enddate) {
    return {};
  }

  const startDateTime = moment(incomeData.startdate, 'YYYY-MM-DD')
    .startOf('day')
    .utcOffset(Number(inputData.headers.timezoneoffset), true);
  const endDateTime = moment(incomeData.enddate, 'YYYY-MM-DD')
    .endOf('day')
    .utcOffset(Number(inputData.headers.timezoneoffset), true);

  return {
    start_date: moment(startDateTime).format(),
    end_date: moment(endDateTime).format(),
  };
};

const buildFilterReplacements = (incomeData) => {
  const filterMappings = [
    { key: 'statusFilter', replacementKey: 'status', condition: (value) => value && value !== '' && value !== 0 },
    { key: 'gateFilter', replacementKey: 'gate', condition: (value) => value && value !== 0 },
    { key: 'equipmentFilter', replacementKey: 'equipment', condition: (value) => value && value !== 0 },
    { key: 'defineFilter', replacementKey: 'define', condition: (value) => value && value !== 0 },
    { key: 'memberFilter', replacementKey: 'member', condition: (value) => value && value !== 0 },
    { key: 'companyFilter', replacementKey: 'company', condition: (value) => value && value !== 0 },
    { key: 'locationFilter', replacementKey: 'location', condition: (value) => value && value !== 0 },
  ];

  return filterMappings.reduce((replacements, { key, replacementKey, condition }) => {
    if (condition(incomeData[key])) {
      if (key === 'statusFilter') {
        return {
          ...replacements,
          status: incomeData[key],
          status_fix: incomeData[key] === 'Delivered' ? 'Completed' : incomeData[key],
        };
      }
      return { ...replacements, [replacementKey]: incomeData[key] };
    }
    return replacements;
  }, {});
};

const replacementsBuilderExternal = async (inputData, incomeData, params) => {
  const offset = (+params.pageNo - 1) * +params.pageSize;
  const baseReplacements = { project_id: +params.ProjectId, offset, limit: +params.pageSize };

  const dateReplacements = buildDateReplacements(incomeData, inputData);
  const filterReplacements = buildFilterReplacements(incomeData);

  return {
    ...baseReplacements,
    ...dateReplacements,
    ...filterReplacements,
  };
};

const defaultTimeSlots = () => {
  const baseObj = {};
  const timeslots = [
    '01 AM',
    '02 AM',
    '03 AM',
    '04 AM',
    '05 AM',
    '06 AM',
    '07 AM',
    '08 AM',
    '09 AM',
    '10 AM',
    '11 AM',
    '12 PM',
    '01 PM',
    '02 PM',
    '03 PM',
    '04 PM',
    '05 PM',
    '06 PM',
    '07 PM',
    '08 PM',
    '09 PM',
    '10 PM',
    '11 PM',
    '12 AM',
  ];
  _.forEach(timeslots, (d) => (baseObj[d] = 0));
  return baseObj;
};

module.exports = {
  queryBuilderExternal,
  replacementsBuilderExternal,
  defaultTimeSlots,
  convertToCron,
  convertToCronMonthly,
  convertToCronYearly,
};
