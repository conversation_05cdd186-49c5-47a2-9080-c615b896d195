module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestHistory = sequelize.define(
    'ConcreteRequestHistory',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      ConcreteRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      type: {
        type: DataTypes.STRING,
      },
      description: DataTypes.STRING,
    },
    {},
  );
  ConcreteRequestHistory.associate = (models) => {
    ConcreteRequestHistory.belongsTo(models.Member);
    ConcreteRequestHistory.belongsTo(models.ConcreteRequest);
  };
  ConcreteRequestHistory.getAll = async (attr) => {
    const newCncreteRequestHistory = await ConcreteRequestHistory.findAll({
      where: { ...attr },
    });
    return newCncreteRequestHistory;
  };
  ConcreteRequestHistory.createInstance = async (paramData) => {
    const newCncreteRequestHistory = await ConcreteRequestHistory.create(paramData);
    return newCncreteRequestHistory;
  };
  return ConcreteRequestHistory;
};
