module.exports = (sequelize, DataTypes) => {
  const InspectionEquipment = sequelize.define(
    'InspectionEquipment',
    {
      InspectionId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      InspectionCode: DataTypes.INTEGER,
      EquipmentId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  InspectionEquipment.associate = (models) => {
    // associations can be defined here
    InspectionEquipment.belongsTo(models.InspectionRequest, {
      as: 'inspectionrequest',
      foreignKey: 'InspectionId',
    });
    InspectionEquipment.belongsTo(models.Equipments, {
      as: 'Equipments',
      foreignKey: 'EquipmentId',
    });
    InspectionEquipment.belongsTo(models.Equipments);
  };
  InspectionEquipment.createInstance = async (paramData) => {
    const newInspectionEquipment = await InspectionEquipment.create(paramData);
    return newInspectionEquipment;
  };
  return InspectionEquipment;
};
