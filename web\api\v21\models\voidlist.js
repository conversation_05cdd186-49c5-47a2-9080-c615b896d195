module.exports = (sequelize, DataTypes) => {
  const VoidList = sequelize.define(
    'VoidList',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      DeliveryRequestId: DataTypes.INTEGER,
      CraneRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      isDeliveryRequest: DataTypes.BOOLEAN,
      ConcreteRequestId: DataTypes.INTEGER,
      InspectionRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  VoidList.associate = (models) => {
    VoidList.belongsTo(models.Project);
    VoidList.belongsTo(models.Member);
    VoidList.belongsTo(models.DeliveryRequest);
    VoidList.belongsTo(models.CraneRequest);
    VoidList.belongsTo(models.InspectionRequest);
    return VoidList;
  };
  VoidList.createInstance = async (voidData) => {
    const newVoidData = await VoidList.create(voidData);
    return newVoidData;
  };
  return VoidList;
};
