/* eslint-disable no-loop-func */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
const moment = require('moment');
const Moment = require('moment');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');

const momentRange = MomentRange.extendMoment(Moment);
const Cryptr = require('cryptr');
const ApiError = require('../helpers/apiError');

const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  TimeZone,
} = require('../models');
let {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  Role,
  Gates,
  Equipments,
  DeliverDefineWork,
  Company,
  Project,
  DeliverDefine,
  DeliverHistory,
  VoidList,
  User,
  Notification,
  DeliveryPersonNotification,
  CraneRequestResponsiblePerson,
  CraneRequest,
} = require('../models');
const {
  CraneRequestDefinableFeatureOfWork,
  CraneRequestCompany,
  CraneRequestEquipment,
  CraneGate,
  CraneRequestHistory,
  ConcreteRequest,
  RequestRecurrenceSeries,
  LocationNotificationPreferences,
  Locations,
  ProjectSettings,
  EquipmentMapping
} = require('../models');
const MAILER = require('../mailer');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const concreteRequestService = require('./concreteRequestService');
const voidService = require('./voidService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const craneRequestService = {
  // Helper function to validate timezone for crane requests
  async validateCraneTimeZone(timeZoneId) {
    const eventTimeZone = await TimeZone.findOne({
      where: {
        isDeleted: false,
        id: +timeZoneId,
      },
      attributes: [
        'id',
        'location',
        'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes',
        'dayLightSavingTimeInMinutes',
        'timezone',
      ],
    });
    return eventTimeZone;
  },

  // Helper function to validate crane request times
  validateCraneRequestTimes(craneRequestDetail) {
    if (craneRequestDetail.startPicker === craneRequestDetail.endPicker) {
      return { error: true, message: 'Delivery Start time and End time should not be the same' };
    }
    if (craneRequestDetail.startPicker > craneRequestDetail.endPicker) {
      return { error: true, message: 'Please enter From Time lesser than To Time' };
    }
    return { error: false };
  },

  // Helper function to validate crane delivery window dates
  async validateCraneDeliveryWindowDates(craneRequestDetail, eventTimeZone, projectDetails) {
    let startDate;
    let endDate;
    if (craneRequestDetail.recurrence) {
      startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        craneRequestDetail.craneDeliveryStart,
        craneRequestDetail.startPicker,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
      endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        craneRequestDetail.craneDeliveryEnd,
        craneRequestDetail.endPicker,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
    }

    if (startDate || endDate) {
      if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
        if (craneRequestDetail.recurrence === 'Does Not Repeat') {
          return { error: true, message: 'Please enter Future Date/Time' };
        }
        return { error: true, message: 'Please enter Future Start or End Date/Time' };
      }
      return {
        error: true,
        message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
      };
    }
    return { error: false };
  },

  // Helper function to get next crane request ID
  async getNextCraneRequestId(projectId) {
    let lastData = {};
    lastData = await CraneRequest.findOne({
      where: { ProjectId: +projectId, isDeleted: false },
      order: [['CraneRequestId', 'DESC']],
    });
    const deliveryRequestList = await DeliveryRequest.findOne({
      where: {
        ProjectId: +projectId,
        isDeleted: false,
        isAssociatedWithCraneRequest: true,
      },
      order: [['CraneRequestId', 'DESC']],
    });
    if (deliveryRequestList) {
      if (lastData) {
        if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      } else {
        lastData = {};
        lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
      }
    }
    if (lastData) {
      const data = lastData.CraneRequestId;
      lastData.CraneRequestId = 0;
      lastData.CraneRequestId = data + 1;
    } else {
      lastData = {};
      lastData.CraneRequestId = 1;
    }
    let craneId = 0;
    const newId = JSON.parse(JSON.stringify(lastData));
    if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
      craneId = newId.CraneRequestId;
    }
    return craneId;
  },

  // Helper function to create crane request parameters
  createCraneRequestParam(craneRequestDetail, id, craneDeliveryStart, craneDeliveryEnd, memberDetails, recurrenceId) {
    return {
      description: craneRequestDetail.description,
      CraneRequestId: id,
      craneDeliveryStart,
      craneDeliveryEnd,
      ProjectId: craneRequestDetail.ProjectId,
      createdBy: memberDetails.id,
      recurrenceId,
      LocationId: craneRequestDetail.LocationId,
      OriginationAddress: craneRequestDetail.originationAddress,
    };
  },

  // Helper function to set crane request approval status
  setCraneRequestApprovalStatus(craneParam, memberDetails, roleDetails, accountRoleDetails, projectDetails) {
    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      craneParam.status = 'Approved';
      craneParam.approvedBy = memberDetails.id;
      craneParam.approved_at = new Date();
    }
    return craneParam;
  },

  // Helper function to process daily crane recurrence
  async processDailyCraneRecurrence(params) {
    const { craneRequestDetail, user, eventTimeZone, totalDays, startId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
    const startTime = craneRequestDetail.startPicker;
    const endTime = craneRequestDetail.endPicker;
    let dailyIndex = 0;
    let id = startId;
    const eventsArray = [];

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      craneRequestDetail,
      user,
      'craneRequest',
      eventTimeZone.timezone,
    );

    while (dailyIndex < totalDays.length) {
      const data = totalDays[dailyIndex];
      if (
        moment(data).isBetween(
          moment(craneRequestDetail.craneDeliveryStart),
          moment(craneRequestDetail.craneDeliveryEnd),
          null,
          '[]',
        ) ||
        moment(data).isSame(craneRequestDetail.craneDeliveryStart) ||
        moment(data).isSame(craneRequestDetail.craneDeliveryEnd)
      ) {
        id += 1;
        const date = moment(data).format('MM/DD/YYYY');
        const chosenTimezoneCraneDeliveryStart = moment.tz(
          `${date} ${startTime}`,
          'MM/DD/YYYY HH:mm',
          eventTimeZone.timezone,
        );
        const chosenTimezoneCraneDeliveryEnd = moment.tz(
          `${date} ${endTime}`,
          'MM/DD/YYYY HH:mm',
          eventTimeZone.timezone,
        );
        const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');
        const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');

        let craneRequestParam = {
          description: craneRequestDetail.description,
          isEscortNeeded: craneRequestDetail.isEscortNeeded,
          LocationId: craneRequestDetail.LocationId,
          additionalNotes: craneRequestDetail.additionalNotes,
          CraneRequestId: id,
          craneDeliveryStart,
          craneDeliveryEnd,
          ProjectId: craneRequestDetail.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
          pickUpLocation: craneRequestDetail.pickUpLocation,
          dropOffLocation: craneRequestDetail.dropOffLocation,
          recurrenceId,
        };

        craneRequestParam = this.setCraneRequestApprovalStatus(
          craneRequestParam,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );

        eventsArray.push(craneRequestParam);
        dailyIndex += +craneRequestDetail.repeatEveryCount;
      }
    }

    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
      );
      if (isOverlapping?.error) {
        return {
          error: true,
          message: isOverlapping.message,
        };
      }
    }

    return {
      error: false,
      eventsArray,
      lastId: id
    };
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async _resolveDomainContext(inputData) {
    let domainName = inputData?.user?.domainName;
    const email = inputData?.user?.email;
    const ParentCompanyId =
      inputData?.body?.ParentCompanyId ?? inputData?.params?.ParentCompanyId;

    let enterpriseValue = null;

    // Try resolving using domainName
    ({ domainName, enterpriseValue } = await this._resolveByDomainName(domainName));

    // If domainName wasn't resolved, try ParentCompanyId/user membership
    if (!enterpriseValue) {
      ({ domainName, enterpriseValue } = await this._resolveByParentCompany(
        domainName,
        ParentCompanyId,
        email
      ));
    }

    return { domainName, enterpriseValue };
  },

  async _resolveByDomainName(domainName) {
    let enterpriseValue = null;

    if (domainName) {
      const found = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (found) {
        enterpriseValue = found;
        domainName = found.name.toLowerCase();
        return { domainName, enterpriseValue };
      } else {
        domainName = '';
      }
    }
    return { domainName, enterpriseValue };
  },

  async _resolveByParentCompany(domainName, ParentCompanyId, email) {
    let enterpriseValue = null;

    const hasParent =
      ParentCompanyId !== undefined && ParentCompanyId !== 'undefined';

    if (!domainName && hasParent) {
      let userData = null;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }

      let whereClause = { ParentCompanyId, status: 'completed' };

      if (userData) {
        const memberData = await publicMember.findOne({
          where: {
            UserId: userData.id,
            RoleId: { [Op.ne]: 4 },
            isDeleted: false,
          },
        });

        if (memberData?.isAccount) {
          whereClause = { id: memberData.EnterpriseId, status: 'completed' };
        }
      }

      enterpriseValue = await Enterprise.findOne({ where: whereClause });
      domainName = enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
    }

    return { domainName, enterpriseValue };
  },


  async _loadAndBindDomainModels(domainName, inputData, enterpriseValue) {
    const modelObj = await helper.getDynamicModel(domainName);

    // Expose models. Prefer attaching to `this` to avoid leaking globals.
    this.DeliveryRequest = modelObj.DeliveryRequest;
    this.Member = modelObj.Member;
    this.DeliveryPerson = modelObj.DeliveryPerson;
    this.DeliverGate = modelObj.DeliverGate;
    this.DeliverEquipment = modelObj.DeliverEquipment;
    this.DeliverCompany = modelObj.DeliverCompany;
    this.Role = modelObj.Role;
    this.Gates = modelObj.Gates;
    this.Equipments = modelObj.Equipments;
    this.DeliverDefineWork = modelObj.DeliverDefineWork;
    this.Company = modelObj.Company;
    this.Project = modelObj.Project;
    this.User = modelObj.User;
    this.DeliverDefine = modelObj.DeliverDefine;
    this.DeliverHistory = modelObj.DeliverHistory;
    this.VoidList = modelObj.VoidList;
    this.DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    this.CraneRequestResponsiblePerson = modelObj.CraneRequestResponsiblePerson;
    this.CraneRequest = modelObj.CraneRequest;
    this.Notification = modelObj.Notification;

    // Preserve original behavior: if enterpriseValue found, reload user from the *domain* User model.
    if (enterpriseValue && inputData?.user?.email) {
      const newUser = await this.User.findOne({
        where: { email: inputData.user.email },
      });
      if (newUser) {
        // Mutates caller object (original code did this).
        inputData.user = newUser;
      }
    }

    // Original code declared ProjectId but never set it. Leaving null until you wire it in.
    const ProjectId = null;
    return { ProjectId };
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const { domainName, enterpriseValue } = await this._resolveDomainContext(
      inputData
    );

    const { ProjectId } = await this._loadAndBindDomainModels(
      domainName,
      inputData,
      enterpriseValue
    );

    return ProjectId; // currently null unless you set it in _loadAndBindDomainModels
  },

  async listCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      const { sort, sortByField } = incomeData;

      if (params.void >= 1 && params.void <= 0) {
        return done(null, { message: 'Please enter void as 1 or 0' });
      }

      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: params.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });

      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      // --- Pre-fetch Void Lists ---
      const [voidDelivery, voidCraneDelivery] = await this._getVoidLists(params.ProjectId);

      // --- Build conditions ---
      const { craneCondition, condition } = this._buildConditions(params, voidDelivery, voidCraneDelivery);

      // --- Fetch Requests ---
      const { craneRequestList, deliveryRequest } = await this._fetchRequests({
        inputData, incomeData, memberDetails, craneCondition, condition, sort, sortByField, params
      });

      // --- Process Search & Limit ---
      const finalList = await this._processSearchAndLimit({
        inputData, incomeData, craneRequestList, deliveryRequest, memberDetails, sort, sortByField, params
      });

      return done(finalList, false);
    } catch (e) {
      return done(null, e);
    }
  },

  // ----------------- Helper Methods -----------------

  async _getVoidLists(projectId) {
    const voidDeliveryList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    const voidDelivery = voidDeliveryList.map(e => e.DeliveryRequestId);

    const voidCraneRequestList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeliveryRequest: false,
        CraneRequestId: { [Op.ne]: null },
      },
    });
    const voidCraneDelivery = voidCraneRequestList.map(e => e.CraneRequestId);

    return [voidDelivery, voidCraneDelivery];
  },

  _buildConditions(params, voidDelivery, voidCraneDelivery) {
    const baseCondition = { ProjectId: +params.ProjectId, isDeleted: false };
    const condition = { ...baseCondition };
    const craneCondition = { ...baseCondition };

    const isVoidZero = params.void === '0' || params.void === 0;
    condition['$DeliveryRequest.id$'] = { [Op.and]: [isVoidZero ? { [Op.notIn]: voidDelivery } : { [Op.in]: voidDelivery }] };
    craneCondition['$CraneRequest.id$'] = { [Op.and]: [isVoidZero ? { [Op.notIn]: voidCraneDelivery } : { [Op.in]: voidCraneDelivery }] };

    return { condition, craneCondition };
  },

  async _fetchRequests(paramsData) {
    const { inputData, incomeData, memberDetails, craneCondition, condition, sort, sortByField, params } = paramsData;
    const roleId = memberDetails.RoleId;
    const memberId = memberDetails.id;

    const craneRequestList = (incomeData.gateFilter > 0 || incomeData.statusFilter === 'Delivered')
      ? []
      : await CraneRequest.getAll({
        req: inputData,
        roleId: roleId,
        attr: craneCondition,
        filters: {
          description: incomeData.descriptionFilter,
          company: incomeData.companyFilter,
          member: incomeData.memberFilter,
          equipment: incomeData.equipmentFilter,
          status: incomeData.statusFilter,
          id: incomeData.idFilter,
          pickFrom: incomeData.pickFrom,
          pickTo: incomeData.pickTo
        },
        dateRange: {
          startdate: incomeData.dateFilter || incomeData.startdate,
          enddate: incomeData.dateFilter || incomeData.enddate
        },
        search: incomeData.search,
        order: sort,
        sort: sort,
        sortColumn: sortByField
      });

    const deliveryRequest = (incomeData.statusFilter === 'Completed')
      ? []
      : await DeliveryRequest.getCraneAssociatedRequest(
        inputData, roleId, memberId, condition, incomeData.descriptionFilter,
        incomeData.startdate, incomeData.enddate, incomeData.companyFilter,
        incomeData.memberFilter, incomeData.equipmentFilter, incomeData.statusFilter,
        incomeData.idFilter, incomeData.pickFrom, incomeData.pickTo,
        incomeData.search, incomeData.gateFilter, undefined, sort, sortByField,
        params.void, incomeData.dateFilter
      );

    return { craneRequestList, deliveryRequest };
  },

  async _processSearchAndLimit(paramsData) {
    let { inputData, incomeData, craneRequestList, deliveryRequest, memberDetails, sort, sortByField, params } = paramsData;
    craneRequestList = await this._getSearchCraneDataPromise(inputData, incomeData, craneRequestList, memberDetails, params);
    deliveryRequest = await this._getSearchDeliveryDataPromise(inputData, incomeData, deliveryRequest, memberDetails, params);

    craneRequestList.push(...deliveryRequest);

    const limitedData = await this._getLimitDataPromise(
      craneRequestList, 0, +params.pageSize, [], incomeData, inputData.headers.timezoneoffset
    );

    if (sort === 'ASC') {
      limitedData.sort((a, b) => {
        if (a[sortByField] > b[sortByField]) {
          return 1;
        } else if (b[sortByField] > a[sortByField]) {
          return -1;
        } else {
          return 0;
        }
      });

    } else {
      limitedData.sort((a, b) => {
        if (b[sortByField] > a[sortByField]) {
          return 1;
        } else if (a[sortByField] > b[sortByField]) {
          return -1;
        } else {
          return 0;
        }
      });

    }

    return {
      count: craneRequestList.length,
      rows: limitedData.slice((+params.pageNo - 1) * +params.pageSize, (+params.pageNo - 1) * +params.pageSize + +params.pageSize),
    };
  },

  // Convert callback-based methods to promises
  _getSearchCraneDataPromise(inputData, incomeData, craneRequestList, memberDetails, params) {
    return new Promise((resolve, reject) => {
      this.getSearchCraneData(
        { req: inputData, incomeData, deliveryList: craneRequestList, result: [], limit: +params.pageSize, index: 0, count: 0, memberDetails },
        (res, err) => {
          if (err) {
            const errorObj = err instanceof Error ? err : new Error(err);
            return reject(errorObj);
          }
          resolve(res);
        }
      );
    });
  },

  _getSearchDeliveryDataPromise(inputData, incomeData, deliveryRequest, memberDetails, params) {
    return new Promise((resolve, reject) => {
      this.getSearchDeliveryData(
        { req: inputData, incomeData, deliveryList: deliveryRequest, result: [], limit: +params.pageSize, index: 0, count: 0, memberDetails },
        (res, err) => {
          if (err) {
            const errorObj = err instanceof Error ? err : new Error(err);
            return reject(errorObj);
          }
          resolve(res);
        }
      );
    });
  },

  _getLimitDataPromise(craneRequestList, start, limit, result, incomeData, timezoneOffset) {
    return new Promise((resolve, reject) => {
      this.getLimitData(
        craneRequestList, start, limit, result, incomeData, timezoneOffset,
        (res, err) => {
          if (err) {
            const errorObj = err instanceof Error ? err : new Error(err);
            return reject(errorObj);
          }
          resolve(res);
        }
      );
    });
  },

  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(result, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async lastCraneRequest(inputData, done) {
    try {
      if (!inputData) {
        return done(null, { message: 'Invalid request data' });
      }
      const { params = {} } = inputData;
      let data;
      let lastData = {};
      lastData = await CraneRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['CraneRequestId', 'DESC']],
      });
      const deliveryRequestList = await DeliveryRequest.findOne({
        where: {
          ProjectId: params.ProjectId,
          isDeleted: false,
          isAssociatedWithCraneRequest: true,
        },
        order: [['CraneRequestId', 'DESC']],
      });
      if (deliveryRequestList) {
        if (lastData) {
          if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
            lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
          }
        } else {
          lastData = {};
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      }
      if (lastData) {
        data = lastData.CraneRequestId + 1;
      } else {
        data = 1;
      }
      done({ CraneRequestId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },

  async prepareCraneRequestInput(inputData) {
    await this.getDynamicModel(inputData);
    const requestBody = inputData.body || {};
    const eventTimeZone = await this.validateCraneTimeZone(requestBody.TimeZoneId);
    if (!eventTimeZone) {
      return { error: true, message: 'Provide a valid timezone' };
    }

    const craneRequestDetail = requestBody;
    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +craneRequestDetail.ProjectId,
    });

    if (!projectDetails?.ProjectSettings) {
      return { error: true, message: 'Project does not exist or has no settings' };
    }

    const timeValidation = this.validateCraneRequestTimes(craneRequestDetail);
    if (timeValidation.error) {
      return { error: true, message: timeValidation.message };
    }

    const windowValidation = await this.validateCraneDeliveryWindowDates(craneRequestDetail, eventTimeZone, projectDetails);
    if (windowValidation.error) {
      return { error: true, message: windowValidation.message };
    }

    return { craneRequestDetail, eventTimeZone, projectDetails };
  },

  async generateCraneEvents(inputData, craneRequestDetail, eventTimeZone, projectDetails) {
    const loginUser = inputData.user;
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: craneRequestDetail.ProjectId,
      isActive: true,
      isDeleted: false,
    });

    if (!memberDetails) {
      return { error: true, message: 'You are not allowed to create Crane Booking for this project.' };
    }

    let id = await this.getNextCraneRequestId(memberDetails.ProjectId);
    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');
    const recurrence = craneRequestDetail.recurrence;
    let eventsArray = [];

    switch (recurrence) {
      case 'Daily': {
        const dailyResult = await this.processDailyCraneRecurrence({
          craneRequestDetail,
          user: loginUser,
          eventTimeZone,
          id,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        });
        if (dailyResult.error) return dailyResult;
        eventsArray = dailyResult.eventsArray;
        break;
      }

      case 'Weekly': {
        const weeklyResult = await this.processWeeklyCraneRecurrence({
          craneRequestDetail, eventTimeZone, loginUser, id,
          memberDetails, roleDetails, accountRoleDetails, projectDetails
        });
        if (weeklyResult.error) return weeklyResult;
        eventsArray = weeklyResult.eventsArray;
        break;
      }

      case 'Monthly': {
        const monthlyResult = await this.processMonthlyCraneRecurrence({
          craneRequestDetail, eventTimeZone, loginUser, id,
          memberDetails, roleDetails, accountRoleDetails, projectDetails
        });
        if (monthlyResult.error) return monthlyResult;
        eventsArray = monthlyResult.eventsArray;
        break;
      }

      case 'Yearly': {
        const yearlyResult = await this.processYearlyCraneRecurrence({
          craneRequestDetail, eventTimeZone, loginUser, id,
          memberDetails, roleDetails, accountRoleDetails, projectDetails
        });
        if (yearlyResult.error) return yearlyResult;
        eventsArray = yearlyResult.eventsArray;
        break;
      }

      case 'Does Not Repeat': {
        const singleResult = await this.processSingleCraneRequest({
          craneRequestDetail, eventTimeZone, loginUser, id,
          memberDetails, roleDetails, accountRoleDetails, projectDetails
        });
        if (singleResult.error) return singleResult;
        eventsArray = singleResult.eventsArray;
        break;
      }

      default:
        return { error: true, message: `Unsupported recurrence type: ${recurrence}` };
    }


    const isOverlapping = await this.checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, 'add');
    if (isOverlapping?.error) {
      return { error: true, message: isOverlapping.message };
    }

    return { eventsArray, memberDetails, roleDetails, accountRoleDetails };
  },


  async createCraneRequestBookings(eventsArray, inputData, memberDetails, craneRequestDetail, projectDetails, done) {
    const loginUser = inputData.user;
    let newCraneRequestData = {};

    for (const event of eventsArray) {
      newCraneRequestData = await CraneRequest.createInstance(event);
      await this.createCraneRequestAssociations(event, craneRequestDetail, newCraneRequestData);

      await this.createCraneRequestHistory(event, newCraneRequestData, loginUser, memberDetails, craneRequestDetail, projectDetails);
      if (newCraneRequestData.status === 'Approved') {
        await this.createCraneRequestApprovalHistory(newCraneRequestData, memberDetails, craneRequestDetail, loginUser);
      }
    }

    if (Object.keys(newCraneRequestData).length > 0) {
      const history = await this.dispatchNotifications(newCraneRequestData, inputData, memberDetails, projectDetails, craneRequestDetail);
      return done(history, false);
    }

    return done(null, { message: 'Bookings will not be created for the scheduled date/time' });
  },

  async newCraneRequest(inputData, done) {
    try {
      const prepResult = await this.prepareCraneRequestInput(inputData);
      if (prepResult.error) return done(null, { message: prepResult.message });

      const { craneRequestDetail, eventTimeZone, projectDetails } = prepResult;
      this.checkInputDatas(inputData, async (checkResponse, checkError) => {
        if (checkError) return done(null, checkError);

        const eventResult = await this.generateCraneEvents(inputData, craneRequestDetail, eventTimeZone, projectDetails);
        if (eventResult.error) return done(null, { message: eventResult.message });

        const { eventsArray, memberDetails } = eventResult;
        await this.createCraneRequestBookings(eventsArray, inputData, memberDetails, craneRequestDetail, projectDetails, done);
      });
    } catch (e) {
      done(null, e);
    }
  },

  async processMonthlyCraneRecurrence(
    params
  ) {
    let { craneRequestDetail,
      eventTimeZone,
      loginUser,
      id,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails } = params;

    const startTime = craneRequestDetail.startPicker;
    const endTime = craneRequestDetail.endPicker;
    const startDate = moment(craneRequestDetail.craneDeliveryStart);
    const endDate = moment(craneRequestDetail.craneDeliveryEnd);
    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      craneRequestDetail,
      loginUser,
      'craneRequest',
      eventTimeZone.timezone
    );

    const eventsArray = [];
    let currentDate = startDate.clone();

    while (currentDate.isSameOrBefore(endDate, 'month')) {
      const recurrenceDate = craneRequestDetail.chosenDateOfMonth
        ? currentDate.clone().date(parseInt(craneRequestDetail.dateOfMonth))
        : this.getNthWeekdayOfMonth(currentDate, craneRequestDetail.monthlyRepeatType);

      if (recurrenceDate?.isBetween(startDate, endDate, null, '[]')) {
        id += 1;
        const dateStr = recurrenceDate.format('MM/DD/YYYY');
        const start = moment.tz(`${dateStr} ${startTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone).tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
        const end = moment.tz(`${dateStr} ${endTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone).tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

        const craneRequestParam = {
          description: craneRequestDetail.description,
          isEscortNeeded: craneRequestDetail.isEscortNeeded,
          additionalNotes: craneRequestDetail.additionalNotes,
          CraneRequestId: id,
          craneDeliveryStart: start,
          craneDeliveryEnd: end,
          ProjectId: craneRequestDetail.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
          pickUpLocation: craneRequestDetail.pickUpLocation,
          dropOffLocation: craneRequestDetail.dropOffLocation,
          recurrenceId,
          LocationId: craneRequestDetail.LocationId,
          status: this.determineCraneStatus(memberDetails, roleDetails, accountRoleDetails, projectDetails),
          approvedBy: memberDetails.id,
          approved_at: new Date(),
        };

        eventsArray.push(craneRequestParam);
      }

      currentDate.add(craneRequestDetail.repeatEveryCount || 1, 'months');
    }

    return { eventsArray };
  },

  // Helper: get nth weekday of a month (e.g., 'second Monday')
  getNthWeekdayOfMonth(baseDate, monthlyRepeatType) {
    const [week, day] = monthlyRepeatType.split(' ');
    const weekday = moment().day(day).day();
    const firstOfMonth = baseDate.clone().startOf('month');
    let result = firstOfMonth.clone().day(weekday);
    if (result.date() > 7) result.add(7, 'days');
    const weekMap = { first: 0, second: 1, third: 2, fourth: 3, last: -1 };

    if (week === 'last') {
      const lastDay = baseDate.clone().endOf('month').day(weekday);
      return lastDay.date() > baseDate.daysInMonth() ? lastDay.subtract(7, 'days') : lastDay;
    }

    return result.add(weekMap[week] * 7, 'days');
  },

  determineCraneStatus(memberDetails, roleDetails, accountRoleDetails, projectDetails) {
    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      return 'Approved';
    }
    return 'Pending';
  },


  async createCraneRequestAssociations(event, craneRequestDetail, newCraneRequestData) {
    const updateParam = {
      CraneRequestId: newCraneRequestData.id,
      CraneRequestCode: newCraneRequestData.CraneRequestId,
      ProjectId: craneRequestDetail.ProjectId,
    };

    const gates = [craneRequestDetail.GateId];
    const equipments = craneRequestDetail.EquipmentId;
    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestDetail;

    await this.createEquipmentMapping({
      EquipmentId: craneRequestDetail.EquipmentId,
      startTime: event.craneDeliveryStart,
      endTime: event.craneDeliveryEnd,
      GateId: craneRequestDetail.GateId,
      LocationId: event.LocationId,
      CraneId: newCraneRequestData.id
    });

    for (const companyId of companies) {
      await CraneRequestCompany.createInstance({ ...updateParam, CompanyId: companyId });
    }

    for (const gateId of gates) {
      await CraneGate.createInstance({ ...updateParam, GateId: gateId });
    }

    for (const equipmentId of equipments) {
      await CraneRequestEquipment.createInstance({ ...updateParam, EquipmentId: equipmentId });
    }

    for (const personId of responsiblePersons) {
      await CraneRequestResponsiblePerson.createInstance({ ...updateParam, MemberId: personId });
    }

    for (const dfwId of definableFeatureOfWorks) {
      await CraneRequestDefinableFeatureOfWork.createInstance({ ...updateParam, DeliverDefineWorkId: dfwId });
    }
  },
  async dispatchNotifications(newCraneRequestData, inputData, memberDetails, projectDetails, craneRequestDetail) {
    const loginUser = inputData.user;

    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: craneRequestDetail.ProjectId,
        id: craneRequestDetail.LocationId,
      },
    });

    const history = {
      CraneRequestId: newCraneRequestData.id,
      MemberId: memberDetails.id,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}.`,
      locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}. Location: ${locationChosen.locationPath}.`,
      memberData: [],
      ProjectId: craneRequestDetail.ProjectId,
      projectName: projectDetails.projectName,
      createdAt: new Date(),
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
    };

    const newNotification = await Notification.createInstance({
      ...history,
      LocationId: craneRequestDetail.LocationId,
      title: 'Crane Booking Creation',
      isDeliveryRequest: false,
      requestType: 'craneRequest',
      recurrenceType: `${craneRequestDetail.recurrence} From ${moment(craneRequestDetail.craneDeliveryStart).format('MM/DD/YYYY')} to ${moment(craneRequestDetail.craneDeliveryEnd).format('MM/DD/YYYY')}`,
    });

    const locationPrefs = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: craneRequestDetail.ProjectId,
        LocationId: craneRequestDetail.LocationId,
        follow: true,
      },
      include: [{
        association: 'Member',
        attributes: ['id', 'RoleId'],
        include: [{
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }],
        where: {
          id: { [Op.ne]: memberDetails.id }
        }
      }]
    });

    // Push & In-App Notification
    await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
      locationPrefs, newCraneRequestData.CraneRequestId,
      history.locationFollowDescription, newCraneRequestData.requestType,
      newCraneRequestData.ProjectId, newCraneRequestData.id, 3
    );

    await notificationHelper.createMemberDeliveryLocationInAppNotification(
      DeliveryPersonNotification, craneRequestDetail.ProjectId,
      newNotification.id, locationPrefs, 3
    );

    const personData = await this.getResponsiblePersonData(newCraneRequestData.id, memberDetails.id, craneRequestDetail);
    const adminData = await this.getResponsibleAdmins(craneRequestDetail, memberDetails.id, locationPrefs);

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData, [], projectDetails, newNotification, DeliveryPersonNotification,
      memberDetails, loginUser, 3, 'created a', 'Crane Request',
      `crane Booking (${newCraneRequestData.CraneRequestId} - ${newCraneRequestData.description})`,
      newCraneRequestData.CraneRequestId
    );

    await pushNotification.sendPushNotificationForCrane(history, 3, craneRequestDetail.ProjectId);

    history.CraneRequestId = newCraneRequestData.CraneRequestId;

    await this.sendEmailNotificationToUser(
      history, memberDetails, loginUser, newCraneRequestData,
      craneRequestDetail, locationPrefs
    );

    return history;
  },
  async processWeeklyCraneRecurrence(
    params
  ) {
    let { craneRequestDetail,
      eventTimeZone,
      loginUser,
      id,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails } = params;
    const startTime = craneRequestDetail.startPicker;
    const endTime = craneRequestDetail.endPicker;

    const start = moment(craneRequestDetail.craneDeliveryStart).startOf('week');
    const end = moment(craneRequestDetail.craneDeliveryEnd).endOf('week');
    const totalDays = Array.from(momentRange.range(start, end).by('day'));
    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      craneRequestDetail,
      loginUser,
      'craneRequest',
      eventTimeZone.timezone
    );

    const eventsArray = [];

    for (const current of totalDays) {
      const dayName = current.format('dddd');

      if (
        craneRequestDetail.days.includes(dayName) &&
        !current.isBefore(craneRequestDetail.craneDeliveryStart) &&
        !current.isAfter(craneRequestDetail.craneDeliveryEnd)
      ) {
        id += 1;
        const dateStr = current.format('MM/DD/YYYY');
        const startUTC = moment
          .tz(`${dateStr} ${startTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone)
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');
        const endUTC = moment
          .tz(`${dateStr} ${endTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone)
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');

        const craneRequestParam = {
          description: craneRequestDetail.description,
          isEscortNeeded: craneRequestDetail.isEscortNeeded,
          additionalNotes: craneRequestDetail.additionalNotes,
          CraneRequestId: id,
          craneDeliveryStart: startUTC,
          craneDeliveryEnd: endUTC,
          ProjectId: craneRequestDetail.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
          pickUpLocation: craneRequestDetail.pickUpLocation,
          dropOffLocation: craneRequestDetail.dropOffLocation,
          recurrenceId,
          LocationId: craneRequestDetail.LocationId,
          status: this.determineCraneStatus(
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectDetails
          ),
          approvedBy: memberDetails.id,
          approved_at: new Date(),
        };

        eventsArray.push(craneRequestParam);
      }
    }


    return { eventsArray };
  },

  async processYearlyCraneRecurrence(
    params
  ) {
    let { craneRequestDetail,
      eventTimeZone,
      loginUser,
      id,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails } = params;
    const startTime = craneRequestDetail.startPicker;
    const endTime = craneRequestDetail.endPicker;

    const startDate = moment(craneRequestDetail.craneDeliveryStart);
    const endDate = moment(craneRequestDetail.craneDeliveryEnd);
    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      craneRequestDetail,
      loginUser,
      'craneRequest',
      eventTimeZone.timezone
    );

    const eventsArray = [];
    let current = startDate.clone();

    while (current.isSameOrBefore(endDate)) {
      let recurrenceDate;

      if (craneRequestDetail.chosenDateOfMonth) {
        recurrenceDate = current.clone().date(parseInt(craneRequestDetail.dateOfMonth));
      } else {
        recurrenceDate = this.getNthWeekdayOfMonth(current, craneRequestDetail.monthlyRepeatType);
      }

      if (recurrenceDate?.isBetween(startDate, endDate, null, '[]')) {
        id += 1;
        const dateStr = recurrenceDate.format('MM/DD/YYYY');
        const startUTC = moment.tz(`${dateStr} ${startTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone).tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
        const endUTC = moment.tz(`${dateStr} ${endTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone).tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

        const craneRequestParam = {
          description: craneRequestDetail.description,
          isEscortNeeded: craneRequestDetail.isEscortNeeded,
          additionalNotes: craneRequestDetail.additionalNotes,
          CraneRequestId: id,
          craneDeliveryStart: startUTC,
          craneDeliveryEnd: endUTC,
          ProjectId: craneRequestDetail.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
          pickUpLocation: craneRequestDetail.pickUpLocation,
          dropOffLocation: craneRequestDetail.dropOffLocation,
          recurrenceId,
          LocationId: craneRequestDetail.LocationId,
          status: this.determineCraneStatus(memberDetails, roleDetails, accountRoleDetails, projectDetails),
          approvedBy: memberDetails.id,
          approved_at: new Date(),
        };

        eventsArray.push(craneRequestParam);
      }

      current.add(+craneRequestDetail.repeatEveryCount || 1, 'year');
    }

    return { eventsArray };
  },

  async processSingleCraneRequest(
    params
  ) {
    let { craneRequestDetail,
      eventTimeZone,
      loginUser,
      id,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails } = params;
    try {
      id += 1;
      const startDate = craneRequestDetail.craneDeliveryStart;
      const endDate = craneRequestDetail.craneDeliveryEnd;

      const chosenTimezoneCraneDeliveryStart = moment.tz(
        `${startDate} ${craneRequestDetail.startPicker}`,
        'YYYY-MM-DD HH:mm',
        eventTimeZone.timezone
      );

      const chosenTimezoneCraneDeliveryEnd = moment.tz(
        `${endDate} ${craneRequestDetail.endPicker}`,
        'YYYY-MM-DD HH:mm',
        eventTimeZone.timezone
      );

      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
        .clone()
        .tz('UTC')
        .format('YYYY-MM-DD HH:mm:ssZ');

      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
        .clone()
        .tz('UTC')
        .format('YYYY-MM-DD HH:mm:ssZ');

      const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
        craneRequestDetail,
        loginUser,
        'craneRequest',
        eventTimeZone.timezone
      );

      const craneRequestParam = {
        description: craneRequestDetail.description,
        isEscortNeeded: craneRequestDetail.isEscortNeeded,
        additionalNotes: craneRequestDetail.additionalNotes,
        CraneRequestId: id,
        craneDeliveryStart,
        craneDeliveryEnd,
        ProjectId: craneRequestDetail.ProjectId,
        createdBy: memberDetails.id,
        isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
        pickUpLocation: craneRequestDetail.pickUpLocation,
        dropOffLocation: craneRequestDetail.dropOffLocation,
        recurrenceId,
        LocationId: craneRequestDetail.LocationId,
        GateId: craneRequestDetail.GateId,
      };

      if (
        memberDetails.RoleId === roleDetails.id ||
        memberDetails.RoleId === accountRoleDetails.id ||
        memberDetails.isAutoApproveEnabled ||
        projectDetails.ProjectSettings.isAutoApprovalEnabled
      ) {
        craneRequestParam.status = 'Approved';
        craneRequestParam.approvedBy = memberDetails.id;
        craneRequestParam.approved_at = new Date();
      }

      return {
        eventsArray: [craneRequestParam]
      };
    } catch (err) {
      return {
        error: true,
        message: 'Failed to generate one-time crane booking',
        details: err.message || err
      };
    }
  },

  async sendEmailNotificationToUser(
    history,
    memberDetails,
    loginUser,
    newCraneRequestData,
    craneRequestDetail,
    memberLocationPreference
  ) {
    const notificationList = await NotificationPreference.findAll({
      where: {
        ProjectId: craneRequestDetail.ProjectId,
        isDeleted: false,
      },
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 8,
            isDeleted: false,
          },
        },
      ],
    });

    for (const pref of notificationList) {
      const { MemberId, instant, dailyDigest } = pref;

      const member = await Member.findOne({
        where: { id: MemberId, isDeleted: false },
        include: [{
          association: 'User',
          attributes: ['email', 'firstName', 'lastName'],
        }]
      });

      if (!member?.User?.email) continue;

      const name = member.User.firstName
        ? `${member.User.firstName} ${member.User.lastName}`
        : 'User';

      const memberRole = await Role.findOne({ where: { id: memberDetails.RoleId } });

      const mailPayload = {
        name,
        email: member.User.email,
        content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a crane booking ${newCraneRequestData.CraneRequestId} and it’s waiting for your approval.`,
      };

      if (instant) {
        await MAILER.sendMail(
          mailPayload,
          'notifyPAForApproval',
          `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName}`,
          `Crane Booking ${newCraneRequestData.CraneRequestId} awaiting your approval`,
          (info, err) => console.log(info, err)
        );
      }

      if (dailyDigest) {
        await this.createDailyDigestDataApproval({
          RoleId: member.RoleId,
          MemberId: MemberId,
          ProjectId: craneRequestDetail.ProjectId,
          ParentCompanyId: craneRequestDetail.ParentCompanyId,
          loginUser,
          dailyDigestMessage: 'created a',
          requestType: 'Crane Request',
          messages: `crane Booking (${newCraneRequestData.CraneRequestId} - ${newCraneRequestData.description})`,
          messages2: 'and waiting for your approval',
          requestId: newCraneRequestData.CraneRequestId,
        });
      }
    }
  },

  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const craneRequestData = inputData.body || {};
    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
    const equipments = craneRequestData.EquipmentId;
    const inputProjectId = craneRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: {
        id: { [Op.in]: definableFeatureOfWorks },
        ProjectId: inputProjectId,
        isDeleted: false,
      },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +craneRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      craneRequestData.responsiblePersons &&
      craneRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if ((craneRequestData.EquipmentId && equipments[0] != 0) && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in the project' });
    }
    if (
      craneRequestData.companies &&
      craneRequestData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (
      craneRequestData.definableFeatureOfWorks &&
      craneRequestData.definableFeatureOfWorks.length > 0 &&
      defineList !== definableFeatureOfWorks.length
    ) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async updateValues(condition, done) {
    try {
      await CraneRequestCompany.update({ isDeleted: true }, { where: condition });
      await CraneRequestResponsiblePerson.update({ isDeleted: true }, { where: condition });
      await CraneRequestEquipment.update({ isDeleted: true }, { where: condition });
      await CraneGate.update({ isDeleted: true }, { where: condition });
      await CraneRequestDefinableFeatureOfWork.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },

  async prepareInitialCraneData(inputData) {
    await this.getDynamicModel(inputData);
    const craneRequestData = inputData.body || {};
    const loginUser = inputData.user;

    const projectSettingDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +craneRequestData.ProjectId,
    });

    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: craneRequestData.ProjectId,
      isActive: true,
      isDeleted: false,
    });

    const range = momentRange.range(
      moment(craneRequestData.craneDeliveryStart),
      moment(craneRequestData.recurrenceEndDate).add(1, 'day')
    );
    const totalDays = Array.from(range.by('day'));

    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    const date1 = moment(craneRequestData.craneDeliveryStart).format('MM/DD/YYYY');
    const date2 = moment(craneRequestData.recurrenceEndDate).format('MM/DD/YYYY');

    const chosenTimezoneDeliveryStart = moment.tz(
      `${date1} ${craneRequestData.deliveryStartTime}`,
      'MM/DD/YYYY HH:mm',
      craneRequestData.timezone
    );
    const chosenTimezoneDeliveryEnd = moment.tz(
      `${date2} ${craneRequestData.deliveryEndTime}`,
      'MM/DD/YYYY HH:mm',
      craneRequestData.timezone
    );

    const deliveryStartUTC = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    const recurrenceEndUTC = chosenTimezoneDeliveryEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

    return {
      craneRequestData,
      loginUser,
      projectSettingDetails,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      totalDays,
      deliveryStartUTC,
      recurrenceEndUTC
    };
  },

  async handleSingleRequestUpdate(craneRequestData, projectSettingDetails, deliveryStartUTC, done) {
    const requestArray = [{
      ProjectId: craneRequestData.ProjectId,
      craneDeliveryStart: deliveryStartUTC,
      craneDeliveryEnd: craneRequestData.craneDeliveryEnd,
      id: craneRequestData.id
    }];

    const isOverlapping = await this.checkDoubleBookingAllowedOrNot(requestArray, projectSettingDetails, 'edit');

    if (isOverlapping?.error) {
      return done(null, { message: isOverlapping.message });
    }
  },

  async handleRecurringRequestUpdate(params, done
  ) {
    const {
      craneRequestData, editCraneData, recurrenceId,
      projectSettingDetails, memberDetails,
      totalDays, roleDetails, accountRoleDetails,
      loginUser } = params;
    const requestSeries = await CraneRequest.findAll({
      where: [
        Sequelize.and({
          recurrenceId,
          craneDeliveryStart: { [Op.gte]: editCraneData.craneDeliveryStart },
          isDeleted: false,
        }),
      ],
    });

    if (craneRequestData.recurrenceEdited) {
      let AllRequestSeries = [];
      let updateRequestSeries = [];
      let newRequestSeries = [];
      let deleteRequestSeries = [];

      await this.createNewRecurrenceEvents(
        craneRequestData,
        memberDetails,
        totalDays,
        roleDetails,
        accountRoleDetails,
        projectSettingDetails,
        async (checkResponse, checkError) => {
          if (checkError) return done(null, checkError);
          AllRequestSeries = checkResponse;

          AllRequestSeries.forEach((item1) => {
            const match = requestSeries.find(item2 =>
              new Date(item2.craneDeliveryStart).getTime() === new Date(item1.craneDeliveryStart).getTime()
            );
            match ? updateRequestSeries.push(Object.assign(match, item1)) : newRequestSeries.push(item1);
          });

          requestSeries.forEach((item2) => {
            const match = AllRequestSeries.find(item1 =>
              new Date(item1.craneDeliveryStart).getTime() === new Date(item2.craneDeliveryStart).getTime()
            );
            if (!match) deleteRequestSeries.push(item2);
          });

          await this.applyRequestChanges(updateRequestSeries, deleteRequestSeries, newRequestSeries, memberDetails, craneRequestData, loginUser);
        }
      );
    }
  },

  async updateSeriesMetaInformation(
    craneRequestData, editCraneData, recurrenceId,
    deliveryStartUTC, recurrenceEndUTC, timezone
  ) {
    const nextSeries = await CraneRequest.findAll({
      where: [
        Sequelize.and({
          recurrenceId,
          craneDeliveryStart: { [Op.gt]: editCraneData.craneDeliveryStart },
          isDeleted: false,
        }),
      ],
      order: [['id', 'DESC']],
    });

    const previousSeries = await CraneRequest.findAll({
      where: [
        Sequelize.and({
          recurrenceId,
          craneDeliveryStart: { [Op.lt]: editCraneData.craneDeliveryStart },
          isDeleted: false,
        }),
      ],
      order: [['id', 'DESC']],
    });

    if (nextSeries?.length && !previousSeries?.length) {
      const chosenTimezoneDeliveryStart = moment.tz(
        `${craneRequestData.nextSeriesRecurrenceStartDate} 00:00`,
        'YYYY-MM-DD HH:mm',
        timezone
      );
      const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
      await RequestRecurrenceSeries.update({ recurrenceStartDate: utcDate }, { where: { id: nextSeries[0].recurrenceId } });
    }

    if (previousSeries?.length && !nextSeries?.length) {
      await RequestRecurrenceSeries.update(
        { recurrenceEndDate: previousSeries[0].craneDeliveryStart },
        { where: { id: previousSeries[0].recurrenceId } }
      );
    }
  },

  async finalizeCraneEdit(
    editSeriesRequests, craneRequestData, loginUser,
    projectSettingDetails, newRecurrenceId, done
  ) {
    for (const seriesData of editSeriesRequests) {
      const isCraneRequestExists = await CraneRequest.findOne({ where: { id: seriesData.id } });
      if (!isCraneRequestExists) return done(null, { message: 'Crane Booking id is not available' });

      const existsCraneRequest = await CraneRequest.getSingleCraneRequestData({ id: +seriesData.id });
      await this.handleCraneEditWorkflow(
        craneRequestData, loginUser, projectSettingDetails,
        newRecurrenceId, isCraneRequestExists, existsCraneRequest, done
      );
    }
  },

  async editCraneRequest(inputData, done) {
    try {
      const {
        craneRequestData, loginUser, projectSettingDetails,
        memberDetails, roleDetails, accountRoleDetails,
        totalDays, deliveryStartUTC, recurrenceEndUTC
      } = await this.prepareInitialCraneData(inputData);

      const editCraneData = await CraneRequest.findOne({ where: { id: craneRequestData.id } });

      if (craneRequestData.seriesOption === 1) {
        await this.handleSingleRequestUpdate(craneRequestData, projectSettingDetails, deliveryStartUTC, done);
      }

      let newRecurrenceId = null;
      if (craneRequestData.seriesOption === 2) {
        await this.handleRecurringRequestUpdate({
          craneRequestData, editCraneData, recurrenceId: craneRequestData.recurrenceId,
          projectSettingDetails, memberDetails,
          totalDays, roleDetails, accountRoleDetails,
          loginUser
        }, done
        );
      }

      await this.updateSeriesMetaInformation(
        craneRequestData, editCraneData, craneRequestData.recurrenceId,
        deliveryStartUTC, recurrenceEndUTC, craneRequestData.timezone
      );

      const editSeriesRequests = await CraneRequest.findAll({
        where: Sequelize.and({
          recurrenceId: craneRequestData.recurrenceId,
          isDeleted: false
        })
      });

      await this.finalizeCraneEdit(
        editSeriesRequests, craneRequestData, loginUser,
        projectSettingDetails, newRecurrenceId, done
      );

    } catch (e) {
      done(null, e);
    }
  },

  async handleDeleteAndUpdateRequests(updateRequestSeries, deleteRequestSeries) {
    // Handle delete requests
    for (const deleteRequest of deleteRequestSeries) {
      await CraneRequest.update({ isDeleted: true }, { where: { id: deleteRequest.id } });
      await this.deleteEquipmentMapping({
        GateId: deleteRequest.GateId,
        LocationId: deleteRequest.LocationId,
        CraneId: deleteRequest.id,
      });
    }

    // Handle update requests
    for (const updateItem of updateRequestSeries) {
      await CraneRequest.update(updateItem, { where: { id: updateItem.id } });
      await this.createEquipmentMapping({
        EquipmentId: updateItem.EquipmentId,
        startTime: updateItem.craneDeliveryStart,
        endTime: updateItem.craneDeliveryEnd,
        GateId: updateItem.GateId,
        LocationId: updateItem.LocationId,
        CraneId: updateItem.id,
      });
    }
  },

  async handleNewRequests(newRequestSeries, memberDetails, craneRequestData, loginUser) {
    if (newRequestSeries.length === 0) return;

    let id = 0;
    const lastIdValue = await CraneRequest.findOne({
      where: { ProjectId: memberDetails.ProjectId },
      order: [['CraneRequestId', 'DESC']],
    });
    id = lastIdValue?.CraneRequestId || 0;

    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
    const equipments = craneRequestData.EquipmentId;
    const gates = [craneRequestData.GateId];

    for (const newRequest of newRequestSeries) {
      id += 1;
      newRequest.CraneRequestId = id;

      const newCraneRequestData = await CraneRequest.createInstance(newRequest);
      await this.createEquipmentMapping({
        EquipmentId: newRequest.EquipmentId,
        startTime: newRequest.craneDeliveryStart,
        endTime: newRequest.craneDeliveryEnd,
        GateId: newRequest.GateId,
        LocationId: newRequest.LocationId,
        CraneId: newCraneRequestData.id,
      });

      const updateParam = {
        CraneRequestId: newCraneRequestData.id,
        CraneRequestCode: newCraneRequestData.CraneRequestId,
        ProjectId: craneRequestData.ProjectId,
      };

      // Create related records
      for (const element of companies) {
        await CraneRequestCompany.createInstance({ ...updateParam, CompanyId: element });
      }
      for (const element of gates) {
        await CraneGate.createInstance({ ...updateParam, GateId: element });
      }
      for (const element of equipments) {
        await CraneRequestEquipment.createInstance({ ...updateParam, EquipmentId: element });
      }
      for (const element of responsiblePersons) {
        await CraneRequestResponsiblePerson.createInstance({ ...updateParam, MemberId: element });
      }
      for (const element of definableFeatureOfWorks) {
        await CraneRequestDefinableFeatureOfWork.createInstance({ ...updateParam, DeliverDefineWorkId: element });
      }

      await CraneRequestHistory.createInstance({
        CraneRequestId: newCraneRequestData.id,
        MemberId: memberDetails.id,
        type: 'create',
        description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestData.description}.`,
        ProjectId: newRequest.ProjectId,
        title: 'Crane Booking Creation',
      });
    }
  },

  async applyRequestChanges(updateRequestSeries, deleteRequestSeries, newRequestSeries, memberDetails, craneRequestData, loginUser) {
    await this.handleDeleteAndUpdateRequests(updateRequestSeries, deleteRequestSeries);
    await this.handleNewRequests(newRequestSeries, memberDetails, craneRequestData, loginUser);
  },


  async handleCraneEditWorkflow(
    craneRequestData, loginUser, projectSettingDetails,
    newRecurrenceId, isCraneRequestExists, existsCraneRequest, done
  ) {
    const memberData = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: craneRequestData.ProjectId,
    });

    const history = {
      CraneRequestId: isCraneRequestExists.id,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} Edited this Crane Booking.`,
    };

    const craneRequestParam = {
      description: craneRequestData.description,
      isEscortNeeded: craneRequestData.isEscortNeeded,
      additionalNotes: craneRequestData.additionalNotes,
      isAssociatedWithDeliveryRequest: craneRequestData.isAssociatedWithDeliveryRequest,
      pickUpLocation: craneRequestData.pickUpLocation,
      dropOffLocation: craneRequestData.dropOffLocation,
      recurrenceId: craneRequestData.seriesOption !== 1 ? newRecurrenceId : craneRequestData.recurrenceId,
      LocationId: craneRequestData.LocationId,
    };

    if (craneRequestData.seriesOption === 1) {
      craneRequestParam.craneDeliveryStart = craneRequestData.craneDeliveryStart;
      craneRequestParam.craneDeliveryEnd = craneRequestData.craneDeliveryEnd;
    }

    if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
      const localStart = moment.utc(isCraneRequestExists.craneDeliveryStart).tz(craneRequestData.timezone);
      const localEnd = moment.utc(isCraneRequestExists.craneDeliveryEnd).tz(craneRequestData.timezone);

      craneRequestParam.craneDeliveryStart = await concreteRequestService.convertTimezoneToUtc(
        moment(localStart).format('MM/DD/YYYY'),
        craneRequestData.timezone,
        craneRequestData.deliveryStartTime,
      );

      craneRequestParam.craneDeliveryEnd = await concreteRequestService.convertTimezoneToUtc(
        moment(localEnd).format('MM/DD/YYYY'),
        craneRequestData.timezone,
        craneRequestData.deliveryEndTime,
      );
    }

    // Approval status logic
    const autoApprove = memberData.isAutoApproveEnabled || projectSettingDetails.ProjectSettings.isAutoApprovalEnabled;
    const isAdmin = memberData.RoleId === 1 || memberData.RoleId === 2;

    if ((isAdmin && isCraneRequestExists.status === 'Approved') || autoApprove) {
      craneRequestParam.status = 'Approved';
      craneRequestParam.approvedBy = memberData.id;
      craneRequestParam.approved_at = new Date();
    }

    await CraneRequest.update(craneRequestParam, { where: { id: isCraneRequestExists.id } });

    // Update all related mappings and send notifications
    await this.updateRequestAssociationsAndNotify(
      craneRequestData,
      isCraneRequestExists,
      existsCraneRequest,
      loginUser,
      memberData,
      history,
      done,
      projectSettingDetails
    );
  },

  async updateRequestAssociationsAndNotify(
    craneRequestData,
    isCraneRequestExists,
    existsCraneRequest,
    loginUser,
    memberData,
    history,
    done,
    projectSettingDetails
  ) {
    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
    const equipments = craneRequestData.EquipmentId;
    const gates = [craneRequestData.GateId];

    const condition = Sequelize.and({
      ProjectId: craneRequestData.ProjectId,
      CraneRequestId: isCraneRequestExists.id,
    });

    const updateParam = {
      CraneRequestId: isCraneRequestExists.id,
      CraneRequestCode: isCraneRequestExists.CraneRequestId,
      ProjectId: craneRequestData.ProjectId,
      isDeleted: false,
    };

    // Fetch existing associations
    const [existCompanies, existEquipments, existGates, existResponsiblePersons, existDfows] =
      await Promise.all([
        CraneRequestCompany.findAll({ where: condition }),
        CraneRequestEquipment.findAll({ where: condition }),
        CraneGate.findAll({ where: condition }),
        CraneRequestResponsiblePerson.findAll({ where: condition }),
        CraneRequestDefinableFeatureOfWork.findAll({ where: condition }),
      ]);

    // Association update logic
    await this.updateAssociations(companies, existCompanies, CraneRequestCompany, 'CompanyId', updateParam);
    await this.updateAssociations(gates, existGates, CraneGate, 'GateId', updateParam);
    await this.updateAssociations(equipments, existEquipments, CraneRequestEquipment, 'EquipmentId', updateParam);
    await this.updateAssociations(responsiblePersons, existResponsiblePersons, CraneRequestResponsiblePerson, 'MemberId', updateParam);
    await this.updateAssociations(definableFeatureOfWorks, existDfows, CraneRequestDefinableFeatureOfWork, 'DeliverDefineWorkId', updateParam);

    // Location & project meta
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: craneRequestData.ProjectId,
        id: craneRequestData.LocationId,
      },
    });

    const projectDetails = await Project.findByPk(craneRequestData.ProjectId);

    // Update history content
    history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Booking ${craneRequestData.description}`;
    history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Booking, ${craneRequestData.description}. Location: ${locationChosen.locationPath}.`;
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = craneRequestData.ProjectId;
    history.projectName = projectDetails.projectName;

    const notification = {
      ...history,
      ProjectId: isCraneRequestExists.ProjectId,
      title: `Crane Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`,
      isDeliveryRequest: false,
      requestType: 'craneRequest',
    };

    if (existsCraneRequest?.recurrence?.recurrence) {
      notification.recurrenceType = `${existsCraneRequest.recurrence.recurrence} From ${moment(existsCraneRequest.recurrence.recurrenceStartDate).format('MM/DD/YYYY')} to ${moment(existsCraneRequest.recurrence.recurrenceEndDate).format('MM/DD/YYYY')}`;
    }

    const newNotification = await Notification.createInstance(notification);

    // Send push & in-app notifications
    await this.handleNotificationDispatch(
      craneRequestData,
      isCraneRequestExists,
      newNotification,
      memberData,
      loginUser,
      history,
      projectDetails
    );

    // Send emails to guest users (responsible persons)
    const userDataMail = await this.getGuestUsersToNotify(craneRequestData.id);
    for (const mail of userDataMail) {
      const email = mail.Member?.User?.email;
      if (email && mail.Member.isGuestUser) {
        await MAILER.sendMail(
          {
            email,
            guestName: mail.Member.User.firstName,
            content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} has updated a crane booking - ${isCraneRequestExists.description}.`,
          },
          'notifyGuestOnEdit',
          `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
          `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName}`
        );
      }
    }

    // Handle status updates (pending → approved, etc.)
    await this.updateCraneStatusPostEdit(
      existsCraneRequest,
      memberData,
      craneRequestData,
      isCraneRequestExists,
      loginUser,
      projectSettingDetails
    );

    return done(history, false);
  },

  async updateAssociations(newItems, existingItems, Model, keyField, updateParam) {
    // Handle case where newItems is undefined or null
    if (!newItems || !Array.isArray(newItems)) {
      newItems = [];
    }

    // Update existing items or create new ones
    for (const item of newItems) {
      const existingIndex = existingItems.findIndex(existing => existing[keyField] === item);
      const itemParam = { ...updateParam, [keyField]: item };

      if (existingIndex !== -1) {
        // Update existing item
        await Model.update(itemParam, { where: { id: existingItems[existingIndex].id } });
      } else {
        // Create new item
        await Model.createInstance(itemParam);
      }
    }

    // Mark items as deleted that are no longer in the new list
    const itemsToDelete = existingItems.filter(existing =>
      !newItems.includes(existing[keyField]) && !existing.isDeleted
    );

    for (const itemToDelete of itemsToDelete) {
      await Model.update({ isDeleted: true }, { where: { id: itemToDelete.id } });
    }
  },

  async getGuestUsersToNotify(requestId) {
    return await CraneRequestResponsiblePerson.findAll({
      where: { CraneRequestId: requestId, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id', 'isGuestUser'],
        },
      ],
    });
  },

  async handleNotificationDispatch(
    craneRequestData,
    isCraneRequestExists,
    newNotification,
    memberData,
    loginUser,
    history,
    projectDetails
  ) {
    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: craneRequestData.ProjectId,
        LocationId: craneRequestData.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            id: { [Op.ne]: memberData.id },
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });


    // Push + in-app notifications
    if (memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
        memberLocationPreference,
        craneRequestData.CraneRequestId,
        history.locationFollowDescription,
        'craneRequest',
        craneRequestData.ProjectId,
        craneRequestData.id,
        5 // NotificationPreferenceItemId: "edit crane request"
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        craneRequestData.ProjectId,
        newNotification.id,
        memberLocationPreference,
        5
      );
    }

    // Store members notified for audit
    history.memberData = [...memberLocationPreference];
  },


  async getSingleCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const getCraneRequest = await CraneRequest.findOne({
        where: { CraneRequestId: params.CraneRequestId, ProjectId: params.ProjectId },
      });
      const craneRequest = await CraneRequest.getSingleCraneRequestData({
        id: +getCraneRequest.id,
      });
      done(craneRequest, false);
    } catch (e) {
      done(null, e);
    }
  },

  async updateCraneStatusPostEdit(
    existsCraneRequest,
    memberData,
    craneRequestData,
    isCraneRequestExists,
    loginUser,
    projectSettingDetails
  ) {
    const updatedCraneRequest = await CraneRequest.getSingleCraneRequestData({
      id: isCraneRequestExists.id,
    });

    const autoApprove =
      memberData.isAutoApproveEnabled || projectSettingDetails.ProjectSettings.isAutoApprovalEnabled;
    const isAdmin = memberData.RoleId === 1 || memberData.RoleId === 2;

    const fieldsChanged =
      existsCraneRequest.description !== updatedCraneRequest.description ||
      existsCraneRequest.CraneRequestId !== updatedCraneRequest.CraneRequestId ||
      existsCraneRequest.LocationId !== updatedCraneRequest.LocationId ||
      existsCraneRequest.additionalNotes !== updatedCraneRequest.additionalNotes ||
      existsCraneRequest.isAssociatedWithDeliveryRequest !== updatedCraneRequest.isAssociatedWithDeliveryRequest ||
      existsCraneRequest.isEscortNeeded !== updatedCraneRequest.isEscortNeeded ||
      existsCraneRequest.dropOffLocation !== updatedCraneRequest.dropOffLocation ||
      existsCraneRequest.pickUpLocation !== updatedCraneRequest.pickUpLocation;

    const deliveryDateTimeChanged =
      moment(existsCraneRequest.craneDeliveryStart).format('HH:mm') !== moment(updatedCraneRequest.craneDeliveryStart).format('HH:mm') ||
      moment(existsCraneRequest.craneDeliveryEnd).format('HH:mm') !== moment(updatedCraneRequest.craneDeliveryEnd).format('HH:mm');

    const needsApproval = fieldsChanged || deliveryDateTimeChanged;

    let newStatus = null;
    if (updatedCraneRequest.status === 'Completed' || updatedCraneRequest.status === 'Approved') {
      if (!isAdmin && needsApproval && !autoApprove) {
        newStatus = 'Pending';
      } else if (isAdmin || autoApprove) {
        newStatus = 'Approved';
      }
    }

    if (updatedCraneRequest.status === 'Declined' || updatedCraneRequest.status === 'Expired') {
      newStatus = autoApprove || isAdmin ? 'Approved' : 'Pending';
    }

    if (updatedCraneRequest.status === 'Pending') {
      newStatus = autoApprove || isAdmin ? 'Approved' : 'Pending';
    }

    if (newStatus && newStatus !== updatedCraneRequest.status) {
      await CraneRequest.update({ status: newStatus }, { where: { id: updatedCraneRequest.id } });

      if (newStatus === 'Approved') {
        const approvalHistory = {
          ProjectId: craneRequestData.ProjectId,
          MemberId: memberData.id,
          CraneRequestId: updatedCraneRequest.id,
          isDeleted: false,
          type: 'approved',
          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking, ${craneRequestData.description}`,
        };
        await CraneRequestHistory.createInstance(approvalHistory);
      }
    }
  },


  // Helper function to validate crane status update permissions
  async validateCraneStatusUpdatePermissions(updateData, loginUser, statusValue) {
    const memberValue = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      }),
    });

    if (!memberValue) {
      return { error: true, message: 'Member not found in project.' };
    }

    if (![1, 2, 3, 4].includes(memberValue.RoleId)) {
      return { error: true, message: 'Insufficient permissions to update status.' };
    }

    // Special validation for SC role
    if (memberValue.RoleId === 4) {
      const NDRData = await CraneRequest.getSingleCraneRequestData({ id: updateData.id });
      if (+loginUser.id !== +NDRData.createdUserDetails.User.id) {
        return {
          error: true,
          message: 'SC can able to deliver the Crane Booking which was created by him only.'
        };
      }
    }

    return { error: false, memberValue };
  },

  // Helper function to get crane location and notification preferences
  async getCraneLocationAndNotificationPreferences(statusValue, memberValue) {
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: statusValue.ProjectId,
        id: statusValue.LocationId,
      },
    });

    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        LocationId: statusValue.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            [Op.and]: [
              {
                id: { [Op.ne]: memberValue.id },
              },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    return { locationChosen, memberLocationPreference };
  },

  // Helper function to prepare crane history and notification data
  async prepareCraneHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen) {
    const history = {
      CraneRequestId: statusValue.id,
      MemberId: memberValue.id,
      CraneId: statusValue.CraneRequestId,
    };

    const notification = { ...history };
    notification.ProjectId = statusValue.ProjectId;

    if (statusValue?.recurrence?.recurrence) {
      notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
        statusValue.recurrence.recurrenceStartDate,
      ).format('MM/DD/YYYY')} to ${moment(
        statusValue.recurrence.recurrenceEndDate,
      ).format('MM/DD/YYYY')}`;
    }

    notification.requestType = 'craneRequest';

    return { history, notification };
  },

  // Helper function to handle crane approved status
  async handleCraneApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
    if (updateData.statuschange && updateData.statuschange === 'Reverted') {
      history.type = 'approved';
      history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Crane Booking , ${statusValue.description}`;
      history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Crane Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
    } else {
      history.type = 'approved';
      history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking`;
      history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
    }

    notification.title = `Crane Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
    await CraneRequest.update(
      { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
      { where: { id: updateData.id } },
    );

    const object = {
      ProjectId: statusValue.ProjectId,
      MemberId: memberValue.id,
      CraneRequestId: statusValue.id,
      isDeleted: false,
      type: updateData.status.toLowerCase(),
      description: history.description,
    };
    await CraneRequestHistory.createInstance(object);

    return { history, notification };
  },

  // Helper function to handle crane declined or delivered status
  async handleCraneDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
    history.type = updateData.status.toLowerCase();
    history.description = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Crane Booking`;
    history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Crane Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;

    notification.title = `Crane Booking ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}`;
    await CraneRequest.update(
      { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
      { where: { id: updateData.id } },
    );

    const object = {
      ProjectId: statusValue.ProjectId,
      MemberId: memberValue.id,
      CraneRequestId: statusValue.id,
      isDeleted: false,
      type: updateData.status.toLowerCase(),
      description: history.description,
    };
    await CraneRequestHistory.createInstance(object);

    return { history, notification };
  },

  async updateCraneRequestStatus(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const updateData = inputData.body || {};
      const loginUser = inputData.user;

      const statusValue = await CraneRequest.findOne({
        where: { id: updateData.id },
        include: [
          {
            association: 'recurrence',
            required: false,
            attributes: [
              'id',
              'recurrence',
              'recurrenceStartDate',
              'recurrenceEndDate',
              'dateOfMonth',
              'monthlyRepeatType',
              'repeatEveryCount',
              'days',
              'requestType',
              'repeatEveryType',
              'chosenDateOfMonth',
              'createdBy',
              'chosenDateOfMonthValue',
            ],
          },
        ],
      });

      if (!statusValue) {
        return done(null, { message: 'Id does not exist.' });
      }

      // Validate permissions
      const permissionResult = await this.validateCraneStatusUpdatePermissions(updateData, loginUser, statusValue);
      if (permissionResult.error) {
        return done(null, { message: permissionResult.message });
      }
      const { memberValue } = permissionResult;

      // Get location and notification preferences
      const { locationChosen, memberLocationPreference } = await this.getCraneLocationAndNotificationPreferences(statusValue, memberValue);

      // Prepare history and notification data
      const { history, notification } = await this.prepareCraneHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen);

      // Handle different status types
      if (updateData.status === 'Approved') {
        const result = await this.handleCraneApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
        await this.processCraneApprovedStatusNotifications(result.history, result.notification, statusValue, memberLocationPreference, memberValue, loginUser, done);
      } else if (updateData.status === 'Declined' || updateData.status === 'Delivered') {
        const result = await this.handleCraneDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
        const params = { history: result.history, notification: result.notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData }
        await this.processCraneDeclinedOrDeliveredStatusNotifications(params, done);
      } else {
        return done(null, { message: 'Invalid Status' });
      }

    } catch (e) {
      return done(null, e);
    }
  },

  // Helper function to process crane approved status notifications
  async processCraneApprovedStatusNotifications(history, notification, statusValue, memberLocationPreference, memberValue, loginUser, done) {
    // Add common history properties
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = statusValue.ProjectId;

    const projectDetails = await Project.findByPk(statusValue.ProjectId);
    history.projectName = projectDetails.projectName;

    const newNotification = await Notification.createInstance(notification);

    // Get member and admin data
    const { personData, adminData } = await this.getCraneMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

    // Send notifications
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        statusValue.CraneRequestId,
        history.locationFollowDescription,
        statusValue.requestType,
        statusValue.ProjectId,
        statusValue.id,
        6,
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        statusValue.ProjectId,
        newNotification.id,
        memberLocationPreference,
        6,
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberValue,
      loginUser,
      6,
      'approved a',
      'Crane Request',
      `Crane Booking (${statusValue.CraneRequestId} - ${statusValue.description})`,
      statusValue.id,
    );

    // Get notification preferences and send push notifications
    const checkMemberNotification = await NotificationPreference.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 6,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });

    history.notificationPreference = checkMemberNotification;
    await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData.push(...memberLocationPreference);
    }

    // Send guest user notifications
    await this.sendCraneGuestUserNotifications(statusValue, loginUser, 'Approved');

    return done(history, false);
  },

  // Helper function to process crane declined or delivered status notifications
  async processCraneDeclinedOrDeliveredStatusNotifications(params, done) {
    const { history, notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData } = params;
    // Add common history properties
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = statusValue.ProjectId;

    const projectDetails = await Project.findByPk(statusValue.ProjectId);
    history.projectName = projectDetails.projectName;

    const newNotification = await Notification.createInstance(notification);

    // Get member and admin data
    const { personData, adminData } = await this.getCraneMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

    // Send notifications similar to approved status
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        statusValue.CraneRequestId,
        history.locationFollowDescription,
        statusValue.requestType,
        statusValue.ProjectId,
        statusValue.id,
        6,
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        statusValue.ProjectId,
        newNotification.id,
        memberLocationPreference,
        6,
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberValue,
      loginUser,
      6,
      `${updateData.status.toLowerCase()}`,
      'Crane Request',
      `Crane Booking (${statusValue.CraneRequestId} - ${statusValue.description})`,
      statusValue.id,
    );

    await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData.push(...memberLocationPreference);
    }

    // Send guest user notifications
    await this.sendCraneGuestUserNotifications(statusValue, loginUser, updateData.status);

    return done(history, false);
  },

  // Helper function to get crane member and admin data for notifications
  async getCraneMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification) {
    const locationFollowMembers = [];
    memberLocationPreference.forEach(async (element) => {
      locationFollowMembers.push(element.Member.id);
    });

    const NDRData = await CraneRequest.getSingleCraneRequestData({ id: statusValue.id });
    const bookingMemberDetails = [];
    NDRData.memberDetails.forEach(async (element) => {
      bookingMemberDetails.push(element.Member.id);
    });

    const personData = await CraneRequestResponsiblePerson.findAll({
      where: { CraneRequestId: statusValue.id, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
          where: {
            [Op.and]: {
              RoleId: {
                [Op.notIn]: [1, 2],
              },
              id: { [Op.notIn]: locationFollowMembers },
            },
          },
          attributes: ['id', 'RoleId'],
        },
      ],
      attributes: ['id'],
    });

    const adminData = await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: statusValue.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: bookingMemberDetails } },
          { id: { [Op.ne]: newNotification.MemberId } },
          { id: { [Op.notIn]: locationFollowMembers } },
        ],
      },
      include: [
        {
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
      attributes: ['id'],
    });

    return { personData, adminData, locationFollowMembers, bookingMemberDetails };
  },

  // Helper function to send crane guest user notifications
  async sendCraneGuestUserNotifications(statusValue, loginUser, status) {
    const exist = await CraneRequest.findOne({
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: [
                    'email',
                    'phoneCode',
                    'phoneNumber',
                    'firstName',
                    'lastName',
                  ],
                },
              ],
            },
          ],
        },
      ],
      where: { id: statusValue.id },
    });

    if (exist?.memberDetails) {
      const userDataMail = exist.memberDetails;
      for (const mail of userDataMail) {
        const responsibleGuestUser = mail.Member.isGuestUser;
        if (responsibleGuestUser) {
          const guestMailPayload = {
            email: mail.Member.User.email,
            guestName: mail.Member.User.firstName,
            content: `We would like to inform you that
        ${loginUser.firstName} ${loginUser.lastName} ${status} the Crane Booking - ${statusValue.description}.`,
          };
          await MAILER.sendMail(
            guestMailPayload,
            'notifyGuestOnEdit',
            `Crane Booking ${status} by ${loginUser.firstName} `,
            `Crane Booking ${status}`,
            async (info, err) => {
              console.log(info, err);
            },
          );
        }
      }
    }
  },
  // Helper function to search crane data with iterative approach instead of recursion
  async getSearchCraneData(params, done) {
    const { req, incomeData, deliveryList, result, limit, index, count, memberDetails } = params;
    const elementValue = deliveryList[index];

    if (!elementValue) return done(result, false);

    const element = JSON.parse(JSON.stringify(elementValue));
    const status = { companyCondition: true, memberCondition: true };

    // Check company condition separately
    if (!(await this.isCompanyConditionValid(req, incomeData, element))) {
      status.companyCondition = false;
    }

    if (status.memberCondition && status.companyCondition) {
      result.push(element);
    }

    // Continue iteration
    if (index < deliveryList.length - 1) {
      return this.getSearchCraneData(
        { req, incomeData, deliveryList, result, limit, index: index + 1, count: count + 1, memberDetails },
        (response, err) => done(err ? null : response, err || false)
      );
    }

    return done(result, false);
  },

  async isCompanyConditionValid(req, incomeData, element) {
    if (incomeData.companyFilter <= 0) return true;
    const isVoid = Number(req.params.void);

    if (isVoid === 1) {
      return element.companyDetails.some(
        (ele) => ele.Company.companyName === incomeData.companyFilter
      );
    }

    if (isVoid === 0) {
      // Skip company filter check if companyFilter is not provided or invalid
      if (!incomeData.companyFilter || isNaN(+incomeData.companyFilter)) {
        return true;
      }

      const data = await CraneRequestCompany.findOne({
        where: {
          CraneRequestId: element.id,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      return Boolean(data);
    }

    return true;
  },


  // Helper function to search delivery data with iterative approach instead of recursion
  async getSearchDeliveryData(params, done) {
    const { req, incomeData, deliveryList, result, limit, index, count, memberDetails } = params;
    const elementValue = deliveryList[index];

    if (!elementValue) return done(result, false);

    const element = JSON.parse(JSON.stringify(elementValue));
    const status = { companyCondition: true, memberCondition: true };

    // Check company condition separately
    if (!(await this.isDeliveryCompanyConditionValid(req, incomeData, element))) {
      status.companyCondition = false;
    }

    if (status.memberCondition && status.companyCondition) {
      result.push(element);
    }

    // Continue iteration
    if (index < deliveryList.length - 1) {
      return this.getSearchDeliveryData(
        { req, incomeData, deliveryList, result, limit, index: index + 1, count: count + 1, memberDetails },
        (response, err) => done(err ? null : response, err || false)
      );
    }

    return done(result, false);
  },

  async isDeliveryCompanyConditionValid(req, incomeData, element) {
    if (incomeData.companyFilter <= 0) return true;
    const isVoid = Number(req.params.void);

    if (isVoid === 1) {
      return element.companyDetails.some(
        (ele) => ele.Company.companyName === incomeData.companyFilter
      );
    }

    if (isVoid === 0) {
      const data = await DeliverCompany.findOne({
        where: {
          DeliveryId: element.id,
          CompanyId: +incomeData.companyFilter,
          isDeleted: false,
        },
      });
      return Boolean(data);
    }

    return true;
  },

  async updateEditCraneRequestHistory(userEditedCraneRequestData, existsCraneRequestData, updatedCraneRequest, history, loginUser) {
    await this.updateGeneralHistory(userEditedCraneRequestData, existsCraneRequestData, updatedCraneRequest, history, loginUser);
    await this.updateMemberAndCompanyHistory(updatedCraneRequest, existsCraneRequestData, history, loginUser);
    await this.updateWorkAndEquipmentHistory(updatedCraneRequest, existsCraneRequestData, history, loginUser);
  },

  async updateWorkAndEquipmentHistory(updatedCraneRequest, existsCraneRequestData, history, loginUser) {
    const historyObject = history;

    // Define Work Details
    if (updatedCraneRequest.defineWorkDetails.length > 0 && existsCraneRequestData.defineWorkDetails.length > 0) {
      const addedDfow = updatedCraneRequest.defineWorkDetails.filter((el) =>
        !existsCraneRequestData.defineWorkDetails.find((element) => element.id === el.id)
      );
      const deletedDfow = existsCraneRequestData.defineWorkDetails.filter((el) =>
        !updatedCraneRequest.defineWorkDetails.find((element) => element.id === el.id)
      );

      for (const element of addedDfow) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
        await CraneRequestHistory.createInstance(historyObject);
      }

      for (const element of deletedDfow) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }

    // Equipment Details
    if (updatedCraneRequest.equipmentDetails.length > 0 && existsCraneRequestData.equipmentDetails.length > 0) {
      const addedEquipment = updatedCraneRequest.equipmentDetails.filter((el) =>
        !existsCraneRequestData.equipmentDetails.find((element) => element.Equipment.id === el.Equipment.id)
      );
      const deletedEquipment = existsCraneRequestData.equipmentDetails.filter((el) =>
        !updatedCraneRequest.equipmentDetails.find((element) => element.Equipment.id === el.Equipment.id)
      );

      for (const element of addedEquipment) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Equipment ${element.Equipment.equipmentName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }

      for (const element of deletedEquipment) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Equipment ${element.Equipment.equipmentName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }
  },

  async updateMemberAndCompanyHistory(updatedCraneRequest, existsCraneRequestData, history, loginUser) {
    const historyObject = history;

    // Members
    if (updatedCraneRequest.memberDetails.length > 0 && existsCraneRequestData.memberDetails.length > 0) {
      const addedMember = updatedCraneRequest.memberDetails.filter((el) =>
        !existsCraneRequestData.memberDetails.find((element) => element.id === el.id)
      );
      const deletedMember = existsCraneRequestData.memberDetails.filter((el) =>
        !updatedCraneRequest.memberDetails.find((element) => element.id === el.id)
      );

      for (const element of addedMember) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }

      for (const element of deletedMember) {
        historyObject.description = element.Member.User.firstName
          ? `${loginUser.firstName} ${loginUser.lastName} deleted the member ${element.Member.User.firstName} ${element.Member.User.lastName}`
          : `${loginUser.firstName} ${loginUser.lastName} deleted the member`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }

    // Companies
    if (updatedCraneRequest.companyDetails.length > 0 && existsCraneRequestData.companyDetails.length > 0) {
      const addedCompany = updatedCraneRequest.companyDetails.filter((el) =>
        !existsCraneRequestData.companyDetails.find((element) => element.id === el.id)
      );
      const deletedCompany = existsCraneRequestData.companyDetails.filter((el) =>
        !updatedCraneRequest.companyDetails.find((element) => element.id === el.id)
      );

      for (const element of addedCompany) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the company ${element.Company.companyName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }

      for (const element of deletedCompany) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the company ${element.Company.companyName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }
  },

  async updateGeneralHistory(userEditedCraneRequestData, existsCraneRequestData, updatedCraneRequest, history, loginUser) {
    const historyObject = history;

    if (
      userEditedCraneRequestData.description?.toLowerCase() !==
      existsCraneRequestData.description?.toLowerCase()
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Description ${userEditedCraneRequestData.description}`;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (
      userEditedCraneRequestData.LocationId !== existsCraneRequestData.LocationId &&
      updatedCraneRequest?.location?.locationPath
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedCraneRequest.location.locationPath} `;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (
      new Date(userEditedCraneRequestData.craneDeliveryStart).getTime() !==
      new Date(existsCraneRequestData.craneDeliveryStart).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Pick From Time ${userEditedCraneRequestData.craneDeliveryStart}`;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (
      new Date(userEditedCraneRequestData.craneDeliveryEnd).getTime() !==
      new Date(existsCraneRequestData.craneDeliveryEnd).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Pick To Time ${userEditedCraneRequestData.craneDeliveryEnd}`;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (userEditedCraneRequestData.additionalNotes) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${userEditedCraneRequestData.additionalNotes}`;
      await CraneRequestHistory.createInstance(historyObject);
    } else if (existsCraneRequestData.additionalNotes) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existsCraneRequestData.additionalNotes}`;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (userEditedCraneRequestData.pickUpLocation !== existsCraneRequestData.pickUpLocation) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking From ${userEditedCraneRequestData.pickUpLocation}`;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (userEditedCraneRequestData.dropOffLocation !== existsCraneRequestData.dropOffLocation) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking To ${userEditedCraneRequestData.dropOffLocation}`;
      await CraneRequestHistory.createInstance(historyObject);
    }

    if (userEditedCraneRequestData.escort !== existsCraneRequestData.escort) {
      historyObject.description = userEditedCraneRequestData.escort
        ? `${loginUser.firstName} ${loginUser.lastName} enabled the Escort`
        : `${loginUser.firstName} ${loginUser.lastName} disabled the Escort`;
      await CraneRequestHistory.createInstance(historyObject);
    }
  },

  async upcomingCraneRequest(req) {
    try {
      req.body.ParentCompanyId = req.query.ParentCompanyId;
      await this.getDynamicModel(req);
      const data = req.query;
      const loginUser = req.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: data.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      if (memberDetails) {
        let craneRequestList = await CraneRequest.upcomingCraneRequest(data.ProjectId);
        const deliveryRequestCondition = {
          ProjectId: +data.ProjectId,
          isDeleted: false,
          deliveryStart: {
            [Op.gt]: new Date(),
          },
          isQueued: false,
          isAssociatedWithCraneRequest: true,
        };
        if (!data.limit) {
          delete deliveryRequestCondition.isAssociatedWithCraneRequest;
        }

        const deliveryRequestList = await DeliveryRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'companyDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: deliveryRequestCondition,
        });
        const concreteRequestCondition = {
          ProjectId: +data.ProjectId,
          isDeleted: false,
          concretePlacementStart: {
            [Op.gt]: new Date(),
          },
        };
        const concreteRequestList = await ConcreteRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'concreteSupplierDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: concreteRequestCondition,
        });
        if (deliveryRequestList.length > 0) {
          craneRequestList.push(...deliveryRequestList);
        }
        if (craneRequestList.length > 0) {
          // Extract the date values for sorting
          craneRequestList.sort((a, b) => {
            const aDate = a.craneDeliveryStart ? a.craneDeliveryStart : a.deliveryStart;
            const bDate = b.craneDeliveryStart ? b.craneDeliveryStart : b.deliveryStart;
            return aDate > bDate ? 1 : -1;
          });
        }
        if (concreteRequestList.length > 0) {
          craneRequestList.push(...concreteRequestList);
        }
        if (data.limit) {
          craneRequestList = craneRequestList.slice(0, +data.limit);
        }
        return { status: 200, data: craneRequestList };
      }
      return { status: 422, msg: 'Project Id/ParendCompany Id/Member does not exist' };
    } catch (e) {
      return e;
    }
  },
  async upcomingRequestList(req) {
    try {
      let upcomingList = [];
      const craneRequestData = await this.upcomingCraneRequest(req);
      if (craneRequestData?.data?.length > 0) {
        upcomingList.push(...craneRequestData.data);
      }
      if (upcomingList.length > 0) {
        upcomingList.sort((a, b) => {
          const aDate = a.craneDeliveryStart ? a.craneDeliveryStart : a.deliveryStart;
          const bDate = b.craneDeliveryStart ? b.craneDeliveryStart : b.deliveryStart;
          return aDate > bDate ? 1 : -1;
        });
      }
      upcomingList = upcomingList.slice(0, 10);
      return { status: 200, data: upcomingList };
    } catch (e) {
      return e;
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
        if (index === -1) {
          existAdminData.push({ email: element.User.email });
          emailArray.push({
            email: element.User.email,
            firstName: element.User.firstName,
            lastName: element.User.lastName,
            UserId: element.User.id,
            MemberId: element.id,
            RoleId: element.RoleId,
          });
        }
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    return emailArray;
  },
  // prettier-ignore
  async createDailyDigestData(
    params
  ) {
    const {
      MemberId,
      ProjectId,
      ParentCompanyId,
      loginUser,
      dailyDigestMessage,
      requestType,
      messages,
      requestId, } = params;
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" target="" style="text-decoration: none;color:#4470FF;">
						${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
				<a href="${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >${messages}</a>
					<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div>`,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async prepareVoidRequestData(inputData) {
    await this.getDynamicModel(inputData);
    const { params } = inputData;
    const loginUser = inputData.user;
    const incomeData = inputData.body || {};
    const { sort, sortByField } = incomeData;

    // Validate void parameter
    if (!(params.void === '0' || params.void === 0 || params.void === '1' || params.void === 1)) {
      return { error: 'Please enter void as 1 or 0' };
    }

    const memberDetails = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
      }),
    });

    if (!memberDetails) {
      return { error: 'Project Id/Member does not exist' };
    }

    // Prepare void lists
    const [voidDeliveryList, voidCraneRequestList, voidInspectionRequestList] = await Promise.all([
      VoidList.findAll({
        where: {
          ProjectId: +params.ProjectId,
          isDeliveryRequest: true,
          DeliveryRequestId: { [Op.ne]: null },
        },
      }),
      VoidList.findAll({
        where: {
          ProjectId: +params.ProjectId,
          isDeliveryRequest: false,
          CraneRequestId: { [Op.ne]: null },
        },
      }),
      VoidList.findAll({
        where: {
          ProjectId: +params.ProjectId,
          InspectionRequestId: { [Op.ne]: null },
        },
      }),
    ]);

    const voidDelivery = voidDeliveryList.map(el => el.DeliveryRequestId);
    const voidCraneDelivery = voidCraneRequestList.map(el => el.CraneRequestId);
    const voidInspection = voidInspectionRequestList.map(el => el.InspectionRequestId);

    // Conditions
    const condition = { ProjectId: +params.ProjectId, isDeleted: false };
    const craneCondition = { ProjectId: +params.ProjectId, isDeleted: false };

    if (params.void === '0' || params.void === 0) {
      condition['$DeliveryRequest.id$'] = { [Op.and]: [{ [Op.notIn]: voidDelivery }] };
      craneCondition['$CraneRequest.id$'] = { [Op.and]: [{ [Op.notIn]: voidCraneDelivery }] };
    } else {
      condition['$DeliveryRequest.id$'] = { [Op.and]: [{ [Op.in]: voidDelivery }] };
      craneCondition['$CraneRequest.id$'] = { [Op.and]: [{ [Op.in]: voidCraneDelivery }] };
    }

    return {
      params,
      loginUser,
      incomeData,
      sort,
      sortByField,
      memberDetails,
      condition,
      craneCondition,
      voidDelivery,
      voidCraneDelivery,
      voidInspection,
    };
  },

  async getVoidRequest(inputData, done) {
    try {
      const preparedData = await this.prepareVoidRequestData(inputData);
      if (preparedData.error) {
        return done(null, { message: preparedData.error });
      }

      const {
        params,
        incomeData,
        sort,
        sortByField,
        memberDetails,
        condition,
        craneCondition,
      } = preparedData;

      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      let order;
      let craneRequestList;
      let deliveryRequest;

      // Fetch crane requests
      if (
        (incomeData.gateFilter && incomeData.gateFilter > 0) ||
        (incomeData.statusFilter && incomeData.statusFilter === 'Delivered')
      ) {
        craneRequestList = [];
      } else {
        craneRequestList = await CraneRequest.getAll({
          req: inputData,
          roleId,
          attr: craneCondition,
          filters: {
            description: incomeData.descriptionFilter,
            company: incomeData.companyFilter,
            member: incomeData.memberFilter,
            equipment: incomeData.equipmentFilter,
            status: incomeData.statusFilter,
            id: incomeData.idFilter,
            pickFrom: incomeData.pickFrom,
            pickTo: incomeData.pickTo,
          },
          dateRange: {
            startdate: incomeData.startdate,
            enddate: incomeData.enddate,
          },
          search: incomeData.search,
          order,
          sort,
          sortColumn: sortByField,
        });
      }

      // Fetch delivery requests
      if (incomeData.statusFilter && incomeData.statusFilter === 'Completed') {
        deliveryRequest = [];
      } else {
        deliveryRequest = await DeliveryRequest.getCraneAssociatedRequest({
          roleId,
          attr: condition,
          voidType: params.void,
          order,
          sort,
          sortByField,
        });
      }

      // Post-processing search and limit
      this.getSearchCraneData(
        {
          req: inputData,
          incomeData,
          deliveryList: craneRequestList,
          result: [],
          limit: +params.pageSize,
          index: 0,
          count: 0,
          memberDetails,
        },
        async (checkResponse, checkError) => {
          if (!checkError) {
            craneRequestList = checkResponse;
            this.getSearchDeliveryData(
              {
                req: inputData,
                incomeData,
                deliveryList: deliveryRequest,
                result: [],
                limit: +params.pageSize,
                index: 0,
                count: 0,
                memberDetails,
              },
              async (checkResponse1, checkError1) => {
                if (!checkError1) {
                  deliveryRequest = checkResponse1;
                  craneRequestList.push(...deliveryRequest);
                  this.getLimitData(
                    craneRequestList,
                    0,
                    +params.pageSize,
                    [],
                    incomeData,
                    inputData.headers.timezoneoffset,
                    async (newResponse, newError) => {
                      if (!newError) {
                        done(newResponse, false);
                      } else {
                        done(null, { message: 'Something went wrong' });
                      }
                    },
                  );
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          } else {
            done(null, { message: 'Something went wrong' });
          }
        },
      );
    } catch (e) {
      done(null, e);
    }
  },


  async createDailyDigestDataApproval(
    params
  ) {
    const {
      MemberId,
      ProjectId,
      ParentCompanyId,
      loginUser,
      dailyDigestMessage,
      requestType,
      messages,
      messages2,
      requestId, } = params;
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" target="" style="text-decoration: none;color:#4470FF;">
						${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
				<a href="${process.env.BASE_URL
        }/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >
      ${messages}
					</a>
             ${messages2}
					<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div>`,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
    addedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === addedCompany.length - 1) {
          addDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === addedCompany.length - 1) {
        addDesc += `,${newCompanyData.companyName}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newCompanyData.companyName}`;
      }
    });
    deletedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === deletedCompany.length - 1) {
          deleteDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === deletedCompany.length - 1) {
        deleteDesc += `,${newCompanyData.companyName}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newCompanyData.companyName}`;
      }
    });
  },
  async updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Equipment`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Equipment`;
    addedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === addedEquipment.length - 1) {
          addDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === addedEquipment.length - 1) {
        addDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
    deletedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === deletedEquipment.length - 1) {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === deletedEquipment.length - 1) {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
  },

  async updateGateHistory(addedGate, deletedGate, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Gate`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Gate`;
    addedGate.forEach(async (element, i) => {
      const newEquipmentData = await Gates.findOne({
        where: { id: element.GateId },
      });
      if (i === 0) {
        if (i === addedGate.length - 1) {
          addDesc += ` ${newEquipmentData.gateName}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newEquipmentData.gateName}`;
        }
      } else if (i === addedGate.length - 1) {
        addDesc += `,${newEquipmentData.gateName}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newEquipmentData.gateName}`;
      }
    });
    deletedGate.forEach(async (element, i) => {
      const newEquipmentData = await Gates.findOne({
        where: { id: element.GateId },
      });
      if (i === 0) {
        if (i === deletedGate.length - 1) {
          deleteDesc += ` ${newEquipmentData.gateName}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newEquipmentData.gateName}`;
        }
      } else if (i === deletedGate.length - 1) {
        deleteDesc += `,${newEquipmentData.gateName}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newEquipmentData.gateName}`;
      }
    });
  },
  async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
    addedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === addedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === addedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
    deletedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === deletedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === deletedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
  },
  async updateDefineHistory(addedDefine, deletedDefine, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the DFOW`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the DFOW`;
    addedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === addedDefine.length - 1) {
          addDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === addedDefine.length - 1) {
        addDesc += `,${newDefineData.DFOW}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newDefineData.DFOW}`;
      }
    });
    deletedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === deletedDefine.length - 1) {
          deleteDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === deletedDefine.length - 1) {
        deleteDesc += `,${newDefineData.DFOW}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newDefineData.DFOW}`;
      }
    });
  },

  parseEditPayload(req) {
    const payload = req.body;
    const loginUser = req.user;
    const fieldsArray = payload.editedFields?.split(',') || [];
    const responsiblePersonsEdited = fieldsArray.includes('Responsible Person');
    const escortEdited = fieldsArray.includes('Escort');

    return { payload, loginUser, responsiblePersonsEdited, escortEdited };
  },

  async handleRecurrenceUpdates(getCraneRequestDetail, getExistsSingleDeliveryRequest, payload, loginUser) {
    const recurrenceId = getCraneRequestDetail.recurrenceId;
    if (!recurrenceId) return null;

    const recurrenceSeriesEndDate = getExistsSingleDeliveryRequest.recurrence.recurrenceEndDate;

    const previousSeriesEndDate = moment(getCraneRequestDetail.craneDeliveryStart).add(-1, 'days').format('YYYY-MM-DD');
    const nextSeriesStartDate = moment(getCraneRequestDetail.craneDeliveryStart).add(1, 'days').format('YYYY-MM-DD');

    const utcPrevious = moment.tz(`${previousSeriesEndDate} 00:00`, 'YYYY-MM-DD HH:mm', payload.timezone).tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    const utcNext = moment.tz(`${nextSeriesStartDate} 00:00`, 'YYYY-MM-DD HH:mm', payload.timezone).tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

    const previousRecords = await CraneRequest.findAll({
      where: [Sequelize.and({ recurrenceId, id: { [Op.lt]: getCraneRequestDetail.id }, isDeleted: false })],
      order: [['id', 'DESC']],
    });

    const nextRecords = await CraneRequest.findAll({
      where: [Sequelize.and({ recurrenceId, id: { [Op.gt]: getCraneRequestDetail.id }, isDeleted: false })],
      order: [['id', 'DESC']],
    });

    if (previousRecords.length === 1) {
      await CraneRequest.update({ recurrenceId: null }, { where: { id: previousRecords[0].id } });
    }

    if (nextRecords.length === 1) {
      await CraneRequest.update({ recurrenceId: null }, { where: { id: nextRecords[0].id } });
    }

    if (previousRecords.length > 1) {
      await RequestRecurrenceSeries.update({ recurrenceEndDate: utcPrevious }, {
        where: { id: previousRecords[0].recurrenceId },
      });
    }

    if (nextRecords.length > 1 && (previousRecords.length <= 1)) {
      await RequestRecurrenceSeries.update({ recurrenceStartDate: utcNext }, {
        where: { id: nextRecords[0].recurrenceId },
      });
    } else if (nextRecords.length > 1 && previousRecords.length > 1) {
      const recurrenceObject = {
        ProjectId: +payload.ProjectId,
        ParentCompanyId: +payload.ParentCompanyId,
        recurrence: getExistsSingleDeliveryRequest.recurrence.recurrence,
        repeatEveryCount: getExistsSingleDeliveryRequest.recurrence.repeatEveryCount,
        repeatEveryType: getExistsSingleDeliveryRequest.recurrence.repeatEveryType,
        days: getExistsSingleDeliveryRequest.recurrence.days,
        dateOfMonth: getExistsSingleDeliveryRequest.recurrence.dateOfMonth,
        chosenDateOfMonth: getExistsSingleDeliveryRequest.recurrence.chosenDateOfMonth,
        monthlyRepeatType: getExistsSingleDeliveryRequest.recurrence.monthlyRepeatType,
        requestType: getExistsSingleDeliveryRequest.requestType,
        createdBy: loginUser.id,
        recurrenceStartDate: utcNext,
        recurrenceEndDate: recurrenceSeriesEndDate,
      };
      const recurrenceSeries = await RequestRecurrenceSeries.createInstance(recurrenceObject);
      const newRecurrenceId = recurrenceSeries.id;

      for (const record of nextRecords) {
        await CraneRequest.update({ recurrenceId: newRecurrenceId }, {
          where: { id: record.id },
        });
      }

      return newRecurrenceId;
    }

    return null;
  },

  async editMultipleCraneRequest(req) {
    try {
      const { payload, loginUser, responsiblePersonsEdited, escortEdited } = this.parseEditPayload(req);
      if (!payload.craneRequestIds || payload.craneRequestIds.length === 0) {
        return { success: false, message: 'Please select Delivery booking to update.!' };
      }

      await this.getDynamicModel(req);
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +payload.ProjectId,
      });

      const responseData = [];
      for (const booking of payload.selectedBookingTypes) {
        if (booking.requestType === 'craneRequest') {
          const craneRequestId = +booking.id;
          const getCraneRequestDetail = await CraneRequest.findOne({ where: { id: craneRequestId } });
          const getExistsSingleDeliveryRequest = await CraneRequest.getSingleCraneRequestData({ id: craneRequestId });

          if (!getCraneRequestDetail) {
            return { success: false, message: 'Crane Booking id is not available' };
          }

          const memberData = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: payload.ProjectId,
          });

          await this.handleRecurrenceUpdates(getCraneRequestDetail, getExistsSingleDeliveryRequest, payload, loginUser);

          const { existsCraneRequest, updatedCraneRequest, history } = await this.updateCraneRequestEntities(
            craneRequestId,
            payload,
            loginUser,
            memberData,
            projectSettingDetails,
            responsiblePersonsEdited,
            escortEdited
          );

          await this.handleStatusUpdateTransition(
            existsCraneRequest,
            updatedCraneRequest,
            memberData,
            payload,
            projectSettingDetails,
            loginUser
          );

          responseData.push(history);
        } else if (booking.requestType === 'deliveryRequestWithCrane') {
          const deliveryService = require('./deliveryService');
          const deliveryPayload = {
            body: {
              ...payload,
              deliveryRequestIds: [booking.id],
              GateId: null,
              isAssociatedWithCraneRequest: true,
            },
            user: req.user,
          };
          const deliveryResponse = await deliveryService.editMultipleDeliveryRequest(deliveryPayload);
          responseData.push(deliveryResponse.data);
        }
      }

      return { success: true, data: responseData };
    } catch (error) {
      return error;
    }
  },

  async updateCraneRequestEntities(
    craneRequestId,
    payload,
    loginUser,
    memberData,
    projectSettings,
    responsiblePersonsEdited,
    escortEdited
  ) {
    const condition = Sequelize.and({
      ProjectId: payload.ProjectId,
      CraneRequestId: craneRequestId,
    });

    const updateParam = {
      CraneRequestId: craneRequestId,
      CraneRequestCode: craneRequestId,
      ProjectId: payload.ProjectId,
      isDeleted: false,
    };

    const history = {
      CraneRequestId: craneRequestId,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} edited the Crane Booking.`,
    };

    const existsCraneRequest = await CraneRequest.getSingleCraneRequestData({ id: craneRequestId });

    // Part 1: Update Companies, Persons, DFOW, Equipment
    await this.handleEntityUpdates(
      craneRequestId,
      payload,
      loginUser,
      history,
      condition,
      updateParam,
      responsiblePersonsEdited
    );

    // Part 2: Update Escort, Void, Delivery Times
    await this.handleSpecialUpdates(craneRequestId, payload, loginUser, memberData, escortEdited);

    // Part 3: Fetch updated data & return
    return await this.finalizeUpdate(craneRequestId, existsCraneRequest, history);
  },

  async handleEntityUpdates(
    craneRequestId,
    payload,
    loginUser,
    history,
    condition,
    updateParam,
    responsiblePersonsEdited
  ) {
    // === Company Updates ===
    if (payload.companies?.length > 0) {
      await this.updateCompanies(payload, loginUser, history, condition, updateParam);
    }

    // === Responsible Persons Updates ===
    if (responsiblePersonsEdited && payload.persons?.length > 0) {
      await this.updateResponsiblePersons(payload, loginUser, history, condition, updateParam);
    }

    // === DFOW Updates ===
    if (payload.define?.length > 0) {
      await this.updateDFOW(payload, loginUser, history, condition, updateParam);
    }

    // === Equipment Updates ===
    if (payload.EquipmentId?.length > 0) {
      await this.updateEquipment(payload, loginUser, history, condition, updateParam);
    }
  },

  async handleSpecialUpdates(craneRequestId, payload, loginUser, memberData, escortEdited) {
    // === Escort Update ===
    if (escortEdited && payload.escort) {
      await CraneRequest.update({ escort: payload.escort }, { where: { id: craneRequestId } });
    }

    // === Void Update ===
    if (payload.void === true) {
      const existingVoid = await VoidList.findOne({ where: Sequelize.and({ CraneRequestId: craneRequestId }) });
      if (!existingVoid) {
        const voidData = {
          CraneRequestId: craneRequestId,
          ProjectId: payload.ProjectId,
          ParentCompanyId: payload.ParentCompanyId,
        };
        const newVoid = await VoidList.createInstance(voidData);
        if (newVoid) {
          await CraneRequestHistory.createInstance({
            ProjectId: payload.ProjectId,
            MemberId: memberData.id,
            CraneRequestId: craneRequestId,
            isDeleted: false,
            type: 'void',
            description: `${loginUser.firstName} ${loginUser.lastName} Voided the Crane Booking.`,
          });
        }
      }
    }

    // === Delivery Time Update ===
    if (payload.deliveryStart && payload.deliveryEnd) {
      const now = Date.now();
      const start = new Date(payload.deliveryStart).getTime();
      const end = new Date(payload.deliveryEnd).getTime();
      if (start > now && end > now) {
        await CraneRequest.update(
          {
            craneDeliveryStart: payload.deliveryStart,
            craneDeliveryEnd: payload.deliveryEnd,
          },
          { where: { id: craneRequestId } }
        );
      }
    }
  },

  async finalizeUpdate(craneRequestId, existsCraneRequest, history) {
    const updatedCraneRequest = await CraneRequest.getSingleCraneRequestData({ id: craneRequestId });
    return { existsCraneRequest, updatedCraneRequest, history };
  },

  detectRelevantChanges(existsCraneRequest, updatedCraneRequest) {
    let tagsUpdated = false;
    let fieldsChanged = false;

    const compareLists = (updatedList, originalList, key = 'id') =>
      updatedList.filter(item => !originalList.some(orig => orig[key] === item[key]));

    const compareTagList = (updated, original, extract) =>
      compareLists(updated.map(extract), original.map(extract)).length > 0;

    if (compareTagList(updatedCraneRequest.defineWorkDetails, existsCraneRequest.defineWorkDetails, d => ({ id: d.id }))) {
      tagsUpdated = true;
    }

    if (compareTagList(updatedCraneRequest.equipmentDetails, existsCraneRequest.equipmentDetails, d => ({ id: d.Equipment?.id }))) {
      tagsUpdated = true;
    }

    if (compareTagList(updatedCraneRequest.companyDetails, existsCraneRequest.companyDetails, d => ({ id: d.Company?.id }))) {
      tagsUpdated = true;
    }

    if (compareTagList(updatedCraneRequest.memberDetails, existsCraneRequest.memberDetails, d => ({ id: d.Member?.id }))) {
      tagsUpdated = true;
    }

    const scalarFields = [
      'description',
      'CraneRequestId',
      'LocationId',
      'requestType',
      'additionalNotes',
      'isAssociatedWithDeliveryRequest',
      'isEscortNeeded',
      'dropOffLocation',
      'pickUpLocation'
    ];

    if (scalarFields.some(field => existsCraneRequest[field] !== updatedCraneRequest[field]) || tagsUpdated) {
      fieldsChanged = true;
    }

    const deliveryDateTimeChanged =
      +new Date(existsCraneRequest.craneDeliveryStart) !== +new Date(updatedCraneRequest.craneDeliveryStart) ||
      +new Date(existsCraneRequest.craneDeliveryEnd) !== +new Date(updatedCraneRequest.craneDeliveryEnd);

    return { fieldsChanged, deliveryDateTimeChanged, tagsUpdated };
  },

  async handleStatusUpdateTransition(
    existsCraneRequest,
    updatedCraneRequest,
    memberData,
    payload,
    projectSettings,
    loginUser
  ) {
    const { fieldsChanged, deliveryDateTimeChanged } = this.detectRelevantChanges(existsCraneRequest, updatedCraneRequest);

    const needsReapproval = fieldsChanged || deliveryDateTimeChanged;

    const canAutoApprove =
      memberData.RoleId === 2 ||
      memberData.isAutoApproveEnabled ||
      projectSettings?.ProjectSettings?.isAutoApprovalEnabled;

    // If status is explicitly provided in the payload
    if (payload.status && canAutoApprove) {
      await CraneRequest.update(
        {
          status: payload.status,
          approvedBy: memberData.id,
          approved_at: new Date(),
        },
        { where: { id: updatedCraneRequest.id } }
      );
      return;
    }

    // If no explicit status in payload, infer based on role and change
    const status = existsCraneRequest.status;

    if (['Delivered', 'Completed', 'Approved', 'Expired', 'Declined', 'Pending'].includes(status)) {
      if (needsReapproval) {
        if (canAutoApprove) {
          await CraneRequest.update(
            {
              status: 'Approved',
              approvedBy: memberData.id,
              approved_at: new Date(),
            },
            { where: { id: updatedCraneRequest.id } }
          );

          // Record history entry for re-approval
          await CraneRequestHistory.createInstance({
            ProjectId: payload.ProjectId,
            MemberId: memberData.id,
            CraneRequestId: updatedCraneRequest.id,
            isDeleted: false,
            type: 'approved',
            description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking, ${updatedCraneRequest.description}`,
          });
        } else {
          await CraneRequest.update(
            { status: 'Pending' },
            { where: { id: updatedCraneRequest.id } }
          );
        }
      }
    }
  },

  async createCopyofCraneRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
    const memberDetails = await this.getMemberDetails(payload, loginUser);
    const projectDetails = await this.getProjectDetails(payload);

    await this.validateAgainstDeliveryWindow(payload, dates, projectDetails);

    const baseCraneRequestId = await this.getNextCraneRequestId(memberDetails.ProjectId);

    const eventsArray = await this.generateRecurringCraneRequests(
      dataInSeries,
      payload,
      dates,
      newRecurrenceId,
      memberDetails,
      projectDetails,
      baseCraneRequestId
    );

    await this.persistCraneRequests(eventsArray, payload, loginUser, memberDetails);
  },

  async getMemberDetails(payload, loginUser) {
    return await Member.getBy({
      UserId: loginUser.id,
      ProjectId: payload.ProjectId,
      isActive: true,
      isDeleted: false,
    });
  },

  async getProjectDetails(payload) {
    return await Project.getProjectAndSettings({
      isDeleted: false,
      id: +payload.ProjectId,
    });
  },

  async validateAgainstDeliveryWindow(payload, dates, projectDetails) {
    if (payload.recurrence && dates?.length > 0) {
      const startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        dates[0],
        payload.deliveryStartTime,
        payload.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit
      );

      const endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        dates[0],
        payload.deliveryEndTime,
        payload.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit
      );

      if (startDate || endDate) {
        throw new Error(
          `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`
        );
      }
    }
  },

  async generateRecurringCraneRequests(
    dataInSeries,
    payload,
    dates,
    recurrenceId,
    memberDetails,
    projectDetails,
    startingId
  ) {
    const recurrenceType = dataInSeries.recurrence.recurrence;
    switch (recurrenceType) {
      case 'Daily':
        return await this.generateDailyRequests(dataInSeries, payload, dates, recurrenceId, memberDetails, projectDetails, startingId);
      case 'Weekly':
        return await this.generateWeeklyRequests(dataInSeries, payload, dates, recurrenceId, memberDetails, projectDetails, startingId);
      case 'Monthly':
        return await this.generateMonthlyRequests(dataInSeries, payload, dates, recurrenceId, memberDetails, projectDetails, startingId);
      case 'Yearly':
        return await this.generateYearlyRequests(dataInSeries, payload, dates, recurrenceId, memberDetails, projectDetails, startingId);
      default:
        return [];
    }
  },

  async persistCraneRequests(eventsArray, payload, loginUser, memberDetails) {
    const {
      companies,
      responsiblePersons,
      definableFeatureOfWorks,
      EquipmentId: equipments,
      GateId,
    } = payload;

    for (const event of eventsArray) {
      const newCraneRequest = await CraneRequest.createInstance(event);
      const updateParam = {
        CraneRequestId: newCraneRequest.id,
        CraneRequestCode: newCraneRequest.CraneRequestId,
        ProjectId: payload.ProjectId,
      };

      await Promise.all([
        ...companies.map((c) =>
          CraneRequestCompany.createInstance({ ...updateParam, CompanyId: c })
        ),
        ...[GateId].map((g) => CraneGate.createInstance({ ...updateParam, GateId: g })),
        ...equipments.map((e) =>
          CraneRequestEquipment.createInstance({ ...updateParam, EquipmentId: e })
        ),
        ...responsiblePersons.map((m) =>
          CraneRequestResponsiblePerson.createInstance({ ...updateParam, MemberId: m })
        ),
        ...definableFeatureOfWorks.map((d) =>
          CraneRequestDefinableFeatureOfWork.createInstance({
            ...updateParam,
            DeliverDefineWorkId: d,
          })
        ),
        CraneRequestHistory.createInstance({
          CraneRequestId: newCraneRequest.id,
          MemberId: memberDetails.id,
          ProjectId: payload.ProjectId,
          isDeleted: false,
          type: 'create',
          description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${payload.description}.`,
        }),
        ...(newCraneRequest.status === 'Approved'
          ? [
            CraneRequestHistory.createInstance({
              CraneRequestId: newCraneRequest.id,
              MemberId: memberDetails.id,
              ProjectId: payload.ProjectId,
              isDeleted: false,
              type: 'approved',
              description: `${loginUser.firstName} ${loginUser.lastName} Approved Crane Booking, ${payload.description}.`,
            }),
          ]
          : []),
      ]);
    }
  },

  async generateDailyRequests(
    dataInSeries,
    payload,
    dates,
    recurrenceId,
    memberDetails,
    projectDetails,
    startingId
  ) {
    const events = [];
    let id = startingId - 1;
    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    for (let i = 0; i < dates.length; i += +dataInSeries.recurrence.repeatEveryCount) {
      const date = dates[i];
      if (!date) continue;

      id++;
      const formattedDate = moment(date).format('MM/DD/YYYY');

      const event = {
        description: payload.description,
        isEscortNeeded: payload.isEscortNeeded,
        additionalNotes: payload.additionalNotes,
        CraneRequestId: id,
        craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
          formattedDate,
          payload.timezone,
          payload.deliveryStartTime
        ),
        craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
          formattedDate,
          payload.timezone,
          payload.deliveryEndTime
        ),
        ProjectId: payload.ProjectId,
        createdBy: memberDetails.id,
        isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
        pickUpLocation: payload.pickUpLocation,
        dropOffLocation: payload.dropOffLocation,
        recurrenceId,
      };

      if (
        memberDetails.RoleId === roleDetails.id ||
        memberDetails.RoleId === accountRoleDetails.id ||
        memberDetails.isAutoApproveEnabled ||
        projectDetails.ProjectSettings.isAutoApprovalEnabled
      ) {
        event.status = 'Approved';
        event.approvedBy = memberDetails.id;
        event.approved_at = new Date();
      }

      events.push(event);
    }

    return events;
  },
  async generateWeeklyRequests(
    dataInSeries,
    payload,
    dates,
    recurrenceId,
    memberDetails,
    projectDetails,
    startingId
  ) {
    const events = [];
    let id = startingId - 1;
    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');
    const start = moment(dates[0]).startOf('week');
    const end = moment(dates[dates.length - 1]).endOf('week');
    const range = momentRange.range(start, end);
    const days = Array.from(range.by('day'));

    for (let i = 0; i < days.length; i += 1) {
      const day = days[i];
      const dayName = day.format('dddd');
      if (!dataInSeries.recurrence.days.includes(dayName)) continue;
      if (day.isBefore(dates[0]) || day.isAfter(dates[dates.length - 1])) continue;

      id++;
      const formattedDate = day.format('MM/DD/YYYY');

      const event = {
        description: payload.description,
        isEscortNeeded: payload.isEscortNeeded,
        additionalNotes: payload.additionalNotes,
        CraneRequestId: id,
        craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
          formattedDate,
          payload.timezone,
          payload.deliveryStartTime
        ),
        craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
          formattedDate,
          payload.timezone,
          payload.deliveryEndTime
        ),
        ProjectId: payload.ProjectId,
        createdBy: memberDetails.id,
        isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
        pickUpLocation: payload.pickUpLocation,
        dropOffLocation: payload.dropOffLocation,
        recurrenceId,
      };

      if (
        memberDetails.RoleId === roleDetails.id ||
        memberDetails.RoleId === accountRoleDetails.id ||
        memberDetails.isAutoApproveEnabled ||
        projectDetails.ProjectSettings.isAutoApprovalEnabled
      ) {
        event.status = 'Approved';
        event.approvedBy = memberDetails.id;
        event.approved_at = new Date();
      }

      events.push(event);
    }

    return events;
  },

  async getMonthlyEventDate(dataInSeries, current) {
    if (dataInSeries.recurrence.chosenDateOfMonth) {
      return current.clone().date(+dataInSeries.recurrence.dateOfMonth);
    } else {
      const repeatType = dataInSeries.recurrence.monthlyRepeatType; // e.g., "second Monday"
      const [weekStr, dayStr] = repeatType.split(' ');
      const day = dayStr.toLowerCase();
      let weekday = current.clone().startOf('month').day(day);

      if (weekday.date() > 7) weekday.add(7, 'd');
      const month = weekday.month();

      const allDays = [];
      while (weekday.month() === month) {
        allDays.push(weekday.clone());
        weekday.add(7, 'd');
      }

      const weekMap = { first: 0, second: 1, third: 2, fourth: 3, last: allDays.length - 1 };
      return allDays[weekMap[weekStr.toLowerCase()]];
    }
  },

  async generateMonthlyRequests(
    dataInSeries,
    payload,
    dates,
    recurrenceId,
    memberDetails,
    projectDetails,
    startingId
  ) {
    const events = [];
    let id = startingId - 1;
    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    let current = moment(dates[0]).startOf('month');
    const end = moment(dates[dates.length - 1]).endOf('month');

    while (current.isSameOrBefore(end)) {
      const eventDate = await this.getMonthlyEventDate(dataInSeries, current);

      if (
        eventDate &&
        (eventDate.isBetween(dates[0], dates[dates.length - 1], null, '[]') ||
          eventDate.isSame(dates[0]) ||
          eventDate.isSame(dates[dates.length - 1]))
      ) {
        id++;
        const formattedDate = eventDate.format('MM/DD/YYYY');
        const event = {
          description: payload.description,
          isEscortNeeded: payload.isEscortNeeded,
          additionalNotes: payload.additionalNotes,
          CraneRequestId: id,
          craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
            formattedDate,
            payload.timezone,
            payload.deliveryStartTime
          ),
          craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
            formattedDate,
            payload.timezone,
            payload.deliveryEndTime
          ),
          ProjectId: payload.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
          pickUpLocation: payload.pickUpLocation,
          dropOffLocation: payload.dropOffLocation,
          recurrenceId,
        };

        if (
          memberDetails.RoleId === roleDetails.id ||
          memberDetails.RoleId === accountRoleDetails.id ||
          memberDetails.isAutoApproveEnabled ||
          projectDetails.ProjectSettings.isAutoApprovalEnabled
        ) {
          event.status = 'Approved';
          event.approvedBy = memberDetails.id;
          event.approved_at = new Date();
        }

        events.push(event);
      }

      current.add(+dataInSeries.recurrence.repeatEveryCount, 'months');
    }

    return events;
  },

  async getYearlyEventDate(current, recurrence) {
    if (recurrence.chosenDateOfMonth) {
      return current.clone().date(+recurrence.dateOfMonth);
    }

    const repeatType = recurrence.monthlyRepeatType;
    const [weekStr, dayStr] = repeatType.split(' ');
    const day = dayStr.toLowerCase();
    let weekday = current.clone().startOf('month').day(day);

    if (weekday.date() > 7) weekday.add(7, 'd');
    const month = weekday.month();

    const allDays = [];
    while (weekday.month() === month) {
      allDays.push(weekday.clone());
      weekday.add(7, 'd');
    }

    const weekMap = { first: 0, second: 1, third: 2, fourth: 3, last: allDays.length - 1 };
    return allDays[weekMap[weekStr.toLowerCase()]];
  },

  async generateYearlyRequests(
    dataInSeries,
    payload,
    dates,
    recurrenceId,
    memberDetails,
    projectDetails,
    startingId
  ) {
    const events = [];
    let id = startingId - 1;
    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    let current = moment(dates[0]).startOf('month');
    const end = moment(dates[dates.length - 1]).endOf('month');

    while (current.isSameOrBefore(end)) {
      const eventDate = await this.getYearlyEventDate(current, dataInSeries.recurrence);

      if (
        eventDate &&
        (eventDate.isBetween(dates[0], dates[dates.length - 1], null, '[]') ||
          eventDate.isSame(dates[0]) ||
          eventDate.isSame(dates[dates.length - 1]))
      ) {
        id++;
        const formattedDate = eventDate.format('MM/DD/YYYY');
        const event = {
          description: payload.description,
          isEscortNeeded: payload.isEscortNeeded,
          additionalNotes: payload.additionalNotes,
          CraneRequestId: id,
          craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
            formattedDate,
            payload.timezone,
            payload.deliveryStartTime
          ),
          craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
            formattedDate,
            payload.timezone,
            payload.deliveryEndTime
          ),
          ProjectId: payload.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
          pickUpLocation: payload.pickUpLocation,
          dropOffLocation: payload.dropOffLocation,
          recurrenceId,
        };

        if (
          memberDetails.RoleId === roleDetails.id ||
          memberDetails.RoleId === accountRoleDetails.id ||
          memberDetails.isAutoApproveEnabled ||
          projectDetails.ProjectSettings.isAutoApprovalEnabled
        ) {
          event.status = 'Approved';
          event.approvedBy = memberDetails.id;
          event.approved_at = new Date();
        }

        events.push(event);
      }

      current.add(+dataInSeries.recurrence.repeatEveryCount, 'years');
    }

    return events;
  },

  async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type) {
    if (!projectDetails.ProjectSettings.craneAllowOverlappingBooking) {
      const checkOverlapping = await this.checkCraneConflictsWithAlreadyScheduled(
        eventsArray,
        type,
      );
      if (checkOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with another booking. Overlapping is disabled by the administrator.',
        };
      }
    }
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.craneAllowOverlappingCalenderEvents
    ) {
      const checkCalenderOverlap =
        await concreteRequestService.checkCalenderEventsOverlappingWithBooking(
          eventsArray,
          'crane',
          type,
        );
      if (checkCalenderOverlap) {
        return {
          error: true,
          message:
            'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
        };
      }
    }
  },
  async checkCraneConflictsWithAlreadyScheduled(craneData, type) {
    const datesStartArr = [];
    const datesEndArr = [];
    const requestIds = [];
    const recurrenceIds = [];
    craneData.forEach((data) => {
      datesStartArr.push(new Date(data.craneDeliveryStart));
      datesEndArr.push(new Date(data.craneDeliveryEnd));
      if (type === 'edit') {
        if (data.id) {
          requestIds.push(data.id);
        }
        if (data.recurrenceId) {
          recurrenceIds.push(data.recurrenceId);
        }
      }
    });
    let condition = {
      ProjectId: craneData[0].ProjectId,
      status: {
        [Op.notIn]: ['Completed', 'Expired'],
      },
      isDeleted: false,
    };
    if (type === 'edit') {
      if (recurrenceIds && recurrenceIds.length > 0) {
        condition = {
          ...condition,
          recurrenceId: {
            [Op.notIn]: recurrenceIds,
          },
        };
      } else {
        condition = {
          ...condition,
          id: {
            [Op.notIn]: requestIds,
          },
        };
      }
    }
    const craneExist = await CraneRequest.findAll({
      where: {
        ...condition,
        [Op.or]: [
          {
            [Op.or]: datesStartArr.map((date) => ({
              craneDeliveryStart: { [Op.lt]: date },
              craneDeliveryEnd: { [Op.gt]: date },
            })),
          },
          {
            [Op.or]: datesEndArr.map((date) => ({
              craneDeliveryStart: { [Op.lt]: date },
              craneDeliveryEnd: { [Op.gt]: date },
            })),
          },
        ],
      },
    });

    const deliveryExist = await DeliveryRequest.findAll({
      where: {
        ProjectId: craneData[0].ProjectId,
        requestType: 'deliveryRequestWithCrane',
        status: {
          [Op.notIn]: ['Delivered', 'Expired'],
        },
        [Op.or]: [
          {
            [Op.or]: datesStartArr.map((date) => ({
              deliveryStart: { [Op.lte]: date },
              deliveryEnd: { [Op.gte]: date },
            })),
          },
          {
            [Op.or]: datesEndArr.map((date) => ({
              deliveryStart: { [Op.lte]: date },
              deliveryEnd: { [Op.gte]: date },
            })),
          },
        ],
      },
    });
    const overlappingDeliveryIds = []
    const overlappingCraneIds = []
    deliveryExist.forEach(data => {
      overlappingDeliveryIds.push(data.id)
    })
    craneExist.forEach((data) => {
      overlappingCraneIds.push(data.id)
    })
    const voidData = await VoidList.findAll({
      where: {
        [Op.or]: [
          { DeliveryRequestId: { [Op.in]: overlappingDeliveryIds } },
          { CraneRequestId: { [Op.in]: overlappingCraneIds } }
        ]
      }
    });

    const checkDeliveryOverlappingInVoid = deliveryExist.map(data => data.id);
    const checkCraneOverlappingInVoid = craneExist.map(data => data.id);


    const allDeliveryPresentInVoidList = checkDeliveryOverlappingInVoid.every(id =>
      voidData.some(voidEntry => voidEntry.DeliveryRequestId === id)
    );
    const allCranePresentInVoidList = checkCraneOverlappingInVoid.every(id =>
      voidData.some(voidEntry => voidEntry.CraneRequestId === id)
    );
    return allDeliveryPresentInVoidList.length || allCranePresentInVoidList.length;
  },
  async upcomingRequestListForMobile(req) {
    try {
      const requestList = [];
      const data = req.query;
      const loginUser = req.user;
      const statusData = await ProjectSettings.getCalendarStatusColor(req.query.ProjectId);
      const cardData = await ProjectSettings.getCalendarCard(req.query.ProjectId);
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: data.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      if (memberDetails) {
        const concreteCondition = {
          isDeleted: false,
          concretePlacementStart: {
            [Op.gt]: new Date(),
          },
        };
        const craneCondition = {
          isDeleted: false,
          craneDeliveryStart: {
            [Op.gt]: new Date(),
          },
        };
        const deliveryCondition = {
          isDeleted: false,
          deliveryStart: {
            [Op.gt]: new Date(),
          },
        };
        const voidDelivery = [];
        const voidConcrete = [];
        const voidCrane = [];
        const voidList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        deliveryCondition['$DeliveryRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidDelivery }],
        };
        const voidConcreteRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            ConcreteRequestId: { [Op.ne]: null },
          },
        });
        voidConcreteRequestList.forEach(async (element) => {
          voidConcrete.push(element.ConcreteRequestId);
        });
        concreteCondition['$ConcreteRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidConcrete }],
        };
        const voidCraneList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            CraneRequestId: { [Op.ne]: null },
          },
        });
        voidCraneList.forEach(async (element) => {
          voidCrane.push(element.CraneRequestId);
        });
        craneCondition['$CraneRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidCrane }],
        };
        const concreteRequestList = await ConcreteRequest.upcomingConcreteRequestForMobile(
          concreteCondition,
          data.ProjectId,
        );
        let craneRequestList = await CraneRequest.upcomingCraneRequestForMobile(
          craneCondition,
          data.ProjectId,
        );
        const deliveryRequestList = await DeliveryRequest.upcomingDeliveryRequestForMobile(
          deliveryCondition,
          data.ProjectId,
        );
        if (deliveryRequestList.length > 0) {
          craneRequestList.push(...deliveryRequestList);
        }
        if (craneRequestList.length > 0) {
          // Extract the date values for sorting
          craneRequestList.sort((a, b) => {
            const aDate = a.craneDeliveryStart ? a.craneDeliveryStart : a.deliveryStart;
            const bDate = b.craneDeliveryStart ? b.craneDeliveryStart : b.deliveryStart;
            return aDate > bDate ? 1 : -1;
          });
        }
        craneRequestList = craneRequestList.slice(0, 2);
        requestList.push({
          craneList: craneRequestList,
          concreteList: concreteRequestList,
          cardData,
          statusData,
        });
        return { status: 200, data: requestList };
      }
      return { status: 422, msg: 'Project Id/ParendCompany Id/Member does not exist' };
    } catch (e) {
      console.log(e);
      return e;
    }
  },
  async updateCraneRequestTimeAndLocationHistory(
    userEditedCraneRequestData,
    existsCraneRequestData,
    historyObject,
    loginUser
  ) {
    // Crane delivery start and end time update
    if (
      userEditedCraneRequestData.craneDeliveryStart &&
      userEditedCraneRequestData.craneDeliveryEnd
    ) {
      if (
        new Date(userEditedCraneRequestData.craneDeliveryStart).getTime() !==
        new Date(existsCraneRequestData.craneDeliveryStart).getTime()
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Pick From Time ${userEditedCraneRequestData.craneDeliveryStart}`;
        CraneRequestHistory.createInstance(historyObject);
      }
      if (
        new Date(userEditedCraneRequestData.craneDeliveryEnd).getTime() !==
        new Date(existsCraneRequestData.craneDeliveryEnd).getTime()
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Pick To Time ${userEditedCraneRequestData.craneDeliveryEnd}`;
        CraneRequestHistory.createInstance(historyObject);
      }
    }

    // Pickup and drop-off location update
    if (
      userEditedCraneRequestData.pickUpLocation &&
      userEditedCraneRequestData.dropOffLocation
    ) {
      if (
        userEditedCraneRequestData.pickUpLocation !==
        existsCraneRequestData.pickUpLocation
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking From ${userEditedCraneRequestData.pickUpLocation}`;
        CraneRequestHistory.createInstance(historyObject);
      }
      if (
        userEditedCraneRequestData.dropOffLocation !==
        existsCraneRequestData.dropOffLocation
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking To ${userEditedCraneRequestData.dropOffLocation}`;
        CraneRequestHistory.createInstance(historyObject);
      }
    }
  },

  async updateCraneRequestEscortHistory(
    userEditedCraneRequestData,
    existsCraneRequestData,
    historyObject,
    loginUser
  ) {
    if (userEditedCraneRequestData.escort !== undefined) {
      if (userEditedCraneRequestData.escort !== existsCraneRequestData.escort) {
        historyObject.description = userEditedCraneRequestData.escort
          ? `${loginUser.firstName} ${loginUser.lastName} enabled the Escort`
          : `${loginUser.firstName} ${loginUser.lastName} disabled the Escort`;
        CraneRequestHistory.createInstance(historyObject);
      }
    }
  },

  async updateCraneRequestBasicDetailsHistory(
    userEditedCraneRequestData,
    existsCraneRequestData,
    historyObject,
    loginUser
  ) {
    await this.updateCraneRequestTimeAndLocationHistory(
      userEditedCraneRequestData,
      existsCraneRequestData,
      historyObject,
      loginUser
    );

    await this.updateCraneRequestEscortHistory(
      userEditedCraneRequestData,
      existsCraneRequestData,
      historyObject,
      loginUser
    );
  },


  async updateCraneRequestMemberAndCompanyHistory(
    updatedCraneRequest,
    existsCraneRequestData,
    historyObject,
    loginUser
  ) {
    // Member Details
    if (updatedCraneRequest.memberDetails.length > 0 && existsCraneRequestData.memberDetails.length > 0) {
      const addedMember = updatedCraneRequest.memberDetails.filter(el =>
        !existsCraneRequestData.memberDetails.find(element => element.id === el.id)
      );
      const deletedMember = existsCraneRequestData.memberDetails.filter(el =>
        !updatedCraneRequest.memberDetails.find(element => element.id === el.id)
      );

      for (const element of addedMember) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
      for (const element of deletedMember) {
        historyObject.description = element.Member.User.firstName
          ? `${loginUser.firstName} ${loginUser.lastName} deleted the member ${element.Member.User.firstName} ${element.Member.User.lastName}`
          : `${loginUser.firstName} ${loginUser.lastName} deleted the member`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }

    // Company Details
    if (updatedCraneRequest.companyDetails.length > 0 && existsCraneRequestData.companyDetails.length > 0) {
      const addedCompany = updatedCraneRequest.companyDetails.filter(el =>
        !existsCraneRequestData.companyDetails.find(element => element.id === el.id)
      );
      const deletedCompany = existsCraneRequestData.companyDetails.filter(el =>
        !updatedCraneRequest.companyDetails.find(element => element.id === el.id)
      );

      for (const element of addedCompany) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the company ${element.Company.companyName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
      for (const element of deletedCompany) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the company ${element.Company.companyName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }
  },

  async updateCraneRequestWorkAndEquipmentHistory(
    updatedCraneRequest,
    existsCraneRequestData,
    historyObject,
    loginUser
  ) {
    // DFOW
    if (updatedCraneRequest.defineWorkDetails.length > 0 && existsCraneRequestData.defineWorkDetails.length > 0) {
      const addedDfow = updatedCraneRequest.defineWorkDetails.filter(el =>
        !existsCraneRequestData.defineWorkDetails.find(element => element.id === el.id)
      );
      const deletedDfow = existsCraneRequestData.defineWorkDetails.filter(el =>
        !updatedCraneRequest.defineWorkDetails.find(element => element.id === el.id)
      );

      for (const element of addedDfow) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
      for (const element of deletedDfow) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }

    // Equipment
    if (updatedCraneRequest.equipmentDetails.length > 0 && existsCraneRequestData.equipmentDetails.length > 0) {
      const addedEquipment = updatedCraneRequest.equipmentDetails.filter(el =>
        !existsCraneRequestData.equipmentDetails.find(element => element.Equipment.id === el.Equipment.id)
      );
      const deletedEquipment = existsCraneRequestData.equipmentDetails.filter(el =>
        !updatedCraneRequest.equipmentDetails.find(element => element.Equipment.id === el.Equipment.id)
      );

      for (const element of addedEquipment) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Equipment ${element.Equipment.equipmentName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
      for (const element of deletedEquipment) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Equipment ${element.Equipment.equipmentName}`;
        await CraneRequestHistory.createInstance(historyObject);
      }
    }
  },

  async updateMultipleEditCraneRequestHistory(
    userEditedCraneRequestData,
    existsCraneRequestData,
    updatedCraneRequest,
    history,
    loginUser
  ) {
    const historyObject = history;

    await this.updateCraneRequestBasicDetailsHistory(userEditedCraneRequestData, existsCraneRequestData, historyObject, loginUser);
    await this.updateCraneRequestMemberAndCompanyHistory(updatedCraneRequest, existsCraneRequestData, historyObject, loginUser);
    await this.updateCraneRequestWorkAndEquipmentHistory(updatedCraneRequest, existsCraneRequestData, historyObject, loginUser);
  },

  async createNewRecurrenceEvents(
    craneRequestDetail,
    memberDetails,
    totalDays1,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    done,
  ) {
    let recurrenceEvents = [];
    switch (craneRequestDetail.recurrence) {
      case 'Daily':
        recurrenceEvents = await this._createDailyRecurrenceEvents(
          craneRequestDetail,
          memberDetails,
          totalDays1,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );
        break;
      case 'Weekly':
        recurrenceEvents = await this._createWeeklyRecurrenceEvents(
          craneRequestDetail,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );
        break;
      case 'Monthly':
        recurrenceEvents = await this._createMonthlyRecurrenceEvents(
          craneRequestDetail,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );
        break;
      case 'Yearly':
        recurrenceEvents = await this._createYearlyRecurrenceEvents(
          craneRequestDetail,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );
        break;
      default:
        return done(null, { message: 'Invalid recurrence type' });
    }
    if (recurrenceEvents && recurrenceEvents.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        recurrenceEvents,
        projectDetails,
        'edit',
      );
      if (isOverlapping?.error) {
        return done(null, {
          message: isOverlapping.message,
        });
      }
      return done(recurrenceEvents, false);
    }
    return done([], false);
  },

  async _createDailyRecurrenceEvents(
    craneRequestDetail,
    memberDetails,
    totalDays,
    roleDetails,
    accountRoleDetails,
    projectDetails
  ) {
    const eventsArray = [];
    const startTime = craneRequestDetail.deliveryStartTime;
    const endTime = craneRequestDetail.deliveryEndTime;
    let dailyIndex = 0;
    const { recurrenceId } = craneRequestDetail;
    while (dailyIndex < totalDays.length) {
      const data = totalDays[dailyIndex];
      if (
        moment(data).isBetween(
          moment(craneRequestDetail.craneDeliveryStart),
          moment(craneRequestDetail.recurrenceEndDate).add(1, 'day'),
          null,
          '[]',
        ) ||
        moment(data).isSame(craneRequestDetail.craneDeliveryStart) ||
        moment(data).isSame(craneRequestDetail.recurrenceEndDate).add(1, 'day')
      ) {
        const date = moment(data).format('MM/DD/YYYY');
        const chosenTimezoneCraneDeliveryStart = moment.tz(
          `${date} ${startTime}`,
          'MM/DD/YYYY HH:mm',
          craneRequestDetail.timezone,
        );
        const chosenTimezoneCraneDeliveryEnd = moment.tz(
          `${date} ${endTime}`,
          'MM/DD/YYYY HH:mm',
          craneRequestDetail.timezone,
        );
        const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');
        const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');
        let craneRequestParam = {
          description: craneRequestDetail.description,
          isEscortNeeded: craneRequestDetail.isEscortNeeded,
          LocationId: craneRequestDetail.LocationId,
          additionalNotes: craneRequestDetail.additionalNotes,
          CraneRequestId: null,
          craneDeliveryStart,
          craneDeliveryEnd,
          ProjectId: craneRequestDetail.ProjectId,
          createdBy: memberDetails.id,
          isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
          pickUpLocation: craneRequestDetail.pickUpLocation,
          dropOffLocation: craneRequestDetail.dropOffLocation,
          recurrenceId,
        };
        if (
          memberDetails.RoleId === roleDetails.id ||
          memberDetails.RoleId === accountRoleDetails.id ||
          memberDetails.isAutoApproveEnabled ||
          projectDetails.ProjectSettings.isAutoApprovalEnabled
        ) {
          craneRequestParam.status = 'Approved';
          craneRequestParam.approvedBy = memberDetails.id;
          craneRequestParam.approved_at = new Date();
        }
        eventsArray.push(craneRequestParam);
        dailyIndex += +craneRequestDetail.repeatEveryCount;
      } else {
        dailyIndex += 1;
      }
    }
    return eventsArray;
  },

  async _createWeeklyRecurrenceEvents(
    craneRequestDetail,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails
  ) {
    const eventsArray = [];
    const totalDays = this._getTotalDays(craneRequestDetail);

    let count, weekIncrement;
    if (+craneRequestDetail.repeatEveryCount > 1) {
      count = +craneRequestDetail.repeatEveryCount - 1;
      weekIncrement = 7;
    } else {
      count = 1;
      weekIncrement = 0;
    }

    for (let indexba = 0; indexba < totalDays.length; indexba += weekIncrement * count) {
      const totalLength = indexba + 6;
      for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
        const data = totalDays[indexb];
        if (
          data &&
          !moment(data).isBefore(craneRequestDetail.craneDeliveryStart) &&
          !moment(data).isAfter(craneRequestDetail.recurrenceEndDate)
        ) {
          const day = moment(data).format('dddd');
          if (craneRequestDetail.days.includes(day)) {
            const craneRequestParam = this._createSingleEvent(
              craneRequestDetail,
              memberDetails,
              roleDetails,
              accountRoleDetails,
              projectDetails,
              data
            );
            eventsArray.push(craneRequestParam);
          }
        }
      }
    }

    return eventsArray;
  },

  _getTotalDays(craneRequestDetail) {
    const startDayWeek = moment(craneRequestDetail.craneDeliveryStart).startOf('week');
    const endDayWeek = moment(craneRequestDetail.recurrenceEndDate).endOf('week');
    const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
    return Array.from(range1.by('day'));
  },

  _createSingleEvent(
    craneRequestDetail,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    data
  ) {
    const startTime = craneRequestDetail.deliveryStartTime;
    const endTime = craneRequestDetail.deliveryEndTime;
    const date = moment(data).format('MM/DD/YYYY');

    const chosenTimezoneCraneDeliveryStart = moment.tz(
      `${date} ${startTime}`,
      'MM/DD/YYYY HH:mm',
      craneRequestDetail.timezone
    );
    const chosenTimezoneCraneDeliveryEnd = moment.tz(
      `${date} ${endTime}`,
      'MM/DD/YYYY HH:mm',
      craneRequestDetail.timezone
    );

    const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    const craneRequestParam = {
      description: craneRequestDetail.description,
      isEscortNeeded: craneRequestDetail.isEscortNeeded,
      additionalNotes: craneRequestDetail.additionalNotes,
      CraneRequestId: null,
      craneDeliveryStart,
      craneDeliveryEnd,
      ProjectId: craneRequestDetail.ProjectId,
      createdBy: memberDetails.id,
      isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
      pickUpLocation: craneRequestDetail.pickUpLocation,
      dropOffLocation: craneRequestDetail.dropOffLocation,
      recurrenceId: craneRequestDetail.recurrenceId,
      LocationId: craneRequestDetail.LocationId,
    };

    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      craneRequestParam.status = 'Approved';
      craneRequestParam.approvedBy = memberDetails.id;
      craneRequestParam.approved_at = new Date();
    }

    return craneRequestParam;
  },


  async _createMonthlyRecurrenceEvents(
    craneRequestDetail,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails
  ) {
    const eventsArray = [];
    const { recurrenceId, repeatEveryCount } = craneRequestDetail;
    const allMonthsInPeriod = this._generateMonthlyPeriod(craneRequestDetail);

    let k = 0;
    while (k < allMonthsInPeriod.length + 1) {
      const validDates = this._generateMonthlyDates(allMonthsInPeriod[k], craneRequestDetail);

      for (const date of validDates) {
        const event = this._createEventObject(
          date,
          craneRequestDetail,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          recurrenceId
        );
        if (event) eventsArray.push(event);
      }
      k += +repeatEveryCount;
    }
    return eventsArray;
  },

  _generateMonthlyPeriod(craneRequestDetail) {
    let startDate = moment(craneRequestDetail.craneDeliveryStart);
    const endDate = moment(craneRequestDetail.recurrenceEndDate).endOf('month');
    const allMonthsInPeriod = [];

    while (startDate.isBefore(endDate)) {
      allMonthsInPeriod.push(startDate.format('YYYY-MM'));
      startDate = startDate.add(1, 'month');
    }
    return allMonthsInPeriod;
  },

  _generateMonthlyDates(month, craneRequestDetail) {
    if (!month) return [];
    const { craneDeliveryStart, recurrenceEndDate, chosenDateOfMonth, dateOfMonth, monthlyRepeatType } = craneRequestDetail;
    const start = moment(craneDeliveryStart);
    const end = moment(recurrenceEndDate).add(1, 'day');

    if (chosenDateOfMonth) {
      const monthDates = Array.from(
        { length: moment(month, 'YYYY-MM').daysInMonth() },
        (_, j) => moment(month, 'YYYY-MM').startOf('month').add(j, 'days')
      );
      return monthDates
        .filter((d) => moment(d).format('DD') === dateOfMonth)
        .filter((d) => moment(d).isBetween(start, end, null, '[]'));
    } else {
      const [week, day] = monthlyRepeatType.split(' ').map((s) => s.toLowerCase());
      const chosenDay = moment(month, 'YYYY-MM').startOf('month').day(day);
      if (chosenDay.date() > 7) chosenDay.add(7, 'd');

      const days = [];
      while (chosenDay.month() === moment(month, 'YYYY-MM').month()) {
        days.push(chosenDay.clone());
        chosenDay.add(7, 'd');
      }

      let index = { first: 0, second: 1, third: 2, fourth: 3, last: days.length - 1 }[week] || 0;
      const finalDay = days[index];
      return finalDay && moment(finalDay).isBetween(start, end, null, '[]') ? [finalDay] : [];
    }
  },

  _createEventObject(date, craneRequestDetail, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId) {
    if (!date) return null;
    const { deliveryStartTime, deliveryEndTime, timezone } = craneRequestDetail;

    const dateStr = moment(date).format('MM/DD/YYYY');
    const start = moment.tz(`${dateStr} ${deliveryStartTime}`, 'MM/DD/YYYY HH:mm', timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    const end = moment.tz(`${dateStr} ${deliveryEndTime}`, 'MM/DD/YYYY HH:mm', timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

    let event = {
      description: craneRequestDetail.description,
      isEscortNeeded: craneRequestDetail.isEscortNeeded,
      additionalNotes: craneRequestDetail.additionalNotes,
      CraneRequestId: null,
      craneDeliveryStart: start,
      craneDeliveryEnd: end,
      ProjectId: craneRequestDetail.ProjectId,
      createdBy: memberDetails.id,
      isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
      pickUpLocation: craneRequestDetail.pickUpLocation,
      dropOffLocation: craneRequestDetail.dropOffLocation,
      recurrenceId,
      LocationId: craneRequestDetail.LocationId,
    };

    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      event.status = 'Approved';
      event.approvedBy = memberDetails.id;
      event.approved_at = new Date();
    }

    return event;
  },

  async _createYearlyRecurrenceEvents(
    craneRequestDetail,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails
  ) {
    const eventsArray = [];
    const allMonthsInPeriod = this._getAllMonthsInPeriod(
      craneRequestDetail.craneDeliveryStart,
      craneRequestDetail.recurrenceEndDate
    );

    for (let k = 0; k < allMonthsInPeriod.length; k++) {
      const month = allMonthsInPeriod[k];
      const monthEvents = this._processMonthDates(
        craneRequestDetail,
        memberDetails,
        roleDetails,
        accountRoleDetails,
        projectDetails,
        month
      );
      eventsArray.push(...monthEvents);
    }

    return eventsArray;
  },

  _getAllMonthsInPeriod(startDate, endDate) {
    let start = moment(startDate);
    const end = moment(endDate).endOf('month');
    const months = [];

    while (start.isBefore(end)) {
      months.push(start.format('YYYY-MM'));
      start = start.add(12, 'month'); // yearly recurrence
    }
    return months;
  },

  _createCraneRequest(craneRequestDetail, memberDetails, roleDetails, accountRoleDetails, projectDetails, date) {
    const startTime = craneRequestDetail.deliveryStartTime;
    const endTime = craneRequestDetail.deliveryEndTime;
    const recurrenceId = craneRequestDetail.recurrenceId;

    const chosenTimezoneCraneDeliveryStart = moment.tz(
      `${date} ${startTime}`,
      'MM/DD/YYYY HH:mm',
      craneRequestDetail.timezone
    );
    const chosenTimezoneCraneDeliveryEnd = moment.tz(
      `${date} ${endTime}`,
      'MM/DD/YYYY HH:mm',
      craneRequestDetail.timezone
    );

    const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    const craneRequestParam = {
      description: craneRequestDetail.description,
      isEscortNeeded: craneRequestDetail.isEscortNeeded,
      additionalNotes: craneRequestDetail.additionalNotes,
      CraneRequestId: null,
      craneDeliveryStart,
      craneDeliveryEnd,
      ProjectId: craneRequestDetail.ProjectId,
      createdBy: memberDetails.id,
      isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
      pickUpLocation: craneRequestDetail.pickUpLocation,
      dropOffLocation: craneRequestDetail.dropOffLocation,
      recurrenceId,
      LocationId: craneRequestDetail.LocationId,
    };

    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      craneRequestParam.status = 'Approved';
      craneRequestParam.approvedBy = memberDetails.id;
      craneRequestParam.approved_at = new Date();
    }

    return craneRequestParam;
  },

  _processMonthDates(craneRequestDetail, memberDetails, roleDetails, accountRoleDetails, projectDetails, month) {
    const events = [];
    const start = moment(craneRequestDetail.craneDeliveryStart);
    const end = moment(craneRequestDetail.recurrenceEndDate).add(1, 'day');

    const isInRange = (date) =>
      moment(date).isBetween(start, end, null, '[]') ||
      moment(date).isSame(start) ||
      moment(date).isSame(end);

    const addEventIfInRange = (date) => {
      if (isInRange(date)) {
        events.push(
          this._createCraneRequest(
            craneRequestDetail,
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectDetails,
            moment(date).format('MM/DD/YYYY')
          )
        );
      }
    };

    if (craneRequestDetail.chosenDateOfMonth) {
      this._handleFixedDayOfMonth(craneRequestDetail, month, addEventIfInRange);
    } else {
      this._handleNthWeekdayOfMonth(craneRequestDetail, month, addEventIfInRange);
    }

    return events;
  },

  _handleFixedDayOfMonth(craneRequestDetail, month, addEventIfInRange) {
    const currentMonthDates = Array.from(
      { length: moment(month, 'YYYY-MM').daysInMonth() },
      (_, j) => moment(month, 'YYYY-MM').startOf('month').add(j, 'days')
    );
    const chosenDate = currentMonthDates.find(
      (date) => moment(date).format('DD') === craneRequestDetail.dateOfMonth
    );

    if (chosenDate) {
      addEventIfInRange(chosenDate);
    }
  },

  _handleNthWeekdayOfMonth(craneRequestDetail, month, addEventIfInRange) {
    const [week, day] = craneRequestDetail.monthlyRepeatType.split(' ');
    const chosenDay = moment(month, 'YYYY-MM').startOf('month').day(day.toLowerCase());
    const allDays = [];

    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
    const currentMonth = chosenDay.month();

    while (currentMonth === chosenDay.month()) {
      allDays.push(chosenDay.clone().format('YYYY-MM-DD'));
      chosenDay.add(7, 'd');
    }

    let i = ['first', 'second', 'third', 'fourth'].indexOf(week.toLowerCase());
    if (week.toLowerCase() === 'last') i = allDays.length - 1;
    if (i < 0) i = 0;

    const finalDay = allDays[i];
    if (finalDay) {
      addEventIfInRange(finalDay);
    }
  },

  async createEquipmentMapping(payload) {
    try {
      if (payload) {
        payload.EquipmentId = JSON.stringify(payload.EquipmentId)
        const createMapping = await EquipmentMapping.create(payload);
        if (createMapping) {
          return true;
        }
      }
    } catch (err) {
      console.log(err, "error in create Mapping")
      return err
    }
  },

  async deleteEquipmentMapping(payload) {
    try {
      if (payload) {
        const condition = {
          where: {
            [Op.and]: {
              GateId: payload.GateId,
              LocationId: payload.LocationId,
              CraneId: payload.CraneId,
            },
          },
        };
        return await EquipmentMapping.destroy(condition);
      }
    } catch (err) {
      console.log(err, "error in create Mapping")
      return err
    }
  },

  async findEquipmentMapping(payload) {
    const eventTimeZone = await TimeZone.findOne({
      where: {
        isDeleted: false,
        id: +payload.TimeZoneId,
      },
      attributes: [
        'id',
        'location',
        'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes',
        'dayLightSavingTimeInMinutes',
        'timezone',
      ],
    });
    if (!eventTimeZone) {
      return done(null, { message: 'Provide a valid timezone' });
    }
    const chosenTimezoneStart = moment.tz(
      `${payload.craneDeliveryStart} ${payload.startPicker}`,
      'YYYY MM DD 00:00:00 HH:mm',
      eventTimeZone.timezone,
    );
    const chosenTimezoneEnd = moment.tz(
      `${payload.craneDeliveryEnd} ${payload.endPicker}`,
      'YYYY MM DD 00:00:00 HH:mm',
      eventTimeZone.timezone,
    );
    const Start = chosenTimezoneStart
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const End = chosenTimezoneEnd
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const results = await EquipmentMapping.findAll({
      where: {
        [Op.and]: [
          {
            [Op.or]: [
              {
                startTime: {
                  [Op.between]: [Start, End],
                },
              },
              {
                endTime: {
                  [Op.between]: [Start, End],
                },
              },
            ],
          },
          Sequelize.literal(
            `string_to_array(trim(both '[]' from "EquipmentId"), ',')::int[] && ARRAY[${payload.EquipmentId.join(',')}]::int[]`
          ),
        ],
      },
    });

    return results.length === 0;
  }
};
module.exports = craneRequestService;
