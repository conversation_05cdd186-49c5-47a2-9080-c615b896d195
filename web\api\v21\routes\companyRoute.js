const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { CompanyController } = require('../controllers');
const { companyValidation } = require('../middlewares/validations');
const checkAdmin = require('../middlewares/checkAdmin');
const upload1 = multer({ dest: 'uploads/' }); // NOSONAR

const storage = multer.memoryStorage();
const upload = multer({ storage }); // NOSONAR
const companyRoute = {
  get router() {
    const router = Router();
    router.get(
      '/all_companies',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      CompanyController.getAllCompaniesList,
    );
    router.post(
      '/get_companies/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      passportConfig.isAuthenticated,
      validate(companyValidation.listCompany, { keyByField: true }, { abortEarly: false }),
      CompanyController.getAllCompanies,
    );
    router.get(
      '/get_newcompanies/:ProjectId/?:ParentCompanyId',
      passportConfig.isAuthenticated,
      CompanyController.getCompanies,
    );
    router.post(
      '/add_company',
      validate(companyValidation.addCompany, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      CompanyController.addCompany,
    );
    router.post(
      '/upload_logo/:ProjectId/?:ParentCompanyId',
      upload.single('logo'),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      CompanyController.companyLogoUpload,
    );
    router.post(
      '/edit_company',
      validate(companyValidation.editCompany, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      CompanyController.editCompany,
    );
    router.post(
      '/delete_company',
      validate(companyValidation.deleteCompany, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      CompanyController.deleteCompany,
    );
    router.get(
      '/get_definable_work/:ProjectId/?:ParentCompanyId',
      validate(companyValidation.getDefinableWork, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CompanyController.getDefinableWork,
    );
    router.post(
      '/check_exist_company/:ProjectId/?:ParentCompanyId',
      validate(companyValidation.getDefinableWork, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CompanyController.checkExistCompany,
    );
    router.post(
      '/sample_company_template/?:ProjectId/?:ParentCompanyId',
      validate(
        companyValidation.sampleCompanyTemplate,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CompanyController.sampleCompanyTemplate,
    );
    router.post(
      '/create_company/:ProjectId/?:ParentCompanyId',
      upload1.single('company'),
      validate(companyValidation.createCompany, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CompanyController.createCompany,
    );
    return router;
  },
};
module.exports = companyRoute;
