// Mock models first
jest.mock('../../models', () => ({
  OverRide: {
    getOverRides: jest.fn(),
  },
}));

// Mock services
jest.mock('../../services', () => ({
  overRideService: {
    applyOverRide: jest.fn(),
    adminAction: jest.fn(),
  },
}));

const OverRideController = require('../OverRideController');
const { overRideService } = require('../../services');
const { OverRide } = require('../../models');

describe('OverRideController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      params: {},
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('applyOverRide', () => {
    it('should apply override successfully', async () => {
      const mockResponse = { id: 1, status: 'pending' };
      overRideService.applyOverRide.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await OverRideController.applyOverRide(mockReq, mockRes, mockNext);

      expect(overRideService.applyOverRide).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Your Booking sent Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from override application', async () => {
      const mockError = new Error('Service error');
      overRideService.applyOverRide.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await OverRideController.applyOverRide(mockReq, mockRes, mockNext);

      expect(overRideService.applyOverRide).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in override application', async () => {
      const mockError = new Error('Exception error');
      overRideService.applyOverRide.mockImplementation(() => {
        throw mockError;
      });

      await OverRideController.applyOverRide(mockReq, mockRes, mockNext);

      expect(overRideService.applyOverRide).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('listOverRide', () => {
    it('should list overrides successfully', async () => {
      const mockResponse = [{ id: 1, status: 'pending' }];
      mockReq.params = { pageNo: '1', pageSize: '10' };
      OverRide.getOverRides.mockResolvedValue(mockResponse);

      await OverRideController.listOverRide(mockReq, mockRes, mockNext);

      expect(OverRide.getOverRides).toHaveBeenCalledWith(10, 0);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Booking listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from override listing', async () => {
      const mockError = new Error('Database error');
      mockReq.params = { pageNo: '1', pageSize: '10' };
      OverRide.getOverRides.mockRejectedValue(mockError);

      await OverRideController.listOverRide(mockReq, mockRes, mockNext);

      expect(OverRide.getOverRides).toHaveBeenCalledWith(10, 0);
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should calculate offset correctly for different page numbers', async () => {
      const mockResponse = [{ id: 1, status: 'pending' }];
      mockReq.params = { pageNo: '3', pageSize: '5' };
      OverRide.getOverRides.mockResolvedValue(mockResponse);

      await OverRideController.listOverRide(mockReq, mockRes, mockNext);

      expect(OverRide.getOverRides).toHaveBeenCalledWith(5, 10); // (3-1) * 5 = 10
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Booking listed Successfully.',
        data: mockResponse,
      });
    });
  });

  describe('adminAction', () => {
    it('should perform admin action successfully', async () => {
      const mockResponse = { id: 1, status: 'approved' };
      mockReq.body.status = 'approved';
      overRideService.adminAction.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await OverRideController.adminAction(mockReq, mockRes, mockNext);

      expect(overRideService.adminAction).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'This booking approved successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from admin action', async () => {
      const mockError = new Error('Service error');
      overRideService.adminAction.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await OverRideController.adminAction(mockReq, mockRes, mockNext);

      expect(overRideService.adminAction).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in admin action', async () => {
      const mockError = new Error('Exception error');
      overRideService.adminAction.mockImplementation(() => {
        throw mockError;
      });

      await OverRideController.adminAction(mockReq, mockRes, mockNext);

      expect(overRideService.adminAction).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should use correct status message for rejected booking', async () => {
      const mockResponse = { id: 1, status: 'rejected' };
      mockReq.body.status = 'rejected';
      overRideService.adminAction.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await OverRideController.adminAction(mockReq, mockRes, mockNext);

      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'This booking rejected successfully.',
        data: mockResponse,
      });
    });
  });
});
