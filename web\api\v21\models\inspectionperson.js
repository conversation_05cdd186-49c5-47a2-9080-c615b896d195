module.exports = (sequelize, DataTypes) => {
  const InspectionPerson = sequelize.define(
    'InspectionPerson',
    {
      InspectionId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      InspectionCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  InspectionPerson.associate = (models) => {
    // associations can be defined here
    InspectionPerson.belongsTo(models.InspectionRequest, {
      as: 'inspectionrequest',
      foreignKey: 'InspectionId',
    });
    InspectionPerson.belongsTo(models.Member, {
      as: 'Member',
      foreignKey: 'MemberId',
    });
    InspectionPerson.belongsTo(models.Member);
  };
  InspectionPerson.createInstance = async (paramData) => {
    const newInspectionPerson = await InspectionPerson.create(paramData);
    return newInspection<PERSON>erson;
  };
  return InspectionPerson;
};
