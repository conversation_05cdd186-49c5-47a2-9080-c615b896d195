const haulingLogService = require('../haulingLogService');
const { HaulingLog } = require('../models');

// Mock all dependencies
jest.mock('../models', () => ({
  Sequelize: {
    Op: {
      in: jest.fn(),
      ne: jest.fn(),
      and: jest.fn(),
      or: jest.fn(),
      between: jest.fn(),
      notIn: jest.fn(),
    },
  },
  HaulingLog: {
    createHaulingLog: jest.fn(),
    getAll: jest.fn(),
  },
}));

describe('HaulingLogService', () => {
  let done;

  beforeEach(() => {
    jest.clearAllMocks();
    done = jest.fn();
  });

  describe('addHaulingLog', () => {
    it('should add a hauling log and call done with result', async () => {
      const inputData = {
        body: {
          companyName: 'Test Company',
          materialType: 'Concrete',
          quantity: '100',
          projectId: 1
        }
      };
      const mockResult = { id: 1, companyName: 'Test Company', materialType: 'Concrete' };
      HaulingLog.createHaulingLog.mockResolvedValue(mockResult);

      await haulingLogService.addHaulingLog(inputData, done);

      expect(HaulingLog.createHaulingLog).toHaveBeenCalledWith(inputData.body);
      expect(done).toHaveBeenCalledWith(mockResult, false);
    });

    it('should call done with error if createHaulingLog throws', async () => {
      const inputData = {
        body: {
          companyName: 'Test Company',
          materialType: 'Concrete'
        }
      };
      const error = new Error('Database error');
      HaulingLog.createHaulingLog.mockRejectedValue(error);

      await haulingLogService.addHaulingLog(inputData, done);

      expect(HaulingLog.createHaulingLog).toHaveBeenCalledWith(inputData.body);
      expect(done).toHaveBeenCalledWith(null, error);
    });
  });

  describe('listHaulingLog', () => {
    it('should list hauling logs and call done with result', async () => {
      const inputData = {
        params: { pageNo: '1', pageSize: '10' },
        body: { sort: 'ASC', sortByField: 'id' },
        query: { projectId: 123 },
      };
      const mockResult = {
        rows: [{ id: 1, companyName: 'Test Company' }],
        count: 1
      };
      HaulingLog.getAll.mockResolvedValue(mockResult);

      await haulingLogService.listHaulingLog(inputData, done);

      expect(HaulingLog.getAll).toHaveBeenCalledWith(
        { isDeleted: false, projectId: 123 },
        10,
        0,
        {},
        'ASC',
        'id',
      );
      expect(done).toHaveBeenCalledWith(mockResult, false);
    });

    it('should handle pagination correctly', async () => {
      const inputData = {
        params: { pageNo: '3', pageSize: '5' },
        body: { sort: 'DESC', sortByField: 'createdAt' },
        query: { projectId: 456 },
      };
      const mockResult = {
        rows: [{ id: 2, companyName: 'Another Company' }],
        count: 1
      };
      HaulingLog.getAll.mockResolvedValue(mockResult);

      await haulingLogService.listHaulingLog(inputData, done);

      expect(HaulingLog.getAll).toHaveBeenCalledWith(
        { isDeleted: false, projectId: 456 },
        5,
        10, // (3-1) * 5 = 10
        {},
        'DESC',
        'createdAt',
      );
      expect(done).toHaveBeenCalledWith(mockResult, false);
    });

    it('should call done with error if getAll throws', async () => {
      const inputData = {
        params: { pageNo: '1', pageSize: '10' },
        body: { sort: 'ASC', sortByField: 'id' },
        query: { projectId: 123 },
      };
      const error = new Error('Database connection failed');
      HaulingLog.getAll.mockRejectedValue(error);

      await haulingLogService.listHaulingLog(inputData, done);

      expect(HaulingLog.getAll).toHaveBeenCalledWith(
        { isDeleted: false, projectId: 123 },
        10,
        0,
        {},
        'ASC',
        'id',
      );
      expect(done).toHaveBeenCalledWith(null, error);
    });

    it('should handle missing sort parameters', async () => {
      const inputData = {
        params: { pageNo: '1', pageSize: '10' },
        body: {}, // No sort or sortByField
        query: { projectId: 789 },
      };
      const mockResult = {
        rows: [{ id: 3, companyName: 'Third Company' }],
        count: 1
      };
      HaulingLog.getAll.mockResolvedValue(mockResult);

      await haulingLogService.listHaulingLog(inputData, done);

      expect(HaulingLog.getAll).toHaveBeenCalledWith(
        { isDeleted: false, projectId: 789 },
        10,
        0,
        {},
        undefined, // sort is undefined
        undefined, // sortByField is undefined
      );
      expect(done).toHaveBeenCalledWith(mockResult, false);
    });
  });
});
