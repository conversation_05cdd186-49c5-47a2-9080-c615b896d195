// Mock Sequelize first to avoid constructor issues
jest.mock('sequelize', () => {
  const mockSequelize = {
    authenticate: jest.fn(),
    close: jest.fn(),
  };
  return jest.fn(() => mockSequelize);
});

// Mock models
jest.mock('../../models', () => ({
  sequelize: {
    authenticate: jest.fn(),
    close: jest.fn(),
  },
}));

// Mock equipmentLogService
jest.mock('../../services/equipmentLogService', () => ({
  addEquipmentLog: jest.fn(),
  listEquipmentLog: jest.fn(),
}));

const equipmentLogController = require('../equipmentLogController');
const equipmentLogService = require('../../services/equipmentLogService');

describe('equipmentLogController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();

    // Mock console.log to avoid output during tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log
    console.log.mockRestore();
  });

  describe('addEquipmentLog', () => {
    it('should add equipment log successfully', async () => {
      const mockEquipmentLog = {
        id: 1,
        equipmentId: 123,
        action: 'maintenance',
        timestamp: '2023-01-01T10:00:00Z',
      };

      equipmentLogService.addEquipmentLog.mockImplementation((req, callback) => {
        callback(mockEquipmentLog, null);
      });

      await equipmentLogController.addEquipmentLog(mockReq, mockRes, mockNext);

      expect(equipmentLogService.addEquipmentLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'EquipmentLog added successfully.',
        data: mockEquipmentLog,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from add equipment log', async () => {
      const mockError = new Error('Service error');
      equipmentLogService.addEquipmentLog.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await equipmentLogController.addEquipmentLog(mockReq, mockRes, mockNext);

      expect(equipmentLogService.addEquipmentLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(console.log).toHaveBeenCalledWith(mockError, "error======");
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listEquipmentLog', () => {
    it('should list equipment logs successfully', async () => {
      const mockEquipmentLogDetail = [
        { id: 1, equipmentId: 123, action: 'maintenance' },
        { id: 2, equipmentId: 123, action: 'usage' },
      ];

      equipmentLogService.listEquipmentLog.mockImplementation((req, callback) => {
        callback(mockEquipmentLogDetail, null);
      });

      await equipmentLogController.listEquipmentLog(mockReq, mockRes, mockNext);

      expect(equipmentLogService.listEquipmentLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Equipment Log Listed successfully.',
        data: mockEquipmentLogDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list equipment log', async () => {
      const mockError = new Error('Service error');
      equipmentLogService.listEquipmentLog.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await equipmentLogController.listEquipmentLog(mockReq, mockRes, mockNext);

      expect(equipmentLogService.listEquipmentLog).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});



