const ExtendableError = require('../extendableError');

describe('ExtendableError', () => {
  it('should be instance of Error', () => {
    const err = new ExtendableError('fail', 500);
    expect(err).toBeInstanceOf(Error);
  });
  it('should set name, message, and status', () => {
    const err = new ExtendableError('fail', 404);
    expect(err.name).toBe('ExtendableError');
    expect(err.message).toBe('fail');
    expect(err.status).toBe(404);
  });
  it('should have a stack trace', () => {
    const err = new ExtendableError('fail', 500);
    expect(err.stack).toBeDefined();
  });
});
