const { Router } = require('express');
const { validate } = require('express-validation');
const HaulingLogController = require('../controllers/haulingLogController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const HaulingLogRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_haulinglog',
            passportConfig.isAuthenticated,
            HaulingLogController.addHaulingLog,
        );
        router.get(
            '/haulinglog_list/:pageSize/:pageNo',
            passportConfig.isAuthenticated,
            HaulingLogController.listHaulingLog,
        );
        return router;
    },
};
module.exports = HaulingLogRoute;