const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  VoidController: {
    createVoid: jest.fn(),
    getVoids: jest.fn(),
    updateVoid: jest.fn(),
    deleteVoid: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  voidValidation: {
    createVoid: jest.fn(),
    getVoids: jest.fn(),
    updateVoid: jest.fn(),
    deleteVoid: jest.fn(),
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
}));

describe('voidRoute', () => {
  let router;
  let voidRoute;
  let VoidController;
  let passportConfig;
  let voidValidation;
  let checkAdmin;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    voidRoute = require('../voidRoute');
    const controllers = require('../../controllers');
    VoidController = controllers.VoidController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    voidValidation = validations.voidValidation;
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = voidRoute.router;
      
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Add specific route verifications based on actual routes
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = voidRoute.router;
      const result2 = voidRoute.router;
      
      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      voidRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];
      
      allCalls.forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof voidRoute).toBe('object');
      expect(voidRoute).toHaveProperty('router');
      
      const descriptor = Object.getOwnPropertyDescriptor(voidRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(voidRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});