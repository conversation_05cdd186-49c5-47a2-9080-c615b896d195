const companyService = require('../companyService');

// Mock all required dependencies
jest.mock('../../models', () => ({
    Company: {
        findOne: jest.fn(),
        findAll: jest.fn(),
        createInstance: jest.fn(),
        update: jest.fn(),
        getAll: jest.fn(),
        getAllCompany: jest.fn(),
        findByPk: jest.fn(),
    },
    Project: {
        findByPk: jest.fn(),
    },
    Member: {
        findOne: jest.fn(),
        findAll: jest.fn(),
    },
    DeliverDefineWork: {
        findAll: jest.fn(),
        count: jest.fn(),
    },
    CompanyDefine: {
        createInstance: jest.fn(),
        findAll: jest.fn(),
        update: jest.fn(),
    },
    User: {
        findOne: jest.fn(),
    },
    Enterprise: {
        findOne: jest.fn(),
    },
    DeliverCompany: {
        findOne: jest.fn(),
    },
    CraneRequestCompany: {
        findOne: jest.fn(),
    },
    ConcreteRequestCompany: {
        findOne: jest.fn(),
    },
    Sequelize: {
        and: jest.fn(),
        or: jest.fn(),
        Op: {
            in: 'in',
            not: 'not',
            iLike: 'iLike',
            ne: 'ne',
            and: 'and',
            or: 'or',
        },
    },
}));

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn(),
}));

jest.mock('../../middlewares/awsConfig', () => ({
    singleUpload: jest.fn(),
}));

jest.mock('worker_threads', () => ({
    Worker: jest.fn(),
}));

jest.mock('exceljs', () => ({
    Workbook: jest.fn(),
}));

describe('Company Service Tests', () => {
    let mockDone;
    let mockInputData;

    beforeEach(() => {
        jest.clearAllMocks();
        mockDone = jest.fn();
        mockInputData = {
            user: {
                id: 1,
                email: '<EMAIL>',
                domainName: 'testdomain',
            },
            body: {
                ProjectId: 1,
                companyName: 'Test Company',
                website: 'www.test.com',
                definableWorkId: [1, 2],
            },
            params: {
                ProjectId: 1,
                ParentCompanyId: 1,
                pageNo: '1',
                pageSize: '10',
            },
        };

        // Setup common mock responses
        const { Project, Company, User, Member } = require('../../models');
        const { returnProjectModel, getDynamicModel } = require('../../helpers/domainHelper');

        Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });
        Company.findOne.mockResolvedValue(null);
        returnProjectModel.mockResolvedValue({
            Company: Company,
            User: User,
            Member: Member,
        });
        getDynamicModel.mockResolvedValue({
            Company: Company,
            Project: Project,
            Member: Member,
            DeliverDefineWork: require('../../models').DeliverDefineWork,
            CompanyDefine: require('../../models').CompanyDefine,
            User: User,
        });
    });

    describe('Basic functionality', () => {
        it('should have all required methods', () => {
            expect(typeof companyService.addCompany).toBe('function');
            expect(typeof companyService.editCompany).toBe('function');
            expect(typeof companyService.deleteCompany).toBe('function');
            expect(typeof companyService.getAll).toBe('function');
            expect(typeof companyService.getDynamicModel).toBe('function');
            expect(typeof companyService.getDomainEnterpriseValue).toBe('function');
            expect(typeof companyService.getUserEnterpriseValue).toBe('function');
            expect(typeof companyService.getEnterpriseByParentCompany).toBe('function');
            expect(typeof companyService.updateUserData).toBe('function');
            expect(typeof companyService.returnProjectModel).toBe('function');
            expect(typeof companyService.createPublicCompany).toBe('function');
            expect(typeof companyService.checkInputDatas).toBe('function');
            expect(typeof companyService.updateValues).toBe('function');
            expect(typeof companyService.getAllCompany).toBe('function');
            expect(typeof companyService.getDefinableWork).toBe('function');
            expect(typeof companyService.checkCompanyMappings).toBe('function');
            expect(typeof companyService.deleteCompanyRecord).toBe('function');
            expect(typeof companyService.processCompanyDeletion).toBe('function');
            expect(typeof companyService.dfowAndCompanyForBulkUploadDeliveryRequest).toBe('function');
            expect(typeof companyService.checkExistCompany).toBe('function');
            expect(typeof companyService.validateMemberAccess).toBe('function');
            expect(typeof companyService.validateFileFormat).toBe('function');
            expect(typeof companyService.validateProjectDetails).toBe('function');
            expect(typeof companyService.processExcelFile).toBe('function');
            expect(typeof companyService.createCompany).toBe('function');
            expect(typeof companyService.createCompanyData).toBe('function');
            expect(typeof companyService.companyLogoUpload).toBe('function');
        });
    });

    describe('getDomainEnterpriseValue', () => {
        it('should return enterprise value for valid domain', async () => {
            const { Enterprise } = require('../../models');
            const mockEnterprise = { id: 1, name: 'testdomain' };
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await companyService.getDomainEnterpriseValue('testdomain');

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'testdomain' },
            });
        });

        it('should return null for invalid domain', async () => {
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);

            const result = await companyService.getDomainEnterpriseValue('invaliddomain');

            expect(result).toBeNull();
        });

        it('should return null for null domain', async () => {
            const result = await companyService.getDomainEnterpriseValue(null);
            expect(result).toBeNull();
        });
    });

    describe('getEnterpriseByParentCompany', () => {
        it('should return enterprise by parent company', async () => {
            const { Enterprise } = require('../../models');
            const mockEnterprise = { id: 1, status: 'completed' };
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await companyService.getEnterpriseByParentCompany(1);

            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' },
            });
        });
    });

    describe('updateUserData', () => {
        it('should return original inputData when no enterprise value', async () => {
            const { User } = require('../../models');
            const result = await companyService.updateUserData(null, mockInputData, User);
            expect(result).toEqual(mockInputData);
        });

        it('should update user data when enterprise value exists', async () => {
            const { User } = require('../../models');
            const mockEnterprise = { id: 1 };
            const mockNewUser = { id: 2, email: '<EMAIL>' };
            User.findOne.mockResolvedValue(mockNewUser);

            const result = await companyService.updateUserData(mockEnterprise, mockInputData, User);

            expect(result.user).toEqual(mockNewUser);
            expect(User.findOne).toHaveBeenCalledWith({ where: { email: mockInputData.user.email } });
        });
    });

    describe('returnProjectModel', () => {
        it('should return project model successfully', async () => {
            const { returnProjectModel } = require('../../helpers/domainHelper');
            const mockModels = {
                Company: require('../../models').Company,
                User: require('../../models').User,
                Member: require('../../models').Member,
            };
            returnProjectModel.mockResolvedValue(mockModels);

            await companyService.returnProjectModel();

            expect(returnProjectModel).toHaveBeenCalled();
        });
    });

    describe('createPublicCompany', () => {
        it('should create public company when it does not exist', async () => {
            const mockCompanyData = { companyName: 'Test Company', website: 'test.com' };
            const mockPublicCompany = { findOne: jest.fn(), createInstance: jest.fn() };
            const { returnProjectModel } = require('../../helpers/domainHelper');
            returnProjectModel.mockResolvedValue({
                Company: mockPublicCompany,
                User: require('../../models').User,
                Member: require('../../models').Member,
            });
            mockPublicCompany.findOne.mockResolvedValue(null);

            await companyService.createPublicCompany(mockCompanyData);

            expect(mockPublicCompany.createInstance).toHaveBeenCalled();
        });

        it('should not create public company when it already exists', async () => {
            const mockCompanyData = { companyName: 'Test Company', website: 'test.com' };
            const mockPublicCompany = { findOne: jest.fn(), createInstance: jest.fn() };
            const { returnProjectModel } = require('../../helpers/domainHelper');
            returnProjectModel.mockResolvedValue({
                Company: mockPublicCompany,
                User: require('../../models').User,
                Member: require('../../models').Member,
            });
            mockPublicCompany.findOne.mockResolvedValue({ id: 1 });

            await companyService.createPublicCompany(mockCompanyData);

            expect(mockPublicCompany.createInstance).not.toHaveBeenCalled();
        });
    });

    describe('getUserEnterpriseValue', () => {
        it('should return null for null userData', async () => {
            const result = await companyService.getUserEnterpriseValue(null, 1);
            expect(result).toBeNull();
        });

        it('should return enterprise for account member', async () => {
            const mockMember = { id: 1, isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { id: 1, status: 'completed' };
            const { returnProjectModel } = require('../../helpers/domainHelper');
            const mockPublicMember = { findOne: jest.fn() };
            returnProjectModel.mockResolvedValue({
                Company: require('../../models').Company,
                User: require('../../models').User,
                Member: mockPublicMember,
            });
            mockPublicMember.findOne.mockResolvedValue(mockMember);
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await companyService.getUserEnterpriseValue({ id: 1 }, 1);

            expect(result).toEqual(mockEnterprise);
        });
    });

    describe('checkInputDatas', () => {
        it('should validate definable work IDs successfully', async () => {
            mockInputData.body.definableWorkId = [1, 2];
            const { DeliverDefineWork } = require('../../models');
            DeliverDefineWork.count.mockResolvedValue(2);

            await companyService.checkInputDatas(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should return error for invalid definable work IDs', async () => {
            mockInputData.body.definableWorkId = [1, 2, 3];
            const { DeliverDefineWork } = require('../../models');
            DeliverDefineWork.count.mockResolvedValue(2);

            await companyService.checkInputDatas(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Some Definable feature not in this list.' });
        });
    });

    describe('updateValues', () => {
        it('should update company define values successfully', async () => {
            const mockCondition = { ProjectId: 1, CompanyId: 1 };
            const { CompanyDefine } = require('../../models');
            CompanyDefine.update.mockResolvedValue([1]);

            await companyService.updateValues(mockCondition, mockInputData, mockDone);

            expect(CompanyDefine.update).toHaveBeenCalledWith(
                { isDeleted: true },
                { where: mockCondition }
            );
            expect(mockDone).toHaveBeenCalledWith({ status: 'ok' }, false);
        });

        it('should handle errors during update', async () => {
            const mockCondition = { ProjectId: 1, CompanyId: 1 };
            const error = new Error('Update failed');
            const { CompanyDefine } = require('../../models');
            CompanyDefine.update.mockRejectedValue(error);

            await companyService.updateValues(mockCondition, mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });

    describe('getAllCompany', () => {
        it('should return all companies successfully', async () => {
            const mockParentCompany = { id: 1, companyName: 'Parent Company' };
            const mockCompanyList = { rows: [{ id: 2, companyName: 'Child Company' }] };
            const { Company } = require('../../models');
            Company.findOne.mockResolvedValue(mockParentCompany);
            Company.getAllCompany.mockResolvedValue(mockCompanyList);

            await companyService.getAllCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                { companyList: mockCompanyList, parentCompany: mockParentCompany },
                false
            );
        });

        it('should handle errors', async () => {
            const error = new Error('Database error');
            const { Company } = require('../../models');
            Company.findOne.mockRejectedValue(error);

            await companyService.getAllCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });

    describe('getDefinableWork', () => {
        it('should return definable work records successfully', async () => {
            const mockDefineRecords = [
                { id: 1, DFOW: 'Work B' },
                { id: 2, DFOW: 'Work A' },
            ];
            const { DeliverDefineWork } = require('../../models');
            DeliverDefineWork.findAll.mockResolvedValue(mockDefineRecords);

            await companyService.getDefinableWork(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                { defineRecord: expect.any(Array) },
                false
            );
        });

        it('should handle errors', async () => {
            const error = new Error('Database error');
            const { DeliverDefineWork } = require('../../models');
            DeliverDefineWork.findAll.mockRejectedValue(error);

            await companyService.getDefinableWork(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });

    describe('checkCompanyMappings', () => {
        beforeEach(() => {
            const { DeliverCompany, CraneRequestCompany, ConcreteRequestCompany, Member } = require('../../models');
            DeliverCompany.findOne.mockResolvedValue(null);
            CraneRequestCompany.findOne.mockResolvedValue(null);
            ConcreteRequestCompany.findOne.mockResolvedValue(null);
            Member.findOne.mockResolvedValue(null);
        });

        it('should return canDelete true when no mappings exist', async () => {
            const mockItem = { id: 1, companyName: 'Test Company' };
            const mockCompanyData = { ProjectId: 1 };

            const result = await companyService.checkCompanyMappings(mockItem, mockCompanyData);

            expect(result.canDelete).toBe(true);
        });

        it('should return canDelete false when delivery mapping exists', async () => {
            const { DeliverCompany } = require('../../models');
            DeliverCompany.findOne.mockResolvedValue({ id: 1 });
            const mockItem = { id: 1, companyName: 'Test Company' };
            const mockCompanyData = { ProjectId: 1 };

            const result = await companyService.checkCompanyMappings(mockItem, mockCompanyData);

            expect(result.canDelete).toBe(false);
            expect(result.message).toContain('mapped to submitted bookings');
        });

        it('should return canDelete false when member mapping exists', async () => {
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue({ id: 1 });
            const mockItem = { id: 1, companyName: 'Test Company' };
            const mockCompanyData = { ProjectId: 1 };

            const result = await companyService.checkCompanyMappings(mockItem, mockCompanyData);

            expect(result.canDelete).toBe(false);
            expect(result.message).toContain('mapped to a member');
        });
    });

    describe('addCompany', () => {
        it('should successfully add a new company', async () => {
            const mockCompany = { id: 1, companyName: 'Test Company' };
            const { Company, DeliverDefineWork, CompanyDefine } = require('../../models');

            // Reset mocks to ensure clean state
            Company.findOne
                .mockResolvedValueOnce(null) // For existence check
                .mockResolvedValueOnce(null); // For lastIdValue check
            Company.createInstance.mockResolvedValue(mockCompany);
            DeliverDefineWork.count.mockResolvedValue(2);
            CompanyDefine.createInstance.mockResolvedValue({ id: 1 });

            await companyService.addCompany(mockInputData, mockDone);

            expect(Company.createInstance).toHaveBeenCalled();
            expect(CompanyDefine.createInstance).toHaveBeenCalledTimes(2);
            expect(mockDone).toHaveBeenCalledWith(mockCompany, false);
        });

        it('should return error if company name already exists', async () => {
            const { Company } = require('../../models');
            Company.findOne.mockResolvedValue({ id: 2, companyName: 'Test Company' });

            await companyService.addCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Company name/Website Already exist.' });
        });

        it('should return error if project does not exist', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(null);

            await companyService.addCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Project Does not exist.');
        });

        it('should handle auto ID generation when no previous company exists', async () => {
            const { Company, DeliverDefineWork, CompanyDefine } = require('../../models');
            Company.findOne
                .mockResolvedValueOnce(null) // For existence check
                .mockResolvedValueOnce(null); // For lastIdValue check
            const mockCompany = { id: 1, companyName: 'Test Company' };
            Company.createInstance.mockResolvedValue(mockCompany);
            DeliverDefineWork.count.mockResolvedValue(2);
            CompanyDefine.createInstance.mockResolvedValue({ id: 1 });

            await companyService.addCompany(mockInputData, mockDone);

            expect(Company.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({ companyAutoId: 2 })
            );
            expect(mockDone).toHaveBeenCalledWith(mockCompany, false);
        });

        it('should handle null website', async () => {
            mockInputData.body.website = null;
            const { Company, DeliverDefineWork, CompanyDefine } = require('../../models');
            Company.findOne
                .mockResolvedValueOnce(null) // For existence check
                .mockResolvedValueOnce(null); // For lastIdValue check
            const mockCompany = { id: 1, companyName: 'Test Company' };
            Company.createInstance.mockResolvedValue(mockCompany);
            DeliverDefineWork.count.mockResolvedValue(2);
            CompanyDefine.createInstance.mockResolvedValue({ id: 1 });

            await companyService.addCompany(mockInputData, mockDone);

            expect(Company.createInstance).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(mockCompany, false);
        });
    });

    describe('editCompany', () => {
        beforeEach(() => {
            mockInputData.body.id = 1;
            mockInputData.body.definableWorkId = [1, 2];
        });

        it('should successfully edit an existing company', async () => {
            const mockUpdateResult = [1];
            const { Company, CompanyDefine } = require('../../models');
            Company.findOne.mockResolvedValue(null);
            Company.update.mockResolvedValue(mockUpdateResult);
            CompanyDefine.findAll.mockResolvedValue([]);
            CompanyDefine.update.mockResolvedValue([1]);
            CompanyDefine.createInstance.mockResolvedValue({ id: 1 });

            await companyService.editCompany(mockInputData, mockDone);

            expect(Company.update).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(mockUpdateResult, false);
        });

        it('should return error if company name already exists for another company', async () => {
            const { Company } = require('../../models');
            Company.findOne.mockResolvedValue({ id: 2, companyName: 'Test Company' });

            await companyService.editCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Company name/Website Already exist.' });
        });

        it('should return error if project does not exist', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(null);

            await companyService.editCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Project Does not exist.');
        });
    });

    describe('deleteCompany', () => {
        beforeEach(() => {
            mockInputData.body.id = [1];
            mockInputData.body.isSelectAll = false;
        });

        it('should successfully delete a company', async () => {
            const mockCompany = { id: 1, companyName: 'Test Company', isParent: false };
            const { Company, DeliverCompany, CraneRequestCompany, ConcreteRequestCompany, Member } = require('../../models');
            Company.findAll.mockResolvedValue([mockCompany]);
            Company.findByPk.mockResolvedValue(mockCompany);
            Company.update.mockResolvedValue([1]);
            DeliverCompany.findOne.mockResolvedValue(null);
            CraneRequestCompany.findOne.mockResolvedValue(null);
            ConcreteRequestCompany.findOne.mockResolvedValue(null);
            Member.findOne.mockResolvedValue(null);

            await companyService.deleteCompany(mockInputData, mockDone);

            expect(Company.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.any(Object)
            );
            expect(mockDone).toHaveBeenCalledWith('success', false);
        });

        it('should not delete parent company', async () => {
            const mockCompany = { id: 1, companyName: 'Test Company', isParent: true };
            const { Company } = require('../../models');
            Company.findAll.mockResolvedValue([mockCompany]);
            Company.findByPk.mockResolvedValue(mockCompany);

            await companyService.deleteCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'You cannot able to delete, because this is Base Company.' });
        });

        it('should handle no companies found', async () => {
            const { Company } = require('../../models');
            Company.findAll.mockResolvedValue([]);

            await companyService.deleteCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'No companies found to delete' });
        });
    });

    describe('getAll', () => {
        beforeEach(() => {
            mockInputData.body.inviteMember = false;
        });

        it('should return all companies with pagination', async () => {
            const mockCompanies = {
                rows: [
                    { id: 1, companyName: 'Company 1' },
                    { id: 2, companyName: 'Company 2' },
                ],
                count: 2,
            };
            const { Company } = require('../../models');
            Company.getAll.mockResolvedValue(mockCompanies);
            Company.findAll.mockResolvedValue([]);

            await companyService.getAll(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    companyArray: expect.any(Object),
                    parentCompany: expect.any(Array),
                }),
                false
            );
        });

        it('should handle search conditions', async () => {
            mockInputData.body.search = 'test';
            const mockCompanies = { rows: [], count: 0 };
            const { Company, Sequelize } = require('../../models');
            Company.getAll.mockResolvedValue(mockCompanies);
            Company.findAll.mockResolvedValue([]);

            await companyService.getAll(mockInputData, mockDone);

            expect(Company.getAll).toHaveBeenCalledWith(
                expect.any(Object),
                expect.any(Number),
                expect.any(Number),
                expect.objectContaining({
                    [Sequelize.Op.and]: expect.any(Array),
                }),
                undefined,
                undefined
            );
        });
    });

    describe('companyLogoUpload', () => {
        it('should successfully upload company logo', async () => {
            const mockResult = { url: 'https://example.com/logo.png' };
            const { singleUpload } = require('../../middlewares/awsConfig');
            singleUpload.mockImplementation((_input, callback) => callback(mockResult, null));

            await companyService.companyLogoUpload(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(mockResult, null);
        });

        it('should handle upload errors', async () => {
            const mockError = new Error('Upload failed');
            const { singleUpload } = require('../../middlewares/awsConfig');
            singleUpload.mockImplementation((_input, callback) => callback(null, mockError));

            await companyService.companyLogoUpload(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('validateMemberAccess', () => {
        it('should validate member access successfully', async () => {
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue({ id: 1, RoleId: 1 });

            const result = await companyService.validateMemberAccess(mockInputData, 1);
            expect(result).toBeDefined();
        });

        it('should throw error for invalid member access', async () => {
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue(null);

            await expect(companyService.validateMemberAccess(mockInputData, 1))
                .rejects
                .toThrow('Project Does not exist or you are not a valid member.');
        });
    });

    describe('validateFileFormat', () => {
        it('should validate file format successfully', async () => {
            const mockFile = {
                originalname: 'TestProject_1_2023.xlsx',
            };

            const result = await companyService.validateFileFormat(mockFile);

            expect(result.extension).toBe('xlsx');
            expect(result.projectFileName).toBe('TestProject');
            expect(result.projectId).toBe('1');
        });

        it('should throw error for missing file', async () => {
            await expect(companyService.validateFileFormat(null))
                .rejects
                .toThrow('Please select a file.');
        });

        it('should throw error for invalid file name format', async () => {
            const mockFile = {
                originalname: 'InvalidFileName.xlsx',
            };

            await expect(companyService.validateFileFormat(mockFile))
                .rejects
                .toThrow('Invalid file');
        });
    });

    describe('validateProjectDetails', () => {
        it('should validate project details successfully', async () => {
            const mockProject = { id: 1, projectName: 'TestProject' };
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(mockProject);

            const result = await companyService.validateProjectDetails(1, 'TestProject', '1');

            expect(result).toEqual(mockProject);
        });

        it('should throw error for non-existent project', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(null);

            await expect(companyService.validateProjectDetails(1, 'TestProject', '1'))
                .rejects
                .toThrow('Project not found');
        });
    });

    describe('getDynamicModel', () => {
        it('should get dynamic model based on domain name', async () => {
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ id: 1, name: 'testdomain' });

            const result = await companyService.getDynamicModel(mockInputData);
            expect(result).toEqual(mockInputData.body.ProjectId);
            const { getDynamicModel } = require('../../helpers/domainHelper');
            expect(getDynamicModel).toHaveBeenCalled();
        });

        it('should handle enterprise value when domain name is not available', async () => {
            const testInputData = { ...mockInputData };
            testInputData.user.domainName = null;
            testInputData.body.ParentCompanyId = 1;
            const { Enterprise } = require('../../models');
            const { returnProjectModel } = require('../../helpers/domainHelper');

            const mockPublicUser = { findOne: jest.fn() };
            returnProjectModel.mockResolvedValue({
                Company: require('../../models').Company,
                User: mockPublicUser,
                Member: require('../../models').Member,
            });
            mockPublicUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
            Enterprise.findOne.mockResolvedValue({ id: 1, name: 'testdomain', status: 'completed' });

            const result = await companyService.getDynamicModel(testInputData);
            expect(result).toEqual(testInputData.body.ProjectId);
            const { getDynamicModel } = require('../../helpers/domainHelper');
            expect(getDynamicModel).toHaveBeenCalled();
        });
    });

    describe('dfowAndCompanyForBulkUploadDeliveryRequest', () => {
        it('should return define records and company list successfully', async () => {
            const mockDefineRecords = [{ id: 1, DFOW: 'Work 1' }];
            const mockParentCompany = { id: 1, companyName: 'Parent Company' };
            const mockCompanyList = { rows: [{ id: 2, companyName: 'Child Company' }] };

            const { DeliverDefineWork, Company } = require('../../models');
            DeliverDefineWork.findAll.mockResolvedValue(mockDefineRecords);
            Company.findOne.mockResolvedValue(mockParentCompany);
            Company.getAllCompany.mockResolvedValue(mockCompanyList);

            const result = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(mockInputData);

            expect(result.defineRecord).toEqual(mockDefineRecords);
            expect(result.newCompanyList).toContainEqual({
                id: mockParentCompany.id,
                companyName: mockParentCompany.companyName,
            });
        });

        it('should handle case when parent company already exists in list', async () => {
            const mockDefineRecords = [{ id: 1, DFOW: 'Work 1' }];
            const mockParentCompany = { id: 1, companyName: 'Parent Company' };
            const mockCompanyList = {
                rows: [
                    { id: 1, companyName: 'Parent Company' },
                    { id: 2, companyName: 'Child Company' }
                ]
            };

            const { DeliverDefineWork, Company } = require('../../models');
            DeliverDefineWork.findAll.mockResolvedValue(mockDefineRecords);
            Company.findOne.mockResolvedValue(mockParentCompany);
            Company.getAllCompany.mockResolvedValue(mockCompanyList);

            const result = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(mockInputData);

            expect(result.defineRecord).toEqual(mockDefineRecords);
            expect(result.newCompanyList).toHaveLength(2);
        });

        it('should handle case when no parent company exists', async () => {
            const mockDefineRecords = [{ id: 1, DFOW: 'Work 1' }];
            const mockCompanyList = { rows: [{ id: 2, companyName: 'Child Company' }] };

            const { DeliverDefineWork, Company } = require('../../models');
            DeliverDefineWork.findAll.mockResolvedValue(mockDefineRecords);
            Company.findOne.mockResolvedValue(null);
            Company.getAllCompany.mockResolvedValue(mockCompanyList);

            const result = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(mockInputData);

            expect(result.defineRecord).toEqual(mockDefineRecords);
            expect(result.newCompanyList).toHaveLength(1);
        });

        it('should handle errors gracefully', async () => {
            const error = new Error('Database error');
            const { DeliverDefineWork } = require('../../models');
            DeliverDefineWork.findAll.mockRejectedValue(error);

            const result = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(mockInputData);

            expect(result).toBeUndefined();
        });
    });

    describe('checkExistCompany', () => {
        beforeEach(() => {
            mockInputData.body.companyName = 'Test Company Name';
            mockInputData.params = { ProjectId: '1', ParentCompanyId: '1' };
        });

        it('should check existing company successfully', async () => {
            const mockExistInProject = [{ id: 2, companyName: 'Test Company' }];
            const mockSameAsParent = { id: 3, companyName: 'Test Parent' };

            const { Project, Company } = require('../../models');
            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });
            Company.findAll.mockResolvedValue(mockExistInProject);
            Company.findOne.mockResolvedValue(mockSameAsParent);

            const result = await companyService.checkExistCompany(mockInputData);

            expect(result.existInProject).toEqual(mockExistInProject);
            expect(result.sameAsParentCompany).toEqual(mockSameAsParent);
        });

        it('should handle case with existing company id', async () => {
            mockInputData.body.id = 1;
            const { Project, Company } = require('../../models');
            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });
            Company.findAll.mockResolvedValue([]);
            Company.findOne.mockResolvedValue(null);

            const result = await companyService.checkExistCompany(mockInputData);

            expect(result.existInProject).toEqual([]);
            expect(result.sameAsParentCompany).toBeNull();
        });

        it('should return error when project does not exist', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(null);

            const result = await companyService.checkExistCompany(mockInputData);

            expect(result.status).toBe(500);
            expect(result.message).toBe('ProjectId does not exist.');
        });

        it('should handle errors', async () => {
            const error = new Error('Database error');
            const { Project } = require('../../models');
            Project.findByPk.mockRejectedValue(error);

            const result = await companyService.checkExistCompany(mockInputData);

            expect(result).toEqual(error);
        });
    });

    describe('processCompanyDeletion', () => {
        const mockItem = { id: 1, companyName: 'Test Company' };
        const mockCompanyData = { ProjectId: 1 };

        it('should return error when company does not exist', async () => {
            const { Company } = require('../../models');
            Company.findByPk.mockResolvedValue(null);

            const result = await companyService.processCompanyDeletion(mockItem, mockCompanyData);

            expect(result.error).toBe('Company Does not Exist.');
        });

        it('should return error when trying to delete parent company', async () => {
            const mockCompanyDetails = { id: 1, isParent: true };
            const { Company } = require('../../models');
            Company.findByPk.mockResolvedValue(mockCompanyDetails);

            const result = await companyService.processCompanyDeletion(mockItem, mockCompanyData);

            expect(result.error).toBe('You cannot able to delete, because this is Base Company.');
        });

        it('should return error when company has mappings', async () => {
            const mockCompanyDetails = { id: 1, isParent: false };
            const { Company, DeliverCompany } = require('../../models');
            Company.findByPk.mockResolvedValue(mockCompanyDetails);
            DeliverCompany.findOne.mockResolvedValue({ id: 1 });

            const result = await companyService.processCompanyDeletion(mockItem, mockCompanyData);

            expect(result.error).toContain('mapped to submitted bookings');
        });

        it('should successfully delete company when no issues', async () => {
            const mockCompanyDetails = { id: 1, isParent: false };
            const { Company, DeliverCompany, CraneRequestCompany, ConcreteRequestCompany, Member } = require('../../models');
            Company.findByPk.mockResolvedValue(mockCompanyDetails);
            Company.update.mockResolvedValue([1]);
            DeliverCompany.findOne.mockResolvedValue(null);
            CraneRequestCompany.findOne.mockResolvedValue(null);
            ConcreteRequestCompany.findOne.mockResolvedValue(null);
            Member.findOne.mockResolvedValue(null);

            const result = await companyService.processCompanyDeletion(mockItem, mockCompanyData);

            expect(result.success).toBe(true);
            expect(Company.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.any(Object)
            );
        });
    });

    describe('validateProjectDetails', () => {
        it('should validate project details successfully', async () => {
            const mockProject = { id: 1, projectName: 'TestProject' };
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(mockProject);

            const result = await companyService.validateProjectDetails(1, 'TestProject', '1');

            expect(result).toEqual(mockProject);
        });

        it('should throw error for non-existent project', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(null);

            await expect(companyService.validateProjectDetails(1, 'TestProject', '1'))
                .rejects
                .toThrow('Project not found');
        });

        it('should throw error for mismatched project name', async () => {
            const mockProject = { id: 1, projectName: 'DifferentProject' };
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(mockProject);

            await expect(companyService.validateProjectDetails(1, 'TestProject', '1'))
                .rejects
                .toThrow('Invalid file');
        });

        it('should throw error for mismatched project ID', async () => {
            const mockProject = { id: 1, projectName: 'TestProject' };
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue(mockProject);

            await expect(companyService.validateProjectDetails(1, 'TestProject', '2'))
                .rejects
                .toThrow('Invalid file');
        });
    });

    describe('processExcelFile', () => {
        it('should process Excel file successfully', async () => {
            const mockFile = { path: '/path/to/file.xlsx' };
            const mockWorkbook = {
                xlsx: { readFile: jest.fn() },
                getWorksheet: jest.fn().mockReturnValue({ name: 'Company' })
            };
            const ExcelJS = require('exceljs');
            ExcelJS.Workbook.mockImplementation(() => mockWorkbook);

            const mockDone = jest.fn();
            const mockCreateCompanyData = jest.spyOn(companyService, 'createCompanyData');
            mockCreateCompanyData.mockImplementation((_worksheet, _inputData, callback) => {
                callback({ message: 'success' }, null);
            });

            await companyService.processExcelFile(mockFile, mockInputData, mockDone);

            expect(mockWorkbook.xlsx.readFile).toHaveBeenCalledWith(mockFile.path);
            expect(mockWorkbook.getWorksheet).toHaveBeenCalledWith('Company');
            expect(mockDone).toHaveBeenCalledWith({ message: 'success' }, false);

            mockCreateCompanyData.mockRestore();
        });

        it('should handle errors in processExcelFile', async () => {
            const mockFile = { path: '/path/to/file.xlsx' };
            const mockWorkbook = {
                xlsx: { readFile: jest.fn() },
                getWorksheet: jest.fn().mockReturnValue({ name: 'Company' })
            };
            const ExcelJS = require('exceljs');
            ExcelJS.Workbook.mockImplementation(() => mockWorkbook);

            const mockDone = jest.fn();
            const mockCreateCompanyData = jest.spyOn(companyService, 'createCompanyData');
            mockCreateCompanyData.mockImplementation((_worksheet, _inputData, callback) => {
                callback(null, { message: 'Error processing' });
            });

            await companyService.processExcelFile(mockFile, mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Error processing' });

            mockCreateCompanyData.mockRestore();
        });
    });

    describe('createCompany', () => {
        beforeEach(() => {
            mockInputData.file = {
                originalname: 'TestProject_1_2023.xlsx',
                path: '/path/to/file.xlsx'
            };
            mockInputData.params = { ProjectId: '1' };
        });

        it('should create company successfully with xlsx file', async () => {
            const { Member, Project } = require('../../models');
            Member.findOne.mockResolvedValue({ id: 1, RoleId: 1 });
            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'TestProject' });

            const mockProcessExcelFile = jest.spyOn(companyService, 'processExcelFile');
            mockProcessExcelFile.mockImplementation((_file, _inputData, callback) => {
                callback({ message: 'success' }, false);
            });

            await companyService.createCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith({ message: 'success' }, false);

            mockProcessExcelFile.mockRestore();
        });

        it('should return error for non-xlsx file', async () => {
            mockInputData.file.originalname = 'TestProject_1_2023.pdf';
            const { Member, Project } = require('../../models');
            Member.findOne.mockResolvedValue({ id: 1, RoleId: 1 });
            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'TestProject' });

            await companyService.createCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Please choose valid file' });
        });

        it('should handle validation errors', async () => {
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue(null);

            await companyService.createCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, {
                message: 'Project Does not exist or you are not a valid member.'
            });
        });
    });

    describe('createCompanyData', () => {
        const mockWorksheet = {
            eachRow: jest.fn()
        };

        beforeEach(() => {
            mockInputData.params = { ProjectId: '1' };
            mockInputData.file = { originalname: 'test.xlsx' };
            global.io = { emit: jest.fn() };
        });

        it('should handle empty company records', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });

            mockWorksheet.eachRow.mockImplementation((callback) => {
                // Simulate header row only
                callback({ values: [null, 'Header1', 'Header2'] }, 2);
            });

            await companyService.createCompanyData(mockWorksheet, mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, {
                message: 'Please upload proper document / Please fill mandatory column.',
            });
        });

        it('should handle invalid file format', async () => {
            const { Project } = require('../../models');
            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });

            mockWorksheet.eachRow.mockImplementation((callback) => {
                // Simulate header row with wrong number of columns
                callback({ values: [null, 'Header1', 'Header2'] }, 2);
                // Simulate data row
                callback({ values: [null, 'Company1', 'Data2'] }, 3);
            });

            await companyService.createCompanyData(mockWorksheet, mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Invalid File.!' });
        });

        it('should handle null worksheet', async () => {
            await companyService.createCompanyData(null, mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Invalid File' });
        });

        it('should process valid file successfully', async () => {
            const { Project } = require('../../models');
            const { Worker } = require('worker_threads');

            Project.findByPk.mockResolvedValue({ id: 1, projectName: 'Test Project' });

            const mockWorker = {
                postMessage: jest.fn(),
                on: jest.fn(),
                terminate: jest.fn()
            };
            Worker.mockImplementation(() => mockWorker);

            mockWorksheet.eachRow.mockImplementation((callback) => {
                // Simulate header row with correct number of columns
                callback({ values: [null, 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'H7', 'H8', 'H9', 'H10', 'H11'] }, 2);
                // Simulate data row
                callback({ values: [null, 'Company1', 'Data2', 'Data3'] }, 3);
            });

            await companyService.createCompanyData(mockWorksheet, mockInputData, mockDone);

            expect(Worker).toHaveBeenCalled();
            expect(mockWorker.postMessage).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith({ message: 'success' }, false);
        });

        it('should handle errors in createCompanyData', async () => {
            const error = new Error('Database error');
            const { Project } = require('../../models');
            Project.findByPk.mockRejectedValue(error);

            await companyService.createCompanyData(mockWorksheet, mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });

    describe('getUserEnterpriseValue - additional scenarios', () => {
        it('should return enterprise by parent company when member is not account', async () => {
            const mockMember = { id: 1, isAccount: false, EnterpriseId: 1 };
            const mockEnterprise = { id: 1, status: 'completed' };
            const { returnProjectModel } = require('../../helpers/domainHelper');
            const mockPublicMember = { findOne: jest.fn() };
            returnProjectModel.mockResolvedValue({
                Company: require('../../models').Company,
                User: require('../../models').User,
                Member: mockPublicMember,
            });
            mockPublicMember.findOne.mockResolvedValue(mockMember);
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await companyService.getUserEnterpriseValue({ id: 1 }, 1);

            expect(result).toEqual(mockEnterprise);
        });

        it('should return enterprise by parent company when no member found', async () => {
            const mockEnterprise = { id: 1, status: 'completed' };
            const { returnProjectModel } = require('../../helpers/domainHelper');
            const mockPublicMember = { findOne: jest.fn() };
            returnProjectModel.mockResolvedValue({
                Company: require('../../models').Company,
                User: require('../../models').User,
                Member: mockPublicMember,
            });
            mockPublicMember.findOne.mockResolvedValue(null);
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await companyService.getUserEnterpriseValue({ id: 1 }, 1);

            expect(result).toEqual(mockEnterprise);
        });
    });

    describe('getAll - additional scenarios', () => {
        beforeEach(() => {
            mockInputData.body.inviteMember = false;
        });

        it('should handle dfowFilter condition', async () => {
            mockInputData.body.dfowFilter = 1;
            const mockCompanies = { rows: [], count: 0 };
            const { Company } = require('../../models');
            Company.getAll.mockResolvedValue(mockCompanies);
            Company.findAll.mockResolvedValue([]);

            await companyService.getAll(mockInputData, mockDone);

            expect(Company.getAll).toHaveBeenCalledWith(
                expect.objectContaining({
                    '$define.DeliverDefineWork.id$': 1,
                }),
                expect.any(Number),
                expect.any(Number),
                expect.any(Object),
                undefined,
                undefined
            );
        });

        it('should handle companyFilter condition', async () => {
            mockInputData.body.companyFilter = 'test';
            const mockCompanies = { rows: [], count: 0 };
            const { Company, Sequelize } = require('../../models');
            Company.getAll.mockResolvedValue(mockCompanies);
            Company.findAll.mockResolvedValue([]);

            await companyService.getAll(mockInputData, mockDone);

            expect(Company.getAll).toHaveBeenCalledWith(
                expect.objectContaining({
                    companyName: {
                        [Sequelize.Op.iLike]: '%test%',
                    },
                }),
                expect.any(Number),
                expect.any(Number),
                expect.any(Object),
                undefined,
                undefined
            );
        });

        it('should handle inviteMember page scenario', async () => {
            mockInputData.body.inviteMember = true;
            const mockCompanies = {
                rows: [
                    { id: 2, companyName: 'Company B' },
                    { id: 1, companyName: 'Company A' },
                ],
                count: 2,
            };
            const { Company } = require('../../models');
            Company.getAll.mockResolvedValue(mockCompanies);
            Company.findAll.mockResolvedValue([]);

            await companyService.getAll(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    companyArray: expect.objectContaining({
                        rows: expect.arrayContaining([
                            expect.objectContaining({ companyName: 'Company A' }),
                            expect.objectContaining({ companyName: 'Company B' }),
                        ]),
                    }),
                }),
                false
            );
        });

        it('should handle numeric search condition', async () => {
            mockInputData.body.search = '123';
            const mockCompanies = { rows: [], count: 0 };
            const { Company, Sequelize } = require('../../models');
            Company.getAll.mockResolvedValue(mockCompanies);
            Company.findAll.mockResolvedValue([]);

            await companyService.getAll(mockInputData, mockDone);

            expect(Company.getAll).toHaveBeenCalledWith(
                expect.any(Object),
                expect.any(Number),
                expect.any(Number),
                expect.objectContaining({
                    [Sequelize.Op.and]: expect.arrayContaining([
                        expect.objectContaining({
                            [Sequelize.Op.or]: expect.arrayContaining([
                                expect.any(Array),
                                expect.objectContaining({
                                    [Sequelize.Op.and]: expect.arrayContaining([
                                        expect.objectContaining({
                                            companyAutoId: '123',
                                        }),
                                    ]),
                                }),
                            ]),
                        }),
                    ]),
                }),
                undefined,
                undefined
            );
        });
    });

    describe('deleteCompany - additional scenarios', () => {
        beforeEach(() => {
            mockInputData.body.id = [1];
            mockInputData.body.isSelectAll = false;
        });

        it('should handle isSelectAll scenario', async () => {
            mockInputData.body.isSelectAll = true;
            const mockCompany = { id: 1, companyName: 'Test Company', isParent: false };
            const { Company, DeliverCompany, CraneRequestCompany, ConcreteRequestCompany, Member } = require('../../models');
            Company.findAll.mockResolvedValue([mockCompany]);
            Company.findByPk.mockResolvedValue(mockCompany);
            Company.update.mockResolvedValue([1]);
            DeliverCompany.findOne.mockResolvedValue(null);
            CraneRequestCompany.findOne.mockResolvedValue(null);
            ConcreteRequestCompany.findOne.mockResolvedValue(null);
            Member.findOne.mockResolvedValue(null);

            await companyService.deleteCompany(mockInputData, mockDone);

            expect(Company.findAll).toHaveBeenCalledWith({
                where: { ProjectId: mockInputData.body.ProjectId, isDeleted: false },
            });
            expect(mockDone).toHaveBeenCalledWith('success', false);
        });

        it('should handle crane request mapping', async () => {
            const mockCompany = { id: 1, companyName: 'Test Company', isParent: false };
            const { Company, DeliverCompany, CraneRequestCompany, ConcreteRequestCompany, Member } = require('../../models');
            Company.findAll.mockResolvedValue([mockCompany]);
            Company.findByPk.mockResolvedValue(mockCompany);
            DeliverCompany.findOne.mockResolvedValue(null);
            CraneRequestCompany.findOne.mockResolvedValue({ id: 1 });
            ConcreteRequestCompany.findOne.mockResolvedValue(null);
            Member.findOne.mockResolvedValue(null);

            await companyService.deleteCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, {
                message: expect.stringContaining('mapped to submitted bookings')
            });
        });

        it('should handle concrete request mapping', async () => {
            const mockCompany = { id: 1, companyName: 'Test Company', isParent: false };
            const { Company, DeliverCompany, CraneRequestCompany, ConcreteRequestCompany, Member } = require('../../models');
            Company.findAll.mockResolvedValue([mockCompany]);
            Company.findByPk.mockResolvedValue(mockCompany);
            DeliverCompany.findOne.mockResolvedValue(null);
            CraneRequestCompany.findOne.mockResolvedValue(null);
            ConcreteRequestCompany.findOne.mockResolvedValue({ id: 1 });
            Member.findOne.mockResolvedValue(null);

            await companyService.deleteCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, {
                message: expect.stringContaining('mapped to submitted bookings')
            });
        });
    });

    describe('addCompany - additional error scenarios', () => {
        it('should handle errors in addCompany', async () => {
            const error = new Error('Database error');
            const { Project } = require('../../models');
            Project.findByPk.mockRejectedValue(error);

            await companyService.addCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });

    describe('editCompany - additional error scenarios', () => {
        beforeEach(() => {
            mockInputData.body.id = 1;
            mockInputData.body.definableWorkId = [1, 2];
        });

        it('should handle errors in editCompany', async () => {
            const error = new Error('Database error');
            const { Project } = require('../../models');
            Project.findByPk.mockRejectedValue(error);

            await companyService.editCompany(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });
    });
});
