module.exports = (sequelize, DataTypes) => {
  const DeliverEquipment = sequelize.define(
    'DeliverEquipment',
    {
      DeliveryId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      DeliveryCode: DataTypes.INTEGER,
      EquipmentId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  DeliverEquipment.associate = (models) => {
    // associations can be defined here
    DeliverEquipment.belongsTo(models.DeliveryRequest, {
      as: 'deliveryrequest',
      foreignKey: 'DeliveryId',
    });
    DeliverEquipment.belongsTo(models.Equipments, {
      as: 'Equipments',
      foreignKey: 'EquipmentId',
    });
    DeliverEquipment.belongsTo(models.Equipments);
  };
  DeliverEquipment.createInstance = async (paramData) => {
    const newDeliverEquipment = await DeliverEquipment.create(paramData);
    return newDeliverEquipment;
  };
  return DeliverEquipment;
};
