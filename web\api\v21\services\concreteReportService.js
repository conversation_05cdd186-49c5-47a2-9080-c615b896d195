const { Sequelize, Enterprise } = require('../models');
let { Member, VoidList, User } = require('../models');
const moment = require('moment');
const { ConcreteRequest } = require('../models');
const exportService = require('./exportService');
const pdfConcreteReportService = require('./pdfConcreteReportService');
const csvConcreteReportService = require('./csvConcreteReportService');
const excelConcreteReportService = require('./excelConcreteReportService');
const awsConfig = require('../middlewares/awsConfig');
const deliveryReportService = require('./deliveryreportService');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const helper = require('../helpers/domainHelper');

const concreteReportRequestService = {
  async getDomainFromUserData(email) {
    if (!email) return null;
    const userData = await publicUser.findOne({ where: { email } });
    return userData;
  },

  async getDomainFromMemberData(userData, ParentCompanyId) {
    if (!userData) {
      return this.getEnterpriseName(ParentCompanyId);
    }

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });

    if (!memberData?.isAccount) { // Refactored line using optional chaining
      return this.getEnterpriseName(ParentCompanyId);
    }

    return this.getEnterpriseNameById(memberData.EnterpriseId);
  },

  async getEnterpriseName(ParentCompanyId) {
    const enterpriseValue = await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async getEnterpriseNameById(EnterpriseId) {
    const enterpriseValue = await Enterprise.findOne({
      where: { id: EnterpriseId, status: 'completed' },
    });
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async resolveDomainName(domainName, ParentCompanyId, email) {
    if (domainName) {
      return this.verifyDomainName(domainName);
    }

    if (!ParentCompanyId || ParentCompanyId === 'undefined') {
      return '';
    }

    const userData = await this.getDomainFromUserData(email);
    return this.getDomainFromMemberData(userData, ParentCompanyId);
  },

  async verifyDomainName(domainName) {
    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });
    return domainEnterpriseValue ? domainName : '';
  },

  async updateUserData(inputData, resolvedDomainName) {
    if (!resolvedDomainName) return inputData;

    const newUser = await User.findOne({ where: { email: inputData.user.email } });
    if (newUser) {
      inputData.user = newUser;
    }
    return inputData;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;

    const resolvedDomainName = await this.resolveDomainName(
      domainName,
      ParentCompanyId,
      inputData.user.email
    );

    const modelObj = await helper.getDynamicModel(resolvedDomainName);
    Member = modelObj.Member;
    User = modelObj.User;
    VoidList = modelObj.VoidList;

    await this.updateUserData(inputData, resolvedDomainName);
    return inputData.body.ProjectId;
  },

  async buildConcreteConditions(params, incomeData, voidConcreteDelivery) {
    const concreteCondition = {
      ProjectId: +params.ProjectId,
      isDeleted: false,
    };
    this.addFilterConditions(concreteCondition, incomeData, voidConcreteDelivery, params);
    return concreteCondition;
  },

  addFilterConditions(concreteCondition, incomeData, voidConcreteDelivery, params) {
    if (incomeData.statusFilter.length) {
      concreteCondition.status = { [Op.in]: incomeData.statusFilter };
    }

    if (incomeData.locationFilter.length) {
      concreteCondition['$location.id$'] = { [Op.in]: incomeData.locationFilter };
    }

    concreteCondition['$ConcreteRequest.id$'] = {
      [Op.and]: [{
        [(params.void === 0 ? Op.notIn : Op.in)]: voidConcreteDelivery
      }],
    };

    this.addSpecificFilters(concreteCondition, incomeData);
  },

  addSpecificFilters(concreteCondition, incomeData) {
    if (incomeData.idFilter) {
      concreteCondition.ConcreteRequestId = incomeData.idFilter;
    }
    if (incomeData.quantityFilter) {
      concreteCondition.concreteQuantityOrdered = incomeData.quantityFilter;
    }
    if (incomeData.slumpFilter) {
      concreteCondition.slump = incomeData.slumpFilter;
    }
    if (incomeData.truckspacingFilter) {
      concreteCondition.truckSpacingHours = incomeData.truckspacingFilter;
    }
    if (incomeData.primerFilter) {
      concreteCondition.primerForPump = incomeData.primerFilter;
    }
  },

  async getVoidConcreteDeliveryList(ProjectId) {
    const voidConcreteDelivery = [];
    const voidConcreteRequestList = await VoidList.findAll({
      where: {
        ProjectId,
        ConcreteRequestId: { [Op.ne]: null },
      },
    });
    voidConcreteRequestList.forEach(element => {
      voidConcreteDelivery.push(element.ConcreteRequestId);
    });
    return voidConcreteDelivery;
  },

  async validateMemberAccess(loginUser, ProjectId) {
    const memberDetails = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId,
        isDeleted: false,
        isActive: true,
      }),
    });

    if (!memberDetails) {
      throw new Error('Project Id/Member does not exist');
    }

    return memberDetails;
  },

  async processConcreteRequestResults(inputData, getConcreteRequest, params) {
    const newResult = { count: 0, rows: [] };

    if (inputData.body.exportType) {
      newResult.rows = getConcreteRequest;
    } else {
      const offset = (+params.pageNo - 1) * +params.pageSize;
      newResult.rows = getConcreteRequest.slice(offset, offset + +params.pageSize);
    }

    newResult.count = getConcreteRequest.length;
    return newResult;
  },

  async buildSearchConditions(search) {
    if (!search) return {};

    return {
      [Op.and]: [{
        [Op.or]: [
          { description: { [Sequelize.Op.iLike]: `%${search}%` } },
          { concreteOrderNumber: { [Sequelize.Op.iLike]: `%${search}%` } },
          { slump: { [Sequelize.Op.iLike]: `%${search}%` } },
          { primerForPump: { [Sequelize.Op.iLike]: `%${search}%` } },
          { status: { [Sequelize.Op.iLike]: `%${search}%` } },
          { pumpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
          { cubicYardsTotal: { [Sequelize.Op.iLike]: `%${search}%` } },
          { concreteQuantityOrdered: { [Sequelize.Op.iLike]: `%${search}%` } },
          { truckSpacingHours: { [Sequelize.Op.iLike]: `%${search}%` } },
          { notes: { [Sequelize.Op.iLike]: `%${search}%` } },
          { hoursToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
          { minutesToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
          { '$location.locationPath$': { [Sequelize.Op.iLike]: `%${search}%` } },
          { '$mixDesignDetails.ConcreteMixDesign.mixDesign$': { [Sequelize.Op.iLike]: `%${search}%` } },
          { '$pumpSizeDetails.ConcretePumpSize.pumpSize$': { [Sequelize.Op.iLike]: `%${search}%` } },
          { '$concreteSupplierDetails.Company.companyName$': { [Sequelize.Op.iLike]: `%${search}%` } },
        ],
      }],
    };
  },

  async buildDateConditions(startdate, enddate, timezoneoffset) {
    if (!startdate) {
      const startDateTime = moment(new Date(), 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      return {
        concretePlacementStart: {
          [Op.gte]: moment(startDateTime),
        },
      };
    }

    const startDateTime = moment(startdate, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(enddate, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);

    return {
      concretePlacementStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };
  },

  async buildOrderQuery(sortByFieldName, sortByColumnType) {
    const orderMap = {
      member: [['memberDetails', 'Member', 'User', 'firstName', sortByColumnType]],
      company: [['concreteSupplierDetails', 'Company', 'companyName', sortByColumnType]],
      approvedUser: [['approverDetails', 'User', 'firstName', sortByColumnType]],
      pumpsize: [['pumpSizeDetails', 'ConcretePumpSize', 'pumpSize', sortByColumnType]],
      mixDesign: [['mixDesignDetails', 'ConcreteMixDesign', 'mixDesign', sortByColumnType]],
    };

    if (orderMap[sortByFieldName]) {
      return orderMap[sortByFieldName];
    }

    const directFields = [
      'description', 'id', 'status', 'slump', 'concreteOrderNumber',
      'truckSpacingHours', 'primerForPump', 'concreteQuantityOrdered',
      'concretePlacementStart'
    ];

    if (directFields.includes(sortByFieldName)) {
      return [[sortByFieldName, sortByColumnType]];
    }

    return null;
  },

  async getAllConcreteRequest({ attr, filters, sortConfig, timezoneoffset }) {
    const {
      descriptionFilter,
      locationFilter,
      concreteSupplierFilter,
      orderNumberFilter,
      statusFilter,
      mixDesignFilter,
      startdate,
      enddate,
      memberFilter,
      search,
      locationPathFilter
    } = filters;

    const { sort, sortByField } = sortConfig;
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sort || 'DESC';
    const orderQuery = await this.buildOrderQuery(sortByFieldName, sortByColumnType);

    this.applyFilters(attr, {
      descriptionFilter,
      locationFilter,
      locationPathFilter,
      dateConditions: await this.buildDateConditions(startdate, enddate, timezoneoffset),
      concreteSupplierFilter,
      orderNumberFilter,
      statusFilter,
      memberFilter,
      mixDesignFilter,
      searchConditions: await this.buildSearchConditions(search)
    });

    return await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        this.getMemberDetails(),
        this.getProjectDetails(),
        this.getCreatedUserDetails(),
        this.getLocationDetails(),
        this.getMixDesignDetails(),
        this.getPumpSizeDetails(),
        this.getConcreteSupplierDetails(),
        this.getVoidListDetails(),
        this.getApproverDetails(),
        this.getLocationAttrs()
      ],
      where: {
        ...attr,
        isDeleted: false,
      },
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
      ],
      order: orderQuery,
    });
  },

  applyFilters(attr, filters) {
    const {
      descriptionFilter,
      locationFilter,
      locationPathFilter,
      dateConditions,
      concreteSupplierFilter,
      orderNumberFilter,
      statusFilter,
      memberFilter,
      mixDesignFilter,
      searchConditions
    } = filters;

    if (descriptionFilter) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }],
      });
    }

    if (locationFilter?.length) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ '$location.id$': { [Op.in]: locationFilter } }],
      });
    }

    if (locationPathFilter) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ '$location.locationPath$': { [Sequelize.Op.iLike]: locationPathFilter } }],
      });
    }

    if (concreteSupplierFilter?.length) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ '$concreteSupplierDetails.Company.id$': { [Op.in]: concreteSupplierFilter } }],
      });
    }

    if (orderNumberFilter) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ concreteOrderNumber: { [Sequelize.Op.iLike]: `%${orderNumberFilter}%` } }],
      });
    }

    if (statusFilter?.length) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ status: statusFilter }],
      });
    }

    if (memberFilter > 0) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ '$memberDetails.Member.id$': +memberFilter }],
      });
    }

    if (mixDesignFilter) {
      attr[Op.and] = (attr[Op.and] || []).concat({
        [Op.or]: [{ '$mixDesignDetails.ConcreteMixDesign.mixDesign$': { [Sequelize.Op.iLike]: `%${mixDesignFilter}%` } }],
      });
    }

    Object.assign(attr, dateConditions, searchConditions);
  },

  getMemberDetails() {
    return {
      required: false,
      association: 'memberDetails',
      where: { isDeleted: false, isActive: true },
      attributes: ['id'],
      include: [
        {
          association: 'Member',
          where: { isDeleted: false },
          attributes: ['id', 'UserId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
        },
      ],
    };
  },

  getProjectDetails() {
    return {
      association: 'Project',
      where: { isDeleted: false },
      attributes: ['id', 'projectName'],
    };
  },

  getCreatedUserDetails() {
    return {
      association: 'createdUserDetails',
      required: false,
      attributes: ['id', 'RoleId'],
      include: [
        {
          association: 'User',
          where: { isDeleted: false },
          attributes: ['email', 'id', 'firstName', 'lastName'],
        },
      ],
    };
  },

  getLocationDetails() {
    return {
      association: 'locationDetails',
      where: { isDeleted: false },
      required: false,
      attributes: ['id'],
      include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
    };
  },

  getMixDesignDetails() {
    return {
      association: 'mixDesignDetails',
      where: { isDeleted: false },
      required: false,
      attributes: ['id'],
      include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
    };
  },

  getPumpSizeDetails() {
    return {
      association: 'pumpSizeDetails',
      where: { isDeleted: false },
      required: false,
      attributes: ['id'],
      include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
    };
  },

  getConcreteSupplierDetails() {
    return {
      association: 'concreteSupplierDetails',
      where: { isDeleted: false },
      required: false,
      attributes: ['id'],
      include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
    };
  },

  getVoidListDetails() {
    return {
      association: 'voidList',
      attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId'],
    };
  },

  getApproverDetails() {
    return {
      association: 'approverDetails',
      attributes: ['id'],
      include: [
        {
          association: 'User',
          attributes: ['email', 'firstName', 'lastName'],
        },
      ],
    };
  },

  getLocationAttrs() {
    return {
      association: 'location',
      required: false,
      attributes: ['id', 'locationPath'],
    };
  },

  async fetchConcreteRequestData(inputData, concreteCondition, memberDetails) {
    return await this.getAllConcreteRequest({
      attr: concreteCondition,
      filters: {
        descriptionFilter: inputData.body?.descriptionFilter,
        locationFilter: inputData.body?.locationFilter,
        concreteSupplierFilter: inputData.body?.concreteSupplierFilter,
        orderNumberFilter: inputData.body?.orderNumberFilter,
        statusFilter: inputData.body?.statusFilter,
        mixDesignFilter: inputData.body?.mixDesignFilter,
        startdate: inputData.body?.startdate,
        enddate: inputData.body?.enddate,
        memberFilter: inputData.body?.memberFilter,
        search: inputData.body?.search,
        locationPathFilter: inputData.body?.locationPathFilter,
      },
      sortConfig: {
        sort: inputData.body?.sort,
        sortByField: inputData.body?.sortByField,
      },
      timezoneoffset: inputData.headers.timezoneoffset,
    });
  },

  async handlePdfExport(req, response) {
    const loginUser = req.user;
    return new Promise((resolve, reject) => {
      pdfConcreteReportService.pdfFormatOfConcreteRequest(
        req.params,
        loginUser,
        response.rows,
        req,
        (pdfFile, err) => {
          if (err) reject(new Error(err.message || 'Failed to generate PDF'));
          else resolve(pdfFile);
        }
      );
    });
  },

  async handleExcelExport(req, response) {
    const workbook = await exportService.createWorkbook();
    const reportWorkbook = await excelConcreteReportService.concreteReport(
      workbook,
      response.rows,
      req.body.selectedHeaders,
      req.headers.timezoneoffset,
    );

    if (!reportWorkbook) {
      throw new Error('Failed to generate Excel report');
    }

    const buffer = await reportWorkbook.xlsx.writeBuffer();
    const excelFile = await deliveryReportService.saveExcelReport(
      buffer,
      req.body.reportName,
      req.body.exportType,
    );

    if (!excelFile) {
      throw new Error('Failed to create saved reports');
    }

    req.body.reportType = 'Concrete';
    await deliveryReportService.createSavedReports(req, excelFile);
    return excelFile;
  },

  async handleCsvExport(req, response) {
    return new Promise((resolve, reject) => {
      csvConcreteReportService.exportConcreteReportInCsvFormat(
        response.rows,
        req.body.selectedHeaders,
        req.headers.timezoneoffset,
        req.body.reportName,
        req.body.exportType,
        (csvFile, err) => {
          if (err) reject(new Error(err.message || 'Failed to generate CSV'));
          else resolve(csvFile);
        }
      );
    });
  },

  async saveReportIfNeeded(req, result, exportType) {
    if (!req.body.saved || exportType === 'EXCEL') {
      return result;
    }

    req.body.reportType = 'Concrete';
    const savedData = await deliveryReportService.createSavedReports(req, result);
    if (!savedData) {
      throw new Error('Failed to create saved reports');
    }
    return result;
  },

  async handleExportResponse(req, response, done) {
    const exportHandlers = {
      PDF: () => this.handlePdfExport(req, response),
      EXCEL: () => this.handleExcelExport(req, response),
      CSV: () => this.handleCsvExport(req, response)
    };

    try {
      if (response.count === 0) {
        throw new Error('No Data Found');
      }

      const exportType = req.body.exportType;
      const handler = exportHandlers[exportType];

      if (!handler) {
        throw new Error(`Unsupported export type: ${exportType}`);
      }

      const result = await handler();
      const finalResult = await this.saveReportIfNeeded(req, result, exportType);
      return done(finalResult, false);
    } catch (error) {
      return done(null, error);
    }
  },

  async listConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;

      if (params.void < 0 || params.void > 1) {
        return done(null, { message: 'Please enter void as 1 or 0' });
      }

      const memberDetails = await this.validateMemberAccess(inputData.user, params.ProjectId);
      const voidConcreteDelivery = await this.getVoidConcreteDeliveryList(params.ProjectId);
      const concreteCondition = await this.buildConcreteConditions(params, inputData.body, voidConcreteDelivery);

      const getConcreteRequest = await this.fetchConcreteRequestData(inputData, concreteCondition, memberDetails);
      const result = await this.processConcreteRequestResults(inputData, getConcreteRequest, params);

      if (inputData.body.exportType) {
        return this.handleExportResponse(inputData, result, done);
      }

      done(result, false);
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async exportReportForScheduler(req) {
    return new Promise((res, rej) => {
      this.listConcreteRequest(req, async (response, error) => {
        if (error) {
          return rej(new Error(error.message || 'Failed to process report request'));
        }

        try {
          if (response.count === 0) {
            return rej(new Error('No Data Found'));
          }

          let exportResult;
          if (req.body.exportType === 'PDF') {
            exportResult = await this.handlePdfExport(req, response);
          } else if (req.body.exportType === 'EXCEL') {
            exportResult = await this.handleExcelExport(req, response);
          } else if (req.body.exportType === 'CSV') {
            exportResult = await this.handleCsvExport(req, response);
          }
          res(exportResult);
        } catch (err) {
          rej(new Error(err.message || 'Failed to upload report'));
        }
      });
    });
  },

  async exportReport(req, done) {
    this.listConcreteRequest(req, async (response, error) => {
      if (error) {
        return done(null, new Error(error.message || 'Failed to process report request'));
      }

      try {
        let exportResult;
        if (req.body.exportType === 'PDF') {
          exportResult = await this.handlePdfExport(req, response);
        } else if (req.body.exportType === 'EXCEL') {
          exportResult = await this.handleExcelExport(req, response);
        } else if (req.body.exportType === 'CSV') {
          exportResult = await this.handleCsvExport(req, response);
        }

        if (req.body.saved) {
          req.body.reportType = 'Concrete';
          const savedData = await deliveryReportService.createSavedReports(req, exportResult);
          if (!savedData) {
            throw new Error('Failed to create saved reports');
          }
        }

        done(exportResult, false);
      } catch (err) {
        done(null, new Error(err.message || 'Failed to generate report'));
      }
    });
  },
};

module.exports = concreteReportRequestService;