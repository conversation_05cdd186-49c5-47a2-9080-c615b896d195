const status = require('http-status');
const {
    Sequelize,
    BookingTemplates,
} = require('../models');
let { Project } = require('../models');
const ApiError = require('../helpers/apiError');
const { Op } = Sequelize;

const bookingTemplatesService = {
    async createTemplate(user, payload) {
        try {
            const userDetails = user;
            payload.createdBy = userDetails.id;
            const projectDetails = await Project.getProject({ id: payload.ProjectId });
            if (projectDetails) {
                const isNameExist = await BookingTemplates.findOne({
                    where: {
                        template_name: payload.template_name,
                        isDeleted: false,
                        ProjectId: payload.ProjectId,
                    }
                });
                if (isNameExist) {
                    throw new Error('Template Name Already exist.', status.BAD_REQUEST);
                } else {
                    const recurrenceObject = payload.recurrence;
                    const recurrenceString = typeof recurrenceObject === 'string' ? recurrenceObject : JSON.stringify(recurrenceObject);
                    payload.recurrence = recurrenceString;
                    const newTemplate = await BookingTemplates.createTempalte(payload);
                    return newTemplate
                }
            } else {
                throw new Error('Project does not exist.', status.BAD_REQUEST);
            }
        } catch (err) {
            throw Error(err);
        }
    },
    async updateTemplate(user, payload) {
        try {
            const userDetails = user;
            payload.createdBy = userDetails.id;
            const projectDetails = await Project.getProject({ id: payload.ProjectId });
            if (projectDetails) {
                const isNameExist = await BookingTemplates.findOne({
                    where: {
                        id: { [Op.in]: payload.id },
                        template_name: payload.template_name,
                        isDeleted: false,
                        ProjectId: payload.ProjectId,
                    }
                });
                if (isNameExist) {
                    throw new Error('Template Name Already exist.', status.BAD_REQUEST);
                } else {
                    const recurrenceObject = payload.recurrence;
                    const recurrenceString = typeof recurrenceObject === 'string' ? recurrenceObject : JSON.stringify(recurrenceObject);
                    payload.recurrence = recurrenceString;
                    const template = await BookingTemplates.updateInstance(payload.id, payload);
                    return template
                }
            } else {
                throw new Error('Project does not exist.', status.BAD_REQUEST);
            }
        } catch (err) {
            throw Error(err);
        }
    },
    async getTemplates(req) {
        try {
            const projectDetails = await Project.getProject({ id: req.params.ProjectId });
            if (projectDetails) {
                const { pageNo, pageSize, ParentCompanyId, sort, sortColumn } = req.query
                const attr = {
                    ProjectId: req.params.ProjectId,
                    ParentCompanyId: ParentCompanyId,
                    isDeleted: false,
                    createdBy: +req.user.id
                }
                if (req.query.isDropdown) {
                    const getTemplates = await BookingTemplates.getTemplates(
                        attr
                    );
                    return getTemplates
                } else {
                    const offset = (pageNo - 1) * pageSize;
                    const getTemplates = await BookingTemplates.get(
                        attr, pageSize, offset, sort, sortColumn
                    );
                    return getTemplates
                }
            } else {
                const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
                throw Error(err);
            }
        } catch (err) {
            throw Error(err);
        }
    },
    async deleteTemplate(req) {
        try {
            const projectDetails = await Project.getProject({ id: req.params.ProjectId });
            if (projectDetails) {
                if (req.body.selectAll) {
                    await BookingTemplates.update(
                        { isDeleted: true },
                        {
                            where: { ProjectId: +req.params.ProjectId, ParentCompanyId: req.body.ParentCompanyId, isDeleted: false, createdBy: +req.user.id },
                        },
                    );
                } else {
                    await BookingTemplates.update(
                        { isDeleted: true },
                        {
                            where: {
                                id: { [Op.in]: req.body.id },
                                ProjectId: +req.params.ProjectId, ParentCompanyId: req.body.ParentCompanyId, isDeleted: false
                            },
                        },
                    );
                }
            } else {
                throw new Error('Project does not exist.', status.BAD_REQUEST);
            }
        } catch (err) {
            throw Error(err);
        }
    },
    async getTemplate(req) {
        const projectDetails = await Project.getProject({ id: req.query.ProjectId });
        if (projectDetails) {
            const isTemplateExist = await BookingTemplates.findOne({
                where: Sequelize.and(
                    {
                        id: req.query.id,
                    },
                    { ProjectId: req.query.ProjectId },
                    { ParentCompanyId: req.query.ParentCompanyId },
                ),
            });
            if (isTemplateExist) {
                return isTemplateExist
            } else {
                throw new Error('Template does not exist.', status.BAD_REQUEST);
            }
        } else {
            throw new Error('Project does not exist.', status.BAD_REQUEST);
        }

    },
};
module.exports = bookingTemplatesService;