// Mock all external dependencies BEFORE importing the service (deliveryService pattern)

// Mock sequelize
const mockSequelize = {
  DataTypes: {
    STRING: 'STRING',
    INTEGER: 'INTEGER',
    BOOLEAN: 'BOOLEAN',
    DATE: 'DATE',
    TEXT: 'TEXT',
    DECIMAL: 'DECIMAL'
  },
  and: jest.fn(() => ({ and: 'condition' })),
  or: jest.fn(() => ({ or: 'condition' })),
  literal: jest.fn(() => 'LITERAL_RESULT'),
  Op: {
    and: Symbol('and'),
    or: Symbol('or'),
    eq: Symbol('eq'),
    ne: Symbol('ne'),
    in: Symbol('in'),
    notIn: Symbol('notIn'),
    like: Symbol('like'),
    notLike: Symbol('notLike'),
    gt: Symbol('gt'),
    gte: Symbol('gte'),
    lt: Symbol('lt'),
    lte: Symbol('lte')
  }
};

jest.mock('sequelize', () => mockSequelize);

// Mock the mailer
jest.mock('../../mailer', () => ({
  sendMail: jest.fn().mockResolvedValue(true)
}));

// Mock all models
const mockModels = {
  Sequelize: {
    Op: {
      and: 'and',
      or: 'or',
      in: 'in',
      notIn: 'notIn',
      between: 'between',
      gte: 'gte',
      lte: 'lte',
      ne: 'ne',
      like: 'like'
    }
  },
  Enterprise: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  TimeZone: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getProjectAndSettings: jest.fn()
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
    count: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  InspectionRequest: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    getAll: jest.fn(),
    getInspectionRequestData: jest.fn(),
    getSingleInspectionRequestData: jest.fn()
  },
  InspectionType: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  InspectionCriteria: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  InspectionLocation: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  InspectionRequestResponsiblePerson: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  InspectionRequestCompany: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  InspectionRequestHistory: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn()
  },
  InspectionScore: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  },
  Company: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  Role: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  RequestRecurrenceSeries: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn()
  },
  ProjectSettings: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Notification: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  }
};

// Mock external services
jest.mock('../../models', () => mockModels);

jest.mock('../../helpers/domainHelper', () => ({
  returnProjectModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  }),
  getDynamicModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  })
}));

jest.mock('../../helpers/notificationHelper', () => ({
  sendNotification: jest.fn()
}));

jest.mock('../../config/fcm', () => ({
  sendPushNotification: jest.fn()
}));

jest.mock('../voidService', () => ({
  checkVoidStatus: jest.fn()
}));

// Import the actual service AFTER all mocks are set up (deliveryService pattern)
const inspectionService = require('../inspectionService');

describe('InspectionService Comprehensive Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateTimeZone', () => {
    it('should validate timezone successfully', async () => {
      const mockTimeZone = {
        id: 1,
        location: 'America/New_York',
        isDayLightSavingEnabled: true,
        timeZoneOffsetInMinutes: -300,
        dayLightSavingTimeInMinutes: -240,
        timezone: 'America/New_York'
      };
      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);

      const result = await inspectionService.validateTimeZone(1);

      expect(result).toEqual(mockTimeZone);
    });

    it('should return null for invalid timezone', async () => {
      mockModels.TimeZone.findOne.mockResolvedValue(null);

      const result = await inspectionService.validateTimeZone(999);

      expect(result).toBeNull();
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully', async () => {
      const inputData = {
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockModelData = { Member: mockModels.Member, User: mockModels.User };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelData);

      await inspectionService.getDynamicModel(inputData);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalled();
      expect(require('../../helpers/domainHelper').getDynamicModel).toHaveBeenCalled();
    });
  });

  describe('checkInputDatas', () => {
    it('should validate input data successfully', async () => {
      const inputData = {
        body: {
          companies: [1, 2],
          responsiblePersons: [1, 2],
          inspectionTypeId: 1,
          inspectionCriteriaId: 1,
          locationId: 1,
          ProjectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.InspectionType.count.mockResolvedValue(1);
      mockModels.InspectionCriteria.count.mockResolvedValue(1);
      mockModels.InspectionLocation.count.mockResolvedValue(1);
      mockModels.Company.count.mockResolvedValue(2);

      const done = jest.fn();
      await inspectionService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should handle validation errors', async () => {
      const inputData = {
        body: {
          companies: [1, 2, 3],
          responsiblePersons: [1, 2],
          inspectionTypeId: 999, // Invalid inspection type
          ProjectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.InspectionType.count.mockResolvedValue(0); // Invalid inspection type
      mockModels.Company.count.mockResolvedValue(2);

      const done = jest.fn();
      await inspectionService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('newInspectionRequest', () => {
    it('should create new inspection request successfully', async () => {
      const inputData = {
        body: {
          description: 'Test inspection request',
          inspectionDate: '2024-08-01',
          startPicker: '09:00',
          endPicker: '11:00',
          recurrence: 'Does Not Repeat',
          TimeZoneId: 1,
          ProjectId: 1,
          ParentCompanyId: null,
          inspectionTypeId: 1,
          inspectionCriteriaId: 1,
          locationId: 1,
          companies: [1],
          responsiblePersons: [1]
        },
        params: { ParentCompanyId: null },
        user: {
          id: 1,
          domainName: 'example.com',
          firstName: 'John',
          lastName: 'Doe'
        }
      };

      // Setup comprehensive mocks
      const mockTimeZone = { id: 1, timezone: 'America/New_York' };
      const mockProject = {
        id: 1,
        ProjectSettings: {
          isAutoApprovalEnabled: false,
          inspectionWindowTime: 24,
          inspectionWindowTimeUnit: 'hours'
        }
      };
      const mockMember = {
        id: 1,
        ProjectId: 1,
        RoleId: 2,
        User: { id: 1, firstName: 'John', lastName: 'Doe' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);
      mockModels.Project.getProjectAndSettings.mockResolvedValue(mockProject);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(1);
      mockModels.InspectionType.count.mockResolvedValue(1);
      mockModels.InspectionCriteria.count.mockResolvedValue(1);
      mockModels.InspectionLocation.count.mockResolvedValue(1);
      mockModels.Company.count.mockResolvedValue(1);
      mockModels.InspectionRequest.findOne.mockResolvedValue({ InspectionRequestId: 15 });
      mockModels.InspectionRequest.createInstance.mockResolvedValue({ id: 1 });

      const done = jest.fn();
      await inspectionService.newInspectionRequest(inputData, done);

      expect(done).toHaveBeenCalled();
    });
  });

  describe('listInspectionRequest', () => {
    it('should list inspection requests successfully', async () => {
      const inputData = {
        params: {
          ProjectId: 1,
          ParentCompanyId: null,
          pageSize: 10,
          pageNumber: 1,
          void: 0
        },
        body: {
          ParentCompanyId: null
        },
        user: {
          id: 1,
          domainName: 'example.com'
        }
      };

      const mockInspectionRequests = [
        { id: 1, description: 'Inspection 1', status: 'Approved', score: 85 },
        { id: 2, description: 'Inspection 2', status: 'Pending', score: null }
      ];
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockMember = { id: 1, ProjectId: 1, RoleId: 2 };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.InspectionRequest.getAll.mockResolvedValue(mockInspectionRequests);

      const done = jest.fn();
      await inspectionService.listInspectionRequest(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array)
      }), false);
    });
  });

  describe('Utility Methods', () => {
    describe('calculateInspectionScore', () => {
      it('should calculate inspection score correctly', () => {
        const criteria = [
          { weight: 30, score: 90 },
          { weight: 40, score: 85 },
          { weight: 30, score: 80 }
        ];

        const result = inspectionService.calculateInspectionScore(criteria);

        // Expected: (30*90 + 40*85 + 30*80) / 100 = 85
        expect(result).toBe(85);
      });

      it('should handle empty criteria', () => {
        const result = inspectionService.calculateInspectionScore([]);

        expect(result).toBe(0);
      });
    });

    describe('validateInspectionCriteria', () => {
      it('should validate inspection criteria successfully', async () => {
        const criteriaId = 1;
        const mockCriteria = {
          id: 1,
          name: 'Safety Check',
          description: 'Safety inspection criteria',
          maxScore: 100
        };

        mockModels.InspectionCriteria.findOne.mockResolvedValue(mockCriteria);

        const result = await inspectionService.validateInspectionCriteria(criteriaId);

        expect(result).toEqual(mockCriteria);
      });

      it('should return null for invalid criteria', async () => {
        mockModels.InspectionCriteria.findOne.mockResolvedValue(null);

        const result = await inspectionService.validateInspectionCriteria(999);

        expect(result).toBeNull();
      });
    });
  });
});