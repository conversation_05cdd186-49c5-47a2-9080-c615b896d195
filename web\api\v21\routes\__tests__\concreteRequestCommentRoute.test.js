const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  concreteRequestCommentController: {
    createConcreteRequestComment: jest.fn(),
    getConcreteRequestComments1: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  concreteRequestCommentValidation: {
    createConcreteRequestComment: jest.fn(),
    getConcreteRequestComments: jest.fn(),
  },
}));

describe('concreteRequestCommentRoute', () => {
  let router;
  let concreteRequestCommentRoute;
  let concreteRequestCommentController;
  let passportConfig;
  let concreteRequestCommentValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    concreteRequestCommentRoute = require('../concreteRequestCommentRoute');
    const controllers = require('../../controllers');
    concreteRequestCommentController = controllers.concreteRequestCommentController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    concreteRequestCommentValidation = validations.concreteRequestCommentValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = concreteRequestCommentRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST route
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/create_concrete_request_comment',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        concreteRequestCommentController.createConcreteRequestComment,
      );

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_concrete_request_comments/:ConcreteRequestId/:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        concreteRequestCommentController.getConcreteRequestComments1,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(2);
      expect(validate).toHaveBeenCalledWith(
        concreteRequestCommentValidation.createConcreteRequestComment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        concreteRequestCommentValidation.getConcreteRequestComments,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = concreteRequestCommentRoute.router;
      const result2 = concreteRequestCommentRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication and validation middleware for all routes', () => {
      concreteRequestCommentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have validation and authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toHaveLength(4); // path + validation + auth + controller
      });
    });

    it('should configure routes in the correct order', () => {
      concreteRequestCommentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      expect(postCalls[0][0]).toBe('/create_concrete_request_comment');
      expect(getCalls[0][0]).toBe('/get_concrete_request_comments/:ConcreteRequestId/:ParentCompanyId/:ProjectId');
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof concreteRequestCommentRoute).toBe('object');
      expect(concreteRequestCommentRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestCommentRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestCommentRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
