const moment = require('moment');

const excelWeeklyCalendarService = {
  filterDataByDateRange(data, startRange, endRange, timezoneoffset, dateField) {
    return data.filter(object =>
      moment(moment(object[dateField]).add(Number(timezoneoffset), 'm')).isBetween(
        startRange, endRange, null, '[]'
      )
    );
  },

  formatDateTime(date, timezoneoffset) {
    return moment(date).add(Number(timezoneoffset), 'm').format('hh:mm A');
  },

  formatDate(date, timezoneoffset) {
    return moment(new Date(`${moment(date).format('MM/DD/YYYY')} ${moment(date).format('hh:mm a')}`))
      .add(Number(timezoneoffset), 'm')
      .format('MM/DD/YYYY');
  },

  formatApproverName(approverDetails) {
    return approverDetails?.User ? `${approverDetails.User.firstName} ${approverDetails.User.lastName}` : '-';
  },

  formatJoinedValues(details, key) {
    if (!details?.length) return '-';
    const values = details.map(detail => detail[key]?.name || detail[key]?.DFOW || detail[key]?.companyName || '-');
    return values.join(', ');
  },

  processRow(worksheet, row, data, cellRange, rowValues, timezoneoffset, dateField) {
    worksheet.getCell(`${cellRange[rowValues.indexOf('Description')]}${row}`).value = data.description;
    worksheet.getCell(`${cellRange[rowValues.indexOf('Time')]}${row}`).value =
      `${this.formatDateTime(data[dateField], timezoneoffset)} - ${this.formatDateTime(data[dateField.replace('Start', 'End')], timezoneoffset)}`;
    worksheet.getCell(`${cellRange[rowValues.indexOf('Date')]}${row}`).value = this.formatDate(data[dateField], timezoneoffset);
    worksheet.getCell(`${cellRange[rowValues.indexOf('Status')]}${row}`).value = data.status;
    worksheet.getCell(`${cellRange[rowValues.indexOf('Approved By')]}${row}`).value = this.formatApproverName(data.approverDetails);

    if (data.equipmentDetails) {
      worksheet.getCell(`${cellRange[rowValues.indexOf('Equipment')]}${row}`).value = this.formatJoinedValues(data.equipmentDetails, 'Equipment');
    }
    if (data.defineWorkDetails) {
      worksheet.getCell(`${cellRange[rowValues.indexOf('Definable Feature of Work')]}${row}`).value = this.formatJoinedValues(data.defineWorkDetails, 'DeliverDefineWork');
    }
    if (data.location) {
      worksheet.getCell(`${cellRange[rowValues.indexOf('Location')]}${row}`).value = data.location.locationPath || '-';
    }
  },

  processDeliveryData(deliveryArray, worksheet, rowValues, cellRange, timezoneoffset) {
    if (!deliveryArray?.length) return;
    for (let deliveryIndex = 1; deliveryIndex <= deliveryArray.length; deliveryIndex += 1) {
      worksheet.addRow();
      const row = deliveryIndex + 1;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Description')]}${row}`).value = deliveryArray[deliveryIndex - 1].description;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Time')]}${row}`).value = this.formatDeliveryTime(deliveryArray[deliveryIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Date')]}${row}`).value = this.formatDeliveryDate(deliveryArray[deliveryIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Status')]}${row}`).value = deliveryArray[deliveryIndex - 1].status;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Approved By')]}${row}`).value = this.formatApproverName(deliveryArray[deliveryIndex - 1].approverDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Equipment')]}${row}`).value = this.formatEquipmentDetails(deliveryArray[deliveryIndex - 1].equipmentDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Definable Feature of Work')]}${row}`).value = this.formatDfowDetails(deliveryArray[deliveryIndex - 1].defineWorkDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Gate')]}${row}`).value = deliveryArray[deliveryIndex - 1].gateDetails?.[0]?.Gate?.gateName ?? '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Company')]}${row}`).value = this.formatCompanyDetails(deliveryArray[deliveryIndex - 1].companyDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Person')]}${row}`).value = this.formatMemberDetails(deliveryArray[deliveryIndex - 1].memberDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Location')]}${row}`).value = deliveryArray[deliveryIndex - 1].location?.locationPath ?? '-';
    }
  },

  processCraneData(craneArray, worksheet, rowValues, cellRange, timezoneoffset) {
    if (!craneArray?.length) return;
    for (let craneIndex = 1; craneIndex <= craneArray.length; craneIndex += 1) {
      worksheet.addRow();
      const row = craneIndex + 1;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Description')]}${row}`).value = craneArray[craneIndex - 1].description;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Time')]}${row}`).value = this.formatCraneTime(craneArray[craneIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Date')]}${row}`).value = this.formatCraneDate(craneArray[craneIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Status')]}${row}`).value = craneArray[craneIndex - 1].status;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Approved By')]}${row}`).value = this.formatApproverName(craneArray[craneIndex - 1].approverDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Equipment')]}${row}`).value = craneArray[craneIndex - 1].equipmentDetails?.[0]?.Equipment?.equipmentName ?? '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Definable Feature of Work')]}${row}`).value = this.formatDfowDetails(craneArray[craneIndex - 1].defineWorkDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Company')]}${row}`).value = this.formatCompanyDetails(craneArray[craneIndex - 1].companyDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Person')]}${row}`).value = this.formatMemberDetails(craneArray[craneIndex - 1].memberDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Picking From')]}${row}`).value = this.formatPickingFrom(craneArray[craneIndex - 1]);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Picking To')]}${row}`).value = this.formatPickingTo(craneArray[craneIndex - 1]);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Location')]}${row}`).value = craneArray[craneIndex - 1].location?.locationPath ?? '-';
    }
  },

  processConcreteData(concreteArray, worksheet, rowValues, cellRange, timezoneoffset) {
    if (!concreteArray?.length) return;
    for (let concreteIndex = 1; concreteIndex <= concreteArray.length; concreteIndex += 1) {
      worksheet.addRow();
      const row = concreteIndex + 1;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Description')]}${row}`).value = concreteArray[concreteIndex - 1].description;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Time')]}${row}`).value = this.formatConcreteTime(concreteArray[concreteIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Date')]}${row}`).value = this.formatConcreteDate(concreteArray[concreteIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Status')]}${row}`).value = concreteArray[concreteIndex - 1].status;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Approved By')]}${row}`).value = this.formatApproverName(concreteArray[concreteIndex - 1].approverDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Concrete Supplier')]}${row}`).value = this.formatConcreteSupplier(concreteArray[concreteIndex - 1].concreteSupplierDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Order Number')]}${row}`).value = concreteArray[concreteIndex - 1].concreteOrderNumber || '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Slump')]}${row}`).value = concreteArray[concreteIndex - 1].slump || '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Truck Spacing')]}${row}`).value = concreteArray[concreteIndex - 1].truckSpacingHours || '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Primer Ordered')]}${row}`).value = concreteArray[concreteIndex - 1].primerForPump || '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Person')]}${row}`).value = this.formatMemberDetails(concreteArray[concreteIndex - 1].memberDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Quantity Ordered')]}${row}`).value = concreteArray[concreteIndex - 1].concreteQuantityOrdered || '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Location')]}${row}`).value = concreteArray[concreteIndex - 1].location?.locationPath ?? '-';
    }
  },

  processCalendarData(calendarEvents, worksheet, rowValues, cellRange, timezoneoffset) {
    if (!calendarEvents?.length) return;
    for (let eventIndex = 1; eventIndex <= calendarEvents.length; eventIndex += 1) {
      worksheet.addRow();
      const row = eventIndex + 1;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Description')]}${row}`).value = calendarEvents[eventIndex - 1].description;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Time')]}${row}`).value = this.formatCalendarTime(calendarEvents[eventIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Date')]}${row}`).value = this.formatCalendarDate(calendarEvents[eventIndex - 1], timezoneoffset);
    }
  },

  processInspectionData(inspectionArray, worksheet, rowValues, cellRange, timezoneoffset) {
    if (!inspectionArray?.length) return;
    for (let inspectionIndex = 1; inspectionIndex <= inspectionArray.length; inspectionIndex += 1) {
      worksheet.addRow();
      const row = inspectionIndex + 1;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Description')]}${row}`).value = inspectionArray[inspectionIndex - 1].description;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Time')]}${row}`).value = this.formatInspectionTime(inspectionArray[inspectionIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Date')]}${row}`).value = this.formatInspectionDate(inspectionArray[inspectionIndex - 1], timezoneoffset);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Status')]}${row}`).value = inspectionArray[inspectionIndex - 1].status === 'Delivered' ? 'Completed' : inspectionArray[inspectionIndex - 1].status;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Inspection Status')]}${row}`).value = inspectionArray[inspectionIndex - 1].inspectionStatus || '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Inspection Type')]}${row}`).value = inspectionArray[inspectionIndex - 1].inspectionType;
      worksheet.getCell(`${cellRange[rowValues.indexOf('Approved By')]}${row}`).value = this.formatApproverName(inspectionArray[inspectionIndex - 1].approverDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Equipment')]}${row}`).value = this.formatEquipmentDetails(inspectionArray[inspectionIndex - 1].equipmentDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Definable Feature of Work')]}${row}`).value = this.formatDfowDetails(inspectionArray[inspectionIndex - 1].defineWorkDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Gate')]}${row}`).value = inspectionArray[inspectionIndex - 1].gateDetails?.[0]?.Gate?.gateName ?? '-';
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Company')]}${row}`).value = this.formatCompanyDetails(inspectionArray[inspectionIndex - 1].companyDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Responsible Person')]}${row}`).value = this.formatMemberDetails(inspectionArray[inspectionIndex - 1].memberDetails);
      worksheet.getCell(`${cellRange[rowValues.indexOf('Location')]}${row}`).value = inspectionArray[inspectionIndex - 1].location?.locationPath ?? '-';
    }
  },

  // Helper functions for formatting
  formatDeliveryTime(delivery, timezoneoffset) {
    const fromTime = moment(delivery.deliveryStart).add(Number(timezoneoffset), 'm').format('hh:mm A');
    const toTime = moment(delivery.deliveryEnd).add(Number(timezoneoffset), 'm').format('hh:mm A');
    return `${fromTime} - ${toTime}`;
  },

  formatDeliveryDate(delivery, timezoneoffset) {
    return moment(new Date(`${moment(delivery.deliveryStart).format('MM/DD/YYYY')} ${moment(delivery.deliveryStart).format('hh:mm a')}`))
      .add(Number(timezoneoffset), 'm')
      .format('MM/DD/YYYY');
  },

  formatCraneTime(crane, timezoneoffset) {
    const fromTime = moment(crane.craneDeliveryStart).add(Number(timezoneoffset), 'm').format('hh:mm A');
    const toTime = moment(crane.craneDeliveryEnd).add(Number(timezoneoffset), 'm').format('hh:mm A');
    return `${fromTime} - ${toTime}`;
  },

  formatCraneDate(crane, timezoneoffset) {
    return moment(new Date(`${moment(crane.craneDeliveryStart).format('MM/DD/YYYY')} ${moment(crane.craneDeliveryStart).format('hh:mm a')}`))
      .add(Number(timezoneoffset), 'm')
      .format('MM/DD/YYYY');
  },

  formatConcreteTime(concrete, timezoneoffset) {
    const fromTime = moment(concrete.concretePlacementStart).add(Number(timezoneoffset), 'm').format('hh:mm A');
    const toTime = moment(concrete.concretePlacementEnd).add(Number(timezoneoffset), 'm').format('hh:mm A');
    return `${fromTime} - ${toTime}`;
  },

  formatConcreteDate(concrete, timezoneoffset) {
    return moment(new Date(`${moment(concrete.concretePlacementStart).format('MM/DD/YYYY')} ${moment(concrete.concretePlacementStart).format('hh:mm a')}`))
      .add(Number(timezoneoffset), 'm')
      .format('MM/DD/YYYY');
  },

  formatCalendarTime(calendar, timezoneoffset) {
    const fromTime = moment(calendar.fromDate).add(Number(timezoneoffset), 'm').format('hh:mm A');
    const toTime = moment(calendar.toDate).add(Number(timezoneoffset), 'm').format('hh:mm A');
    return `${fromTime} - ${toTime}`;
  },

  formatCalendarDate(calendar, timezoneoffset) {
    return moment(new Date(`${moment(calendar.fromDate).format('MM/DD/YYYY')} ${moment(calendar.fromDate).format('hh:mm a')}`))
      .add(Number(timezoneoffset), 'm')
      .format('MM/DD/YYYY');
  },

  formatInspectionTime(inspection, timezoneoffset) {
    const fromTime = moment(inspection.inspectionStart).add(Number(timezoneoffset), 'm').format('hh:mm A');
    const toTime = moment(inspection.inspectionEnd).add(Number(timezoneoffset), 'm').format('hh:mm A');
    return `${fromTime} - ${toTime}`;
  },

  formatInspectionDate(inspection, timezoneoffset) {
    return moment(new Date(`${moment(inspection.inspectionStart).format('MM/DD/YYYY')} ${moment(inspection.inspectionStart).format('hh:mm a')}`))
      .add(Number(timezoneoffset), 'm')
      .format('MM/DD/YYYY');
  },

  formatEquipmentDetails(equipmentDetails) {
    if (!equipmentDetails?.length) return '-';
    const values = equipmentDetails.map(equipment => equipment?.Equipment?.equipmentName || '-');
    return values.join(', ');
  },

  formatDfowDetails(defineWorkDetails) {
    if (!defineWorkDetails?.length) return '-';
    const values = defineWorkDetails.map(workDetail => workDetail?.DeliverDefineWork?.DFOW || '-');
    return values.join(', ');
  },

  formatCompanyDetails(companyDetails) {
    if (!companyDetails?.length) return '-';
    const values = companyDetails.map(company => company?.Company?.companyName || '-');
    return values.join(', ');
  },

  formatMemberDetails(memberDetails) {
    if (!memberDetails?.length) return '-';
    const values = memberDetails.map(member =>
      member?.Member?.User ? `${member.Member.User.firstName} ${member.Member.User.lastName}` : '-'
    );
    return values.join(', ');
  },

  formatPickingFrom(crane) {
    return crane.requestType === 'craneRequest' ? crane.pickUpLocation : '-';
  },

  formatPickingTo(crane) {
    return crane.requestType === 'craneRequest' ? crane.dropOffLocation : '-';
  },

  formatConcreteSupplier(supplierDetails) {
    if (!supplierDetails?.length) return '-';
    const values = supplierDetails.map(supplier => supplier?.Company?.companyName || '-');
    return values.join(', ');
  },

  async weeklyCalendarReport(
    finalArray,
    weekStartDate,
    weekEndDate,
    chosenDateRangeFilter,
    workbook,
    responseData,
    timezoneoffset,
  ) {
    const worksheet = workbook.addWorksheet('Delivery Report');
    const worksheet1 = workbook.addWorksheet('Crane Report');
    const worksheet2 = workbook.addWorksheet('Concrete Report');
    const worksheet3 = workbook.addWorksheet('Calendar Events');
    const worksheet4 = workbook.addWorksheet('Inspection Report');
    /* Note */
    /* Column headers */
    const rowValues = [];
    const columns = [];
    const rowValuesCrane = [];
    const columnsCrane = [];
    const rowValuesConcrete = [];
    const columnsConcrete = [];
    const rowValuesEvent = [];
    const columnsEvent = [];
    const rowValuesInspection = [];
    const columnsInspection = [];

    rowValues.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Approved By',
      'Equipment',
      'Definable Feature of Work',
      'Gate',
      'Responsible Company',
      'Responsible Person',
      'Location',
    );
    columns.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'equipment', width: 32 },
      { key: 'dfow', width: 32 },
      { key: 'gate', width: 32 },
      { key: 'company', width: 32 },
      { key: 'name', width: 32 },
      { key: 'location', width: 32 },
    );
    rowValuesCrane.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Approved By',
      'Equipment',
      'Definable Feature of Work',
      'Responsible Company',
      'Responsible Person',
      'Picking From',
      'Picking To',
      'Location',
    );
    columnsCrane.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'equipment', width: 32 },
      { key: 'dfow', width: 32 },
      { key: 'company', width: 32 },
      { key: 'name', width: 32 },
      { key: 'pickingFrom', width: 32 },
      { key: 'pickingTo', width: 32 },
      { key: 'location', width: 32 },
    );
    rowValuesConcrete.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Approved By',
      'Concrete Supplier',
      'Order Number',
      'Slump',
      'Truck Spacing',
      'Primer Ordered',
      'Responsible Person',
      'Quantity Ordered',
      'Location',
    );
    columnsConcrete.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'company', width: 32 },
      { key: 'orderNumber', width: 32 },
      { key: 'slump', width: 32 },
      { key: 'truckSpacing', width: 32 },
      { key: 'primer', width: 32 },
      { key: 'name', width: 32 },
      { key: 'quantity', width: 32 },
      { key: 'location', width: 32 },
    );
    rowValuesEvent.push('Description', 'Time', 'Date');
    columnsEvent.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
    );
    rowValuesInspection.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Inspection Status',
      'Inspection Type',
      'Approved By',
      'Equipment',
      'Definable Feature of Work',
      'Gate',
      'Responsible Company',
      'Responsible Person',
      'Location',
    );
    columnsInspection.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'inspectionStatus', width: 32 },
      { key: 'inspectionType', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'equipment', width: 32 },
      { key: 'dfow', width: 32 },
      { key: 'gate', width: 32 },
      { key: 'company', width: 32 },
      { key: 'name', width: 32 },
      { key: 'location', width: 32 },
    );
    worksheet.getRow(1).values = rowValues;
    worksheet.columns = columns;
    worksheet1.getRow(1).values = rowValuesCrane;
    worksheet1.columns = columnsCrane;
    worksheet2.getRow(1).values = rowValuesConcrete;
    worksheet2.columns = columnsConcrete;
    worksheet3.getRow(1).values = rowValuesEvent;
    worksheet3.columns = columnsEvent;
    worksheet4.getRow(1).values = rowValuesInspection;
    worksheet4.columns = columnsInspection;
    const worksheetMap = {
      delivery: worksheet,
      crane: worksheet1,
      concrete: worksheet2,
      calendar: worksheet3,
      inspection: worksheet4
    };
    const rowValuesMap = {
      delivery: rowValues,
      crane: rowValuesCrane,
      concrete: rowValuesConcrete,
      calendar: rowValuesEvent,
      inspection: rowValuesInspection
    };
    const cellRange = {
      0: 'A',
      1: 'B',
      2: 'C',
      3: 'D',
      4: 'E',
      5: 'F',
      6: 'G',
      7: 'H',
      8: 'I',
      9: 'J',
      10: 'K',
      11: 'L',
      12: 'M',
      13: 'N',
      14: 'O',
    };
    let startRange = moment(weekStartDate);
    let endRange = moment(weekEndDate);
    if (!chosenDateRangeFilter) {
      startRange = moment(moment(weekStartDate, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
      endRange = moment(moment(weekEndDate, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
    }
    if (responseData && responseData.length > 0) {
      const filteredData = {
        delivery: responseData.filter(r => r.requestType === 'deliveryRequest' || r.requestType === 'deliveryRequestWithCrane'),
        crane: finalArray.includes('Crane') ? responseData.filter(r => r.requestType === 'craneRequest' || r.requestType === 'deliveryRequestWithCrane') : [],
        concrete: responseData.filter(r => r.requestType === 'concreteRequest'),
        calendar: responseData.filter(r => r.requestType === 'calendarEvent'),
        inspection: responseData.filter(r => r.requestType === 'inspectionRequest' || r.requestType === 'inspectionRequestWithCrane')
      };

      const dateFields = {
        delivery: 'deliveryStart',
        crane: 'craneDeliveryStart',
        concrete: 'concretePlacementStart',
        calendar: 'fromDate',
        inspection: 'inspectionStart'
      };

      Object.keys(filteredData).forEach(type => {
        const filtered = this.filterDataByDateRange(filteredData[type], startRange, endRange, timezoneoffset, dateFields[type]);
        if (filtered.length) {
          filtered.forEach((item, index) => {
            worksheetMap[type].addRow();
            this.processRow(worksheetMap[type], index + 2, item, cellRange, rowValuesMap[type], timezoneoffset, dateFields[type]);
          });
        }
      });
    }
    return workbook;
  },
};
module.exports = excelWeeklyCalendarService;