const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../middlewares/awsConfig');

const csvCraneReportService = {
  async exportCraneReportInCsvFormat(data, selectedHeaders, timezoneoffset, fileName, exportType, done) {
    const {selectedFlags } = this.extractCraneHeaders(selectedHeaders);
    const values = this.constructCraneCsvRows(data, selectedFlags, timezoneoffset);
    const csvFile = await this.generateCsvFile(values);

    if (csvFile) {
      const buffer = Buffer.from(csvFile, 'utf-8');
      awsConfig.reportUpload(buffer, fileName, exportType, async (result, error1) => {
        if (!error1) {
          return done(result, false);
        }
        return done(null, { message: 'cannot export document' });
      });
    }
  },
  extractCraneHeaders(selectedHeaders) {
    const rowValues = [];
    const columns = [];
    const selectedFlags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isEquipmentSelected: false,
      isDfowSelected: false,
      isGateSelected: false,
      isCompanySelected: false,
      isPersonSelected: false,
      isPickingFromSelected: false,
      isPickingToSelected: false,
      isLocationSelected: false,
    };

    const keyMap = {
      id: 'isIdSelected',
      description: 'isDescriptionSelected',
      date: 'isDateSelected',
      status: 'isStatusSelected',
      approvedby: 'isApprovedBySelected',
      equipment: 'isEquipmentSelected',
      dfow: 'isDfowSelected',
      gate: 'isGateSelected',
      company: 'isCompanySelected',
      name: 'isPersonSelected',
      pickingFrom: 'isPickingFromSelected',
      pickingTo: 'isPickingToSelected',
      location: 'isLocationSelected',
    };

    selectedHeaders.forEach((object) => {
      if (object.isActive) {
        rowValues.push(object.title);
        columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });
        const flag = keyMap[object.key];
        if (flag) selectedFlags[flag] = true;
      }
    });

    return { rowValues, columns, selectedFlags };
  },
  constructCraneCsvRows(data, flags, timezoneoffset) {
    return data.map(item => this.buildCraneRow(item, flags, timezoneoffset));
  },

  buildCraneRow(item, flags, timezoneoffset) {
    const row = {};
    
    this.addBasicFields(row, item, flags);
    this.addDateTimeField(row, item, flags, timezoneoffset);
    this.addApproverField(row, item, flags);
    this.addEquipmentField(row, item, flags);
    this.addDfowField(row, item, flags);
    this.addGateField(row, item, flags);
    this.addCompanyField(row, item, flags);
    this.addPersonField(row, item, flags);
    this.addLocationFields(row, item, flags);
    
    return row;
  },

  addBasicFields(row, item, flags) {
    if (flags.isIdSelected) row.Id = item.CraneRequestId;
    if (flags.isDescriptionSelected) row.Description = item.description;
    if (flags.isStatusSelected) row.Status = item.status;
  },

  addDateTimeField(row, item, flags, timezoneoffset) {
    if (!flags.isDateSelected) return;
    
    const isCrane = item.requestType === 'craneRequest';
    const start = moment(isCrane ? item.craneDeliveryStart : item.deliveryStart).add(Number(timezoneoffset), 'minutes');
    const end = moment(isCrane ? item.craneDeliveryEnd : item.deliveryEnd).add(Number(timezoneoffset), 'minutes');
    row['Date & Time'] = `${start.format('MMM-DD-YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
  },

  addApproverField(row, item, flags) {
    if (!flags.isApprovedBySelected) return;
    
    row['Approved By'] = item.approverDetails?.User?.firstName
      ? `${item.approverDetails.User.firstName} ${item.approverDetails.User.lastName}`
      : '-';
  },

  addEquipmentField(row, item, flags) {
    if (!flags.isEquipmentSelected) return;
    
    const craneEquipments = item.equipmentDetails
      ?.filter(e => e.Equipment?.PresetEquipmentType?.isCraneType)
      .map(e => e.Equipment.equipmentName)
      .filter(Boolean) || [];
    row.Equipment = craneEquipments.length ? craneEquipments.join(', ') : '-';
  },

  addDfowField(row, item, flags) {
    if (!flags.isDfowSelected) return;
    
    const dfows = item.defineWorkDetails?.map(d => d.DeliverDefineWork?.DFOW).filter(Boolean) || [];
    row['Definable Feature of Work'] = dfows.length ? dfows.join(', ') : '-';
  },

  addGateField(row, item, flags) {
    if (!flags.isGateSelected) return;
    
    row.Gate = item.gateDetails?.[0]?.Gate?.gateName || '-';
  },

  addCompanyField(row, item, flags) {
    if (!flags.isCompanySelected) return;
    
    const companies = item.companyDetails?.map(c => c.Company?.companyName).filter(Boolean) || [];
    row['Responsible Company'] = companies.length ? companies.join(', ') : '-';
  },

  addPersonField(row, item, flags) {
    if (!flags.isPersonSelected) return;
    
    const persons = item.memberDetails
      ?.map(m => `${m.Member?.User?.firstName} ${m.Member?.User?.lastName}`.trim())
      .filter(Boolean) || [];
    row['Responsible Person'] = persons.length ? persons.join(', ') : '-';
  },

  addLocationFields(row, item, flags) {
    if (flags.isPickingFromSelected) {
      row['Picking From'] = this.getPickingFromLocation(item);
    }
    
    if (flags.isPickingToSelected) {
      row['Picking To'] = this.getPickingToLocation(item);
    }
    
    if (flags.isLocationSelected) {
      row.Location = item.location?.locationPath || '-';
    }
  },

  getPickingFromLocation(item) {
    if (item.requestType === 'craneRequest') return item.pickUpLocation;
    if (item.requestType === 'deliveryRequestWithCrane') return item.cranePickUpLocation;
    return '-';
  },

  getPickingToLocation(item) {
    if (item.requestType === 'craneRequest') return item.dropOffLocation;
    if (item.requestType === 'deliveryRequestWithCrane') return item.craneDropOffLocation;
    return '-';
  },
  async generateCsvFile(values) {
    const options = {
      showLabels: true,
      showTitle: false,
      useTextFile: false,
      useBom: false,
      useKeysAsHeaders: true,
    };

    const csvExporter = new ExportToCsv(options);
    return await csvExporter.generateCsv(values, true);
  }

};
module.exports = csvCraneReportService;
