const moment = require('moment');
const { Op } = require('sequelize');
const deliveryService = require('../calendarService');
const helper = require('../../helpers/domainHelper');

// Mock all dependencies
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            between: Symbol('between'),
            and: Symbol('and'),
            or: Symbol('or'),
            ne: Symbol('ne'),
            notIn: Symbol('notIn'),
            in: Symbol('in'),
            iLike: Symbol('iLike')
        },
        and: jest.fn()
    },
    Enterprise: {
        findOne: jest.fn()
    },
    InspectionRequest: {
        getCalendarData: jest.fn()
    },
    DeliveryRequest: {
        getCalendarData: jest.fn(),
        getCraneAssociatedRequest: jest.fn()
    },
    CraneRequest: {
        getAll: jest.fn()
    },
    Member: {
        findOne: jest.fn()
    },
    User: {
        findOne: jest.fn()
    },
    VoidList: {
        findAll: jest.fn()
    },
    DeliverCompany: {
        findOne: jest.fn()
    },
    DeliverGate: {
        findOne: jest.fn()
    },
    InspectionCompany: {
        findOne: jest.fn()
    },
    InspectionGate: {
        findOne: jest.fn()
    },
    CraneRequestCompany: {
        findOne: jest.fn()
    },
    CraneRequestResponsiblePerson: {
        findOne: jest.fn()
    },
    ConcreteRequest: {
        getAll: jest.fn()
    }
}));

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn()
}));

describe('Calendar Service', () => {
    let mockInputData;
    let mockDone;
    let mockMemberDetails;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common mock data
        mockInputData = {
            headers: { timezoneoffset: '-240' },
            params: { ProjectId: 1, void: '0' },
            body: {
                start: '2024-03-20',
                end: '2024-03-21',
                descriptionFilter: '',
                companyFilter: 0,
                gateFilter: 0,
                memberFilter: 0,
                dateFilter: null,
                search: ''
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                domainName: 'testdomain'
            }
        };

        mockDone = jest.fn();

        mockMemberDetails = {
            id: 1,
            RoleId: 2,
            UserId: 1,
            ProjectId: 1,
            isDeleted: false,
            isActive: true
        };

        // Setup common mock responses
        helper.returnProjectModel.mockResolvedValue({ User: { findOne: jest.fn() } });
        helper.getDynamicModel.mockResolvedValue({});

        // Setup VoidList mock to return empty array by default
        const { VoidList } = require('../../models');
        VoidList.findAll.mockResolvedValue([]);
    });

    describe('getEventNDR', () => {
        it('should successfully get delivery events', async () => {
            // Arrange
            const mockDeliveryList = {
                rows: [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            const { Member, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCalendarData.mockResolvedValue(mockDeliveryList);

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(Member.findOne).toHaveBeenCalled();
            expect(DeliveryRequest.getCalendarData).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(mockDeliveryList, false);
        });

        it('should return error when member not found', async () => {
            // Arrange
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue(null);

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Member not found' });
        });

        it('should handle empty delivery list', async () => {
            // Arrange
            const mockEmptyList = { rows: [], count: 0 };
            const { Member, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCalendarData.mockResolvedValue(mockEmptyList);

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(mockEmptyList, false);
        });

        it('should handle errors gracefully', async () => {
            // Arrange
            const { Member } = require('../../models');
            Member.findOne.mockRejectedValue(new Error('Database error'));

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle DeliveryRequest.getCalendarData error', async () => {
            // Arrange
            const { Member, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCalendarData.mockRejectedValue(new Error('Calendar data error'));

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should apply filters when shouldApplyFilters returns true', async () => {
            // Arrange
            const mockDeliveryList = {
                rows: [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }],
                count: 1
            };
            mockInputData.body.companyFilter = 1;

            const { Member, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCalendarData.mockResolvedValue(mockDeliveryList);

            // Mock the applyFiltersAndReturn method
            const applyFiltersSpy = jest.spyOn(deliveryService, 'applyFiltersAndReturn');
            applyFiltersSpy.mockImplementation((_incomeData, _deliveryList, _memberDetails, _timezoneoffset, done) => {
                done({ rows: [], count: 0 }, false);
            });

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(applyFiltersSpy).toHaveBeenCalled();
            applyFiltersSpy.mockRestore();
        });

        it('should handle VoidList.findAll error', async () => {
            // Arrange
            const { Member, VoidList } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            VoidList.findAll.mockRejectedValue(new Error('VoidList error'));

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle different timezone offsets', async () => {
            // Arrange
            mockInputData.headers.timezoneoffset = '+300';
            const mockDeliveryList = {
                rows: [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            const { Member, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCalendarData.mockResolvedValue(mockDeliveryList);

            // Act
            await deliveryService.getEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(mockDeliveryList, false);
        });
    });

    describe('getInspectionEventNDR', () => {
        it('should successfully get inspection events', async () => {
            // Arrange
            const mockInspectionList = {
                rows: [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            const { Member, InspectionRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            InspectionRequest.getCalendarData.mockResolvedValue(mockInspectionList);

            // Act
            await deliveryService.getInspectionEventNDR(mockInputData, mockDone);

            // Assert
            expect(InspectionRequest.getCalendarData).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(mockInspectionList, false);
        });

        it('should apply filters when filter conditions are met', async () => {
            // Arrange
            const mockInspectionList = {
                rows: [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            mockInputData.body.companyFilter = 1;
            const { Member, InspectionRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            InspectionRequest.getCalendarData.mockResolvedValue(mockInspectionList);

            // Mock the applyInspectionFiltersAndReturn method
            const applyFiltersSpy = jest.spyOn(deliveryService, 'applyInspectionFiltersAndReturn');
            applyFiltersSpy.mockImplementation((_incomeData, _deliveryList, _memberDetails, _timezoneoffset, done) => {
                done({ rows: [], count: 0 }, false);
            });

            // Act
            await deliveryService.getInspectionEventNDR(mockInputData, mockDone);

            // Assert
            expect(InspectionRequest.getCalendarData).toHaveBeenCalled();
            expect(applyFiltersSpy).toHaveBeenCalled();
            applyFiltersSpy.mockRestore();
        });

        it('should return error when member not found', async () => {
            // Arrange
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue(null);

            // Act
            await deliveryService.getInspectionEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Member not found' });
        });

        it('should handle empty inspection list', async () => {
            // Arrange
            const mockEmptyList = { rows: [], count: 0 };
            const { Member, InspectionRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            InspectionRequest.getCalendarData.mockResolvedValue(mockEmptyList);

            // Act
            await deliveryService.getInspectionEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(mockEmptyList, false);
        });

        it('should handle InspectionRequest.getCalendarData error', async () => {
            // Arrange
            const { Member, InspectionRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            InspectionRequest.getCalendarData.mockRejectedValue(new Error('Inspection data error'));

            // Act
            await deliveryService.getInspectionEventNDR(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should apply inspection filters when shouldApplyFilters returns true', async () => {
            // Arrange
            const mockInspectionList = {
                rows: [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }],
                count: 1
            };
            mockInputData.body.gateFilter = 1;

            const { Member, InspectionRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            InspectionRequest.getCalendarData.mockResolvedValue(mockInspectionList);

            // Mock the applyInspectionFiltersAndReturn method
            const applyFiltersSpy = jest.spyOn(deliveryService, 'applyInspectionFiltersAndReturn');
            applyFiltersSpy.mockImplementation((_incomeData, _deliveryList, _memberDetails, _timezoneoffset, done) => {
                done({ rows: [], count: 0 }, false);
            });

            // Act
            await deliveryService.getInspectionEventNDR(mockInputData, mockDone);

            // Assert
            expect(applyFiltersSpy).toHaveBeenCalled();
            applyFiltersSpy.mockRestore();
        });
    });

    describe('getDeliveryRequestWithCrane', () => {
        it('should successfully get delivery requests with crane', async () => {
            // Arrange
            const mockCraneList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
            const mockDeliveryList = [{ id: 2, deliveryStart: '2024-03-20T11:00:00' }];

            const { Member, CraneRequest, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            CraneRequest.getAll.mockResolvedValue(mockCraneList);
            DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryList);

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(CraneRequest.getAll).toHaveBeenCalled();
            expect(DeliveryRequest.getCraneAssociatedRequest).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(expect.arrayContaining([
                expect.objectContaining({ id: 1 }),
                expect.objectContaining({ id: 2 })
            ]), false);
        });

        it('should skip crane request when gate filter is applied', async () => {
            // Arrange
            mockInputData.body.gateFilter = 1;
            const { Member, CraneRequest, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue([]);

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(CraneRequest.getAll).not.toHaveBeenCalled();
            expect(DeliveryRequest.getCraneAssociatedRequest).toHaveBeenCalled();
        });

        it('should return error when member not found', async () => {
            // Arrange
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue(null);

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Member not found' });
        });

        it('should handle CraneRequest.getAll error', async () => {
            // Arrange
            const { Member, CraneRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            CraneRequest.getAll.mockRejectedValue(new Error('Crane request error'));

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle DeliveryRequest.getCraneAssociatedRequest error', async () => {
            // Arrange
            const { Member, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCraneAssociatedRequest.mockRejectedValue(new Error('Delivery request error'));

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should skip crane request when status filter is Delivered', async () => {
            // Arrange
            mockInputData.body.statusFilter = 'Delivered';
            const { Member, CraneRequest, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue([]);

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(CraneRequest.getAll).not.toHaveBeenCalled();
            expect(DeliveryRequest.getCraneAssociatedRequest).toHaveBeenCalled();
        });

        it('should skip delivery request when status filter is Completed', async () => {
            // Arrange
            mockInputData.body.statusFilter = 'Completed';
            const mockCraneList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
            const { Member, CraneRequest, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            CraneRequest.getAll.mockResolvedValue(mockCraneList);

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(CraneRequest.getAll).toHaveBeenCalled();
            expect(DeliveryRequest.getCraneAssociatedRequest).not.toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(mockCraneList, false);
        });

        it('should apply filters when shouldApplyFilters returns true', async () => {
            // Arrange
            const mockCraneList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
            const mockDeliveryList = [{ id: 2, deliveryStart: '2024-03-20T11:00:00' }];
            mockInputData.body.companyFilter = 1;

            const { Member, CraneRequest, DeliveryRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            CraneRequest.getAll.mockResolvedValue(mockCraneList);
            DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryList);

            // Mock the applyFiltersAndReturn method for crane requests
            const applyFiltersSpy = jest.spyOn(deliveryService, 'applyFiltersAndReturn');
            applyFiltersSpy.mockImplementation((_incomeData, _combinedList, _memberDetails, _timezoneoffset, done) => {
                done([], false);
            });

            // Act
            await deliveryService.getDeliveryRequestWithCrane(mockInputData, mockDone);

            // Assert
            expect(applyFiltersSpy).toHaveBeenCalled();
            applyFiltersSpy.mockRestore();
        });
    });

    describe('getConcreteRequest', () => {
        it('should successfully get concrete requests', async () => {
            // Arrange
            const mockConcreteList = {
                rows: [{ id: 1, concretePlacementStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            const { Member, ConcreteRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            ConcreteRequest.getAll.mockResolvedValue(mockConcreteList);

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(ConcreteRequest.getAll).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(mockConcreteList, false);
        });

        it('should return error for invalid void parameter', async () => {
            // Arrange
            mockInputData.params.void = 2;

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Please enter void as 1 or 0' });
        });

        it('should return error when member not found', async () => {
            // Arrange
            const { Member } = require('../../models');
            Member.findOne.mockResolvedValue(null);

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project Id/Member does not exist' });
        });

        it('should handle ConcreteRequest.getAll error', async () => {
            // Arrange
            const { Member, ConcreteRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            ConcreteRequest.getAll.mockRejectedValue(new Error('Concrete request error'));

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle VoidList.findAll error for concrete requests', async () => {
            // Arrange
            const { Member, VoidList } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            VoidList.findAll.mockRejectedValue(new Error('VoidList error'));

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle void parameter as string "1"', async () => {
            // Arrange
            mockInputData.params.void = '1';
            const mockConcreteList = {
                rows: [{ id: 1, concretePlacementStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            const { Member, ConcreteRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            ConcreteRequest.getAll.mockResolvedValue(mockConcreteList);

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(mockConcreteList, false);
        });

        it('should handle void parameter as number 1', async () => {
            // Arrange
            mockInputData.params.void = 1;
            const mockConcreteList = {
                rows: [{ id: 1, concretePlacementStart: '2024-03-20T10:00:00' }],
                count: 1
            };

            const { Member, ConcreteRequest } = require('../../models');
            Member.findOne.mockResolvedValue(mockMemberDetails);
            ConcreteRequest.getAll.mockResolvedValue(mockConcreteList);

            // Act
            await deliveryService.getConcreteRequest(mockInputData, mockDone);

            // Assert
            expect(mockDone).toHaveBeenCalledWith(mockConcreteList, false);
        });
    });

    describe('Helper Functions', () => {
        describe('getDateTimeRange', () => {
            it('should correctly calculate date range with timezone offset', () => {
                // Arrange
                const incomeData = {
                    start: '2024-03-20',
                    end: '2024-03-21'
                };
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.getDateTimeRange(incomeData, timezoneoffset);

                // Assert
                expect(result.startDateTime.format()).toBe('2024-03-20T00:00:00-04:00');
                expect(result.endDateTime.format()).toBe('2024-03-21T23:59:59-04:00');
            });

            it('should handle positive timezone offset', () => {
                // Arrange
                const incomeData = {
                    start: '2024-03-20',
                    end: '2024-03-21'
                };
                const timezoneoffset = '+300';

                // Act
                const result = deliveryService.getDateTimeRange(incomeData, timezoneoffset);

                // Assert
                expect(result.startDateTime.format()).toBe('2024-03-20T00:00:00+05:00');
                expect(result.endDateTime.format()).toBe('2024-03-21T23:59:59+05:00');
            });

            it('should handle zero timezone offset', () => {
                // Arrange
                const incomeData = {
                    start: '2024-03-20',
                    end: '2024-03-21'
                };
                const timezoneoffset = '0';

                // Act
                const result = deliveryService.getDateTimeRange(incomeData, timezoneoffset);

                // Assert
                expect(result.startDateTime.format()).toBe('2024-03-20T00:00:00Z');
                expect(result.endDateTime.format()).toBe('2024-03-21T23:59:59Z');
            });
        });

        describe('shouldApplyFilters', () => {
            it('should return true when any filter is applied', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 1,
                    gateFilter: 0,
                    memberFilter: 0,
                    dateFilter: null
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when no filters are applied', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 0,
                    gateFilter: 0,
                    memberFilter: 0,
                    dateFilter: null
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when gateFilter is applied', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 0,
                    gateFilter: 1,
                    memberFilter: 0,
                    dateFilter: null
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return true when memberFilter is applied', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 0,
                    gateFilter: 0,
                    memberFilter: 1,
                    dateFilter: null
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return true when dateFilter is applied', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 0,
                    gateFilter: 0,
                    memberFilter: 0,
                    dateFilter: '2024-03-20'
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return true when multiple filters are applied', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 1,
                    gateFilter: 1,
                    memberFilter: 1,
                    dateFilter: '2024-03-20'
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('isValidVoidParam', () => {
            it('should return true for valid void parameters', () => {
                expect(deliveryService.isValidVoidParam('0')).toBe(true);
                expect(deliveryService.isValidVoidParam('1')).toBe(true);
                expect(deliveryService.isValidVoidParam(0)).toBe(true);
                expect(deliveryService.isValidVoidParam(1)).toBe(true);
            });

            it('should return false for invalid void parameters', () => {
                expect(deliveryService.isValidVoidParam(2)).toBe(false);
                expect(deliveryService.isValidVoidParam(-1)).toBe(false);
                expect(deliveryService.isValidVoidParam('2')).toBe(false);
            });

            it('should return false for null and undefined', () => {
                expect(deliveryService.isValidVoidParam(null)).toBe(false);
                expect(deliveryService.isValidVoidParam(undefined)).toBe(false);
            });

            it('should return false for non-numeric strings', () => {
                expect(deliveryService.isValidVoidParam('abc')).toBe(false);
                expect(deliveryService.isValidVoidParam('')).toBe(false);
                expect(deliveryService.isValidVoidParam('true')).toBe(false);
            });

            it('should return false for boolean values', () => {
                expect(deliveryService.isValidVoidParam(true)).toBe(false);
                expect(deliveryService.isValidVoidParam(false)).toBe(false);
            });

            it('should return false for decimal numbers', () => {
                expect(deliveryService.isValidVoidParam(0.5)).toBe(false);
                expect(deliveryService.isValidVoidParam(1.5)).toBe(false);
            });
        });
    });

    describe('Dynamic Model Setup', () => {
        it('should setup models with domain name', async () => {
            // Arrange
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });

            // Act
            await deliveryService.getDynamicModel(mockInputData);

            // Assert
            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
        });

        it('should setup models with parent company', async () => {
            // Arrange
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);

            // Mock findUserByEmail to return a user
            const findUserSpy = jest.spyOn(deliveryService, 'findUserByEmail');
            findUserSpy.mockResolvedValue({ id: 1 });

            // Mock findEnterpriseForUser to return an enterprise
            const findEnterpriseSpy = jest.spyOn(deliveryService, 'findEnterpriseForUser');
            findEnterpriseSpy.mockResolvedValue({ name: 'ParentCompany' });

            // Ensure domain name is empty and parent company ID is set
            mockInputData.user.domainName = '';
            mockInputData.user.email = '<EMAIL>';
            mockInputData.body.ParentCompanyId = 1;

            // Act
            await deliveryService.getDynamicModel(mockInputData);

            // Assert
            expect(helper.getDynamicModel).toHaveBeenCalledWith('parentcompany');
            findUserSpy.mockRestore();
            findEnterpriseSpy.mockRestore();
        });

        it('should setup default models when no domain or parent company', async () => {
            // Arrange
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);
            mockInputData.user.domainName = '';
            mockInputData.body.ParentCompanyId = undefined;

            // Act
            await deliveryService.getDynamicModel(mockInputData);

            // Assert
            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle missing user email in setupModelsWithParentCompany', async () => {
            // Arrange
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);
            mockInputData.user.domainName = '';
            mockInputData.user.email = '';
            mockInputData.body.ParentCompanyId = 1;

            // Act
            await deliveryService.getDynamicModel(mockInputData);

            // Assert
            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle user not found in setupModelsWithParentCompany', async () => {
            // Arrange
            const { Enterprise, User } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);
            User.findOne.mockResolvedValue(null);
            mockInputData.user.domainName = '';
            mockInputData.body.ParentCompanyId = 1;

            // Act
            await deliveryService.getDynamicModel(mockInputData);

            // Assert
            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle enterprise not found in setupModelsWithParentCompany', async () => {
            // Arrange
            const { Enterprise, User } = require('../../models');
            Enterprise.findOne.mockResolvedValue(null);
            User.findOne.mockResolvedValue({ id: 1 });

            // Mock findEnterpriseForUser to return null
            const findEnterpriseSpy = jest.spyOn(deliveryService, 'findEnterpriseForUser');
            findEnterpriseSpy.mockResolvedValue(null);

            mockInputData.user.domainName = '';
            mockInputData.body.ParentCompanyId = 1;

            // Act
            await deliveryService.getDynamicModel(mockInputData);

            // Assert
            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
            findEnterpriseSpy.mockRestore();
        });

        it('should handle helper.returnProjectModel error', async () => {
            // Arrange
            helper.returnProjectModel.mockRejectedValue(new Error('Project model error'));

            // Act & Assert
            await expect(deliveryService.getDynamicModel(mockInputData)).rejects.toThrow('Project model error');
        });

        it('should handle helper.getDynamicModel error', async () => {
            // Arrange
            const { Enterprise } = require('../../models');
            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });
            helper.getDynamicModel.mockRejectedValue(new Error('Dynamic model error'));

            // Act & Assert
            await expect(deliveryService.getDynamicModel(mockInputData)).rejects.toThrow('Dynamic model error');
        });
    });

    describe('Search and Filter Functions', () => {
        describe('getSearchData', () => {
            it('should filter delivery list based on search conditions', async () => {
                // Arrange
                const deliveryList = [
                    { id: 1, deliveryStart: '2024-03-20T10:00:00' },
                    { id: 2, deliveryStart: '2024-03-20T11:00:00' }
                ];
                const incomeData = {
                    companyFilter: 1,
                    gateFilter: 1,
                    dateFilter: '2024-03-20'
                };
                const { DeliverCompany, DeliverGate } = require('../../models');
                DeliverCompany.findOne.mockResolvedValue({ id: 1 });
                DeliverGate.findOne.mockResolvedValue({ id: 1 });

                // Act
                await deliveryService.getSearchData(
                    incomeData,
                    deliveryList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(DeliverCompany.findOne).toHaveBeenCalled();
                expect(DeliverGate.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle empty delivery list', async () => {
                // Arrange
                const deliveryList = [];
                const incomeData = {};

                // Act
                await deliveryService.getSearchData(
                    incomeData,
                    deliveryList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(deliveryList, false);
            });

            it('should handle company filter not found', async () => {
                // Arrange
                const deliveryList = [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }];
                const incomeData = { companyFilter: 1 };
                const { DeliverCompany } = require('../../models');
                DeliverCompany.findOne.mockResolvedValue(null);

                // Act
                await deliveryService.getSearchData(
                    incomeData,
                    deliveryList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(DeliverCompany.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle gate filter not found', async () => {
                // Arrange
                const deliveryList = [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }];
                const incomeData = { gateFilter: 1 };
                const { DeliverGate } = require('../../models');
                DeliverGate.findOne.mockResolvedValue(null);

                // Act
                await deliveryService.getSearchData(
                    incomeData,
                    deliveryList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(DeliverGate.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle element not in date range', async () => {
                // Arrange
                const deliveryList = [{ id: 1, deliveryStart: '2024-03-19T10:00:00' }];
                const incomeData = { dateFilter: '2024-03-20' };

                // Act
                await deliveryService.getSearchData(
                    incomeData,
                    deliveryList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle database error in search conditions', async () => {
                // Arrange
                const deliveryList = [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }];
                const incomeData = { companyFilter: 1 };
                const { DeliverCompany } = require('../../models');
                DeliverCompany.findOne.mockRejectedValue(new Error('Database error'));

                // Act & Assert
                await expect(deliveryService.getSearchData(
                    incomeData,
                    deliveryList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                )).rejects.toThrow('Database error');
            });
        });

        describe('getInspectionSearchData', () => {
            it('should filter inspection list based on search conditions', async () => {
                // Arrange
                const inspectionList = [
                    { id: 1, inspectionStart: '2024-03-20T10:00:00' },
                    { id: 2, inspectionStart: '2024-03-20T11:00:00' }
                ];
                const incomeData = {
                    companyFilter: 1,
                    gateFilter: 1,
                    dateFilter: '2024-03-20'
                };
                const { InspectionCompany, InspectionGate } = require('../../models');
                InspectionCompany.findOne.mockResolvedValue({ id: 1 });
                InspectionGate.findOne.mockResolvedValue({ id: 1 });

                // Act
                await deliveryService.getInspectionSearchData(
                    incomeData,
                    inspectionList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(InspectionCompany.findOne).toHaveBeenCalled();
                expect(InspectionGate.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle empty inspection list', async () => {
                // Arrange
                const inspectionList = [];
                const incomeData = {};

                // Act
                await deliveryService.getInspectionSearchData(
                    incomeData,
                    inspectionList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(inspectionList, false);
            });

            it('should handle inspection company filter not found', async () => {
                // Arrange
                const inspectionList = [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }];
                const incomeData = { companyFilter: 1 };
                const { InspectionCompany } = require('../../models');
                InspectionCompany.findOne.mockResolvedValue(null);

                // Act
                await deliveryService.getInspectionSearchData(
                    incomeData,
                    inspectionList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(InspectionCompany.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle inspection gate filter not found', async () => {
                // Arrange
                const inspectionList = [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }];
                const incomeData = { gateFilter: 1 };
                const { InspectionGate } = require('../../models');
                InspectionGate.findOne.mockResolvedValue(null);

                // Act
                await deliveryService.getInspectionSearchData(
                    incomeData,
                    inspectionList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(InspectionGate.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle inspection element not in date range', async () => {
                // Arrange
                const inspectionList = [{ id: 1, inspectionStart: '2024-03-19T10:00:00' }];
                const incomeData = { dateFilter: '2024-03-20' };

                // Act
                await deliveryService.getInspectionSearchData(
                    incomeData,
                    inspectionList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle database error in inspection search conditions', async () => {
                // Arrange
                const inspectionList = [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }];
                const incomeData = { companyFilter: 1 };
                const { InspectionCompany } = require('../../models');
                InspectionCompany.findOne.mockRejectedValue(new Error('Database error'));

                // Act & Assert
                await expect(deliveryService.getInspectionSearchData(
                    incomeData,
                    inspectionList,
                    0,
                    [],
                    mockMemberDetails,
                    '-240',
                    mockDone
                )).rejects.toThrow('Database error');
            });
        });
    });

    describe('Additional Helper Functions', () => {
        describe('shouldSkipCraneRequest', () => {
            it('should return true when gateFilter is greater than 0', () => {
                // Arrange
                const incomeData = { gateFilter: 1 };

                // Act
                const result = deliveryService.shouldSkipCraneRequest(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return true when statusFilter is Delivered', () => {
                // Arrange
                const incomeData = { statusFilter: 'Delivered' };

                // Act
                const result = deliveryService.shouldSkipCraneRequest(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when no skip conditions are met', () => {
                // Arrange
                const incomeData = { gateFilter: 0, statusFilter: 'Pending' };

                // Act
                const result = deliveryService.shouldSkipCraneRequest(incomeData);

                // Assert
                expect(result).toBe(false);
            });
        });

        describe('shouldSkipDeliveryRequest', () => {
            it('should return true when statusFilter is Completed', () => {
                // Arrange
                const incomeData = { statusFilter: 'Completed' };

                // Act
                const result = deliveryService.shouldSkipDeliveryRequest(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when statusFilter is not Completed', () => {
                // Arrange
                const incomeData = { statusFilter: 'Pending' };

                // Act
                const result = deliveryService.shouldSkipDeliveryRequest(incomeData);

                // Assert
                expect(result).toBe(false);
            });
        });

        describe('prepareElement', () => {
            it('should create a deep copy of the element', () => {
                // Arrange
                const element = { id: 1, nested: { value: 'test' } };

                // Act
                const result = deliveryService.prepareElement(element);

                // Assert
                expect(result).toEqual(element);
                expect(result).not.toBe(element);
                expect(result.nested).not.toBe(element.nested);
            });
        });

        describe('areAllConditionsMet', () => {
            it('should return true when all conditions are true', () => {
                // Arrange
                const status = {
                    companyCondition: true,
                    gateCondition: true,
                    memberCondition: true
                };

                // Act
                const result = deliveryService.areAllConditionsMet(status);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when any condition is false', () => {
                // Arrange
                const status = {
                    companyCondition: true,
                    gateCondition: false,
                    memberCondition: true
                };

                // Act
                const result = deliveryService.areAllConditionsMet(status);

                // Assert
                expect(result).toBe(false);
            });
        });

        describe('areAllInspectionConditionsMet', () => {
            it('should return true when all inspection conditions are true', () => {
                // Arrange
                const status = {
                    companyCondition: true,
                    gateCondition: true,
                    memberCondition: true
                };

                // Act
                const result = deliveryService.areAllInspectionConditionsMet(status);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when any inspection condition is false', () => {
                // Arrange
                const status = {
                    companyCondition: false,
                    gateCondition: true,
                    memberCondition: true
                };

                // Act
                const result = deliveryService.areAllInspectionConditionsMet(status);

                // Assert
                expect(result).toBe(false);
            });
        });

        describe('shouldAddCraneElement', () => {
            it('should return true when all crane conditions are met', () => {
                // Arrange
                const status = {
                    companyCondition: true,
                    memberCondition: true
                };

                // Act
                const result = deliveryService.shouldAddCraneElement(status);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when crane company condition is false', () => {
                // Arrange
                const status = {
                    companyCondition: false,
                    memberCondition: true
                };

                // Act
                const result = deliveryService.shouldAddCraneElement(status);

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when crane member condition is false', () => {
                // Arrange
                const status = {
                    companyCondition: true,
                    memberCondition: false
                };

                // Act
                const result = deliveryService.shouldAddCraneElement(status);

                // Assert
                expect(result).toBe(false);
            });
        });

        describe('isElementInDateRange', () => {
            it('should return true when element is in date range', () => {
                // Arrange
                const element = { deliveryStart: '2024-03-20T12:00:00' };
                const dateFilter = '2024-03-20';
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.isElementInDateRange(element, dateFilter, timezoneoffset);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when element is not in date range', () => {
                // Arrange
                const element = { deliveryStart: '2024-03-19T12:00:00' };
                const dateFilter = '2024-03-20';
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.isElementInDateRange(element, dateFilter, timezoneoffset);

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when element has no deliveryStart', () => {
                // Arrange
                const element = {};
                const dateFilter = '2024-03-20';
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.isElementInDateRange(element, dateFilter, timezoneoffset);

                // Assert
                expect(result).toBe(false);
            });
        });

        describe('isInspectionElementInDateRange', () => {
            it('should return true when inspection element is in date range', () => {
                // Arrange
                const element = { inspectionStart: '2024-03-20T12:00:00' };
                const dateFilter = '2024-03-20';
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.isInspectionElementInDateRange(element, dateFilter, timezoneoffset);

                // Assert
                expect(result).toBe(true);
            });

            it('should return false when inspection element is not in date range', () => {
                // Arrange
                const element = { inspectionStart: '2024-03-19T12:00:00' };
                const dateFilter = '2024-03-20';
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.isInspectionElementInDateRange(element, dateFilter, timezoneoffset);

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when inspection element has no inspectionStart', () => {
                // Arrange
                const element = {};
                const dateFilter = '2024-03-20';
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.isInspectionElementInDateRange(element, dateFilter, timezoneoffset);

                // Assert
                expect(result).toBe(false);
            });
        });
    });

    describe('Build Condition Methods', () => {
        describe('buildDeliveryCondition', () => {
            it('should build delivery condition with basic parameters', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = {};

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result).toHaveProperty('ProjectId', 1);
                expect(result).toHaveProperty('isQueued', false);
                expect(result).toHaveProperty('deliveryStart');
                expect(VoidList.findAll).toHaveBeenCalled();
            });

            it('should apply description filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { descriptionFilter: 'test description' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result.description).toBeDefined();
                expect(result.description[require('sequelize').Op.iLike]).toBe('%test description%');
            });

            it('should apply equipment filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { equipmentFilter: 5 };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result['$equipmentDetails.Equipment.id$']).toBe(5);
            });

            it('should apply member filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { memberFilter: 3 };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result['$memberDetails.Member.id$']).toBe(3);
            });

            it('should apply location filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { locationFilter: 'Building A' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result['$location.locationPath$']).toBe('Building A');
            });

            it('should apply status filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { statusFilter: 'Approved' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result.status).toBe('Approved');
            });

            it('should apply pick from filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { pickFrom: 'Warehouse A' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result.cranePickUpLocation).toBeDefined();
                expect(result.cranePickUpLocation[require('sequelize').Op.iLike]).toBe('%Warehouse A%');
            });

            it('should apply pick to filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { pickTo: 'Site B' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildDeliveryCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result.craneDropOffLocation).toBeDefined();
                expect(result.craneDropOffLocation[require('sequelize').Op.iLike]).toBe('%Site B%');
            });
        });

        describe('buildInspectionCondition', () => {
            it('should build inspection condition with basic parameters', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = {};

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildInspectionCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result).toHaveProperty('ProjectId', 1);
                expect(result).toHaveProperty('isQueued', false);
                expect(result).toHaveProperty('inspectionStart');
                expect(VoidList.findAll).toHaveBeenCalled();
            });

            it('should apply inspection status filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { inspectionStatusFilter: 'Pass' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildInspectionCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result.inspectionStatus).toBeDefined();
                expect(result.inspectionStatus[require('sequelize').Op.iLike]).toBe('%Pass%');
            });

            it('should apply inspection type filter to condition', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };
                const incomeData = { inspectionTypeFilter: 'Quality' };

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.buildInspectionCondition(params, startDateTime, endDateTime, memberDetails, incomeData);

                // Assert
                expect(result.inspectionType).toBeDefined();
                expect(result.inspectionType[require('sequelize').Op.iLike]).toBe('%Quality%');
            });
        });

        describe('applyStatusFilter', () => {
            it('should not apply filter when statusFilter is null', () => {
                // Arrange
                const condition = {};
                const statusFilter = null;

                // Act
                deliveryService.applyStatusFilter(condition, statusFilter);

                // Assert
                expect(condition.inspectionStatus).toBeUndefined();
                expect(condition.status).toBeUndefined();
            });

            it('should apply Delivered status filter correctly', () => {
                // Arrange
                const condition = {};
                const statusFilter = 'Delivered';

                // Act
                deliveryService.applyStatusFilter(condition, statusFilter);

                // Assert
                expect(condition.inspectionStatus).toBeDefined();
                expect(condition.inspectionStatus[require('sequelize').Op.or]).toBeDefined();
            });

            it('should apply Approved status filter correctly', () => {
                // Arrange
                const condition = {};
                const statusFilter = 'Approved';

                // Act
                deliveryService.applyStatusFilter(condition, statusFilter);

                // Assert
                expect(condition.status).toBe('Approved');
                expect(condition.inspectionStatus).toBe(null);
            });

            it('should apply other status filters correctly', () => {
                // Arrange
                const condition = {};
                const statusFilter = 'Pending';

                // Act
                deliveryService.applyStatusFilter(condition, statusFilter);

                // Assert
                expect(condition.status).toBe('Pending');
            });
        });

        describe('buildSearchCondition', () => {
            it('should return empty object when no search term', () => {
                // Arrange
                const incomeData = { search: '' };
                const params = { ProjectId: 1 };

                // Act
                const result = deliveryService.buildSearchCondition(incomeData, params);

                // Assert
                expect(result).toEqual({});
            });

            it('should build search condition for text search', () => {
                // Arrange
                const incomeData = { search: 'test search' };
                const params = { ProjectId: 1 };

                // Act
                const result = deliveryService.buildSearchCondition(incomeData, params);

                // Assert
                expect(result[require('sequelize').Op.and]).toBeDefined();
                expect(result[require('sequelize').Op.and][0][require('sequelize').Op.or]).toBeDefined();
            });

            it('should build search condition for numeric search', () => {
                // Arrange
                const incomeData = { search: '123' };
                const params = { ProjectId: 1 };

                // Act
                const result = deliveryService.buildSearchCondition(incomeData, params);

                // Assert
                expect(result[require('sequelize').Op.and]).toBeDefined();
                expect(result[require('sequelize').Op.and][0][require('sequelize').Op.or]).toBeDefined();
            });
        });
    });

    describe('Crane Request Search Functions', () => {
        describe('getSearchCraneRequestCalendarData', () => {
            it('should handle empty delivery list', async () => {
                // Arrange
                const incomeData = {};
                const deliveryList = [];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                // Act
                await deliveryService.getSearchCraneRequestCalendarData(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(deliveryList, false);
            });

            it('should process crane request with company filter', async () => {
                // Arrange
                const incomeData = { companyFilter: 1 };
                const deliveryList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                const { CraneRequestCompany } = require('../../models');
                CraneRequestCompany.findOne.mockResolvedValue({ id: 1 });

                // Act
                await deliveryService.getSearchCraneRequestCalendarData(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(CraneRequestCompany.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should process crane request with member filter', async () => {
                // Arrange
                const incomeData = { memberFilter: 2 };
                const deliveryList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                const { CraneRequestResponsiblePerson } = require('../../models');
                CraneRequestResponsiblePerson.findOne.mockResolvedValue({ id: 1 });

                // Act
                await deliveryService.getSearchCraneRequestCalendarData(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(CraneRequestResponsiblePerson.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalled();
            });

            it('should not add element when company condition fails', async () => {
                // Arrange
                const incomeData = { companyFilter: 1 };
                const deliveryList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                const { CraneRequestCompany } = require('../../models');
                CraneRequestCompany.findOne.mockResolvedValue(null);

                // Act
                await deliveryService.getSearchCraneRequestCalendarData(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(CraneRequestCompany.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalledWith([], false);
            });

            it('should not add element when member condition fails', async () => {
                // Arrange
                const incomeData = { memberFilter: 2 };
                const deliveryList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                const { CraneRequestResponsiblePerson } = require('../../models');
                CraneRequestResponsiblePerson.findOne.mockResolvedValue(null);

                // Act
                await deliveryService.getSearchCraneRequestCalendarData(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(CraneRequestResponsiblePerson.findOne).toHaveBeenCalled();
                expect(mockDone).toHaveBeenCalledWith([], false);
            });
        });

        describe('checkCraneSearchConditions', () => {
            it('should return true conditions when no filters applied', async () => {
                // Arrange
                const incomeData = {};
                const element = { id: 1 };

                // Act
                const result = await deliveryService.checkCraneSearchConditions(incomeData, element);

                // Assert
                expect(result.companyCondition).toBe(true);
                expect(result.memberCondition).toBe(true);
            });

            it('should check company condition when company filter applied', async () => {
                // Arrange
                const incomeData = { companyFilter: 1 };
                const element = { id: 1 };

                const { CraneRequestCompany } = require('../../models');
                CraneRequestCompany.findOne.mockResolvedValue({ id: 1 });

                // Act
                const result = await deliveryService.checkCraneSearchConditions(incomeData, element);

                // Assert
                expect(result.companyCondition).toBe(true);
                expect(CraneRequestCompany.findOne).toHaveBeenCalledWith({
                    where: {
                        CraneRequestId: 1,
                        CompanyId: 1,
                        isDeleted: false,
                    },
                });
            });

            it('should check member condition when member filter applied', async () => {
                // Arrange
                const incomeData = { memberFilter: 2 };
                const element = { id: 1 };

                const { CraneRequestResponsiblePerson } = require('../../models');
                CraneRequestResponsiblePerson.findOne.mockResolvedValue({ id: 1 });

                // Act
                const result = await deliveryService.checkCraneSearchConditions(incomeData, element);

                // Assert
                expect(result.memberCondition).toBe(true);
                expect(CraneRequestResponsiblePerson.findOne).toHaveBeenCalledWith({
                    where: {
                        CraneRequestId: 1,
                        MemberId: 2,
                        isDeleted: false,
                    },
                });
            });
        });
    });

    describe('Void List Functions', () => {
        describe('getVoidDeliveryIds', () => {
            it('should return array of void delivery IDs', async () => {
                // Arrange
                const projectId = 1;
                const mockVoidList = [
                    { DeliveryRequestId: 1 },
                    { DeliveryRequestId: 2 },
                    { DeliveryRequestId: 3 }
                ];

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue(mockVoidList);

                // Act
                const result = await deliveryService.getVoidDeliveryIds(projectId);

                // Assert
                expect(result).toEqual([1, 2, 3]);
                expect(VoidList.findAll).toHaveBeenCalledWith({
                    where: {
                        ProjectId: projectId,
                        isDeliveryRequest: true,
                        DeliveryRequestId: { [require('sequelize').Op.ne]: null },
                    },
                });
            });

            it('should return empty array when no void deliveries', async () => {
                // Arrange
                const projectId = 1;

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue([]);

                // Act
                const result = await deliveryService.getVoidDeliveryIds(projectId);

                // Assert
                expect(result).toEqual([]);
            });
        });

        describe('getVoidInspectionIds', () => {
            it('should return array of void inspection IDs', async () => {
                // Arrange
                const projectId = 1;
                const mockVoidList = [
                    { InspectionRequestId: 1 },
                    { InspectionRequestId: 2 }
                ];

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue(mockVoidList);

                // Act
                const result = await deliveryService.getVoidInspectionIds(projectId);

                // Assert
                expect(result).toEqual([1, 2]);
                expect(VoidList.findAll).toHaveBeenCalledWith({
                    where: {
                        ProjectId: projectId,
                        InspectionRequestId: { [require('sequelize').Op.ne]: null },
                    },
                });
            });
        });

        describe('getVoidConcreteDeliveryIds', () => {
            it('should return array of void concrete delivery IDs', async () => {
                // Arrange
                const projectId = 1;
                const mockVoidList = [
                    { ConcreteRequestId: 1 },
                    { ConcreteRequestId: 2 }
                ];

                const { VoidList } = require('../../models');
                VoidList.findAll.mockResolvedValue(mockVoidList);

                // Act
                const result = await deliveryService.getVoidConcreteDeliveryIds(projectId);

                // Assert
                expect(result).toEqual([1, 2]);
                expect(VoidList.findAll).toHaveBeenCalledWith({
                    where: {
                        ProjectId: projectId,
                        isDeliveryRequest: false,
                        ConcreteRequestId: { [require('sequelize').Op.ne]: null },
                    },
                });
            });
        });

        describe('getVoidLists', () => {
            it('should return both void delivery and crane delivery lists', async () => {
                // Arrange
                const projectId = 1;
                const mockDeliveryVoidList = [{ DeliveryRequestId: 1 }, { DeliveryRequestId: 2 }];
                const mockCraneVoidList = [{ CraneRequestId: 1 }];

                const { VoidList } = require('../../models');
                VoidList.findAll
                    .mockResolvedValueOnce(mockDeliveryVoidList)
                    .mockResolvedValueOnce(mockCraneVoidList);

                // Act
                const result = await deliveryService.getVoidLists(projectId);

                // Assert
                expect(result.voidDelivery).toEqual([1, 2]);
                expect(result.voidCraneDelivery).toEqual([1]);
                expect(VoidList.findAll).toHaveBeenCalledTimes(2);
            });
        });

        describe('applyVoidCondition', () => {
            it('should apply notIn operator when void is 0', () => {
                // Arrange
                const params = { void: '0' };
                const condition = {};
                const voidDelivery = [1, 2, 3];

                // Act
                deliveryService.applyVoidCondition(params, condition, voidDelivery);

                // Assert
                expect(condition['$DeliveryRequest.id$']).toBeDefined();
                expect(condition['$DeliveryRequest.id$'][require('sequelize').Op.and]).toBeDefined();
            });

            it('should apply in operator when void is 1', () => {
                // Arrange
                const params = { void: '1' };
                const condition = {};
                const voidDelivery = [1, 2, 3];

                // Act
                deliveryService.applyVoidCondition(params, condition, voidDelivery);

                // Assert
                expect(condition['$DeliveryRequest.id$']).toBeDefined();
                expect(condition['$DeliveryRequest.id$'][require('sequelize').Op.and]).toBeDefined();
            });

            it('should apply notIn operator when void is numeric 0', () => {
                // Arrange
                const params = { void: 0 };
                const condition = {};
                const voidDelivery = [1, 2, 3];

                // Act
                deliveryService.applyVoidCondition(params, condition, voidDelivery);

                // Assert
                expect(condition['$DeliveryRequest.id$']).toBeDefined();
            });
        });

        describe('applyVoidInspectionCondition', () => {
            it('should apply void condition for inspection requests', () => {
                // Arrange
                const params = { void: '0' };
                const condition = {};
                const voidDelivery = [1, 2];

                // Act
                deliveryService.applyVoidInspectionCondition(params, condition, voidDelivery);

                // Assert
                expect(condition['$InspectionRequest.id$']).toBeDefined();
                expect(condition['$InspectionRequest.id$'][require('sequelize').Op.and]).toBeDefined();
            });
        });

        describe('applyVoidConditions', () => {
            it('should apply void conditions for both delivery and crane requests', () => {
                // Arrange
                const params = { void: '0' };
                const deliveryRequestCondition = {};
                const craneDeliveryRequestCondition = {};
                const voidDelivery = [1, 2];
                const voidCraneDelivery = [3, 4];

                // Act
                deliveryService.applyVoidConditions(
                    params,
                    deliveryRequestCondition,
                    craneDeliveryRequestCondition,
                    voidDelivery,
                    voidCraneDelivery
                );

                // Assert
                expect(deliveryRequestCondition['$DeliveryRequest.id$']).toBeDefined();
                expect(craneDeliveryRequestCondition['$CraneRequest.id$']).toBeDefined();
            });
        });
    });

    describe('Additional Edge Cases and Negative Tests', () => {
        describe('getEventNDR edge cases', () => {
            it('should handle missing timezone offset', async () => {
                // Arrange
                const inputDataWithoutTimezone = {
                    headers: {},
                    params: { ProjectId: 1, void: '0' },
                    body: {
                        start: '2024-03-20',
                        end: '2024-03-21'
                    },
                    user: {
                        id: 1,
                        email: '<EMAIL>',
                        domainName: 'testdomain'
                    }
                };

                const { Member, DeliveryRequest } = require('../../models');
                Member.findOne.mockResolvedValue(mockMemberDetails);
                DeliveryRequest.getCalendarData.mockResolvedValue({ rows: [], count: 0 });

                // Act
                await deliveryService.getEventNDR(inputDataWithoutTimezone, mockDone);

                // Assert
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle invalid date formats', async () => {
                // Arrange
                const inputDataWithInvalidDates = {
                    headers: { timezoneoffset: '-240' },
                    params: { ProjectId: 1, void: '0' },
                    body: {
                        start: 'invalid-date',
                        end: 'invalid-date'
                    },
                    user: {
                        id: 1,
                        email: '<EMAIL>',
                        domainName: 'testdomain'
                    }
                };

                const { Member } = require('../../models');
                Member.findOne.mockResolvedValue(mockMemberDetails);

                // Act
                await deliveryService.getEventNDR(inputDataWithInvalidDates, mockDone);

                // Assert
                expect(mockDone).toHaveBeenCalled();
            });

            it('should handle multiple filters combination', async () => {
                // Arrange
                const inputDataWithMultipleFilters = {
                    headers: { timezoneoffset: '-240' },
                    params: { ProjectId: 1, void: '0' },
                    body: {
                        start: '2024-03-20',
                        end: '2024-03-21',
                        companyFilter: 1,
                        gateFilter: 1,
                        memberFilter: 1,
                        dateFilter: '2024-03-20',
                        descriptionFilter: 'test',
                        equipmentFilter: 1,
                        locationFilter: 'Building A',
                        statusFilter: 'Approved',
                        pickFrom: 'Warehouse',
                        pickTo: 'Site'
                    },
                    user: {
                        id: 1,
                        email: '<EMAIL>',
                        domainName: 'testdomain'
                    }
                };

                const mockDeliveryList = {
                    rows: [{ id: 1, deliveryStart: '2024-03-20T10:00:00' }],
                    count: 1
                };

                const { Member, DeliveryRequest } = require('../../models');
                Member.findOne.mockResolvedValue(mockMemberDetails);
                DeliveryRequest.getCalendarData.mockResolvedValue(mockDeliveryList);

                // Mock the applyFiltersAndReturn method
                const applyFiltersSpy = jest.spyOn(deliveryService, 'applyFiltersAndReturn');
                applyFiltersSpy.mockImplementation((_incomeData, _deliveryList, _memberDetails, _timezoneoffset, done) => {
                    done({ rows: [], count: 0 }, false);
                });

                // Act
                await deliveryService.getEventNDR(inputDataWithMultipleFilters, mockDone);

                // Assert
                expect(applyFiltersSpy).toHaveBeenCalled();
                applyFiltersSpy.mockRestore();
            });
        });

        describe('getInspectionEventNDR edge cases', () => {
            it('should handle inspection with all filters', async () => {
                // Arrange
                const inputDataWithAllFilters = {
                    headers: { timezoneoffset: '-240' },
                    params: { ProjectId: 1, void: '0' },
                    body: {
                        start: '2024-03-20',
                        end: '2024-03-21',
                        companyFilter: 1,
                        gateFilter: 1,
                        memberFilter: 1,
                        dateFilter: '2024-03-20',
                        descriptionFilter: 'test',
                        equipmentFilter: 1,
                        locationFilter: 'Building A',
                        statusFilter: 'Delivered',
                        inspectionStatusFilter: 'Pass',
                        inspectionTypeFilter: 'Quality',
                        pickFrom: 'Warehouse',
                        pickTo: 'Site'
                    },
                    user: {
                        id: 1,
                        email: '<EMAIL>',
                        domainName: 'testdomain'
                    }
                };

                const mockInspectionList = {
                    rows: [{ id: 1, inspectionStart: '2024-03-20T10:00:00' }],
                    count: 1
                };

                const { Member, InspectionRequest } = require('../../models');
                Member.findOne.mockResolvedValue(mockMemberDetails);
                InspectionRequest.getCalendarData.mockResolvedValue(mockInspectionList);

                // Mock the applyInspectionFiltersAndReturn method
                const applyFiltersSpy = jest.spyOn(deliveryService, 'applyInspectionFiltersAndReturn');
                applyFiltersSpy.mockImplementation((_incomeData, _deliveryList, _memberDetails, _timezoneoffset, done) => {
                    done({ rows: [], count: 0 }, false);
                });

                // Act
                await deliveryService.getInspectionEventNDR(inputDataWithAllFilters, mockDone);

                // Assert
                expect(applyFiltersSpy).toHaveBeenCalled();
                applyFiltersSpy.mockRestore();
            });
        });

        describe('getDeliveryRequestWithCrane edge cases', () => {
            it('should handle complex filter combinations', async () => {
                // Arrange
                const inputDataWithComplexFilters = {
                    headers: { timezoneoffset: '-240' },
                    params: { ProjectId: 1, void: '0' },
                    body: {
                        start: '2024-03-20',
                        end: '2024-03-21',
                        companyFilter: 1,
                        memberFilter: 1,
                        equipmentFilter: 1,
                        statusFilter: 'Pending',
                        descriptionFilter: 'crane operation',
                        pickFrom: 'Yard A',
                        pickTo: 'Site B',
                        search: 'crane',
                        dateFilter: '2024-03-20'
                    },
                    user: {
                        id: 1,
                        email: '<EMAIL>',
                        domainName: 'testdomain'
                    }
                };

                const mockCraneList = [{ id: 1, craneDeliveryStart: '2024-03-20T10:00:00' }];
                const mockDeliveryList = [{ id: 2, deliveryStart: '2024-03-20T11:00:00' }];

                const { Member, CraneRequest, DeliveryRequest } = require('../../models');
                Member.findOne.mockResolvedValue(mockMemberDetails);
                CraneRequest.getAll.mockResolvedValue(mockCraneList);
                DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryList);

                // Mock the applyFiltersAndReturn method
                const applyFiltersSpy = jest.spyOn(deliveryService, 'applyFiltersAndReturn');
                applyFiltersSpy.mockImplementation((_incomeData, _craneRequestArray, _deliveryList, _memberDetails, _timezoneoffset, done) => {
                    done([], false);
                });

                // Act
                await deliveryService.getDeliveryRequestWithCrane(inputDataWithComplexFilters, mockDone);

                // Assert
                expect(applyFiltersSpy).toHaveBeenCalled();
                applyFiltersSpy.mockRestore();
            });
        });
    });

    describe('Domain Model Helper Functions', () => {
        describe('getDomainNameFromInput', () => {
            it('should return domain name from user input', () => {
                // Arrange
                const inputData = {
                    user: { domainName: 'testdomain' }
                };

                // Act
                const result = deliveryService.getDomainNameFromInput(inputData);

                // Assert
                expect(result).toBe('testdomain');
            });

            it('should return empty string when no domain name', () => {
                // Arrange
                const inputData = {
                    user: {}
                };

                // Act
                const result = deliveryService.getDomainNameFromInput(inputData);

                // Assert
                expect(result).toBe('');
            });

            it('should return empty string when no user', () => {
                // Arrange
                const inputData = {};

                // Act
                const result = deliveryService.getDomainNameFromInput(inputData);

                // Assert
                expect(result).toBe('');
            });
        });

        describe('getParentCompanyId', () => {
            it('should return ParentCompanyId from body', () => {
                // Arrange
                const inputData = {
                    body: { ParentCompanyId: 123 }
                };

                // Act
                const result = deliveryService.getParentCompanyId(inputData);

                // Assert
                expect(result).toBe(123);
            });

            it('should return ParentCompanyId from params', () => {
                // Arrange
                const inputData = {
                    params: { ParentCompanyId: 456 }
                };

                // Act
                const result = deliveryService.getParentCompanyId(inputData);

                // Assert
                expect(result).toBe(456);
            });

            it('should return undefined when no ParentCompanyId', () => {
                // Arrange
                const inputData = {
                    body: {},
                    params: {}
                };

                // Act
                const result = deliveryService.getParentCompanyId(inputData);

                // Assert
                expect(result).toBeUndefined();
            });
        });

        describe('findDomainEnterprise', () => {
            it('should find enterprise by domain name', async () => {
                // Arrange
                const domainName = 'testdomain';
                const mockEnterprise = { id: 1, name: 'testdomain' };

                const { Enterprise } = require('../../models');
                Enterprise.findOne.mockResolvedValue(mockEnterprise);

                // Act
                const result = await deliveryService.findDomainEnterprise(domainName);

                // Assert
                expect(result).toBe(mockEnterprise);
                expect(Enterprise.findOne).toHaveBeenCalledWith({
                    where: { name: 'testdomain' },
                });
            });

            it('should return null when domain name is empty', async () => {
                // Arrange
                const domainName = '';

                // Act
                const result = await deliveryService.findDomainEnterprise(domainName);

                // Assert
                expect(result).toBeNull();
            });

            it('should return null when domain name is null', async () => {
                // Arrange
                const domainName = null;

                // Act
                const result = await deliveryService.findDomainEnterprise(domainName);

                // Assert
                expect(result).toBeNull();
            });
        });

        describe('setupModelsWithDomain', () => {
            it('should setup models with domain name', async () => {
                // Arrange
                const domainName = 'testdomain';
                const inputData = {};
                const mockModelObj = {};

                helper.getDynamicModel.mockResolvedValue(mockModelObj);

                // Act
                const result = await deliveryService.setupModelsWithDomain(domainName, inputData);

                // Assert
                expect(helper.getDynamicModel).toHaveBeenCalledWith(domainName);
                expect(result).toBeNull();
            });
        });

        describe('setupDefaultModels', () => {
            it('should setup default models', async () => {
                // Arrange
                const inputData = {};
                const mockModelObj = {};

                helper.getDynamicModel.mockResolvedValue(mockModelObj);

                // Act
                const result = await deliveryService.setupDefaultModels(inputData);

                // Assert
                expect(helper.getDynamicModel).toHaveBeenCalledWith('');
                expect(result).toBeNull();
            });
        });

        describe('findMemberData', () => {
            it('should find member data by user ID', async () => {
                // Arrange
                const userData = { id: 1 };
                const mockMember = { id: 1, UserId: 1, RoleId: 2, isDeleted: false };

                const { Member } = require('../../models');
                Member.findOne.mockResolvedValue(mockMember);

                // Act
                const result = await deliveryService.findMemberData(userData);

                // Assert
                expect(result).toBe(mockMember);
                expect(Member.findOne).toHaveBeenCalledWith({
                    where: {
                        UserId: 1,
                        RoleId: { [require('sequelize').Op.ne]: 4 },
                        isDeleted: false
                    },
                });
            });
        });

        describe('findEnterpriseById', () => {
            it('should find enterprise by ID', async () => {
                // Arrange
                const enterpriseId = 1;
                const mockEnterprise = { id: 1, status: 'completed' };

                const { Enterprise } = require('../../models');
                Enterprise.findOne.mockResolvedValue(mockEnterprise);

                // Act
                const result = await deliveryService.findEnterpriseById(enterpriseId);

                // Assert
                expect(result).toBe(mockEnterprise);
                expect(Enterprise.findOne).toHaveBeenCalledWith({
                    where: {
                        id: enterpriseId,
                        status: 'completed'
                    },
                });
            });
        });

        describe('findEnterpriseByParentCompany', () => {
            it('should find enterprise by parent company ID', async () => {
                // Arrange
                const parentCompanyId = 1;
                const mockEnterprise = { id: 1, ParentCompanyId: 1, status: 'completed' };

                const { Enterprise } = require('../../models');
                Enterprise.findOne.mockResolvedValue(mockEnterprise);

                // Act
                const result = await deliveryService.findEnterpriseByParentCompany(parentCompanyId);

                // Assert
                expect(result).toBe(mockEnterprise);
                expect(Enterprise.findOne).toHaveBeenCalledWith({
                    where: {
                        ParentCompanyId: parentCompanyId,
                        status: 'completed'
                    },
                });
            });
        });
    });

    describe('Additional Method Coverage', () => {
        describe('fetchDeliveryList', () => {
            it('should fetch delivery list with correct parameters', async () => {
                // Arrange
                const condition = { ProjectId: 1 };
                const memberDetails = { RoleId: 2, id: 1 };
                const searchCondition = {};
                const mockDeliveryList = { rows: [], count: 0 };

                const { DeliveryRequest } = require('../../models');
                DeliveryRequest.getCalendarData.mockResolvedValue(mockDeliveryList);

                // Act
                const result = await deliveryService.fetchDeliveryList(condition, memberDetails, searchCondition);

                // Assert
                expect(result).toBe(mockDeliveryList);
                expect(DeliveryRequest.getCalendarData).toHaveBeenCalledWith(
                    condition,
                    memberDetails.RoleId,
                    memberDetails.id,
                    searchCondition,
                    'DESC'
                );
            });
        });

        describe('buildRequestConditions', () => {
            it('should build request conditions for delivery and crane requests', async () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const startDateTime = moment('2024-03-20T00:00:00');
                const endDateTime = moment('2024-03-21T23:59:59');
                const memberDetails = { id: 1, RoleId: 2 };

                const { VoidList } = require('../../models');
                VoidList.findAll
                    .mockResolvedValueOnce([{ DeliveryRequestId: 1 }])
                    .mockResolvedValueOnce([{ CraneRequestId: 1 }]);

                // Act
                const result = await deliveryService.buildRequestConditions(params, startDateTime, endDateTime, memberDetails);

                // Assert
                expect(result.deliveryRequestCondition).toBeDefined();
                expect(result.craneDeliveryRequestCondition).toBeDefined();
                expect(result.deliveryRequestCondition.ProjectId).toBe(1);
                expect(result.craneDeliveryRequestCondition.ProjectId).toBe(1);
            });
        });

        describe('fetchRequestData', () => {
            it('should fetch both crane and delivery request data', async () => {
                // Arrange
                const inputData = { params: { ProjectId: 1 } };
                const memberDetails = { roleId: 2, memberId: 1 };
                const deliveryRequestCondition = {};
                const craneDeliveryRequestCondition = {};
                const incomeData = {};

                const mockCraneList = [{ id: 1 }];
                const mockDeliveryList = [{ id: 2 }];

                const { CraneRequest, DeliveryRequest } = require('../../models');
                CraneRequest.getAll.mockResolvedValue(mockCraneList);
                DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryList);

                // Act
                const result = await deliveryService.fetchRequestData(
                    inputData,
                    memberDetails,
                    deliveryRequestCondition,
                    craneDeliveryRequestCondition,
                    incomeData
                );

                // Assert
                expect(result.craneRequestArray).toBe(mockCraneList);
                expect(result.deliveryList).toBe(mockDeliveryList);
            });

            it('should skip crane request when shouldSkipCraneRequest returns true', async () => {
                // Arrange
                const inputData = { params: { ProjectId: 1 } };
                const memberDetails = { roleId: 2, memberId: 1 };
                const deliveryRequestCondition = {};
                const craneDeliveryRequestCondition = {};
                const incomeData = { gateFilter: 1 };

                const mockDeliveryList = [{ id: 2 }];

                const { DeliveryRequest } = require('../../models');
                DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryList);

                // Act
                const result = await deliveryService.fetchRequestData(
                    inputData,
                    memberDetails,
                    deliveryRequestCondition,
                    craneDeliveryRequestCondition,
                    incomeData
                );

                // Assert
                expect(result.craneRequestArray).toEqual([]);
                expect(result.deliveryList).toBe(mockDeliveryList);
            });

            it('should skip delivery request when shouldSkipDeliveryRequest returns true', async () => {
                // Arrange
                const inputData = { params: { ProjectId: 1 } };
                const memberDetails = { roleId: 2, memberId: 1 };
                const deliveryRequestCondition = {};
                const craneDeliveryRequestCondition = {};
                const incomeData = { statusFilter: 'Completed' };

                const mockCraneList = [{ id: 1 }];

                const { CraneRequest } = require('../../models');
                CraneRequest.getAll.mockResolvedValue(mockCraneList);

                // Act
                const result = await deliveryService.fetchRequestData(
                    inputData,
                    memberDetails,
                    deliveryRequestCondition,
                    craneDeliveryRequestCondition,
                    incomeData
                );

                // Assert
                expect(result.craneRequestArray).toBe(mockCraneList);
                expect(result.deliveryList).toEqual([]);
            });
        });

        describe('buildConcreteCondition', () => {
            it('should build concrete condition with all parameters', () => {
                // Arrange
                const params = { ProjectId: 1, void: '0' };
                const timezoneoffset = '-240';
                const incomeData = {
                    start: '2024-03-20',
                    end: '2024-03-21'
                };
                const voidConcreteDelivery = [1, 2, 3];

                // Act
                const result = deliveryService.buildConcreteCondition(params, timezoneoffset, incomeData, voidConcreteDelivery);

                // Assert
                expect(result.ProjectId).toBe(1);
                expect(result.isDeleted).toBe(false);
                expect(result.concretePlacementStart).toBeDefined();
                expect(result['$ConcreteRequest.id$']).toBeDefined();
            });

            it('should apply in operator when void is 1', () => {
                // Arrange
                const params = { ProjectId: 1, void: '1' };
                const timezoneoffset = '-240';
                const incomeData = {
                    start: '2024-03-20',
                    end: '2024-03-21'
                };
                const voidConcreteDelivery = [1, 2, 3];

                // Act
                const result = deliveryService.buildConcreteCondition(params, timezoneoffset, incomeData, voidConcreteDelivery);

                // Assert
                expect(result['$ConcreteRequest.id$']).toBeDefined();
                expect(result['$ConcreteRequest.id$'][require('sequelize').Op.and]).toBeDefined();
            });
        });

        describe('getMemberDetails', () => {
            it('should get member details by user and project', async () => {
                // Arrange
                const loginUser = { id: 1 };
                const projectId = 1;
                const mockMember = { id: 1, UserId: 1, ProjectId: 1, isDeleted: false, isActive: true };

                const { Member } = require('../../models');
                Member.findOne.mockResolvedValue(mockMember);

                // Act
                const result = await deliveryService.getMemberDetails(loginUser, projectId);

                // Assert
                expect(result).toBe(mockMember);
                expect(Member.findOne).toHaveBeenCalledWith({
                    where: require('sequelize').and({
                        UserId: 1,
                        ProjectId: 1,
                        isDeleted: false,
                        isActive: true,
                    }),
                });
            });
        });

        describe('returnProjectModel', () => {
            it('should return project model and set publicUser', async () => {
                // Arrange
                const mockModelData = { User: { findOne: jest.fn() } };
                helper.returnProjectModel.mockResolvedValue(mockModelData);

                // Act
                await deliveryService.returnProjectModel();

                // Assert
                expect(helper.returnProjectModel).toHaveBeenCalled();
            });
        });

        describe('assignModels', () => {
            it('should handle model assignment', () => {
                // Arrange
                const modelObj = {};

                // Act & Assert - This method is currently empty but should not throw
                expect(() => deliveryService.assignModels(modelObj)).not.toThrow();
            });
        });
    });

    describe('Final Edge Cases and Error Scenarios', () => {
        describe('shouldAddElement edge cases', () => {
            it('should return false when conditions are not met', () => {
                // Arrange
                const status = { companyCondition: false, gateCondition: true, memberCondition: true };
                const incomeData = {};
                const element = {};
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.shouldAddElement(status, incomeData, element, timezoneoffset);

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when all conditions met and no date filter', () => {
                // Arrange
                const status = { companyCondition: true, gateCondition: true, memberCondition: true };
                const incomeData = {};
                const element = {};
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.shouldAddElement(status, incomeData, element, timezoneoffset);

                // Assert
                expect(result).toBe(true);
            });

            it('should check date range when date filter is applied', () => {
                // Arrange
                const status = { companyCondition: true, gateCondition: true, memberCondition: true };
                const incomeData = { dateFilter: '2024-03-20' };
                const element = { deliveryStart: '2024-03-20T12:00:00' };
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.shouldAddElement(status, incomeData, element, timezoneoffset);

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('shouldAddInspectionElement edge cases', () => {
            it('should return false when inspection conditions are not met', () => {
                // Arrange
                const status = { companyCondition: false, gateCondition: true, memberCondition: true };
                const incomeData = {};
                const element = {};
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.shouldAddInspectionElement(status, incomeData, element, timezoneoffset);

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when all inspection conditions met and no date filter', () => {
                // Arrange
                const status = { companyCondition: true, gateCondition: true, memberCondition: true };
                const incomeData = {};
                const element = {};
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.shouldAddInspectionElement(status, incomeData, element, timezoneoffset);

                // Assert
                expect(result).toBe(true);
            });

            it('should check inspection date range when date filter is applied', () => {
                // Arrange
                const status = { companyCondition: true, gateCondition: true, memberCondition: true };
                const incomeData = { dateFilter: '2024-03-20' };
                const element = { inspectionStart: '2024-03-20T12:00:00' };
                const timezoneoffset = '-240';

                // Act
                const result = deliveryService.shouldAddInspectionElement(status, incomeData, element, timezoneoffset);

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('processNextElement edge cases', () => {
            it('should call done when at end of list', () => {
                // Arrange
                const incomeData = {};
                const deliveryList = [{ id: 1 }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                // Act
                const returnValue = deliveryService.processNextElement(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(result, false);
            });

            it('should continue processing when not at end of list', () => {
                // Arrange
                const incomeData = {};
                const deliveryList = [{ id: 1 }, { id: 2 }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                // Mock getSearchData to avoid infinite recursion
                const getSearchDataSpy = jest.spyOn(deliveryService, 'getSearchData');
                getSearchDataSpy.mockImplementation((incomeData, deliveryList, index, result, memberDetails, timezoneoffset, callback) => {
                    callback(result, false);
                });

                // Act
                deliveryService.processNextElement(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(getSearchDataSpy).toHaveBeenCalled();
                getSearchDataSpy.mockRestore();
            });
        });

        describe('processNextInspectionElement edge cases', () => {
            it('should call done when at end of inspection list', () => {
                // Arrange
                const incomeData = {};
                const deliveryList = [{ id: 1 }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                // Act
                const returnValue = deliveryService.processNextInspectionElement(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(result, false);
            });
        });

        describe('processNextCraneElement edge cases', () => {
            it('should call done when at end of crane list', () => {
                // Arrange
                const incomeData = {};
                const deliveryList = [{ id: 1 }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';

                // Act
                const returnValue = deliveryService.processNextCraneElement(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(result, false);
            });
        });

        describe('Error handling in recursive functions', () => {
            it('should handle errors in processNextElement callback', () => {
                // Arrange
                const incomeData = {};
                const deliveryList = [{ id: 1 }, { id: 2 }];
                const index = 0;
                const result = [];
                const memberDetails = mockMemberDetails;
                const timezoneoffset = '-240';
                const testError = new Error('Test error');

                // Mock getSearchData to simulate error
                const getSearchDataSpy = jest.spyOn(deliveryService, 'getSearchData');
                getSearchDataSpy.mockImplementation((incomeData, deliveryList, index, result, memberDetails, timezoneoffset, callback) => {
                    callback(null, testError);
                });

                // Act
                deliveryService.processNextElement(
                    incomeData,
                    deliveryList,
                    index,
                    result,
                    memberDetails,
                    timezoneoffset,
                    mockDone
                );

                // Assert
                expect(mockDone).toHaveBeenCalledWith(null, testError);
                getSearchDataSpy.mockRestore();
            });
        });

        describe('Complex filter scenarios', () => {
            it('should handle all filters applied simultaneously in shouldApplyFilters', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 1,
                    gateFilter: 1,
                    memberFilter: 1,
                    dateFilter: '2024-03-20'
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(true);
            });

            it('should handle zero values in filters', () => {
                // Arrange
                const incomeData = {
                    companyFilter: 0,
                    gateFilter: 0,
                    memberFilter: 0,
                    dateFilter: null
                };

                // Act
                const result = deliveryService.shouldApplyFilters(incomeData);

                // Assert
                expect(result).toBe(false);
            });
        });
    });
});