const moment = require('moment');
const momenttz = require('moment-timezone');
const cron = require('node-cron');
const stripe = require('stripe');
const _ = require('lodash');

// Mock all required modules
jest.mock('moment', () => {
    const mockMoment = jest.fn((input) => {
        const mockInstance = {
            format: jest.fn((format) => {
                if (format === 'YYYY-MM-DD') return '2024-03-20';
                return '2024-03-20';
            }),
            isSame: jest.fn((date) => {
                // Mock isSame to return true for same dates
                if (typeof date === 'string' && date.includes('2024-03-20')) return true;
                return false;
            }),
            diff: jest.fn((date) => {
                // Mock diff to return appropriate values for date comparisons
                if (typeof date === 'string') {
                    if (date.includes('2024-03-24')) return -5; // 5 days in future (negative for future dates)
                    if (date.includes('2024-03-22')) return -3; // 3 days in future
                    if (date.includes('2024-03-21')) return -1; // 1 day in future
                    if (date.includes('2024-03-19')) return 1; // 1 day in past
                    if (date.includes('2024-03-20')) return 0; // same day
                }
                return 0;
            }),
            add: jest.fn().mockReturnThis(),
            subtract: jest.fn().mockReturnThis(),
            utc: jest.fn().mockReturnThis(),
            tz: jest.fn().mockReturnThis(),
            isBefore: jest.fn((date) => {
                if (typeof date === 'string' && date.includes('2024-03-21')) return true;
                return false;
            }),
            isAfter: jest.fn((date) => {
                if (typeof date === 'string' && date.includes('2024-03-19')) return true;
                return false;
            }),
            valueOf: jest.fn().mockReturnValue(1710979200000), // Fixed timestamp for 2024-03-20
        };

        // Handle array input for moment([year, month, day])
        if (Array.isArray(input)) {
            mockInstance.diff = jest.fn().mockReturnValue(0);
        }

        return mockInstance;
    });

    // Mock static methods
    mockMoment.utc = jest.fn().mockReturnValue({
        format: jest.fn().mockReturnValue('2024-03-20 10:00:00 UTC'),
    });

    mockMoment.duration = jest.fn((diff) => ({
        asMinutes: jest.fn().mockReturnValue(60),
        asHours: jest.fn().mockReturnValue(Math.abs(diff) > 12 ? 14 : 1),
    }));

    // Mock moment() without arguments to return current date
    mockMoment.mockReturnValue({
        format: jest.fn((format) => {
            if (format === 'YYYY-MM-DD') return '2024-03-20';
            return '2024-03-20';
        }),
        isSame: jest.fn((date) => {
            if (typeof date === 'string' && date.includes('2024-03-20')) return true;
            return false;
        }),
        diff: jest.fn((date) => {
            if (typeof date === 'string') {
                if (date.includes('2024-03-24')) return -5; // 5 days in future
                if (date.includes('2024-03-22')) return -3; // 3 days in future
                if (date.includes('2024-03-21')) return -1; // 1 day in future
                if (date.includes('2024-03-19')) return 1; // 1 day in past
                if (date.includes('2024-03-20')) return 0; // same day
            }
            return 0;
        }),
        add: jest.fn().mockReturnThis(),
        subtract: jest.fn().mockReturnThis(),
        utc: jest.fn().mockReturnThis(),
        tz: jest.fn().mockReturnThis(),
        isBefore: jest.fn().mockReturnValue(false),
        isAfter: jest.fn().mockReturnValue(false),
        valueOf: jest.fn().mockReturnValue(1710979200000),
    });

    return mockMoment;
});

jest.mock('moment-timezone', () => {
    const mockMomentTz = jest.fn(() => ({
        utc: jest.fn().mockReturnThis(),
        format: jest.fn().mockReturnValue('2024-03-20'),
        tz: jest.fn().mockReturnThis(),
        minute: jest.fn().mockReturnValue(30),
        hour: jest.fn().mockReturnValue(10),
    }));

    mockMomentTz.tz = jest.fn(() => ({
        format: jest.fn((format) => {
            if (format === 'h:mm A') return '10:00 AM';
            if (format === 'YYYY-MM-DD HH:mm:ss') return '2024-03-20 10:00:00';
            return '2024-03-20';
        }),
        utc: jest.fn().mockReturnThis(),
        utcOffset: jest.fn().mockReturnValue(0),
        valueOf: jest.fn().mockReturnValue(1710979200000),
        minute: jest.fn().mockReturnValue(30),
        hour: jest.fn().mockReturnValue(10),
    }));

    return mockMomentTz;
});

jest.mock('node-cron', () => ({
    schedule: jest.fn((_, callback) => {
        // Return a mock job object
        const mockJob = {
            start: jest.fn(),
            stop: jest.fn(),
            destroy: jest.fn()
        };

        // Store the callback for later execution in tests
        if (callback && typeof callback === 'function') {
            mockJob.callback = callback;
            // Immediately execute callback for testing purposes
            setTimeout(() => {
                try {
                    callback();
                } catch (e) {
                    console.log('Callback execution error:', e);
                }
            }, 0);
        }

        return mockJob;
    }),
    validate: jest.fn().mockReturnValue(true)
}));
// Mock stripe module
const mockStripeRetrieve = jest.fn();
jest.mock('stripe', () => {
    return jest.fn(() => ({
        subscriptions: {
            retrieve: mockStripeRetrieve,
        },
    }));
});
jest.mock('lodash');
jest.mock('fs');

jest.mock('../../middlewares/awsConfig', () => ({}));

// Create mock Op object
const mockOp = {
    and: Symbol('and'),
    or: Symbol('or'),
    gte: Symbol('gte'),
    lte: Symbol('lte'),
    ne: Symbol('ne'),
    eq: Symbol('eq'),
    in: Symbol('in'),
    notIn: Symbol('notIn'),
};

jest.mock('../models', () => ({
    Project: {
        findAll: jest.fn(),
        update: jest.fn(),
        findOne: jest.fn(),
    },
    DeliveryRequest: {
        update: jest.fn().mockResolvedValue([1]),
    },
    CraneRequest: {
        update: jest.fn().mockResolvedValue([1]),
    },
    ConcreteRequest: {
        update: jest.fn().mockResolvedValue([1]),
    },
    SchedulerDateRange: {
        findAll: jest.fn().mockResolvedValue([]),
    },
    Sequelize: { Op: mockOp },
    DigestNotification: {
        findAll: jest.fn(),
        update: jest.fn(),
    },
    Member: {
        findAll: jest.fn(),
    },
    User: {
        findOne: jest.fn(),
    },
    TimeZone: {
        findAll: jest.fn(),
    },
    SchedulerReport: {
        createInstance: jest.fn(),
        updateInstance: jest.fn(),
        getAll: jest.fn(),
        findOne: jest.fn(),
        findAll: jest.fn(),
        update: jest.fn(),
    },
}));

jest.mock('../../mailer', () => ({
    sendMail: jest.fn().mockImplementation((_, __, ___, ____, callback) => {
        if (callback) callback();
        return Promise.resolve(true);
    }),
    sendReportMail: jest.fn().mockResolvedValue(true),
}));

jest.mock('../../middlewares/awsConfig', () => ({}));

jest.mock('../helpers/queryBuilderExternal', () => ({
    convertToCron: jest.fn().mockReturnValue('0 10 * * *'),
    convertToCronMonthly: jest.fn().mockReturnValue('0 10 1 * *'),
    convertToCronYearly: jest.fn().mockReturnValue('0 10 1 1 *'),
}));

// Create mock services that will be used by the cornService
const mockConcreteReportService = {
    exportReportForScheduler: jest.fn().mockResolvedValue('https://example.com/concrete.pdf')
};
const mockCraneReportService = {
    exportReportForScheduler: jest.fn().mockResolvedValue('https://example.com/crane.pdf')
};
const mockDeliveryReportService = {
    exportReportForScheduler: jest.fn().mockResolvedValue('https://example.com/delivery.pdf'),
    exportWeeklyCalendarReportForScheduler: jest.fn().mockResolvedValue('https://example.com/calendar.pdf'),
    exportHeatMapReportForSceduler: jest.fn().mockResolvedValue('https://example.com/heatmap.pdf')
};
const mockInspectionReportService = {
    exportReportForScheduler: jest.fn().mockResolvedValue('https://example.com/inspection.pdf')
};

// Mock the require function to return our mocked services when cornService requires '.'
jest.doMock('..', () => ({
    concreteReportService: mockConcreteReportService,
    craneReportService: mockCraneReportService,
    deliveryReportService: mockDeliveryReportService,
    inspectionReportService: mockInspectionReportService
}));

// Also mock the index file directly
jest.mock('../index', () => ({
    concreteReportService: mockConcreteReportService,
    craneReportService: mockCraneReportService,
    deliveryReportService: mockDeliveryReportService,
    inspectionReportService: mockInspectionReportService
}));



// Import the service after mocks
const cornService = require('../cornService');
const { Project, DigestNotification, Member, User, TimeZone, SchedulerReport, SchedulerDateRange, ConcreteRequest, DeliveryRequest, CraneRequest } = require('../models');

// Use the mock objects directly
const concreteReportService = mockConcreteReportService;
const craneReportService = mockCraneReportService;
const deliveryReportService = mockDeliveryReportService;
const inspectionReportService = mockInspectionReportService;

// Get the mocked cron for testing
// const cron = require('node-cron'); // Already imported at the top

describe('cornService', () => {
    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Clear module cache to ensure fresh requires
        delete require.cache[require.resolve('..')];
        delete require.cache[require.resolve('../index')];

        // Reset service mocks
        mockConcreteReportService.exportReportForScheduler.mockResolvedValue('https://example.com/concrete.pdf');
        mockCraneReportService.exportReportForScheduler.mockResolvedValue('https://example.com/crane.pdf');
        mockDeliveryReportService.exportReportForScheduler.mockResolvedValue('https://example.com/delivery.pdf');
        mockDeliveryReportService.exportWeeklyCalendarReportForScheduler.mockResolvedValue('https://example.com/calendar.pdf');
        mockDeliveryReportService.exportHeatMapReportForSceduler.mockResolvedValue('https://example.com/heatmap.pdf');
        mockInspectionReportService.exportReportForScheduler.mockResolvedValue('https://example.com/inspection.pdf');

        // Setup default stripe mock
        mockStripeRetrieve.mockResolvedValue({
            status: 'active',
            collection_method: 'charge_automatically',
            trial_end: Math.floor(Date.now() / 1000) + 86400, // 1 day from now
        });

        // Default cron mock
        cron.schedule.mockReturnValue({
            stop: jest.fn(),
        });
        cron.validate.mockReturnValue(true);
    });

    describe('checkOverDue', () => {
        beforeEach(() => {
            // Mock the checkProject method to avoid recursive calls
            cornService.checkProject = jest.fn((_, __, overDueProject, remainderProject, expiredTrialPlans, remainderProjectSubscription, done) => {
                done({
                    overdue: overDueProject,
                    remainder: remainderProject,
                    trialExpired: expiredTrialPlans,
                    projectSubscription: remainderProjectSubscription,
                }, false);
            });
        });

        it('should process projects when project details exist', async () => {
            const mockProjects = [{
                id: 1,
                status: 'active',
                PlanId: 2,
                StripeSubscription: { subscriptionId: 'sub_123' },
                ParentCompany: {
                    Company: {
                        companyName: 'Test Company'
                    }
                },
                userDetails: {
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            }];

            Project.findAll.mockResolvedValue(mockProjects);

            await cornService.checkOverDue();

            expect(Project.findAll).toHaveBeenCalled();
            expect(cornService.checkProject).toHaveBeenCalled();
        });

        it('should not process projects when no project details exist', async () => {
            Project.findAll.mockResolvedValue([]);

            await cornService.checkOverDue();

            expect(Project.findAll).toHaveBeenCalled();
            expect(cornService.checkProject).not.toHaveBeenCalled();
        });

        it('should handle production environment', async () => {
            const originalEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'prod';

            const mockProjects = [{
                id: 1,
                status: 'active',
                PlanId: 2,
                ParentCompany: {
                    Company: {
                        companyName: 'Test Company'
                    }
                },
                userDetails: {
                    email: '<EMAIL>'
                }
            }];

            Project.findAll.mockResolvedValue(mockProjects);

            await cornService.checkOverDue();

            expect(Project.findAll).toHaveBeenCalled();

            process.env.NODE_ENV = originalEnv;
        });

        it('should handle errors during project fetching', async () => {
            Project.findAll.mockRejectedValue(new Error('Database error'));

            await expect(cornService.checkOverDue()).rejects.toThrow('Database error');
        });
    });

    describe('checkNDRExpiration', () => {
        it('should update expired delivery requests', async () => {
            await cornService.checkNDRExpiration();

            expect(DeliveryRequest.update).toHaveBeenCalledWith(
                { status: 'Expired' },
                {
                    where: {
                        status: 'Pending',
                        deliveryStart: {
                            [mockOp.lte]: expect.any(Date),
                        },
                    },
                    order: [['id', 'ASC']],
                }
            );
        });

        it('should update expired crane requests', async () => {
            await cornService.checkNDRExpiration();

            expect(CraneRequest.update).toHaveBeenCalledWith(
                { status: 'Expired' },
                {
                    where: {
                        status: 'Pending',
                        craneDeliveryStart: {
                            [mockOp.lte]: expect.any(Date),
                        },
                    },
                    order: [['id', 'ASC']],
                }
            );
        });

        it('should update expired concrete requests', async () => {
            await cornService.checkNDRExpiration();

            expect(ConcreteRequest.update).toHaveBeenCalledWith(
                { status: 'Expired' },
                {
                    where: {
                        status: 'Pending',
                        concreteDeliveryStart: {
                            [mockOp.lte]: expect.any(Date),
                        },
                    },
                    order: [['id', 'ASC']],
                }
            );
        });

        it('should handle database errors gracefully', async () => {
            DeliveryRequest.update.mockRejectedValue(new Error('Database connection failed'));

            await expect(cornService.checkNDRExpiration()).rejects.toThrow('Database connection failed');
        });
    });

    describe('handleStripeSubscription', () => {
        it('should mark project as overdue when subscription is canceled', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            // Override the default mock for this specific test
            mockStripeRetrieve.mockResolvedValue({
                status: 'canceled',
                collection_method: 'charge_automatically'
            });

            const result = await cornService.handleStripeSubscription(mockElement, []);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 1 } }
            );
        });

        it('should handle subscription with past_due status', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            mockStripeRetrieve.mockResolvedValue({
                status: 'active',
                collection_method: 'past_due'
            });

            const result = await cornService.handleStripeSubscription(mockElement, []);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 1 } }
            );
        });

        it('should return false for active subscription', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            mockStripeRetrieve.mockResolvedValue({
                status: 'active',
                collection_method: 'charge_automatically'
            });

            const result = await cornService.handleStripeSubscription(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should handle stripe API errors', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            mockStripeRetrieve.mockRejectedValue(new Error('Stripe API error'));

            await expect(cornService.handleStripeSubscription(mockElement, [])).rejects.toThrow('Stripe API error');
        });

        it('should handle element without StripeSubscription', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: null,
            };

            const result = await cornService.handleStripeSubscription(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should handle element with StripeSubscription but no subscriptionId', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: {},
            };

            const result = await cornService.handleStripeSubscription(mockElement, []);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 1 } }
            );
        });

        it('should handle incomplete subscription status', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            mockStripeRetrieve.mockResolvedValue({
                status: 'incomplete',
                collection_method: 'charge_automatically'
            });

            const result = await cornService.handleStripeSubscription(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });
    });

    describe('handleTrialPlan', () => {
        it('should handle trial plan expiration', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: '2024-03-01',
                mailSendOn: '2024-03-01',
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);
        });

        it('should handle canceled trial subscription', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: '2024-03-01',
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            mockStripeRetrieve.mockResolvedValue({ status: 'canceled' });

            await cornService.handleTrialPlan(mockElement, [], [], []);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 1 } }
            );
        });

        it('should handle trial plan with 14 days difference', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);
        });

        it('should handle trial plan without subscribedOn date', async () => {
            const mockElement = {
                id: 1,
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);
        });

        it('should handle trial plan with special conditions', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: '2024-03-01',
                isSpecialTrial: true,
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);
        });

        it('should add to remainder when trial is 12-13 days old and mail not sent', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
                mailSendOn: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
            };
            const remainderProject = [];

            await cornService.handleTrialPlan(mockElement, [], remainderProject, []);

            expect(remainderProject).toContain(mockElement);
        });

        it('should not add to remainder when mail already sent for same day', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
                mailSendOn: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
            };
            const remainderProject = [];

            await cornService.handleTrialPlan(mockElement, [], remainderProject, []);

            expect(remainderProject).not.toContain(mockElement);
        });

        it('should handle trial plan without StripeSubscription', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
                StripeSubscription: null,
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);

            // Should call handleTrialExpiration
        });

        it('should handle trial plan with empty StripeSubscription', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
                StripeSubscription: {},
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);

            // Should call handleTrialExpiration
        });

        it('should handle stripe API errors gracefully', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: '2024-03-01',
                StripeSubscription: { subscriptionId: 'sub_123' },
            };

            mockStripeRetrieve.mockRejectedValue(new Error('Stripe API error'));

            await expect(cornService.handleTrialPlan(mockElement, [], [], [])).rejects.toThrow('Stripe API error');
        });
    });

    describe('schedulerReportRequest', () => {
        it('should create scheduler report successfully', async () => {
            const mockInputData = {
                params: { ProjectId: 1 },
                body: {
                    reportName: 'Test Report',
                    reportType: 'Weekly Calendar',
                    outputFormat: 'PDF',
                    recurrence: 'Daily',
                    runReportAt: '2024-03-20 10:00:00',
                    repeatEvery: { day: 1 },
                    timezone: 'UTC',
                },
                user: { id: 1 },
            };

            const mockScheduler = {
                id: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            SchedulerReport.createInstance.mockResolvedValue(mockScheduler);
            User.findOne.mockResolvedValue({ id: 1, firstName: 'Test', lastName: 'User' });

            const done = jest.fn();

            await cornService.schedulerReportRequest(mockInputData, done);

            expect(SchedulerReport.createInstance).toHaveBeenCalled();
            expect(cron.schedule).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(
                { message: 'Reports scheduled successfully' },
                false
            );
        });

        it('should handle invalid cron expression', async () => {
            const mockInputData = {
                params: { ProjectId: 1 },
                body: {
                    reportName: 'Test Report',
                    reportType: 'Weekly Calendar',
                    outputFormat: 'PDF',
                    recurrence: 'Daily',
                    runReportAt: '2024-03-20 10:00:00',
                    repeatEvery: { day: 1 },
                    timezone: 'UTC',
                },
                user: { id: 1 },
            };

            cron.validate.mockReturnValue(false);

            const done = jest.fn();

            await cornService.schedulerReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle scheduler creation failure', async () => {
            const mockInputData = {
                params: { ProjectId: 1 },
                body: {
                    reportName: 'Test Report',
                    reportType: 'Weekly Calendar',
                    outputFormat: 'PDF',
                    recurrence: 'Daily',
                    runReportAt: '2024-03-20 10:00:00',
                    repeatEvery: { day: 1 },
                    timezone: 'UTC',
                },
                user: { id: 1 },
            };

            SchedulerReport.createInstance.mockResolvedValue(null);

            const done = jest.fn();

            await cornService.schedulerReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong while creating scheduler record' });
        });

        it('should handle user not found', async () => {
            const mockInputData = {
                params: { ProjectId: 1 },
                body: {
                    reportName: 'Test Report',
                    reportType: 'Weekly Calendar',
                    outputFormat: 'PDF',
                    recurrence: 'Daily',
                    runReportAt: '2024-03-20 10:00:00',
                    repeatEvery: { day: 1 },
                    timezone: 'UTC',
                },
                user: { id: 1 },
            };

            const mockScheduler = { id: 1 };
            SchedulerReport.createInstance.mockResolvedValue(mockScheduler);
            User.findOne.mockResolvedValue(null);

            const done = jest.fn();

            await cornService.schedulerReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith(
                { message: 'Reports scheduled successfully' },
                false
            );
        });

        it('should handle database errors', async () => {
            const mockInputData = {
                params: { ProjectId: 1 },
                body: {
                    reportName: 'Test Report',
                    reportType: 'Weekly Calendar',
                    outputFormat: 'PDF',
                    recurrence: 'Daily',
                    runReportAt: '2024-03-20 10:00:00',
                    repeatEvery: { day: 1 },
                    timezone: 'UTC',
                },
                user: { id: 1 },
            };

            SchedulerReport.createInstance.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();

            await cornService.schedulerReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('generateReportAll', () => {
        it('should generate heat map report', async () => {
            const mockSchedulerData = {
                reportType: 'Heat Map',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            // Mock the payloadHeatMapGenerationAndSendS3URL method
            cornService.payloadHeatMapGenerationAndSendS3URL = jest.fn().mockResolvedValue('https://example.com/report.pdf');

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBe('https://example.com/report.pdf');
            expect(cornService.payloadHeatMapGenerationAndSendS3URL).toHaveBeenCalledWith(mockSchedulerData);
        });

        it('should generate weekly calendar report', async () => {
            const mockSchedulerData = {
                reportType: 'Weekly Calendar',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            cornService.payloadWeeklyCalendarGenerationAndSendS3URL = jest.fn().mockResolvedValue('https://example.com/calendar.pdf');

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBe('https://example.com/calendar.pdf');
            expect(cornService.payloadWeeklyCalendarGenerationAndSendS3URL).toHaveBeenCalledWith(mockSchedulerData);
        });

        it('should generate delivery report', async () => {
            const mockSchedulerData = {
                reportType: 'Delivery',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            cornService.payloadDeliveryGenerationAndSendS3URL = jest.fn().mockResolvedValue('https://example.com/delivery.pdf');

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBe('https://example.com/delivery.pdf');
            expect(cornService.payloadDeliveryGenerationAndSendS3URL).toHaveBeenCalledWith(mockSchedulerData);
        });

        it('should generate crane report', async () => {
            const mockSchedulerData = {
                reportType: 'Crane',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            cornService.payloadCraneGenerationAndSendS3URL = jest.fn().mockResolvedValue('https://example.com/crane.pdf');

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBe('https://example.com/crane.pdf');
            expect(cornService.payloadCraneGenerationAndSendS3URL).toHaveBeenCalledWith(mockSchedulerData);
        });

        it('should generate concrete report', async () => {
            const mockSchedulerData = {
                reportType: 'Concrete',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            cornService.payloadConcreteGenerationAndSendS3URL = jest.fn().mockResolvedValue('https://example.com/concrete.pdf');

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBe('https://example.com/concrete.pdf');
            expect(cornService.payloadConcreteGenerationAndSendS3URL).toHaveBeenCalledWith(mockSchedulerData);
        });

        it('should generate inspection report', async () => {
            const mockSchedulerData = {
                reportType: 'Inspection',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            cornService.payloadInspectionGenerationAndSendS3URL = jest.fn().mockResolvedValue('https://example.com/inspection.pdf');

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBe('https://example.com/inspection.pdf');
            expect(cornService.payloadInspectionGenerationAndSendS3URL).toHaveBeenCalledWith(mockSchedulerData);
        });

        it('should handle unsupported report type', async () => {
            const mockSchedulerData = {
                reportType: 'Unsupported Report',
            };

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBeUndefined();
        });

        it('should handle errors during report generation', async () => {
            const mockSchedulerData = {
                reportType: 'Heat Map',
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
            };

            cornService.payloadHeatMapGenerationAndSendS3URL = jest.fn().mockRejectedValue(new Error('Report generation failed'));

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBeUndefined();
        });
    });

    describe('checkDailyDigestEmailNotification', () => {
        it('should send digest notifications', async () => {
            const mockTimeZones = [{ id: 1, timezone: 'UTC' }];
            const mockMembers = [{
                id: 1,
                ProjectId: 1,
                time: '10:00',
                timeFormat: 'AM',
                User: { email: '<EMAIL>' },
            }];
            const mockNotifications = [{
                description: 'Test notification',
            }];

            TimeZone.findAll.mockResolvedValue(mockTimeZones);
            Member.findAll.mockResolvedValue(mockMembers);
            DigestNotification.findAll.mockResolvedValue(mockNotifications);
            Project.findOne.mockResolvedValue({ projectName: 'Test Project' });

            await cornService.checkDailyDigestEmailNotification();

            expect(TimeZone.findAll).toHaveBeenCalled();
            expect(Member.findAll).toHaveBeenCalled();
            expect(DigestNotification.findAll).toHaveBeenCalled();
            expect(DigestNotification.update).toHaveBeenCalled();
        });

        it('should not send notifications when no members found', async () => {
            const mockTimeZones = [{ id: 1, timezone: 'UTC' }];

            TimeZone.findAll.mockResolvedValue(mockTimeZones);
            Member.findAll.mockResolvedValue([]);

            await cornService.checkDailyDigestEmailNotification();

            expect(TimeZone.findAll).toHaveBeenCalled();
            expect(Member.findAll).toHaveBeenCalled();
            expect(DigestNotification.findAll).not.toHaveBeenCalled();
        });

        it('should handle no timezones found', async () => {
            TimeZone.findAll.mockResolvedValue([]);

            await cornService.checkDailyDigestEmailNotification();

            expect(TimeZone.findAll).toHaveBeenCalled();
            expect(Member.findAll).not.toHaveBeenCalled();
        });

        it('should handle members with PM time format', async () => {
            const mockTimeZones = [{ id: 1, timezone: 'UTC' }];
            const mockMembers = [{
                id: 1,
                ProjectId: 1,
                time: '02:00',
                timeFormat: 'PM',
                User: { email: '<EMAIL>' },
            }];
            const mockNotifications = [{
                description: 'Test notification',
            }];

            TimeZone.findAll.mockResolvedValue(mockTimeZones);
            Member.findAll.mockResolvedValue(mockMembers);
            DigestNotification.findAll.mockResolvedValue(mockNotifications);
            Project.findOne.mockResolvedValue({ projectName: 'Test Project' });

            await cornService.checkDailyDigestEmailNotification();

            expect(TimeZone.findAll).toHaveBeenCalled();
            expect(Member.findAll).toHaveBeenCalled();
        });

        it('should handle no notifications found', async () => {
            const mockTimeZones = [{ id: 1, timezone: 'UTC' }];
            const mockMembers = [{
                id: 1,
                ProjectId: 1,
                time: '10:00',
                timeFormat: 'AM',
                User: { email: '<EMAIL>' },
            }];

            TimeZone.findAll.mockResolvedValue(mockTimeZones);
            Member.findAll.mockResolvedValue(mockMembers);
            DigestNotification.findAll.mockResolvedValue([]);

            await cornService.checkDailyDigestEmailNotification();

            expect(TimeZone.findAll).toHaveBeenCalled();
            expect(Member.findAll).toHaveBeenCalled();
            expect(DigestNotification.findAll).toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            TimeZone.findAll.mockRejectedValue(new Error('Database error'));

            await expect(cornService.checkDailyDigestEmailNotification()).rejects.toThrow('Database error');
        });
    });

    describe('runtimeScheduler', () => {
        it('should start scheduler jobs for active reports', async () => {
            const mockSchedulerData = [{
                id: 1,
                isDeleted: false,
                isEndDateMeet: false,
                isSaved: false,
            }];

            SchedulerReport.findAll.mockResolvedValue(mockSchedulerData);
            cornService.cronSchedulerJob = jest.fn();

            await cornService.runtimeScheduler();

            expect(SchedulerReport.findAll).toHaveBeenCalled();
            expect(cornService.cronSchedulerJob).toHaveBeenCalled();
        });

        it('should handle no active reports', async () => {
            SchedulerReport.findAll.mockResolvedValue([]);
            cornService.cronSchedulerJob = jest.fn();

            await cornService.runtimeScheduler();

            expect(SchedulerReport.findAll).toHaveBeenCalled();
            expect(cornService.cronSchedulerJob).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            SchedulerReport.findAll.mockRejectedValue(new Error('Database error'));

            await cornService.runtimeScheduler();

            expect(SchedulerReport.findAll).toHaveBeenCalled();
        });
    });

    describe('cronSchedulerJob', () => {
        it('should schedule job for non-repeating report', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);
            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalledWith(
                mockSchedulerData.cronExpression,
                expect.any(Function),
                {
                    name: mockSchedulerData.id,
                    timezone: mockSchedulerData.timezone,
                }
            );
        });

        it('should schedule job for repeating report', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                endDate: '2024-12-31',
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle errors in cronSchedulerJob', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: 'invalid-cron',
                timezone: 'UTC',
            };

            cron.schedule.mockImplementation(() => {
                throw new Error('Invalid cron expression');
            });

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });
    });

    describe('updateProjectStatus', () => {
        it('should update project status and add to overdue list', async () => {
            const overDueProject = [];

            await cornService.updateProjectStatus(1, 'overdue', overDueProject);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 1 } }
            );
            expect(overDueProject).toContain(1);
        });

        it('should handle trial overdue status', async () => {
            const overDueProject = [];

            await cornService.updateProjectStatus(2, 'trialoverdue', overDueProject);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 2 } }
            );
            expect(overDueProject).toContain(2);
        });
    });

    describe('sendMail', () => {
        it('should send trial ending reminder mail', async () => {
            const mockRemainder = [{
                id: 1,
                userDetails: {
                    email: '<EMAIL>',
                    firstName: 'Test'
                },
                projectName: 'Test Project',
                subscribedOn: '2024-03-01'
            }];

            await cornService.sendMail(mockRemainder, 0);

            expect(Project.update).toHaveBeenCalledWith(
                { mailSendOn: expect.any(Date) },
                { where: { id: 1 } }
            );
        });
    });

    describe('sendTrialExpiredMail', () => {
        it('should send trial expired mail', async () => {
            const mockTrialExpired = [{
                id: 1,
                userDetails: {
                    email: '<EMAIL>',
                    firstName: 'Test'
                },
                projectName: 'Test Project',
                subscribedOn: '2024-03-01'
            }];

            await cornService.sendTrialExpiredMail(mockTrialExpired, 0);

            // Should not throw any errors
        });

        it('should handle multiple trial expired mails recursively', async () => {
            const mockTrialExpired = [
                {
                    id: 1,
                    userDetails: { email: '<EMAIL>', firstName: 'Test1' },
                    projectName: 'Test Project 1',
                    subscribedOn: '2024-03-01'
                },
                {
                    id: 2,
                    userDetails: { email: '<EMAIL>', firstName: 'Test2' },
                    projectName: 'Test Project 2',
                    subscribedOn: '2024-03-02'
                }
            ];

            await cornService.sendTrialExpiredMail(mockTrialExpired, 0);

            // Should not throw any errors
        });

        it('should handle empty trial expired array', async () => {
            const mockTrialExpired = [];

            await expect(cornService.sendTrialExpiredMail(mockTrialExpired, 0)).rejects.toThrow();
        });
    });

    describe('sendProjectSubscription', () => {
        it('should send project subscription invoice mail', async () => {
            const mockProjectSubscription = [{
                id: 1,
                userDetails: {
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                projectName: 'Test Project',
                startDate: '2024-03-01',
                endDate: '2024-03-31',
                ParentCompany: {
                    Company: [{
                        address: '123 Test St',
                        city: 'Test City',
                        state: 'Test State',
                        zipCode: '12345',
                        country: 'Test Country'
                    }]
                },
                stripePlan: {
                    stripeAmount: 10000
                }
            }];

            await cornService.sendProjectSubscription(mockProjectSubscription, 0);

            expect(Project.update).toHaveBeenCalledWith(
                { mailSendOn: expect.any(Date) },
                { where: { id: 1 } }
            );
        });

        it('should handle multiple project subscriptions recursively', async () => {
            const mockProjectSubscription = [
                {
                    id: 1,
                    userDetails: { email: '<EMAIL>', firstName: 'Test1', lastName: 'User1' },
                    projectName: 'Test Project 1',
                    startDate: '2024-03-01',
                    endDate: '2024-03-31',
                    ParentCompany: { Company: [{ address: '123 Test St', city: 'Test City', state: 'Test State', zipCode: '12345', country: 'Test Country' }] },
                    stripePlan: { stripeAmount: 10000 }
                },
                {
                    id: 2,
                    userDetails: { email: '<EMAIL>', firstName: 'Test2', lastName: 'User2' },
                    projectName: 'Test Project 2',
                    startDate: '2024-04-01',
                    endDate: '2024-04-30',
                    ParentCompany: { Company: [{ address: '456 Test Ave', city: 'Test City 2', state: 'Test State 2', zipCode: '67890', country: 'Test Country 2' }] },
                    stripePlan: { stripeAmount: 15000 }
                }
            ];

            await cornService.sendProjectSubscription(mockProjectSubscription, 0);

            expect(Project.update).toHaveBeenCalledTimes(1);
        });

        it('should handle empty project subscription array', async () => {
            const mockProjectSubscription = [];

            await expect(cornService.sendProjectSubscription(mockProjectSubscription, 0)).rejects.toThrow();
        });
    });

    describe('handleSpecialProjects', () => {
        it('should mark special project as overdue when end date matches today', async () => {
            const mockElement = {
                id: 31, // Special project ID
                endDate: '2024-03-20' // Today's date from mock
            };
            const overDueProject = [];

            const result = await cornService.handleSpecialProjects(mockElement, overDueProject);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 31 } }
            );
            expect(overDueProject).toContain(31);
        });

        it('should not mark special project as overdue when end date is in future', async () => {
            const mockElement = {
                id: 32, // Special project ID
                endDate: '2024-03-21' // Future date
            };
            const overDueProject = [];

            const result = await cornService.handleSpecialProjects(mockElement, overDueProject);

            // Since 2024-03-21 is in the future (diff returns -1), it should not be marked as overdue
            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should return false for non-special project IDs', async () => {
            const mockElement = {
                id: 999, // Non-special project ID
                endDate: moment().format('YYYY-MM-DD')
            };
            const overDueProject = [];

            const result = await cornService.handleSpecialProjects(mockElement, overDueProject);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should handle all special project IDs', async () => {
            const specialIds = [31, 32, 33, 35, 52];

            for (const id of specialIds) {
                const mockElement = {
                    id: id,
                    endDate: '2024-03-20' // Today's date from mock
                };
                const overDueProject = [];

                const result = await cornService.handleSpecialProjects(mockElement, overDueProject);

                expect(result).toBe(true);
            }
        });
    });

    describe('handleRegularProjects', () => {
        it('should mark regular project as overdue when end date matches today', async () => {
            const today = new Date();
            const mockElement = {
                id: 100, // Non-special project ID
                endDate: today
            };
            const overDueProject = [];

            // Mock Date constructor to return same date for comparison
            const originalDate = global.Date;
            global.Date = jest.fn(() => today);
            global.Date.UTC = originalDate.UTC;
            global.Date.parse = originalDate.parse;
            global.Date.now = originalDate.now;

            const result = await cornService.handleRegularProjects(mockElement, overDueProject);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 100 } }
            );

            global.Date = originalDate;
        });

        it('should not mark regular project as overdue when end date is different', async () => {
            const mockElement = {
                id: 100, // Non-special project ID
                endDate: new Date('2024-01-01')
            };
            const overDueProject = [];

            const result = await cornService.handleRegularProjects(mockElement, overDueProject);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should return false for special project IDs', async () => {
            const mockElement = {
                id: 31, // Special project ID
                endDate: new Date()
            };
            const overDueProject = [];

            const result = await cornService.handleRegularProjects(mockElement, overDueProject);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });
    });

    describe('checkProjectSubscriptionReminder', () => {
        it('should add project to reminder list when end date is 5 days away and no mail sent', async () => {
            const endDate = '2024-03-24'; // 5 days from mocked current date
            const mockElement = {
                id: 1,
                endDate: endDate,
                mailSendOn: null
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            expect(remainderProjectSubscription).toContain(mockElement);
        });

        it('should not add project to reminder list when mail already sent', async () => {
            const endDate = '2024-03-24'; // 5 days from mocked current date
            const mockElement = {
                id: 1,
                endDate: endDate,
                mailSendOn: new Date()
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            expect(remainderProjectSubscription).not.toContain(mockElement);
        });

        it('should not add project to reminder list when end date is not 5 days away', async () => {
            const endDate = '2024-03-22'; // 3 days from mocked current date
            const mockElement = {
                id: 1,
                endDate: endDate,
                mailSendOn: null
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            // Since our mock returns -3 for 2024-03-22, and the logic checks for exactly 5 days,
            // this should not be added to the reminder list
            expect(remainderProjectSubscription).toHaveLength(0);
        });

        it('should handle edge case with exact 5 day difference', async () => {
            const endDate = '2024-03-24'; // 5 days from mocked current date
            const mockElement = {
                id: 1,
                endDate: endDate,
                mailSendOn: false // falsy value
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            expect(remainderProjectSubscription).toContain(mockElement);
        });
    });

    describe('handleTrialExpiration', () => {
        it('should mark project 20 as trial overdue after 75 days', async () => {
            const mockElement = { id: 20 };
            const diffDays = 75;
            const subDetail = null;
            const overDueProject = [];
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, subDetail, overDueProject, expiredTrialPlans);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 20 } }
            );
        });

        it('should mark special project as trial overdue when end date matches today', async () => {
            const mockElement = {
                id: 34, // Special project ID
                endDate: '2024-03-20' // Today's date from mock
            };
            const diffDays = 10;
            const subDetail = null;
            const overDueProject = [];
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, subDetail, overDueProject, expiredTrialPlans);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 34 } }
            );
        });

        it('should mark non-special project as trial overdue after 14 days without subscription', async () => {
            const mockElement = { id: 100 }; // Non-special project
            const diffDays = 14;
            const subDetail = null;
            const overDueProject = [];
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, subDetail, overDueProject, expiredTrialPlans);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 100 } }
            );
        });

        it('should mark project as trial overdue when subscription trial ended', async () => {
            const mockElement = { id: 100 };
            const diffDays = 14;
            const subDetail = {
                status: 'active',
                trial_end: Math.floor(Date.now() / 1000) - 86400 // 1 day ago
            };
            const overDueProject = [];
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, subDetail, overDueProject, expiredTrialPlans);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 100 } }
            );
        });

        it('should add project to expired trial plans when diffDays equals 14', async () => {
            const mockElement = { id: 100 };
            const diffDays = 14;
            const subDetail = {
                status: 'trialing',
                trial_end: Math.floor(Date.now() / 1000) + 86400 // 1 day from now
            };
            const overDueProject = [];
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, subDetail, overDueProject, expiredTrialPlans);

            expect(expiredTrialPlans).toContain(mockElement);
        });

        it('should not mark project as overdue when trial is still active', async () => {
            const mockElement = { id: 100 };
            const diffDays = 14;
            const subDetail = {
                status: 'trialing',
                trial_end: Math.floor(Date.now() / 1000) + 86400 // 1 day from now
            };
            const overDueProject = [];
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, subDetail, overDueProject, expiredTrialPlans);

            expect(Project.update).not.toHaveBeenCalled();
        });
    });

    describe('getSchedulerTimelineNames', () => {
        it('should return scheduler date ranges successfully', async () => {
            const mockDateRanges = [
                { id: 1, name: 'Today' },
                { id: 2, name: 'Next 7 Days' }
            ];

            SchedulerDateRange.findAll.mockResolvedValue(mockDateRanges);

            const done = jest.fn();
            await cornService.getSchedulerTimelineNames({}, done);

            expect(SchedulerDateRange.findAll).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(mockDateRanges, false);
        });

        it('should handle no data found', async () => {
            SchedulerDateRange.findAll.mockResolvedValue(null);

            const done = jest.fn();
            await cornService.getSchedulerTimelineNames({}, done);

            expect(done).toHaveBeenCalledWith('No data found', false);
        });

        it('should handle database errors', async () => {
            SchedulerDateRange.findAll.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();
            await cornService.getSchedulerTimelineNames({}, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('utility methods', () => {
        const mockSchedulerData = {
            customStartDate: null,
            customEndDate: null,
            startDate: '2024-03-20/10:00:00',
            endDate: '2024-03-21/11:00:00'
        };

        describe('getStartDate', () => {
            it('should return custom start date when available', () => {
                const data = { ...mockSchedulerData, customStartDate: '2024-03-15' };
                const result = cornService.getStartDate(data);
                expect(result).toBe('2024-03-15');
            });

            it('should return start date from startDate when no custom date', () => {
                const result = cornService.getStartDate(mockSchedulerData);
                expect(result).toBe('2024-03-20');
            });
        });

        describe('getEndDate', () => {
            it('should return custom end date when available', () => {
                const data = { ...mockSchedulerData, customEndDate: '2024-03-25' };
                const result = cornService.getEndDate(data);
                expect(result).toBe('2024-03-25');
            });

            it('should return end date from endDate when no custom date', () => {
                const result = cornService.getEndDate(mockSchedulerData);
                expect(result).toBe('2024-03-21');
            });
        });

        describe('getCurrentStart', () => {
            it('should return formatted current start date and time', () => {
                const result = cornService.getCurrentStart(mockSchedulerData);
                expect(result).toBe('2024-03-20 10:00:00');
            });
        });

        describe('getCurrentEnd', () => {
            it('should return formatted current end date and time', () => {
                const result = cornService.getCurrentEnd(mockSchedulerData);
                expect(result).toBe('2024-03-21 11:00:00');
            });
        });
    });

    describe('getConcreteReportPayload', () => {
        it('should generate concrete report payload correctly', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                firstName: 'Test',
                lastName: 'User',
                truckspacingFilter: 'test',
                slumpFilter: 'test',
                mixDesignFilter: 'test',
                orderNumberFilter: 'test',
                primerFilter: 'test',
                quantityFilter: 'test',
                locationFilter: 'test',
                descriptionFilter: 'test',
                sort: 'ASC',
                sortByField: 'name',
                queuedNdr: true,
                selectedHeaders: '[]',
                companyFilter: 'test',
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'active',
                memberFilterId: 1,
                gateId: 1,
                equipmentId: 1,
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Test Report'
            };

            const result = await cornService.getConcreteReportPayload(mockSchedulerData);

            expect(result).toHaveProperty('params');
            expect(result).toHaveProperty('user');
            expect(result).toHaveProperty('body');
            expect(result).toHaveProperty('headers');
            expect(result.params.ProjectId).toBe(1);
            expect(result.user.id).toBe(1);
            expect(result.body.startdate).toBe('2024-03-20');
            expect(result.body.enddate).toBe('2024-03-21');
        });

        it('should use default values when optional fields are missing', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Test Report'
            };

            const result = await cornService.getConcreteReportPayload(mockSchedulerData);

            expect(result.body.sort).toBe('DESC');
            expect(result.body.sortByField).toBe('id');
            expect(result.body.queuedNdr).toBe(false);
            expect(result.user.firstName).toBe('Test');
            expect(result.user.lastName).toBe('');
        });
    });

    describe('payloadInspectionGenerationAndSendS3URL', () => {
        it('should generate inspection report successfully', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Test Report'
            };

            const mockInspectionService = {
                exportReportForScheduler: jest.fn().mockResolvedValue('https://example.com/inspection.pdf')
            };

            // Mock the require call
            jest.doMock('.', () => ({
                inspectionReportService: mockInspectionService
            }), { virtual: true });

            const result = await cornService.payloadInspectionGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/inspection.pdf');
        });

        it('should handle errors during inspection report generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' }
            };

            // Mock the service to throw an error
            inspectionReportService.exportReportForScheduler.mockRejectedValueOnce(new Error('Report generation failed'));

            const result = await cornService.payloadInspectionGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeInstanceOf(Error);
        });
    });

    describe('cronSchedulerJob - additional edge cases', () => {
        it('should handle repeating job with dateRangeId 1 (today)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: '2024-03-21' // Tomorrow from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);
            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle repeating job with dateRangeId 2 (next 7 days)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Weekly',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 2,
                recurrenceEndDate: '2024-04-20' // Next month from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);
            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle repeating job with dateRangeId 3 (next 15 days)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 3,
                recurrenceEndDate: '2024-04-20' // Next month from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle repeating job with dateRangeId 4 (this month)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Monthly',
                cronExpression: '0 10 1 * *',
                timezone: 'UTC',
                dateRangeId: 4,
                recurrenceEndDate: '2024-05-20' // Two months from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle repeating job with dateRangeId 5 (next month)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Monthly',
                cronExpression: '0 10 1 * *',
                timezone: 'UTC',
                dateRangeId: 5,
                recurrenceEndDate: '2024-06-20' // Three months from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle repeating job with dateRangeId 6 (last 7 days)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Weekly',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 6,
                recurrenceEndDate: '2024-04-20' // Next month from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle repeating job with dateRangeId 7 (last 15 days)', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 7,
                recurrenceEndDate: '2024-04-20' // Next month from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should stop job when recurrence end date has passed', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: '2024-03-18' // Yesterday from mock
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });

        it('should handle no data found in report for repeating job', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: moment().add(1, 'day').format('YYYY-MM-DD')
            };

            const mockJob = { stop: jest.fn() };
            cron.schedule.mockReturnValue(mockJob);
            cornService.generateReportAll = jest.fn().mockResolvedValue('invalid-url');

            await cornService.cronSchedulerJob(mockSchedulerData);

            expect(cron.schedule).toHaveBeenCalled();
        });
    });

    describe('checkProject', () => {
        it('should process project with PlanId 2 recursively', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'active',
                    PlanId: 2,
                    StripeSubscription: { subscriptionId: 'sub_123' },
                    endDate: '2024-03-20',
                    mailSendOn: null
                },
                {
                    id: 2,
                    status: 'active',
                    PlanId: 1,
                    subscribedOn: '2024-03-01'
                }
            ];

            mockStripeRetrieve.mockResolvedValue({
                status: 'active',
                collection_method: 'charge_automatically'
            });

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (response, err) => {
                    expect(err).toBe(false);
                    expect(response).toHaveProperty('overdue');
                    expect(response).toHaveProperty('remainder');
                    expect(response).toHaveProperty('trialExpired');
                    expect(response).toHaveProperty('projectSubscription');
                    done();
                }
            );
        });

        it('should skip overdue projects', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'overdue',
                    PlanId: 2
                }
            ];

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (response, err) => {
                    expect(err).toBe(false);
                    expect(response.overdue).toEqual([]);
                    done();
                }
            );
        });

        it('should handle trial projects', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'active',
                    PlanId: 1,
                    subscribedOn: '2024-03-01',
                    mailSendOn: '2024-03-05'
                }
            ];

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (_, err) => {
                    expect(err).toBe(false);
                    done();
                }
            );
        });

        it('should skip trialoverdue projects', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'trialoverdue',
                    PlanId: 1
                }
            ];

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (response, err) => {
                    expect(err).toBe(false);
                    expect(response.overdue).toEqual([]);
                    done();
                }
            );
        });

        it('should handle project with PlanId 3', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'active',
                    PlanId: 3,
                    StripeSubscription: { subscriptionId: 'sub_123' },
                    endDate: '2024-03-20'
                }
            ];

            mockStripeRetrieve.mockResolvedValue({
                status: 'active',
                collection_method: 'charge_automatically'
            });

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (_, err) => {
                    expect(err).toBe(false);
                    done();
                }
            );
        });

        it('should handle project with unknown PlanId', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'active',
                    PlanId: 999 // Unknown plan ID
                }
            ];

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (response, err) => {
                    expect(err).toBe(false);
                    expect(response.overdue).toEqual([]);
                    done();
                }
            );
        });

        it('should handle single project (no recursion)', (done) => {
            const mockProjectDetail = [
                {
                    id: 1,
                    status: 'active',
                    PlanId: 1,
                    subscribedOn: '2024-03-01'
                }
            ];

            cornService.checkProject(
                mockProjectDetail,
                0,
                [],
                [],
                [],
                [],
                (response, err) => {
                    expect(err).toBe(false);
                    expect(response).toHaveProperty('overdue');
                    expect(response).toHaveProperty('remainder');
                    expect(response).toHaveProperty('trialExpired');
                    expect(response).toHaveProperty('projectSubscription');
                    done();
                }
            );
        });
    });

    describe('sendEmail', () => {
        it('should send email when notifications exist', async () => {
            const mockMemberData = {
                id: 1,
                ProjectId: 1,
                User: { email: '<EMAIL>' }
            };
            const mockNotifications = [
                { description: 'Test notification 1' },
                { description: 'Test notification 2' }
            ];

            Project.findOne.mockResolvedValue({
                projectName: 'Test Project'
            });

            await cornService.sendEmail(mockMemberData, mockNotifications);

            expect(Project.findOne).toHaveBeenCalled();
        });

        it('should return true when no notifications exist', async () => {
            const mockMemberData = {
                id: 1,
                ProjectId: 1,
                User: { email: '<EMAIL>' }
            };

            const result = await cornService.sendEmail(mockMemberData, []);

            expect(result).toBe(true);
        });

        it('should handle single notification', async () => {
            const mockMemberData = {
                id: 1,
                ProjectId: 1,
                User: { email: '<EMAIL>' }
            };
            const mockNotifications = [
                { description: 'Single notification' }
            ];

            Project.findOne.mockResolvedValue({
                projectName: 'Test Project'
            });

            await cornService.sendEmail(mockMemberData, mockNotifications);

            expect(Project.findOne).toHaveBeenCalledWith({
                where: { isDeleted: false, id: 1 }
            });
        });

        it('should handle project not found', async () => {
            const mockMemberData = {
                id: 1,
                ProjectId: 1,
                User: { email: '<EMAIL>' }
            };
            const mockNotifications = [
                { description: 'Test notification' }
            ];

            Project.findOne.mockResolvedValue(null);

            await expect(cornService.sendEmail(mockMemberData, mockNotifications)).rejects.toThrow();
        });

        it('should handle database errors', async () => {
            const mockMemberData = {
                id: 1,
                ProjectId: 1,
                User: { email: '<EMAIL>' }
            };
            const mockNotifications = [
                { description: 'Test notification' }
            ];

            Project.findOne.mockRejectedValue(new Error('Database error'));

            await expect(cornService.sendEmail(mockMemberData, mockNotifications)).rejects.toThrow('Database error');
        });

        it('should handle mailer errors', async () => {
            const mockMemberData = {
                id: 1,
                ProjectId: 1,
                User: { email: '<EMAIL>' }
            };
            const mockNotifications = [
                { description: 'Test notification' }
            ];

            Project.findOne.mockResolvedValue({
                projectName: 'Test Project'
            });

            const MAILER = require('../../mailer');
            MAILER.sendMail.mockImplementation((_, __, ___, ____, callback) => {
                callback(new Error('Mail sending failed'));
            });

            await cornService.sendEmail(mockMemberData, mockNotifications);

            // Should handle the error gracefully
        });
    });

    describe('validateWeeklyCalendarReport', () => {
        it('should throw error for PDF with more than 12 hours difference', async () => {
            const mockData = {
                reportType: 'Weekly Calendar',
                outputFormat: 'PDF',
                startTime: '08:00:00',
                endTime: '22:00:00'
            };

            await expect(cornService.validateWeeklyCalendarReport(mockData))
                .rejects.toThrow('Currently, we are not supporting more than 12 hours time difference');
        });

        it('should not throw error for non-PDF reports', async () => {
            const mockData = {
                reportType: 'Weekly Calendar',
                outputFormat: 'Excel',
                startTime: '08:00:00',
                endTime: '22:00:00'
            };

            await expect(cornService.validateWeeklyCalendarReport(mockData))
                .resolves.not.toThrow();
        });

        it('should not throw error for non-Weekly Calendar reports', async () => {
            const mockData = {
                reportType: 'Heat Map',
                outputFormat: 'PDF',
                startTime: '08:00:00',
                endTime: '22:00:00'
            };

            await expect(cornService.validateWeeklyCalendarReport(mockData))
                .resolves.not.toThrow();
        });

        it('should not throw error for PDF with less than 12 hours difference', async () => {
            const mockData = {
                reportType: 'Weekly Calendar',
                outputFormat: 'PDF',
                startTime: '08:00:00',
                endTime: '18:00:00'
            };

            await expect(cornService.validateWeeklyCalendarReport(mockData))
                .resolves.not.toThrow();
        });
    });

    describe('getSchedulerReportRequest', () => {
        it('should get scheduler reports successfully', async () => {
            const mockInputData = {
                query: {
                    pageNo: 1,
                    pageSize: 10,
                    ProjectId: 1,
                    sortByField: 'id',
                    sort: 'DESC',
                    createdUserId: 1,
                    reportName: 'Test',
                    templateType: 'Weekly',
                    lastRun: '2024-03-20',
                    search: 'test',
                    timezone: 'UTC'
                },
                body: { saved: true }
            };

            const mockReports = {
                scheduledData: [{ id: 1, reportName: 'Test Report' }],
                count: 1
            };

            SchedulerReport.getAll.mockResolvedValue(mockReports);

            const done = jest.fn();
            await cornService.getSchedulerReportRequest(mockInputData, done);

            expect(SchedulerReport.getAll).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(
                { scheduledReports: mockReports.scheduledData, count: mockReports.count },
                false
            );
        });

        it('should handle errors in getSchedulerReportRequest', async () => {
            const mockInputData = {
                query: { ProjectId: 1 },
                body: {}
            };

            SchedulerReport.getAll.mockRejectedValue(new Error('Database error'));

            const done = jest.fn();
            await cornService.getSchedulerReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('getRerunReportRequest', () => {
        it('should rerun report successfully', async () => {
            const mockInputData = {
                query: { id: 1, ProjectId: 1 }
            };

            const mockSchedulerData = {
                id: 1,
                reportType: 'Heat Map',
                createdUser: { email: '<EMAIL>' }
            };

            SchedulerReport.findOne.mockResolvedValue(mockSchedulerData);
            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');

            const done = jest.fn();
            await cornService.getRerunReportRequest(mockInputData, done);

            expect(SchedulerReport.findOne).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith('https://example.com/report.pdf', false);
        });

        it('should handle invalid URL format', async () => {
            const mockInputData = {
                query: { id: 1, ProjectId: 1 }
            };

            const mockSchedulerData = {
                id: 1,
                reportType: 'Heat Map'
            };

            SchedulerReport.findOne.mockResolvedValue(mockSchedulerData);
            cornService.generateReportAll = jest.fn().mockResolvedValue('invalid-url');

            const done = jest.fn();
            await cornService.getRerunReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith('No data found', false);
        });

        it('should handle no scheduler data found', async () => {
            const mockInputData = {
                query: { id: 1, ProjectId: 1 }
            };

            SchedulerReport.findOne.mockResolvedValue(null);

            const done = jest.fn();
            await cornService.getRerunReportRequest(mockInputData, done);

            expect(done).toHaveBeenCalledWith('No data found', false);
        });
    });

    describe('createBaseSchedulerData', () => {
        it('should create base scheduler data correctly', () => {
            const mockInputData = {
                params: { ProjectId: 1 },
                user: { id: 1 },
                body: {
                    reportName: 'Test Report',
                    reportType: 'Heat Map',
                    outputFormat: 'PDF',
                    recurrence: 'Daily',
                    repeatEvery: { day: 1 },
                    sendTo: '<EMAIL>',
                    subject: 'Test Subject',
                    message: 'Test Message',
                    timezone: 'UTC',
                    startDate: '2024-03-20',
                    startTime: '10:00:00',
                    endDate: '2024-03-21',
                    endTime: '11:00:00'
                }
            };

            const result = cornService.createBaseSchedulerData(mockInputData);

            expect(result).toHaveProperty('reportName', 'Test Report');
            expect(result).toHaveProperty('ProjectId', 1);
            expect(result).toHaveProperty('createdBy', 1);
            expect(result).toHaveProperty('timezone', 'UTC');
        });
    });

    describe('generateCronExpression', () => {
        it('should return null when no runReportAt provided', () => {
            const mockData = { recurrence: 'Daily' };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateCronExpression(mockData, mockServerDate);

            expect(result).toBeNull();
        });

        it('should generate cron for non-repeating report', () => {
            const mockData = {
                runReportAt: '2024-03-20 10:30:00',
                recurrence: 'Does Not Repeat'
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateCronExpression(mockData, mockServerDate);

            expect(result).toBe('0 10 * * *');
        });

        it('should generate recurring cron expression', () => {
            const mockData = {
                runReportAt: '2024-03-20 10:30:00',
                recurrence: 'Daily',
                repeatEvery: { day: 1 }
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateCronExpression(mockData, mockServerDate);

            expect(result).toBe('30 10 */1 * *');
        });
    });

    describe('generateRecurringCronExpression', () => {
        it('should generate daily cron expression', () => {
            const mockData = {
                recurrence: 'Daily',
                repeatEvery: { day: 2 }
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateRecurringCronExpression(mockData, mockServerDate);

            expect(result).toBe('30 10 */2 * *');
        });

        it('should generate weekly cron expression', () => {
            const mockData = {
                recurrence: 'Weekly',
                repeatEvery: { day: 1, specificDays: [1, 3, 5] }
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateRecurringCronExpression(mockData, mockServerDate);

            expect(result).toBe('30 10 * * 1,3,5/1');
        });

        it('should generate monthly cron expression', () => {
            const mockData = {
                recurrence: 'Monthly',
                repeatEvery: { options: 'on_day', specificDays: { day: 15 }, day: 1 }
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateRecurringCronExpression(mockData, mockServerDate);

            expect(result).toBe('30 10 15 */1 *');
        });

        it('should generate yearly cron expression', () => {
            const mockData = {
                recurrence: 'Yearly',
                repeatEvery: { options: 'on_day', specificDays: { day: 15, month: 6 } }
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateRecurringCronExpression(mockData, mockServerDate);

            expect(result).toBe('30 10 15 6 *');
        });

        it('should return null for unsupported recurrence', () => {
            const mockData = {
                recurrence: 'Unsupported',
                repeatEvery: {}
            };
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateRecurringCronExpression(mockData, mockServerDate);

            expect(result).toBeNull();
        });
    });

    describe('generateDailyCronExpression', () => {
        it('should generate daily cron expression', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };

            const result = cornService.generateDailyCronExpression(mockServerDate, 2);

            expect(result).toBe('30 10 */2 * *');
        });
    });

    describe('generateWeeklyCronExpression', () => {
        it('should generate weekly cron expression', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };
            const mockRepeatEvery = { day: 1, specificDays: [1, 3, 5] };

            const result = cornService.generateWeeklyCronExpression(mockServerDate, mockRepeatEvery);

            expect(result).toBe('30 10 * * 1,3,5/1');
        });
    });

    describe('generateMonthlyCronExpression', () => {
        it('should generate monthly cron expression for on_day option', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };
            const mockRepeatEvery = { options: 'on_day', specificDays: { day: 15 }, day: 1 };

            const result = cornService.generateMonthlyCronExpression(mockServerDate, mockRepeatEvery);

            expect(result).toBe('30 10 15 */1 *');
        });

        it('should generate monthly cron expression for on_the option', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };
            const mockRepeatEvery = {
                options: 'on_the',
                specificDays: { order: 'first', specificDay: 'monday' },
                day: 1
            };

            const result = cornService.generateMonthlyCronExpression(mockServerDate, mockRepeatEvery);

            expect(result).toBe('0 10 1 * *');
        });
    });

    describe('generateYearlyCronExpression', () => {
        it('should generate yearly cron expression for on_day option', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };
            const mockRepeatEvery = {
                options: 'on_day',
                specificDays: { day: 15, month: 6 }
            };

            const result = cornService.generateYearlyCronExpression(mockServerDate, mockRepeatEvery);

            expect(result).toBe('30 10 15 6 *');
        });

        it('should generate yearly cron expression for on_the option', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };
            const mockRepeatEvery = {
                options: 'on_the',
                specificDays: { order: 'first', specificDay: 'monday', month: 6 }
            };

            const result = cornService.generateYearlyCronExpression(mockServerDate, mockRepeatEvery);

            expect(result).toBe('0 10 1 1 *');
        });

        it('should return null for unsupported yearly option', () => {
            const mockServerDate = { minute: () => 30, hour: () => 10 };
            const mockRepeatEvery = {
                options: 'unsupported',
                specificDays: {}
            };

            const result = cornService.generateYearlyCronExpression(mockServerDate, mockRepeatEvery);

            expect(result).toBeNull();
        });
    });

    describe('sendProjectSubscription', () => {
        it('should send project subscription mail', async () => {
            const mockProjectSubscription = [{
                id: 1,
                userDetails: {
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                ParentCompany: {
                    Company: [{
                        address: '123 Test St',
                        city: 'Test City',
                        state: 'Test State',
                        zipCode: '12345',
                        country: 'Test Country'
                    }]
                },
                projectName: 'Test Project',
                startDate: '2024-03-01',
                endDate: '2024-03-31',
                stripePlan: {
                    stripeAmount: 10000
                }
            }];

            await cornService.sendProjectSubscription(mockProjectSubscription, 0);

            expect(Project.update).toHaveBeenCalledWith(
                { mailSendOn: expect.any(Date) },
                { where: { id: 1 } }
            );
        });
    });

    describe('handleSpecialProjects', () => {
        it('should mark special project as overdue when end date matches today', async () => {
            const mockElement = {
                id: 31,
                endDate: moment().format('YYYY-MM-DD')
            };

            const result = await cornService.handleSpecialProjects(mockElement, []);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 31 } }
            );
        });

        it('should return false for non-special project', async () => {
            const mockElement = {
                id: 999,
                endDate: moment().format('YYYY-MM-DD')
            };

            const result = await cornService.handleSpecialProjects(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should return false when end date does not match today', async () => {
            const mockElement = {
                id: 31,
                endDate: moment().add(1, 'day').format('YYYY-MM-DD')
            };

            const result = await cornService.handleSpecialProjects(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });
    });

    describe('handleRegularProjects', () => {
        it('should mark regular project as overdue when end date matches today', async () => {
            const mockElement = {
                id: 999,
                endDate: new Date()
            };

            const result = await cornService.handleRegularProjects(mockElement, []);

            expect(result).toBe(true);
            expect(Project.update).toHaveBeenCalledWith(
                { status: 'overdue' },
                { where: { id: 999 } }
            );
        });

        it('should return false for special project', async () => {
            const mockElement = {
                id: 31,
                endDate: new Date()
            };

            const result = await cornService.handleRegularProjects(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });

        it('should return false when end date does not match today', async () => {
            const mockElement = {
                id: 999,
                endDate: new Date(Date.now() + 86400000) // tomorrow
            };

            const result = await cornService.handleRegularProjects(mockElement, []);

            expect(result).toBe(false);
            expect(Project.update).not.toHaveBeenCalled();
        });
    });

    describe('checkProjectSubscriptionReminder', () => {
        it('should add project to reminder when 5 days before end date', async () => {
            const mockElement = {
                endDate: moment().add(5, 'days').format('YYYY-MM-DD'),
                mailSendOn: null
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            expect(remainderProjectSubscription).toContain(mockElement);
        });

        it('should not add project when mail already sent', async () => {
            const mockElement = {
                endDate: moment().add(5, 'days').format('YYYY-MM-DD'),
                mailSendOn: new Date()
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            expect(remainderProjectSubscription).not.toContain(mockElement);
        });

        it('should not add project when not 5 days before end date', async () => {
            const mockElement = {
                endDate: moment().add(3, 'days').format('YYYY-MM-DD'),
                mailSendOn: null
            };
            const remainderProjectSubscription = [];

            await cornService.checkProjectSubscriptionReminder(mockElement, remainderProjectSubscription);

            expect(remainderProjectSubscription).not.toContain(mockElement);
        });
    });

    describe('handleTrialExpiration', () => {
        it('should mark project 20 as overdue when 75+ days', async () => {
            const mockElement = { id: 20 };
            const diffDays = 76;

            await cornService.handleTrialExpiration(mockElement, diffDays, null, [], []);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 20 } }
            );
        });

        it('should mark special project as overdue when end date matches today', async () => {
            const mockElement = {
                id: 34,
                endDate: moment().format('YYYY-MM-DD')
            };
            const diffDays = 10;

            await cornService.handleTrialExpiration(mockElement, diffDays, null, [], []);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 34 } }
            );
        });

        it('should mark regular project as overdue when 14+ days and no subscription', async () => {
            const mockElement = { id: 999 };
            const diffDays = 15;

            await cornService.handleTrialExpiration(mockElement, diffDays, null, [], []);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 999 } }
            );
        });

        it('should mark project as overdue when subscription not trialing and trial ended', async () => {
            const mockElement = { id: 999 };
            const diffDays = 15;
            const mockSubDetail = {
                status: 'active',
                trial_end: Math.floor(Date.now() / 1000) - 86400 // 1 day ago
            };

            await cornService.handleTrialExpiration(mockElement, diffDays, mockSubDetail, [], []);

            expect(Project.update).toHaveBeenCalledWith(
                { status: 'trialoverdue' },
                { where: { id: 999 } }
            );
        });

        it('should add to expired trial plans when exactly 14 days', async () => {
            const mockElement = { id: 999 };
            const diffDays = 14;
            const expiredTrialPlans = [];

            await cornService.handleTrialExpiration(mockElement, diffDays, null, [], expiredTrialPlans);

            expect(expiredTrialPlans).toContain(mockElement);
        });

        it('should not mark project as overdue when subscription is trialing', async () => {
            const mockElement = { id: 999 };
            const diffDays = 15;
            const mockSubDetail = {
                status: 'trialing',
                trial_end: Math.floor(Date.now() / 1000) + 86400 // 1 day from now
            };

            await cornService.handleTrialExpiration(mockElement, diffDays, mockSubDetail, [], []);

            expect(Project.update).not.toHaveBeenCalled();
        });
    });

    describe('Edge cases and error handling', () => {
        it('should handle null/undefined values in handleStripeSubscription', async () => {
            const mockElement = {
                id: 1,
                StripeSubscription: null
            };
            const overDueProject = [];

            const result = await cornService.handleStripeSubscription(mockElement, overDueProject);

            expect(result).toBe(false);
        });

        it('should handle missing endDate in handleSpecialProjects', async () => {
            const mockElement = {
                id: 31,
                endDate: null
            };
            const overDueProject = [];

            const result = await cornService.handleSpecialProjects(mockElement, overDueProject);

            expect(result).toBe(false);
        });

        it('should handle missing subscribedOn in handleTrialPlan', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: null,
                mailSendOn: null
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);

            // Should handle gracefully without throwing
        });

        it('should handle malformed date strings', async () => {
            const mockElement = {
                id: 1,
                subscribedOn: 'invalid-date',
                mailSendOn: 'invalid-date'
            };

            await cornService.handleTrialPlan(mockElement, [], [], []);

            // Should handle gracefully without throwing
        });

        it('should handle empty arrays in sendMail', async () => {
            await expect(cornService.sendMail([], 0)).rejects.toThrow();
        });

        it('should handle negative index in sendMail', async () => {
            const mockData = [{
                id: 1,
                userDetails: { email: '<EMAIL>', firstName: 'Test' },
                projectName: 'Test Project',
                subscribedOn: '2024-03-01'
            }];

            await expect(cornService.sendMail(mockData, -1)).rejects.toThrow();
        });

        it('should handle out of bounds index in sendMail', async () => {
            const mockData = [{
                id: 1,
                userDetails: { email: '<EMAIL>', firstName: 'Test' },
                projectName: 'Test Project',
                subscribedOn: '2024-03-01'
            }];

            await expect(cornService.sendMail(mockData, 5)).rejects.toThrow();
        });

        it('should handle missing user details in sendMail', async () => {
            const mockData = [{
                id: 1,
                userDetails: null,
                projectName: 'Test Project',
                subscribedOn: '2024-03-01'
            }];

            await expect(cornService.sendMail(mockData, 0)).rejects.toThrow();
        });

        it('should handle missing project name in sendMail', async () => {
            const mockData = [{
                id: 1,
                userDetails: { email: '<EMAIL>', firstName: 'Test' },
                projectName: null,
                subscribedOn: '2024-03-01'
            }];

            await cornService.sendMail(mockData, 0);

            // Should handle gracefully
        });

        it('should handle timezone errors in checkDailyDigestEmailNotification', async () => {
            const mockTimeZones = [{ id: 1, timezone: 'Invalid/Timezone' }];

            TimeZone.findAll.mockResolvedValue(mockTimeZones);

            await cornService.checkDailyDigestEmailNotification();

            // Should handle gracefully without throwing
        });

        it('should handle missing User association in Member', async () => {
            const mockTimeZones = [{ id: 1, timezone: 'UTC' }];
            const mockMembers = [{
                id: 1,
                ProjectId: 1,
                time: '10:00',
                timeFormat: 'AM',
                User: null
            }];

            TimeZone.findAll.mockResolvedValue(mockTimeZones);
            Member.findAll.mockResolvedValue(mockMembers);

            await cornService.checkDailyDigestEmailNotification();

            // Should handle gracefully without throwing
        });

        it('should handle missing ConcreteRequest update in checkNDRExpiration', async () => {
            ConcreteRequest.update.mockRejectedValue(new Error('ConcreteRequest update failed'));

            await expect(cornService.checkNDRExpiration()).rejects.toThrow('ConcreteRequest update failed');
        });

        it('should handle invalid cron expressions in cronSchedulerJob', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: null,
                timezone: 'UTC'
            };

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Should handle gracefully
        });

        it('should handle missing timezone in cronSchedulerJob', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: '0 10 * * *',
                timezone: null
            };

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Should handle gracefully
        });

        it('should handle generateReportAll returning undefined', async () => {
            const mockSchedulerData = {
                reportType: 'Unknown Report Type'
            };

            const result = await cornService.generateReportAll(mockSchedulerData);

            expect(result).toBeUndefined();
        });

        it('should handle missing createdUser in payload methods', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: null,
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Test Report'
            };

            const result = await cornService.getConcreteReportPayload(mockSchedulerData);

            expect(result.user.firstName).toBe('Test');
            expect(result.user.lastName).toBe('');
        });
    });

    describe('payloadCraneGenerationAndSendS3URL', () => {
        it('should generate crane report successfully', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                firstName: 'Test',
                lastName: 'User',
                descriptionFilter: 'test description',
                sort: 'ASC',
                sortByField: 'CraneRequestId',
                queuedNdr: true,
                pickFrom: 'Location A',
                pickTo: 'Location B',
                idFilter: 5,
                selectedHeaders: '["header1", "header2"]',
                companyFilter: 'Test Company',
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'active',
                memberFilterId: 1,
                gateId: 1,
                equipmentFilter: 'crane1',
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Crane Report',
                locationFilter: 'test location'
            };

            const result = await cornService.payloadCraneGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/crane.pdf');
        });

        it('should handle errors in crane report generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC'
            };

            // Mock the service to throw an error
            craneReportService.exportReportForScheduler.mockRejectedValueOnce(new Error('Crane service error'));

            const result = await cornService.payloadCraneGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeInstanceOf(Error);
        });

        it('should use default values for missing crane data', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Test Report'
            };

            await cornService.payloadCraneGenerationAndSendS3URL(mockSchedulerData);

            expect(craneReportService.exportReportForScheduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        descriptionFilter: '',
                        sort: 'DESC',
                        sortByField: 'CraneRequestId',
                        queuedNdr: false,
                        pickFrom: '',
                        pickTo: '',
                        idFilter: 0
                    })
                })
            );
        });
    });

    describe('payloadDeliveryGenerationAndSendS3URL', () => {
        it('should generate delivery report successfully', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                descriptionFilter: 'test description',
                sort: 'ASC',
                sortByField: 'deliveryId',
                queuedNdr: true,
                selectedHeaders: '["header1"]',
                companyId: 1,
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'completed',
                memberFilterId: 1,
                gateId: 1,
                equipmentId: 1,
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Delivery Report',
                locationFilter: 'warehouse'
            };

            const result = await cornService.payloadDeliveryGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/delivery.pdf');
        });

        it('should handle errors in delivery report generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00'
            };

            // Mock the service to throw an error
            deliveryReportService.exportReportForScheduler.mockRejectedValueOnce(new Error('Delivery service error'));

            const result = await cornService.payloadDeliveryGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeInstanceOf(Error);
        });

        it('should use default values for missing delivery data', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC'
            };

            await cornService.payloadDeliveryGenerationAndSendS3URL(mockSchedulerData);

            expect(deliveryReportService.exportReportForScheduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        descriptionFilter: '',
                        sort: 'DESC',
                        sortByField: 'id',
                        queuedNdr: false,
                        statusFilter: ''
                    })
                })
            );
        });
    });

    describe('payloadWeeklyCalendarGenerationAndSendS3URL', () => {
        it('should generate weekly calendar report successfully', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                companyId: 1,
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'active',
                memberFilterId: 1,
                gateId: 1,
                equipmentId: 1,
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Weekly Calendar',
                locationFilter: 'office'
            };

            const result = await cornService.payloadWeeklyCalendarGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/calendar.pdf');
        });

        it('should handle errors in weekly calendar generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00'
            };

            // Mock the service to throw an error
            deliveryReportService.exportWeeklyCalendarReportForScheduler.mockRejectedValueOnce(new Error('Calendar service error'));

            const result = await cornService.payloadWeeklyCalendarGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeUndefined();
        });

        it('should use default values for missing calendar data', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC'
            };

            await cornService.payloadWeeklyCalendarGenerationAndSendS3URL(mockSchedulerData);

            expect(deliveryReportService.exportWeeklyCalendarReportForScheduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        statusFilter: '',
                        startDate: '2024-03-15',
                        endDate: '2024-03-16'
                    }),
                    headers: expect.objectContaining({
                        timezoneoffset: 0
                    })
                })
            );
        });
    });

    describe('payloadHeatMapGenerationAndSendS3URL', () => {
        it('should generate heat map report successfully', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                companyId: 1,
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'active',
                memberFilterId: 1,
                gateId: 1,
                equipmentId: 1,
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Heat Map',
                locationFilter: 'zone1'
            };

            const result = await cornService.payloadHeatMapGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/heatmap.pdf');
        });

        it('should handle errors in heat map generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00'
            };

            // Mock the service to throw an error
            deliveryReportService.exportHeatMapReportForSceduler.mockRejectedValueOnce(new Error('Heat map service error'));

            const result = await cornService.payloadHeatMapGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeUndefined();
        });

        it('should use default values for missing heat map data', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC'
            };

            await cornService.payloadHeatMapGenerationAndSendS3URL(mockSchedulerData);

            expect(deliveryReportService.exportHeatMapReportForSceduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        statusFilter: '',
                        startDate: '2024-03-15',
                        endDate: '2024-03-16',
                        exportType: 'PDF'
                    })
                })
            );
        });
    });

    describe('cronSchedulerJob - callback execution', () => {
        beforeEach(() => {
            // Mock the cron.schedule to capture and execute the callback
            cron.schedule.mockImplementation((_, callback, __) => {
                // Store the callback for manual execution in tests
                callback._testCallback = callback;
                const mockJob = {
                    stop: jest.fn(),
                    _callback: callback
                };
                // Execute callback immediately for testing
                setTimeout(() => callback(), 0);
                return mockJob;
            });
        });

        it('should execute non-repeating job callback with valid URL', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: '0 10 * * *',
                timezone: 'UTC'
            };

            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');
            SchedulerReport.updateInstance = jest.fn().mockResolvedValue(true);
            const MAILER = require('../../mailer');
            MAILER.sendReportMail = jest.fn().mockResolvedValue(true);

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(cornService.generateReportAll).toHaveBeenCalledWith(mockSchedulerData);
            expect(SchedulerReport.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                lastRun: expect.any(String),
                s3_url: 'https://example.com/report.pdf',
                isEndDateMeet: true
            }));
            expect(MAILER.sendReportMail).toHaveBeenCalled();
        });

        it('should execute non-repeating job callback with invalid URL', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: '0 10 * * *',
                timezone: 'UTC'
            };

            cornService.generateReportAll = jest.fn().mockResolvedValue('invalid-url');
            SchedulerReport.updateInstance = jest.fn().mockResolvedValue(true);
            const MAILER = require('../../mailer');
            MAILER.sendReportMail = jest.fn().mockResolvedValue(true);

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(SchedulerReport.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                lastRun: expect.any(String),
                isEndDateMeet: true
            }));
            expect(MAILER.sendReportMail).toHaveBeenCalledWith(expect.objectContaining({
                emailTemplate: 'schedulerReportNoData',
                message: 'There is no data found in the report for the scheduled date'
            }));
        });

        it('should handle errors in non-repeating job callback', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Does Not Repeat',
                cronExpression: '0 10 * * *',
                timezone: 'UTC'
            };

            cornService.generateReportAll = jest.fn().mockRejectedValue(new Error('Report generation failed'));
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(consoleSpy).toHaveBeenCalledWith('(ERROR)', expect.any(Error));
            consoleSpy.mockRestore();
        });

        it('should execute repeating job callback with valid URL', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: '2024-03-21' // Tomorrow from mock
            };

            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');
            SchedulerReport.updateInstance = jest.fn().mockResolvedValue(true);
            const MAILER = require('../../mailer');
            MAILER.sendReportMail = jest.fn().mockResolvedValue(true);

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(cornService.generateReportAll).toHaveBeenCalledWith(mockSchedulerData);
            expect(SchedulerReport.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                lastRun: expect.any(String),
                s3_url: 'https://example.com/report.pdf',
                isEndDateMeet: false
            }));
        });

        it('should execute repeating job callback with invalid URL', async () => {
            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: '2024-03-21' // Tomorrow from mock
            };

            cornService.generateReportAll = jest.fn().mockResolvedValue('invalid-url');
            SchedulerReport.updateInstance = jest.fn().mockResolvedValue(true);
            const MAILER = require('../../mailer');
            MAILER.sendReportMail = jest.fn().mockResolvedValue(true);

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(MAILER.sendReportMail).toHaveBeenCalledWith(expect.objectContaining({
                emailTemplate: 'schedulerReportNoData',
                message: 'There is no data found in the report for the scheduled date'
            }));
        });

        it('should stop job when recurrence end date has passed', async () => {
            const mockJob = { stop: jest.fn() };
            cron.schedule.mockImplementation((_, callback, __) => {
                setTimeout(() => callback(), 0);
                return mockJob;
            });

            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: '2024-03-18' // Yesterday from mock
            };

            SchedulerReport.updateInstance = jest.fn().mockResolvedValue(true);

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(SchedulerReport.updateInstance).toHaveBeenCalledWith(1, { isEndDateMeet: true });
            expect(mockJob.stop).toHaveBeenCalled();
        });

        it('should handle end date exactly today', async () => {
            const mockJob = { stop: jest.fn() };
            cron.schedule.mockImplementation((_, callback, __) => {
                setTimeout(() => callback(), 0);
                return mockJob;
            });

            const mockSchedulerData = {
                id: 1,
                recurrence: 'Daily',
                cronExpression: '0 10 * * *',
                timezone: 'UTC',
                dateRangeId: 1,
                recurrenceEndDate: '2024-03-20' // Today from mock
            };

            cornService.generateReportAll = jest.fn().mockResolvedValue('https://example.com/report.pdf');
            SchedulerReport.updateInstance = jest.fn().mockResolvedValue(true);
            const MAILER = require('../../mailer');
            MAILER.sendReportMail = jest.fn().mockResolvedValue(true);

            await cornService.cronSchedulerJob(mockSchedulerData);

            // Wait for async callback execution
            await new Promise(resolve => setTimeout(resolve, 10));

            expect(SchedulerReport.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                isEndDateMeet: true
            }));
            expect(mockJob.stop).toHaveBeenCalled();
        });
    });

    describe('checkNDRExpiration - ConcreteRequest coverage', () => {
        it('should update expired concrete requests', async () => {
            await cornService.checkNDRExpiration();

            expect(ConcreteRequest.update).toHaveBeenCalledWith(
                { status: 'Expired' },
                {
                    where: {
                        status: 'Pending',
                        concreteDeliveryStart: {
                            [mockOp.lte]: expect.any(Date),
                        },
                    },
                    order: [['id', 'ASC']],
                }
            );
        });
    });

    describe('payloadConcreteGenerationAndSendS3URL - full coverage', () => {
        it('should generate concrete report with all parameters', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                firstName: 'Test',
                lastName: 'User',
                truckspacingFilter: 'test spacing',
                slumpFilter: 'test slump',
                mixDesignFilter: 'test mix',
                orderNumberFilter: 'test order',
                primerFilter: 'test primer',
                quantityFilter: 'test quantity',
                locationFilter: 'test location',
                descriptionFilter: 'test description',
                sort: 'ASC',
                sortByField: 'name',
                queuedNdr: true,
                selectedHeaders: '["header1"]',
                companyFilter: 'test company',
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'active',
                memberFilterId: 1,
                gateId: 1,
                equipmentId: 1,
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Concrete Report'
            };

            const result = await cornService.payloadConcreteGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/concrete.pdf');
            expect(concreteReportService.exportReportForScheduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        truckspacingFilter: 'test spacing',
                        slumpFilter: 'test slump',
                        mixDesignFilter: 'test mix',
                        orderNumberFilter: 'test order',
                        primerFilter: 'test primer',
                        quantityFilter: 'test quantity',
                        concreteSupplierFilter: 'test company',
                        startdate: '2024-03-20',
                        enddate: '2024-03-21'
                    })
                })
            );
        });

        it('should handle errors in concrete report generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00'
            };

            // Mock the service to throw an error
            concreteReportService.exportReportForScheduler.mockRejectedValueOnce(new Error('Concrete service error'));

            const result = await cornService.payloadConcreteGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeInstanceOf(Error);
        });
    });

    describe('payloadInspectionGenerationAndSendS3URL - full coverage', () => {
        it('should generate inspection report with all parameters', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                descriptionFilter: 'test description',
                sort: 'ASC',
                sortByField: 'inspectionId',
                queuedNdr: true,
                selectedHeaders: '["header1"]',
                companyId: 1,
                customStartDate: '2024-03-20',
                customEndDate: '2024-03-21',
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                status: 'completed',
                inspectionStatusFilter: 'passed',
                inspectionTypeFilter: 'safety',
                memberFilterId: 1,
                gateId: 1,
                equipmentId: 1,
                templateFilterType: '[]',
                defineId: 1,
                parentFilterCompanyId: 1,
                timezone: 'UTC',
                outputFormat: 'PDF',
                reportName: 'Inspection Report',
                locationFilter: 'site1'
            };

            const result = await cornService.payloadInspectionGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBe('https://example.com/inspection.pdf');
            const inspectionReportService = require('../inspectionReportService');
            expect(inspectionReportService.exportReportForScheduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        descriptionFilter: 'test description',
                        sort: 'ASC',
                        sortByField: 'inspectionId',
                        queuedNdr: true,
                        inspectionStatusFilter: 'passed',
                        inspectionTypeFilter: 'safety',
                        startdate: '2024-03-20',
                        enddate: '2024-03-21'
                    })
                })
            );
        });

        it('should use default values for missing inspection data', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00',
                timezone: 'UTC'
            };

            await cornService.payloadInspectionGenerationAndSendS3URL(mockSchedulerData);

            expect(inspectionReportService.exportReportForScheduler).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.objectContaining({
                        descriptionFilter: '',
                        sort: 'DESC',
                        sortByField: 'id',
                        queuedNdr: false,
                        statusFilter: ''
                    })
                })
            );
        });

        it('should handle errors in inspection report generation', async () => {
            const mockSchedulerData = {
                ProjectId: 1,
                createdBy: 1,
                createdUser: { firstName: 'Test', lastName: 'User' },
                startDate: '2024-03-15/10:00:00',
                endDate: '2024-03-16/11:00:00'
            };

            // Mock the service to throw an error
            inspectionReportService.exportReportForScheduler.mockRejectedValueOnce(new Error('Inspection service error'));

            const result = await cornService.payloadInspectionGenerationAndSendS3URL(mockSchedulerData);

            expect(result).toBeInstanceOf(Error);
        });
    });

    describe('getSchedulerReportRequest - missing coverage', () => {
        it('should handle missing pageNo in query', async () => {
            const mockInputData = {
                query: {
                    ProjectId: 1,
                    pageSize: 10,
                    sortByField: 'id',
                    sort: 'DESC'
                },
                body: { saved: true }
            };

            const mockReports = {
                scheduledData: [{ id: 1, reportName: 'Test Report' }],
                count: 1
            };

            SchedulerReport.getAll.mockResolvedValue(mockReports);

            const done = jest.fn();
            await cornService.getSchedulerReportRequest(mockInputData, done);

            expect(SchedulerReport.getAll).toHaveBeenCalledWith(
                1, 10, undefined, 'id', 'DESC', NaN, undefined, undefined, undefined, undefined, undefined, true, false
            );
            expect(done).toHaveBeenCalledWith(
                { scheduledReports: mockReports.scheduledData, count: mockReports.count },
                false
            );
        });

        it('should handle body without saved property', async () => {
            const mockInputData = {
                query: {
                    ProjectId: 1,
                    pageNo: 1,
                    pageSize: 10
                },
                body: {}
            };

            const mockReports = {
                scheduledData: [],
                count: 0
            };

            SchedulerReport.getAll.mockResolvedValue(mockReports);

            const done = jest.fn();
            await cornService.getSchedulerReportRequest(mockInputData, done);

            expect(SchedulerReport.getAll).toHaveBeenCalledWith(
                1, 10, 0, undefined, undefined, NaN, undefined, undefined, undefined, undefined, undefined, undefined, true
            );
        });
    });
});