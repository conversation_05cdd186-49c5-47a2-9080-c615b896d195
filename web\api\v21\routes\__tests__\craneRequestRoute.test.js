const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../controllers', () => ({
  CraneRequestController: {
    createCraneRequest: jest.fn(),
    editCraneRequest: jest.fn(),
    getCraneRequestList: jest.fn(),
    getLastCraneRequestId: jest.fn(),
    getSingleCraneRequest: jest.fn(),
    updateCraneRequestStatus: jest.fn(),
    upcomingRequestListForMobile: jest.fn(),
    upcomingRequestList: jest.fn(),
    editMultipleDeliveryRequest: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  craneRequestValidation: {
    craneRequest: jest.fn(),
    editCraneRequest: jest.fn(),
    getSingleCraneRequest: jest.fn(),
    updateNDRStatus: jest.fn(),
    upcomingRequestList: jest.fn(),
  },
}));

describe('craneRequestRoute', () => {
  let router;
  let craneRequestRoute;
  let CraneRequestController;
  let passportConfig;
  let craneRequestValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    craneRequestRoute = require('../craneRequestRoute');
    const controllers = require('../../controllers');
    CraneRequestController = controllers.CraneRequestController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    craneRequestValidation = validations.craneRequestValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = craneRequestRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(5);
      expect(router.get).toHaveBeenCalledTimes(4);

      // Verify POST routes with validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/create_crane_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CraneRequestController.createCraneRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/edit_crane_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CraneRequestController.editCraneRequest,
      );

      // Verify POST routes without validation
      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/get_crane_request_list/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        CraneRequestController.getCraneRequestList,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/update_crane_request_status',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CraneRequestController.updateCraneRequestStatus,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/edit_multiple_request',
        passportConfig.isAuthenticated,
        CraneRequestController.editMultipleDeliveryRequest,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_last_crane_request_id/:ProjectId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        CraneRequestController.getLastCraneRequestId,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_single_crane_request/:CraneRequestId/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CraneRequestController.getSingleCraneRequest,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/upcoming_crane_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CraneRequestController.upcomingRequestListForMobile,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        4,
        '/upcoming_request_list',
        passportConfig.isAuthenticated,
        CraneRequestController.upcomingRequestList,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(5);
      expect(validate).toHaveBeenCalledWith(
        craneRequestValidation.craneRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestValidation.editCraneRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestValidation.getSingleCraneRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestValidation.updateNDRStatus,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestValidation.upcomingRequestList,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = craneRequestRoute.router;
      const result2 = craneRequestRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      craneRequestRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should use validation middleware for specific routes', () => {
      craneRequestRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Routes with validation should have 4 parameters
      const routesWithValidation = [
        postCalls[0], // create_crane_request
        postCalls[1], // edit_crane_request
        postCalls[3], // update_crane_request_status (moved to position 4)
        getCalls[1],  // get_single_crane_request
        getCalls[2],  // upcoming_crane_request
      ];

      routesWithValidation.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // Routes without validation should have 3 parameters
      const routesWithoutValidation = [
        postCalls[2], // get_crane_request_list (moved to position 3)
        postCalls[4], // edit_multiple_request
        getCalls[0],  // get_last_crane_request_id
        getCalls[3],  // upcoming_request_list
      ];

      routesWithoutValidation.forEach(call => {
        expect(call).toHaveLength(3); // path + auth + controller
        expect(call[1]).toBe(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof craneRequestRoute).toBe('object');
      expect(craneRequestRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
