/* eslint-disable no-await-in-loop */
const path = require('path');
const status = require('http-status');
const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});
const { Worker } = require('worker_threads');
const moment = require('moment');
const creditCardType = require('credit-card-type');
const Cryptr = require('cryptr');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const axios = require('axios');
const {
  Sequelize,
  StripePlan,
  RestrictEmail,
  Enterprise,
  ProjectBillingHistories,
  NotificationPreference,
  NotificationPreferenceItem,
  TimeZone,
  ProjectSettings,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
let {
  Project,
  Member,
  User,
  Role,
  Company,
  ParentCompany,
  Notification,
  DeliveryPersonNotification,
} = require('../models');
const { StripeSubscription } = require('../models');

const stripeService = require('./stripeService');
const MAILER = require('../mailer');
const ApiError = require('../helpers/apiError');
const deepLinkService = require('./deepLinkService');

const { Op } = Sequelize;

let publicUser;
let publicMember;
let publicProject;
let domainNewName;

const { generatePassword } = require('../helpers/generatePassword');
const awsConfig = require('../middlewares/awsConfig');

const imageConversionProcess = path.join(__dirname, './imageConversionProcess.js');

const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');

const projectService = {
  async existProject(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const parentId = await Member.findOne({ where: { UserId: loginUser.id, isDeleted: false } });
      const existProject = await Project.findOne({
        where: Sequelize.and({
          ParentCompanyId: parentId.ParentCompanyId,
          projectName: inputData.body.projectName,
        }),
      });
      if (existProject) {
        done(null, { message: 'Project Name Already exist.' });
      } else {
        done({ message: 'success' }, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const domainNewName = await this.getDomainName(inputData);
    const modelObj = await helper.getDynamicModel(domainNewName);
    this.assignModels(modelObj);

    if (inputData.user && domainNewName) {
      const newUser = await modelObj.User.findOne({
        where: { email: inputData.user.email, isDeleted: false },
      });
      inputData.user = newUser;
    }
  },

  async getEnterpriseDomainNameFromEmail(email, ParentCompanyId) {
    if (!email) return '';

    const userData = await publicUser.findOne({ where: { email, isDeleted: false } });
    if (!userData) return '';

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });

    let enterpriseValue = null;

    if (memberData) {
      enterpriseValue = await Enterprise.findOne({
        where: {
          id: memberData.isAccount ? memberData.EnterpriseId : undefined,
          ParentCompanyId: !memberData.isAccount ? ParentCompanyId : undefined,
          status: 'completed',
        },
      });
    } else {
      enterpriseValue = await Enterprise.findOne({
        where: { ParentCompanyId, status: 'completed' },
      });
    }

    return enterpriseValue?.name?.toLowerCase() || '';
  },

  async getDomainName(inputData) {
    let domainNewName = inputData.user.domainName;
    const ParentCompanyId = inputData.body.ParentCompanyId ?? inputData.params.ParentCompanyId;

    if (domainNewName) {
      const domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainNewName.toLowerCase() },
      });
      if (!domainEnterpriseValue) domainNewName = '';
    }

    if (!domainNewName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      domainNewName = await this.getEnterpriseDomainNameFromEmail(inputData.user.email, ParentCompanyId);
    }

    return domainNewName;
  },

  assignModels(modelObj) {
    User = modelObj.User;
    Member = modelObj.Member;
    Role = modelObj.Role;
    Company = modelObj.Company;
    Project = modelObj.Project;
    ParentCompany = modelObj.ParentCompany;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Notification = modelObj.Notification;
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
    publicProject = modelData.Project;
  },
  async initialSetupCreateProject(inputData, done) {
    await this.getDynamicModel(inputData);
    const projectData = inputData.body;
    const loginUser = inputData.user;
    const parentId = await Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
    const passData = inputData;
    passData.emailValidationData = emailValidationData;
    if (projectData.email !== loginUser.email) {
      const firstSplit = projectData.email.split('@')[1];
      const secondSplit = firstSplit.split('.');
      let emailDomainName;
      if (secondSplit.length === 2) {
        emailDomainName = firstSplit;
      } else if (secondSplit.length > 2) {
        const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
        emailDomainName = str;
      }
      const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: 1 });
      if (restrict) {
        done(null, { message: 'This email is not allowed' });
      } else if (emailValidationData.emailDomainName === emailDomainName) {
        done(passData, false);
      } else {
        done(null, { message: 'This user is not our organization.' });
      }
    } else {
      done(passData, false);
    }
  },
  async createProject(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      this.initialSetupCreateProject(inputData, (passData, errorValue) => {
        if (!errorValue) {
          this.findProjectPlan(passData, (response, error) => {
            if (!error) {
              done(response, false);
            } else {
              done(null, error);
            }
          });
        } else {
          done(null, errorValue);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async createAccountProject(inputData, done) {
    try {
      const startDate = new Date(inputData.body.startDate).getTime();
      const endDate = new Date(inputData.body.endDate).getTime();
      if (startDate < endDate) {
        await this.getDynamicModel(inputData);
        if (domainNewName) {
          const enterPriseValue = await Enterprise.findOne({
            where: Sequelize.where(
              Sequelize.fn('lower', Sequelize.col('name')),
              Sequelize.fn('lower', domainNewName),
            ),
          });
          const projectCount = await Member.findAll({ where: { UserId: inputData.user.id } });
          if (enterPriseValue) {
            if (enterPriseValue.projectCount < projectCount.length + 1) {
              done(null, { message: 'Your Project limit reached.!' });
            } else {
              const incomeData = inputData;
              incomeData.body.email = incomeData.user.email;
              this.initialSetupCreateProject(incomeData, async (passData, errorValue) => {
                if (!errorValue) {
                  await this.returnProjectModel();
                  const tempInput = incomeData;
                  tempInput.domainBo = true;
                  tempInput.emailValidationData = passData.emailValidationData;
                  this.createNewProject(tempInput, (response, error) => {
                    if (!error) {
                      done(response, false);
                    } else {
                      done(null, error);
                    }
                  });
                } else {
                  done(null, errorValue);
                }
              });
            }
          } else {
            done(null, { message: 'Something went wrong' });
          }
        } else {
          done(null, { message: 'Something went wrong' });
        }
      } else {
        done(null, { message: 'End Date must be higher than Start Date' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async findProjectPlan(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const planDetails = await StripePlan.getBy({ id: inputData.body.PlanId });

      if (!planDetails) {
        return done(null, { message: 'Plan Does not exist.' });
      }

      const planType = planDetails.Plan.planType.toLowerCase();

      if (planType === 'trial plan') {
        await this.handleTrialPlan(inputData, planDetails, done);
      } else if (planType === 'project plan') {
        await this.handleProjectPlan(inputData, planDetails, done);
      } else {
        done({ message: 'Please Contact Sales team.' }, false);
      }

    } catch (e) {
      done(null, e);
    }
  },

  async handleTrialPlan(inputData, planDetails, done) {
    const loginUser = inputData.user;
    const CompanyDet = await Member.findOne({
      where: { UserId: loginUser.id, isDeleted: false },
      include: [{ association: 'Company', required: true, attributes: ['id', 'city', 'country', 'address', 'zipCode', 'state'] }],
      attributes: ['id'],
    });

    const existingProjects = await Project.count({
      where: Sequelize.and({ createdBy: loginUser.id, PlanId: inputData.body.PlanId, isDeleted: false }),
    });

    let maxProjects = 1;
    if (process.env.NODE_ENV === 'prod' && +loginUser.id === 5) maxProjects = 20;
    if (process.env.NODE_ENV === 'staging' && +loginUser.id === 227) maxProjects = 10;

    if (existingProjects > maxProjects) {
      return done(null, { message: 'Trial Plan Limit Reached.' });
    }

    const user = await User.getBy({ id: loginUser.id });
    let customer = user.stripeCustomerId ? await stripe.customers.retrieve(user.stripeCustomerId) : await this.createStripeCustomer(user, CompanyDet.Company);

    if (!user.stripeCustomerId) {
      await user.updateInstance(user.id, { stripeCustomerId: customer.id });
    }

    const plan = await StripePlan.getBy({ id: inputData.body.PlanId });
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      collection_method: 'send_invoice',
      trial_period_days: 14,
      days_until_due: 0,
      items: [{ plan: plan.stripePlanId }],
    });

    subscription.status = 'active';

    const newStripeSubscription = await StripeSubscription.createInstance(user, subscription);
    inputData.stripeDetails = newStripeSubscription;

    this.createNewProject(inputData, (response, error) => {
      if (error) return done(null, error);
      done(response, false);
    });
  },

  async createStripeCustomer(user, company) {
    return await stripe.customers.create({
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      phone: `${user.phoneCode} ${user.phoneNumber}`,
      address: {
        city: company.city,
        country: company.country,
        line1: company.address,
        postal_code: company.zipCode,
        state: company.state,
      },
    });
  },

  async handleProjectPlan(inputData, planDetails, done) {
    const CompanyDet = await Member.findOne({
      where: { UserId: inputData.user.id, isDeleted: false },
      include: [{ association: 'Company', required: true, attributes: ['id', 'city', 'country', 'address', 'zipCode', 'state'] }],
      attributes: ['id'],
    });

    const planData = await StripePlan.getBy({ id: inputData.body.PlanId });
    inputData.planData = planData;
    inputData.CompanyDet = CompanyDet.Company;

    if (!inputData.body.stripeCustomerId) return done(null, { message: 'Missing Stripe Customer ID.' });

    const user = await User.findOne({ where: { stripeCustomerId: inputData.body.stripeCustomerId } });
    const subscriptionList = await stripe.subscriptions.list({ customer: inputData.body.stripeCustomerId, limit: 1 });

    const latestSubscription = subscriptionList.data[0];
    const newSubscription = await StripeSubscription.create({
      UserId: user.id,
      status: 'active',
      subscriptionId: latestSubscription.id,
    });

    inputData.stripeDetails = newSubscription;

    this.createNewProject(inputData, (response, error) => {
      if (error) return done(null, error);
      done(response, false);
    });
  },


  async getPlansAndProjects(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const condition = {};
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = params;
      const { sortByField } = params;
      const { search } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const projectData = await Project.getPlansAndProjects(
        inputData.user,
        condition,
        pageSize,
        offset,
        sort,
        sortByField,
        search,
      );
      this.getStripeDate(inputData.user, projectData.rows, 0, [], (response, err) => {
        if (!err) {
          const responseData = {
            rows: response,
            count: projectData.count,
          };
          done(responseData, false);
        } else {
          done(null, err);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async getSubscriptionDetail(newData) {
    try {
      const subDetail = await stripe.subscriptions.retrieve(
        newData.StripeSubscription.subscriptionId,
      );

      const subStart = new Date(subDetail.current_period_start * 1000);
      const subEnd = new Date(subDetail.current_period_end * 1000);
      const subInfo = {
        subscribedOn: subStart,
        autoRenewal: subEnd,
      };

      if (subDetail.cancel_at_period_end || subDetail.status === 'canceled') {
        newData.cancel_at_period_end = true;
        newData.cancel_at = new Date(subDetail.cancel_at * 1000);

        if (subDetail.cancel_at_period_end) {
          subInfo.autoRenewal = 'Not applicable';
          subInfo.status = 'To be cancel';
        } else {
          subInfo.autoRenewal = '------';
          subInfo.status = 'Canceled';
        }
      }

      newData.subDetail = subInfo;
    } catch (error) {
      const isTestOrStaging = ['testing', 'staging'].includes(process.env.NODE_ENV);
      const isNotFound = error.type === 'StripeInvalidRequestError' && error.statusCode === 404;

      if (isTestOrStaging && isNotFound) {
        newData.subDetail = { status: 'Not applicable' };
        newData.cancel_at_period_end = true;
      } else {
        throw error;
      }
    }
  },

  async processSingleStripeData(user, dataItem) {
    const newData = JSON.parse(JSON.stringify(dataItem));
    newData.cancel_at_period_end = false;

    newData.enableBilling =
      newData?.StripeSubscription && user.id === newData.StripeSubscription.UserId;

    const isStripeSubscriptionPresent =
      newData.StripeSubscriptionId !== null &&
      !newData.isSuperAdminCreatedProject &&
      newData.PlanId !== 1;

    if (isStripeSubscriptionPresent) {
      await this.getSubscriptionDetail(newData);
    } else if (newData.PlanId !== 1) {
      newData.subDetail = {
        subscribedOn: new Date(newData.startDate),
        autoRenewal: new Date(newData.endDate),
      };
    }

    return newData;
  },



  async getStripeDate(user, inputData, index, resultData, done) {
    try {
      if (inputData.length > 0) {
        const dataItem = inputData[index];
        try {
          const processedData = await this.processSingleStripeData(user, dataItem);
          resultData.push(processedData);
        } catch (error) {
          return done(null, error);
        }

        if (index < inputData.length - 1) {
          this.getStripeDate(user, inputData, index + 1, resultData, done);
        } else {
          done(resultData, false);
        }
      } else {
        done(inputData, false);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async getProSubStripeDate(newData) {
    const subscriptionData = newData;
    subscriptionData.cancel_at_period_end = false;
    subscriptionData.subDetail = {};
    if (newData.PlanId !== 1) {
      let subDetail;
      if (newData?.StripeSubscription?.subscriptionId) {
        subDetail = await stripe.subscriptions.retrieve(newData.StripeSubscription.subscriptionId);
      }
      if (subDetail) {
        subscriptionData.subDetail = {
          subscribedOn: new Date(subDetail.current_period_start * 1000),
          autoRenewal: new Date(subDetail.current_period_end * 1000),
        };
        if (subDetail.cancel_at_period_end) {
          subscriptionData.subDetail.autoRenewal = 'Not applicable';
          subscriptionData.cancel_at_period_end = true;
          subscriptionData.cancel_at = new Date(subDetail.cancel_at * 1000);
          subscriptionData.subDetail.status = 'To be cancel';
        } else if (subDetail.status === 'canceled') {
          subscriptionData.subDetail.autoRenewal = '------';
          subscriptionData.cancel_at_period_end = true;
          subscriptionData.cancel_at = new Date(subDetail.cancel_at * 1000);
          subscriptionData.subDetail.status = 'Canceled';
        }
      } else {
        subscriptionData.subDetail.subscribedOn = newData.subscribedOn;
        subscriptionData.subDetail.status = newData.status;
      }
      return subscriptionData;
    }
    subscriptionData.subDetail.subscribedOn = null;
    subscriptionData.subDetail.status = newData.status;
    return subscriptionData;
  },
  async getProjects(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const condition = {};
      const incomeData = inputData.body;
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      let searchCondition;
      const offset = (pageNumber - 1) * pageSize;
      if (incomeData.idFilter) {
        condition.id = incomeData.idFilter;
      }
      if (incomeData.nameFilter) {
        condition.projectName = {
          [Sequelize.Op.iLike]: `%${incomeData.nameFilter}%`,
        };
      }

      if (incomeData.search) {
        const searchDefault = [
          {
            projectName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];

        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        id: incomeData.search,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const projectData = await Project.getProjectsProjectAdmin(
        inputData.user,
        condition,
        searchCondition,
        pageSize,
        offset,
      );
      done(projectData, false);
    } catch (e) {
      done(null, e);
    }
  },
  // Function 1: Handles matching logic
  isMatchingSearch(element, search) {
    if (!search) return true;

    if (element.id === search || element.projectName.startsWith(search)) {
      return true;
    }

    return element.projectAdminDetails.some((data) => {
      const { email, firstName } = data.User;
      return email.startsWith(search) || firstName.startsWith(search);
    });
  },

  // Function 2: Handles pagination
  async getPagination(projectData, index, pageSize, resultData, search, done) {
    try {
      const element = projectData[index];

      if (element) {
        if (this.isMatchingSearch(element, search)) {
          resultData.push(element);
        }

        if (index + 1 < pageSize) {
          await this.getPagination(
            projectData,
            index + 1,
            pageSize,
            resultData,
            search,
            (response, error) => {
              if (error) {
                done(null, error);
              } else {
                done(response, false);
              }
            },
          );
        } else {
          done(resultData, false);
        }
      } else {
        done(resultData, false);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async createNewProject(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const projectData = inputData.body;
      const loginUser = inputData.user;
      let startDate;
      let endDate;
      const StripeSubscriptionId = inputData?.stripeDetails?.id ? inputData.stripeDetails.id : null;
      const planData = await StripePlan.getBy({ id: projectData.PlanId });
      if (planData.stripePlanName === 'free') {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment().add(14, 'days').format('YYYY-MM-DD');
      } else if (
        planData.stripeProductName.toLowerCase() === 'project plan' &&
        planData.stripePlanName.toLowerCase() === 'monthly'
      ) {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment(startDate, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
      } else if (
        planData.stripeProductName.toLowerCase() === 'project plan' &&
        planData.stripePlanName.toLowerCase() === 'yearly'
      ) {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment(startDate, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
      }
      const projectParam = {
        projectName: projectData.projectName,
        projectLocation: projectData.projectLocation,
        projectLocationLatitude: projectData.projectLocationLatitude,
        projectLocationLongitude: projectData.projectLocationLongitude,
        PlanId: projectData.PlanId,
        startDate,
        endDate,
        ParentCompanyId: projectData.ParentCompanyId,
        subscribedOn: new Date(),
        createdBy: loginUser.id,
        TimeZoneId: null,
      };
      const timezoneList = await TimeZone.getAll();
      const getUserTimeZone = timezoneList.filter(
        (object) => object.timezone === projectData.timezone,
      );
      if (getUserTimeZone?.length > 0) {
        const userTimeZone = getUserTimeZone[0];
        projectParam.TimeZoneId = userTimeZone.id;
      }
      if (StripeSubscriptionId) {
        projectParam.StripeSubscriptionId = StripeSubscriptionId;
      }
      const newProject = await Project.createInstance(projectParam);
      const createObject = {
        deliveryWindowTime: 1,
        deliveryWindowTimeUnit: 'days',
        inspectionWindowTime: 1,
        inspectionWindowTimeUnit: 'days',
        craneWindowTime: 1,
        craneWindowTimeUnit: 'days',
        concreteWindowTime: 1,
        concreteWindowTimeUnit: 'days',
        ProjectId: +newProject.id,
      };
      await ProjectSettings.create(createObject);
      const projectObject = {
        ProjectId: +newProject.id,
        projectName: projectData.projectName,
      };
      this.generatePublicUrlForCurrentProject(projectObject);
      let domainValue;
      let publicProjectId;
      if (inputData.domainBo) {
        domainValue = await Enterprise.findOne({
          where: Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('name')),
            Sequelize.fn('lower', domainNewName),
          ),
        });
        projectParam.EnterpriseId = domainValue.id;
        projectParam.isAccount = true;
        projectParam.publicSchemaId = newProject.id;
        const publicProjectParam = await publicProject.create(projectParam);
        publicProjectId = publicProjectParam.id;
        await Project.update({ publicSchemaId: publicProjectId }, { where: { id: newProject.id } });
      }
      const existUser = await User.findOne({
        where: Sequelize.and(
          Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('email')),
            Sequelize.fn('lower', projectData.email),
          ),
          { isDeleted: false },
        ),
      });
      let publicUserId;
      if (inputData.domainBo) {
        const newUserValue = await publicUser.findOne({
          where: Sequelize.and(
            Sequelize.where(
              Sequelize.fn('lower', Sequelize.col('email')),
              Sequelize.fn('lower', projectData.email),
            ),
            { isDeleted: false },
          ),
        });
        publicUserId = newUserValue.id;
      }
      if (existUser) {
        this.createMember(
          inputData,
          existUser,
          newProject,
          publicUserId,
          publicProjectId,
          (response, error) => {
            if (error) {
              done(null, error);
            } else {
              done(response, false);
            }
          },
        );
      } else {
        const userParam = {
          firstName: projectData.firstName,
          email: projectData.email,
          password: generatePassword(),
          phoneNumber: projectData.phoneNumber,
        };
        const newUser = await User.createInstance(userParam);
        if (inputData.domainBo) {
          const newUserValue = await publicUser.create(userParam);
          publicUserId = newUserValue.id;
        }
        this.createMember(
          inputData,
          newUser,
          newProject,
          publicUserId,
          publicProjectId,
          (response, error) => {
            if (error) {
              done(null, error);
            } else {
              done(response, false);
            }
          },
        );
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createMember(inputData, user, newProject, publicUserId, publicProjectId, done) {
    try {
      await this.getDynamicModel(inputData);
      const projectData = inputData.body;
      const { ParentCompanyId } = projectData;
      const loginUser = inputData.user;

      const CompanyDet = await Member.findOne({
        where: { UserId: loginUser.id, isDeleted: false },
        include: [{ association: 'Company', required: true, attributes: ['id'] }],
        attributes: ['id'],
      });

      if (!CompanyDet) {
        return done(null, { message: 'Something went wrong' });
      }

      const roleDetails = await Role.getBy(inputData.domainBo ? 'Account Admin' : 'Project Admin');

      const newMember = await this.prepareAndCreateMembers({
        inputData,
        loginUser,
        user,
        newProject,
        CompanyDet,
        roleDetails,
        ParentCompanyId,
        publicUserId,
        publicProjectId,
      });

      done(newMember, false);
    } catch (e) {
      done(null, e);
    }
  },

  async prepareAndCreateMembers({
    inputData,
    loginUser,
    user,
    newProject,
    CompanyDet,
    roleDetails,
    ParentCompanyId,
    publicUserId,
    publicProjectId,
  }) {
    const isSameUser = loginUser.email === user.email;

    const baseMemberParam = {
      firstName: user.firstName,
      CompanyId: CompanyDet.Company.id,
      RoleId: roleDetails.id,
      ParentCompanyId,
      createdBy: user.id,
      password: generatePassword(),
      phoneNumber: user.phoneNumber,
      phoneCode: user.phoneCode,
      memberId: 1,
      status: 'completed',
    };

    if (isSameUser) {
      const memberParam = {
        ...baseMemberParam,
        UserId: user.id,
        ProjectId: newProject.id,
      };

      const newMember = await Member.createInstance(memberParam);
      await this.createMemberNotificationPreference(newMember, 'member');
      await this.createMemberLocationFollowPreference(
        newProject.id,
        ParentCompanyId,
        newMember.id,
        newProject.projectName,
        user.id,
        'member',
      );

      if (memberParam.RoleId === 2 || memberParam.RoleId === 1) {
        notificationHelper.memberNotificationCreation(
          newMember,
          DeliveryPersonNotification,
          Notification,
        );
      }

      if (inputData.domainBo) {
        const enterpriseValue = await Enterprise.findOne({
          where: Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('name')),
            Sequelize.fn('lower', domainNewName),
          ),
        });

        const publicMemberParam = {
          ...memberParam,
          UserId: publicUserId,
          ProjectId: publicProjectId,
          isAccount: true,
          EnterpriseId: enterpriseValue.id,
        };

        await publicMember.create(publicMemberParam);
      }

      return newMember;
    } else {
      const memberArray = [
        {
          ...baseMemberParam,
          UserId: user.id,
          ProjectId: newProject.id,
        },
        {
          ...baseMemberParam,
          UserId: loginUser.id,
          firstName: loginUser.firstName,
          CompanyId: CompanyDet.id,
          ProjectId: newProject.id,
        },
      ];

      const newMembers = await Member.createMultipleInstance(memberArray);
      await this.createMemberNotificationPreference(newMembers, 'members');
      await this.createMemberLocationFollowPreference(
        newProject.id,
        ParentCompanyId,
        newMembers,
        newProject.projectName,
        loginUser.id,
        'members',
      );

      if (inputData.domainBo) {
        await publicMember.createMultipleInstance(memberArray);
      }

      return newMembers;
    }
  },

  async getNewDynamicModel(inputData) {
    let { domainNewValue } = inputData;
    if (domainNewValue) {
      return domainNewValue;
    }

    const { email } = inputData.user;
    if (!email) {
      return domainNewValue;
    }

    const userData = await User.findOne({ where: { email } });
    if (!userData) {
      return domainNewValue;
    }

    const memberData = await Member.findOne({
      where: {
        UserId: userData.id,
        RoleId: { [Op.ne]: 4 },
        isDeleted: false
      },
    });
    if (!memberData?.isAccount) {
      return domainNewValue;
    }

    const enterpriseValue = await Enterprise.findOne({
      where: {
        id: memberData.EnterpriseId,
        status: 'completed'
      },
    });
    if (enterpriseValue) {
      domainNewValue = enterpriseValue.name.toLowerCase();
    }

    return domainNewValue;
  },

  async getProjectsCompany(inputData, projectList, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyList = [];
      projectList.forEach((item) => {
        const companyData = item.ParentCompany.Company[0];
        const index = companyList.findIndex(
          (companyNew) => companyNew.id === item.ParentCompany.id,
        );

        if (index === -1) {
          companyList.push({
            id: item.ParentCompany.id,
            companyName: companyData.companyName,
            projectList: [{ id: item.id, projectName: item.projectName }],
          });
        } else {
          companyList[index].projectList.push({ id: item.id, projectName: item.projectName });
        }
      });
      if (companyList && companyList.length > 0) {
        companyList.sort((a, b) => {
          const companyA = a.companyName.toLowerCase();
          const companyB = b.companyName.toLowerCase();
          return companyA.localeCompare(companyB);
        });
      }
      done(companyList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async upgradePlan(inputData, done) {
    await this.getDynamicModel(inputData);
    const projectData = inputData.body;
    const loginUser = inputData.user;

    const validationResult = await this.validateUpgradeRequest(projectData);
    if (validationResult.error) {
      return done(null, validationResult.error);
    }

    const { planDetails, existingPlanDetails } = validationResult;

    this.handlePlanUpgrade(
      inputData,
      planDetails,
      projectData,
      loginUser,
      existingPlanDetails,
      done
    );
  },

  async validateUpgradeRequest(projectData) {
    const planDetails = await StripePlan.getBy({ id: projectData.PlanId });
    const projectDetails = await Project.findByPk(projectData.ProjectId);

    if (!planDetails || !projectDetails) {
      return { error: { message: 'Plan/Project Does not exist.' } };
    }

    if (
      projectDetails.PlanId === projectData.PlanId &&
      projectDetails.status !== 'canceled' &&
      projectDetails.status !== 'overdue'
    ) {
      return { error: { message: 'You have already subscribed to this plan.' } };
    }

    const existingPlanDetails = await StripePlan.getBy({ id: projectDetails.PlanId });

    return { planDetails, projectDetails, existingPlanDetails };
  },

  handlePlanUpgrade(inputData, planDetails, projectData, loginUser, existingPlanDetails, done) {
    const isCancelablePlan = ['project plan', 'trial plan'].includes(
      existingPlanDetails.Plan.planType.toLowerCase()
    );

    const upgrade = () => {
      this.convertPlan(inputData, planDetails, projectData, loginUser, async (planResult, planErr) => {
        if (planErr) {
          return done(null, planErr);
        }
        return done(planResult, false);
      });
    };

    if (isCancelablePlan) {
      stripeService.cancelSubscription(inputData, upgrade);
    } else {
      upgrade();
    }
  },

  async sendMail(userData, mailData, done) {
    const user = userData;
    await MAILER.sendMail(
      user,
      'upgradeplan',
      `Follo Upgrade - Congratulations! ${userData.projectName} have upgraded for - 
      ${userData.interval}  from  ${userData.projectStartDate} to ${userData.projectEndDate}`,
      'Plan Upgraded',
      (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          return done(null, newError);
        }
        return done(userData, false);
      },
    );
  },
  async convertPlan(inputData, planDetails, projectData, loginUser, done) {
    await this.getDynamicModel(inputData);
    const planType = planDetails.Plan.planType.toLowerCase();

    switch (planType) {
      case 'project plan':
        return this.handleProjectPlanData(inputData, projectData, loginUser, done);

      case 'trial plan':
        return this.handleTrialPlanData(inputData, projectData, loginUser, done);

      case 'enterprise plan':
        return this.handleEnterprisePlan(projectData, done);

      default:
        return done(null, { message: 'Plan Id does not exist' });
    }
  },

  async handleProjectPlanData(inputData, projectData, loginUser, done) {
    const CompanyDet = await Member.findOne({
      where: {
        UserId: loginUser.id,
        ProjectId: inputData.body.ProjectId,
        isDeleted: false,
      },
      include: [{
        association: 'Company',
        required: true,
        attributes: ['id', 'city', 'country', 'address', 'zipCode', 'state'],
      }],
      attributes: ['id'],
    });

    const planData = await StripePlan.getBy({ id: projectData.PlanId });
    inputData.planData = planData;
    inputData.CompanyDet = CompanyDet.Company;

    if (!inputData.body.stripeCustomerId) {
      return done(null, { message: 'Something went wrong.' });
    }

    await User.update(
      { stripeCustomerId: inputData.body.stripeCustomerId },
      { where: { id: inputData.user.id } }
    );

    let startDate = moment().format('YYYY-MM-DD');
    let endDate = startDate;

    if (planData.stripeProductName.toLowerCase() === 'trial plan') {
      endDate = moment().add(14, 'days').format('YYYY-MM-DD');
    } else if (planData.stripePlanName === 'monthly') {
      endDate = moment(startDate).add(1, 'M').format('YYYY-MM-DD');
    } else if (planData.stripePlanName === 'yearly') {
      endDate = moment(startDate).add(12, 'M').format('YYYY-MM-DD');
    }

    const subscription = await stripe.subscriptions.list({
      customer: inputData.body.stripeCustomerId,
      limit: 1,
    });

    const latestSubscriptionObject = subscription.data[0];
    const newSubscription = await StripeSubscription.create({
      UserId: inputData.user.id,
      status: 'active',
      subscriptionId: latestSubscriptionObject.id,
    });

    const newProject = await Project.update(
      {
        PlanId: projectData.PlanId,
        StripeSubscriptionId: newSubscription.id,
        status: '',
        subscribedOn: new Date(),
        startDate,
        endDate,
      },
      { where: { id: projectData.ProjectId } }
    );

    newProject.stripeCustomerId = inputData.body.stripeCustomerId;
    return done(newProject, false);
  },

  async handleTrialPlanData(inputData, projectData, loginUser, done) {
    const existProject = await Project.count({
      where: Sequelize.and({
        createdBy: loginUser.id,
        PlanId: projectData.PlanId,
        isDeleted: false,
      }),
    });

    let projectCount = 2;
    if (process.env.NODE_ENV === 'prod' && +loginUser.id === 5) {
      projectCount = 20;
    }
    if (process.env.NODE_ENV === 'staging' && +loginUser.id === 227) {
      projectCount = 10;
    }

    if (existProject >= projectCount) {
      return done(null, { message: 'Trial Plan Limit Reached.' });
    }

    const newProject = await Project.update(
      {
        PlanId: projectData.PlanId,
        status: '',
        StripeSubscriptionId: null,
        subscribedOn: new Date(),
      },
      { where: { id: projectData.ProjectId } }
    );

    return done(newProject, false);
  },

  async handleEnterprisePlan(projectData, done) {
    await Project.updateInstance(
      { id: projectData.ProjectId },
      { PlanId: projectData.PlanId, status: '' }
    );

    return done({ message: 'Please Contact Sales team.' }, false);
  },

  async getProjectDetails(req) {
    try {
      await this.getDynamicModel(req);
      const projectDetails = await Project.findByPk(+req.params.ProjectId);
      return projectDetails;
    } catch (e) {
      console.log(e);
    }
  },
  // Get All Project Lists
  async getProjectList(req) {
    try {
      const {
        search,
        pageSize,
        pageNo,
        sortColumn,
        sortType,
        projectId,
        projectName,
        companyName,
      } = req.query;
      let count = 0;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const projectList = await Project.getAllProjects(
        pageSize,
        offset,
        search,
        sortColumn,
        sortType,
        projectId,
        projectName,
        companyName,
      );
      count = projectList.length;
      return { projectList, count };
    } catch (e) {
      console.log(e);
    }
  },
  // Updated Existing project
  async prepareProjectUpdateData(projectData, projectId) {
    let startDate, endDate;

    const lowerPlanType = projectData.planType.toLowerCase();
    const lowerInterval = projectData.interval?.toLowerCase();

    const today = moment().format('YYYY-MM-DD');
    if (lowerPlanType === 'trial plan') {
      startDate = today;
      endDate = moment().add(14, 'days').format('YYYY-MM-DD');
    } else if (lowerPlanType === 'project plan' && lowerInterval === 'monthly') {
      startDate = today;
      endDate = moment(today, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
    } else if (lowerPlanType === 'project plan' && lowerInterval === 'yearly') {
      startDate = today;
      endDate = moment(today, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
    }

    const planWords = lowerPlanType.split(' ').map(word => word[0].toUpperCase() + word.slice(1));
    const selectedPlan = planWords.join(' ');

    const planDetails = await StripePlan.findOne({
      where: selectedPlan === 'Project Plan'
        ? { stripeProductName: selectedPlan, stripePlanName: lowerInterval }
        : { stripeProductName: selectedPlan },
    });

    const projectDetail = await Project.findByPk(projectId);
    const stripeSubscription = await StripeSubscription.getBy({ id: projectDetail.StripeSubscriptionId });

    if (stripeSubscription && stripeSubscription.status !== 'inactive') {
      await stripe.subscriptions.del(stripeSubscription.subscriptionId);
      await Project.update({ status: 'canceled' }, { where: { id: projectId } });
      await stripeSubscription.updateInstance(stripeSubscription.id, { status: 'inactive' });
    }

    return {
      PlanId: planDetails.id,
      projectName: projectData.projectName,
      projectLocation: projectData.projectLocation,
      subscribedOn: new Date(),
      status: '',
      startDate,
      endDate,
      isSuperAdminCreatedProject: true,
    };
  },

  async editMemberProject(req) {
    try {
      const projectData = req.body;
      const { id } = req.params;
      let updatedProject;

      if (projectData) {
        const updatePayload = await this.prepareProjectUpdateData(projectData, id);
        updatedProject = await Project.update(updatePayload, { where: { id } });
      }

      const mailObject = {
        name: projectData.assignedTo,
        projectName: projectData.projectName,
        email: projectData.email,
      };

      await MAILER.sendMail(
        mailObject,
        'editProjectBySA',
        'Project Details Updated.!',
        'Project Details Edited by Super Admin',
        (info, err) => {
          console.log(info, err);
        },
      );

      return updatedProject;
    } catch (e) {
      console.log(e);
    }
  },

  // Updated Existing project
  async getMemberProject(req) {
    try {
      const { id } = req.params;
      const project = await Project.getMemberProject({ id });
      return project;
    } catch (e) {
      console.log(e);
    }
  },
  // assign New Project To Member
  async assignNewProjectToMember(req) {
    try {
      const { userId } = req.body;
      const projectData = req.body;
      let planDetails;
      if (projectData.planType.toLowerCase() === 'trial plan') {
        planDetails = await StripePlan.getBy({
          stripeProductName: 'Trial Plan',
        });
      }
      if (projectData.planType.toLowerCase() === 'project plan')
        planDetails = await StripePlan.getBy({
          stripeProductName: 'Project Plan',
          stripePlanName: projectData.interval.toLowerCase(),
        });
      const CompanyDetails = await Member.findOne({
        where: {
          UserId: userId,
          isDeleted: false,
        },
        include: [
          {
            association: 'Company',
            required: true,
            attributes: [
              'id',
              'companyName',
              'website',
              'address',
              'secondAddress',
              'country',
              'city',
              'companyAutoId',
              'state',
              'zipCode',
              'scope',
              'logo',
              'ParentCompanyId',
            ],
          },
        ],
        attributes: ['id', 'ParentCompanyId'],
      });
      const parentId = await Member.getBy({ UserId: userId });
      const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
      const projectParam = {
        projectName: projectData.projectName,
        projectLocation: projectData.projectLocation,
        PlanId: planDetails.PlanId,
        startDate: projectData.projectStartDate,
        endDate: projectData.projectEndDate,
        ParentCompanyId: emailValidationData.id,
        subscribedOn: new Date(),
        createdBy: userId,
        isSuperAdminCreatedProject: true,
      };
      const newProject = await Project.createInstance(projectParam);
      const roleDetails = await Role.getBy('Project Admin');
      if (CompanyDetails != null) {
        const companyParam = CompanyDetails.Company;
        const companyObject = {
          companyName: companyParam.companyName,
          website: companyParam.website,
          address: companyParam.address,
          secondAddress: companyParam.secondAddress,
          country: companyParam.country,
          city: companyParam.city,
          companyAutoId: companyParam.companyAutoId,
          state: companyParam.state,
          zipCode: companyParam.zipCode,
          scope: companyParam.scope,
          logo: companyParam.logo,
          createdBy: req.user.id,
          ProjectId: newProject.id,
          ParentCompanyId: companyParam.ParentCompanyId,
          isParent: false,
          isDeleted: false,
        };
        const newCompany = await Company.create(companyObject);
        const memberParam = {
          UserId: userId,
          firstName: projectData.firstName,
          CompanyId: newCompany.id,
          RoleId: roleDetails.id,
          createdBy: req.user.id,
          phoneNumber: projectData.phoneNumber,
          phoneCode: projectData.phoneCode,
          memberId: 1,
          ParentCompanyId: emailValidationData.id,
          password: generatePassword(),
          ProjectId: newProject.id,
          status: 'completed',
        };
        const newMember = await Member.createInstance(memberParam);
        await this.createMemberNotificationPreference(newMember, 'member');
        await this.createMemberLocationFollowPreference(
          newProject.id,
          emailValidationData.id,
          newMember.id,
          newProject.projectName,
          userId,
          'member',
        );
        const mailObject = {
          firstName: projectData.assignedTo,
          projectName: newProject.projectName,
          email: req.body.email,
        };
        await MAILER.sendMail(
          mailObject,
          'addproject',
          'You were added as a member',
          'Assigned a New Project',
          (info, err) => {
            console.log(info, err);
          },
        );
        return newMember;
      }
    } catch (err) {
      console.log(err);
    }
  },
  // Project Billing Histories
  async projectsBillingHistories(req) {
    try {
      const { search, pageSize, pageNo, sortColumn, sortType, userName, projectName, planType } =
        req.query;
      let count = 0;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const projectList = await Project.getAllProjectsForBilling(
        pageSize,
        offset,
        sortColumn,
        sortType,
        userName,
        projectName,
        planType,
        search,
      );
      count = projectList.length;
      return { projectList, count };
    } catch (err) {
      console.log(err);
    }
  },
  // Single Project Billing Histories
  async getProjectBillingHistories(req) {
    try {
      const { search, recepientName, paymentMethod, amount } = req.query;
      const billingStatus = req.query.status;
      const projectId = req.params.id;
      const projectBillingHistories = await ProjectBillingHistories.getProjectsWithBillingHistories(
        projectId,
        recepientName,
        billingStatus,
        paymentMethod,
        amount,
        search,
      );
      return { projectBillingHistories };
    } catch (err) {
      console.log(err);
    }
  },
  async createMemberNotificationPreference(memberData, data) {
    await this.returnProjectModel();
    if (data === 'member') {
      const memberDetail = await Member.findOne({
        where: { id: memberData.id, isDeleted: false },
      });
      const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
        where: { isDeleted: false },
      });
      const getProject = await Project.findOne({
        where: {
          isDeleted: false,
          id: +memberDetail.ProjectId,
        },
        include: [
          {
            where: { isDeleted: false },
            association: 'TimeZone',
            required: false,
            attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
          },
        ],
      });
      const attr = {
        time: '05:00',
        timeFormat: 'AM',
      };
      let projectObject;
      if (getProject) {
        projectObject = getProject.toJSON();
      }
      if (projectObject?.TimeZone) {
        attr.TimeZoneId = projectObject.TimeZone.id;
      } else {
        attr.TimeZoneId = 3;
      }
      await Member.update(attr, { where: { id: memberDetail.id } });
      for (const item of getNotificationPreferenceItemsList) {
        const object = {
          MemberId: memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };

        if (
          (item.id === 7 &&
            item.description === 'When a comment is added to a delivery/crane/concrete request' &&
            item.itemId === 4 &&
            item.emailNotification === true &&
            item.inappNotification === false &&
            item.isDeleted === false) ||
          item.inappNotification === true
        ) {
          object.instant = true;
          object.dailyDigest = false;
        }

        await NotificationPreference.createInstance(object);
      }

    } else if (data === 'members') {
      const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
        where: { isDeleted: false },
      });
      memberData.map(async (member) => {
        const memberData1 = member;

        const getProject = await Project.findOne({
          where: {
            isDeleted: false,
            id: +memberData1.ProjectId,
          },
          include: [
            {
              where: { isDeleted: false },
              association: 'TimeZone',
              required: false,
              attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
            },
          ],
        });
        const attr = {
          time: '05:00',
          timeFormat: 'AM',
        };
        let projectObject;
        if (getProject) {
          projectObject = getProject.toJSON();
        }
        if (projectObject?.TimeZone) {
          attr.TimeZoneId = projectObject.TimeZone.id;
        } else {
          attr.TimeZoneId = 3;
        }
        await Member.update(attr, { where: { id: memberData1.id } });
        for (const item of getNotificationPreferenceItemsList) {
          const isSpecialCase =
            item.id === 7 &&
            item.description === 'When a comment is added to a delivery/crane/concrete request' &&
            item.itemId === 4 &&
            item.emailNotification === true &&
            item.inappNotification === false &&
            item.isDeleted === false;

          const object = {
            MemberId: memberData1.id,
            ProjectId: memberData1.ProjectId,
            ParentCompanyId: memberData1.ParentCompanyId,
            NotificationPreferenceItemId: item.id,
            instant: isSpecialCase || item.inappNotification === true,
            dailyDigest: !(isSpecialCase || item.inappNotification === true),
            isDeleted: false,
          };

          await NotificationPreference.createInstance(object);
        }

      });
    }
  },
  async getTotalProjects() {
    const data = await Project.findAll({
      where: {
        isDeleted: false,
      },
      include: [
        {
          association: 'userDetails',
          attributes: ['id', 'firstName', 'email', 'lastName'],
        },
        {
          association: 'ParentCompany',
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              where: { isParent: true },
              attributes: ['id', 'companyName'],
              required: true,
            },
          ],
        },
      ],
      attributes: [
        'id',
        'projectName',
        [Sequelize.literal('date("startDate")'), 'startDate'],
        [Sequelize.literal('date("endDate")'), 'endDate'],
        'status',
      ],
    }).then((projects) => {
      if (projects && projects.length > 0) {
        projects.map((project) => {
          const projectData = project;
          if (!projectData.status) projectData.status = 'active';
          return project;
        });
        return projects;
      }
    });
    return data;
  },
  async extendProjectDuration(req) {
    const project = await Project.findOne({
      where: {
        isDeleted: false,
        id: req.body.ProjectId,
      },
      attributes: ['id', 'StripeSubscriptionId', 'status', 'startDate', 'endDate', 'PlanId'],
    });
    if (project?.StripeSubscriptionId) {
      const subscriptionObject = await StripeSubscription.findOne({
        where: {
          id: project.StripeSubscriptionId,
        },
      });
      if (subscriptionObject) {
        const subscription = await stripe.subscriptions.update(subscriptionObject.subscriptionId, {
          trial_end:
            new Date(moment(req.body.date, 'YYYY-MM-DD').format('YYYY-MM-DD 23:59:59')).getTime() /
            1000,
        });
        if (subscription) {
          const currentDate1 = moment().format('YYYY-MM-DD');
          const requestedEndDate1 = moment(req.body.date, 'YYYY-MM-DD').format('YYYY-MM-DD');
          const splittedCurrentDate = currentDate1.split('-');
          const currentDate = moment([
            splittedCurrentDate[0],
            splittedCurrentDate[1] - 1,
            splittedCurrentDate[2],
          ]);
          const splittedRequestedEndDate = requestedEndDate1.split('-');
          const requestedEndDate = moment([
            splittedRequestedEndDate[0],
            splittedRequestedEndDate[1] - 1,
            splittedRequestedEndDate[2],
          ]);
          const numberOfDaysExtended = requestedEndDate.diff(currentDate, 'days');
          const updateObject = {
            status: null,
            endDate: moment(req.body.date).format('YYYY-MM-DD 00:00:00+00'),
          };
          if (+numberOfDaysExtended >= 365) {
            updateObject.PlanId = 2;
          }
          const projectExtended = await Project.update(updateObject, { where: { id: project.id } });
          return !!projectExtended;
        }
        return false;
      }
      return false;
    }
    if (project && !project.StripeSubscriptionId) {
      const currentDate1 = moment().format('YYYY-MM-DD');
      const requestedEndDate1 = moment(req.body.date, 'YYYY-MM-DD').format('YYYY-MM-DD');
      const splittedCurrentDate = currentDate1.split('-');
      const currentDate = moment([
        splittedCurrentDate[0],
        splittedCurrentDate[1] - 1,
        splittedCurrentDate[2],
      ]);
      const splittedRequestedEndDate = requestedEndDate1.split('-');
      const requestedEndDate = moment([
        splittedRequestedEndDate[0],
        splittedRequestedEndDate[1] - 1,
        splittedRequestedEndDate[2],
      ]);
      const numberOfDaysExtended = requestedEndDate.diff(currentDate, 'days');
      const updateObject = {
        status: null,
        endDate: moment(req.body.date).format('YYYY-MM-DD 00:00:00+00'),
      };
      if (+numberOfDaysExtended >= 365) {
        updateObject.PlanId = 2;
      }
      const projectExtended = await Project.update(updateObject, { where: { id: project.id } });
      return !!projectExtended;
    }
    return false;
  },

  async updateProjectSharingSettings(payload) {
    try {
      const projectSettings = await ProjectSettings.update(payload, {
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      return projectSettings;
    } catch (err) {
      return err;
    }
  },
  async uploadLogisticPlan(inputData, done) {
    const { params } = inputData;
    let uploadedPdfFileLink;
    let uploadedPdfFileName;
    awsConfig.logisticPlanUpload(inputData, async (res, err) => {
      if (!err) {
        uploadedPdfFileLink = res[0].fileLink;
        uploadedPdfFileName = res[0].fileName;
        const fileType = inputData.file.originalname.replace(/^.*\./, '');
        let payload;
        if (fileType.toLowerCase() === 'pdf') {
          payload = {
            isPdfUploaded: true,
            pdfOriginalName: uploadedPdfFileName,
            projectLogisticPlanUrl: uploadedPdfFileLink,
            convertedImageLinks: null,
            sitePlanStatus: 'uploading',
          };
        } else {
          payload = {
            isPdfUploaded: true,
            pdfOriginalName: uploadedPdfFileName,
            projectLogisticPlanUrl: uploadedPdfFileLink,
            convertedImageLinks: null,
            sitePlanStatus: 'uploaded',
          };
        }
        const projectSettings = await ProjectSettings.update(payload, {
          where: {
            ProjectId: +params.ProjectId,
          },
        });
        if (projectSettings) {
          done(
            {
              fileUrl: uploadedPdfFileLink,
              fileName: uploadedPdfFileName,
            },
            false,
          );
        } else {
          done(null, err);
        }
      } else {
        done(null, err);
      }
    });
  },

  async generatePublicUrlForExistingProjects() {
    const projectList = await Project.findAll({
      where: { isDeleted: false },
    });
    for (let index = 0; index < projectList.length; index += 1) {
      const project = projectList[index];
      const textToBeEncrypted = `${project.id}_${project.projectName}`;
      const uniqueStringForAProject = cryptr.encrypt(textToBeEncrypted);
      const projectPubliWebsiteUrl =
        await deepLinkService.getGuestUserDeepLink(uniqueStringForAProject);
      const payload = {
        publicWebsiteUrl: projectPubliWebsiteUrl.link,
      };
      await ProjectSettings.update(payload, {
        where: {
          ProjectId: +project.id,
        },
      });
      if (+projectList.length === index + 1) {
        return {
          error: false,
          status: 'done',
        };
      }
    }
  },
  async decodeProjectDetailUrl(req) {
    let ProjectId;
    if (req.body.encodeUrl) {
      const url = req.body.encodeUrl;
      const decodedUrl = cryptr.decrypt(url);
      const splitCodeUrl = decodedUrl.split('_');
      ProjectId = +splitCodeUrl[0];
    }
    const projectList = await Project.findOne({
      where: { id: ProjectId },
      attributes: [
        'id',
        'projectName',
        'projectLocation',
        'projectLocationLatitude',
        'projectLocationLongitude',
        'ParentCompanyId',
        'status',
      ],
      include: ['ProjectSettings'],
    });
    return { projectList };
  },
  async createMemberLocationFollowPreference(
    ProjectId,
    ParentCompanyId,
    MemberId,
    projectName,
    createdBy,
    data,
  ) {
    if (data === 'member') {
      const locationObject = {
        ProjectId,
        ParentCompanyId,
        notes: null,
        MemberId,
        createdBy,
        platform: 'web',
        locationName: projectName,
        locationPath: projectName,
        isDefault: true,
      };
      const location = await Locations.create(locationObject);
      const object = {
        MemberId,
        ProjectId,
        LocationId: location.id,
        follow: true,
        ParentCompanyId,
        isDeleted: false,
      };
      await LocationNotificationPreferences.createInstance(object);
    } else if (data === 'members') {
      MemberId.map(async (memberData) => {
        const memberData1 = memberData;
        const locationObject = {
          ProjectId,
          ParentCompanyId,
          notes: null,
          MemberId: memberData1.id,
          createdBy,
          platform: 'web',
          locationName: projectName,
          locationPath: projectName,
          isDefault: true,
        };
        const location = await Locations.create(locationObject);
        const object = {
          MemberId: memberData1.id,
          ProjectId,
          LocationId: location.id,
          follow: true,
          ParentCompanyId,
          isDeleted: false,
        };
        await LocationNotificationPreferences.createInstance(object);
      });
    }
  },
  async generatePublicUrlForCurrentProject(data) {
    try {
      const textToBeEncrypted = `${data.ProjectId}_${data.projectName}`;
      const uniqueStringForAProject = cryptr.encrypt(textToBeEncrypted);
      const projectPublicWebsiteUrl =
        await deepLinkService.getGuestUserDeepLink(uniqueStringForAProject);
      const payload = {
        publicWebsiteUrl: projectPublicWebsiteUrl.link,
      };
      await ProjectSettings.update(payload, {
        where: {
          ProjectId: +data.ProjectId,
        },
      });
    } catch (error) {
      console.error('Error:', error);
    }
  },

  async updateDashboardLogisticPlan(payload) {
    try {
      const projectSettings = await ProjectSettings.update(payload, {
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      return projectSettings;
    } catch (err) {
      return err;
    }
  },

  async retoolParentCompanyWithProjects(done) {
    try {
      const projectList = await Project.retoolParentCompanyWithProjects();
      const companyList = [];
      projectList.forEach((item) => {
        const companyData = item.ParentCompany.Company[0];
        const index = companyList.findIndex(
          (companyNew) => companyNew.id === item.ParentCompany.id,
        );

        if (index === -1) {
          companyList.push({
            id: item.ParentCompany.id,
            companyName: companyData.companyName,
            projectList: [{ id: item.id, projectName: item.projectName }],
          });
        } else {
          companyList[index].projectList.push({ id: item.id, projectName: item.projectName });
        }
      });
      if (companyList && companyList.length > 0) {
        companyList.sort((a, b) => {
          const companyA = a.companyName.toLowerCase();
          const companyB = b.companyName.toLowerCase();
          return companyA.localeCompare(companyB);
        });
      }
      return companyList;
    } catch (e) {
      throw new Error(e);
    }
  },
  async updatedSitePlanInAWS(req) {
    try {
      const { statusCode, imageLinks, ProjectId } = req.body;
      if (statusCode === 200) {
        const cloudFrontURl = process.env.CLOUD_FRONT_URL;
        const modifiedImageLinks = imageLinks.map((link) => cloudFrontURl + link);
        const payload = {
          isPdfUploaded: true,
          convertedImageLinks: JSON.stringify(modifiedImageLinks),
          sitePlanStatus: 'uploaded',
        };
        await ProjectSettings.update(payload, {
          where: {
            ProjectId: +ProjectId,
          },
        });
        global.io.emit('pdfToImageConvertion', payload);
      } else {
        const payload = {
          isPdfUploaded: false,
          convertedImageLinks: null,
          sitePlanStatus: 'failed',
        };
        await ProjectSettings.update(payload, {
          where: {
            ProjectId: +ProjectId,
          },
        });
        global.io.emit('pdfToImageConvertion', payload);
      }
      return true;
    } catch (err) {
      return err;
    }
  },
};

module.exports = projectService;
