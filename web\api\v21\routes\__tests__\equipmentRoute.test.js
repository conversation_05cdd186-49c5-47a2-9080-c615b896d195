const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../middlewares/validations', () => ({
  equipmentValidation: {
    addEquipment: jest.fn(),
    updateEquipment: jest.fn(),
    equipmentDetail: jest.fn(),
    craneEquipmentDetail: jest.fn(),
    listEquipmentType: jest.fn(),
    deleteEquipment: jest.fn(),
  },
}));

jest.mock('../../controllers', () => ({
  EquipmentController: {
    addEquipment: jest.fn(),
    updateEquipment: jest.fn(),
    listEquipment: jest.fn(),
    craneListEquipment: jest.fn(),
    listEquipmentType: jest.fn(),
    deleteEquipment: jest.fn(),
    getPresetEquipmentTypeList: jest.fn(),
    getMappedRequests: jest.fn(),
    deactivateEquipment: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
}));

describe('equipmentRoute', () => {
  let router;
  let equipmentRoute;
  let EquipmentController;
  let equipmentValidation;
  let passportConfig;
  let checkAdmin;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    equipmentRoute = require('../equipmentRoute');
    const controllers = require('../../controllers');
    EquipmentController = controllers.EquipmentController;
    const validations = require('../../middlewares/validations');
    equipmentValidation = validations.equipmentValidation;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = equipmentRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(7);
      expect(router.get).toHaveBeenCalledTimes(2);

      // Verify POST routes with validation and admin check
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/add_equipment',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        EquipmentController.addEquipment,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/update_equipment',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        EquipmentController.updateEquipment,
      );

      // POST routes with validation only
      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/equipment_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        EquipmentController.listEquipment,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/crane_equipment_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        EquipmentController.craneListEquipment,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/delete_equipments',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isProjectAdmin,
        EquipmentController.deleteEquipment,
      );

      // POST routes with auth only
      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/get_mapped_requests',
        passportConfig.isAuthenticated,
        EquipmentController.getMappedRequests,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        7,
        '/deactivate_equipment',
        passportConfig.isAuthenticated,
        EquipmentController.deactivateEquipment,
      );

      // GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/equipment_type_list/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        EquipmentController.listEquipmentType,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/preset_equipment_type_list',
        passportConfig.isAuthenticated,
        EquipmentController.getPresetEquipmentTypeList,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(6);
      expect(validate).toHaveBeenCalledWith(
        equipmentValidation.addEquipment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        equipmentValidation.updateEquipment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        equipmentValidation.equipmentDetail,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        equipmentValidation.craneEquipmentDetail,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        equipmentValidation.listEquipmentType,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        equipmentValidation.deleteEquipment,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = equipmentRoute.router;
      const result2 = equipmentRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      equipmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure different middleware combinations correctly', () => {
      equipmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Routes with validation + auth + admin (5 parameters)
      const routesWithAdmin = [postCalls[0], postCalls[1], postCalls[4]]; // add, update, delete
      routesWithAdmin.forEach(call => {
        expect(call).toHaveLength(5); // path + validation + auth + admin + controller
        expect(call[1]).toBe('mocked-validate-middleware');
        expect(call[3]).toBe(checkAdmin.isProjectAdmin);
      });

      // Routes with validation + auth (4 parameters)
      const routesWithValidation = [postCalls[2], postCalls[3], getCalls[0]]; // equipment_list, crane_equipment_list, equipment_type_list
      routesWithValidation.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // Routes with auth only (3 parameters)
      const routesWithAuthOnly = [postCalls[5], postCalls[6], getCalls[1]]; // get_mapped_requests, deactivate_equipment, preset_equipment_type_list
      routesWithAuthOnly.forEach(call => {
        expect(call).toHaveLength(3); // path + auth + controller
        expect(call[1]).toBe(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof equipmentRoute).toBe('object');
      expect(equipmentRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(equipmentRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(equipmentRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
