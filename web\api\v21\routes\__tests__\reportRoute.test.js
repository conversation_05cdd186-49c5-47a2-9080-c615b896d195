const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
  findUserPayload: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  ReportController: {
    deliveryRequest: jest.fn(),
    craneRequest: jest.fn(),
    concreteRequest: jest.fn(),
    exportDeliveryReport: jest.fn(),
    exportCraneReport: jest.fn(),
    exportConcreteReport: jest.fn(),
    weeklyCalendarRequest: jest.fn(),
    exportWeeklyCalendarRequest: jest.fn(),
    heatMapdeliveryRequest: jest.fn(),
    exportHeatMapRequest: jest.fn(),
    deleteSchedulerReport: jest.fn(),
    inspectionRequest: jest.fn(),
    exportInspectionReport: jest.fn(),
  },
  CornController: {
    getSchedulerReportRequest: jest.fn(),
    schedulerReportRequest: jest.fn(),
    getRerunReportRequest: jest.fn(),
    getSchedulerTimelineNames: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  deliveryReportsValidation: {
    SavedReports: { params: {}, body: {} },
    HeatMapDeliveryReports: { params: {}, body: {} },
    GetScheduleReports: { query: {}, body: {} },
    deleteSchedulerReport: { query: {} },
    rerunSchedulerReport: { query: {} },
  },
}));

describe('reportRoute', () => {
  let router;
  let reportRoute;
  let ReportController;
  let CornController;
  let passportConfig;
  let deliveryReportsValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    reportRoute = require('../reportRoute');
    const controllers = require('../../controllers');
    ReportController = controllers.ReportController;
    CornController = controllers.CornController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    deliveryReportsValidation = validations.deliveryReportsValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = reportRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all POST routes are configured
      expect(router.post).toHaveBeenCalledWith(
        '/delivery_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.deliveryRequest
      );

      expect(router.post).toHaveBeenCalledWith(
        '/crane_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.craneRequest
      );

      expect(router.post).toHaveBeenCalledWith(
        '/concrete_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.concreteRequest
      );

      expect(router.post).toHaveBeenCalledWith(
        '/export_delivery_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.exportDeliveryReport
      );

      expect(router.post).toHaveBeenCalledWith(
        '/export_crane_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.exportCraneReport
      );

      expect(router.post).toHaveBeenCalledWith(
        '/export_concrete_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.exportConcreteReport
      );

      expect(router.post).toHaveBeenCalledWith(
        '/weekly_calendar_request/:ProjectId/:void',
        passportConfig.isAuthenticated,
        ReportController.weeklyCalendarRequest
      );

      expect(router.post).toHaveBeenCalledWith(
        '/export_weekly_calendar/:ProjectId/:void',
        passportConfig.isAuthenticated,
        ReportController.exportWeeklyCalendarRequest
      );

      expect(router.post).toHaveBeenCalledWith(
        '/inspection_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.inspectionRequest
      );

      expect(router.post).toHaveBeenCalledWith(
        '/export_inspection_request/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ReportController.exportInspectionReport
      );

      expect(router.post).toHaveBeenCalledWith(
        '/schedule/:ProjectId/',
        passportConfig.isAuthenticated,
        CornController.schedulerReportRequest
      );

      // Verify GET routes
      expect(router.get).toHaveBeenCalledWith(
        '/scheduler-timeline-names',
        passportConfig.isAuthenticated,
        CornController.getSchedulerTimelineNames
      );

      // Verify DELETE routes
      expect(router.delete).toHaveBeenCalledWith(
        '/schedule',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        ReportController.deleteSchedulerReport
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = reportRoute.router;
      const result2 = reportRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes with saved middleware that sets req.body.saved = true', () => {
      reportRoute.router;

      // Find calls with saved middleware
      const savedExportDeliveryCall = router.post.mock.calls.find(call =>
        call[0] === '/saved/export_delivery_request/:ProjectId/:pageSize/:pageNo/:void'
      );
      const savedExportCraneCall = router.post.mock.calls.find(call =>
        call[0] === '/saved/export_crane_request/:ProjectId/:pageSize/:pageNo/:void'
      );
      const savedExportConcreteCall = router.post.mock.calls.find(call =>
        call[0] === '/saved/export_concrete_request/:ProjectId/:pageSize/:pageNo/:void'
      );
      const savedExportWeeklyCalendarCall = router.post.mock.calls.find(call =>
        call[0] === '/saved/export_weekly_calendar/:ProjectId/:void'
      );
      const savedExportHeatMapCall = router.post.mock.calls.find(call =>
        call[0] === '/saved/export_heat_map/:ProjectId/:pageSize/:pageNo/:void/:sortOrder'
      );
      const savedExportInspectionCall = router.post.mock.calls.find(call =>
        call[0] === '/saved/export_inspection_request/:ProjectId/:pageSize/:pageNo/:void'
      );

      // Test the middleware functions
      [savedExportDeliveryCall, savedExportCraneCall, savedExportConcreteCall,
       savedExportWeeklyCalendarCall, savedExportHeatMapCall, savedExportInspectionCall].forEach(call => {
        expect(call).toBeDefined();
        const middleware = call[1]; // First middleware should be the saved middleware
        expect(typeof middleware).toBe('function');

        // Test the middleware
        const req = { body: {} };
        const res = {};
        const next = jest.fn();

        middleware(req, res, next);

        expect(req.body.saved).toBe(true);
        expect(next).toHaveBeenCalled();
      });
    });

    it('should configure routes with validation middleware', () => {
      reportRoute.router;

      // Check that validate is called with correct validations
      expect(validate).toHaveBeenCalledWith(
        deliveryReportsValidation.SavedReports,
        { keyByField: true },
        { abortEarly: false }
      );

      expect(validate).toHaveBeenCalledWith(
        deliveryReportsValidation.HeatMapDeliveryReports,
        { keyByField: true },
        { abortEarly: false }
      );

      expect(validate).toHaveBeenCalledWith(
        deliveryReportsValidation.GetScheduleReports,
        { keyByField: true },
        { abortEarly: false }
      );

      expect(validate).toHaveBeenCalledWith(
        deliveryReportsValidation.deleteSchedulerReport,
        { keyByField: true },
        { abortEarly: false }
      );

      expect(validate).toHaveBeenCalledWith(
        deliveryReportsValidation.rerunSchedulerReport,
        { keyByField: true },
        { abortEarly: false }
      );
    });

    it('should configure GET routes with req.body.saved middleware', () => {
      reportRoute.router;

      const scheduleCall = router.get.mock.calls.find(call =>
        call[0] === '/schedule'
      );
      const savedReportsCall = router.get.mock.calls.find(call =>
        call[0] === '/saved-reports'
      );

      // Test schedule route middleware (sets saved = false)
      expect(scheduleCall).toBeDefined();
      const scheduleMiddleware = scheduleCall[1];
      expect(typeof scheduleMiddleware).toBe('function');

      const req1 = { body: {} };
      const res1 = {};
      const next1 = jest.fn();

      scheduleMiddleware(req1, res1, next1);

      expect(req1.body.saved).toBe(false);
      expect(next1).toHaveBeenCalled();

      // Test saved-reports route middleware (sets saved = true)
      expect(savedReportsCall).toBeDefined();
      const savedReportsMiddleware = savedReportsCall[1];
      expect(typeof savedReportsMiddleware).toBe('function');

      const req2 = { body: {} };
      const res2 = {};
      const next2 = jest.fn();

      savedReportsMiddleware(req2, res2, next2);

      expect(req2.body.saved).toBe(true);
      expect(next2).toHaveBeenCalled();
    });
  });

  describe('async middleware functions', () => {
    it('should test the async middleware for weekly_calendar_request/no_auth route', async () => {
      // Mock findUserPayload to return a user
      const mockUser = { id: 1, name: 'Test User' };
      passportConfig.findUserPayload.mockResolvedValue(mockUser);

      reportRoute.router;

      const noAuthCall = router.post.mock.calls.find(call =>
        call[0] === '/weekly_calendar_request/no_auth/:ProjectId/:void'
      );

      expect(noAuthCall).toBeDefined();
      const asyncMiddleware = noAuthCall[1]; // First middleware should be the async middleware
      expect(typeof asyncMiddleware).toBe('function');

      // Test the async middleware
      const req = {
        body: {
          noAuth: { userId: 1, token: 'test-token' }
        }
      };
      const res = {};
      const next = jest.fn();

      await asyncMiddleware(req, res, next);

      expect(passportConfig.findUserPayload).toHaveBeenCalledWith({
        userId: 1,
        token: 'test-token'
      });
      expect(req.user).toBe(mockUser);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof reportRoute).toBe('object');
      expect(reportRoute).toHaveProperty('router');
      
      const descriptor = Object.getOwnPropertyDescriptor(reportRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(reportRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});