const moment = require('moment');
const { Op } = require('sequelize');
const inspectionReportService = require('../inspectionReportService');
const {
    InspectionPerson,
    VoidList,
    SchedulerReport,
    Member,
    Enterprise,
    User,
} = require('../../models');
const helper = require('../../helpers/domainHelper');
const ApiError = require('../../helpers/apiError');
const pdfInspectionReportService = require('../pdfInspectionReportService');
const csvInspectionReportService = require('../csvInspectionReportService');
const excelInspectionReportService = require('../excelInspectionReportService');

// Mock publicUser and publicMember
let mockPublicUser;
let mockPublicMember;

// Mock all dependencies
jest.mock('../../models', () => ({
    InspectionPerson: {
        findAll: jest.fn(),
        findOne: jest.fn(),
    },
    VoidList: {
        findAll: jest.fn(),
    },
    SchedulerReport: {
        findAll: jest.fn(),
    },
    Member: {
        findOne: jest.fn(),
    },
    Enterprise: {
        findOne: jest.fn(),
    },
    User: {
        findOne: jest.fn(),
    },
    Sequelize: {},
    sequelize: {},
    Op: {},
}));

jest.mock('../../helpers/domainHelper', () => ({
    getDynamicModel: jest.fn(),
    returnProjectModel: jest.fn(),
}));

jest.mock('../pdfInspectionReportService', () => ({
    pdfFormatOfInspectionRequest: jest.fn(),
}));

jest.mock('../csvInspectionReportService', () => ({
    csvFormatOfInspectionRequest: jest.fn(),
}));

jest.mock('../excelInspectionReportService', () => ({
    inspectionReport: jest.fn(),
}));

jest.mock('../exportService', () => ({
    // Mock any methods that might be used
}));

jest.mock('moment', () => {
    const actualMoment = jest.requireActual('moment');
    const mockMoment = jest.fn((date) => {
        if (date) {
            return actualMoment(date);
        }
        return actualMoment('2024-01-15T10:00:00Z');
    });

    // Copy all moment methods
    Object.keys(actualMoment).forEach((key) => {
        mockMoment[key] = actualMoment[key];
    });

    return mockMoment;
});

describe('InspectionReportService', () => {
    let mockInputData;
    let mockUser;
    let mockEnterprise;
    let mockMember;
    let mockVoidList;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup mock public models
        mockPublicUser = {
            findOne: jest.fn(),
        };
        mockPublicMember = {
            findOne: jest.fn(),
        };

        // Setup common mock data
        mockUser = {
            id: 1,
            email: '<EMAIL>',
            domainName: 'testdomain',
        };

        mockEnterprise = {
            id: 1,
            name: 'testdomain',
            ParentCompanyId: 1,
            status: 'completed',
        };

        mockMember = {
            id: 1,
            UserId: 1,
            EnterpriseId: 1,
            RoleId: 2,
            isDeleted: false,
            isAccount: false,
        };

        mockVoidList = [{ InspectionRequestId: 1 }, { InspectionRequestId: 2 }];

        mockInputData = {
            user: mockUser,
            body: {
                ParentCompanyId: 1,
                ProjectId: 1,
                search: '',
                startdate: '2024-01-01',
                enddate: '2024-01-31',
                sort: 'DESC',
                sortByField: 'inspectionStart',
                exportType: null,
            },
            params: {
                ProjectId: 1,
                void: '0',
            },
            headers: {
                timezoneoffset: '-330',
            },
        };

        // Setup default mock implementations
        Enterprise.findOne.mockResolvedValue(mockEnterprise);
        User.findOne.mockResolvedValue(mockUser);
        Member.findOne.mockResolvedValue(mockMember);
        VoidList.findAll.mockResolvedValue(mockVoidList);
        mockPublicUser.findOne.mockResolvedValue(mockUser);
        mockPublicMember.findOne.mockResolvedValue(mockMember);
        helper.getDynamicModel.mockResolvedValue({
            Member,
            User,
        });
        helper.returnProjectModel.mockResolvedValue({
            Member: mockPublicMember,
            User: mockPublicUser,
        });

        // Add missing methods to the service for testing
        inspectionReportService.getSearchData = jest.fn().mockResolvedValue({
            rows: [],
            count: 0,
            error: null
        });
        inspectionReportService.getStartAndEndDate = jest.fn().mockReturnValue([
            moment('2024-01-01').startOf('day'),
            moment('2024-01-31').endOf('day'),
        ]);
        inspectionReportService.handlePdfExport = jest.fn();
        inspectionReportService.handleExcelExport = jest.fn();
        inspectionReportService.handleCsvExport = jest.fn();
        inspectionReportService.processCalendarData = jest.fn().mockResolvedValue({ rows: [], count: 0 });
        inspectionReportService.processRegularData = jest.fn().mockResolvedValue({ rows: [], count: 0 });
        inspectionReportService.handleResponse = jest.fn();
    });

    describe('resolveDomainName', () => {
        it('should return domain name when user has domainName', async () => {
            const result = await inspectionReportService.resolveDomainName(mockUser);
            expect(result).toBe('testdomain');
        });

        it('should return empty string when user has no domainName', async () => {
            const userWithoutDomain = { ...mockUser, domainName: null };
            const result = await inspectionReportService.resolveDomainName(userWithoutDomain);
            expect(result).toBe('');
        });

        it('should return empty string when enterprise not found', async () => {
            Enterprise.findOne.mockResolvedValue(null);
            const result = await inspectionReportService.resolveDomainName(mockUser);
            expect(result).toBe('');
        });
    });

    describe('findEnterprise', () => {
        it('should return null when email is not provided', async () => {
            const inputData = { user: {} };
            const result = await inspectionReportService.findEnterprise(inputData);
            expect(result).toBeNull();
        });

        it('should return enterprise for account member', async () => {
            const accountMember = { ...mockMember, isAccount: true };
            mockPublicMember.findOne.mockResolvedValue(accountMember);

            const inputData = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 },
            };

            const result = await inspectionReportService.findEnterprise(inputData);
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: accountMember.EnterpriseId, status: 'completed' },
            });
        });

        it('should return enterprise for non-account member', async () => {
            const inputData = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 },
            };

            const result = await inspectionReportService.findEnterprise(inputData);
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' },
            });
        });

        it('should return enterprise when member data is null', async () => {
            mockPublicMember.findOne.mockResolvedValue(null);
            const inputData = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 },
            };

            const result = await inspectionReportService.findEnterprise(inputData);
            expect(result).toEqual(mockEnterprise);
            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 1, status: 'completed' },
            });
        });
    });

    describe('getDynamicModel', () => {
        it('should get dynamic model and update user data', async () => {
            const result = await inspectionReportService.getDynamicModel(mockInputData);
            expect(result).toBe(mockInputData.body.ProjectId);
            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
            expect(User.findOne).toHaveBeenCalledWith({
                where: { email: mockUser.email },
            });
        });

        it('should handle case when domain name is not available', async () => {
            const inputData = {
                ...mockInputData,
                user: { ...mockUser, domainName: null },
            };

            // Mock findEnterprise to return null so domainName stays empty
            inspectionReportService.findEnterprise = jest.fn().mockResolvedValue(null);

            await inspectionReportService.getDynamicModel(inputData);
            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });
    });

    describe('buildConditions', () => {
        it('should build default search conditions', async () => {
            const incomeData = {
                search: 'test',
                ProjectId: 1,
            };

            const result = await inspectionReportService.buildConditions(
                incomeData,
                { ProjectId: 1 },
                'default',
                mockInputData,
            );

            expect(result[Op.or]).toBeDefined();
            expect(result[Op.or].length).toBe(5);
        });

        it('should build date conditions', async () => {
            const incomeData = {
                startdate: '2024-01-01',
                enddate: '2024-01-31',
            };

            // Mock getStartAndEndDate method
            inspectionReportService.getStartAndEndDate.mockReturnValue([
                moment('2024-01-01').startOf('day'),
                moment('2024-01-31').endOf('day'),
            ]);

            const result = await inspectionReportService.buildConditions(
                incomeData,
                { ProjectId: 1 },
                'date',
                mockInputData,
            );

            expect(result.inspectionStart).toBeDefined();
            expect(result.inspectionStart[Op.between]).toBeDefined();
        });

        it('should build filter conditions', async () => {
            const incomeData = {
                descriptionFilter: 'test',
                equipmentFilter: [1, 2],
                statusFilter: ['active'],
                gateFilter: [1],
                companyFilter: [1],
                inspectionStatusFilter: ['pending'],
                inspectionTypeFilter: ['routine'],
                locationFilter: [1],
                defineFilter: [1],
                pickFrom: 'location1',
                pickTo: 'location2',
                idFilter: 123,
            };

            const result = await inspectionReportService.buildConditions(
                incomeData,
                { ProjectId: 1 },
                'filter',
                mockInputData,
            );

            expect(result.description).toBeDefined();
            expect(result['$equipmentDetails.Equipment.id$']).toBeDefined();
            expect(result.status).toBeDefined();
            expect(result['$gateDetails.Gate.id$']).toBeDefined();
            expect(result['$companyDetails.Company.id$']).toBeDefined();
            expect(result.inspectionStatus).toBeDefined();
            expect(result.inspectionType).toBeDefined();
            expect(result['$location.id$']).toBeDefined();
            expect(result['$defineWorkDetails.DeliverDefineWork.id$']).toBeDefined();
            expect(result.cranePickUpLocation).toBeDefined();
            expect(result.craneDropOffLocation).toBeDefined();
            expect(result.InspectionId).toBe(123);
        });
    });

    describe('processInspectionList', () => {
        it('should process inspection list successfully', async () => {
            const mockInspectionList = {
                rows: [
                    { id: 1, inspectionStart: '2024-01-01' },
                    { id: 2, inspectionStart: '2024-01-02' },
                ],
            };

            const mockReq = {
                body: {
                    sort: 'DESC',
                    sortByField: 'inspectionStart',
                },
                user: mockUser,
            };

            const mockDone = jest.fn();

            await inspectionReportService.processInspectionList(
                mockInspectionList,
                { pageSize: 10 },
                mockReq,
                mockDone,
            );

            expect(mockDone).toHaveBeenCalled();
        });

        it('should handle errors in processing', async () => {
            const mockInspectionList = {
                rows: [],
            };

            const mockReq = {
                body: {},
                user: mockUser,
            };

            const mockDone = jest.fn();

            // Mock getSearchData to return undefined to trigger the error path
            inspectionReportService.getSearchData.mockResolvedValue(undefined);

            await inspectionReportService.processInspectionList(
                mockInspectionList,
                { pageSize: 10 },
                mockReq,
                mockDone,
            );

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'An error occurred during processing' });
        });
    });

    describe('getVoidInspections', () => {
        it('should return list of void inspection IDs', async () => {
            const result = await inspectionReportService.getVoidInspections({ ProjectId: 1 });
            expect(result).toEqual([1, 2]);
            expect(VoidList.findAll).toHaveBeenCalledWith({
                where: {
                    ProjectId: 1,
                    isDeliveryRequest: true,
                    InspectionRequestId: { [Op.ne]: null },
                },
            });
        });

        it('should return empty array when no void inspections found', async () => {
            VoidList.findAll.mockResolvedValue([]);
            const result = await inspectionReportService.getVoidInspections({ ProjectId: 1 });
            expect(result).toEqual([]);
        });
    });

    describe('buildBaseCondition', () => {
        it('should build base condition for non-void inspections', () => {
            const result = inspectionReportService.buildBaseCondition(
                { ProjectId: 1, void: '0' },
                [1, 2],
            );

            expect(result.ProjectId).toBe(1);
            expect(result.isQueued).toBe(false);
            expect(result['$InspectionRequest.id$'][Op.and][Op.notIn]).toEqual([1, 2]);
        });

        it('should build base condition for void inspections', () => {
            const result = inspectionReportService.buildBaseCondition(
                { ProjectId: 1, void: '1' },
                [1, 2],
            );

            expect(result.ProjectId).toBe(1);
            expect(result.isQueued).toBe(false);
            expect(result['$InspectionRequest.id$'][Op.and][Op.in]).toEqual([1, 2]);
        });

        it('should handle string ProjectId conversion', () => {
            const result = inspectionReportService.buildBaseCondition(
                { ProjectId: '123', void: '0' },
                [1, 2],
            );

            expect(result.ProjectId).toBe(123);
        });
    });

    describe('shouldUseCalendar', () => {
        it('should return true when company filter is present', () => {
            const result = inspectionReportService.shouldUseCalendar(
                {},
                { void: '0' },
                { companyFilter: [1] },
            );
            expect(result).toBe(true);
        });

        it('should return true when gate filter is present', () => {
            const result = inspectionReportService.shouldUseCalendar(
                {},
                { void: '0' },
                { gateFilter: [1] },
            );
            expect(result).toBe(true);
        });

        it('should return false when no filters are present and not void', () => {
            const result = inspectionReportService.shouldUseCalendar(
                {},
                { void: '0' },
                { upcoming: true },
            );
            expect(result).toBe(false);
        });
    });

    describe('getDateRange', () => {
        it('should return correct date range with timezone offset', () => {
            const [start, end] = inspectionReportService.getDateRange('2024-01-01', '2024-01-31', {
                headers: { timezoneoffset: '-330' },
            });

            expect(start.format('YYYY-MM-DD')).toBe('2024-01-01');
            expect(end.format('YYYY-MM-DD')).toBe('2024-01-31');
        });
    });

    describe('getStartOfDayOffset', () => {
        it('should return start of day with timezone offset', () => {
            const result = inspectionReportService.getStartOfDayOffset({
                headers: { timezoneoffset: '-330' },
            });

            expect(result.format('YYYY-MM-DD')).toBe(moment().format('YYYY-MM-DD'));
        });

        it('should handle missing timezone offset', () => {
            const result = inspectionReportService.getStartOfDayOffset({
                headers: {},
            });

            expect(result.format('YYYY-MM-DD')).toBe(moment().format('YYYY-MM-DD'));
        });

        it('should handle positive timezone offset', () => {
            const result = inspectionReportService.getStartOfDayOffset({
                headers: { timezoneoffset: '300' },
            });

            expect(result.format('YYYY-MM-DD')).toBe(moment().format('YYYY-MM-DD'));
        });
    });

    describe('returnProjectModel', () => {
        it('should call helper.returnProjectModel and set publicMember and publicUser', async () => {
            const mockModelData = {
                Member: { findOne: jest.fn() },
                User: { findOne: jest.fn() },
            };
            helper.returnProjectModel.mockResolvedValue(mockModelData);

            await inspectionReportService.returnProjectModel();

            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle error in returnProjectModel', async () => {
            const error = new Error('Model error');
            helper.returnProjectModel.mockRejectedValue(error);

            await expect(inspectionReportService.returnProjectModel()).rejects.toThrow('Model error');
        });
    });

    describe('sortResponse', () => {
        it('should sort response in ascending order', () => {
            const response = [
                { inspectionStart: '2024-01-03' },
                { inspectionStart: '2024-01-01' },
                { inspectionStart: '2024-01-02' },
            ];

            const result = inspectionReportService.sortResponse(response, 'ASC', 'inspectionStart');

            expect(result[0].inspectionStart).toBe('2024-01-01');
            expect(result[1].inspectionStart).toBe('2024-01-02');
            expect(result[2].inspectionStart).toBe('2024-01-03');
        });

        it('should sort response in descending order', () => {
            const response = [
                { inspectionStart: '2024-01-01' },
                { inspectionStart: '2024-01-03' },
                { inspectionStart: '2024-01-02' },
            ];

            const result = inspectionReportService.sortResponse(response, 'DESC', 'inspectionStart');

            expect(result[0].inspectionStart).toBe('2024-01-03');
            expect(result[1].inspectionStart).toBe('2024-01-02');
            expect(result[2].inspectionStart).toBe('2024-01-01');
        });

        it('should handle empty response array', () => {
            const result = inspectionReportService.sortResponse([], 'DESC', 'inspectionStart');
            expect(result).toEqual([]);
        });

        it('should handle single item response', () => {
            const response = [{ inspectionStart: '2024-01-01' }];
            const result = inspectionReportService.sortResponse(response, 'DESC', 'inspectionStart');
            expect(result).toEqual(response);
        });
    });

    describe('handleExport', () => {
        let mockReq;
        let mockDone;

        beforeEach(() => {
            mockReq = {
                body: { exportType: 'PDF' },
            };
            mockDone = jest.fn();

            // Mock the export methods
            inspectionReportService.handlePdfExport = jest.fn();
            inspectionReportService.handleExcelExport = jest.fn();
            inspectionReportService.handleCsvExport = jest.fn();
        });

        it('should handle PDF export', async () => {
            mockReq.body.exportType = 'PDF';
            const response = { data: 'test' };

            await inspectionReportService.handleExport(response, mockReq, mockDone);

            expect(inspectionReportService.handlePdfExport).toHaveBeenCalledWith(
                response,
                mockReq,
                mockDone,
            );
        });

        it('should handle EXCEL export', async () => {
            mockReq.body.exportType = 'EXCEL';
            const response = { data: 'test' };

            await inspectionReportService.handleExport(response, mockReq, mockDone);

            expect(inspectionReportService.handleExcelExport).toHaveBeenCalledWith(
                response,
                mockReq,
                mockDone,
            );
        });

        it('should handle CSV export', async () => {
            mockReq.body.exportType = 'CSV';
            const response = { data: 'test' };

            await inspectionReportService.handleExport(response, mockReq, mockDone);

            expect(inspectionReportService.handleCsvExport).toHaveBeenCalledWith(
                response,
                mockReq,
                mockDone,
            );
        });

        it('should handle invalid export type', async () => {
            mockReq.body.exportType = 'INVALID';
            const response = { data: 'test' };

            const result = await inspectionReportService.handleExport(response, mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Invalid export type' });
        });

        it('should handle missing export type', async () => {
            mockReq.body.exportType = null;
            const response = { data: 'test' };

            const result = await inspectionReportService.handleExport(response, mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Invalid export type' });
        });
    });

    describe('handleResponse', () => {
        let mockReq;
        let mockDone;

        beforeEach(() => {
            mockReq = {
                body: {},
            };
            mockDone = jest.fn();
            inspectionReportService.handleExport = jest.fn();
        });

        it('should call handleExport when exportType is present', async () => {
            mockReq.body.exportType = 'PDF';
            const response = { data: 'test' };

            await inspectionReportService.handleResponse(response, mockReq, mockDone);

            expect(inspectionReportService.handleExport).toHaveBeenCalledWith(
                response,
                mockReq,
                mockDone,
            );
        });

        it('should call done directly when no exportType', async () => {
            const response = { data: 'test' };

            await inspectionReportService.handleResponse(response, mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(response, false);
            expect(inspectionReportService.handleExport).not.toHaveBeenCalled();
        });
    });

    // Additional comprehensive tests for edge cases and error handling
    describe('Edge Cases and Error Handling', () => {
        describe('resolveDomainName - Additional Cases', () => {
            it('should handle undefined user', async () => {
                const result = await inspectionReportService.resolveDomainName({});
                expect(result).toBe('');
            });

            it('should handle empty domainName string', async () => {
                const user = { domainName: '' };
                const result = await inspectionReportService.resolveDomainName(user);
                expect(result).toBe('');
            });

            it('should handle database error when finding enterprise', async () => {
                const error = new Error('Database connection failed');
                Enterprise.findOne.mockRejectedValue(error);

                await expect(inspectionReportService.resolveDomainName(mockUser)).rejects.toThrow(
                    'Database connection failed',
                );
            });
        });

        describe('findEnterprise - Additional Cases', () => {
            it('should handle null user data from database', async () => {
                mockPublicUser.findOne.mockResolvedValue(null);
                const inputData = { user: { email: '<EMAIL>' }, body: { ParentCompanyId: 1 } };

                const result = await inspectionReportService.findEnterprise(inputData);
                expect(result).toBeNull();
            });

            it('should handle member not found scenario', async () => {
                mockPublicUser.findOne.mockResolvedValue({ id: 1 });
                mockPublicMember.findOne.mockResolvedValue(null);
                const inputData = { user: { email: '<EMAIL>' }, body: { ParentCompanyId: 1 } };

                const result = await inspectionReportService.findEnterprise(inputData);
                expect(result).toEqual(mockEnterprise);
            });

            it('should handle database error in findEnterprise', async () => {
                const error = new Error('Database error');
                mockPublicUser.findOne.mockRejectedValue(error);
                const inputData = { user: { email: '<EMAIL>' }, body: { ParentCompanyId: 1 } };

                await expect(inspectionReportService.findEnterprise(inputData)).rejects.toThrow(
                    'Database error',
                );
            });

            it('should handle missing ParentCompanyId in params', async () => {
                const inputData = {
                    user: { email: '<EMAIL>' },
                    body: {},
                    params: { ParentCompanyId: 2 },
                };
                mockPublicUser.findOne.mockResolvedValue({ id: 1 });
                mockPublicMember.findOne.mockResolvedValue(null);

                const result = await inspectionReportService.findEnterprise(inputData);
                expect(Enterprise.findOne).toHaveBeenCalledWith({
                    where: { ParentCompanyId: 2, status: 'completed' },
                });
            });
        });

        describe('getDynamicModel - Additional Cases', () => {
            it('should handle missing user email', async () => {
                const inputData = {
                    ...mockInputData,
                    user: { domainName: 'testdomain' },
                };

                const result = await inspectionReportService.getDynamicModel(inputData);
                expect(result).toBe(inputData.body.ProjectId);
            });

            it('should handle enterprise not found scenario', async () => {
                const inputData = {
                    ...mockInputData,
                    user: { email: '<EMAIL>' },
                };

                inspectionReportService.findEnterprise = jest.fn().mockResolvedValue(null);

                const result = await inspectionReportService.getDynamicModel(inputData);
                expect(helper.getDynamicModel).toHaveBeenCalledWith('');
            });

            it('should handle error in getDynamicModel', async () => {
                const error = new Error('Model error');
                helper.getDynamicModel.mockRejectedValue(error);

                await expect(inspectionReportService.getDynamicModel(mockInputData)).rejects.toThrow(
                    'Model error',
                );
            });
        });

        describe('buildConditions - Additional Cases', () => {
            it('should handle numeric search in default conditions', async () => {
                const incomeData = { search: '123' };
                const result = await inspectionReportService.buildConditions(
                    incomeData,
                    { ProjectId: 1 },
                    'default',
                    mockInputData,
                );

                expect(result[Op.and]).toBeDefined();
                expect(result[Op.and][0].InspectionId).toBe(123);
            });

            it('should handle empty startdate in date conditions', async () => {
                const incomeData = { startdate: '' };
                const result = await inspectionReportService.buildConditions(
                    incomeData,
                    { ProjectId: 1 },
                    'date',
                    mockInputData,
                );

                expect(result.inspectionStart[Op.gte]).toBeDefined();
            });

            it('should handle enddateFilter in date conditions', async () => {
                const incomeData = { enddateFilter: '2024-01-31' };
                inspectionReportService.getStartAndEndDate = jest
                    .fn()
                    .mockReturnValue([
                        moment('2024-01-31').startOf('day'),
                        moment('2024-01-31').endOf('day'),
                    ]);

                const result = await inspectionReportService.buildConditions(
                    incomeData,
                    { ProjectId: 1 },
                    'date',
                    mockInputData,
                );

                expect(result.inspectionStart[Op.between]).toBeDefined();
            });

            it('should handle empty arrays in filter conditions', async () => {
                const incomeData = {
                    equipmentFilter: [],
                    gateFilter: [],
                    statusFilter: [],
                };

                const result = await inspectionReportService.buildConditions(
                    incomeData,
                    { ProjectId: 1 },
                    'filter',
                    mockInputData,
                );

                expect(result['$equipmentDetails.Equipment.id$']).toBeUndefined();
                expect(result['$gateDetails.Gate.id$']).toBeUndefined();
                expect(result.statusFilter).toBeUndefined();
            });

            it('should return empty conditions for unknown type', async () => {
                const result = await inspectionReportService.buildConditions(
                    {},
                    { ProjectId: 1 },
                    'unknown',
                    mockInputData,
                );

                expect(Object.keys(result)).toHaveLength(0);
            });
        });

        describe('processInspectionList - Additional Cases', () => {
            it('should handle export type in request', async () => {
                const mockInspectionList = { rows: [{ id: 1 }] };
                const mockReq = {
                    body: { exportType: 'PDF', sort: 'DESC', sortByField: 'inspectionStart' },
                    user: mockUser,
                };
                const mockDone = jest.fn();

                // Mock getSearchData to return successful response
                inspectionReportService.getSearchData = jest.fn().mockResolvedValue({
                    rows: [{ id: 1 }],
                    count: 1,
                });

                await inspectionReportService.processInspectionList(
                    mockInspectionList,
                    { pageSize: 10 },
                    mockReq,
                    mockDone,
                );

                expect(mockDone).toHaveBeenCalledWith(
                    expect.objectContaining({
                        rows: expect.any(Array),
                        count: 1,
                    }),
                    false,
                );
            });

            it('should handle pagination when no export type', async () => {
                const mockInspectionList = {
                    rows: Array(20)
                        .fill()
                        .map((_, i) => ({ id: i + 1 })),
                };
                const mockReq = {
                    body: { sort: 'DESC', sortByField: 'inspectionStart' },
                    user: mockUser,
                };
                const mockDone = jest.fn();

                inspectionReportService.getSearchData = jest.fn().mockResolvedValue({
                    rows: mockInspectionList.rows,
                    count: 20,
                });

                await inspectionReportService.processInspectionList(
                    mockInspectionList,
                    { pageSize: 10 },
                    mockReq,
                    mockDone,
                );

                const result = mockDone.mock.calls[0][0];
                expect(result.rows).toHaveLength(10); // Should be paginated
                expect(result.count).toBe(20);
            });

            it('should handle getSearchData error', async () => {
                const mockInspectionList = { rows: [{ id: 1 }] };
                const mockReq = {
                    body: { sort: 'DESC', sortByField: 'inspectionStart' },
                    user: mockUser,
                };
                const mockDone = jest.fn();

                inspectionReportService.getSearchData = jest.fn().mockResolvedValue({
                    error: 'Search failed',
                });

                await inspectionReportService.processInspectionList(
                    mockInspectionList,
                    { pageSize: 10 },
                    mockReq,
                    mockDone,
                );

                expect(mockDone).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
            });

            it('should handle exception in processInspectionList', async () => {
                const mockInspectionList = { rows: [{ id: 1 }] };
                const mockReq = {
                    body: { sort: 'DESC', sortByField: 'inspectionStart' },
                    user: mockUser,
                };
                const mockDone = jest.fn();

                inspectionReportService.getSearchData = jest
                    .fn()
                    .mockRejectedValue(new Error('Database error'));

                await inspectionReportService.processInspectionList(
                    mockInspectionList,
                    { pageSize: 10 },
                    mockReq,
                    mockDone,
                );

                expect(mockDone).toHaveBeenCalledWith(null, {
                    message: 'An error occurred during processing',
                });
            });
        });

        describe('shouldUseCalendar - Additional Cases', () => {
            it('should return true when memberFilter is present', () => {
                const result = inspectionReportService.shouldUseCalendar(
                    {},
                    { void: '0' },
                    { memberFilter: [1] },
                );
                expect(result).toBe(true);
            });

            it('should return true when assignedFilter is present', () => {
                const result = inspectionReportService.shouldUseCalendar(
                    {},
                    { void: '0' },
                    { assignedFilter: [1] },
                );
                expect(result).toBe(true);
            });

            it('should return true when void is 0 and upcoming is false', () => {
                const result = inspectionReportService.shouldUseCalendar(
                    {},
                    { void: '0' },
                    { upcoming: false },
                );
                expect(result).toBe(true);
            });

            it('should return false when void is 1', () => {
                const result = inspectionReportService.shouldUseCalendar({}, { void: '1' }, {});
                expect(result).toBe(false);
            });
        });

        describe('getDateRange - Additional Cases', () => {
            it('should handle missing timezone offset', () => {
                const [start, end] = inspectionReportService.getDateRange('2024-01-01', '2024-01-31', {
                    headers: {},
                });

                expect(start.format('YYYY-MM-DD')).toBe('2024-01-01');
                expect(end.format('YYYY-MM-DD')).toBe('2024-01-31');
            });

            it('should handle different date formats', () => {
                const [start, end] = inspectionReportService.getDateRange('2024-12-01', '2024-12-31', {
                    headers: { timezoneoffset: '0' },
                });

                expect(start.format('YYYY-MM-DD')).toBe('2024-12-01');
                expect(end.format('YYYY-MM-DD')).toBe('2024-12-31');
            });
        });

        describe('buildBaseCondition - Additional Cases', () => {
            it('should handle string ProjectId', () => {
                const result = inspectionReportService.buildBaseCondition(
                    { ProjectId: '123', void: '0' },
                    [1, 2],
                );

                expect(result.ProjectId).toBe(123);
                expect(result.isQueued).toBe(false);
            });

            it('should handle empty void inspections array', () => {
                const result = inspectionReportService.buildBaseCondition({ ProjectId: 1, void: '0' }, []);

                expect(result['$InspectionRequest.id$'][Op.and][Op.notIn]).toEqual([]);
            });
        });
    });

    // Tests for methods that need to be covered for 100% coverage
    describe('Additional Methods for 100% Coverage', () => {
        describe('getInspectionData', () => {
            it('should process inspection data and use calendar when conditions met', async () => {
                const mockResponse = { rows: [], count: 0 };
                inspectionReportService.processCalendarData.mockResolvedValue(mockResponse);

                const inputData = {
                    ...mockInputData,
                    body: { ...mockInputData.body, companyFilter: [1] }
                };

                const result = await inspectionReportService.getInspectionData(inputData);
                expect(inspectionReportService.processCalendarData).toHaveBeenCalled();
                expect(result).toEqual(mockResponse);
            });

            it('should process inspection data and use regular processing when calendar not needed', async () => {
                const mockResponse = { rows: [], count: 0 };
                inspectionReportService.processRegularData.mockResolvedValue(mockResponse);

                const inputData = {
                    ...mockInputData,
                    body: { ...mockInputData.body, upcoming: true },
                    params: { ...mockInputData.params, void: '1' }
                };

                const result = await inspectionReportService.getInspectionData(inputData);
                expect(inspectionReportService.processRegularData).toHaveBeenCalled();
                expect(result).toEqual(mockResponse);
            });
        });

        describe('listInspectionRequest', () => {
            it('should handle successful inspection request listing', async () => {
                const mockResponse = { rows: [], count: 0 };
                inspectionReportService.getInspectionData = jest.fn().mockResolvedValue(mockResponse);
                const mockDone = jest.fn();

                await inspectionReportService.listInspectionRequest(mockInputData, mockDone);

                expect(inspectionReportService.handleResponse).toHaveBeenCalledWith(mockResponse, mockInputData, mockDone);
            });

            it('should handle error response from getInspectionData', async () => {
                const mockResponse = { error: 'Something went wrong' };
                inspectionReportService.getInspectionData = jest.fn().mockResolvedValue(mockResponse);
                const mockDone = jest.fn();

                await inspectionReportService.listInspectionRequest(mockInputData, mockDone);

                expect(mockDone).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
            });

            it('should handle exceptions in listInspectionRequest', async () => {
                const error = new Error('Database error');
                inspectionReportService.getInspectionData = jest.fn().mockRejectedValue(error);
                const mockDone = jest.fn();

                await inspectionReportService.listInspectionRequest(mockInputData, mockDone);

                expect(mockDone).toHaveBeenCalledWith(null, error);
            });
        });
    });
});
