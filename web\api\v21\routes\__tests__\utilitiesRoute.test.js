const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  UtilitiesController: {
    getUtilities: jest.fn(),
    updateUtilities: jest.fn(),
    calculateDistance: jest.fn(),
    geocodeAddress: jest.fn(),
  },
}));

jest.mock('../../controllers/UtilitiesController', () => ({
  addUtilities: jest.fn(),
  listUtilities: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
  isProjectAdminOnly: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  utilitiesValidation: {
    getUtilities: jest.fn(),
    updateUtilities: jest.fn(),
    calculateDistance: jest.fn(),
    geocodeAddress: jest.fn(),
  },
}));

describe('utilitiesRoute', () => {
  let router;
  let utilitiesRoute;
  let UtilitiesController;
  let passportConfig;
  let utilitiesValidation;
  let validate;
  let utilitiesController;
  let checkAdmin;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    utilitiesRoute = require('../utilitiesRoute');
    const controllers = require('../../controllers');
    UtilitiesController = controllers.UtilitiesController;
    utilitiesController = require('../../controllers/UtilitiesController');
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    utilitiesValidation = validations.utilitiesValidation;
    validate = require('express-validation').validate;
    checkAdmin = require('../../middlewares/checkAdmin');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = utilitiesRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify POST route
      expect(router.post).toHaveBeenCalledWith(
        '/add_Utilities',
        passportConfig.isAuthenticated,
        utilitiesController.addUtilities,
      );

      // Verify GET route
      expect(router.get).toHaveBeenCalledWith(
        '/Utilities_list/:pageSize/:pageNo',
        passportConfig.isAuthenticated,
        utilitiesController.listUtilities,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = utilitiesRoute.router;
      const result2 = utilitiesRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      utilitiesRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];

      allCalls.forEach((call) => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof utilitiesRoute).toBe('object');
      expect(utilitiesRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(utilitiesRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(utilitiesRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
