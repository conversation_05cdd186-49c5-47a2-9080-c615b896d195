const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  return multer;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  DeliveryController: {
    newRequest: jest.fn(),
    editRequest: jest.fn(),
    updateNDRStatus: jest.fn(),
    listNDR: jest.fn(),
    getNDRData: jest.fn(),
    getMemberData: jest.fn(),
    sampleBulkDeliveryRequestTemplate: jest.fn(),
    bulkUploadDeliveryRequest: jest.fn(),
    deleteQueuedNdr: jest.fn(),
    editMultipleDeliveryRequest: jest.fn(),
    getLastDeliveryRequestId: jest.fn(),
    decryption: jest.fn(),
    setReadAllNotification: jest.fn(),
    markAllNotification: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  deliveryValidation: {
    newRequest: jest.fn(),
    editRequest: jest.fn(),
    editQueuedRequest: jest.fn(),
    updateQueuedRequest: jest.fn(),
    updateNDRStatus: jest.fn(),
    listNDR: jest.fn(),
    getNDRData: jest.fn(),
    getMemberData: jest.fn(),
    uploadBulkNDRTemplate: jest.fn(),
    bulkUploadDeliveryRequest: jest.fn(),
  },
}));

describe('deliveryRoute', () => {
  let router;
  let deliveryRoute;
  let DeliveryController;
  let passportConfig;
  let deliveryValidation;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    deliveryRoute = require('../deliveryRoute');
    const controllers = require('../../controllers');
    DeliveryController = controllers.DeliveryController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    deliveryValidation = validations.deliveryValidation;
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = deliveryRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer).toHaveBeenCalledWith({ dest: 'uploads/' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(12);
      expect(router.get).toHaveBeenCalledTimes(5);

      // Verify POST routes with validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/new_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.newRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/edit_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.editRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/edit_queued_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.editRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/update_to_current_NDR',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.editRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/update_status',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.updateNDRStatus,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/list_NDR/:ProjectId/:pageSize/:pageNo/:void',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.listNDR,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        7,
        '/list_Queued_NDR/:ProjectId/:pageSize/:pageNo/:void',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.listNDR,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        8,
        '/sample_delivery_request_template/?:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.sampleBulkDeliveryRequestTemplate,
      );

      // POST route with multer
      expect(router.post).toHaveBeenNthCalledWith(
        9,
        '/bulk_upload_delivery_request/?:ProjectId/?:ParentCompanyId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.bulkUploadDeliveryRequest,
      );

      // POST routes without validation
      expect(router.post).toHaveBeenNthCalledWith(
        10,
        '/delete_queued_Ndr',
        passportConfig.isAuthenticated,
        DeliveryController.deleteQueuedNdr,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        11,
        '/edit_multiple_request',
        passportConfig.isAuthenticated,
        DeliveryController.editMultipleDeliveryRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        12,
        '/decrypt',
        passportConfig.isAuthenticated,
        DeliveryController.decryption,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_single_NDR/:DeliveryRequestId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.getNDRData,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_user_role/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        DeliveryController.getMemberData,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/get_last_delivery_request_id/:ProjectId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        DeliveryController.getLastDeliveryRequestId,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        4,
        '/readall_notification',
        passportConfig.isAuthenticated,
        DeliveryController.setReadAllNotification,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        5,
        '/markall_notification/:ProjectId',
        passportConfig.isAuthenticated,
        DeliveryController.markAllNotification,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(10);
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.newRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.editRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.editQueuedRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.updateQueuedRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.updateNDRStatus,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.listNDR,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.getNDRData,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.getMemberData,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.uploadBulkNDRTemplate,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        deliveryValidation.bulkUploadDeliveryRequest,
        { keyByField: true },
        { abortEarly: false },
      );

      // Verify multer single method is called
      const uploadInstance = multer.mock.results[0].value;
      expect(uploadInstance.single).toHaveBeenCalledWith('delivery_request');
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = deliveryRoute.router;
      const result2 = deliveryRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      deliveryRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure different middleware combinations correctly', () => {
      deliveryRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Routes with validation only (4 parameters)
      const routesWithValidation = [
        postCalls[0], postCalls[1], postCalls[2], postCalls[3],
        postCalls[4], postCalls[5], postCalls[6], postCalls[7],
        getCalls[0], getCalls[1]
      ];

      routesWithValidation.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // Route with multer + validation (5 parameters)
      expect(postCalls[8]).toHaveLength(5); // path + multer + validation + auth + controller
      expect(postCalls[8][1]).toBe('mocked-multer-middleware');
      expect(postCalls[8][2]).toBe('mocked-validate-middleware');

      // Routes with auth only (3 parameters)
      const routesWithAuthOnly = [
        postCalls[9], postCalls[10], postCalls[11],
        getCalls[2], getCalls[3], getCalls[4]
      ];

      routesWithAuthOnly.forEach(call => {
        expect(call).toHaveLength(3); // path + auth + controller
        expect(call[1]).toBe(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof deliveryRoute).toBe('object');
      expect(deliveryRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(deliveryRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(deliveryRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
