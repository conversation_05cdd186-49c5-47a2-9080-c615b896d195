const restrictMailService = require('../restrictMailService');
const { RestrictEmail, Sequelize } = require('../../models');

// Mock the models
jest.mock('../../models', () => ({
    RestrictEmail: {
        getBy: jest.fn(),
        createInstance: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
    },
    Sequelize: {
        and: jest.fn(),
        Op: {
            notIn: 'notIn',
            in: 'in',
        },
    },
}));

describe('restrictMailService', () => {
    let mockDone;

    beforeEach(() => {
        mockDone = jest.fn();
        jest.clearAllMocks();
    });

    describe('addRestrictMail', () => {
        const validInput = {
            body: {
                domainName: 'test.com',
                isActive: true,
            },
        };

        // Positive test cases
        it('should successfully add a new restricted mail domain', async () => {
            const mockNewData = { id: 1, domainName: 'test.com', isActive: true };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: 'test.com' });
            expect(RestrictEmail.createInstance).toHaveBeenCalledWith(validInput.body);
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        it('should successfully add domain with isActive false', async () => {
            const inputWithInactive = {
                body: {
                    domainName: 'inactive.com',
                    isActive: false,
                },
            };
            const mockNewData = { id: 2, domainName: 'inactive.com', isActive: false };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(inputWithInactive, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: 'inactive.com' });
            expect(RestrictEmail.createInstance).toHaveBeenCalledWith(inputWithInactive.body);
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        it('should handle domain names with special characters', async () => {
            const inputWithSpecialChars = {
                body: {
                    domainName: 'test-domain.co.uk',
                    isActive: true,
                },
            };
            const mockNewData = { id: 3, domainName: 'test-domain.co.uk', isActive: true };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(inputWithSpecialChars, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: 'test-domain.co.uk' });
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        // Negative test cases
        it('should return error when domain already exists', async () => {
            const existingData = { id: 1, domainName: 'test.com' };
            RestrictEmail.getBy.mockResolvedValue(existingData);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: 'test.com' });
            expect(RestrictEmail.createInstance).not.toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(null, 'Domain Already exist.');
        });

        it('should handle creation failure when createInstance returns null', async () => {
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(null);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle creation failure when createInstance returns undefined', async () => {
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(undefined);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle creation failure when createInstance returns false', async () => {
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(false);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle unexpected errors from getBy', async () => {
            const error = new Error('Database error');
            RestrictEmail.getBy.mockRejectedValue(error);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle unexpected errors from createInstance', async () => {
            const error = new Error('Creation failed');
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockRejectedValue(error);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle missing body property', async () => {
            const invalidInput = {};

            await restrictMailService.addRestrictMail(invalidInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle empty domain name', async () => {
            const inputWithEmptyDomain = {
                body: {
                    domainName: '',
                    isActive: true,
                },
            };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(null);

            await restrictMailService.addRestrictMail(inputWithEmptyDomain, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: '' });
            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle null domain name', async () => {
            const inputWithNullDomain = {
                body: {
                    domainName: null,
                    isActive: true,
                },
            };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(null);

            await restrictMailService.addRestrictMail(inputWithNullDomain, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: null });
            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle uppercase domain names', async () => {
            const inputWithUppercase = {
                body: {
                    domainName: 'EXAMPLE.COM',
                    isActive: true,
                },
            };
            const mockNewData = { id: 4, domainName: 'EXAMPLE.COM', isActive: true };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(inputWithUppercase, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: 'EXAMPLE.COM' });
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        it('should handle missing domainName property', async () => {
            const inputWithoutDomain = {
                body: {
                    isActive: true,
                },
            };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(null);

            await restrictMailService.addRestrictMail(inputWithoutDomain, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: undefined });
            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle missing isActive property', async () => {
            const inputWithoutIsActive = {
                body: {
                    domainName: 'test.com',
                },
            };
            const mockNewData = { id: 5, domainName: 'test.com' };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(inputWithoutIsActive, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: 'test.com' });
            expect(RestrictEmail.createInstance).toHaveBeenCalledWith(inputWithoutIsActive.body);
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        it('should handle numeric domain names', async () => {
            const inputWithNumeric = {
                body: {
                    domainName: '123.456',
                    isActive: true,
                },
            };
            const mockNewData = { id: 6, domainName: '123.456', isActive: true };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(inputWithNumeric, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: '123.456' });
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        it('should handle very long domain names', async () => {
            const longDomain = `${'a'.repeat(100)}.com`;
            const inputWithLongDomain = {
                body: {
                    domainName: longDomain,
                    isActive: true,
                },
            };
            const mockNewData = { id: 7, domainName: longDomain, isActive: true };
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(mockNewData);

            await restrictMailService.addRestrictMail(inputWithLongDomain, mockDone);

            expect(RestrictEmail.getBy).toHaveBeenCalledWith({ domainName: longDomain });
            expect(mockDone).toHaveBeenCalledWith(mockNewData, false);
        });

        it('should handle createInstance returning 0', async () => {
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue(0);

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle createInstance returning empty string', async () => {
            RestrictEmail.getBy.mockResolvedValue(null);
            RestrictEmail.createInstance.mockResolvedValue('');

            await restrictMailService.addRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });
    });

    describe('updateRestrictMail', () => {
        const validInput = {
            body: {
                id: 1,
                domainName: 'updated.com',
                isActive: false,
            },
        };

        it('should successfully update a restricted mail domain', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue([1]);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(RestrictEmail.findOne).toHaveBeenCalled();
            expect(RestrictEmail.update).toHaveBeenCalledWith(
                { domainName: 'updated.com', isActive: false },
                { where: { id: { [Sequelize.Op.in]: [1] } } },
            );
            expect(mockDone).toHaveBeenCalledWith([1], false);
        });

        it('should return error when updated domain already exists', async () => {
            const existingData = { id: 2, domainName: 'updated.com' };
            RestrictEmail.findOne.mockResolvedValue(existingData);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(RestrictEmail.findOne).toHaveBeenCalled();
            expect(RestrictEmail.update).not.toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(null, 'Domain Already exist.');
        });

        it('should handle update failure', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue([0]);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith([0], false);
        });

        it('should handle unexpected errors during update', async () => {
            const error = new Error('Database error');
            RestrictEmail.findOne.mockRejectedValue(error);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        // Additional comprehensive test cases for updateRestrictMail
        it('should handle update with different domain name', async () => {
            const inputWithDifferentDomain = {
                body: {
                    id: 2,
                    domainName: 'newdomain.org',
                    isActive: true,
                },
            };
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue([1]);

            await restrictMailService.updateRestrictMail(inputWithDifferentDomain, mockDone);

            expect(RestrictEmail.findOne).toHaveBeenCalledWith({
                where: Sequelize.and({
                    domainName: 'newdomain.org',
                    id: { [Sequelize.Op.notIn]: [2] },
                }),
            });
            expect(RestrictEmail.update).toHaveBeenCalledWith(
                { domainName: 'newdomain.org', isActive: true },
                { where: { id: { [Sequelize.Op.in]: [2] } } },
            );
            expect(mockDone).toHaveBeenCalledWith([1], false);
        });

        it('should handle update with multiple affected rows', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue([3]);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith([3], false);
        });

        it('should handle update returning null', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue(null);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle update returning undefined', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue(undefined);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle update returning false', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue(false);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle update returning empty array', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue([]);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith([], false);
        });

        it('should handle update error from update method', async () => {
            const error = new Error('Update failed');
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockRejectedValue(error);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        it('should handle missing body in update', async () => {
            const invalidInput = {};

            await restrictMailService.updateRestrictMail(invalidInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing id in update', async () => {
            const inputWithoutId = {
                body: {
                    domainName: 'test.com',
                    isActive: true,
                },
            };

            await restrictMailService.updateRestrictMail(inputWithoutId, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should verify Sequelize.and is called correctly', async () => {
            RestrictEmail.findOne.mockResolvedValue(null);
            RestrictEmail.update.mockResolvedValue([1]);

            await restrictMailService.updateRestrictMail(validInput, mockDone);

            expect(Sequelize.and).toHaveBeenCalledWith({
                domainName: 'updated.com',
                id: { [Sequelize.Op.notIn]: [1] },
            });
        });
    });

    describe('deleteRestrictMail', () => {
        const validInput = {
            body: {
                id: 1,
            },
        };

        it('should successfully delete a restricted mail domain', async () => {
            RestrictEmail.destroy.mockResolvedValue(1);

            await restrictMailService.deleteRestrictMail(validInput, mockDone);

            expect(RestrictEmail.destroy).toHaveBeenCalledWith({
                where: { id: { [Sequelize.Op.in]: [1] } },
            });
            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should handle deletion failure', async () => {
            RestrictEmail.destroy.mockResolvedValue(0);

            await restrictMailService.deleteRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle unexpected errors during deletion', async () => {
            const error = new Error('Database error');
            RestrictEmail.destroy.mockRejectedValue(error);

            await restrictMailService.deleteRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, error);
        });

        // Additional comprehensive test cases for deleteRestrictMail
        it('should handle deletion with multiple IDs', async () => {
            const inputWithMultipleIds = {
                body: {
                    id: [1, 2, 3],
                },
            };
            RestrictEmail.destroy.mockResolvedValue(3);

            await restrictMailService.deleteRestrictMail(inputWithMultipleIds, mockDone);

            expect(RestrictEmail.destroy).toHaveBeenCalledWith({
                where: { id: { [Sequelize.Op.in]: [[1, 2, 3]] } },
            });
            expect(mockDone).toHaveBeenCalledWith(3, false);
        });

        it('should handle deletion returning null', async () => {
            RestrictEmail.destroy.mockResolvedValue(null);

            await restrictMailService.deleteRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle deletion returning undefined', async () => {
            RestrictEmail.destroy.mockResolvedValue(undefined);

            await restrictMailService.deleteRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle deletion returning false', async () => {
            RestrictEmail.destroy.mockResolvedValue(false);

            await restrictMailService.deleteRestrictMail(validInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle missing body in deletion', async () => {
            const invalidInput = {};

            await restrictMailService.deleteRestrictMail(invalidInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing id in deletion', async () => {
            const inputWithoutId = {
                body: {},
            };

            await restrictMailService.deleteRestrictMail(inputWithoutId, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle empty array id in deletion', async () => {
            const inputWithEmptyArray = {
                body: {
                    id: [],
                },
            };
            RestrictEmail.destroy.mockResolvedValue(0);

            await restrictMailService.deleteRestrictMail(inputWithEmptyArray, mockDone);

            expect(RestrictEmail.destroy).toHaveBeenCalledWith({
                where: { id: { [Sequelize.Op.in]: [[]] } },
            });
            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle null id in deletion', async () => {
            const inputWithNullId = {
                body: {
                    id: null,
                },
            };

            await restrictMailService.deleteRestrictMail(inputWithNullId, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, 'Something went wrong.');
        });

        it('should handle string id in deletion', async () => {
            const inputWithStringId = {
                body: {
                    id: '1',
                },
            };
            RestrictEmail.destroy.mockResolvedValue(1);

            await restrictMailService.deleteRestrictMail(inputWithStringId, mockDone);

            expect(RestrictEmail.destroy).toHaveBeenCalledWith({
                where: { id: { [Sequelize.Op.in]: ['1'] } },
            });
            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should handle very large number of IDs', async () => {
            const largeIdArray = Array.from({ length: 1000 }, (_, i) => i + 1);
            const inputWithLargeArray = {
                body: {
                    id: largeIdArray,
                },
            };
            RestrictEmail.destroy.mockResolvedValue(1000);

            await restrictMailService.deleteRestrictMail(inputWithLargeArray, mockDone);

            expect(RestrictEmail.destroy).toHaveBeenCalledWith({
                where: { id: { [Sequelize.Op.in]: [largeIdArray] } },
            });
            expect(mockDone).toHaveBeenCalledWith(1000, false);
        });
    });
});
