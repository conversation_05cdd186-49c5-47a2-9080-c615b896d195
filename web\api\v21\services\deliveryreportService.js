const moment = require('moment');
const _ = require('lodash');
const httpStatus = require('http-status');
const { Sequelize, sequelize, Enterprise } = require('../models');
const { Delivery<PERSON>erson, VoidList, SchedulerReport, CraneRequest, ConcreteRequest, InspectionRequest } = require('../models');
let { DeliveryRequest, Member, DeliverGate, DeliverCompany, User } = require('../models');
const exportService = require('./exportService');
const pdfDeliveryReportService = require('./pdfDeliveryReportService');
const csvDeliveryReportService = require('./csvDeliveryReportService');
const calendarSettingsService = require('./calendarSettingsService');
const ApiError = require('../helpers/apiError');
const helper = require('../helpers/domainHelper');
const {
  queryBuilderExternal,
  replacementsBuilderExternal,
  defaultTimeSlots,
} = require('../helpers/queryBuilderExternal');
const { Op } = Sequelize;

let publicUser;
let publicMember;

const deliveryReportService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
    domainName = await this.getDomainName(domainName, ParentCompanyId, inputData.user.email);
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliverGate = modelObj.DeliverGate;
    DeliverCompany = modelObj.DeliverCompany;
    User = modelObj.User;
    return null;
  },

  async getDomainName(domainName, ParentCompanyId, email) {
    if (domainName) {
      const domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) domainName = '';
    } else if (ParentCompanyId) {
      domainName = await this.findDomainNameByCompanyIdOrEmail(ParentCompanyId, email);
    }
    return domainName;
  },

  async findDomainNameByCompanyIdOrEmail(ParentCompanyId, email) {
    const userData = email ? await publicUser.findOne({ where: { email } }) : null;
    const memberData = userData ? await this.getPublicMemberData(userData.id) : null;
    return this.findDomainNameByMemberData(memberData, ParentCompanyId);
  },

  async getPublicMemberData(userId) {
    return publicMember.findOne({
      where: { UserId: userId, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });
  },

  async findDomainNameByMemberData(memberData, ParentCompanyId) {
    const enterpriseValue = memberData?.isAccount
      ? await this.getEnterpriseById(memberData.EnterpriseId)
      : await this.getEnterpriseDomainName(ParentCompanyId);
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async getEnterpriseById(EnterpriseId) {
    return Enterprise.findOne({
      where: { id: EnterpriseId, status: 'completed' },
    });
  },

  async getEnterpriseDomainName(ParentCompanyId) {
    const enterpriseValue = await this.getEnterpriseByParentCompanyId(ParentCompanyId);
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async getEnterpriseByParentCompanyId(ParentCompanyId) {
    return Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
  },

  async listDeliveryRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      if (this.isInvalidVoidParam(inputData.params.void)) {
        return done({ message: 'Please enter void as 1 or 0' }, null);
      }

      const memberDetails = await this.getMemberDetails(inputData.user.id, inputData.params.ProjectId);
      if (!memberDetails) {
        return done({ message: 'Project Id/Member does not exist' }, null);
      }

      const searchCondition = this.buildSearchCondition(inputData.body);
      const deliveryList = await this.fetchDeliveryRequests(inputData, memberDetails, searchCondition);

      this.processDeliveryList(deliveryList, inputData, done);
    } catch (e) {
      done({ message: 'Something went wrong' }, e);
    }
  },

  isInvalidVoidParam(voidParam) {
    return voidParam !== '0' && voidParam !== '1';
  },

  async getMemberDetails(userId, ProjectId) {
    return Member.findOne({
      where: Sequelize.and({
        UserId: userId,
        ProjectId,
        isDeleted: false,
        isActive: true,
      }),
    });
  },

  buildSearchCondition(incomeData) {
    if (!incomeData.search) return {};
    const search = { [Op.iLike]: `%${incomeData.search}%` };
    return {
      [Op.or]: [
        { '$approverDetails.User.firstName$': search },
        { '$equipmentDetails.Equipment.equipmentName$': search },
        { description: search },
        { cranePickUpLocation: search },
        { craneDropOffLocation: search },
      ],
    };
  },

  async fetchDeliveryRequests(inputData, memberDetails, searchCondition) {
    const { pageNo, pageSize, void: voidParam, ProjectId } = inputData.params;
    const offset = this.calculateOffset(pageNo, pageSize);
    const condition = this.buildCondition(inputData.body, ProjectId, voidParam);

    // Build filter conditions for the model
    const filterConditions = this.buildFilterConditions(inputData.body);

    return DeliveryRequest.getAll({
      attr: condition,
      roleId: memberDetails.RoleId,
      memberId: memberDetails.id,
      limit: +pageSize,
      offset,
      searchCondition: { ...searchCondition, ...filterConditions },
    });
  },

  calculateOffset(pageNo, pageSize) {
    return (+pageNo - 1) * +pageSize;
  },

  processDeliveryList(deliveryList, inputData, done) {
    // The model returns findAndCountAll result with {count, rows} format
    done({ count: deliveryList.count, rows: deliveryList.rows }, null);
  },

  buildCondition(incomeData, ProjectId, voidParam) {
    const condition = { ProjectId: +ProjectId, isQueued: false };
    const voidDelivery = voidParam === '1' ? [] : undefined;

    // Only include basic conditions, let the model handle filter conditions
    return { ...condition, ...voidDelivery };
  },

  buildFilterConditions(incomeData) {
    const filterConditions = {};

    // Build actual database conditions based on filter properties
    if (incomeData.descriptionFilter) {
      filterConditions.description = { [Op.iLike]: `%${incomeData.descriptionFilter}%` };
    }

    if (incomeData.companyFilter && typeof incomeData.companyFilter === 'string' && incomeData.companyFilter !== '') {
      filterConditions['$companyDetails.Company.companyName$'] = { [Op.iLike]: `%${incomeData.companyFilter}%` };
    }

    if (incomeData.memberFilter > 0) {
      filterConditions['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }

    if (incomeData.equipmentFilter) {
      filterConditions['$equipmentDetails.Equipment.equipmentName$'] = { [Op.iLike]: `%${incomeData.equipmentFilter}%` };
    }

    if (incomeData.statusFilter) {
      filterConditions.status = incomeData.statusFilter;
    }

    if (incomeData.locationFilter) {
      filterConditions['$location.id$'] = { [Op.in]: Array.isArray(incomeData.locationFilter) ? incomeData.locationFilter : [incomeData.locationFilter] };
    }

    if (incomeData.gateFilter > 0) {
      filterConditions['$gateDetails.Gate.id$'] = +incomeData.gateFilter;
    }

    if (incomeData.pickFrom) {
      filterConditions.cranePickUpLocation = { [Op.iLike]: `%${incomeData.pickFrom}%` };
    }

    if (incomeData.pickTo) {
      filterConditions.craneDropOffLocation = { [Op.iLike]: `%${incomeData.pickTo}%` };
    }

    // Date filtering would need to be handled with proper timezone logic
    // For now, just include basic status and description filters

    return filterConditions;
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  emptyHeatMapResult() {
    const finalResult = {};
    finalResult.count = 0;
    finalResult.result = {};
    return finalResult;
  },

  async heatMapQueryOut(inputData, incomeData, params) {
    const result = {};
    if (incomeData.templateType.length === 1) {
      if (incomeData.templateType.find((ele) => ele.id === 1) && incomeData.gateFilter != 0) {
        return this.emptyHeatMapResult();
      }
      if (
        incomeData.templateType.find((ele) => ele.id === 2) &&
        (incomeData.gateFilter != 0 ||
          incomeData.equipmentFilter != 0 ||
          incomeData.defineFilter != 0)
      ) {
        return this.emptyHeatMapResult();
      }
    } else if (incomeData.templateType.length === 2) {
      const IDs = incomeData.templateType.map((d) => parseInt(d.id));
      const isCrane_Concrete_Combained = IDs.includes(1) && IDs.includes(2);
      if (isCrane_Concrete_Combained && incomeData.gateFilter != 0) {
        return this.emptyHeatMapResult();
      }
    }
    const queryBuilderOut = (await queryBuilderExternal(inputData, incomeData, params)).toString();
    const replacementsObj = await replacementsBuilderExternal(inputData, incomeData, params);
    const deliveryList = await sequelize.query(queryBuilderOut, {
      replacements: { ...replacementsObj },
      type: Sequelize.QueryTypes.SELECT,
    });
    // sorting
    let deliveryListSort = _.sortBy(deliveryList, (o) => moment(o.Date, 'MMMM DD,YYYY').toDate());
    if (params.sortOrder == 'desc') {
      deliveryListSort = _.sortBy(deliveryList, (o) =>
        moment(o.Date, 'MMMM DD,YYYY').toDate(),
      ).reverse();
    }

    _.forEach(deliveryListSort, (d) => {
      if (!result.hasOwnProperty(d.Date)) {
        result[d.Date] = {
          timeslots: defaultTimeSlots(),
          totalCount: 0 + parseInt(d.Count),
        };
        result[d.Date].timeslots[d.Time] = parseInt(d.Count);
      } else {
        result[d.Date].timeslots[d.Time] = parseInt(d.Count);
        result[d.Date].totalCount = result[d.Date].totalCount + parseInt(d.Count);
      }
    });

    // pagination
    const finalData = _.map(result, function (value, key) {
      return { date: key, data: value };
    });

    let pageData;

    if (incomeData.hasOwnProperty('exportType')) {
      pageData = finalData;
    } else {
      const pagesize = parseInt(params.pageSize);
      const pagenumber = parseInt(params.pageNo.trim(), 10);
      const startIndex = (pagenumber - 1) * pagesize;
      const endIndex = pagenumber * pagesize;
      pageData = _.slice(finalData, startIndex, endIndex);
    }

    const convertedObject = _.reduce(
      pageData,
      function (result, value) {
        result[value.date] = value.data;
        return result;
      },
      {},
    );

    const finalResult = {};
    finalResult.count = finalData.length;
    finalResult.result = convertedObject;
    return finalResult;
  },

  async heatMapListDeliveryRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const result = await this.heatMapQueryOut(inputData, incomeData, params);
          done(result, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      // console.log("********Error*********", e)
      done(null, e);
    }
  },

  async getTotalRequestList(req, done) {
    const { timezoneoffset } = req.headers;
    await this.getDynamicModel(req);
    const incomeData = req.body;
    const { params } = req;
    const loginUser = req.user;
    const memberDetails = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
      }),
    });
    const condition = {
      ProjectId: +params.ProjectId,
      isQueued: false,
      isDeleted: false,
    };
    const craneDeliveryRequestCondition = {
      ProjectId: +params.ProjectId,
      isDeleted: false,
    };
    const concreteCondition = {
      ProjectId: +params.ProjectId,
      isDeleted: false,
    };
    const inspectionCondition = {
      ProjectId: +params.ProjectId,
      isQueued: false,
      isDeleted: false,
    };

    if (memberDetails) {
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      const finalArray = incomeData.templateType.map((obj) => {
        return obj.name;
      });
      if (finalArray && finalArray.length > 0) {
        const result = { count: 0, rows: [] };
        const eventArray = [];
        if (finalArray.includes('Delivery')) {
          if (finalArray.includes('Crane')) {
            condition.requestType = 'deliveryRequest';
          }
          const deliveryList = await this.getWeeklyDeliveryRequest(
            condition,
            incomeData,
            params,
            timezoneoffset,
            roleId,
            req,
          );
          if (deliveryList && deliveryList.rows && deliveryList.rows.length > 0) {
            eventArray.push(...deliveryList.rows);
          }
        }
        if (finalArray.includes('Crane')) {
          if (incomeData.statusFilter) {
            craneDeliveryRequestCondition.status = incomeData.statusFilter;
          }
          if (req.body.exportType === 'EXCEL') {
            req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
            req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
          }
          let startDate;
          let endDate;
          if (incomeData.startDate) {
            startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
            endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
              .add(1, 'days')
              .format('YYYY-MM-DD 00:00:00');
          }
          if (incomeData.defineFilter) {
            craneDeliveryRequestCondition['$defineWorkDetails.DeliverDefineWork.id$'] =
              incomeData.defineFilter;
          }
          if (incomeData.companyFilter) {
            craneDeliveryRequestCondition['$companyDetails.Company.id$'] = incomeData.companyFilter;
          }
          if (incomeData.memberFilter > 0) {
            craneDeliveryRequestCondition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
          }
          if (incomeData.equipmentFilter) {
            craneDeliveryRequestCondition['$equipmentDetails.Equipment.id$'] =
              incomeData.equipmentFilter;
          }
          if (incomeData.locationFilter !== 0 && incomeData.locationFilter !== '0') {
            craneDeliveryRequestCondition['$location.id$'] = +incomeData.locationFilter;
          }
          const voidCraneDelivery = [];
          const voidCraneRequestList = await VoidList.findAll({
            where: {
              ProjectId: +params.ProjectId,
              isDeliveryRequest: false,
              CraneRequestId: { [Op.ne]: null },
            },
          });
          voidCraneRequestList.forEach(async (element) => {
            voidCraneDelivery.push(element.CraneRequestId);
          });
          if (params.void === '0' || params.void === 0) {
            craneDeliveryRequestCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
            };
          }
          let craneRequestList;
          if (incomeData.gateFilter) {
            craneRequestList = [];
          } else {
            craneRequestList = await CraneRequest.getWeeklyCalendarList({
              req,
              roleId,
              attr: craneDeliveryRequestCondition,
              start: incomeData.start,
              end: incomeData.end,
              startTime: incomeData.startTime,
              endTime: incomeData.endTime,
              startDate,
              endDate,
              typeFormat: incomeData.typeFormat,
              timezone: incomeData.timezone,
              eventStartTime: incomeData.eventStartTime,
              eventEndTime: incomeData.eventEndTime,
            });
          }
          condition.requestType = 'deliveryRequestWithCrane';
          const deliveryListWithCraneEquipment = await this.getWeeklyDeliveryRequest(
            condition,
            incomeData,
            params,
            timezoneoffset,
            roleId,
            req,
          );
          if (
            deliveryListWithCraneEquipment &&
            deliveryListWithCraneEquipment.rows &&
            deliveryListWithCraneEquipment.rows.length > 0
          ) {
            eventArray.push(...deliveryListWithCraneEquipment.rows);
          }
          if (craneRequestList && craneRequestList.length > 0) {
            eventArray.push(...craneRequestList);
          }
        }
        if (finalArray.includes('Concrete')) {
          if (req.body.exportType === 'EXCEL') {
            req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
            req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
          }
          let startDate;
          let endDate;
          if (incomeData.startDate) {
            startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
            endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
              .add(1, 'days')
              .format('YYYY-MM-DD 00:00:00');
          }
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          }
          let getConcreteRequest;
          if (incomeData.equipmentFilter || incomeData.gateFilter) {
            getConcreteRequest = [];
          } else {
            getConcreteRequest = await ConcreteRequest.getWeeklyCalendarList({
              attr: concreteCondition,
              memberId,
              filters: {
                company: incomeData.companyFilter,
                status: incomeData.statusFilter,
                member: incomeData.memberFilter,
                location: incomeData.locationFilter,
              },
              dateRange: {
                startDate,
                endDate,
              },
              timezone: incomeData.timezone,
              eventStartTime: incomeData.eventStartTime,
              eventEndTime: incomeData.eventEndTime,
            });
          }
          if (getConcreteRequest && getConcreteRequest.length > 0) {
            eventArray.push(...getConcreteRequest);
          }
        }
        if (finalArray.includes('Calendar Events')) {
          let getAllEvents = [];
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.weeklyReportTest = 'weeklyReport';
          req.query.calendarView = req.body.calendarView;
          req.query.timezone = req.body.timezone;
          req.query.isDST = req.body.isDST;
          req.body.eventStartTime = moment(req.body.eventStartTime, 'HH:mm:00').format('HH:mm');
          req.body.eventEndTime = moment(req.body.eventEndTime, 'HH:mm:00').format('HH:mm');
          if (req.body.exportType === 'EXCEL') {
            req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
            req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
          }
          if (incomeData.startDate) {
            req.body.startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format(
              'YYYY-MM-DD 00:00:00',
            );
            req.body.endDate = moment(incomeData.endDate, 'YYYY-MM-DD').format(
              'YYYY-MM-DD 00:00:00',
            );
          }
          if (
            (req.body.equipmentFilter && req.body.equipmentFilter > 0) ||
            (req.body.defineFilter && req.body.defineFilter > 0) ||
            req.body.statusFilter ||
            (req.body.memberFilter && req.body.memberFilter > 0) ||
            (req.body.companyFilter && req.body.companyFilter > 0) ||
            (req.body.gateFilter && req.body.gateFilter > 0)
          ) {
            getAllEvents = [];
          } else {
            getAllEvents = await calendarSettingsService.getAll(req, done);
          }
          if (getAllEvents && getAllEvents.length > 0) {
            eventArray.push(...getAllEvents);
          }
        }
        if (finalArray.includes('Inspection')) {
          const inspectionList = await this.getWeeklyInspectionRequest(
            inspectionCondition,
            incomeData,
            params,
            timezoneoffset,
            roleId,
            req,
          );
          if (inspectionList && inspectionList.rows && inspectionList.rows.length > 0) {
            eventArray.push(...inspectionList.rows);
          }
        }
        if (eventArray && eventArray.length > 0) {
          result.rows = eventArray;
          result.count = eventArray.length;
          if (req.body.exportType) {
            return done(result, false);
          }
          return result;
        }
        if (req.body.exportType) {
          return done(eventArray, false);
        }
        return eventArray;
      }
      throw new ApiError('Please choose a Template type', httpStatus.BAD_REQUEST);
    }
  },

  async getWeeklyDeliveryRequest(condition1, incomeData, params, timezoneoffset, roleId, req) {
    const condition = condition1;
    if (req.body.exportType === 'EXCEL') {
      req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
      req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
    }
    let startDate;
    let endDate;
    if (incomeData.startDate) {
      startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
      endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
        .add(1, 'days')
        .format('YYYY-MM-DD 00:00:00');
    }
    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }
    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }
    if (incomeData.gateFilter) {
      condition['$gateDetails.Gate.id$'] = incomeData.gateFilter;
    }
    if (incomeData.defineFilter) {
      condition['$defineWorkDetails.DeliverDefineWork.id$'] = incomeData.defineFilter;
    }
    if (incomeData.companyFilter) {
      condition['$companyDetails.Company.id$'] = incomeData.companyFilter;
    }
    if (incomeData.memberFilter > 0) {
      condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }
    if (incomeData.locationFilter !== 0 && incomeData.locationFilter !== '0') {
      condition['$location.id$'] = +incomeData.locationFilter;
    }
    const voidDelivery = [];
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: +params.ProjectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    voidList.forEach(async (element) => {
      voidDelivery.push(element.DeliveryRequestId);
    });
    if (params.void === '0' || params.void === 0) {
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };
    }
    const deliveryList = await DeliveryRequest.getWeeklyCalendarData(
      req,
      condition,
      roleId,
      req.body.start,
      req.body.end,
      incomeData.startTime,
      incomeData.endTime,
      startDate,
      endDate,
      incomeData.typeFormat,
      incomeData.timezone,
      incomeData.eventStartTime,
      incomeData.eventEndTime,
    );
    return deliveryList;
  },

  async getWeeklyInspectionRequest(condition1, incomeData, params, timezoneoffset, roleId, req) {
    const condition = condition1;
    if (req.body.exportType === 'EXCEL') {
      req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
      req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
    }
    let startDate;
    let endDate;
    if (incomeData.startDate) {
      startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
      endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
        .add(1, 'days')
        .format('YYYY-MM-DD 00:00:00');
    }
    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }
    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }
    if (incomeData.gateFilter) {
      condition['$gateDetails.Gate.id$'] = incomeData.gateFilter;
    }
    if (incomeData.defineFilter) {
      condition['$defineWorkDetails.DeliverDefineWork.id$'] = incomeData.defineFilter;
    }
    if (incomeData.companyFilter) {
      condition['$companyDetails.Company.id$'] = incomeData.companyFilter;
    }
    if (incomeData.memberFilter > 0) {
      condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }
    if (incomeData.locationFilter !== 0 && incomeData.locationFilter !== '0') {
      condition['$location.id$'] = +incomeData.locationFilter;
    }
    const voidInspection = [];
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: +params.ProjectId,
        isDeliveryRequest: true,
        InspectionRequestId: { [Op.ne]: null },
      },
    });
    voidList.forEach(async (element) => {
      voidInspection.push(element.InspectionRequestId);
    });
    if (params.void === '0' || params.void === 0) {
      condition['$InspectionRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidInspection }],
      };
    }
    const inspectionList = await InspectionRequest.getWeeklyCalendarData({
      req,
      attr: condition,
      roleId,
      dateRange: {
        start: req.body.start,
        end: req.body.end,
        startTime: incomeData.startTime,
        endTime: incomeData.endTime,
        startDate,
        endDate,
      },
      typeFormat: incomeData.typeFormat,
      timezone: incomeData.timezone,
      eventTime: {
        eventStartTime: incomeData.eventStartTime,
        eventEndTime: incomeData.eventEndTime,
      },
    });
    return inspectionList;
  },
};

module.exports = deliveryReportService;