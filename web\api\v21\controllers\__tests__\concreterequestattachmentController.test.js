// Mock concreteRequestAttachmentService
jest.mock('../../services', () => ({
  concreteRequestAttachmentService: {
    createConcreteRequestAttachment: jest.fn(),
    getConcreteRequestAttachments: jest.fn(),
    deleteConcreteRequestAttachment: jest.fn(),
  },
}));

const concreteRequestAttachmentController = require('../concreteRequestAttachmentController');
const { concreteRequestAttachmentService } = require('../../services');

describe('concreteRequestAttachmentController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createConcreteRequestAttachment', () => {
    it('should create concrete request attachment successfully', async () => {
      const mockResponse = { id: 1, filename: 'document.pdf', size: 1024 };

      concreteRequestAttachmentService.createConcreteRequestAttachment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestAttachmentController.createConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.createConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create concrete request attachment', async () => {
      const mockError = new Error('Service error');
      concreteRequestAttachmentService.createConcreteRequestAttachment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestAttachmentController.createConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.createConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create concrete request attachment', async () => {
      const mockError = new Error('Exception error');
      concreteRequestAttachmentService.createConcreteRequestAttachment.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestAttachmentController.createConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.createConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getConcreteRequestAttachments', () => {
    it('should get concrete request attachments successfully', async () => {
      const mockResponse = {
        attachmentList: [
          { id: 1, filename: 'doc1.pdf' },
          { id: 2, filename: 'doc2.pdf' },
        ],
        exist: { id: 1, name: 'Test Request' }
      };

      concreteRequestAttachmentService.getConcreteRequestAttachments.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestAttachmentController.getConcreteRequestAttachments(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.getConcreteRequestAttachments).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Attachment Viewed Successfully.',
        data: mockResponse.attachmentList,
        concreteRequest: mockResponse.exist,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get concrete request attachments', async () => {
      const mockError = new Error('Service error');
      concreteRequestAttachmentService.getConcreteRequestAttachments.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestAttachmentController.getConcreteRequestAttachments(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.getConcreteRequestAttachments).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get concrete request attachments', async () => {
      const mockError = new Error('Exception error');
      concreteRequestAttachmentService.getConcreteRequestAttachments.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestAttachmentController.getConcreteRequestAttachments(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.getConcreteRequestAttachments).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteConcreteRequestAttachment', () => {
    it('should delete concrete request attachment successfully', async () => {
      const mockResponse = { deleted: true, id: 1 };

      concreteRequestAttachmentService.deleteConcreteRequestAttachment.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await concreteRequestAttachmentController.deleteConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.deleteConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Booking Attachment Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from delete concrete request attachment', async () => {
      const mockError = new Error('Service error');
      concreteRequestAttachmentService.deleteConcreteRequestAttachment.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await concreteRequestAttachmentController.deleteConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.deleteConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in delete concrete request attachment', async () => {
      const mockError = new Error('Exception error');
      concreteRequestAttachmentService.deleteConcreteRequestAttachment.mockImplementation(() => {
        throw mockError;
      });

      await concreteRequestAttachmentController.deleteConcreteRequestAttachment(mockReq, mockRes, mockNext);

      expect(concreteRequestAttachmentService.deleteConcreteRequestAttachment).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
