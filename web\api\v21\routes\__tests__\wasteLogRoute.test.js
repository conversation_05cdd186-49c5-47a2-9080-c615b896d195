const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  WasteLogController: {
    addWasteLog: jest.fn(),
    getWasteLogs: jest.fn(),
    updateWasteLog: jest.fn(),
    deleteWasteLog: jest.fn(),
  },
}));

jest.mock('../../controllers/wasteLogController', () => ({
  addWasteLog: jest.fn(),
  listWasteLog: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  wasteLogValidation: {
    addWasteLog: jest.fn(),
    getWasteLogs: jest.fn(),
    updateWasteLog: jest.fn(),
    deleteWasteLog: jest.fn(),
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
  isProjectAdminOnly: jest.fn(),
}));

describe('wasteLogRoute', () => {
  let router;
  let wasteLogRoute;
  let WasteLogController;
  let passportConfig;
  let wasteLogValidation;
  let validate;
  let checkAdmin;
  let wasteLogController;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    wasteLogRoute = require('../wasteLogRoute');
    const controllers = require('../../controllers');
    WasteLogController = controllers.WasteLogController;
    const wasteLogController = require('../../controllers/wasteLogController');
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    wasteLogValidation = validations.wasteLogValidation;
    validate = require('express-validation').validate;
    checkAdmin = require('../../middlewares/checkAdmin');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = wasteLogRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify POST route
      expect(router.post).toHaveBeenCalledWith(
        '/add_wastelog',
        passportConfig.isAuthenticated,
        wasteLogController.addWasteLog,
      );

      // Verify GET route
      expect(router.get).toHaveBeenCalledWith(
        '/wastelog_list/:pageSize/:pageNo',
        passportConfig.isAuthenticated,
        wasteLogController.listWasteLog,
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = wasteLogRoute.router;
      const result2 = wasteLogRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      wasteLogRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];

      allCalls.forEach((call) => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof wasteLogRoute).toBe('object');
      expect(wasteLogRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(wasteLogRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(wasteLogRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
