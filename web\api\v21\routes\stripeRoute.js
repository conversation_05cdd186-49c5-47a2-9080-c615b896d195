const { Router } = require('express');
const { validate } = require('express-validation');
const express = require('express');
const bodyParser = require('body-parser');
const { StripeController } = require('../controllers');
const { stripeValidation } = require('../middlewares/validations');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const stripeRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_plan',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(stripeValidation.createSubscription, { keyByField: true }, { abortEarly: false }),
      StripeController.createPlan,
    );
    router.post(
      '/edit_plan',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(stripeValidation.editSubscription, { keyByField: true }, { abortEarly: false }),
      StripeController.editPlan,
    );
    router.get('/list_all_plans/:interval', StripeController.listAllPlans);
    router.get('/list_upgrade_plans/:interval', StripeController.upgradePlanList);
    router.post('/add_card', passportConfig.isAuthenticated, StripeController.addCard);
    router.get(
      '/list_plans',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      StripeController.listPlans,
    );
    router.put(
      '/update_plan_detail/:id',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      StripeController.updatePlanDetail,
    );
    router
      .route('/cancel_subscription/:ProjectId')
      .get(
        passportConfig.isAuthenticated,
        validate(stripeValidation.cancelSubscription, { keyByField: true }, { abortEarly: false }),
        checkAdmin.isProjectAdmin,
        StripeController.cancelSubscription,
      );
    router
      .route('/hold_resume_subscription/:ProjectId')
      .get(
        passportConfig.isAuthenticated,
        validate(stripeValidation.holdSubscription, { keyByField: true }, { abortEarly: false }),
        checkAdmin.isProjectAdmin,
        StripeController.holdSubscription,
      );
    router.get(
      '/get_session_url',
      passportConfig.isAuthenticated,
      StripeController.stripePortalSession,
    );
    router.post('/webhook', StripeController.wehook);
    router.post('/checkout', passportConfig.isAuthenticated, StripeController.checkout);
    router.post('/create_checkout_session', StripeController.createCheckoutSession);
    return router;
  },
};

module.exports = stripeRoute;
