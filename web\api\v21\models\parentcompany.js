module.exports = (sequelize, DataTypes) => {
  const ParentCompany = sequelize.define(
    'ParentCompany',
    {
      UserId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      emailDomainName: DataTypes.STRING,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  ParentCompany.associate = (models) => {
    ParentCompany.hasMany(models.Company, {
      as: 'Company',
      foreignKey: 'ParentCompanyId',
    });
    return ParentCompany;
  };
  ParentCompany.createInstance = async (paramData) => {
    const newParentCompany = await ParentCompany.create(paramData);
    return newParentCompany;
  };
  ParentCompany.createMultipleInstance = async (paramData) => {
    const newParentCompany = await ParentCompany.bulkCreate(paramData);
    return newParentCompany;
  };
  ParentCompany.getBy = async (attr) => {
    const parentCompany = await ParentCompany.findOne({ where: { ...attr } });

    return parentCompany;
  };
  ParentCompany.getCompany = async (attr) => {
    const parentCompany = await ParentCompany.findOne({
      where: { ...attr },
      include: [
        {
          where: { isParent: true },
          association: 'Company',
          attributes: [
            'id',
            'companyName',
            'website',
            'address',
            'secondAddress',
            'country',
            'city',
            'companyAutoId',
            'state',
            'zipCode',
            'scope',
            'logo',
          ],
        },
      ],
    });

    return parentCompany;
  };
  return ParentCompany;
};
