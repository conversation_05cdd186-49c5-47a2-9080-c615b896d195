const moment = require('moment');
const Cryptr = require('cryptr');
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  CraneRequest,
  CraneRequestResponsible<PERSON>erson,
  CraneRequestComment,
  Member,
  CraneRequestHistory,
  User,
  DeliveryPersonNotification,
  Project,
  Notification,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const MAILER = require('../mailer');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const craneRequestCommentService = {
  async getCraneRequestComments(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const exist = await CraneRequest.findOne({
        where: { CraneRequestId: +params.CraneRequestId, ProjectId: +params.ProjectId },
      });
      if (exist) {
        const commentList = await CraneRequestComment.findAndCountAll({
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          where: {
            CraneRequestId: exist.id,
            isDeleted: false,
          },
        });
        done(commentList, false);
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
    const domainEnterpriseValue = await enterpriseForDomainName(domainName);

    if (!domainEnterpriseValue && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      domainName = await domainForParentCompany(inputData, ParentCompanyId);
    }

    const modelObj = await helper.getDynamicModel(domainName);
    initializeModelVariables(modelObj);

    if (domainEnterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      inputData.user = newUser;
    }
    return true;
  },

  async createCraneRequestComment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const loginUser = inputData.user;
      const exist = await CraneRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id', 'isGuestUser'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: { CraneRequestId: +incomeData.CraneRequestId, ProjectId: +incomeData.ProjectId },
      });
      if (exist) {
        const {
          memberData,
          locationChosen,
          memberLocationPreference,
        } = await retrieveDataForCraneRequest(incomeData, exist.ProjectId, loginUser.id);

        const notification = await prepareCraneRequestNotification(
          incomeData,
          exist,
          loginUser,
          memberData,
          locationChosen
        );

        const previousComments = await fetchPreviousComments(
          incomeData.ProjectId,
          incomeData.CraneRequestId
        );

        const addCraneRequestCommentObject = {
          ProjectId: inputData.body.ProjectId,
          MemberId: memberData.id,
          CraneRequestId: exist.id,
          isDeleted: false,
          comment: inputData.body.comment,
        };
        await CraneRequestComment.create(addCraneRequestCommentObject);

        await createAndNotifyCraneRequestHistory(
          notification,
          memberData,
          inputData,
          loginUser,
          exist,
          memberLocationPreference,
          previousComments
        );
        done(notification.history, false);
      } else {
        done(null, { message: 'Crane Booking does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    populateEmails(data.memberData, existAdminData, emailArray);
    populateEmails(data.adminData, existAdminData, emailArray);
    populateEmails(memberLocationPreference, existAdminData, emailArray, true);
    return emailArray;
  },

  async createDailyDigestData(params) {
    const encryptedRequestId = encryptId(params.requestId);
    const encryptedMemberId = encryptId(params.MemberId);
    const { link } = getRequestDetails(params.requestType);
    const object = {
      description: constructDigestDescription(
        params.loginUser,
        params.dailyDigestMessage,
        link,
        encryptedRequestId,
        encryptedMemberId,
        params.messages
      ),
      MemberId: params.MemberId,
      ProjectId: params.ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId: params.ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
};

async function fetchPreviousComments(projectId, craneRequestId) {
  const previousComments = await CraneRequestComment.findAll({
    where: {
      ProjectId: projectId,
      CraneRequestId: craneRequestId,
    },
    attributes: ['id', 'comment'],
  });
  return previousComments.map((comment) => JSON.stringify(comment.comment)).join(',');
}

function getRequestDetails(requestType) {
  const requestDetails = {
    'Delivery Request': { link: 'delivery-request' },
    'Crane Request': { link: 'crane-request' },
    'Concrete Request': { link: 'concrete-request' },
  };
  return requestDetails[requestType];
}

function constructDigestDescription(
  loginUser,
  dailyDigestMessage,
  link,
  encryptedRequestId,
  encryptedMemberId,
  messages
) {
  return `<div>
    <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
      <li style="display:flex;">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="#" target="" style="text-decoration: none;color:#4470FF;">
            ${loginUser.firstName} ${loginUser.lastName}
          </a> 
          ${dailyDigestMessage}
          <a href="${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;">
          ${messages} 
          </a>
          <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment().utc().format('hh:mm A zz')}</span>
        </p>
      </li>
    </ul>
  </div>`;
}

function encryptId(id) {
  const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
  return cryptr.encrypt(id);
}

function populateEmails(dataArray, existAdminData, emailArray, isLocationPref = false) {
  if (!dataArray) return;

  for (const element of dataArray) {
    const user = isLocationPref ? element.Member.User : element.User;
    const memberId = isLocationPref ? element.Member.id : element.id;
    const roleId = isLocationPref ? element.Member.RoleId : undefined;

    if (!existAdminData.some((admin) => admin.email === user.email)) {
      const emailInfo = {
        email: user.email,
        firstName: user.firstName,
        UserId: user.id,
        MemberId: memberId,
        RoleId: roleId,
      };
      existAdminData.push({ email: user.email });
      emailArray.push(emailInfo);
    }
  }
}

async function createAndNotifyCraneRequestHistory(
  notification,
  memberData,
  inputData,
  loginUser,
  exist,
  memberLocationPreference,
  previousComments
) {
  const userEmails = await craneRequestCommentService.getMemberDetailData(
    notification.history,
    memberLocationPreference
  );
  if (!userEmails.length) {
    return;
  }

  for (const emailObj of userEmails) {
    const mailPayload = prepareMailPayload(
      exist,
      notification,
      previousComments,
      emailObj,
      loginUser
    );
    await processMemberNotifications(
      exist,
      emailObj,
      mailPayload,
      loginUser,
      notification,
      inputData
    );
  }
}

function prepareMailPayload(exist, notification, prevComments, element, loginUser) {
  const timeStamp = moment().utc().format('MM-DD-YYYY hh:mm:ss a zz');
  return {
    craneId: exist.CraneRequestId,
    craneDescription: exist.description,
    craneDeliveryStart: moment(exist.craneDeliveryStart).format('MM-DD-YYYY'),
    craneDeliveryEnd: exist.craneDeliveryEnd,
    newComment: element.body.comment,
    previousComments: prevComments,
    toEmailUserName: element.firstName,
    email: element.email,
    commentedPersonname: loginUser.firstName,
    commentTimeStamp: timeStamp,
  };
}

async function processMemberNotifications(
  exist,
  element,
  mailPayload,
  loginUser,
  notification,
  inputData
) {
  const memberPref = await fetchMemberNotificationPreferences(
    element.MemberId,
    exist.ProjectId,
    exist.LocationId
  );

  if (!memberPref) return;

  if (memberPref.instant) {
    await sendInstantMail(mailPayload);
  }

  if (memberPref.dailyDigest) {
    await craneRequestCommentService.createDailyDigestData({
      MemberId: element.MemberId,
      ProjectId: exist.ProjectId,
      ParentCompanyId: inputData.body.ParentCompanyId,
      loginUser,
      dailyDigestMessage: 'commented in a',
      requestType: 'Crane Request',
      messages: `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
      requestId: exist.CraneRequestId,
    });
  }
}

async function fetchMemberNotificationPreferences(memberId, projectId, locationId) {
  const isMemberFollowLoc = await LocationNotificationPreferences.findOne({
    where: { MemberId: memberId, ProjectId: projectId, LocationId: locationId, isDeleted: false },
  });

  if (!isMemberFollowLoc) return;

  return await NotificationPreference.findOne({
    where: { MemberId: memberId, ProjectId: projectId, isDeleted: false },
    include: [
      {
        association: 'NotificationPreferenceItem',
        where: { id: 7, isDeleted: false },
      },
    ],
  });
}

async function sendInstantMail(mailPayload) {
  await MAILER.sendMail(
    mailPayload,
    'cranecommentadded',
    `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Crane ID ${mailPayload.craneId}`,
    'Comments added against a Crane Booking Notification',
    (info, err) => console.log(info, err)
  );
}

async function prepareCraneRequestNotification(incomeData, exist, loginUser, memberData, locationChosen) {
  const description = `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`;
  const locationDescription = `${description} Location: ${locationChosen.locationPath}.`;
  const notification = {
    CraneRequestId: exist.id,
    MemberId: memberData.id,
    type: 'comment',
    description: description,
    locationFollowDescription: locationDescription,
    ProjectId: exist.ProjectId,
    title: 'Crane Booking Comment',
    isDeliveryRequest: false,
    requestType: 'craneRequest',
  };

  await CraneRequestHistory.create(notification);
  return { history: notification };
}

async function retrieveDataForCraneRequest(incomeData, projectId, userId) {
  const memberData = await findMemberData(projectId, userId);
  const locationChosen = await findLocationChosen(projectId, incomeData.locationId);
  const memberLocationPreference = await findMemberLocationPreference(
    projectId,
    incomeData.locationId,
    memberData.id
  );
  return { memberData, locationChosen, memberLocationPreference };
}

async function findMemberData(projectId, userId) {
  return await Member.findOne({
    where: Sequelize.and({ UserId: userId, ProjectId: projectId, isDeleted: false }),
  });
}

async function findLocationChosen(projectId, locationId) {
  return await Locations.findOne({
    where: { ProjectId: projectId, id: locationId },
  });
}

async function findMemberLocationPreference(projectId, locationId, memberDataId) {
  return await LocationNotificationPreferences.findAll({
    where: {
      ProjectId: projectId,
      LocationId: locationId,
      follow: true,
    },
    include: [
      {
        association: 'Member',
        attributes: ['id', 'RoleId'],
        where: { id: { [Op.ne]: memberDataId } },
        include: [{ association: 'User', attributes: ['id', 'firstName', 'lastName', 'email'] }],
      },
    ],
  });
}

async function domainForParentCompany(inputData, parentCompanyId) {
  const { email } = inputData.user;
  const enterpriseValue = await getEnterpriseValue(email, parentCompanyId);
  return enterpriseValue ? enterpriseValue.toLowerCase() : '';
}

async function getEnterpriseValue(email, parentCompanyId) {
  if (!email) return;

  const userData = await publicUser.findOne({ where: { email } });
  if (!userData) return;

  const memberData = await publicMember.findOne({
    where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
  });

  if (memberData) {
    return memberData.isAccount
      ? await getEnterpriseName(memberData.EnterpriseId)
      : await getEnterpriseName({ ParentCompanyId: parentCompanyId });
  } else {
    return await getEnterpriseName({ ParentCompanyId: parentCompanyId });
  }
}

async function getEnterpriseName(query) {
  const enterpriseValue = await Enterprise.findOne({ where: { ...query, status: 'completed' } });
  return enterpriseValue?.name;
}

async function enterpriseForDomainName(domainName) {
  if (!domainName) return '';

  const domainEnterpriseValue = await Enterprise.findOne({
    where: { name: domainName.toLowerCase() },
  });
  return domainEnterpriseValue ? domainEnterpriseValue.name : '';
}

function initializeModelVariables(modelObj) {
  CraneRequest = modelObj.CraneRequest;
  CraneRequestResponsiblePerson = modelObj.CraneRequestResponsiblePerson;
  CraneRequestComment = modelObj.CraneRequestComment;
  Member = modelObj.Member;
  User = modelObj.User;
  CraneRequestHistory = modelObj.CraneRequestHistory;
  Project = modelObj.Project;
  DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
  Notification = modelObj.Notification;
}

// Export helper functions for testing
craneRequestCommentService.fetchPreviousComments = fetchPreviousComments;
craneRequestCommentService.getRequestDetails = getRequestDetails;

module.exports = craneRequestCommentService;