const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { HistoryController } = require('../controllers');
const { historyValidation } = require('../middlewares/validations');
const cacheMiddleware = require('../middlewares/cacheMiddleware');
const commentRoute = {
  get router() {
    const router = Router();
    router.get(
      '/get_history/:DeliveryRequestId/?:ParentCompanyId',
      validate(historyValidation.getHistory, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheHistory(),
      HistoryController.getHistory,
    );

    router.get(
      '/get_inspection_history/:InspectionRequestId/?:ParentCompanyId',
      validate(historyValidation.getInspectionHistory, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheInspectionHistory(),
      HistoryController.getInspectionHistory,
    );
    return router;
  },
};
module.exports = commentRoute;
