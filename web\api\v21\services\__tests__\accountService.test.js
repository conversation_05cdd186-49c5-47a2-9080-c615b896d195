const { Project, ParentCompany } = require('../models');
const stripeService = require('../accountService');

// Mock the models
jest.mock('../models', () => ({
    Project: {
        findAndCountAll: jest.fn(),
        getAllNonEnterprise: jest.fn(),
    },
    ParentCompany: {
        findAll: jest.fn(),
    },
}));

describe('Stripe Service', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('getEnterpriseAccounts', () => {
        const mockParentCompanies = [
            { id: 1, name: 'Company 1', isDeleted: false },
            { id: 2, name: 'Company 2', isDeleted: false },
            { id: 3, name: 'Company 3', isDeleted: false },
        ];

        const mockProjectCounts = [
            { count: 3 }, // Less than 5 projects
            { count: 6 }, // More than 5 projects
            { count: 2 }, // Less than 5 projects
        ];

        it('should return enterprise accounts with pagination', async () => {
            // Mock ParentCompany.findAll
            ParentCompany.findAll.mockResolvedValue(mockParentCompanies);

            // Mock Project.findAndCountAll for each company
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: mockProjectCounts[0].count })
                .mockResolvedValueOnce({ count: mockProjectCounts[1].count })
                .mockResolvedValueOnce({ count: mockProjectCounts[2].count });

            const req = {
                query: {
                    pageSize: 2,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);

            expect(ParentCompany.findAll).toHaveBeenCalledWith({
                where: { isDeleted: false },
                order: [['name', 'ASC']],
            });

            expect(Project.findAndCountAll).toHaveBeenCalledTimes(3);
            expect(result.count).toBe(2); // Only companies with < 5 projects
            expect(result.rows.length).toBe(2);
            expect(result.rows[0].projectCount).toBe(3);
            expect(result.rows[1].projectCount).toBe(2);
        });

        it('should handle empty parent companies list', async () => {
            ParentCompany.findAll.mockResolvedValue([]);

            const req = {
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);

            expect(result.count).toBe(0);
            expect(result.rows).toHaveLength(0);
        });

        it('should handle database errors gracefully', async () => {
            ParentCompany.findAll.mockRejectedValue(new Error('Database error'));

            const req = {
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);
            expect(result).toBeUndefined();
        });

        it('should handle missing pagination parameters', async () => {
            ParentCompany.findAll.mockResolvedValue(mockParentCompanies);
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: 3 })
                .mockResolvedValueOnce({ count: 6 })
                .mockResolvedValueOnce({ count: 2 });

            const req = {
                query: {
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);
            expect(result.count).toBe(2);
            // When pageSize is undefined, slice(undefined, undefined + undefined) returns empty array
            expect(result.rows).toHaveLength(0);
        });

        it('should handle companies with exactly 5 projects (boundary test)', async () => {
            const mockCompaniesWithFiveProjects = [
                { id: 1, name: 'Company 1', isDeleted: false },
                { id: 2, name: 'Company 2', isDeleted: false },
            ];

            ParentCompany.findAll.mockResolvedValue(mockCompaniesWithFiveProjects);
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: 5 }) // Exactly 5 projects - should be excluded
                .mockResolvedValueOnce({ count: 4 }); // Less than 5 - should be included

            const req = {
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);
            expect(result.count).toBe(1);
            expect(result.rows).toHaveLength(1);
            expect(result.rows[0].projectCount).toBe(4);
        });

        it('should handle companies with more than 5 projects', async () => {
            const mockCompaniesWithManyProjects = [
                { id: 1, name: 'Company 1', isDeleted: false },
                { id: 2, name: 'Company 2', isDeleted: false },
            ];

            ParentCompany.findAll.mockResolvedValue(mockCompaniesWithManyProjects);
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: 10 }) // More than 5 - should be excluded
                .mockResolvedValueOnce({ count: 15 }); // More than 5 - should be excluded

            const req = {
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);
            expect(result.count).toBe(0);
            expect(result.rows).toHaveLength(0);
        });

        it('should handle pagination with offset calculation', async () => {
            const mockManyCompanies = [
                { id: 1, name: 'Company 1', isDeleted: false },
                { id: 2, name: 'Company 2', isDeleted: false },
                { id: 3, name: 'Company 3', isDeleted: false },
                { id: 4, name: 'Company 4', isDeleted: false },
                { id: 5, name: 'Company 5', isDeleted: false },
            ];

            ParentCompany.findAll.mockResolvedValue(mockManyCompanies);
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: 1 })
                .mockResolvedValueOnce({ count: 2 })
                .mockResolvedValueOnce({ count: 3 })
                .mockResolvedValueOnce({ count: 4 })
                .mockResolvedValueOnce({ count: 1 });

            const req = {
                query: {
                    pageSize: 2,
                    pageNo: 2, // Second page
                    sortColumn: 'name',
                    sortType: 'DESC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);
            expect(result.count).toBe(5);
            expect(result.rows).toHaveLength(2); // Page size is 2
        });

        it('should handle different sort types and columns', async () => {
            ParentCompany.findAll.mockResolvedValue(mockParentCompanies);
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: 1 })
                .mockResolvedValueOnce({ count: 2 })
                .mockResolvedValueOnce({ count: 3 });

            const req = {
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    sortColumn: 'id',
                    sortType: 'DESC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);

            expect(ParentCompany.findAll).toHaveBeenCalledWith({
                where: { isDeleted: false },
                order: [['id', 'DESC']],
            });
            expect(result.count).toBe(3);
        });

        it('should handle Project.findAndCountAll errors for individual companies', async () => {
            ParentCompany.findAll.mockResolvedValue(mockParentCompanies);
            Project.findAndCountAll
                .mockResolvedValueOnce({ count: 3 })
                .mockRejectedValueOnce(new Error('Project query failed'))
                .mockResolvedValueOnce({ count: 2 });

            const req = {
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getEnterpriseAccounts(req);
            expect(result).toBeUndefined();
        });
    });

    describe('getNonEnterpriseAccountProjects', () => {
        const mockProjects = {
            rows: [
                { id: 1, name: 'Project 1' },
                { id: 2, name: 'Project 2' },
                { id: 3, name: 'Project 3' },
            ],
        };

        it('should return non-enterprise projects with pagination', async () => {
            Project.getAllNonEnterprise.mockResolvedValue(mockProjects);

            const req = {
                params: { id: '1' },
                query: {
                    pageSize: 2,
                    pageNo: 1,
                    sortColumn: 'name',
                    sortType: 'ASC',
                    search: 'test',
                    projectName: 'Project',
                    companyName: 'Company',
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(Project.getAllNonEnterprise).toHaveBeenCalledWith({
                attr: { ParentCompanyId: 1, isDeleted: false },
                limit: 2,
                offset: 0,
                searchText: 'test',
                sortByField: 'name',
                sortByType: 'ASC',
                projectName: 'Project',
                companyName: 'Company',
            });

            expect(result.count).toBe(3);
            expect(result.rows.length).toBe(2);
        });

        it('should handle empty projects list', async () => {
            Project.getAllNonEnterprise.mockResolvedValue({ rows: [] });

            const req = {
                params: { id: '1' },
                query: {
                    pageSize: 10,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(result.count).toBe(0);
            expect(result.rows).toHaveLength(0);
        });

        it('should handle database errors gracefully', async () => {
            Project.getAllNonEnterprise.mockRejectedValue(new Error('Database error'));

            const req = {
                params: { id: '1' },
                query: {
                    pageSize: 10,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);
            expect(result).toBeUndefined();
        });

        it('should handle missing pagination parameters', async () => {
            Project.getAllNonEnterprise.mockResolvedValue(mockProjects);

            const req = {
                params: { id: '1' },
                query: {
                    sortColumn: 'name',
                    sortType: 'ASC',
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);
            expect(result.count).toBe(3);
            // When pageSize is undefined, slice(undefined, undefined + undefined) returns empty array
            expect(result.rows).toHaveLength(0);
        });

        it('should handle invalid parent company ID', async () => {
            Project.getAllNonEnterprise.mockResolvedValue({ rows: [] });

            const req = {
                params: { id: 'invalid' },
                query: {
                    pageSize: 10,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);
            expect(result.count).toBe(0);
            expect(result.rows).toHaveLength(0);
        });

        it('should handle numeric string parent company ID conversion', async () => {
            Project.getAllNonEnterprise.mockResolvedValue(mockProjects);

            const req = {
                params: { id: '123' },
                query: {
                    pageSize: 5,
                    pageNo: 1,
                    sortColumn: 'id',
                    sortType: 'DESC',
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(Project.getAllNonEnterprise).toHaveBeenCalledWith({
                attr: { ParentCompanyId: 123, isDeleted: false },
                limit: 5,
                offset: 0,
                searchText: undefined,
                sortByField: 'id',
                sortByType: 'DESC',
                projectName: undefined,
                companyName: undefined,
            });
            expect(result.count).toBe(3);
        });

        it('should handle all query parameters', async () => {
            const mockProjectsWithAllParams = {
                rows: [
                    { id: 1, name: 'Project 1', company: 'Company A' },
                    { id: 2, name: 'Project 2', company: 'Company B' },
                ],
            };

            Project.getAllNonEnterprise.mockResolvedValue(mockProjectsWithAllParams);

            const req = {
                params: { id: '456' },
                query: {
                    pageSize: 3,
                    pageNo: 2,
                    sortColumn: 'createdAt',
                    sortType: 'ASC',
                    search: 'test search',
                    projectName: 'Test Project',
                    companyName: 'Test Company',
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(Project.getAllNonEnterprise).toHaveBeenCalledWith({
                attr: { ParentCompanyId: 456, isDeleted: false },
                limit: 3,
                offset: 3, // (pageNo - 1) * pageSize = (2 - 1) * 3 = 3
                searchText: 'test search',
                sortByField: 'createdAt',
                sortByType: 'ASC',
                projectName: 'Test Project',
                companyName: 'Test Company',
            });
            expect(result.count).toBe(2);
            // With offset 3 and only 2 items total, slice(3, 3+3) returns empty array
            expect(result.rows).toHaveLength(0);
        });

        it('should handle zero parent company ID', async () => {
            Project.getAllNonEnterprise.mockResolvedValue({ rows: [] });

            const req = {
                params: { id: '0' },
                query: {
                    pageSize: 10,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(Project.getAllNonEnterprise).toHaveBeenCalledWith({
                attr: { ParentCompanyId: 0, isDeleted: false },
                limit: 10,
                offset: 0,
                searchText: undefined,
                sortByField: undefined,
                sortByType: undefined,
                projectName: undefined,
                companyName: undefined,
            });
            expect(result.count).toBe(0);
        });

        it('should handle negative parent company ID', async () => {
            Project.getAllNonEnterprise.mockResolvedValue({ rows: [] });

            const req = {
                params: { id: '-1' },
                query: {
                    pageSize: 10,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(Project.getAllNonEnterprise).toHaveBeenCalledWith({
                attr: { ParentCompanyId: -1, isDeleted: false },
                limit: 10,
                offset: 0,
                searchText: undefined,
                sortByField: undefined,
                sortByType: undefined,
                projectName: undefined,
                companyName: undefined,
            });
            expect(result.count).toBe(0);
        });

        it('should handle pagination edge cases - page 1', async () => {
            const mockLargeProjectList = {
                rows: Array.from({ length: 10 }, (_, i) => ({ id: i + 1, name: `Project ${i + 1}` })),
            };

            Project.getAllNonEnterprise.mockResolvedValue(mockLargeProjectList);

            const req = {
                params: { id: '1' },
                query: {
                    pageSize: 3,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);
            expect(result.count).toBe(10);
            expect(result.rows).toHaveLength(3); // Should slice to page size
        });

        it('should handle pagination edge cases - last page with fewer items', async () => {
            const mockSmallProjectList = {
                rows: [
                    { id: 1, name: 'Project 1' },
                    { id: 2, name: 'Project 2' },
                ],
            };

            Project.getAllNonEnterprise.mockResolvedValue(mockSmallProjectList);

            const req = {
                params: { id: '1' },
                query: {
                    pageSize: 5,
                    pageNo: 1,
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);
            expect(result.count).toBe(2);
            expect(result.rows).toHaveLength(2); // Should return all available items
        });

        it('should handle empty search parameters', async () => {
            Project.getAllNonEnterprise.mockResolvedValue(mockProjects);

            const req = {
                params: { id: '1' },
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    search: '',
                    projectName: '',
                    companyName: '',
                },
            };

            const result = await stripeService.getNonEnterpriseAccountProjects(req);

            expect(Project.getAllNonEnterprise).toHaveBeenCalledWith({
                attr: { ParentCompanyId: 1, isDeleted: false },
                limit: 10,
                offset: 0,
                searchText: '',
                sortByField: undefined,
                sortByType: undefined,
                projectName: '',
                companyName: '',
            });
            expect(result.count).toBe(3);
        });
    });
}); 