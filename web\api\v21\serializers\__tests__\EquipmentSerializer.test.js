const EquipmentSerializer = require('../EquipmentSerializer');

describe('EquipmentSerializer', () => {
  it('should serialize all fields correctly', () => {
    const equipment = {
      id: 1,
      equipmentName: 'Excavator',
      createdBy: 2,
      controlledBy: 3,
      projectId: 4,
      createdAt: '2024-01-01T00:00:00Z',
    };
    const result = EquipmentSerializer.serialize(equipment);
    expect(result).toEqual(equipment);
  });

  it('should serialize with missing fields as undefined', () => {
    const equipment = {
      id: 1,
      equipmentName: 'Excavator',
    };
    const result = EquipmentSerializer.serialize(equipment);
    expect(result).toEqual({
      id: 1,
      equipmentName: 'Excavator',
      createdBy: undefined,
      controlledBy: undefined,
      projectId: undefined,
      createdAt: undefined,
    });
  });
});
