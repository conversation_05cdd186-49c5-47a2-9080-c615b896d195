// Mock all external dependencies BEFORE importing the service (deliveryService pattern)
const moment = require('moment');

// Mock moment
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return actualMoment;
});

// Mock sequelize
const mockSequelize = {
  DataTypes: {
    STRING: 'STRING',
    INTEGER: 'INTEGER',
    BOOLEAN: 'BOOLEAN',
    DATE: 'DATE',
    TEXT: 'TEXT',
    DECIMAL: 'DECIMAL'
  },
  and: jest.fn(() => ({ and: 'condition' })),
  or: jest.fn(() => ({ or: 'condition' })),
  literal: jest.fn(() => 'LITERAL_RESULT'),
  Op: {
    and: Symbol('and'),
    or: Symbol('or'),
    eq: Symbol('eq'),
    ne: Symbol('ne'),
    in: Symbol('in'),
    notIn: Symbol('notIn'),
    like: Symbol('like'),
    notLike: Symbol('notLike'),
    gt: Symbol('gt'),
    gte: Symbol('gte'),
    lt: Symbol('lt'),
    lte: Symbol('lte')
  }
};

jest.mock('sequelize', () => mockSequelize);

// Mock the mailer
jest.mock('../../mailer', () => ({
  sendMail: jest.fn().mockResolvedValue(true)
}));

// Mock all models
const mockModels = {
  Sequelize: {
    Op: {
      and: 'and',
      or: 'or',
      in: 'in',
      notIn: 'notIn',
      between: 'between',
      gte: 'gte',
      lte: 'lte',
      ne: 'ne',
      like: 'like'
    }
  },
  Enterprise: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  TimeZone: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getProjectAndSettings: jest.fn()
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
    count: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  CraneRequest: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    getAll: jest.fn(),
    getCraneRequestData: jest.fn(),
    getSingleCraneRequestData: jest.fn()
  },
  DeliveryRequest: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getCraneAssociatedRequest: jest.fn()
  },
  Equipments: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  Gates: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Locations: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Company: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  Role: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  CraneRequestHistory: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn()
  },
  CraneRequestResponsiblePerson: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  CraneRequestCompany: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  CraneRequestEquipment: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  CraneRequestDefinableFeatureOfWork: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  DeliverDefineWork: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn()
  },
  RequestRecurrenceSeries: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn()
  },
  ProjectSettings: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  EquipmentMapping: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn()
  },
  Notification: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  }
};

// Mock external services
jest.mock('../../models', () => mockModels);

jest.mock('../../helpers/domainHelper', () => ({
  returnProjectModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  }),
  getDynamicModel: jest.fn().mockResolvedValue({
    Member: mockModels.Member,
    User: mockModels.User
  })
}));

jest.mock('../../helpers/notificationHelper', () => ({
  sendNotification: jest.fn()
}));

jest.mock('../../config/fcm', () => ({
  sendPushNotification: jest.fn()
}));

jest.mock('../concreteRequestService', () => ({
  insertRecurrenceSeries: jest.fn()
}));

jest.mock('../voidService', () => ({
  checkVoidStatus: jest.fn()
}));

// Import the actual service AFTER all mocks are set up (deliveryService pattern)
const craneRequestService = require('../craneRequestService');

describe('CraneRequestService Comprehensive Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateCraneTimeZone', () => {
    it('should validate timezone successfully', async () => {
      const mockTimeZone = {
        id: 1,
        location: 'America/New_York',
        isDayLightSavingEnabled: true,
        timeZoneOffsetInMinutes: -300,
        dayLightSavingTimeInMinutes: -240,
        timezone: 'America/New_York'
      };
      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);

      const result = await craneRequestService.validateCraneTimeZone(1);

      expect(result).toEqual(mockTimeZone);
      expect(mockModels.TimeZone.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone'
        ]
      });
    });

    it('should return null for invalid timezone', async () => {
      mockModels.TimeZone.findOne.mockResolvedValue(null);

      const result = await craneRequestService.validateCraneTimeZone(999);

      expect(result).toBeNull();
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully', async () => {
      const inputData = {
        params: { ParentCompanyId: 1 },
        user: {
          domainName: 'example.com',
          email: '<EMAIL>',
          ParentCompanyId: 1
        },
        headers: {
          host: 'example.follo.com'
        },
        body: {
          ProjectId: 1
        }
      };

      const mockEnterprise = {
        id: 1,
        name: 'example',
        domainName: 'example.com',
        isDeleted: false
      };

      const mockProject = {
        id: 1,
        name: 'Test Project',
        domainName: 'example.com'
      };

      const mockModelData = { Member: mockModels.Member, User: mockModels.User };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Project.findOne.mockResolvedValue(mockProject);
      require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelData);

      const result = await craneRequestService.getDynamicModel(inputData);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalled();
      expect(require('../../helpers/domainHelper').getDynamicModel).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('checkInputDatas', () => {
    it('should validate input data successfully', async () => {
      const inputData = {
        body: {
          companies: [1, 2],
          responsiblePersons: [1, 2],
          definableFeatureOfWorks: [1, 2],
          EquipmentId: [1, 2],
          ProjectId: 1
        },
        params: { ParentCompanyId: 1 },
        user: {
          domainName: 'example.com',
          email: '<EMAIL>',
          ParentCompanyId: 1
        },
        headers: {
          host: 'example.follo.com'
        }
      };

      const mockEnterprise = {
        id: 1,
        name: 'example',
        domainName: 'example.com',
        isDeleted: false
      };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.Equipments.count.mockResolvedValue(2);
      mockModels.DeliverDefineWork.count.mockResolvedValue(2);
      mockModels.Company.count.mockResolvedValue(2);

      const done = jest.fn();
      await craneRequestService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should handle validation errors', async () => {
      const inputData = {
        body: {
          companies: [1, 2, 3],
          responsiblePersons: [1, 2],
          definableFeatureOfWorks: [1, 2],
          EquipmentId: [1, 2],
          ProjectId: 1
        },
        params: { ParentCompanyId: 1 },
        user: {
          domainName: 'example.com',
          email: '<EMAIL>',
          ParentCompanyId: 1
        },
        headers: {
          host: 'example.follo.com'
        }
      };

      const mockEnterprise = {
        id: 1,
        name: 'example',
        domainName: 'example.com',
        isDeleted: false
      };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.Equipments.count.mockResolvedValue(2);
      mockModels.DeliverDefineWork.count.mockResolvedValue(2);
      mockModels.Company.count.mockResolvedValue(2); // Less than expected

      const done = jest.fn();
      await craneRequestService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

describe('CraneRequestService Comprehensive Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateCraneTimeZone', () => {
    it('should validate timezone successfully', async () => {
      const mockTimeZone = {
        id: 1,
        location: 'America/New_York',
        isDayLightSavingEnabled: true,
        timeZoneOffsetInMinutes: -300,
        dayLightSavingTimeInMinutes: -240,
        timezone: 'America/New_York'
      };
      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);

      const result = await craneRequestService.validateCraneTimeZone(1);

      expect(result).toEqual(mockTimeZone);
      expect(mockModels.TimeZone.findOne).toHaveBeenCalledWith({
        where: {
          isDeleted: false,
          id: 1
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone'
        ]
      });
    });

    it('should return null for invalid timezone', async () => {
      mockModels.TimeZone.findOne.mockResolvedValue(null);

      const result = await craneRequestService.validateCraneTimeZone(999);

      expect(result).toBeNull();
    });
  });

  describe('getDynamicModel', () => {
    it('should get dynamic model successfully', async () => {
      const inputData = {
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockModelData = { Member: mockModels.Member, User: mockModels.User };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      require('../../helpers/domainHelper').getDynamicModel.mockResolvedValue(mockModelData);

      await craneRequestService.getDynamicModel(inputData);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalled();
      expect(require('../../helpers/domainHelper').getDynamicModel).toHaveBeenCalled();
    });
  });

  describe('checkInputDatas', () => {
    it('should validate input data successfully', async () => {
      const inputData = {
        body: {
          companies: [1, 2],
          responsiblePersons: [1, 2],
          definableFeatureOfWorks: [1, 2],
          EquipmentId: [1, 2],
          ProjectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.Equipments.count.mockResolvedValue(2);
      mockModels.DeliverDefineWork.count.mockResolvedValue(2);
      mockModels.Company.count.mockResolvedValue(2);

      const done = jest.fn();
      await craneRequestService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.any(Object), false);
    });

    it('should handle validation errors', async () => {
      const inputData = {
        body: {
          companies: [1, 2, 3],
          responsiblePersons: [1, 2],
          definableFeatureOfWorks: [1, 2],
          EquipmentId: [1, 2],
          ProjectId: 1
        },
        params: { ParentCompanyId: null },
        user: { domainName: 'example.com' }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(2);
      mockModels.Equipments.count.mockResolvedValue(2);
      mockModels.DeliverDefineWork.count.mockResolvedValue(2);
      mockModels.Company.count.mockResolvedValue(2); // Less than expected

      const done = jest.fn();
      await craneRequestService.checkInputDatas(inputData, done);

      expect(done).toHaveBeenCalledWith(null, expect.any(Error));
    });
  });

  describe('newCraneRequest', () => {
    it('should create new crane request successfully', async () => {
      const inputData = {
        body: {
          description: 'Test crane request',
          craneDeliveryStart: moment().add(1, 'week').format('YYYY-MM-DD'),
          craneDeliveryEnd: moment().add(1, 'week').format('YYYY-MM-DD'),
          startPicker: '10:00',
          endPicker: '12:00',
          recurrence: 'Does Not Repeat',
          TimeZoneId: 1,
          ProjectId: 1,
          ParentCompanyId: null,
          LocationId: 1,
          GateId: 1,
          companies: [1],
          EquipmentId: [1],
          responsiblePersons: [1],
          definableFeatureOfWorks: [1]
        },
        params: { ParentCompanyId: null },
        user: {
          id: 1,
          domainName: 'example.com',
          firstName: 'John',
          lastName: 'Doe'
        }
      };

      // Setup comprehensive mocks
      const mockTimeZone = { id: 1, timezone: 'America/New_York' };
      const mockProject = {
        id: 1,
        ProjectSettings: {
          isAutoApprovalEnabled: false,
          craneWindowTime: 24,
          craneWindowTimeUnit: 'hours'
        }
      };
      const mockMember = {
        id: 1,
        ProjectId: 1,
        RoleId: 3,
        User: { id: 1, firstName: 'John', lastName: 'Doe' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);
      mockModels.Project.getProjectAndSettings.mockResolvedValue(mockProject);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(1);
      mockModels.Equipments.count.mockResolvedValue(1);
      mockModels.DeliverDefineWork.count.mockResolvedValue(1);
      mockModels.Company.count.mockResolvedValue(1);
      mockModels.CraneRequest.findOne.mockResolvedValue({ CraneRequestId: 5 });
      mockModels.CraneRequest.createInstance.mockResolvedValue({ id: 1 });

      const done = jest.fn();
      await craneRequestService.newCraneRequest(inputData, done);

      expect(done).toHaveBeenCalled();
    });

    it('should handle validation errors in newCraneRequest', async () => {
      const inputData = {
        body: {
          TimeZoneId: 999, // Invalid timezone
          ProjectId: 1,
          ParentCompanyId: null
        },
        params: { ParentCompanyId: null },
        user: {
          id: 1,
          domainName: 'example.com'
        }
      };

      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.TimeZone.findOne.mockResolvedValue(null); // Invalid timezone

      const done = jest.fn();
      await craneRequestService.newCraneRequest(inputData, done);

      expect(done).toHaveBeenCalledWith(null, { message: expect.any(String) });
    });
  });

  describe('listCraneRequest', () => {
    it('should list crane requests successfully', async () => {
      const inputData = {
        params: {
          ProjectId: 1,
          ParentCompanyId: null,
          pageSize: 10,
          pageNumber: 1,
          void: 0
        },
        body: {
          ParentCompanyId: null
        },
        user: {
          id: 1,
          domainName: 'example.com'
        }
      };

      const mockCraneRequests = [
        { id: 1, description: 'Crane 1', status: 'Approved' },
        { id: 2, description: 'Crane 2', status: 'Pending' }
      ];
      const mockDeliveryRequests = [
        { id: 1, description: 'Delivery 1', status: 'Approved' }
      ];
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockMember = { id: 1, ProjectId: 1, RoleId: 3 };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.CraneRequest.getAll.mockResolvedValue(mockCraneRequests);
      mockModels.DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryRequests);

      const done = jest.fn();
      await craneRequestService.listCraneRequest(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array)
      }), false);
    });
  });

  describe('Utility Methods', () => {
    describe('prepareCraneRequestInput', () => {
      it('should prepare crane request input successfully', async () => {
        const inputData = {
          body: {
            TimeZoneId: 1,
            ProjectId: 1
          }
        };

        const mockTimeZone = { id: 1, timezone: 'America/New_York' };
        const mockProject = {
          id: 1,
          ProjectSettings: {
            isAutoApprovalEnabled: false
          }
        };

        mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);
        mockModels.Project.getProjectAndSettings.mockResolvedValue(mockProject);

        const result = await craneRequestService.prepareCraneRequestInput(inputData);

        expect(result).toEqual({
          craneRequestDetail: inputData.body,
          eventTimeZone: mockTimeZone,
          projectDetails: mockProject
        });
      });

      it('should handle invalid timezone in prepareCraneRequestInput', async () => {
        const inputData = {
          body: {
            TimeZoneId: 999,
            ProjectId: 1
          }
        };

        mockModels.TimeZone.findOne.mockResolvedValue(null);

        const result = await craneRequestService.prepareCraneRequestInput(inputData);

        expect(result).toEqual({
          error: true,
          message: 'Provide a valid timezone'
        });
      });
    });

    describe('getNextCraneRequestId', () => {
      it('should generate next crane request ID', async () => {
        mockModels.CraneRequest.findOne.mockResolvedValue({ CraneRequestId: 5 });

        const result = await craneRequestService.getNextCraneRequestId(1);

        expect(typeof result).toBe('number');
        expect(result).toBeGreaterThan(5);
      });

      it('should handle no existing crane requests', async () => {
        mockModels.CraneRequest.findOne.mockResolvedValue(null);

        const result = await craneRequestService.getNextCraneRequestId(1);

        expect(result).toBe(1);
      });
    });
  });
});

  describe('listCraneRequest', () => {
    it('should list crane requests successfully', async () => {
      const inputData = {
        params: {
          ProjectId: 1,
          ParentCompanyId: null,
          pageSize: 10,
          pageNumber: 1
        },
        body: {
          ParentCompanyId: null,
          void: 0
        },
        user: {
          id: 1,
          domainName: 'example.com'
        }
      };

      const mockCraneRequests = [
        { id: 1, description: 'Crane 1', status: 'Approved' },
        { id: 2, description: 'Crane 2', status: 'Pending' }
      ];
      const mockDeliveryRequests = [
        { id: 1, description: 'Delivery 1', status: 'Approved' }
      ];
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };
      const mockMember = { id: 1, ProjectId: 1, RoleId: 3 };

      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.CraneRequest.getAll.mockResolvedValue(mockCraneRequests);
      mockModels.DeliveryRequest.getCraneAssociatedRequest.mockResolvedValue(mockDeliveryRequests);

      const done = jest.fn();
      await craneRequestService.listCraneRequest(inputData, done);

      expect(done).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array)
      }), false);
    });
  });

  describe('editCraneRequest', () => {
    it('should edit crane request successfully', async () => {
      const inputData = {
        body: {
          CraneRequestId: 1,
          description: 'Updated crane request',
          craneDeliveryStart: moment().add(1, 'week').format('YYYY-MM-DD'),
          craneDeliveryEnd: moment().add(1, 'week').format('YYYY-MM-DD'),
          startPicker: '11:00',
          endPicker: '13:00',
          TimeZoneId: 1,
          ProjectId: 1,
          ParentCompanyId: null,
          LocationId: 1,
          GateId: 1,
          companies: [1],
          EquipmentId: [1],
          responsiblePersons: [1],
          definableFeatureOfWorks: [1]
        },
        params: { ParentCompanyId: null },
        user: {
          id: 1,
          domainName: 'example.com',
          firstName: 'John',
          lastName: 'Doe'
        }
      };

      // Mock existing crane request
      const mockExistingCrane = {
        id: 1,
        CraneRequestId: 1,
        description: 'Original crane request',
        status: 'Pending'
      };

      const mockTimeZone = { id: 1, timezone: 'America/New_York' };
      const mockProject = {
        id: 1,
        ProjectSettings: {
          isAutoApprovalEnabled: false
        }
      };
      const mockMember = {
        id: 1,
        ProjectId: 1,
        RoleId: 2,
        User: { id: 1, firstName: 'John', lastName: 'Doe' }
      };
      const mockEnterprise = { domainName: 'example.com', isDeleted: false };

      mockModels.CraneRequest.findOne.mockResolvedValue(mockExistingCrane);
      mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);
      mockModels.Project.getProjectAndSettings.mockResolvedValue(mockProject);
      mockModels.Member.getBy.mockResolvedValue(mockMember);
      mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);
      mockModels.Member.count.mockResolvedValue(1);
      mockModels.Equipments.count.mockResolvedValue(1);
      mockModels.DeliverDefineWork.count.mockResolvedValue(1);
      mockModels.Company.count.mockResolvedValue(1);
      mockModels.CraneRequest.update.mockResolvedValue([1]);

      const done = jest.fn();
      await craneRequestService.editCraneRequest(inputData, done);

      expect(done).toHaveBeenCalled();
    });
  });

  describe('Utility Methods', () => {
    describe('prepareCraneRequestInput', () => {
      it('should prepare crane request input successfully', async () => {
        const inputData = {
          body: {
            TimeZoneId: 1,
            ProjectId: 1
          }
        };

        const mockTimeZone = { id: 1, timezone: 'America/New_York' };
        const mockProject = {
          id: 1,
          ProjectSettings: {
            isAutoApprovalEnabled: false
          }
        };

        mockModels.TimeZone.findOne.mockResolvedValue(mockTimeZone);
        mockModels.Project.getProjectAndSettings.mockResolvedValue(mockProject);

        const result = await craneRequestService.prepareCraneRequestInput(inputData);

        expect(result).toEqual({
          craneRequestDetail: inputData.body,
          eventTimeZone: mockTimeZone,
          projectDetails: mockProject
        });
      });

      it('should handle invalid timezone in prepareCraneRequestInput', async () => {
        const inputData = {
          body: {
            TimeZoneId: 999,
            ProjectId: 1
          }
        };

        mockModels.TimeZone.findOne.mockResolvedValue(null);

        const result = await craneRequestService.prepareCraneRequestInput(inputData);

        expect(result).toEqual({
          error: true,
          message: 'Provide a valid timezone'
        });
      });
    });

    describe('getNextCraneRequestId', () => {
      it('should generate next crane request ID', async () => {
        mockModels.CraneRequest.findOne.mockResolvedValue({ CraneRequestId: 5 });

        const result = await craneRequestService.getNextCraneRequestId(1);

        expect(typeof result).toBe('number');
        expect(result).toBeGreaterThan(5);
      });

      it('should handle no existing crane requests', async () => {
        mockModels.CraneRequest.findOne.mockResolvedValue(null);

        const result = await craneRequestService.getNextCraneRequestId(1);

        expect(result).toBe(1);
      });
    });
  });
});