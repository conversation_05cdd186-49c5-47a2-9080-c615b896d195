const { Enterprise, Sequelize } = require('../models');
let { CraneRequestHistory, CraneRequest, User } = require('../models');
const helper = require('../helpers/domainHelper');
const { Op } = Sequelize;
let publicUser;
let publicMember;

const commentService = {
  async getCraneRequestHistories(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await this.checkCraneRequestExistence(inputData);
      if (exist) {
        const historyList = await this.fetchCraneRequestHistories(exist.id, inputData);
        done(historyList, false);
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async checkCraneRequestExistence(inputData) {
    return await CraneRequest.findOne({
      where: {
        CraneRequestId: inputData.params.CraneRequestId,
        ProjectId: +inputData.params.ProjectId,
      },
    });
  },

  async fetchCraneRequestHistories(craneRequestId, inputData) {
    return await CraneRequestHistory.findAll({
      include: [
        {
          association: 'Member',
          include: [
            { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
          ],
        },
      ],
      where: {
        CraneRequestId: craneRequestId,
        ProjectId: +inputData.params.ProjectId,
      },
      order: [['id', 'DESC']],
    });
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const ParentCompanyId = this.getParentCompanyId(inputData);
    let domainEnterpriseValue = await this.getDomainEnterprise(domainName);

    if (!domainEnterpriseValue) domainName = '';

    if (!domainName) {
      const email = inputData.user.email;
      const userData = await this.getUserData(email);
      const enterpriseValue = await this.determineEnterpriseValue(userData, ParentCompanyId);

      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
        await this.updateIncomeUser(inputData, userData.email);
      }
    }

    const modelObj = await helper.getDynamicModel(domainName);
    this.setModels(modelObj);

    return true;
  },

  async getDomainEnterprise(domainName) {
    if (!domainName) return null;
    return await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });
  },

  getParentCompanyId(inputData) {
    return inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
  },

  async getUserData(email) {
    if (!email) return null;
    return await publicUser.findOne({ where: { email } });
  },

  async determineEnterpriseValue(userData, ParentCompanyId) {
    if (!userData) return this.checkEnterpriseByParentCompany(ParentCompanyId);

    const memberData = await this.getMemberData(userData.id);
    if (!memberData) return this.checkEnterpriseByParentCompany(ParentCompanyId);

    return memberData.isAccount
      ? this.fetchEnterprise({
          id: memberData.EnterpriseId,
          status: 'completed',
        })
      : this.checkEnterpriseByParentCompany(ParentCompanyId);
  },

  async getMemberData(userId) {
    return await publicMember.findOne({
      where: { UserId: userId, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });
  },

  async checkEnterpriseByParentCompany(ParentCompanyId) {
    return await this.fetchEnterprise({
      ParentCompanyId,
      status: 'completed',
    });
  },

  async fetchEnterprise(whereClause) {
    return await Enterprise.findOne({ where: whereClause });
  },

  async updateIncomeUser(inputData, email) {
    const newUser = await User.findOne({ where: { email } });
    inputData.user = newUser;
  },

  setModels(modelObj) {
    CraneRequestHistory = modelObj.CraneRequestHistory;
    CraneRequest = modelObj.CraneRequest;
    User = modelObj.User;
  },
};

module.exports = commentService;