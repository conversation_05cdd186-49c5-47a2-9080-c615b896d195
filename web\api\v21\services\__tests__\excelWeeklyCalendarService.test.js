const excelWeeklyCalendarService = require('../excelWeeklyCalendarService');
const moment = require('moment');

// Mock moment to ensure consistent testing
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((date) => actualMoment(date));

  // Copy all moment methods
  Object.setPrototypeOf(mockMoment, actualMoment);
  Object.assign(mockMoment, actualMoment);

  return mockMoment;
});

describe('ExcelWeeklyCalendarService', () => {
  let mockWorksheet;
  let mockWorkbook;
  let mockCell;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Excel worksheet and workbook
    mockCell = {
      value: null
    };

    mockWorksheet = {
      addRow: jest.fn(),
      getCell: jest.fn().mockReturnValue(mockCell),
      getRow: jest.fn().mockReturnValue({ values: [] }),
      columns: []
    };

    mockWorkbook = {
      addWorksheet: jest.fn().mockReturnValue(mockWorksheet)
    };
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(excelWeeklyCalendarService).toBeDefined();
    });

    it('should be an object with expected methods', () => {
      expect(typeof excelWeeklyCalendarService).toBe('object');
      expect(typeof excelWeeklyCalendarService.filterDataByDateRange).toBe('function');
      expect(typeof excelWeeklyCalendarService.formatDateTime).toBe('function');
      expect(typeof excelWeeklyCalendarService.formatDate).toBe('function');
      expect(typeof excelWeeklyCalendarService.formatApproverName).toBe('function');
      expect(typeof excelWeeklyCalendarService.formatJoinedValues).toBe('function');
      expect(typeof excelWeeklyCalendarService.weeklyCalendarReport).toBe('function');
    });
  });

  describe('filterDataByDateRange', () => {
    it('should filter data within date range', () => {
      const testData = [
        { deliveryStart: '2024-01-15T10:00:00Z' },
        { deliveryStart: '2024-01-20T10:00:00Z' },
        { deliveryStart: '2024-01-25T10:00:00Z' }
      ];
      const startRange = moment('2024-01-18');
      const endRange = moment('2024-01-22');
      const timezoneoffset = 0;
      const dateField = 'deliveryStart';

      const result = excelWeeklyCalendarService.filterDataByDateRange(
        testData, startRange, endRange, timezoneoffset, dateField
      );

      expect(result).toHaveLength(1);
      expect(result[0].deliveryStart).toBe('2024-01-20T10:00:00Z');
    });

    it('should handle empty data array', () => {
      const result = excelWeeklyCalendarService.filterDataByDateRange(
        [], moment('2024-01-01'), moment('2024-01-31'), 0, 'dateField'
      );
      expect(result).toEqual([]);
    });

    it('should handle timezone offset', () => {
      const testData = [
        { deliveryStart: '2024-01-15T10:00:00Z' }
      ];
      const startRange = moment('2024-01-15T09:00:00Z');
      const endRange = moment('2024-01-15T11:00:00Z');
      const timezoneoffset = 60; // 1 hour offset
      const dateField = 'deliveryStart';

      const result = excelWeeklyCalendarService.filterDataByDateRange(
        testData, startRange, endRange, timezoneoffset, dateField
      );

      expect(result).toHaveLength(1);
    });
  });

  describe('formatDateTime', () => {
    it('should format date and time correctly', () => {
      const date = '2024-01-15T14:30:00Z';
      const timezoneoffset = 0;

      const result = excelWeeklyCalendarService.formatDateTime(date, timezoneoffset);

      expect(result).toMatch(/\d{2}:\d{2} [AP]M/);
    });

    it('should handle timezone offset in formatDateTime', () => {
      const date = '2024-01-15T14:30:00Z';
      const timezoneoffset = 60; // 1 hour

      const result = excelWeeklyCalendarService.formatDateTime(date, timezoneoffset);

      expect(result).toMatch(/\d{2}:\d{2} [AP]M/);
    });

    it('should handle string timezone offset', () => {
      const date = '2024-01-15T14:30:00Z';
      const timezoneoffset = '60';

      const result = excelWeeklyCalendarService.formatDateTime(date, timezoneoffset);

      expect(result).toMatch(/\d{2}:\d{2} [AP]M/);
    });
  });

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = '2024-01-15T14:30:00Z';
      const timezoneoffset = 0;

      const result = excelWeeklyCalendarService.formatDate(date, timezoneoffset);

      expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
    });

    it('should handle timezone offset in formatDate', () => {
      const date = '2024-01-15T14:30:00Z';
      const timezoneoffset = 120; // 2 hours

      const result = excelWeeklyCalendarService.formatDate(date, timezoneoffset);

      expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
    });
  });

  describe('formatApproverName', () => {
    it('should format approver name when User exists', () => {
      const approverDetails = {
        User: {
          firstName: 'John',
          lastName: 'Doe'
        }
      };

      const result = excelWeeklyCalendarService.formatApproverName(approverDetails);

      expect(result).toBe('John Doe');
    });

    it('should return dash when User is null', () => {
      const approverDetails = {
        User: null
      };

      const result = excelWeeklyCalendarService.formatApproverName(approverDetails);

      expect(result).toBe('-');
    });

    it('should return dash when User is undefined', () => {
      const approverDetails = {};

      const result = excelWeeklyCalendarService.formatApproverName(approverDetails);

      expect(result).toBe('-');
    });

    it('should return dash when approverDetails is null', () => {
      const result = excelWeeklyCalendarService.formatApproverName(null);

      expect(result).toBe('-');
    });

    it('should return dash when approverDetails is undefined', () => {
      const result = excelWeeklyCalendarService.formatApproverName(undefined);

      expect(result).toBe('-');
    });
  });

  describe('formatJoinedValues', () => {
    it('should return dash when details is null', () => {
      const result = excelWeeklyCalendarService.formatJoinedValues(null, 'Equipment');
      expect(result).toBe('-');
    });

    it('should return dash when details is undefined', () => {
      const result = excelWeeklyCalendarService.formatJoinedValues(undefined, 'Equipment');
      expect(result).toBe('-');
    });

    it('should return dash when details is empty array', () => {
      const result = excelWeeklyCalendarService.formatJoinedValues([], 'Equipment');
      expect(result).toBe('-');
    });

    it('should format joined values with name property', () => {
      const details = [
        { Equipment: { name: 'Crane A' } },
        { Equipment: { name: 'Crane B' } }
      ];
      const result = excelWeeklyCalendarService.formatJoinedValues(details, 'Equipment');
      expect(result).toBe('Crane A, Crane B');
    });

    it('should format joined values with DFOW property', () => {
      const details = [
        { DeliverDefineWork: { DFOW: 'Work A' } },
        { DeliverDefineWork: { DFOW: 'Work B' } }
      ];
      const result = excelWeeklyCalendarService.formatJoinedValues(details, 'DeliverDefineWork');
      expect(result).toBe('Work A, Work B');
    });

    it('should format joined values with companyName property', () => {
      const details = [
        { Company: { companyName: 'Company A' } },
        { Company: { companyName: 'Company B' } }
      ];
      const result = excelWeeklyCalendarService.formatJoinedValues(details, 'Company');
      expect(result).toBe('Company A, Company B');
    });

    it('should handle mixed properties and fallback to dash', () => {
      const details = [
        { Equipment: { name: 'Crane A' } },
        { Equipment: null },
        { Equipment: { name: 'Crane C' } }
      ];
      const result = excelWeeklyCalendarService.formatJoinedValues(details, 'Equipment');
      expect(result).toBe('Crane A, -, Crane C');
    });

    it('should handle details with missing key property', () => {
      const details = [
        { SomeOtherKey: { name: 'Test' } },
        { Equipment: null }
      ];
      const result = excelWeeklyCalendarService.formatJoinedValues(details, 'Equipment');
      expect(result).toBe('-, -');
    });
  });

  describe('processRow', () => {
    let mockData;
    let cellRange;
    let rowValues;

    beforeEach(() => {
      mockData = {
        description: 'Test Description',
        deliveryStart: '2024-01-15T10:00:00Z',
        deliveryEnd: '2024-01-15T12:00:00Z',
        status: 'Approved',
        approverDetails: {
          User: { firstName: 'John', lastName: 'Doe' }
        },
        equipmentDetails: [
          { Equipment: { name: 'Crane A' } }
        ],
        defineWorkDetails: [
          { DeliverDefineWork: { DFOW: 'Work A' } }
        ],
        location: {
          locationPath: 'Location A'
        }
      };

      cellRange = {
        0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F', 6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K'
      };

      rowValues = [
        'Description', 'Time', 'Date', 'Status', 'Approved By',
        'Equipment', 'Definable Feature of Work', 'Location'
      ];
    });

    it('should process row with all data', () => {
      excelWeeklyCalendarService.processRow(
        mockWorksheet, 2, mockData, cellRange, rowValues, 0, 'deliveryStart'
      );

      expect(mockWorksheet.getCell).toHaveBeenCalledWith('A2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('B2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('C2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('D2');
      expect(mockWorksheet.getCell).toHaveBeenCalledWith('E2');
    });

    it('should handle data with equipmentDetails', () => {
      excelWeeklyCalendarService.processRow(
        mockWorksheet, 2, mockData, cellRange, rowValues, 0, 'deliveryStart'
      );

      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should handle data with defineWorkDetails', () => {
      excelWeeklyCalendarService.processRow(
        mockWorksheet, 2, mockData, cellRange, rowValues, 0, 'deliveryStart'
      );

      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should handle data with location', () => {
      excelWeeklyCalendarService.processRow(
        mockWorksheet, 2, mockData, cellRange, rowValues, 0, 'deliveryStart'
      );

      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });

    it('should handle data without optional fields', () => {
      const minimalData = {
        description: 'Test Description',
        deliveryStart: '2024-01-15T10:00:00Z',
        deliveryEnd: '2024-01-15T12:00:00Z',
        status: 'Approved',
        approverDetails: null
      };

      excelWeeklyCalendarService.processRow(
        mockWorksheet, 2, minimalData, cellRange, rowValues, 0, 'deliveryStart'
      );

      expect(mockWorksheet.getCell).toHaveBeenCalled();
    });
  });

  describe('Helper formatting functions', () => {
    describe('formatDeliveryTime', () => {
      it('should format delivery time correctly', () => {
        const delivery = {
          deliveryStart: '2024-01-15T10:00:00Z',
          deliveryEnd: '2024-01-15T12:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatDeliveryTime(delivery, 0);
        expect(result).toMatch(/\d{2}:\d{2} [AP]M - \d{2}:\d{2} [AP]M/);
      });

      it('should handle timezone offset in delivery time', () => {
        const delivery = {
          deliveryStart: '2024-01-15T10:00:00Z',
          deliveryEnd: '2024-01-15T12:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatDeliveryTime(delivery, 60);
        expect(result).toMatch(/\d{2}:\d{2} [AP]M - \d{2}:\d{2} [AP]M/);
      });
    });

    describe('formatDeliveryDate', () => {
      it('should format delivery date correctly', () => {
        const delivery = {
          deliveryStart: '2024-01-15T10:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatDeliveryDate(delivery, 0);
        expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
      });
    });

    describe('formatCraneTime', () => {
      it('should format crane time correctly', () => {
        const crane = {
          craneDeliveryStart: '2024-01-15T10:00:00Z',
          craneDeliveryEnd: '2024-01-15T12:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatCraneTime(crane, 0);
        expect(result).toMatch(/\d{2}:\d{2} [AP]M - \d{2}:\d{2} [AP]M/);
      });
    });

    describe('formatCraneDate', () => {
      it('should format crane date correctly', () => {
        const crane = {
          craneDeliveryStart: '2024-01-15T10:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatCraneDate(crane, 0);
        expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
      });
    });

    describe('formatConcreteTime', () => {
      it('should format concrete time correctly', () => {
        const concrete = {
          concretePlacementStart: '2024-01-15T10:00:00Z',
          concretePlacementEnd: '2024-01-15T12:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatConcreteTime(concrete, 0);
        expect(result).toMatch(/\d{2}:\d{2} [AP]M - \d{2}:\d{2} [AP]M/);
      });
    });

    describe('formatConcreteDate', () => {
      it('should format concrete date correctly', () => {
        const concrete = {
          concretePlacementStart: '2024-01-15T10:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatConcreteDate(concrete, 0);
        expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
      });
    });

    describe('formatCalendarTime', () => {
      it('should format calendar time correctly', () => {
        const calendar = {
          fromDate: '2024-01-15T10:00:00Z',
          toDate: '2024-01-15T12:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatCalendarTime(calendar, 0);
        expect(result).toMatch(/\d{2}:\d{2} [AP]M - \d{2}:\d{2} [AP]M/);
      });
    });

    describe('formatCalendarDate', () => {
      it('should format calendar date correctly', () => {
        const calendar = {
          fromDate: '2024-01-15T10:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatCalendarDate(calendar, 0);
        expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
      });
    });

    describe('formatInspectionTime', () => {
      it('should format inspection time correctly', () => {
        const inspection = {
          inspectionStart: '2024-01-15T10:00:00Z',
          inspectionEnd: '2024-01-15T12:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatInspectionTime(inspection, 0);
        expect(result).toMatch(/\d{2}:\d{2} [AP]M - \d{2}:\d{2} [AP]M/);
      });
    });

    describe('formatInspectionDate', () => {
      it('should format inspection date correctly', () => {
        const inspection = {
          inspectionStart: '2024-01-15T10:00:00Z'
        };
        const result = excelWeeklyCalendarService.formatInspectionDate(inspection, 0);
        expect(result).toMatch(/\d{2}\/\d{2}\/\d{4}/);
      });
    });
  });

  describe('Detail formatting functions', () => {
    describe('formatEquipmentDetails', () => {
      it('should return dash when equipmentDetails is null', () => {
        const result = excelWeeklyCalendarService.formatEquipmentDetails(null);
        expect(result).toBe('-');
      });

      it('should return dash when equipmentDetails is undefined', () => {
        const result = excelWeeklyCalendarService.formatEquipmentDetails(undefined);
        expect(result).toBe('-');
      });

      it('should return dash when equipmentDetails is empty array', () => {
        const result = excelWeeklyCalendarService.formatEquipmentDetails([]);
        expect(result).toBe('-');
      });

      it('should format equipment details correctly', () => {
        const equipmentDetails = [
          { Equipment: { equipmentName: 'Crane A' } },
          { Equipment: { equipmentName: 'Crane B' } }
        ];
        const result = excelWeeklyCalendarService.formatEquipmentDetails(equipmentDetails);
        expect(result).toBe('Crane A, Crane B');
      });

      it('should handle missing equipment names', () => {
        const equipmentDetails = [
          { Equipment: { equipmentName: 'Crane A' } },
          { Equipment: null },
          { Equipment: { equipmentName: 'Crane C' } }
        ];
        const result = excelWeeklyCalendarService.formatEquipmentDetails(equipmentDetails);
        expect(result).toBe('Crane A, -, Crane C');
      });
    });

    describe('formatDfowDetails', () => {
      it('should return dash when defineWorkDetails is null', () => {
        const result = excelWeeklyCalendarService.formatDfowDetails(null);
        expect(result).toBe('-');
      });

      it('should return dash when defineWorkDetails is empty array', () => {
        const result = excelWeeklyCalendarService.formatDfowDetails([]);
        expect(result).toBe('-');
      });

      it('should format DFOW details correctly', () => {
        const defineWorkDetails = [
          { DeliverDefineWork: { DFOW: 'Work A' } },
          { DeliverDefineWork: { DFOW: 'Work B' } }
        ];
        const result = excelWeeklyCalendarService.formatDfowDetails(defineWorkDetails);
        expect(result).toBe('Work A, Work B');
      });

      it('should handle missing DFOW values', () => {
        const defineWorkDetails = [
          { DeliverDefineWork: { DFOW: 'Work A' } },
          { DeliverDefineWork: null },
          { DeliverDefineWork: { DFOW: 'Work C' } }
        ];
        const result = excelWeeklyCalendarService.formatDfowDetails(defineWorkDetails);
        expect(result).toBe('Work A, -, Work C');
      });
    });

    describe('formatCompanyDetails', () => {
      it('should return dash when companyDetails is null', () => {
        const result = excelWeeklyCalendarService.formatCompanyDetails(null);
        expect(result).toBe('-');
      });

      it('should return dash when companyDetails is empty array', () => {
        const result = excelWeeklyCalendarService.formatCompanyDetails([]);
        expect(result).toBe('-');
      });

      it('should format company details correctly', () => {
        const companyDetails = [
          { Company: { companyName: 'Company A' } },
          { Company: { companyName: 'Company B' } }
        ];
        const result = excelWeeklyCalendarService.formatCompanyDetails(companyDetails);
        expect(result).toBe('Company A, Company B');
      });

      it('should handle missing company names', () => {
        const companyDetails = [
          { Company: { companyName: 'Company A' } },
          { Company: null },
          { Company: { companyName: 'Company C' } }
        ];
        const result = excelWeeklyCalendarService.formatCompanyDetails(companyDetails);
        expect(result).toBe('Company A, -, Company C');
      });
    });

    describe('formatMemberDetails', () => {
      it('should return dash when memberDetails is null', () => {
        const result = excelWeeklyCalendarService.formatMemberDetails(null);
        expect(result).toBe('-');
      });

      it('should return dash when memberDetails is empty array', () => {
        const result = excelWeeklyCalendarService.formatMemberDetails([]);
        expect(result).toBe('-');
      });

      it('should format member details correctly', () => {
        const memberDetails = [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ];
        const result = excelWeeklyCalendarService.formatMemberDetails(memberDetails);
        expect(result).toBe('John Doe, Jane Smith');
      });

      it('should handle missing member user data', () => {
        const memberDetails = [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: null },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ];
        const result = excelWeeklyCalendarService.formatMemberDetails(memberDetails);
        expect(result).toBe('John Doe, -, Jane Smith');
      });

      it('should handle member without User', () => {
        const memberDetails = [
          { Member: { User: null } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ];
        const result = excelWeeklyCalendarService.formatMemberDetails(memberDetails);
        expect(result).toBe('-, Jane Smith');
      });
    });
  });

  describe('Crane specific formatting functions', () => {
    describe('formatPickingFrom', () => {
      it('should return pickUpLocation for craneRequest', () => {
        const crane = {
          requestType: 'craneRequest',
          pickUpLocation: 'Location A'
        };
        const result = excelWeeklyCalendarService.formatPickingFrom(crane);
        expect(result).toBe('Location A');
      });

      it('should return dash for non-craneRequest', () => {
        const crane = {
          requestType: 'deliveryRequest',
          pickUpLocation: 'Location A'
        };
        const result = excelWeeklyCalendarService.formatPickingFrom(crane);
        expect(result).toBe('-');
      });

      it('should return dash when requestType is undefined', () => {
        const crane = {
          pickUpLocation: 'Location A'
        };
        const result = excelWeeklyCalendarService.formatPickingFrom(crane);
        expect(result).toBe('-');
      });
    });

    describe('formatPickingTo', () => {
      it('should return dropOffLocation for craneRequest', () => {
        const crane = {
          requestType: 'craneRequest',
          dropOffLocation: 'Location B'
        };
        const result = excelWeeklyCalendarService.formatPickingTo(crane);
        expect(result).toBe('Location B');
      });

      it('should return dash for non-craneRequest', () => {
        const crane = {
          requestType: 'deliveryRequest',
          dropOffLocation: 'Location B'
        };
        const result = excelWeeklyCalendarService.formatPickingTo(crane);
        expect(result).toBe('-');
      });
    });
  });

  describe('formatConcreteSupplier', () => {
    it('should return dash when supplierDetails is null', () => {
      const result = excelWeeklyCalendarService.formatConcreteSupplier(null);
      expect(result).toBe('-');
    });

    it('should return dash when supplierDetails is empty array', () => {
      const result = excelWeeklyCalendarService.formatConcreteSupplier([]);
      expect(result).toBe('-');
    });

    it('should format concrete supplier details correctly', () => {
      const supplierDetails = [
        { Company: { companyName: 'Supplier A' } },
        { Company: { companyName: 'Supplier B' } }
      ];
      const result = excelWeeklyCalendarService.formatConcreteSupplier(supplierDetails);
      expect(result).toBe('Supplier A, Supplier B');
    });

    it('should handle missing supplier company names', () => {
      const supplierDetails = [
        { Company: { companyName: 'Supplier A' } },
        { Company: null },
        { Company: { companyName: 'Supplier C' } }
      ];
      const result = excelWeeklyCalendarService.formatConcreteSupplier(supplierDetails);
      expect(result).toBe('Supplier A, -, Supplier C');
    });
  });

  describe('Process data functions', () => {
    describe('processDeliveryData', () => {
      it('should return early when deliveryArray is null', () => {
        excelWeeklyCalendarService.processDeliveryData(null, mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should return early when deliveryArray is empty', () => {
        excelWeeklyCalendarService.processDeliveryData([], mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should process delivery data correctly', () => {
        const deliveryArray = [{
          description: 'Delivery 1',
          deliveryStart: '2024-01-15T10:00:00Z',
          deliveryEnd: '2024-01-15T12:00:00Z',
          status: 'Approved',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          equipmentDetails: [{ Equipment: { equipmentName: 'Crane A' } }],
          defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Work A' } }],
          gateDetails: [{ Gate: { gateName: 'Gate A' } }],
          companyDetails: [{ Company: { companyName: 'Company A' } }],
          memberDetails: [{ Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }],
          location: { locationPath: 'Location A' }
        }];

        const rowValues = [
          'Description', 'Time', 'Date', 'Status', 'Approved By',
          'Equipment', 'Definable Feature of Work', 'Gate',
          'Responsible Company', 'Responsible Person', 'Location'
        ];
        const cellRange = {
          0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F',
          6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K'
        };

        excelWeeklyCalendarService.processDeliveryData(
          deliveryArray, mockWorksheet, rowValues, cellRange, 0
        );

        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
        expect(mockWorksheet.getCell).toHaveBeenCalled();
      });
    });

    describe('processCraneData', () => {
      it('should return early when craneArray is null', () => {
        excelWeeklyCalendarService.processCraneData(null, mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should return early when craneArray is empty', () => {
        excelWeeklyCalendarService.processCraneData([], mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should process crane data correctly', () => {
        const craneArray = [{
          description: 'Crane 1',
          craneDeliveryStart: '2024-01-15T10:00:00Z',
          craneDeliveryEnd: '2024-01-15T12:00:00Z',
          status: 'Approved',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          equipmentDetails: [{ Equipment: { equipmentName: 'Crane A' } }],
          defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Work A' } }],
          companyDetails: [{ Company: { companyName: 'Company A' } }],
          memberDetails: [{ Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }],
          requestType: 'craneRequest',
          pickUpLocation: 'Pick Location',
          dropOffLocation: 'Drop Location',
          location: { locationPath: 'Location A' }
        }];

        const rowValues = [
          'Description', 'Time', 'Date', 'Status', 'Approved By',
          'Equipment', 'Definable Feature of Work', 'Responsible Company',
          'Responsible Person', 'Picking From', 'Picking To', 'Location'
        ];
        const cellRange = {
          0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F',
          6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K', 11: 'L'
        };

        excelWeeklyCalendarService.processCraneData(
          craneArray, mockWorksheet, rowValues, cellRange, 0
        );

        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
        expect(mockWorksheet.getCell).toHaveBeenCalled();
      });
    });

    describe('processConcreteData', () => {
      it('should return early when concreteArray is null', () => {
        excelWeeklyCalendarService.processConcreteData(null, mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should return early when concreteArray is empty', () => {
        excelWeeklyCalendarService.processConcreteData([], mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should process concrete data correctly', () => {
        const concreteArray = [{
          description: 'Concrete 1',
          concretePlacementStart: '2024-01-15T10:00:00Z',
          concretePlacementEnd: '2024-01-15T12:00:00Z',
          status: 'Approved',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          concreteSupplierDetails: [{ Company: { companyName: 'Supplier A' } }],
          concreteOrderNumber: 'ORDER123',
          slump: '5',
          truckSpacingHours: '2',
          primerForPump: 'Yes',
          memberDetails: [{ Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }],
          concreteQuantityOrdered: '100',
          location: { locationPath: 'Location A' }
        }];

        const rowValues = [
          'Description', 'Time', 'Date', 'Status', 'Approved By',
          'Concrete Supplier', 'Order Number', 'Slump', 'Truck Spacing',
          'Primer Ordered', 'Responsible Person', 'Quantity Ordered', 'Location'
        ];
        const cellRange = {
          0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F',
          6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K', 11: 'L', 12: 'M'
        };

        excelWeeklyCalendarService.processConcreteData(
          concreteArray, mockWorksheet, rowValues, cellRange, 0
        );

        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
        expect(mockWorksheet.getCell).toHaveBeenCalled();
      });
    });

    describe('processCalendarData', () => {
      it('should return early when calendarEvents is null', () => {
        excelWeeklyCalendarService.processCalendarData(null, mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should return early when calendarEvents is empty', () => {
        excelWeeklyCalendarService.processCalendarData([], mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should process calendar data correctly', () => {
        const calendarEvents = [{
          description: 'Event 1',
          fromDate: '2024-01-15T10:00:00Z',
          toDate: '2024-01-15T12:00:00Z'
        }];

        const rowValues = ['Description', 'Time', 'Date'];
        const cellRange = { 0: 'A', 1: 'B', 2: 'C' };

        excelWeeklyCalendarService.processCalendarData(
          calendarEvents, mockWorksheet, rowValues, cellRange, 0
        );

        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
        expect(mockWorksheet.getCell).toHaveBeenCalled();
      });
    });

    describe('processInspectionData', () => {
      it('should return early when inspectionArray is null', () => {
        excelWeeklyCalendarService.processInspectionData(null, mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should return early when inspectionArray is empty', () => {
        excelWeeklyCalendarService.processInspectionData([], mockWorksheet, [], {}, 0);
        expect(mockWorksheet.addRow).not.toHaveBeenCalled();
      });

      it('should process inspection data correctly', () => {
        const inspectionArray = [{
          description: 'Inspection 1',
          inspectionStart: '2024-01-15T10:00:00Z',
          inspectionEnd: '2024-01-15T12:00:00Z',
          status: 'Delivered',
          inspectionStatus: 'Passed',
          inspectionType: 'Quality',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          equipmentDetails: [{ Equipment: { equipmentName: 'Equipment A' } }],
          defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Work A' } }],
          gateDetails: [{ Gate: { gateName: 'Gate A' } }],
          companyDetails: [{ Company: { companyName: 'Company A' } }],
          memberDetails: [{ Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }],
          location: { locationPath: 'Location A' }
        }];

        const rowValues = [
          'Description', 'Time', 'Date', 'Status', 'Inspection Status',
          'Inspection Type', 'Approved By', 'Equipment', 'Definable Feature of Work',
          'Gate', 'Responsible Company', 'Responsible Person', 'Location'
        ];
        const cellRange = {
          0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F',
          6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K', 11: 'L', 12: 'M'
        };

        excelWeeklyCalendarService.processInspectionData(
          inspectionArray, mockWorksheet, rowValues, cellRange, 0
        );

        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
        expect(mockWorksheet.getCell).toHaveBeenCalled();
      });

      it('should handle status conversion from Delivered to Completed', () => {
        const inspectionArray = [{
          description: 'Inspection 1',
          inspectionStart: '2024-01-15T10:00:00Z',
          inspectionEnd: '2024-01-15T12:00:00Z',
          status: 'Delivered',
          inspectionStatus: 'Passed',
          inspectionType: 'Quality',
          approverDetails: { User: { firstName: 'John', lastName: 'Doe' } },
          equipmentDetails: [],
          defineWorkDetails: [],
          gateDetails: [],
          companyDetails: [],
          memberDetails: [],
          location: { locationPath: 'Location A' }
        }];

        const rowValues = [
          'Description', 'Time', 'Date', 'Status', 'Inspection Status',
          'Inspection Type', 'Approved By', 'Equipment', 'Definable Feature of Work',
          'Gate', 'Responsible Company', 'Responsible Person', 'Location'
        ];
        const cellRange = {
          0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F',
          6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K', 11: 'L', 12: 'M'
        };

        excelWeeklyCalendarService.processInspectionData(
          inspectionArray, mockWorksheet, rowValues, cellRange, 0
        );

        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1);
        expect(mockWorksheet.getCell).toHaveBeenCalled();
      });
    });
  });

  describe('weeklyCalendarReport', () => {
    let mockWorkbook;

    beforeEach(() => {
      mockWorkbook = {
        addWorksheet: jest.fn().mockReturnValue(mockWorksheet)
      };
    });

    it('should create all required worksheets', async () => {
      const finalArray = ['Delivery', 'Crane', 'Concrete', 'Calendar', 'Inspection'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Delivery Report');
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Crane Report');
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Concrete Report');
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Calendar Events');
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Inspection Report');
      expect(result).toBe(mockWorkbook);
    });

    it('should handle empty responseData', async () => {
      const finalArray = ['Delivery'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle null responseData', async () => {
      const finalArray = ['Delivery'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = null;
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should process delivery requests correctly', async () => {
      const finalArray = ['Delivery'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'deliveryRequest',
        deliveryStart: '2024-01-16T10:00:00Z',
        deliveryEnd: '2024-01-16T12:00:00Z',
        description: 'Test Delivery',
        status: 'Approved',
        approverDetails: { User: { firstName: 'John', lastName: 'Doe' } }
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should process crane requests when Crane is in finalArray', async () => {
      const finalArray = ['Crane'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'craneRequest',
        craneDeliveryStart: '2024-01-16T10:00:00Z',
        craneDeliveryEnd: '2024-01-16T12:00:00Z',
        description: 'Test Crane',
        status: 'Approved',
        approverDetails: { User: { firstName: 'John', lastName: 'Doe' } }
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should not process crane requests when Crane is not in finalArray', async () => {
      const finalArray = ['Delivery'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'craneRequest',
        craneDeliveryStart: '2024-01-16T10:00:00Z',
        craneDeliveryEnd: '2024-01-16T12:00:00Z',
        description: 'Test Crane',
        status: 'Approved'
      }];
      const timezoneoffset = 0;

      await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      // Should not process crane data since 'Crane' is not in finalArray
      expect(mockWorksheet.addRow).not.toHaveBeenCalled();
    });

    it('should process concrete requests correctly', async () => {
      const finalArray = ['Concrete'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'concreteRequest',
        concretePlacementStart: '2024-01-16T10:00:00Z',
        concretePlacementEnd: '2024-01-16T12:00:00Z',
        description: 'Test Concrete',
        status: 'Approved'
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should process calendar events correctly', async () => {
      const finalArray = ['Calendar'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'calendarEvent',
        fromDate: '2024-01-16T10:00:00Z',
        toDate: '2024-01-16T12:00:00Z',
        description: 'Test Event'
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should process inspection requests correctly', async () => {
      const finalArray = ['Inspection'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'inspectionRequest',
        inspectionStart: '2024-01-16T10:00:00Z',
        inspectionEnd: '2024-01-16T12:00:00Z',
        description: 'Test Inspection',
        status: 'Approved'
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should handle chosenDateRangeFilter false and adjust timezone', async () => {
      const finalArray = ['Delivery'];
      const weekStartDate = '2024-01-15 00:00:00';
      const weekEndDate = '2024-01-21 23:59:59';
      const chosenDateRangeFilter = false;
      const responseData = [{
        requestType: 'deliveryRequest',
        deliveryStart: '2024-01-16T10:00:00Z',
        deliveryEnd: '2024-01-16T12:00:00Z',
        description: 'Test Delivery',
        status: 'Approved'
      }];
      const timezoneoffset = 60; // 1 hour

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle deliveryRequestWithCrane type', async () => {
      const finalArray = ['Delivery', 'Crane'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'deliveryRequestWithCrane',
        deliveryStart: '2024-01-16T10:00:00Z',
        deliveryEnd: '2024-01-16T12:00:00Z',
        craneDeliveryStart: '2024-01-16T10:00:00Z',
        craneDeliveryEnd: '2024-01-16T12:00:00Z',
        description: 'Test Delivery with Crane',
        status: 'Approved'
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should handle inspectionRequestWithCrane type', async () => {
      const finalArray = ['Inspection'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [{
        requestType: 'inspectionRequestWithCrane',
        inspectionStart: '2024-01-16T10:00:00Z',
        inspectionEnd: '2024-01-16T12:00:00Z',
        description: 'Test Inspection with Crane',
        status: 'Approved'
      }];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should handle multiple request types in responseData', async () => {
      const finalArray = ['Delivery', 'Crane', 'Concrete', 'Calendar', 'Inspection'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [
        {
          requestType: 'deliveryRequest',
          deliveryStart: '2024-01-16T10:00:00Z',
          deliveryEnd: '2024-01-16T12:00:00Z',
          description: 'Test Delivery',
          status: 'Approved'
        },
        {
          requestType: 'craneRequest',
          craneDeliveryStart: '2024-01-16T14:00:00Z',
          craneDeliveryEnd: '2024-01-16T16:00:00Z',
          description: 'Test Crane',
          status: 'Approved'
        },
        {
          requestType: 'concreteRequest',
          concretePlacementStart: '2024-01-17T08:00:00Z',
          concretePlacementEnd: '2024-01-17T10:00:00Z',
          description: 'Test Concrete',
          status: 'Approved'
        },
        {
          requestType: 'calendarEvent',
          fromDate: '2024-01-18T09:00:00Z',
          toDate: '2024-01-18T11:00:00Z',
          description: 'Test Event'
        },
        {
          requestType: 'inspectionRequest',
          inspectionStart: '2024-01-19T13:00:00Z',
          inspectionEnd: '2024-01-19T15:00:00Z',
          description: 'Test Inspection',
          status: 'Approved'
        }
      ];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(5);
      expect(result).toBe(mockWorkbook);
    });

    it('should filter out data outside date range', async () => {
      const finalArray = ['Delivery'];
      const weekStartDate = '2024-01-15';
      const weekEndDate = '2024-01-21';
      const chosenDateRangeFilter = true;
      const responseData = [
        {
          requestType: 'deliveryRequest',
          deliveryStart: '2024-01-10T10:00:00Z', // Outside range
          deliveryEnd: '2024-01-10T12:00:00Z',
          description: 'Test Delivery Outside Range',
          status: 'Approved'
        },
        {
          requestType: 'deliveryRequest',
          deliveryStart: '2024-01-16T10:00:00Z', // Inside range
          deliveryEnd: '2024-01-16T12:00:00Z',
          description: 'Test Delivery Inside Range',
          status: 'Approved'
        }
      ];
      const timezoneoffset = 0;

      const result = await excelWeeklyCalendarService.weeklyCalendarReport(
        finalArray, weekStartDate, weekEndDate, chosenDateRangeFilter,
        mockWorkbook, responseData, timezoneoffset
      );

      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(1); // Only one should be processed
      expect(result).toBe(mockWorkbook);
    });
  });
});
