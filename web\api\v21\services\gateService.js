const status = require('http-status');
const {
  Sequelize,
  Enterprise,
  DeliverGate,
  DeliveryRequest,
  Member,
  DeliverHistory,
} = require('../models');
let { Gates, Project, User } = require('../models');
const helper = require('../helpers/domainHelper');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const ApiError = require('../helpers/apiError');

const gateService = {
  async addGates(equData, done) {
    try {
      await this.getDynamicModel(equData);
      const userDetails = equData.user;
      const inputData = equData.body;
      inputData.createdBy = userDetails.id;
      const projectDetails = await Project.getProject({ id: inputData.ProjectId });
      if (projectDetails) {
        const nameExist = await Gates.getGates({
          gateName: inputData.gateName,
          isDeleted: false,
          ProjectId: inputData.ProjectId,
        });
        if (nameExist) {
          const err = new ApiError('Gate Name Already exist.', status.BAD_REQUEST);
          done(null, err);
        } else {
          const lastIdValue = await Gates.findOne({
            where: { ProjectId: inputData.ProjectId, isDeleted: false },
            order: [['gateAutoId', 'DESC']],
          });
          let id = 0;
          const newValue = JSON.parse(JSON.stringify(lastIdValue));
          if (newValue && newValue.gateAutoId !== null && newValue.gateAutoId !== undefined) {
            id = newValue.gateAutoId;
          }
          inputData.gateAutoId = id + 1;
          const newGate = await Gates.createGate(inputData);
          done(newGate, false);
        }
      } else {
        const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
        done(null, err);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async updateGates(equData, done) {
    try {
      await this.getDynamicModel(equData);
      const inputData = equData.body;
      const gateDetails = await Gates.findOne({
        where: Sequelize.and(
          {
            id: inputData.id,
          },
          { ProjectId: inputData.ProjectId },
        ),
      });
      if (gateDetails) {
        const projectDetails = await Project.getProject({ id: inputData.ProjectId });
        if (projectDetails) {
          const nameExist = await Gates.getGates({
            gateName: inputData.gateName,
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          });
          if (nameExist && nameExist.id !== inputData.id) {
            const err = new ApiError('Gate Name Already exist.', status.BAD_REQUEST);
            done(null, err);
          } else {
            delete inputData.ProjectId;
            const newGate = await Gates.updateInstance(inputData.id, inputData);
            done(newGate, false);
          }
        } else {
          done(null, { message: 'Project does not exist.' });
        }
      } else {
        done(null, { message: 'Gate id does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const domainName = await this.resolveDomainName(inputData);
    const modelObj = await helper.getDynamicModel(domainName);
    Gates = modelObj.Gates;
    Project = modelObj.Project;
    User = modelObj.User;

    if (domainName) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      if (newUser) {
        inputData.user = newUser;
      }
    }
    return inputData.body.ProjectId;
  },
  async resolveDomainName(inputData) {
    const { domainName } = inputData.user;
    if (domainName) {
      const domainEnterprise = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() }
      });
      if (domainEnterprise) {
        return domainName;
      }
    }
    return await this.resolveDomainFromParentCompany(inputData);
  },
  async resolveDomainFromParentCompany(inputData) {
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
    if (!ParentCompanyId || ParentCompanyId === 'undefined') {
      return '';
    }

    const userData = await publicUser.findOne({ where: { email: inputData.user.email } });
    if (!userData) {
      return await this.getDomainFromParentCompany(ParentCompanyId);
    }

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false }
    });
    if (!memberData) {
      return await this.getDomainFromParentCompany(ParentCompanyId);
    }

    if (memberData.isAccount) {
      const enterprise = await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' }
      });
      return enterprise ? enterprise.name.toLowerCase() : '';
    }

    return await this.getDomainFromParentCompany(ParentCompanyId);
  },
  async getDomainFromParentCompany(ParentCompanyId) {
    const enterprise = await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
    return enterprise ? enterprise.name.toLowerCase() : '';
  },
  async updateModelReferences(modelObj) {
    Gates = modelObj.Gates;
    Project = modelObj.Project;
    User = modelObj.User;
  },
  async updateUserIfNeeded(inputData, domainName) {
    if (domainName) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      if (newUser) {
        inputData.user = newUser;
      }
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async listGates(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        condition.isActive = true;
      }
      let searchCondition = {};
      if (incomeData.search) {
        const searchDefault = [
          {
            gateName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        gateAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const gateData = await Gates.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      if (incomeData.isFilter) {
        if (gateData.rows) {
          if (gateData.rows.length > 0) {
            gateData.rows.sort((a, b) =>
              a.gateName.toLowerCase() > b.gateName.toLowerCase() ? 1 : -1,
            );
          }
        } else if (gateData.length > 0) {
          gateData.sort((a, b) => (a.gateName.toLowerCase() > b.gateName.toLowerCase() ? 1 : -1));
        }
      }

      done(gateData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async deleteGates(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      const { id } = input.body;
      let getGates;
      if (reqData.isSelectAll) {
        getGates = await Gates.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false },
        });
      } else {
        getGates = await Gates.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false, id: { [Op.in]: id } },
        });
      }
      if (getGates && getGates.length > 0) {
        getGates.map(async (item, index) => {
          const isGateMapped = await DeliverGate.findOne({
            where: {
              GateId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          if (isGateMapped) {
            return done(null, {
              message: `${item.gateName} cannot be deleted. ${item.gateName} is mapped to submitted bookings`,
            });
          }
          await Gates.update(
            { isDeleted: true },
            {
              where: { id: +item.id, ProjectId: reqData.ProjectId, isDeleted: false },
            },
          );
          if (index === getGates.length - 1) {
            return done('success', false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async lastGate(inputData, done) {
    try {
      let data;
      const lastData = await Gates.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['gateAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.gateAutoId + 1;
      } else {
        data = 1;
      }
      done({ id: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async gatesForBulkUploadDeliveryRequest(req) {
    try {
      await this.getDynamicModel(req);
      const { params } = req;
      const condition = {
        ProjectId: +params.ProjectId,
        isDeleted: false,
        isActive: true,
      };
      const gates = await Gates.findAll({ where: condition, attributes: ['id', 'gateName'] });
      return gates;
    } catch (e) {
      console.log(e);
    }
  },
  async getMappedRequests(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const gateDetails = await Gates.findOne({
        where: {
          gateAutoId: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
        },
      });

      if (gateDetails) {
        const getMappedRequests = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          attributes: [
            'id',
            'description',
            'deliveryStart',
            'deliveryEnd',
            'status',
            'notes',
            'DeliveryId',
            'approved_at',
            'escort',
            'vehicleDetails',
            'isQueued',
            'isAllDetailsFilled',
            'cranePickUpLocation',
            'craneDropOffLocation',
            'isAssociatedWithCraneRequest',
            'CraneRequestId',
            'requestType',
          ],
          include: [
            {
              association: 'gateDetails',
              where: { isDeleted: false, isActive: true, GateId: gateDetails.id },
              required: true,
              attributes: ['id'],
              include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
            },
          ],
        });
        const allGates = await Gates.findAll({
          where: Sequelize.and({ ProjectId: inputData.ProjectId, isDeleted: false }),
        });
        return done({ mappedRequest: getMappedRequests, gates: allGates }, false);
      }
      const err = new ApiError('Gate not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async deactivateGate(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const loginUser = req.user;
      const { gateSwitchedRequests } = req.body;
      const memberDetails = await Member.getBy({
        UserId: loginUser.id,
        ProjectId: inputData.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      const gateDetails = await Gates.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
        },
      });
      if (gateDetails) {
        const getMappedRequests = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          attributes: [
            'id',
            'description',
            'deliveryStart',
            'deliveryEnd',
            'status',
            'notes',
            'DeliveryId',
            'approved_at',
            'escort',
            'vehicleDetails',
            'isQueued',
            'isAllDetailsFilled',
            'cranePickUpLocation',
            'craneDropOffLocation',
            'isAssociatedWithCraneRequest',
            'CraneRequestId',
            'requestType',
          ],
          include: [
            {
              association: 'gateDetails',
              where: { isDeleted: false, isActive: true, GateId: gateDetails.id },
              required: true,
              attributes: ['id'],
              include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
            },
          ],
        });
        if (getMappedRequests && getMappedRequests.length > 0) {
          getMappedRequests.map(async (object) => {
            const history = {
              DeliveryRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Gate  ${gateDetails.gateName}`,
            };
            await DeliverHistory.createInstance(history);
            await DeliverGate.update(
              { isActive: false },
              {
                where: {
                  GateId: gateDetails.id,
                  ProjectId: inputData.ProjectId,
                  DeliveryId: object.id,
                  isDeleted: false,
                  isActive: true,
                },
              },
            );
          });
        }
        if (gateSwitchedRequests) {
          gateSwitchedRequests.map(async (object) => {
            const updateParam = {
              DeliveryId: object.id,
              DeliveryCode: object.DeliveryId,
              ProjectId: inputData.ProjectId,
              isDeleted: false,
              GateId: +object.changedGateId,
              isActive: true,
            };
            const existGate = await DeliverGate.findAll({
              where: {
                ProjectId: inputData.ProjectId,
                DeliveryId: object.id,
              },
            });
            const index = existGate.findIndex((item) => item.GateId === +object.changedGateId);
            const getGateObject = await Gates.findOne({
              where: {
                id: +object.changedGateId,
              },
            });
            if (getMappedRequests && getMappedRequests.length > 0) {
              const history = {
                DeliveryRequestId: object.id,
                MemberId: memberDetails.id,
                type: 'edit',
                ProjectId: inputData.ProjectId,
                description: `${loginUser.firstName} ${loginUser.lastName} added the Gate ${getGateObject.gateName}`,
              };
              await DeliverHistory.createInstance(history);
            }
            if (index !== -1) {
              await DeliverGate.update(updateParam, {
                where: {
                  id: existGate[index].id,
                  DeliveryId: object.id,
                  ProjectId: inputData.ProjectId,
                },
              });
            } else {
              await DeliverGate.createInstance(updateParam);
            }
            // }
          });
        }
        const gateDeactivated = await Gates.update(
          { isActive: false },
          {
            where: {
              id: gateDetails.id,
              isActive: true,
            },
          },
        );
        return done(gateDeactivated, false);
      }
      const err = new ApiError('Gate not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
};
module.exports = gateService;