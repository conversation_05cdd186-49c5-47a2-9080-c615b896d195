const authService = require('./authService');
const stripeService = require('./stripeService');
const projectService = require('./projectService');
const equipmentService = require('./equipmentService');
const gateService = require('./gateService');
const companyService = require('./companyService');
const memberService = require('./memberService');
const deliveryService = require('./deliveryService');
const inspectionService = require('./inspectionService');
const overRideService = require('./overRideService');
const restrictMailService = require('./restrictMailService');
const addressService = require('./addressService');
const defineService = require('./defineService');
const voidService = require('./voidService');
const commentService = require('./commentService');
const attachementService = require('./attachementService');
const historyService = require('./historyService');
const cornService = require('./cornService');
const calendarService = require('./calendarService');
const notificationService = require('./notificationService');
const adminService = require('./adminService');
const accountCornService = require('./accountCornService');
const dashboardService = require('./dashboardService');
const deviceTokenService = require('./deviceTokenService');
const billingService = require('./billingService');
const accountService = require('./accountService');
const craneRequestService = require('./craneRequestService');
const craneRequestAttachmentService = require('./craneRequestAttachmentService');
const craneRequestCommentService = require('./craneRequestCommentService');
const craneRequestHistoryService = require('./craneRequestHistoryService');
const calendarSettingsService = require('./calendarSettingsService');
const notificationPreferenceService = require('./notificationPreferenceService');
const concreteRequestService = require('./concreteRequestService');
const concreteRequestAttachmentService = require('./concreteRequestAttachmentService');
const concreteRequestCommentService = require('./concreteRequestCommentService');
const concreteRequestHistoryService = require('./concreteRequestHistoryService');
const deliveryReportService = require('./deliveryreportService');
const craneReportService = require('./craneReportService');
const concreteReportService = require('./concreteReportService');
const pdfDeliveryReportService = require('./pdfDeliveryReportService');
const pdfCraneReportService = require('./pdfCraneReportService');
const pdfConcreteReportService = require('./pdfConcreteReportService');
const csvDeliveryReportService = require('./csvDeliveryReportService');
const csvCraneReportService = require('./csvCraneReportService');
const csvConcreteReportService = require('./csvConcreteReportService');
const excelDeliveryReportService = require('./excelDeliveryReportService');
const excelCraneReportService = require('./excelCraneReportService');
const excelConcreteReportService = require('./excelConcreteReportService');
const excelWeeklyCalendarService = require('./excelWeeklyCalendarService');
const projectSettingsService = require('./projectSettingsService');
const locationService = require('./locationService');
const puppeteerService = require('./puppeteerService');
const bookingTemplatesService = require('./bookingTemplatesService');
const inspectionReportService = require('./inspectionReportService');
const pdfInspectionReportService = require('./pdfInspectionReportService');
const excelInspectionReportService = require('./excelInspectionReportService');
const csvInspectionReportService = require('./csvInspectionReportService');

module.exports = {
  authService,
  stripeService,
  projectService,
  equipmentService,
  gateService,
  companyService,
  memberService,
  deliveryService,
  inspectionService,
  overRideService,
  restrictMailService,
  addressService,
  defineService,
  voidService,
  commentService,
  attachementService,
  historyService,
  cornService,
  calendarService,
  notificationService,
  adminService,
  accountCornService,
  dashboardService,
  deviceTokenService,
  billingService,
  accountService,
  craneRequestService,
  craneRequestAttachmentService,
  craneRequestCommentService,
  craneRequestHistoryService,
  calendarSettingsService,
  notificationPreferenceService,
  concreteRequestService,
  concreteRequestAttachmentService,
  concreteRequestCommentService,
  concreteRequestHistoryService,
  deliveryReportService,
  craneReportService,
  concreteReportService,
  pdfDeliveryReportService,
  pdfCraneReportService,
  pdfConcreteReportService,
  csvDeliveryReportService,
  csvCraneReportService,
  csvConcreteReportService,
  excelDeliveryReportService,
  excelCraneReportService,
  excelConcreteReportService,
  excelWeeklyCalendarService,
  projectSettingsService,
  locationService,
  puppeteerService,
  bookingTemplatesService,
  inspectionReportService,
  pdfInspectionReportService,
  excelInspectionReportService,
  csvInspectionReportService,
};
