const { Enterprise, Sequelize } = require('../models');
let { DeliverHistory, DeliveryRequest, User, InspectionRequest, InspectionHistory } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const commentService = {
  async getHistory(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await DeliveryRequest.findOne({ where: { id: inputData.params.DeliveryRequestId } });
      
      if (!exist) {
        return done(null, { message: 'Delivery Booking id does not exist' });
      }

      const historyList = await DeliverHistory.findAll({
        include: [{
          association: 'Member',
          include: [{ association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] }]
        }],
        where: { DeliveryRequestId: inputData.params.DeliveryRequestId },
        order: [['id', 'DESC']]
      });

      done(historyList, false);
      
    } catch (e) {
      done(null, e);
    }
  },

  async getInspectionHistory(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await InspectionRequest.findOne({ where: { id: inputData.params.InspectionRequestId } });
      
      if (!exist) {
        return done(null, { message: 'Inspection Booking id does not exist' });
      }

      const historyList = await InspectionHistory.findAll({
        include: [{
          association: 'Member',
          include: [{ association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] }]
        }],
        where: { InspectionRequestId: inputData.params.InspectionRequestId },
        order: [['id', 'DESC']]
      });

      done(historyList, false);
      
    } catch (e) {
      done(null, e);
    }
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getDomainEnterpriseValue(domainName) {
    if (!domainName) return null;
    
    const enterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() }
    });
    
    return enterpriseValue || null;
  },

  async getEnterpriseValueByMemberData(memberData, ParentCompanyId) {
    const condition = memberData.isAccount 
      ? { id: memberData.EnterpriseId, status: 'completed' }
      : { ParentCompanyId, status: 'completed' };
    
    return await Enterprise.findOne({ where: condition });
  },

  async getEnterpriseValueWithoutMemberData(ParentCompanyId) {
    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
  },
  
  async getUserData(email) {
    if (!email) return null;
    return await publicUser.findOne({ where: { email } });
  },

  async getMemberData(userData) {
    if (!userData) return null;
    return await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false }
    });
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const { email } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;

    let enterpriseValue = await this.getDomainEnterpriseValue(domainName);

    if (!enterpriseValue && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const userData = await this.getUserData(email);
      const memberData = await this.getMemberData(userData);

      enterpriseValue = memberData 
        ? await this.getEnterpriseValueByMemberData(memberData, ParentCompanyId)
        : await this.getEnterpriseValueWithoutMemberData(ParentCompanyId);
        
      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
      }
    }

    const modelObj = await helper.getDynamicModel(domainName);
    DeliverHistory = modelObj.DeliverHistory;
    DeliveryRequest = modelObj.DeliveryRequest;
    User = modelObj.User;

    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email } });
      inputData.user = newUser;
    }

    return true;
  }
};

module.exports = commentService;