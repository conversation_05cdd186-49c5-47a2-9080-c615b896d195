const crypto = require('crypto');
const status = require('http-status');
const bcrypt = require('bcrypt');
const ApiError = require('../helpers/apiError');
const { generatePassword } = require('../helpers/generatePassword');
const MAILER = require('../mailer');
const {
  User,
  ParentCompany,
  Project,
  Company,
  Sequelize,
  Enterprise,
  RestrictEmail,
  Member,
  BillingHistory,
} = require('../models');

const { Op } = Sequelize;

const jwtGenerator = require('../middlewares/jwtGenerator');
const { bcryptPassword } = require('./password');

const adminService = {
  async createAccountAdmin(user, done) {
    try {
      const userData = user;
      const emailDomainName = await this.emailDomain(userData.basicDetails);
      const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
      if (restrict) {
        done(null, { message: 'This Email domain is restricted.' });
      } else {
        userData.emailDomainName = emailDomainName;
        this.registerUser(userData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async emailDomain(userData) {
    const firstSplit = userData.email.split('@')[1];
    const secondSplit = firstSplit.split('.');
    let emailDomainName;
    if (secondSplit.length === 2) {
      emailDomainName = firstSplit;
    } else if (secondSplit.length > 2) {
      const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
      emailDomainName = str;
    }
    return emailDomainName;
  },
  async createNewUser(userInput) {
    const userData = userInput.basicDetails;
    userData.firstName = userInput.companyDetails.fullName;
    userData.password = generatePassword();
    userData.userType = 'account admin';
    const newUser = await User.createInstance(userData);
    return newUser;
  },
  async commonRegister(userInput, newResult, done) {
    try {
      const userData = userInput.basicDetails;
      const { emailDomainName } = userInput;
      const generatedPassword = userData.password;
      const newUser = await this.createNewUser(userInput);
      let existParentCompany = await ParentCompany.getBy({ emailDomainName });
      if (!existParentCompany) {
        const parentParam = {
          UserId: newUser.id,
          emailDomainName,
        };
        existParentCompany = await ParentCompany.createInstance(parentParam);
      }
      const companyParam = userInput.companyDetails;
      companyParam.createdBy = newUser.id;
      companyParam.ParentCompanyId = existParentCompany.id;
      companyParam.companyAutoId = 1;
      delete companyParam.fullName;
      const newCompany = await Company.createInstance(companyParam);
      userData.generatedPassword = generatedPassword;
      const userDetail = await this.createEnterprise(
        userInput,
        newUser,
        newCompany,
        existParentCompany,
      );
      done(userDetail, false);
    } catch (e) {
      done(null, e);
    }
  },
  async createExistAccountAdmin(userInput, done) {
    try {
      const userData = userInput.basicDetails;
      const emailDomainName = await this.emailDomain(userData);
      const existParentCompany = await ParentCompany.getBy({ emailDomainName });
      const userDetail = await this.createEnterprise(
        userInput,
        userInput.userExist,
        { id: null },
        existParentCompany,
      );
      await this.updateProject(emailDomainName, userDetail);
      done(userDetail, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateProject(emailDomainName, userDetail) {
    const existParentCompany = await ParentCompany.getBy({ emailDomainName });
    await Project.update(
      { isAccount: true, EnterpriseId: userDetail.id },
      { where: { ParentCompanyId: existParentCompany.id } },
    );
    const condition = Sequelize.or({ RoleId: [2, 3] });
    const memberDetail = await Member.findAll({
      where: Sequelize.and({ ParentCompanyId: existParentCompany.id }, condition),
    });
    const userId = [];
    memberDetail.forEach(async (element) => {
      userId.push(element.UserId);
    });
    await Member.update(
      { isAccount: true, EnterpriseId: userDetail.id },
      { where: { ParentCompanyId: existParentCompany.id } },
    );
    await User.update({ userType: 'account admin' }, { where: { id: { [Op.in]: userId } } });
    return userDetail;
  },
  async createEnterprise(userInput, newUser, newCompany, existParentCompany) {
    const { amountDetails } = userInput;
    const { basicDetails } = userInput;
    const enterPriseParam = {
      UserId: newUser.id,
      name: userInput.domainDetails.name,
      domainURL: userInput.domainDetails.domainURL,
      CompanyId: newCompany.id,
      isActive: true,
      amount: amountDetails.amount,
      currencyType: amountDetails.currencyType,
      currencySymbol: amountDetails.currencySymbol,
      projectCount: basicDetails.projectCount,
      ParentCompanyId: existParentCompany.id,
    };
    const userDetail = await Enterprise.createInstance(enterPriseParam);
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();
    const nextPayment = new Date(year + 1, month, day);
    const billingDetail = {
      UserId: newUser.id,
      EnterpriseId: userDetail.id,
      lastPayment: new Date(),
      nextPayment,
      amount: amountDetails.amount,
      currency: amountDetails.currencyType,
      status: 'completed',
      paymentMethod: 'offline',
    };
    await BillingHistory.create(billingDetail);
    return userDetail;
  },
  async createNewAccountAdmin(userInput, done) {
    try {
      const userData = userInput;
      const newUser = await this.createNewUser(userData);
      userData.userExist = newUser;
      this.createExistAccountAdmin(userData, async (userDetail, error) => {
        if (error) {
          done({ status: false, error });
        } else {
          done(userDetail, false);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async registerUser(userInput, done) {
    this.commonRegister(userInput, null, async (response, error) => {
      if (error) {
        done(null, error);
      } else {
        done(response, null);
      }
    });
  },
  async sendMail(userData, done) {
    MAILER.sendMail(userData, 'register', 'Registration', 'Sign Up', (info, err) => {
      if (err) {
        const newError = new ApiError(err.message, status.BAD_REQUEST);
        done(null, newError);
      } else {
        done(userData, false);
      }
    });
  },
  async adminLogin(userData, done) {
    const { email, password } = userData;
    const user = await User.findOne({
      where: Sequelize.and({ email }, Sequelize.or({ userType: ['super admin', 'folloit admin'] })),
    });
    if (!user) {
      const error = new ApiError('Invalid Email Id', status.NOT_FOUND);
      done(null, error);
    } else {
      const verifyPassword = await bcrypt.compare(password, user.password);
      if (!verifyPassword) {
        const error = new ApiError('Invalid Password.', status.UNAUTHORIZED);
        done(null, error);
      } else {
        const userParams = {
          loginDateTime: new Date(),
        };
        await user.updateInstance(user.id, userParams);
        const token = jwtGenerator.token(user);
        user.token = token;
        done(user, false);
      }
    }
  },
  async forgotPassword(userObj, done) {
    const user = userObj;
    const params = {};
    if (!user.isActive || user.isActive === null || user.isActive === undefined) {
      const error = new ApiError('User is not verified', status.BAD_REQUEST);
      done(null, error);
    } else {
      params.resetPasswordToken = crypto.randomBytes(64).toString('hex');
      const userData = await user.updateInstance(user.id, params);
      user.resetPasswordToken = params.resetPasswordToken;
      MAILER.sendMail(user, 'forgotPassword', 'Forgot Password', 'Forgot Password', (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          done(null, newError);
        } else {
          done(userData, false);
        }
      });
    }
  },
  async resetPassword(user, params, done) {
    try {
      let { password } = params;
      const encPassword = await bcryptPassword(password);

      const userParams = {
        password: encPassword,
        resetPasswordToken: null,
        otpCode: null,
        secret: null,
      };

      await user.updateInstance(user.id, userParams);
      done(true, false);
    } catch (error) {
      done(true, error);
    }
  },
};

module.exports = adminService;
