const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    array: jest.fn(() => 'mocked-multer-middleware'),
  }));
  multer.memoryStorage = jest.fn(() => 'mocked-storage');
  return multer;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  concreteRequestAttachmentController: {
    createConcreteRequestAttachment: jest.fn(),
    deleteConcreteRequestAttachment: jest.fn(),
    getConcreteRequestAttachments: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  concreteRequestAttachmentValidation: {
    createConcreteRequestAttachment: jest.fn(),
    deleteConcreteRequestAttachment: jest.fn(),
    getConcreteRequestAttachment: jest.fn(),
  },
}));

describe('concreteRequestAttachmentRoute', () => {
  let router;
  let concreteRequestAttachmentRoute;
  let concreteRequestAttachmentController;
  let passportConfig;
  let concreteRequestAttachmentValidation;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    concreteRequestAttachmentRoute = require('../concreteRequestAttachmentRoute');
    const controllers = require('../../controllers');
    concreteRequestAttachmentController = controllers.concreteRequestAttachmentController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    concreteRequestAttachmentValidation = validations.concreteRequestAttachmentValidation;
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = concreteRequestAttachmentRoute.router;
      
      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer.memoryStorage).toHaveBeenCalled();
      expect(multer).toHaveBeenCalledWith({ storage: 'mocked-storage' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(2);
      
      // Verify POST route
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/add_concrete_request_attachment/:ConcreteRequestId/?:ParentCompanyId/:ProjectId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        concreteRequestAttachmentController.createConcreteRequestAttachment,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/remove_concrete_request_attachment/:id/?:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        concreteRequestAttachmentController.deleteConcreteRequestAttachment,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_concrete_request_attachments/:ConcreteRequestId/?:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        concreteRequestAttachmentController.getConcreteRequestAttachments,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(3);
      expect(validate).toHaveBeenCalledWith(
        concreteRequestAttachmentValidation.createConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        concreteRequestAttachmentValidation.deleteConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        concreteRequestAttachmentValidation.getConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      );

      // Verify multer array method is called
      const uploadInstance = multer.mock.results[0].value;
      expect(uploadInstance.array).toHaveBeenCalledWith('attachment', 12);
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = concreteRequestAttachmentRoute.router;
      const result2 = concreteRequestAttachmentRoute.router;
      
      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      concreteRequestAttachmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;
      
      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should use validation middleware for all routes', () => {
      concreteRequestAttachmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;
      
      // All routes should have validation
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
      });
    });

    it('should configure multer middleware for POST route only', () => {
      concreteRequestAttachmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;
      
      // POST route should have multer middleware
      expect(postCalls[0]).toHaveLength(5); // path + multer + validation + auth + controller
      expect(postCalls[0][1]).toBe('mocked-multer-middleware');
      
      // GET routes should not have multer middleware
      getCalls.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call).not.toContain('mocked-multer-middleware');
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof concreteRequestAttachmentRoute).toBe('object');
      expect(concreteRequestAttachmentRoute).toHaveProperty('router');
      
      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestAttachmentRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestAttachmentRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
