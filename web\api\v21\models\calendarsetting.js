const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;

module.exports = (sequelize, DataTypes) => {
  const CalendarSetting = sequelize.define(
    'CalendarSetting',
    {
      description: DataTypes.STRING,
      fromDate: DataTypes.DATE,
      toDate: DataTypes.DATE,
      isAllDay: {
        type: DataTypes.BOOLEAN,
      },
      isApplicableToDelivery: {
        type: DataTypes.BOOLEAN,
      },
      isApplicableToCrane: {
        type: DataTypes.BOOLEAN,
      },
      isApplicableToConcrete: {
        type: DataTypes.BOOLEAN,
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ParentCompanyId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      recurrence: {
        type: DataTypes.ENUM,
        values: ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly', 'Custom'],
      },
      repeatEveryCount: DataTypes.STRING,
      repeatEveryType: DataTypes.STRING,
      days: DataTypes.ARRAY(DataTypes.STRING),
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      startTime: {
        type: DataTypes.STRING,
      },
      endTime: {
        type: DataTypes.STRING,
      },
      chosenDateOfMonth: {
        type: DataTypes.BOOLEAN,
      },
      dateOfMonth: {
        type: DataTypes.STRING,
      },
      monthlyRepeatType: {
        type: DataTypes.STRING,
      },
      TimeZoneId: {
        type: DataTypes.INTEGER,
      },
      GateId: {
        type: DataTypes.STRING,
      },
      LocationId: {
        type: DataTypes.STRING,
      },
      EquipmentId: {
        type: DataTypes.STRING,
      },
      createdBy: {
        type: DataTypes.INTEGER,
      },
      endDate: DataTypes.DATE,
      isApplicableToInspection: {
        type: DataTypes.BOOLEAN,
      },
    },
    {},
  );
  CalendarSetting.associate = (models) => {
    CalendarSetting.belongsTo(models.TimeZone);
    return CalendarSetting;
  };
  CalendarSetting.createInstance = async (data) => {
    const newCalendarEvent = await CalendarSetting.create(data);
    return newCalendarEvent;
  };
  CalendarSetting.getAll = async (condition) => {
    const CalendarSettings = await CalendarSetting.findAll({
      where: { ...condition, isDeleted: false },
      include: [
        {
          required: true,
          association: 'TimeZone',
          attributes: ['id', 'location', 'timezone', 'timeZoneOffsetInMinutes'],
          where: { isDeleted: false },
        },
      ],
      attributes: [
        'id',
        'description',
        'fromDate',
        'toDate',
        'isAllDay',
        'isApplicableToDelivery',
        'isApplicableToCrane',
        'ParentCompanyId',
        'isDeleted',
        'recurrence',
        'repeatEveryType',
        'repeatEveryCount',
        'days',
        'startTime',
        'endTime',
        'chosenDateOfMonth',
        'dateOfMonth',
        'monthlyRepeatType',
        'TimeZoneId',
        'isApplicableToConcrete',
        'endDate',
        'isApplicableToInspection',
        'GateId',
        'LocationId',
        'EquipmentId',
      ],
    });
    return CalendarSettings;
  };
  CalendarSetting.getAllWeeklyReport = async (newCondition) => {
    const commonSearch = {
      ...newCondition,
    };
    const CalendarSettings = await CalendarSetting.findAll({
      include: [
        {
          required: true,
          association: 'TimeZone',
          attributes: ['id', 'location', 'timezone', 'timeZoneOffsetInMinutes'],
          where: { isDeleted: false },
        },
      ],
      where: { ...newCondition, ...commonSearch, isDeleted: false },
      attributes: [
        'id',
        'description',
        'fromDate',
        'toDate',
        'isAllDay',
        'isApplicableToDelivery',
        'isApplicableToCrane',
        'ParentCompanyId',
        'isDeleted',
        'recurrence',
        'repeatEveryType',
        'repeatEveryCount',
        'days',
        'startTime',
        'endTime',
        'chosenDateOfMonth',
        'dateOfMonth',
        'monthlyRepeatType',
        'TimeZoneId',
        'isApplicableToConcrete',
        'endDate',
        'ProjectId',
        'isApplicableToInspection',
      ],
    });
    return CalendarSettings;
  };
  CalendarSetting.getOne = async (id) => {
    const calendarEvent = await CalendarSetting.findOne({
      where: { id, isDeleted: false },
      include: [
        {
          required: true,
          association: 'TimeZone',
          attributes: ['id', 'location', 'timezone', 'timeZoneOffsetInMinutes'],
          where: { isDeleted: false },
        },
      ],
      attributes: [
        'id',
        'description',
        'fromDate',
        'toDate',
        'isAllDay',
        'isApplicableToDelivery',
        'isApplicableToCrane',
        'ParentCompanyId',
        'isDeleted',
        'recurrence',
        'repeatEveryType',
        'repeatEveryCount',
        'days',
        'startTime',
        'endTime',
        'chosenDateOfMonth',
        'dateOfMonth',
        'monthlyRepeatType',
        'TimeZoneId',
        'isApplicableToConcrete',
        'endDate',
        'isApplicableToInspection',
        'EquipmentId',
        'GateId',
        'LocationId'
      ],
    });
    return calendarEvent;
  };
  CalendarSetting.isExits = async (attr) => {
    const calendarEvent = await CalendarSetting.findOne({ where: { id: attr, isDeleted: false } });
    return calendarEvent;
  };
  CalendarSetting.updateInstance = async (id, attributes) => {
    const calendarEvent = await CalendarSetting.update(attributes, { where: { id } });
    return calendarEvent;
  };
  return CalendarSetting;
};
