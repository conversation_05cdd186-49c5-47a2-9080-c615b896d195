const csvConcreteReportService = require('../csvConcreteReportService');
const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../../middlewares/awsConfig');

// Mock dependencies
jest.mock('export-to-csv');
jest.mock('../../middlewares/awsConfig');
jest.mock('moment', () => {
    const actualMoment = jest.requireActual('moment');
    return jest.fn((...args) => actualMoment(...args));
});

describe('csvConcreteReportService', () => {
    let mockData;
    let mockSelectedHeaders;
    const mockTimezoneOffset = -240; // EST timezone
    const mockFileName = 'test-report.csv';
    const mockExportType = 'concrete-report';

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Mock ExportToCsv
        ExportToCsv.mockImplementation(() => ({
            generateCsv: jest.fn().mockResolvedValue('mock,csv,data'),
        }));

        // Mock AWS upload
        awsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
            callback({ success: true }, null);
        });

        // Sample data for testing
        mockData = [{
            ConcreteRequestId: '123',
            description: 'Test Description',
            concretePlacementStart: '2024-03-20T10:00:00Z',
            status: 'Approved',
            approverDetails: {
                User: {
                    firstName: 'John',
                    lastName: 'Doe'
                }
            },
            concreteSupplierDetails: [{
                Company: {
                    companyName: 'Test Company'
                }
            }],
            concreteOrderNumber: 'ORD-123',
            slump: '4"',
            truckSpacingHours: '2',
            primerForPump: 'Yes',
            memberDetails: [{
                Member: {
                    User: {
                        firstName: 'Jane',
                        lastName: 'Smith'
                    }
                }
            }],
            concreteQuantityOrdered: '100',
            mixDesignDetails: [{
                ConcreteMixDesign: {
                    mixDesign: 'Mix A'
                }
            }],
            location: {
                locationPath: 'Test Location'
            }
        }];

        // Sample headers
        mockSelectedHeaders = [
            { key: 'id', title: 'Id', isActive: true },
            { key: 'description', title: 'Description', isActive: true },
            { key: 'date', title: 'Date & Time', isActive: true },
            { key: 'status', title: 'Status', isActive: true },
            { key: 'approvedby', title: 'Approved By', isActive: true },
            { key: 'company', title: 'Concrete Supplier', isActive: true },
            { key: 'orderNumber', title: 'Order Number', isActive: true },
            { key: 'slump', title: 'Slump', isActive: true },
            { key: 'truckSpacing', title: 'Truck Spacing', isActive: true },
            { key: 'primer', title: 'Primer Ordered', isActive: true },
            { key: 'name', title: 'Responsible Person', isActive: true },
            { key: 'quantity', title: 'Quantity Ordered', isActive: true },
            { key: 'mixDesign', title: 'Mix Design', isActive: true },
            { key: 'location', title: 'Location', isActive: true }
        ];
    });

    describe('exportConcreteReportInCsvFormat', () => {
        it('should successfully export CSV with all headers selected', async () => {
            const done = jest.fn();

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                mockData,
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(ExportToCsv).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith({ success: true }, false);
        });

        it('should handle empty data array', async () => {
            const done = jest.fn();

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                [],
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(ExportToCsv).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith({ success: true }, false);
        });

        it('should handle no active headers selected', async () => {
            const done = jest.fn();
            const inactiveHeaders = mockSelectedHeaders.map(header => ({ ...header, isActive: false }));

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                mockData,
                inactiveHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(done).toHaveBeenCalledWith(null, { message: 'No active headers selected for the report' });
            expect(ExportToCsv).not.toHaveBeenCalled();
            expect(awsConfig.reportUpload).not.toHaveBeenCalled();
        });

        it('should handle partial headers selection', async () => {
            const done = jest.fn();
            const partialHeaders = mockSelectedHeaders.filter((_, index) => index < 3);

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                mockData,
                partialHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(ExportToCsv).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith({ success: true }, false);
        });

        it('should handle AWS upload error', async () => {
            const done = jest.fn();
            awsConfig.reportUpload.mockImplementation((buffer, fileName, exportType, callback) => {
                callback(null, { message: 'AWS upload failed' });
            });

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                mockData,
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(done).toHaveBeenCalledWith(null, { message: 'cannot export document' });
        });

        it('should handle missing optional fields in data', async () => {
            const done = jest.fn();
            const incompleteData = [{
                ConcreteRequestId: '123',
                description: 'Test Description',
                concretePlacementStart: '2024-03-20T10:00:00Z',
                status: 'Approved'
                // Missing other fields
            }];

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                incompleteData,
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(ExportToCsv).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith({ success: true }, false);
        });

        it('should handle null or undefined values in nested objects', async () => {
            const done = jest.fn();
            const dataWithNulls = [{
                ...mockData[0],
                approverDetails: null,
                concreteSupplierDetails: null,
                memberDetails: null,
                mixDesignDetails: null,
                location: null
            }];

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                dataWithNulls,
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(ExportToCsv).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith({ success: true }, false);
        });

        it('should correctly format date with timezone offset', async () => {
            const done = jest.fn();
            const testDate = '2024-03-20T10:00:00Z';
            const dataWithDate = [{
                ...mockData[0],
                concretePlacementStart: testDate
            }];

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                dataWithDate,
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(moment).toHaveBeenCalled();
            expect(ExportToCsv).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith({ success: true }, false);
        });

        it('should handle CSV generation error', async () => {
            const done = jest.fn();
            ExportToCsv.mockImplementation(() => ({
                generateCsv: jest.fn().mockResolvedValue(null)
            }));

            await csvConcreteReportService.exportConcreteReportInCsvFormat(
                mockData,
                mockSelectedHeaders,
                mockTimezoneOffset,
                mockFileName,
                mockExportType,
                done
            );

            expect(awsConfig.reportUpload).not.toHaveBeenCalled();
            expect(done).not.toHaveBeenCalled();
        });
    });
}); 