const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../controllers', () => ({
  ConcreteRequestController: {
    createConcreteRequest: jest.fn(),
    editConcreteRequest: jest.fn(),
    getConcreteRequestList: jest.fn(),
    getLastConcreteRequestId: jest.fn(),
    getSingleConcreteRequest: jest.fn(),
    updateConcreteRequestStatus: jest.fn(),
    upcomingConcreteRequest: jest.fn(),
    upcomingRequestList: jest.fn(),
    getConcreteDropdownDetail: jest.fn(),
    deleteConcreteRequest: jest.fn(),
    editMultipleDeliveryRequest: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  concreteRequestValidation: {
    concreteRequest: jest.fn(),
    editConcreteRequest: jest.fn(),
    getSingleConcreteRequest: jest.fn(),
    updateConcreteRequestStatus: jest.fn(),
  },
}));

describe('concreteRequestRoute', () => {
  let router;
  let concreteRequestRoute;
  let ConcreteRequestController;
  let passportConfig;
  let concreteRequestValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    concreteRequestRoute = require('../concreteRequestRoute');
    const controllers = require('../../controllers');
    ConcreteRequestController = controllers.ConcreteRequestController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    concreteRequestValidation = validations.concreteRequestValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = concreteRequestRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(6);
      expect(router.get).toHaveBeenCalledTimes(5);

      // Verify POST routes with validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/create_concrete_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        ConcreteRequestController.createConcreteRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/edit_concrete_request',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        ConcreteRequestController.editConcreteRequest,
      );

      // Verify POST routes without validation
      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/get_concrete_request_list/:ProjectId/:pageSize/:pageNo/:void',
        passportConfig.isAuthenticated,
        ConcreteRequestController.getConcreteRequestList,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/update_concrete_request_status',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        ConcreteRequestController.updateConcreteRequestStatus,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/delete_concrete_request',
        passportConfig.isAuthenticated,
        ConcreteRequestController.deleteConcreteRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/edit_multiple_request',
        passportConfig.isAuthenticated,
        ConcreteRequestController.editMultipleDeliveryRequest,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_last_concrete_request_id/:ProjectId/?:ParentCompanyId',
        passportConfig.isAuthenticated,
        ConcreteRequestController.getLastConcreteRequestId,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_single_Concrete_request/:ConcreteRequestId/:ProjectId/?:ParentCompanyId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        ConcreteRequestController.getSingleConcreteRequest,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        3,
        '/upcoming_Concrete_request',
        passportConfig.isAuthenticated,
        ConcreteRequestController.upcomingConcreteRequest,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        4,
        '/upcoming_request_list',
        passportConfig.isAuthenticated,
        ConcreteRequestController.upcomingRequestList,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        5,
        '/concrete_dropdown_detail',
        passportConfig.isAuthenticated,
        ConcreteRequestController.getConcreteDropdownDetail,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(4);
      expect(validate).toHaveBeenCalledWith(
        concreteRequestValidation.concreteRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        concreteRequestValidation.editConcreteRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        concreteRequestValidation.getSingleConcreteRequest,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        concreteRequestValidation.updateConcreteRequestStatus,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = concreteRequestRoute.router;
      const result2 = concreteRequestRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      concreteRequestRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should use validation middleware for specific routes', () => {
      concreteRequestRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // Routes with validation should have 4 parameters
      const routesWithValidation = [
        postCalls[0], // create_concrete_request
        postCalls[1], // edit_concrete_request
        postCalls[3], // update_concrete_request_status (moved to position 4)
        getCalls[1],  // get_single_Concrete_request
      ];

      routesWithValidation.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call[1]).toBe('mocked-validate-middleware');
      });

      // Routes without validation should have 3 parameters
      const routesWithoutValidation = [
        postCalls[2], // get_concrete_request_list (moved to position 3)
        postCalls[4], // delete_concrete_request
        postCalls[5], // edit_multiple_request
        getCalls[0],  // get_last_concrete_request_id
        getCalls[2],  // upcoming_Concrete_request
        getCalls[3],  // upcoming_request_list
        getCalls[4],  // concrete_dropdown_detail
      ];

      routesWithoutValidation.forEach(call => {
        expect(call).toHaveLength(3); // path + auth + controller
        expect(call[1]).toBe(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof concreteRequestRoute).toBe('object');
      expect(concreteRequestRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(concreteRequestRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
