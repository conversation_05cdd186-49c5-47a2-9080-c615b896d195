const status = require('http-status');
const CalendarController = require('../CalendarController');
const {
  calendarService,
  deliveryService,
  craneRequestService,
  calendarSettingsService,
  inspectionService,
} = require('../../services');
const { ProjectSettings } = require('../../models');

// Mock dependencies
jest.mock('../../services', () => ({
  calendarService: {
    getEventNDR: jest.fn(),
    getDeliveryRequestWithCrane: jest.fn(),
    getConcreteRequest: jest.fn(),
    captureInspectionEventNDR: jest.fn(),
    captureCraneEventNDR: jest.fn(),
    captureConcreteEventNDR: jest.fn(),
    captureEventNDR: jest.fn(),
    getAllCalendarData: jest.fn(),
    checkOverlappingToRestore: jest.fn(),
  },
  deliveryService: {
    lastDelivery: jest.fn(),
  },
  craneRequestService: {
    lastCraneRequest: jest.fn(),
  },
  calendarSettingsService: {
    getAll: jest.fn(),
  },
  inspectionService: {
    lastInspection: jest.fn(),
  },
}));

jest.mock('../../models', () => ({
  ProjectSettings: {
    getCalendarStatusColor: jest.fn(),
    getCalendarCard: jest.fn(),
  },
}));

describe('CalendarController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {
        start: '2023-01-01',
        end: '2023-01-31',
        ParentCompanyId: 1,
        search: '',
        filterCount: 0,
        isApplicableToDelivery: true,
        isApplicableToInspection: false,
        isApplicableToCrane: false,
        isApplicableToConcrete: false,
      },
      params: {
        ProjectId: '1',
      },
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('getEventNDR', () => {
    it('should get event NDR successfully', async () => {
      const mockCalendarResponse = { rows: [{ id: 1, name: 'Event 1' }] };
      const mockSettingsResponse = [{ id: 1, name: 'Setting 1' }];
      const mockStatusResponse = { status: 'active' };
      const mockCardResponse = { card: 'data' };
      const mockLastDelivery = { id: 10 };

      calendarService.getEventNDR.mockImplementation((req, callback) => {
        callback(mockCalendarResponse, null);
      });
      calendarSettingsService.getAll.mockResolvedValue(mockSettingsResponse);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusResponse);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardResponse);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(mockLastDelivery, null);
      });

      await CalendarController.getEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.getEventNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('1');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('1');
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: [...mockCalendarResponse.rows, ...mockSettingsResponse],
        statusData: mockStatusResponse,
        cardData: mockCardResponse,
        lastId: mockLastDelivery,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle calendar service error', async () => {
      const mockError = new Error('Calendar service error');
      calendarService.getEventNDR.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CalendarController.getEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.getEventNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle delivery service error', async () => {
      const mockCalendarResponse = { rows: [{ id: 1, name: 'Event 1' }] };
      const mockSettingsResponse = [{ id: 1, name: 'Setting 1' }];
      const mockStatusResponse = { status: 'active' };
      const mockCardResponse = { card: 'data' };
      const mockDeliveryError = new Error('Delivery service error');

      calendarService.getEventNDR.mockImplementation((req, callback) => {
        callback(mockCalendarResponse, null);
      });
      calendarSettingsService.getAll.mockResolvedValue(mockSettingsResponse);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusResponse);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardResponse);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(null, mockDeliveryError);
      });

      await CalendarController.getEventNDR(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockDeliveryError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle general error', async () => {
      const mockError = new Error('General error');
      calendarService.getEventNDR.mockImplementation(() => {
        throw mockError;
      });

      await CalendarController.getEventNDR(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('event_NDRcalender', () => {
    it('should get event NDR calendar successfully', async () => {
      const mockCalendarResponse = { rows: [{ id: 1, name: 'Event 1' }] };
      const mockSettingsResponse = [{ id: 1, name: 'Setting 1' }];
      const mockLastDelivery = { id: 10 };

      calendarService.getEventNDR.mockImplementation((req, callback) => {
        callback(mockCalendarResponse, null);
      });
      calendarSettingsService.getAll.mockResolvedValue(mockSettingsResponse);
      deliveryService.lastDelivery.mockImplementation((req, callback) => {
        callback(mockLastDelivery, null);
      });

      await CalendarController.event_NDRcalender(mockReq, mockRes, mockNext);

      expect(calendarService.getEventNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(deliveryService.lastDelivery).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking listed Successfully.',
        data: [...mockCalendarResponse.rows, ...mockSettingsResponse],
        lastId: mockLastDelivery,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle calendar service error', async () => {
      const mockError = new Error('Calendar service error');
      calendarService.getEventNDR.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CalendarController.event_NDRcalender(mockReq, mockRes, mockNext);

      expect(calendarService.getEventNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getDeliveryRequestWithCrane', () => {
    it('should get delivery request with crane successfully', async () => {
      const mockCalendarResponse = [{ id: 1, name: 'Crane Event 1' }];
      const mockSettingsResponse = [{ id: 1, name: 'Setting 1' }];
      const mockStatusResponse = { status: 'active' };
      const mockCardResponse = { card: 'data' };
      const mockLastCraneRequest = { id: 10 };

      calendarService.getDeliveryRequestWithCrane.mockImplementation((req, callback) => {
        callback(mockCalendarResponse, null);
      });
      calendarSettingsService.getAll.mockResolvedValue(mockSettingsResponse);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusResponse);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardResponse);
      craneRequestService.lastCraneRequest.mockImplementation((req, callback) => {
        callback(mockLastCraneRequest, null);
      });

      await CalendarController.getDeliveryRequestWithCrane(mockReq, mockRes, mockNext);

      expect(calendarService.getDeliveryRequestWithCrane).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('1');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('1');
      expect(craneRequestService.lastCraneRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Delivery Booking Associated With Crane Equipment Type listed Successfully.',
        data: [...mockCalendarResponse, ...mockSettingsResponse],
        statusData: mockStatusResponse,
        cardData: mockCardResponse,
        lastId: mockLastCraneRequest,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle calendar service error', async () => {
      const mockError = new Error('Calendar service error');
      calendarService.getDeliveryRequestWithCrane.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CalendarController.getDeliveryRequestWithCrane(mockReq, mockRes, mockNext);

      expect(calendarService.getDeliveryRequestWithCrane).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getConcreteRequest', () => {
    it('should get concrete request successfully', async () => {
      const mockCalendarResponse = [{ id: 1, name: 'Concrete Event 1' }];
      const mockSettingsResponse = [{ id: 1, name: 'Setting 1' }];
      const mockStatusResponse = { status: 'active' };
      const mockCardResponse = { card: 'data' };

      calendarService.getConcreteRequest.mockImplementation((req, callback) => {
        callback(mockCalendarResponse, null);
      });
      calendarSettingsService.getAll.mockResolvedValue(mockSettingsResponse);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusResponse);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardResponse);

      await CalendarController.getConcreteRequest(mockReq, mockRes, mockNext);

      expect(calendarService.getConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('1');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('1');
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete booking listed successfully',
        data: [...mockCalendarResponse, ...mockSettingsResponse],
        statusData: mockStatusResponse,
        cardData: mockCardResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle calendar service error', async () => {
      const mockError = new Error('Calendar service error');
      calendarService.getConcreteRequest.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CalendarController.getConcreteRequest(mockReq, mockRes, mockNext);

      expect(calendarService.getConcreteRequest).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getInspectionEventNDR', () => {
    it('should get inspection event NDR successfully', async () => {
      const mockCalendarResponse = { rows: [{ id: 1, name: 'Inspection Event 1' }] };
      const mockSettingsResponse = [{ id: 1, name: 'Setting 1' }];
      const mockStatusResponse = { status: 'active' };
      const mockCardResponse = { card: 'data' };
      const mockLastInspection = { id: 10 };

      calendarService.getEventNDR.mockImplementation((req, callback) => {
        callback(mockCalendarResponse, null);
      });
      calendarSettingsService.getAll.mockResolvedValue(mockSettingsResponse);
      ProjectSettings.getCalendarStatusColor.mockResolvedValue(mockStatusResponse);
      ProjectSettings.getCalendarCard.mockResolvedValue(mockCardResponse);
      inspectionService.lastInspection.mockImplementation((req, callback) => {
        callback(mockLastInspection, null);
      });

      await CalendarController.getInspectionEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.getEventNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(calendarSettingsService.getAll).toHaveBeenCalledWith(mockReq, mockNext);
      expect(ProjectSettings.getCalendarStatusColor).toHaveBeenCalledWith('1');
      expect(ProjectSettings.getCalendarCard).toHaveBeenCalledWith('1');
      expect(inspectionService.lastInspection).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Booking listed Successfully.',
        data: [...mockCalendarResponse.rows, ...mockSettingsResponse],
        statusData: mockStatusResponse,
        cardData: mockCardResponse,
        lastId: mockLastInspection,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('captureInspectionEventNDR', () => {
    it('should capture inspection event NDR successfully', async () => {
      const mockResponse = { id: 1, status: 'captured' };
      calendarService.captureInspectionEventNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CalendarController.captureInspectionEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.captureInspectionEventNDR).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Inspection Event captured Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle service error', async () => {
      const mockError = new Error('Service error');
      calendarService.captureInspectionEventNDR.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await CalendarController.captureInspectionEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.captureInspectionEventNDR).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('captureCraneEventNDR', () => {
    it('should capture crane event NDR successfully', async () => {
      const mockResponse = { id: 1, status: 'captured' };
      calendarService.captureCraneEventNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CalendarController.captureCraneEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.captureCraneEventNDR).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Event captured Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('captureConcreteEventNDR', () => {
    it('should capture concrete event NDR successfully', async () => {
      const mockResponse = { id: 1, status: 'captured' };
      calendarService.captureConcreteEventNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CalendarController.captureConcreteEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.captureConcreteEventNDR).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Concrete Event captured Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('captureEventNDR', () => {
    it('should capture event NDR successfully', async () => {
      const mockResponse = { id: 1, status: 'captured' };
      calendarService.captureEventNDR.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CalendarController.captureEventNDR(mockReq, mockRes, mockNext);

      expect(calendarService.captureEventNDR).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Event captured Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('getAllCalendarData', () => {
    it('should get all calendar data successfully', async () => {
      const mockResponse = { data: 'calendar data' };
      calendarService.getAllCalendarData.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CalendarController.getAllCalendarData(mockReq, mockRes, mockNext);

      expect(calendarService.getAllCalendarData).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Calendar data listed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('checkOverlappingToRestore', () => {
    it('should check overlapping to restore successfully', async () => {
      const mockResponse = { overlapping: false };
      calendarService.checkOverlappingToRestore.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await CalendarController.checkOverlappingToRestore(mockReq, mockRes, mockNext);

      expect(calendarService.checkOverlappingToRestore).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Overlapping check completed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
