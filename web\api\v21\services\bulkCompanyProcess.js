const { parentPort } = require('worker_threads');
const { parse } = require('flatted');
const { Company, CompanyDefine, Member, DeliverDefineWork } = require('../models');
const companyservice = require('./companyService');
const { companyService } = require('.');

// Member validation
async function validateMemberAccess(loginUser, ProjectId) {
  return Member.getBy({
    UserId: loginUser.id,
    ProjectId,
    isActive: true,
    isDeleted: false,
  });
}

// Company ID management
async function getNextCompanyAutoId(ProjectId) {
  const lastIdValue = await Company.findOne({
    where: { ProjectId, isDeleted: false },
    order: [['companyAutoId', 'DESC']],
  });

  const newValue = JSON.parse(JSON.stringify(lastIdValue));
  return (newValue?.companyAutoId ?? 0) + 1;
}

// Website handling
function getWebsiteValue(website) {
  if (!website) return null;
  return typeof website === 'object' ? website.text : website;
}

// Company parameters preparation
function prepareCompanyParams(row, ProjectId) {
  return {
    companyName: row.companyName,
    address: row.address_line_1 || null,
    secondAddress: row.address_line_2 || null,
    country: row.country || null,
    state: row.state || null,
    city: row.city || null,
    website: getWebsiteValue(row.website),
    zipCode: row.zipcode || null,
    additional_notes: row.additional_notes || null,
    ProjectId,
    isParent: false,
  };
}

// DFOW data handling
async function getDFOWData(dfow, ProjectId) {
  if (!dfow) return null;
  return DeliverDefineWork.findOne({
    where: { DFOW: dfow, ProjectId },
  });
}

// Company definition creation
async function createCompanyDefinition(row, newCompany, ProjectId) {
  const dfowData = await getDFOWData(row.dfow, ProjectId);
  if (!dfowData) return;

  await CompanyDefine.createInstance({
    DeliverDefineWorkId: dfowData.id,
    CompanyId: newCompany.id,
    ProjectId,
  });
}

// Public company creation
async function createPublicCompanyIfNeeded(companyParam, inputData) {
  if (!inputData.user.domainName) return;
  await companyService.createPublicCompany(companyParam);
}

// Row data preparation
function prepareRowData(element) {
  const getRow = [...element];
  getRow.shift();

  return {
    companyName: getRow[0],
    dfow: getRow[1],
    address_line_1: getRow[2],
    address_line_2: getRow[3],
    country: getRow[4],
    state: getRow[5],
    city: getRow[6],
    zipcode: getRow[7],
    website: getRow[8],
    additional_notes: getRow[9],
  };
}

// Company processing
async function processCompanyRow(row, projectDetails, loginUser, ProjectId, inputData) {
  if (!row.companyName || !projectDetails) return;

  const memberDetails = await validateMemberAccess(loginUser, ProjectId);
  if (!memberDetails) return;

  const companyParam = prepareCompanyParams(row, ProjectId);
  companyParam.companyAutoId = await getNextCompanyAutoId(ProjectId);

  const newCompany = await Company.createInstance(companyParam);
  await createPublicCompanyIfNeeded(companyParam, inputData);
  await createCompanyDefinition(row, newCompany, ProjectId);
}

// Message handler function for testing
async function messageHandler(message) {
  const { projectDetails, loginUser, companyRecords, ProjectId, inputData } = parse(message);
  await companyservice.getDynamicModel(inputData);

  for (const [i, element] of companyRecords.entries()) {
    const row = prepareRowData(element);
    await processCompanyRow(row, projectDetails, loginUser, ProjectId, inputData);

    if (i === companyRecords.length - 1) {
      console.log('success');
    }
  }

  parentPort.postMessage('done');
}

// Main message handler
parentPort.on('message', messageHandler);

// Export functions for testing
module.exports = {
  validateMemberAccess,
  getNextCompanyAutoId,
  getWebsiteValue,
  prepareCompanyParams,
  getDFOWData,
  createCompanyDefinition,
  createPublicCompanyIfNeeded,
  prepareRowData,
  processCompanyRow,
  messageHandler
};
