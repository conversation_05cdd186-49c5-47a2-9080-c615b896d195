// Mock dependencies before requiring the module
const mockSequelizeInstance = {
  authenticate: jest.fn(),
  close: jest.fn(),
};

const mockSequelize = jest.fn(() => mockSequelizeInstance);
mockSequelize.DataTypes = {
  STRING: 'STRING',
  INTEGER: 'INTEGER',
  BOOLEAN: 'BOOLEAN',
};

jest.mock('sequelize', () => mockSequelize);

const mockFs = {
  readdirSync: jest.fn(),
};
jest.mock('fs', () => mockFs);

const mockPath = {
  join: jest.fn((...args) => args.join('/')),
  resolve: jest.fn((...args) => args.join('/')),
};
jest.mock('path', () => mockPath);

jest.mock('../../config/common', () => ({
  dynamicModels: ['User', 'Member', 'Project', 'TestModel']
}));

jest.mock('../../../../config/db', () => ({
  test: {
    database: 'test_db',
    username: 'test_user',
    password: 'test_pass',
    host: 'localhost',
    dialect: 'postgres'
  },
  development: {
    database: 'dev_db',
    username: 'dev_user',
    password: 'dev_pass',
    host: 'localhost',
    dialect: 'postgres'
  }
}));

jest.mock('../../models', () => ({}));

// Mock the require function to return mock models
const originalRequire = require;
const mockModels = new Map();

// Override require to return mock models
require = jest.fn((modulePath) => {
  if (mockModels.has(modulePath)) {
    return mockModels.get(modulePath);
  }
  return originalRequire(modulePath);
});

// Set up environment
process.env.NODE_ENV = 'test';

const db = require('../../models');

describe('dynamicSchemaModels', () => {
  let domain;
  let originalNodeEnv;

  beforeEach(() => {
    jest.clearAllMocks();
    mockModels.clear();

    // Store original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'test';

    // Reset the module cache to get a fresh instance
    jest.resetModules();

    // Re-require the module after mocks are set up
    const dynamicSchemaModels = require('../dynamicSchemaModels');
    domain = dynamicSchemaModels.domain;
  });

  afterEach(() => {
    // Restore original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
    mockModels.clear();
  });

  it('should load models with associate function and add them to db', async () => {
    // Mock file system
    mockFs.readdirSync.mockReturnValue(['User.js', 'Member.js', 'Project.js', '.hidden', 'index.js', 'NotInDynamicModels.js']);

    // Create mock models
    const mockUserModel = jest.fn(() => ({
      name: 'User',
      associate: jest.fn(),
    }));
    mockUserModel.name = 'User';
    mockUserModel.associate = jest.fn();

    const mockMemberModel = jest.fn(() => ({
      name: 'Member',
      associate: jest.fn(),
    }));
    mockMemberModel.name = 'Member';
    mockMemberModel.associate = jest.fn();

    const mockProjectModel = jest.fn(() => ({
      name: 'Project',
      // No associate function
    }));
    mockProjectModel.name = 'Project';

    // Set up mock models in the require map
    mockModels.set('../models/User.js', mockUserModel);
    mockModels.set('../models/Member.js', mockMemberModel);
    mockModels.set('../models/Project.js', mockProjectModel);

    await domain('testdomain');

    // Verify Sequelize was called with correct parameters
    expect(mockSequelize).toHaveBeenCalledWith('test_db', 'test_user', 'test_pass', {
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      host: 'localhost',
      dialect: 'postgres',
      schema: 'testdomain'
    });

    // Verify fs.readdirSync was called
    expect(mockFs.readdirSync).toHaveBeenCalled();

    // Verify models were called with sequelize instance and DataTypes
    expect(mockUserModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);
    expect(mockMemberModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);
    expect(mockProjectModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);

    // Verify associate was called for models that have it
    expect(mockUserModel.associate).toHaveBeenCalled();
    expect(mockMemberModel.associate).toHaveBeenCalled();

    // Verify models were added to db with Dynamic prefix
    expect(db.DynamicUser).toBeDefined();
    expect(db.DynamicMember).toBeDefined();
    expect(db.DynamicProject).toBeDefined();
    expect(db.dynamicSequelize).toBe(mockSequelizeInstance);
  });

  it('should handle models without associate function', async () => {
    // Mock file system with only one model file
    mockFs.readdirSync.mockReturnValue(['TestModel.js']);

    // Create mock model without associate function
    const mockTestModel = jest.fn(() => ({
      name: 'TestModel',
    }));
    mockTestModel.name = 'TestModel';

    // Mock require call for model file
    jest.doMock('../../models/TestModel.js', () => mockTestModel, { virtual: true });

    await domain('testdomain');

    // Verify model was called
    expect(mockTestModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);

    // Verify model was added to db
    expect(db.DynamicTestModel).toBeDefined();
  });

  it('should skip files that start with dot or are index.js', async () => {
    // Mock file system with files that should be skipped
    mockFs.readdirSync.mockReturnValue(['.hidden', 'index.js', 'User.js']);

    // Create mock model
    const mockUserModel = jest.fn(() => ({
      name: 'User',
    }));
    mockUserModel.name = 'User';

    // Mock require call for model file
    jest.doMock('../../models/User.js', () => mockUserModel, { virtual: true });

    await domain('testdomain');

    // Verify only User.js was processed
    expect(mockUserModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);
    expect(db.DynamicUser).toBeDefined();
  });

  it('should skip models not in dynamicModels array', async () => {
    // Mock file system
    mockFs.readdirSync.mockReturnValue(['User.js', 'NonDynamicModel.js']);

    // Create mock models
    const mockUserModel = jest.fn(() => ({
      name: 'User',
    }));
    mockUserModel.name = 'User';

    const mockNonDynamicModel = jest.fn(() => ({
      name: 'NonDynamicModel',
    }));
    mockNonDynamicModel.name = 'NonDynamicModel';

    // Mock require calls for model files
    jest.doMock('../../models/User.js', () => mockUserModel, { virtual: true });
    jest.doMock('../../models/NonDynamicModel.js', () => mockNonDynamicModel, { virtual: true });

    await domain('testdomain');

    // Verify only User was processed (it's in dynamicModels array)
    expect(mockUserModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);
    expect(db.DynamicUser).toBeDefined();

    // Verify NonDynamicModel was not processed (not in dynamicModels array)
    expect(mockNonDynamicModel).not.toHaveBeenCalled();
    expect(db.DynamicNonDynamicModel).toBeUndefined();
  });

  it('should use development config when NODE_ENV is not test', async () => {
    // Set NODE_ENV to development
    process.env.NODE_ENV = 'development';

    // Reset modules to pick up new NODE_ENV
    jest.resetModules();
    const dynamicSchemaModels = require('../dynamicSchemaModels');
    const domainDev = dynamicSchemaModels.domain;

    // Mock file system
    mockFs.readdirSync.mockReturnValue(['User.js']);

    // Create mock model
    const mockUserModel = jest.fn(() => ({
      name: 'User',
    }));
    mockUserModel.name = 'User';

    // Mock require call for model file
    jest.doMock('../../models/User.js', () => mockUserModel, { virtual: true });

    await domainDev('testdomain');

    // Verify Sequelize was called with development config
    expect(mockSequelize).toHaveBeenCalledWith('dev_db', 'dev_user', 'dev_pass', {
      database: 'dev_db',
      username: 'dev_user',
      password: 'dev_pass',
      host: 'localhost',
      dialect: 'postgres',
      schema: 'testdomain'
    });
  });

  it('should handle empty models directory', async () => {
    // Mock file system with no model files
    mockFs.readdirSync.mockReturnValue([]);

    await domain('testdomain');

    // Verify Sequelize was still called
    expect(mockSequelize).toHaveBeenCalledWith('test_db', 'test_user', 'test_pass', {
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      host: 'localhost',
      dialect: 'postgres',
      schema: 'testdomain'
    });

    // Note: dynamicSequelize is not set when there are no models in the current implementation
    // This is actually a bug in the original code - it should be set outside the forEach loop
    expect(db.dynamicSequelize).toBeUndefined();
  });

  it('should handle models with complex associate functions', async () => {
    // Mock file system
    mockFs.readdirSync.mockReturnValue(['User.js', 'Member.js']);

    // Create mock models with associate functions that reference each other
    const mockUserModel = jest.fn(() => ({
      name: 'User',
      associate: jest.fn((models) => {
        // Simulate association logic
        models.User.hasMany(models.Member);
      }),
    }));
    mockUserModel.name = 'User';
    mockUserModel.associate = jest.fn((models) => {
      models.User.hasMany(models.Member);
    });

    const mockMemberModel = jest.fn(() => ({
      name: 'Member',
      associate: jest.fn((models) => {
        // Simulate association logic
        models.Member.belongsTo(models.User);
      }),
    }));
    mockMemberModel.name = 'Member';
    mockMemberModel.associate = jest.fn((models) => {
      models.Member.belongsTo(models.User);
    });

    // Mock require calls for model files
    jest.doMock('../../models/User.js', () => mockUserModel, { virtual: true });
    jest.doMock('../../models/Member.js', () => mockMemberModel, { virtual: true });

    await domain('testdomain');

    // Verify both models were processed
    expect(mockUserModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);
    expect(mockMemberModel).toHaveBeenCalledWith(mockSequelizeInstance, mockSequelize.DataTypes);

    // Verify associate functions were called with the models object
    expect(mockUserModel.associate).toHaveBeenCalledWith(expect.objectContaining({
      DynamicUser: expect.anything(),
      DynamicMember: expect.anything(),
    }));
    expect(mockMemberModel.associate).toHaveBeenCalledWith(expect.objectContaining({
      DynamicUser: expect.anything(),
      DynamicMember: expect.anything(),
    }));
  });
});