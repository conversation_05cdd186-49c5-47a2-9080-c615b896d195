const exportService = require('../exportService');

// Mock ExcelJS
const mockWorksheet = {
  getRow: jest.fn(() => ({
    values: [],
    height: 0,
  })),
  columns: [],
  mergeCells: jest.fn(),
  getCell: jest.fn(() => ({
    value: '',
    alignment: {},
    font: {},
    dataValidation: {},
    NumberFormat: 0,
  })),
  addRow: jest.fn(() => ({
    font: {},
  })),
};

const mockWorkbook = {
  views: [],
  addWorksheet: jest.fn(() => mockWorksheet),
};

jest.mock('exceljs', () => ({
  Workbook: jest.fn(() => mockWorkbook),
}));

// Mock moment
jest.mock('moment', () => {
  const moment = jest.requireActual('moment');
  return moment;
});

// Mock service dependencies
jest.mock('../gateService', () => ({
  gatesForBulkUploadDeliveryRequest: jest.fn(),
}));

jest.mock('../equipmentService', () => ({
  equipmentsForBulkUploadDeliveryRequest: jest.fn(),
}));

jest.mock('../locationService', () => ({
  getDropdownValuesForLocation: jest.fn(),
}));

jest.mock('../companyService', () => ({
  dfowAndCompanyForBulkUploadDeliveryRequest: jest.fn(),
}));

jest.mock('../memberService', () => ({
  membersForBulkUploadDeliveryRequest: jest.fn(),
}));

// Mock timePicker helper
jest.mock('../../helpers/timePicker', () => [
  '00:00:00',
  '01:00:00',
  '02:00:00',
  '07:00:00',
  '08:00:00',
]);

// Mock all other dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      in: jest.fn(),
      ne: jest.fn(),
      and: jest.fn(),
      or: jest.fn(),
      between: jest.fn(),
      notIn: jest.fn(),
    },
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
    findAll: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    getBy: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

describe('ExportService', () => {
  let mockReq;
  const gateService = require('../gateService');
  const equipmentService = require('../equipmentService');
  const locationService = require('../locationService');
  const companyService = require('../companyService');
  const memberService = require('../memberService');

  beforeEach(() => {
    jest.clearAllMocks();

    mockReq = {
      params: {
        ProjectId: 1,
        ParentCompanyId: 1,
      },
      body: {
        ProjectId: 1,
        ParentCompanyId: 1,
      },
      user: {
        id: 1,
        email: '<EMAIL>',
      },
    };

    // Setup service mocks with realistic data
    gateService.gatesForBulkUploadDeliveryRequest.mockResolvedValue([
      { id: 1, gateName: 'Gate A' },
      { id: 2, gateName: 'Gate B' },
    ]);

    equipmentService.equipmentsForBulkUploadDeliveryRequest.mockResolvedValue([
      { id: 1, equipmentName: 'Crane 1' },
      { id: 2, equipmentName: 'Crane 2' },
    ]);

    locationService.getDropdownValuesForLocation.mockResolvedValue([
      { id: 1, locationPath: 'Building A > Level 1 > Room 1' },
      { id: 2, locationPath: 'Building B > Level 2 > Room 2' },
    ]);

    companyService.dfowAndCompanyForBulkUploadDeliveryRequest.mockResolvedValue({
      newCompanyList: [
        { id: 1, companyName: 'Company A' },
        { id: 2, companyName: 'Company B' },
      ],
      defineRecord: [
        { id: 1, DFOW: 'DFOW 1' },
        { id: 2, DFOW: 'DFOW 2' },
      ],
    });

    memberService.membersForBulkUploadDeliveryRequest.mockResolvedValue([
      { id: 1, User: { email: '<EMAIL>' } },
      { id: 2, User: { email: '<EMAIL>' } },
    ]);
  });

  describe('createWorkbook', () => {
    it('should create a workbook with correct view settings', async () => {
      const workbook = await exportService.createWorkbook();

      expect(workbook).toBeDefined();
      expect(workbook.views).toEqual([
        {
          x: 0,
          y: 0,
          width: 10000,
          height: 20000,
          firstSheet: 0,
          activeTab: 1,
          visibility: 'visible',
        },
      ]);
    });
  });

  describe('exportSampleDocument', () => {
    it('should create a sample DFOW document', async () => {
      const workbook = await exportService.exportSampleDocument();

      expect(workbook).toBeDefined();
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('DFOW');
      expect(mockWorksheet.getRow).toHaveBeenCalledWith(1);
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(3); // Three sample rows
    });

    it('should set correct column headers and widths', async () => {
      await exportService.exportSampleDocument();

      expect(mockWorksheet.columns).toEqual([
        { key: 'id', width: 5 },
        { key: 'Specification', width: 32 },
        { key: 'DFOW', width: 32 },
      ]);
    });
  });

  describe('exportSampleLocationDocument', () => {
    it('should create a location template with instructions', async () => {
      const workbook = await exportService.exportSampleLocationDocument();

      expect(workbook).toBeDefined();
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Location');
      expect(mockWorksheet.mergeCells).toHaveBeenCalledWith('A1:P1');
    });

    it('should set correct column headers', async () => {
      await exportService.exportSampleLocationDocument();

      expect(mockWorksheet.getRow).toHaveBeenCalledWith(2);
      expect(mockWorksheet.columns).toEqual([
        { width: 32 },
        { width: 32 },
        { width: 32 },
        { width: 32 }
      ]);
    });

    it('should add sample location data', async () => {
      await exportService.exportSampleLocationDocument();

      // Should add 18 rows of sample data
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(18);
    });
  });

  describe('sampleDeliveryRequestTemplate', () => {
    it('should create a delivery request template with all dropdowns', async () => {
      const workbook = await exportService.sampleDeliveryRequestTemplate(mockReq);

      expect(workbook).toBeDefined();
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Booking', { state: 'hidden' });
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Delivery Booking');
    });

    it('should call all required services for dropdown data', async () => {
      await exportService.sampleDeliveryRequestTemplate(mockReq);

      expect(gateService.gatesForBulkUploadDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(equipmentService.equipmentsForBulkUploadDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(locationService.getDropdownValuesForLocation).toHaveBeenCalledWith(mockReq);
      expect(companyService.dfowAndCompanyForBulkUploadDeliveryRequest).toHaveBeenCalledWith(mockReq);
      expect(memberService.membersForBulkUploadDeliveryRequest).toHaveBeenCalledWith(mockReq);
    });

    it('should set correct column headers and structure', async () => {
      await exportService.sampleDeliveryRequestTemplate(mockReq);

      expect(mockWorksheet.mergeCells).toHaveBeenCalledWith('A1:P1');
      expect(mockWorksheet.columns).toEqual([
        { key: 'id', width: 5 },
        { key: 'Description', width: 32 },
        { key: 'Responsible_Company', width: 32 },
        { key: 'Definable_Feature_of_Work', width: 32 },
        { key: 'Responsible_Person', width: 32 },
        { key: 'Is_Escort_Needed', width: 32 },
        { key: 'Date', width: 32 },
        { key: 'From_time', width: 32 },
        { key: 'To_time', width: 32 },
        { key: 'Gate', width: 32 },
        { key: 'Equipment', width: 32 },
        { key: 'Picking_From', width: 32 },
        { key: 'Picking_To', width: 32 },
        { key: 'Vehicle_Detail', width: 32 },
        { key: 'Additional_Notes', width: 32 },
        { key: 'Location', width: 32 },
      ]);
    });

    it('should add rows with data validation', async () => {
      await exportService.sampleDeliveryRequestTemplate(mockReq);

      // Should add 499 rows (from 2 to 500)
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(499);
    });

    it('should handle service errors gracefully', async () => {
      gateService.gatesForBulkUploadDeliveryRequest.mockRejectedValue(new Error('Service error'));

      await expect(exportService.sampleDeliveryRequestTemplate(mockReq)).rejects.toThrow('Service error');
    });
  });

  describe('sampleCompanyTemplate', () => {
    it('should create a company template with DFOW dropdown', async () => {
      const workbook = await exportService.sampleCompanyTemplate(mockReq);

      expect(workbook).toBeDefined();
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Booking', { state: 'hidden' });
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Company');
    });

    it('should call company service for DFOW data', async () => {
      await exportService.sampleCompanyTemplate(mockReq);

      expect(companyService.dfowAndCompanyForBulkUploadDeliveryRequest).toHaveBeenCalledWith(mockReq);
    });

    it('should set correct column headers for company template', async () => {
      await exportService.sampleCompanyTemplate(mockReq);

      expect(mockWorksheet.mergeCells).toHaveBeenCalledWith('A1:K1');
      expect(mockWorksheet.columns).toEqual([
        { key: 'id', width: 5 },
        { key: 'Company_Name', width: 32 },
        { key: 'Definable_Feature_of_Work', width: 32 },
        { key: 'Address_Line_1', width: 32 },
        { key: 'Address_Line_2', width: 32 },
        { key: 'Country', width: 32 },
        { key: 'State', width: 32 },
        { key: 'City', width: 32 },
        { key: 'Zipcode', width: 32 },
        { key: 'Website', width: 32 },
        { key: 'Additional_Notes', width: 32 },
      ]);
    });

    it('should add rows with DFOW validation', async () => {
      await exportService.sampleCompanyTemplate(mockReq);

      // Should add 499 rows (from 2 to 500)
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(499);
    });

    it('should handle company service errors gracefully', async () => {
      companyService.dfowAndCompanyForBulkUploadDeliveryRequest.mockRejectedValue(new Error('Company service error'));

      await expect(exportService.sampleCompanyTemplate(mockReq)).rejects.toThrow('Company service error');
    });
  });

  describe('Error Handling', () => {
    it('should handle empty service responses', async () => {
      gateService.gatesForBulkUploadDeliveryRequest.mockResolvedValue([]);
      equipmentService.equipmentsForBulkUploadDeliveryRequest.mockResolvedValue([]);
      locationService.getDropdownValuesForLocation.mockResolvedValue([]);
      companyService.dfowAndCompanyForBulkUploadDeliveryRequest.mockResolvedValue({
        newCompanyList: [],
        defineRecord: [],
      });
      memberService.membersForBulkUploadDeliveryRequest.mockResolvedValue([]);

      const workbook = await exportService.sampleDeliveryRequestTemplate(mockReq);
      expect(workbook).toBeDefined();
    });

    it('should handle undefined service responses', async () => {
      gateService.gatesForBulkUploadDeliveryRequest.mockResolvedValue(undefined);
      equipmentService.equipmentsForBulkUploadDeliveryRequest.mockResolvedValue(undefined);
      locationService.getDropdownValuesForLocation.mockResolvedValue(undefined);
      companyService.dfowAndCompanyForBulkUploadDeliveryRequest.mockResolvedValue({
        newCompanyList: undefined,
        defineRecord: undefined,
      });
      memberService.membersForBulkUploadDeliveryRequest.mockResolvedValue(undefined);

      // This should not throw an error, but handle gracefully
      await expect(exportService.sampleDeliveryRequestTemplate(mockReq)).rejects.toThrow();
    });
  });
});
