const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { inspectionController } = require('../controllers');
const { inspectionValidation } = require('../middlewares/validations');
const upload = multer({ dest: 'uploads/' }); // NOSONAR
const cacheMiddleware = require('../middlewares/cacheMiddleware');

const inspectionRoute = {
    get router() {
        const router = Router();
        router.post(
            '/new_request',
            validate(inspectionValidation.newRequest, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.newRequest,
        );
        router.post(
            '/edit_request',
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.editRequest,
        );
        router.post(
            '/edit_queued_request',
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.editRequest,
        );
        router.post(
            '/update_to_current_NDR',
            validate(
                inspectionValidation.updateQueuedRequest,
                { keyByField: true },
                { abortEarly: false },
            ),
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.editRequest,
        );
        router.post(
            '/update_status',
            validate(inspectionValidation.updateNDRStatus, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.updateNDRStatus,
        );
        router.post('/update_existing_status', inspectionController.updateDeliveredStatus);
        router.post(
            '/list_NDR/:ProjectId/:pageSize/:pageNo/:void',
            validate(inspectionValidation.listNDR, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            cacheMiddleware.cacheInspectionNDRList(),
            inspectionController.listNDR,
        );
        router.post(
            '/list_Queued_NDR/:ProjectId/:pageSize/:pageNo/:void',
            validate(inspectionValidation.listNDR, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            cacheMiddleware.cacheInspectionNDRList(),
            inspectionController.listNDR,
        );
        router.get(
            '/get_single_NDR/:inspectionRequestId/?:ParentCompanyId',
            passportConfig.isAuthenticated,
            cacheMiddleware.cacheInspectionSingleNDR(),
            inspectionController.getNDRData,
        );
        router.get(
            '/get_user_role/:ProjectId/?:ParentCompanyId',
            validate(inspectionValidation.getMemberData, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            inspectionController.getMemberData,
        );
        router.post(
            '/sample_inspection_request_template/?:ProjectId/?:ParentCompanyId',
            validate(
                inspectionValidation.uploadBulkNDRTemplate,
                { keyByField: true },
                { abortEarly: false },
            ),
            passportConfig.isAuthenticated,
            inspectionController.sampleBulkinspectionRequestTemplate,
        );
        router.post(
            '/bulk_upload_inspection_request/?:ProjectId/?:ParentCompanyId',
            upload.single('inspection_request'),
            validate(
                inspectionValidation.bulkUploadinspectionRequest,
                { keyByField: true },
                { abortEarly: false },
            ),
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.bulkUploadinspectionRequest,
        );
        router.post(
            '/delete_queued_Ndr',
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.deleteQueuedNdr,
        );
        router.post(
            '/edit_multiple_request',
            passportConfig.isAuthenticated,
            cacheMiddleware.invalidateAfterAllCalendarMutation(),
            cacheMiddleware.invalidateAfterInspectionNDRListMutation(),
            cacheMiddleware.invalidateAfterInspectionSingleNDRMutation(),
            cacheMiddleware.invalidateAfterHistoryMutation(),
            inspectionController.editMultipleinspectionRequest,
        );
        router.get(
            '/get_last_inspection_request_id/:ProjectId/?:ParentCompanyId',
            passportConfig.isAuthenticated,
            inspectionController.getLastinspectionRequestId,
        );
        router.post('/decrypt', passportConfig.isAuthenticated, inspectionController.decryption);

        router.get(
            '/readall_notification',
            passportConfig.isAuthenticated,
            inspectionController.setReadAllNotification,
        );
        router.get(
            '/markall_notification/:ProjectId',
            passportConfig.isAuthenticated,
            inspectionController.markAllNotification,
        );
        return router;
    },
};
module.exports = inspectionRoute;
