const axios = require('axios');
const domainHelper = require('../domainHelper');
const db = require('../../models');
const dynamicModels = require('../dynamicSchemaModels');

jest.mock('axios');
jest.mock('../../models', () => ({
  syncToSchema: jest.fn(),
  DynamicProject: 'mockDynamicProject',
  DynamicMember: 'mockDynamicMember',
  DynamicCompany: 'mockDynamicCompany',
  DynamicUser: 'mockDynamicUser',
  DynamicRole: 'mockDynamicRole',
  DynamicDeliverDefineWork: 'mockDynamicDeliverDefineWork',
  DynamicCompanyDefine: 'mockDynamicCompanyDefine',
  DynamicParentCompany: 'mockDynamicParentCompany',
  DynamicGates: 'mockDynamicGates',
  DynamicEquipments: 'mockDynamicEquipments',
  DynamicDeliveryRequest: 'mockDynamicDeliveryRequest',
  DynamicInspectionRequest: 'mockDynamicInspectionRequest',
  DynamicInspectionHistory: 'mockDynamicInspectionHistory',
  DynamicInspectionPersonNotification: 'mockDynamicInspectionPersonNotification',
  DynamicInspectionCompany: 'mockDynamicInspectionCompany',
  DynamicInspectionComment: 'mockDynamicInspectionComment',
  DynamicInspectionGate: 'mockDynamicInspectionGate',
  DynamicInspectionEquipment: 'mockDynamicInspectionEquipment',
  DynamicInspectionPerson: 'mockDynamicInspectionPerson',
  DynamicVoidList: 'mockDynamicVoidList',
  DynamicDeliverCompany: 'mockDynamicDeliverCompany',
  DynamicDeliverGate: 'mockDynamicDeliverGate',
  DynamicDeliveryPerson: 'mockDynamicDeliveryPerson',
  DynamicNotification: 'mockDynamicNotification',
  DynamicDeliverEquipment: 'mockDynamicDeliverEquipment',
  DynamicConcreteEquipment: 'mockDynamicConcreteEquipment',
  DynamicCraneGate: 'mockDynamicCraneGate',
  DynamicConcreteGate: 'mockDynamicConcreteGate',
  DynamicDeliverDefine: 'mockDynamicDeliverDefine',
  DynamicDeliverHistory: 'mockDynamicDeliverHistory',
  DynamicDeliverAttachement: 'mockDynamicDeliverAttachement',
  DynamicDeliverComment: 'mockDynamicDeliverComment',
  DynamicDeviceToken: 'mockDynamicDeviceToken',
  DynamicDeliveryPersonNotification: 'mockDynamicDeliveryPersonNotification',
  DynamicProjectBillingHistories: 'mockDynamicProjectBillingHistories',
  DynamicCraneRequest: 'mockDynamicCraneRequest',
  CraneRequestCompany: 'mockCraneRequestCompany',
  CraneRequestDefinableFeatureOfWork: 'mockCraneRequestDefinableFeatureOfWork',
  CraneRequestEquipment: 'mockCraneRequestEquipment',
  CraneRequestResponsiblePerson: 'mockCraneRequestResponsiblePerson',
  PresetEquipmentType: 'mockPresetEquipmentType',
  CraneRequestAttachment: 'mockCraneRequestAttachment',
  CraneRequestComment: 'mockCraneRequestComment',
  CraneRequestHistory: 'mockCraneRequestHistory',
  CalendarSetting: 'mockCalendarSetting',
  TimeZone: 'mockTimeZone',
  NotificationPreferenceItem: 'mockNotificationPreferenceItem',
  NotificationPreference: 'mockNotificationPreference',
  DigestNotification: 'mockDigestNotification',
  ConcreteRequestResponsiblePerson: 'mockConcreteRequestResponsiblePerson',
  ConcreteRequestAttachment: 'mockConcreteRequestAttachment',
  ConcreteRequestComment: 'mockConcreteRequestComment',
  ConcreteRequestHistory: 'mockConcreteRequestHistory',
  ConcreteRequest: 'mockConcreteRequest',
  ConcreteRequestCompany: 'mockConcreteRequestCompany',
  ConcreteLocation: 'mockConcreteLocation',
  ConcreteMixDesign: 'mockConcreteMixDesign',
  ConcretePumpSize: 'mockConcretePumpSize',
  ConcreteRequestLocation: 'mockConcreteRequestLocation',
  ConcreteRequestMixDesign: 'mockConcreteRequestMixDesign',
  ConcreteRequestPumpSize: 'mockConcreteRequestPumpSize',
  SchedulerReport: 'mockSchedulerReport',
  ProjectSettings: 'mockProjectSettings',
  SchedulerDateRange: 'mockSchedulerDateRange',
  RequestRecurrenceSeries: 'mockRequestRecurrenceSeries',
  Locations: 'mockLocations',
  LocationNotificationPreferences: 'mockLocationNotificationPreferences',
  BookingTemplates: 'mockBookingTemplates'
}));
jest.mock('../dynamicSchemaModels', () => ({ domain: jest.fn() }));

describe('domainHelper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.GODADDY_API_URL = 'https://api.godaddy.com';
    process.env.SERVER_IP = '*******';
    process.env.SSO_KEY = 'key';
    process.env.NODE_ENV = 'test';
  });

  it('domainCreation should PATCH domain and return data', async () => {
    axios.request.mockResolvedValue({ data: { success: true } });
    const result = await domainHelper.domainCreation('foo');
    expect(axios.request).toHaveBeenCalledWith({
      url: 'https://api.godaddy.com',
      method: 'PATCH',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'sso-key key',
      },
      data: [
        {
          data: '*******',
          name: 'foo-test',
          ttl: 3600,
          type: 'A',
        },
      ],
    });
    expect(result).toEqual({ success: true });
  });

  it('domainCreation should handle error', async () => {
    axios.request.mockRejectedValue(new Error('fail'));
    const result = await domainHelper.domainCreation('foo');
    expect(result).toBeUndefined();
  });

  it('checkCurrentDomain should call syncToSchema and dynamicModels.domain when domain is not public', async () => {
    const req = {};
    const next = jest.fn();
    domainHelper.currentDomain = jest.fn((req, cb) => cb('bar'));
    await domainHelper.checkCurrentDomain(req, {}, next);
    expect(req.domainName).toBe('bar');
    expect(db.syncToSchema).toHaveBeenCalledWith('bar');
    expect(dynamicModels.domain).toHaveBeenCalledWith('bar');
    expect(next).toHaveBeenCalled();
  });

  it('checkCurrentDomain should call next immediately when domain is public', async () => {
    const req = {};
    const next = jest.fn();
    domainHelper.currentDomain = jest.fn((req, cb) => cb('public'));
    await domainHelper.checkCurrentDomain(req, {}, next);
    expect(req.domainName).toBe('public');
    expect(db.syncToSchema).not.toHaveBeenCalled();
    expect(dynamicModels.domain).not.toHaveBeenCalled();
    expect(next).toHaveBeenCalled();
  });

  it('getDynamicModel should use dynamic models when checkDomain returns data', async () => {
    const mockDbResult = {
      DynamicProject: 'dynamicProject',
      DynamicMember: 'dynamicMember',
      DynamicCompany: 'dynamicCompany',
      DynamicUser: 'dynamicUser',
      DynamicRole: 'dynamicRole',
      DynamicDeliverDefineWork: 'dynamicDeliverDefineWork',
      DynamicCompanyDefine: 'dynamicCompanyDefine',
      DynamicParentCompany: 'dynamicParentCompany',
      DynamicGates: 'dynamicGates',
      DynamicEquipments: 'dynamicEquipments',
      DynamicDeliveryRequest: 'dynamicDeliveryRequest',
      DynamicInspectionRequest: 'dynamicInspectionRequest',
      DynamicInspectionHistory: 'dynamicInspectionHistory',
      DynamicInspectionPersonNotification: 'dynamicInspectionPersonNotification',
      DynamicInspectionCompany: 'dynamicInspectionCompany',
      DynamicInspectionComment: 'dynamicInspectionComment',
      DynamicInspectionGate: 'dynamicInspectionGate',
      DynamicInspectionEquipment: 'dynamicInspectionEquipment',
      DynamicInspectionPerson: 'dynamicInspectionPerson',
      DynamicVoidList: 'dynamicVoidList',
      DynamicDeliverCompany: 'dynamicDeliverCompany',
      DynamicDeliverGate: 'dynamicDeliverGate',
      DynamicDeliveryPerson: 'dynamicDeliveryPerson',
      DynamicNotification: 'dynamicNotification',
      DynamicDeliverEquipment: 'dynamicDeliverEquipment',
      DynamicConcreteEquipment: 'dynamicConcreteEquipment',
      DynamicCraneGate: 'dynamicCraneGate',
      DynamicConcreteGate: 'dynamicConcreteGate',
      DynamicDeliverDefine: 'dynamicDeliverDefine',
      DynamicDeliverHistory: 'dynamicDeliverHistory',
      DynamicDeliverAttachement: 'dynamicDeliverAttachement',
      DynamicDeliverComment: 'dynamicDeliverComment',
      DynamicDeviceToken: 'dynamicDeviceToken',
      DynamicDeliveryPersonNotification: 'dynamicDeliveryPersonNotification',
      DynamicProjectBillingHistories: 'dynamicProjectBillingHistories',
      DynamicCraneRequest: 'dynamicCraneRequest',
      CraneRequestCompany: 'craneRequestCompany',
      CraneRequestDefinableFeatureOfWork: 'craneRequestDefinableFeatureOfWork',
      CraneRequestEquipment: 'craneRequestEquipment',
      CraneRequestResponsiblePerson: 'craneRequestResponsiblePerson',
      PresetEquipmentType: 'presetEquipmentType',
      CraneRequestAttachment: 'craneRequestAttachment',
      CraneRequestComment: 'craneRequestComment',
      CraneRequestHistory: 'craneRequestHistory',
      CalendarSetting: 'calendarSetting',
      TimeZone: 'timeZone',
      NotificationPreferenceItem: 'notificationPreferenceItem',
      NotificationPreference: 'notificationPreference',
      DigestNotification: 'digestNotification',
      ConcreteRequestResponsiblePerson: 'concreteRequestResponsiblePerson',
      ConcreteRequestAttachment: 'concreteRequestAttachment',
      ConcreteRequestComment: 'concreteRequestComment',
      ConcreteRequestHistory: 'concreteRequestHistory',
      ConcreteRequest: 'concreteRequest',
      ConcreteRequestCompany: 'concreteRequestCompany',
      ConcreteLocation: 'concreteLocation',
      ConcreteMixDesign: 'concreteMixDesign',
      ConcretePumpSize: 'concretePumpSize',
      ConcreteRequestLocation: 'concreteRequestLocation',
      ConcreteRequestMixDesign: 'concreteRequestMixDesign',
      ConcreteRequestPumpSize: 'concreteRequestPumpSize',
      SchedulerReport: 'schedulerReport',
      ProjectSettings: 'projectSettings',
      SchedulerDateRange: 'schedulerDateRange',
      RequestRecurrenceSeries: 'requestRecurrenceSeries',
      Locations: 'locations',
      LocationNotificationPreferences: 'locationNotificationPreferences',
      BookingTemplates: 'bookingTemplates'
    };

    domainHelper.checkDomain = jest.fn().mockResolvedValue(mockDbResult);
    const result = await domainHelper.getDynamicModel('foo');
    expect(domainHelper.checkDomain).toHaveBeenCalledWith('foo');
    expect(result.Project).toBe('dynamicProject');
    expect(result.Member).toBe('dynamicMember');
    expect(result.Company).toBe('dynamicCompany');
    expect(result.User).toBe('dynamicUser');
  });

  it('getDynamicModel should use static models when checkDomain returns null', async () => {
    domainHelper.checkDomain = jest.fn().mockResolvedValue(null);
    const result = await domainHelper.getDynamicModel('foo');
    expect(domainHelper.checkDomain).toHaveBeenCalledWith('foo');
    // When dbResult is null, it should use the static models from require('../models')
    expect(result).toBeDefined();
    expect(result.Project).toBeDefined();
    expect(result.Member).toBeDefined();
    expect(result.Company).toBeDefined();
    expect(result.User).toBeDefined();
  });

  it('returnProjectModel should return static project models', async () => {
    const result = await domainHelper.returnProjectModel();
    expect(result).toBeDefined();
    expect(result.Project).toBeDefined();
    expect(result.User).toBeDefined();
    expect(result.Member).toBeDefined();
    expect(result.Company).toBeDefined();
    expect(result.ParentCompany).toBeDefined();
  });

  it('checkDomain should call dynamicModels.domain and return db when domainName is valid', async () => {
    const result = await domainHelper.checkDomain('testdomain');
    expect(dynamicModels.domain).toHaveBeenCalledWith('testdomain');
    expect(result).toBe(db);
  });

  it('checkDomain should return null when domainName is null', async () => {
    const result = await domainHelper.checkDomain(null);
    expect(dynamicModels.domain).not.toHaveBeenCalled();
    expect(result).toBeNull();
  });

  it('checkDomain should return null when domainName is undefined', async () => {
    const result = await domainHelper.checkDomain(undefined);
    expect(dynamicModels.domain).not.toHaveBeenCalled();
    expect(result).toBeNull();
  });

  it('currentDomain should extract domain from origin and call dynamicModels.domain', async () => {
    const req = {
      get: jest.fn().mockReturnValue('https://testdomain-env.example.com')
    };
    const next = jest.fn();

    await domainHelper.currentDomain(req, {}, next);

    expect(req.domainName).toBe('testdomain');
    expect(dynamicModels.domain).toHaveBeenCalledWith('testdomain');
    expect(next).toHaveBeenCalled();
  });

  it('currentDomain should call next without setting domain when origin format is invalid', async () => {
    const req = {
      get: jest.fn().mockReturnValue('https://invalid-format-too-many-parts.example.com')
    };
    const next = jest.fn();

    await domainHelper.currentDomain(req, {}, next);

    expect(req.domainName).toBeUndefined();
    expect(dynamicModels.domain).not.toHaveBeenCalled();
    expect(next).toHaveBeenCalled();
  });

  it('currentDomain should call next when no origin is provided', async () => {
    const req = {
      get: jest.fn().mockReturnValue(undefined)
    };
    const next = jest.fn();

    await domainHelper.currentDomain(req, {}, next);

    expect(req.domainName).toBeUndefined();
    expect(dynamicModels.domain).not.toHaveBeenCalled();
    expect(next).toHaveBeenCalled();
  });

  it('currentDomain should call next when origin does not contain //', async () => {
    const req = {
      get: jest.fn().mockReturnValue('invalid-origin-format')
    };
    const next = jest.fn();

    await domainHelper.currentDomain(req, {}, next);

    expect(req.domainName).toBeUndefined();
    expect(dynamicModels.domain).not.toHaveBeenCalled();
    expect(next).toHaveBeenCalled();
  });
});
