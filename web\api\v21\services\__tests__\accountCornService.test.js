const { describe, it, expect, beforeEach } = require('@jest/globals');
const accountCornService = require('../accountCornService');
const db = require('../../models');
const domainHelper = require('../../helpers/domainHelper');
const dynamicModels = require('../../helpers/dynamicSchemaModels');
const MAILER = require('../../mailer');
const {
  Enterprise,
  Member,
  User,
  Project,
  Company,
  ParentCompany,
  DeliveryPersonNotification,
  DeliverDefineWork,
  Gates,
  Equipments,
  DeliveryPerson,
  DeliverHistory,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  DeliverDefine,
  DeliverComment,
  DeliverAttachement,
  Notification,
  VoidList,
  CompanyDefine,
} = require('../../models');

// Mock all external dependencies
jest.mock('../../models', () => ({
  Enterprise: {
    findAll: jest.fn(),
    update: jest.fn(),
  },
  Member: {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
  },
  Project: {
    findAll: jest.fn(),
    update: jest.fn(),
  },
  Company: {
    findOne: jest.fn(),
    findAll: jest.fn(),
  },
  ParentCompany: {
    findOne: jest.fn(),
  },
  DeliverDefineWork: {
    findAll: jest.fn(),
    findOne: jest.fn(),
  },
  DeliveryRequest: {
    findAll: jest.fn(),
  },
  DeliverGate: {
    findAll: jest.fn(),
  },
  DeliverEquipment: {
    findAll: jest.fn(),
  },
  DeliverCompany: {
    findAll: jest.fn(),
  },
  DeliverAttachement: {
    findAll: jest.fn(),
  },
  DeliverComment: {
    findAll: jest.fn(),
  },
  DeliverDefine: {
    findAll: jest.fn(),
  },
  VoidList: {
    findAll: jest.fn(),
  },
  Notification: {
    findAll: jest.fn(),
  },
  Gates: {
    findAll: jest.fn(),
  },
  CompanyDefine: {
    findAll: jest.fn(),
  },
  Equipments: {
    findAll: jest.fn(),
  },
  DeliverHistory: {
    findAll: jest.fn(),
  },
  DeliveryPerson: {
    findAll: jest.fn(),
  },
  DeliveryPersonNotification: {
    findAll: jest.fn(),
  },
  syncToSchema: jest.fn(),
  DynamicUser: {
    findOne: jest.fn(),
    createInstance: jest.fn(),
    update: jest.fn(),
  },
  DynamicMember: {
    findOne: jest.fn(),
    createInstance: jest.fn(),
  },
  DynamicProject: {
    findOne: jest.fn(),
    createInstance: jest.fn(),
  },
  DynamicCompany: {
    findOne: jest.fn(),
    createInstance: jest.fn(),
  },
  DynamicParentCompany: {
    findOne: jest.fn(),
    create: jest.fn(),
    createInstance: jest.fn(),
  },
  DynamicRole: {
    findOne: jest.fn(),
    bulkCreateRoles: jest.fn(),
  },
  DynamicEquipments: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicGates: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverDefineWork: {
    findOne: jest.fn(),
    createInstance: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliveryRequest: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicNotification: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicVoidList: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverComment: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverAttachement: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverGate: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverCompany: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverEquipment: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliveryPerson: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverHistory: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliverDefine: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicCompanyDefine: {
    findOne: jest.fn(),
    create: jest.fn(),
  },
  DynamicDeliveryPersonNotification: {
    create: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  domainCreation: jest.fn(),
}));

jest.mock('../../mailer', () => ({
  sendMail: jest.fn(),
}));

jest.mock('../../helpers/generatePassword', () => ({
  generatePassword: jest.fn(() => 'testPassword123'),
}));

jest.mock('../../helpers/dynamicSchemaModels', () => ({
  domain: jest.fn(),
}));

jest.mock('../password', () => ({
  bcryptPassword: jest.fn((_, callback) => callback('hashedPassword')),
}));

describe('accountCornService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('createSchemas', () => {
    it('should handle errors during schema creation', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            phoneNumber: '**********',
          },
          Projects: [
            {
              id: 1,
              projectName: 'Test Project',
              ParentCompanyId: 1,
            },
          ],
          Members: [{ id: 1 }],
        },
      ];

      Enterprise.findAll.mockResolvedValue(mockAccountDetail);
      domainHelper.domainCreation.mockRejectedValue(new Error('Domain creation failed'));

      await expect(accountCornService.createSchemas()).rejects.toThrow('Domain creation failed');
    });

    it('should handle empty account details', async () => {
      Enterprise.findAll.mockResolvedValue([]);
      const result = await accountCornService.createSchemas();
      expect(result).toBeNull();
      expect(Enterprise.findAll).toHaveBeenCalled();
    });

    it('should create schemas when account details exist', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
            publicSchemaId: 1,
          },
          Projects: [{ id: 1, projectName: 'Test Project', ParentCompanyId: 1, createdBy: 1 }],
          Members: [],
        },
      ];

      Enterprise.findAll.mockResolvedValue(mockAccountDetail);
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({
        id: 1,
        email: '<EMAIL>',
        publicSchemaId: 1,
      });
      Project.update.mockResolvedValue([1]);
      domainHelper.domainCreation.mockResolvedValue(true);

      const result = await accountCornService.createSchemas();
      expect(result).toBeTruthy();
      expect(Enterprise.findAll).toHaveBeenCalled();
      expect(domainHelper.domainCreation).toHaveBeenCalled();
    });

    it('should handle missing user data in account details', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          Projects: [],
          Members: [],
        },
      ];

      Enterprise.findAll.mockResolvedValue(mockAccountDetail);
      const result = await accountCornService.createSchemas();
      expect(result).toBeNull();
      expect(Enterprise.findAll).toHaveBeenCalled();
    });

    it('should handle missing project data in account details', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
            publicSchemaId: 1,
          },
          Projects: [],
          Members: [],
        },
      ];

      Enterprise.findAll.mockResolvedValue(mockAccountDetail);
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({
        id: 1,
        email: '<EMAIL>',
        publicSchemaId: 1,
      });

      const result = await accountCornService.createSchemas();
      expect(result).toBeTruthy();
      expect(Enterprise.findAll).toHaveBeenCalled();
      expect(domainHelper.domainCreation).toHaveBeenCalled();
    });

    it('should handle database errors during schema creation', async () => {
      Enterprise.findAll.mockRejectedValue(new Error('Database connection failed'));
      await expect(accountCornService.createSchemas()).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('createAccountAdmin', () => {
    it('should create account admin successfully', async () => {
      // Mock data
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
          },
          Projects: [
            {
              id: 1,
              projectName: 'Test Project',
              ParentCompanyId: 1,
            },
          ],
        },
      ];

      // Setup mocks
      domainHelper.domainCreation.mockResolvedValue(true);
      db.syncToSchema.mockResolvedValue(true);
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });

      // Execute
      await accountCornService.createAccountAdmin(mockAccountDetail, 0);

      // Verify
      expect(domainHelper.domainCreation).toHaveBeenCalled();
      expect(db.syncToSchema).toHaveBeenCalled();
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
    });

    it('should handle errors during account admin creation', async () => {
      // Mock data
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
          },
        },
      ];

      // Setup mocks to throw error
      domainHelper.domainCreation.mockRejectedValue(new Error('Domain creation failed'));

      // Execute and verify
      await expect(accountCornService.createAccountAdmin(mockAccountDetail, 0)).rejects.toThrow(
        'Domain creation failed',
      );
    });

    it('should handle missing user data', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: null,
          Projects: [],
        },
      ];

      await expect(accountCornService.createAccountAdmin(mockAccountDetail, 0)).rejects.toThrow();
    });

    it('should handle missing project data', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
          },
          Projects: [],
        },
      ];

      Company.findOne.mockResolvedValue({
        id: 1,
        createdBy: 1,
      });

      ParentCompany.findOne.mockResolvedValue({
        id: 1,
        emailDomainName: 'test.com',
      });

      await accountCornService.createAccountAdmin(mockAccountDetail, 0);
      expect(Company.findOne).toHaveBeenCalled();
    });

    it('should handle database errors during account admin creation', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
            email: '<EMAIL>',
          },
        },
      ];
      User.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.createAccountAdmin(mockAccountDetail, 0)).rejects.toThrow(
        'Database error',
      );
    });

    it('should handle missing email in user data', async () => {
      const mockAccountDetail = [
        {
          id: 1,
          name: 'test-enterprise',
          User: {
            id: 1,
          },
        },
      ];
      await expect(accountCornService.createAccountAdmin(mockAccountDetail, 0)).rejects.toThrow();
    });
  });

  describe('createUser', () => {
    it('should create new user successfully', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.createUser({ UserId: 1 });

      // Verify
      expect(User.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
      expect(result).toEqual({ id: 1 });
    });

    it('should return existing user if found', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.createUser({ UserId: 1 });

      // Verify
      expect(db.DynamicUser.findOne).toHaveBeenCalled();
      expect(db.DynamicUser.createInstance).not.toHaveBeenCalled();
      expect(result).toEqual({ id: 1 });
    });

    it('should handle errors during user creation', async () => {
      // Setup mocks to throw error
      User.findOne.mockRejectedValue(new Error('User lookup failed'));

      // Execute and verify
      await expect(accountCornService.createUser({ UserId: 1 })).rejects.toThrow(
        'User lookup failed',
      );
    });

    it('should handle database errors during user creation', async () => {
      const mockUser = { id: 1 };
      User.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.createUser(mockUser)).rejects.toThrow('Database error');
    });

    it('should handle missing user ID', async () => {
      const mockUser = {};
      await expect(accountCornService.createUser(mockUser)).rejects.toThrow();
    });
  });

  describe('createMember', () => {
    it('should create new member successfully', async () => {
      // Mock data
      const mockMember = {
        id: 1,
        UserId: 1,
        ProjectId: 1,
        CompanyId: 1,
        RoleId: 1,
        firstName: 'Test',
      };

      // Setup mocks
      db.DynamicMember.findOne.mockResolvedValue(null);
      db.DynamicMember.createInstance.mockResolvedValue({ id: 1 });
      db.DynamicCompany.findOne.mockResolvedValue({ id: 1 });
      db.DynamicUser.findOne.mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });

      // Execute
      await accountCornService.createMember(
        [mockMember],
        0,
        { id: 1 },
        { id: 1, firstName: 'Admin' },
        { id: 1 },
        1,
        { id: 1 },
      );

      // Verify
      expect(db.DynamicMember.createInstance).toHaveBeenCalled();
    });

    it('should handle empty member list', async () => {
      // Execute
      await accountCornService.createMember([], 0, { id: 1 }, { id: 1 }, { id: 1 }, 1, { id: 1 });

      // Verify
      expect(db.DynamicMember.createInstance).not.toHaveBeenCalled();
    });

    it('should handle existing member', async () => {
      const mockMemberList = [
        {
          id: 1,
          UserId: 1,
          RoleId: 1,
        },
      ];

      const mockNewParent = {
        id: 1,
      };

      const mockNewUser = {
        id: 1,
      };

      const mockNewProject = {
        id: 1,
      };

      const mockCreatedBy = 1;

      const mockEnterprise = {
        id: 1,
      };

      db.DynamicMember.findOne.mockResolvedValue({
        id: 1,
        UserId: 1,
      });

      await accountCornService.createMember(
        mockMemberList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
        mockCreatedBy,
        mockEnterprise,
      );

      expect(db.DynamicMember.findOne).toHaveBeenCalled();
    });

    it('should handle new member creation', async () => {
      const mockMemberList = [
        {
          id: 1,
          UserId: 1,
          RoleId: 1,
        },
      ];

      const mockNewParent = {
        id: 1,
      };

      const mockNewUser = {
        id: 1,
      };

      const mockNewProject = {
        id: 1,
      };

      const mockCreatedBy = 1;

      const mockEnterprise = {
        id: 1,
      };

      db.DynamicMember.findOne.mockResolvedValue(null);
      db.DynamicUser.findOne.mockResolvedValue({
        id: 1,
        email: '<EMAIL>',
      });

      await accountCornService.createMember(
        mockMemberList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
        mockCreatedBy,
        mockEnterprise,
      );

      expect(db.DynamicMember.createInstance).toHaveBeenCalled();
    });

    it('should handle database errors during member creation', async () => {
      const mockMember = { id: 1 };
      Member.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createMember([mockMember], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in member data', async () => {
      const mockMember = {};
      await expect(
        accountCornService.createMember([mockMember], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createProject', () => {
    it('should create project successfully', async () => {
      // Mock data
      const mockProject = {
        id: 1,
        projectName: 'Test Project',
        ParentCompanyId: 1,
        createdBy: 1,
      };

      // Setup mocks
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      db.DynamicParentCompany.findOne.mockResolvedValue({ id: 1 });
      db.DynamicUser.findOne.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);

      // Execute
      await accountCornService.createProject([mockProject], 0, { id: 1 }, { id: 1 }, 'password123');

      // Verify
      expect(db.DynamicProject.createInstance).toHaveBeenCalled();
    });

    it('should handle project creation with existing project', async () => {
      // Mock data
      const mockProject = {
        id: 1,
        projectName: 'Test Project',
        ParentCompanyId: 1,
      };

      // Setup mocks
      db.DynamicProject.findOne.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);

      // Execute
      await accountCornService.createProject([mockProject], 0, { id: 1 }, { id: 1 }, 'password123');

      // Verify
      expect(db.DynamicProject.createInstance).not.toHaveBeenCalled();
    });

    it('should handle existing parent company', async () => {
      const mockProjectList = [
        {
          id: 1,
          createdBy: 1,
          ParentCompanyId: 1,
          publicSchemaId: 1,
        },
      ];

      const mockNewUser = {
        id: 1,
        email: '<EMAIL>',
      };

      const mockEnterprise = {
        id: 1,
        name: 'test-enterprise',
      };

      ParentCompany.findOne.mockResolvedValue({
        id: 1,
        emailDomainName: 'test.com',
      });

      db.DynamicParentCompany.findOne.mockResolvedValue({
        id: 1,
        emailDomainName: 'test.com',
      });

      await accountCornService.createProject(
        mockProjectList,
        0,
        mockNewUser,
        mockEnterprise,
        'password',
      );
      expect(db.DynamicParentCompany.findOne).toHaveBeenCalled();
    });

    it('should handle non-existent parent company', async () => {
      const mockProjectList = [
        {
          id: 1,
          createdBy: 1,
          ParentCompanyId: 1,
          publicSchemaId: 1,
        },
      ];

      const mockNewUser = {
        id: 1,
        email: '<EMAIL>',
      };

      const mockEnterprise = {
        id: 1,
        name: 'test-enterprise',
      };

      ParentCompany.findOne.mockResolvedValue({
        id: 1,
        emailDomainName: 'test.com',
      });

      db.DynamicParentCompany.findOne.mockResolvedValue(null);

      await accountCornService.createProject(
        mockProjectList,
        0,
        mockNewUser,
        mockEnterprise,
        'password',
      );
      expect(db.DynamicParentCompany.create).toHaveBeenCalled();
    });

    it('should handle database errors during project creation', async () => {
      const mockProject = { id: 1 };
      Project.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createProject([mockProject], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in project data', async () => {
      const mockProject = {};
      await expect(
        accountCornService.createProject([mockProject], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createCompany', () => {
    it('should create company successfully', async () => {
      // Mock data
      const mockCompany = {
        id: 1,
        name: 'Test Company',
        ParentCompanyId: 1,
        createdBy: 1,
      };

      // Setup mocks
      Company.findOne.mockResolvedValue(mockCompany);
      db.DynamicCompany.findOne.mockResolvedValue(null);
      db.DynamicCompany.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createCompany([mockCompany], 0, { id: 1 }, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicCompany.createInstance).toHaveBeenCalled();
    });

    it('should handle company creation with existing company', async () => {
      // Mock data
      const mockCompany = {
        id: 1,
        name: 'Test Company',
      };

      // Setup mocks
      Company.findOne.mockResolvedValue(mockCompany);
      db.DynamicCompany.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createCompany([mockCompany], 0, { id: 1 }, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicCompany.createInstance).not.toHaveBeenCalled();
    });

    it('should handle company creation with existing data', async () => {
      const mockCompanyList = [
        {
          id: 1,
          name: 'Test Company',
        },
      ];

      const mockNewParent = {
        id: 1,
      };

      const mockNewUser = {
        id: 1,
      };

      const mockNewProject = {
        id: 1,
      };

      db.DynamicCompany.findOne.mockResolvedValue({
        id: 1,
        name: 'Test Company',
      });

      await accountCornService.createCompany(
        mockCompanyList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
      );

      expect(db.DynamicCompany.findOne).toHaveBeenCalled();
    });

    it('should handle new company creation', async () => {
      const mockCompanyList = [
        {
          id: 1,
          name: 'Test Company',
        },
      ];

      const mockNewParent = {
        id: 1,
      };

      const mockNewUser = {
        id: 1,
      };

      const mockNewProject = {
        id: 1,
      };

      db.DynamicCompany.findOne.mockResolvedValue(null);

      await accountCornService.createCompany(
        mockCompanyList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
      );

      expect(db.DynamicCompany.createInstance).toHaveBeenCalled();
    });

    it('should handle database errors during company creation', async () => {
      const mockCompany = { id: 1 };
      Company.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createCompany([mockCompany], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in company data', async () => {
      const mockCompany = {};
      await expect(
        accountCornService.createCompany([mockCompany], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createEquipment', () => {
    it('should create equipment successfully', async () => {
      // Mock data
      const mockEquipment = {
        id: 1,
        name: 'Test Equipment',
        createdBy: 1,
      };

      // Setup mocks
      db.DynamicEquipments.findOne.mockResolvedValue(null);
      db.DynamicEquipments.create.mockResolvedValue({ id: 1 });
      db.DynamicUser.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createEquipment([mockEquipment], 0, { id: 1 });

      // Verify
      expect(db.DynamicEquipments.create).toHaveBeenCalled();
    });

    it('should handle equipment creation with existing equipment', async () => {
      // Mock data
      const mockEquipment = {
        id: 1,
        name: 'Test Equipment',
      };

      // Setup mocks
      db.DynamicEquipments.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createEquipment([mockEquipment], 0, { id: 1 });

      // Verify
      expect(db.DynamicEquipments.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during equipment creation', async () => {
      const mockEquipment = { id: 1 };
      Equipments.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createEquipment([mockEquipment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in equipment data', async () => {
      const mockEquipment = {};
      await expect(
        accountCornService.createEquipment([mockEquipment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createGate', () => {
    it('should create gate successfully', async () => {
      // Mock data
      const mockGate = {
        id: 1,
        name: 'Test Gate',
        createdBy: 1,
      };

      // Setup mocks
      db.DynamicGates.findOne.mockResolvedValue(null);
      db.DynamicGates.create.mockResolvedValue({ id: 1 });
      db.DynamicUser.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createGate([mockGate], 0, { id: 1 });

      // Verify
      expect(db.DynamicGates.create).toHaveBeenCalled();
    });

    it('should handle gate creation with existing gate', async () => {
      // Mock data
      const mockGate = {
        id: 1,
        name: 'Test Gate',
      };

      // Setup mocks
      db.DynamicGates.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createGate([mockGate], 0, { id: 1 });

      // Verify
      expect(db.DynamicGates.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during gate creation', async () => {
      const mockGate = { id: 1 };
      Gates.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.createGate([mockGate], 0, {}, {}, {}, 1, {})).rejects.toThrow(
        'Database error',
      );
    });

    it('should handle missing required fields in gate data', async () => {
      const mockGate = {};
      await expect(
        accountCornService.createGate([mockGate], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliveryWork', () => {
    it('should create delivery work successfully', async () => {
      // Mock data
      const mockDeliveryWork = {
        id: 1,
        DFOW: 'TEST-DFOW-001',
        ProjectId: 1,
      };

      // Setup mocks
      db.DynamicDeliverDefineWork.findOne.mockResolvedValue(null);
      db.DynamicDeliverDefineWork.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliveryWork([mockDeliveryWork], 0, { id: 1 });

      // Verify
      expect(db.DynamicDeliverDefineWork.createInstance).toHaveBeenCalled();
    });

    it('should handle delivery work creation with existing work', async () => {
      // Mock data
      const mockDeliveryWork = {
        id: 1,
        DFOW: 'TEST-DFOW-001',
      };

      // Setup mocks
      db.DynamicDeliverDefineWork.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliveryWork([mockDeliveryWork], 0, { id: 1 });

      // Verify
      expect(db.DynamicDeliverDefineWork.createInstance).not.toHaveBeenCalled();
    });

    it('should handle database errors during delivery work creation', async () => {
      const mockDeliveryWork = { id: 1 };
      DeliverDefineWork.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliveryWork([mockDeliveryWork], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in delivery work data', async () => {
      const mockDeliveryWork = {};
      await expect(
        accountCornService.createDeliveryWork([mockDeliveryWork], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliveryRequest', () => {
    it('should create delivery request successfully', async () => {
      // Mock data
      const mockDeliveryRequest = {
        id: 1,
        ProjectId: 1,
        approvedBy: 1,
        createdBy: 1,
      };

      // Setup mocks
      db.DynamicDeliveryRequest.findOne.mockResolvedValue(null);
      db.DynamicDeliveryRequest.create.mockResolvedValue({ id: 1 });
      db.DynamicUser.findOne.mockResolvedValue({ id: 1 });
      DeliveryPerson.findAll.mockResolvedValue([]);
      DeliverHistory.findAll.mockResolvedValue([]);
      DeliverGate.findAll.mockResolvedValue([]);
      DeliverEquipment.findAll.mockResolvedValue([]);
      DeliverCompany.findAll.mockResolvedValue([]);
      DeliverDefine.findAll.mockResolvedValue([]);
      DeliverComment.findAll.mockResolvedValue([]);
      DeliverAttachement.findAll.mockResolvedValue([]);
      Notification.findAll.mockResolvedValue([]);
      VoidList.findAll.mockResolvedValue([]);

      // Execute
      await accountCornService.createDeliveryRequest(
        [mockDeliveryRequest],
        0,
        { id: 1 },
        { id: 1 },
      );

      // Verify
      expect(db.DynamicDeliveryRequest.create).toHaveBeenCalled();
    });

    it('should handle delivery request creation with existing request', async () => {
      // Mock data
      const mockDeliveryRequest = {
        id: 1,
        ProjectId: 1,
      };

      // Setup mocks
      db.DynamicDeliveryRequest.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliveryRequest(
        [mockDeliveryRequest],
        0,
        { id: 1 },
        { id: 1 },
      );

      // Verify
      expect(db.DynamicDeliveryRequest.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during delivery request creation', async () => {
      const mockDeliveryRequest = { id: 1 };
      DeliveryRequest.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliveryRequest([mockDeliveryRequest], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in delivery request data', async () => {
      const mockDeliveryRequest = {};
      await expect(
        accountCornService.createDeliveryRequest([mockDeliveryRequest], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createNotification', () => {
    it('should create notification successfully', async () => {
      // Mock data
      const mockNotification = {
        id: 1,
        ProjectId: 1,
        MemberId: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicNotification.findOne.mockResolvedValue(null);
      db.DynamicNotification.create.mockResolvedValue({ id: 1 });
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createNotification(
        [mockNotification],
        0,
        { id: 1 },
        { id: 1 },
        { id: 1 },
      );

      // Verify
      expect(db.DynamicNotification.create).toHaveBeenCalled();
    });

    it('should handle notification creation with existing notification', async () => {
      // Mock data
      const mockNotification = {
        id: 1,
        ProjectId: 1,
      };

      // Setup mocks
      db.DynamicNotification.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createNotification(
        [mockNotification],
        0,
        { id: 1 },
        { id: 1 },
        { id: 1 },
      );

      // Verify
      expect(db.DynamicNotification.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during notification creation', async () => {
      const mockNotification = { id: 1 };
      Notification.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createNotification([mockNotification], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in notification data', async () => {
      const mockNotification = {};
      await expect(
        accountCornService.createNotification([mockNotification], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createVoidList', () => {
    it('should create void list successfully', async () => {
      // Mock data
      const mockVoidList = {
        id: 1,
        MemberId: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicVoidList.findOne.mockResolvedValue(null);
      db.DynamicVoidList.create.mockResolvedValue({ id: 1 });
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createVoidList([mockVoidList], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicVoidList.create).toHaveBeenCalled();
    });

    it('should handle void list creation with existing void list', async () => {
      // Mock data
      const mockVoidList = {
        id: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicVoidList.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createVoidList([mockVoidList], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicVoidList.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during void list creation', async () => {
      const mockVoidList = { id: 1 };
      VoidList.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createVoidList([mockVoidList], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in void list data', async () => {
      const mockVoidList = {};
      await expect(
        accountCornService.createVoidList([mockVoidList], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliverComment', () => {
    it('should create deliver comment successfully', async () => {
      // Mock data
      const mockComment = {
        id: 1,
        MemberId: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicDeliverComment.findOne.mockResolvedValue(null);
      db.DynamicDeliverComment.create.mockResolvedValue({ id: 1 });
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverComment([mockComment], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverComment.create).toHaveBeenCalled();
    });

    it('should handle deliver comment creation with existing comment', async () => {
      // Mock data
      const mockComment = {
        id: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicDeliverComment.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverComment([mockComment], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverComment.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during deliver comment creation', async () => {
      const mockComment = { id: 1 };
      DeliverComment.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliverComment([mockComment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in deliver comment data', async () => {
      const mockComment = {};
      await expect(
        accountCornService.createDeliverComment([mockComment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliverAttachment', () => {
    it('should create deliver attachment successfully', async () => {
      // Mock data
      const mockAttachment = {
        id: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicDeliverAttachement.findOne.mockResolvedValue(null);
      db.DynamicDeliverAttachement.create.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverAttachment([mockAttachment], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverAttachement.create).toHaveBeenCalled();
    });

    it('should handle deliver attachment creation with existing attachment', async () => {
      // Mock data
      const mockAttachment = {
        id: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicDeliverAttachement.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverAttachment([mockAttachment], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverAttachement.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during deliver attachment creation', async () => {
      const mockAttachment = { id: 1 };
      DeliverAttachement.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliverAttachment([mockAttachment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in deliver attachment data', async () => {
      const mockAttachment = {};
      await expect(
        accountCornService.createDeliverAttachment([mockAttachment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliverGate', () => {
    it('should create deliver gate successfully', async () => {
      // Mock data
      const mockGate = {
        id: 1,
        GateId: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliverGate.findOne.mockResolvedValue(null);
      db.DynamicDeliverGate.create.mockResolvedValue({ id: 1 });
      db.DynamicGates.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverGate([mockGate], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverGate.create).toHaveBeenCalled();
    });

    it('should handle deliver gate creation with existing gate', async () => {
      // Mock data
      const mockGate = {
        id: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliverGate.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverGate([mockGate], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverGate.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during deliver gate creation', async () => {
      const mockGate = { id: 1 };
      DeliverGate.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliverGate([mockGate], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in deliver gate data', async () => {
      const mockGate = {};
      await expect(
        accountCornService.createDeliverGate([mockGate], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliverCompany', () => {
    it('should create deliver company successfully', async () => {
      // Mock data
      const mockCompany = {
        id: 1,
        CompanyId: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliverCompany.findOne.mockResolvedValue(null);
      db.DynamicDeliverCompany.create.mockResolvedValue({ id: 1 });
      db.DynamicCompany.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverCompany([mockCompany], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverCompany.create).toHaveBeenCalled();
    });

    it('should handle deliver company creation with existing company', async () => {
      // Mock data
      const mockCompany = {
        id: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliverCompany.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverCompany([mockCompany], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverCompany.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during deliver company creation', async () => {
      const mockCompany = { id: 1 };
      DeliverCompany.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliverCompany([mockCompany], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in deliver company data', async () => {
      const mockCompany = {};
      await expect(
        accountCornService.createDeliverCompany([mockCompany], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createDeliverEquipment', () => {
    it('should create deliver equipment successfully', async () => {
      // Mock data
      const mockEquipment = {
        id: 1,
        EquipmentId: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliverEquipment.findOne.mockResolvedValue(null);
      db.DynamicDeliverEquipment.create.mockResolvedValue({ id: 1 });
      db.DynamicEquipments.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverEquipment([mockEquipment], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverEquipment.create).toHaveBeenCalled();
    });

    it('should handle deliver equipment creation with existing equipment', async () => {
      // Mock data
      const mockEquipment = {
        id: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliverEquipment.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createDeliverEquipment([mockEquipment], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverEquipment.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during deliver equipment creation', async () => {
      const mockEquipment = { id: 1 };
      DeliverEquipment.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createDeliverEquipment([mockEquipment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in deliver equipment data', async () => {
      const mockEquipment = {};
      await expect(
        accountCornService.createDeliverEquipment([mockEquipment], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createPerson', () => {
    it('should create person successfully', async () => {
      // Mock data
      const mockPerson = {
        id: 1,
        MemberId: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliveryPerson.findOne.mockResolvedValue(null);
      db.DynamicDeliveryPerson.create.mockResolvedValue({ id: 1 });
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createPerson([mockPerson], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliveryPerson.create).toHaveBeenCalled();
    });

    it('should handle person creation with existing person', async () => {
      // Mock data
      const mockPerson = {
        id: 1,
        DeliveryId: 1,
      };

      // Setup mocks
      db.DynamicDeliveryPerson.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createPerson([mockPerson], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliveryPerson.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during person creation', async () => {
      const mockPerson = { id: 1 };
      DeliveryPerson.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createPerson([mockPerson], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in person data', async () => {
      const mockPerson = {};
      await expect(
        accountCornService.createPerson([mockPerson], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('createHistory', () => {
    it('should create history successfully', async () => {
      // Mock data
      const mockHistory = {
        id: 1,
        MemberId: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicDeliverHistory.findOne.mockResolvedValue(null);
      db.DynamicDeliverHistory.create.mockResolvedValue({ id: 1 });
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createHistory([mockHistory], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverHistory.create).toHaveBeenCalled();
    });

    it('should handle history creation with existing history', async () => {
      // Mock data
      const mockHistory = {
        id: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks
      db.DynamicDeliverHistory.findOne.mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createHistory([mockHistory], 0, { id: 1 }, { id: 1 });

      // Verify
      expect(db.DynamicDeliverHistory.create).not.toHaveBeenCalled();
    });

    it('should handle errors during history creation', async () => {
      // Mock data
      const mockHistory = {
        id: 1,
        MemberId: 1,
        DeliveryRequestId: 1,
      };

      // Setup mocks to throw error
      db.DynamicDeliverHistory.findOne.mockRejectedValue(new Error('Database error'));

      // Execute and verify
      await expect(
        accountCornService.createHistory([mockHistory], 0, { id: 1 }, { id: 1 }),
      ).rejects.toThrow('Database error');
    });

    it('should handle database errors during history creation', async () => {
      const mockHistory = { id: 1 };
      DeliverHistory.findOne.mockRejectedValue(new Error('Database error'));
      await expect(
        accountCornService.createHistory([mockHistory], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow('Database error');
    });

    it('should handle missing required fields in history data', async () => {
      const mockHistory = {};
      await expect(
        accountCornService.createHistory([mockHistory], 0, {}, {}, {}, 1, {}),
      ).rejects.toThrow();
    });
  });

  describe('getCreatedBy', () => {
    it('should get created by user successfully', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify
      expect(User.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
      expect(result).toBe(1);
    });

    it('should handle member created by', async () => {
      // Mock data
      const mockMember = {
        id: 1,
        UserId: 2,
      };
      const mockUser = {
        id: 2,
        email: '<EMAIL>',
      };

      // Setup mocks
      User.findOne.mockResolvedValueOnce(null);
      Member.findOne.mockResolvedValue(mockMember);
      User.findOne.mockResolvedValueOnce(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify
      expect(Member.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
      expect(result).toBe(1);
    });

    it('should return null for null createdBy', async () => {
      // Execute
      const result = await accountCornService.getCreatedBy(null);

      // Verify
      expect(result).toBeNull();
    });

    it('should return existing dynamic user when found', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
      };
      const existingDynamicUser = { id: 2, email: '<EMAIL>' };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(existingDynamicUser);

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify
      expect(db.DynamicUser.createInstance).not.toHaveBeenCalled();
      expect(result).toBe(2);
    });

    it('should handle user with isAccount flag', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        isAccount: true,
        userType: 'regular',
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockUser);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
      expect(result).toBe(1);
    });

    it('should handle super admin user type', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        userType: 'super admin',
        firstName: 'Super',
        isAccount: false,
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockUser);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
      expect(result).toBe(1);
    });

    it('should handle folloit admin user type', async () => {
      // Mock data
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        userType: 'folloit admin',
        firstName: 'Folloit',
        isAccount: false,
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockUser);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
      expect(result).toBe(1);
    });

    it('should handle errors during getCreatedBy', async () => {
      // Setup mocks to throw error
      const error = new Error('Database error');
      User.findOne.mockRejectedValue(error);

      // Execute
      const result = await accountCornService.getCreatedBy(1);

      // Verify error is returned
      expect(result).toBe(error);
    });

    it('should handle database errors during getCreatedBy', async () => {
      User.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.getCreatedBy(1)).rejects.toThrow('Database error');
    });

    it('should handle missing user data', async () => {
      User.findOne.mockResolvedValue(null);
      await expect(accountCornService.getCreatedBy(1)).rejects.toThrow();
    });
  });

  describe('convertObject', () => {
    it('should convert object successfully', async () => {
      // Test data
      const testObject = {
        id: 1,
        name: 'Test',
        nested: { value: 'test' },
      };

      // Execute
      const result = await accountCornService.convertObject(testObject);

      // Verify
      expect(result).toEqual(testObject);
    });

    it('should handle null input', async () => {
      // Execute
      const result = await accountCornService.convertObject(null);

      // Verify
      expect(result).toBeNull();
    });

    it('should handle database errors during object conversion', async () => {
      const mockObject = { id: 1 };
      db.DynamicUser.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.convertObject(mockObject)).rejects.toThrow('Database error');
    });

    it('should handle missing object data', async () => {
      await expect(accountCornService.convertObject(null)).rejects.toThrow();
    });
  });

  describe('checkParent', () => {
    it('should find parent company successfully', async () => {
      // Mock data
      const mockParent = {
        id: 1,
        emailDomainName: 'test.com',
      };

      // Setup mocks
      db.DynamicParentCompany.findOne.mockResolvedValue(mockParent);

      // Execute
      const result = await accountCornService.checkParent('test.com');

      // Verify
      expect(db.DynamicParentCompany.findOne).toHaveBeenCalledWith({
        where: { emailDomainName: 'test.com' },
      });
      expect(result).toEqual(mockParent);
    });

    it('should return null when parent not found', async () => {
      // Setup mocks
      db.DynamicParentCompany.findOne.mockResolvedValue(null);

      // Execute
      const result = await accountCornService.checkParent('test.com');

      // Verify
      expect(result).toBeNull();
    });

    it('should handle database errors during parent check', async () => {
      db.DynamicParentCompany.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.checkParent('test.com')).rejects.toThrow('Database error');
    });

    it('should handle missing domain name', async () => {
      await expect(accountCornService.checkParent(null)).rejects.toThrow();
    });
  });

  describe('createParentCompany', () => {
    it('should create parent company successfully', async () => {
      // Mock data
      const mockParent = {
        id: 1,
        name: 'Test Parent',
        emailDomainName: 'test.com',
      };

      // Setup mocks
      ParentCompany.findOne.mockResolvedValue(mockParent);
      db.DynamicParentCompany.create.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.createParentCompany(1);

      // Verify
      expect(ParentCompany.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(db.DynamicParentCompany.create).toHaveBeenCalled();
      expect(result).toEqual({ id: 1 });
    });

    it('should handle errors during parent company creation', async () => {
      // Setup mocks to throw error
      ParentCompany.findOne.mockRejectedValue(new Error('Database error'));

      // Execute and verify
      await expect(accountCornService.createParentCompany(1)).rejects.toThrow('Database error');
    });

    it('should handle database errors during parent company creation', async () => {
      ParentCompany.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.createParentCompany(1)).rejects.toThrow('Database error');
    });

    it('should handle missing company ID', async () => {
      await expect(accountCornService.createParentCompany(null)).rejects.toThrow();
    });
  });

  describe('sendAccountCreationEmail', () => {
    it('should send account creation email successfully', async () => {
      // Mock data
      const mockUser = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      };
      const mockEnterprise = {
        name: 'test-enterprise',
      };

      // Setup mocks
      MAILER.sendMail.mockResolvedValue(true);

      // Execute
      await accountCornService.sendAccountCreationEmail(mockUser, mockEnterprise);

      // Verify
      expect(MAILER.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: mockUser.email,
          subject: expect.any(String),
        }),
      );
    });

    it('should handle errors during email sending', async () => {
      // Setup mocks to throw error
      MAILER.sendMail.mockRejectedValue(new Error('Email sending failed'));

      // Execute
      await expect(
        accountCornService.sendAccountCreationEmail(
          { email: '<EMAIL>' },
          { name: 'test-enterprise' },
        ),
      ).rejects.toThrow('Email sending failed');
    });

    it('should handle database errors during email sending', async () => {
      MAILER.sendMail.mockRejectedValue(new Error('Email sending failed'));
      await expect(accountCornService.sendAccountCreationEmail({}, {})).rejects.toThrow(
        'Email sending failed',
      );
    });

    it('should handle missing user data', async () => {
      await expect(accountCornService.sendAccountCreationEmail(null, {})).rejects.toThrow();
    });

    it('should handle missing enterprise data', async () => {
      await expect(accountCornService.sendAccountCreationEmail({}, null)).rejects.toThrow();
    });
  });

  describe('createUser - Advanced Cases', () => {
    it('should handle user creation with element.id when UserId is not present', async () => {
      // Mock data
      const mockElement = {
        id: 1,
      };
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        userType: 'regular',
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockUser);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 2 });

      // Execute
      const result = await accountCornService.createUser(mockElement);

      // Verify
      expect(User.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toEqual({ id: 2 });
    });

    it('should handle user with undefined isAccount', async () => {
      // Mock data
      const mockElement = {
        UserId: 1,
      };
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        userType: 'regular',
        // isAccount is undefined
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockUser);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 2 });

      // Execute
      const result = await accountCornService.createUser(mockElement);

      // Verify
      expect(result).toEqual({ id: 2 });
    });

    it('should add user to dynamicUserData for regular users', async () => {
      // Mock data
      const mockElement = {
        UserId: 1,
      };
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'Regular',
        userType: 'regular',
        isAccount: false,
      };

      // Setup mocks
      User.findOne.mockResolvedValue(mockUser);
      db.DynamicUser.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockUser);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 2 });

      // Execute
      await accountCornService.createUser(mockElement);

      // Verify user was processed
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
    });
  });

  describe('updateEnterpriseStatus', () => {
    it('should update enterprise status successfully', async () => {
      // Setup mocks
      Enterprise.update.mockResolvedValue([1]);

      // Execute
      await accountCornService.updateEnterpriseStatus(1, 'Active');

      // Verify
      expect(Enterprise.update).toHaveBeenCalledWith({ status: 'Active' }, { where: { id: 1 } });
    });

    it('should handle errors during status update', async () => {
      // Setup mocks to throw error
      Enterprise.update.mockRejectedValue(new Error('Update failed'));

      // Execute and verify
      await expect(accountCornService.updateEnterpriseStatus(1, 'Active')).rejects.toThrow(
        'Update failed',
      );
    });
  });

  describe('sendMail', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'test';
    });

    it('should send mail successfully with dynamic user data', async () => {
      // Mock data
      const mockUser = {
        email: '<EMAIL>',
        firstName: 'Test',
        password: 'password123',
      };
      const mockEnterprise = {
        id: 1,
        name: 'TestEnterprise',
        amount: 100,
      };

      // Setup mocks
      Enterprise.update.mockResolvedValue([1]);
      MAILER.sendMail.mockResolvedValue(true);
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      Member.findOne.mockResolvedValue(null);

      // Execute
      await accountCornService.sendMail(mockUser, mockEnterprise);

      // Verify
      expect(Enterprise.update).toHaveBeenCalledWith(
        { domainURL: 'https://testenterprise-test.folloit.com' },
        { where: { id: 1 } },
      );
      expect(MAILER.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
          firstName: 'Test',
          amount: 100,
          password: 'password123',
          domainURL: 'https://testenterprise-test.folloit.com',
        }),
        'Account Creation',
        'Welcome to Follo!!!! You have got the credentials in this mail to start with us.',
        'Account Admin Created',
        expect.any(Function),
      );
    });

    it('should handle member email sending when user exists', async () => {
      // Mock data
      const mockUser = {
        email: '<EMAIL>',
        firstName: 'Admin',
        password: 'adminpass',
      };
      const mockEnterprise = {
        id: 1,
        name: 'TestEnterprise',
        amount: 100,
      };

      // Setup mocks
      Enterprise.update.mockResolvedValue([1]);
      MAILER.sendMail.mockResolvedValue(true);
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      Member.findOne.mockResolvedValue(null);

      // Execute
      await accountCornService.sendMail(mockUser, mockEnterprise);

      // Verify main email was sent
      expect(MAILER.sendMail).toHaveBeenCalled();
    });

    it('should handle empty dynamicUserData', async () => {
      const mockUser = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      };
      const mockEnterprise = {
        id: 1,
        name: 'test-enterprise',
        amount: 100,
      };

      await accountCornService.sendMail(mockUser, mockEnterprise);
      expect(MAILER.sendMail).toHaveBeenCalled();
      expect(Enterprise.update).toHaveBeenCalled();
    });

    it('should handle errors during mail sending', async () => {
      const mockUser = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      };
      const mockEnterprise = {
        id: 1,
        name: 'test-enterprise',
        amount: 100,
      };

      MAILER.sendMail.mockRejectedValue(new Error('Mail sending failed'));
      await expect(accountCornService.sendMail(mockUser, mockEnterprise)).rejects.toThrow(
        'Mail sending failed',
      );
    });
  });

  describe('dynamicSchemaCreation', () => {
    it('should create dynamic schema successfully', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        name: 'TestEnterprise',
        User: { id: 1, email: '<EMAIL>' },
      };
      const mockAccountDetail = [mockElement];

      // Setup mocks
      domainHelper.domainCreation.mockResolvedValue(true);
      db.syncToSchema.mockResolvedValue(true);
      dynamicModels.domain.mockResolvedValue(true);
      db.DynamicRole.bulkCreateRoles.mockResolvedValue(true);

      // Mock createUserProject method
      accountCornService.createUserProject = jest.fn().mockResolvedValue(true);

      // Execute
      await accountCornService.dynamicSchemaCreation(mockElement, mockAccountDetail, 0);

      // Verify
      expect(domainHelper.domainCreation).toHaveBeenCalledWith('testenterprise');
      expect(db.syncToSchema).toHaveBeenCalledWith('testenterprise');
      expect(dynamicModels.domain).toHaveBeenCalledWith('testenterprise');
      expect(db.DynamicRole.bulkCreateRoles).toHaveBeenCalled();
      expect(accountCornService.createUserProject).toHaveBeenCalledWith(mockAccountDetail, 0);
    });

    it('should handle errors during schema creation', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        name: 'TestEnterprise',
      };

      // Setup mocks to throw error
      domainHelper.domainCreation.mockRejectedValue(new Error('Schema creation failed'));

      // Execute and verify
      await expect(accountCornService.dynamicSchemaCreation(mockElement, [], 0)).rejects.toThrow(
        'Schema creation failed',
      );
    });

    it('should handle database errors during schema creation', async () => {
      domainHelper.domainCreation.mockRejectedValue(new Error('Schema creation failed'));
      await expect(accountCornService.dynamicSchemaCreation({}, [], 0)).rejects.toThrow(
        'Schema creation failed',
      );
    });

    it('should handle missing element data', async () => {
      await expect(accountCornService.dynamicSchemaCreation(null, [], 0)).rejects.toThrow();
    });

    it('should handle missing account detail data', async () => {
      await expect(accountCornService.dynamicSchemaCreation({}, null, 0)).rejects.toThrow();
    });
  });

  describe('createUserProject', () => {
    it('should create user project with existing user', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        name: 'TestEnterprise',
        User: {
          id: 1,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        },
        Projects: [
          {
            id: 1,
            ParentCompanyId: 1,
          },
        ],
        ParentCompanyId: 1,
      };
      const mockAccountDetail = [mockElement];

      // Setup mocks
      db.DynamicUser.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      db.DynamicUser.update.mockResolvedValue([1]);
      Project.update.mockResolvedValue([1]);
      Project.findAll.mockResolvedValue([
        { id: 1, projectName: 'Test Project', ParentCompanyId: 1, createdBy: 1 },
      ]);

      // Mock all the methods that createProject calls to avoid recursive issues
      accountCornService.convertObject = jest.fn().mockResolvedValue({
        id: 1,
        projectName: 'Test Project',
        ParentCompanyId: 1,
        createdBy: 1,
      });
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);
      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      const result = await accountCornService.createUserProject(mockAccountDetail, 0);

      // Verify
      expect(db.DynamicUser.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(db.DynamicUser.update).toHaveBeenCalled();
      expect(Project.update).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should create user project with new user', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        name: 'TestEnterprise',
        User: {
          id: 1,
          email: '<EMAIL>',
          firstName: 'New',
          lastName: 'User',
        },
        Projects: [
          {
            id: 1,
            ParentCompanyId: 1,
          },
        ],
      };
      const mockAccountDetail = [mockElement];

      // Setup mocks
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 2, email: '<EMAIL>' });
      Project.update.mockResolvedValue([1]);
      Project.findAll.mockResolvedValue([
        { id: 1, projectName: 'Test Project', ParentCompanyId: 1, createdBy: 1 },
      ]);

      // Mock all the methods that createProject calls
      accountCornService.convertObject = jest.fn().mockResolvedValue({
        id: 1,
        projectName: 'Test Project',
        ParentCompanyId: 1,
        createdBy: 1,
      });
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);
      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      await accountCornService.createUserProject(mockAccountDetail, 0);

      // Verify
      expect(db.DynamicUser.createInstance).toHaveBeenCalled();
    });

    it('should handle case with no projects', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        name: 'TestEnterprise',
        User: {
          id: 1,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        },
        Projects: [],
        ParentCompanyId: 1,
      };
      const mockAccountDetail = [mockElement];

      // Setup mocks
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      Company.findOne.mockResolvedValue({ id: 1, name: 'Test Company' });
      ParentCompany.findOne.mockResolvedValue({ id: 1, name: 'Parent Company' });
      db.DynamicParentCompany.createInstance.mockResolvedValue({ id: 1 });
      db.DynamicCompany.createInstance.mockResolvedValue({ id: 1 });

      // Mock EnterpriseUpdate method
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      const result = await accountCornService.createUserProject(mockAccountDetail, 0);

      // Verify
      expect(Company.findOne).toHaveBeenCalled();
      expect(ParentCompany.findOne).toHaveBeenCalled();
      expect(db.DynamicParentCompany.createInstance).toHaveBeenCalled();
      expect(db.DynamicCompany.createInstance).toHaveBeenCalled();
      expect(accountCornService.EnterpriseUpdate).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should handle recursive call for multiple account details', async () => {
      // Mock data
      const mockElement1 = {
        id: 1,
        name: 'TestEnterprise1',
        User: { id: 1, email: '<EMAIL>' },
        Projects: [{ id: 1, ParentCompanyId: 1 }],
      };
      const mockElement2 = {
        id: 2,
        name: 'TestEnterprise2',
        User: { id: 2, email: '<EMAIL>' },
        Projects: [{ id: 2, ParentCompanyId: 2 }],
      };
      const mockAccountDetail = [mockElement1, mockElement2];

      // Setup mocks
      db.DynamicUser.findOne.mockResolvedValue(null);
      db.DynamicUser.createInstance.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      Project.update.mockResolvedValue([1]);
      Project.findAll.mockResolvedValue([
        { id: 1, projectName: 'Test Project', ParentCompanyId: 1, createdBy: 1 },
      ]);

      // Mock all the methods that createProject calls
      accountCornService.convertObject = jest.fn().mockResolvedValue({
        id: 1,
        projectName: 'Test Project',
        ParentCompanyId: 1,
        createdBy: 1,
      });
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);
      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);
      accountCornService.dynamicSchemaCreation = jest.fn().mockResolvedValue(true);

      // Execute
      await accountCornService.createUserProject(mockAccountDetail, 0);

      // Verify
      expect(accountCornService.dynamicSchemaCreation).toHaveBeenCalledWith(
        mockElement2,
        mockAccountDetail,
        1,
      );
    });

    it('should handle database errors during user project creation', async () => {
      db.DynamicUser.findOne.mockRejectedValue(new Error('Database error'));
      await expect(accountCornService.createUserProject([], 0)).rejects.toThrow('Database error');
    });

    it('should handle missing account detail data', async () => {
      await expect(accountCornService.createUserProject(null, 0)).rejects.toThrow();
    });
  });

  describe('EnterpriseUpdate', () => {
    it('should update enterprise when mailSendOn is not null', async () => {
      // Mock data
      const mockEnterprise = {
        id: 1,
        mailSendOn: new Date(),
        name: 'TestEnterprise',
      };
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
      };
      const password = 'testPassword';

      // Setup mocks
      Enterprise.update.mockResolvedValue([1]);
      accountCornService.sendMail = jest.fn().mockResolvedValue(true);

      // Execute
      const result = await accountCornService.EnterpriseUpdate(mockEnterprise, mockUser, password);

      // Verify
      expect(Enterprise.update).toHaveBeenCalledWith(
        { status: 'completed', mailSendOn: expect.any(Date) },
        { where: { id: 1 } },
      );
      expect(accountCornService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({ password: 'testPassword' }),
        mockEnterprise,
      );
      expect(result).toBe(true);
    });

    it('should not update enterprise when mailSendOn is null', async () => {
      // Mock data
      const mockEnterprise = {
        id: 1,
        mailSendOn: null,
        name: 'TestEnterprise',
      };
      const mockUser = { id: 1, email: '<EMAIL>' };
      const password = 'testPassword';

      // Execute
      const result = await accountCornService.EnterpriseUpdate(mockEnterprise, mockUser, password);

      // Verify
      expect(Enterprise.update).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });

    it('should handle database errors during enterprise update', async () => {
      Enterprise.update.mockRejectedValue(new Error('Update failed'));
      await expect(accountCornService.EnterpriseUpdate({}, {}, 'password')).rejects.toThrow(
        'Update failed',
      );
    });

    it('should handle missing enterprise data', async () => {
      await expect(accountCornService.EnterpriseUpdate(null, {}, 'password')).rejects.toThrow();
    });

    it('should handle missing user data', async () => {
      await expect(accountCornService.EnterpriseUpdate({}, null, 'password')).rejects.toThrow();
    });

    it('should handle missing password', async () => {
      await expect(accountCornService.EnterpriseUpdate({}, {}, null)).rejects.toThrow();
    });
  });

  describe('createProject', () => {
    it('should create project with all related entities', async () => {
      // Mock data
      const mockProjectList = [
        {
          id: 1,
          projectName: 'Test Project',
          ParentCompanyId: 1,
          createdBy: 1,
        },
      ];
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockEnterprise = { id: 1, name: 'TestEnterprise' };
      const password = 'testPassword';

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockProjectList[0]);
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);

      // Mock all create methods
      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      const result = await accountCornService.createProject(
        mockProjectList,
        0,
        mockNewUser,
        mockEnterprise,
        password,
      );

      // Verify
      expect(accountCornService.getCreatedBy).toHaveBeenCalledWith(1);
      expect(ParentCompany.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(accountCornService.checkParent).toHaveBeenCalledWith('test.com');
      expect(db.DynamicProject.createInstance).toHaveBeenCalled();
      expect(accountCornService.createMember).toHaveBeenCalled();
      expect(accountCornService.EnterpriseUpdate).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });

    it('should handle existing project', async () => {
      // Mock data
      const mockProjectList = [
        {
          id: 1,
          projectName: 'Test Project',
          ParentCompanyId: 1,
          createdBy: 1,
        },
      ];
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockEnterprise = { id: 1, name: 'TestEnterprise' };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockProjectList[0]);
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicProject.findOne.mockResolvedValue({ id: 1 }); // Existing project
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);

      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      await accountCornService.createProject(
        mockProjectList,
        0,
        mockNewUser,
        mockEnterprise,
        'password',
      );

      // Verify
      expect(db.DynamicProject.createInstance).not.toHaveBeenCalled();
      expect(accountCornService.createMember).toHaveBeenCalled();
    });

    it('should create parent company when not exists', async () => {
      // Mock data
      const mockProjectList = [
        {
          id: 1,
          projectName: 'Test Project',
          ParentCompanyId: 1,
          createdBy: 1,
        },
      ];
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockEnterprise = { id: 1, name: 'TestEnterprise' };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockProjectList[0]);
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue(null); // No existing parent
      accountCornService.createParentCompany = jest.fn().mockResolvedValue({ id: 2 });
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);

      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      await accountCornService.createProject(
        mockProjectList,
        0,
        mockNewUser,
        mockEnterprise,
        'password',
      );

      // Verify
      expect(accountCornService.createParentCompany).toHaveBeenCalledWith(1);
    });

    it('should handle recursive call for multiple projects', async () => {
      // Mock data
      const mockProjectList = [
        { id: 1, projectName: 'Project 1', ParentCompanyId: 1, createdBy: 1 },
        { id: 2, projectName: 'Project 2', ParentCompanyId: 1, createdBy: 1 },
      ];
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockEnterprise = { id: 1, name: 'TestEnterprise' };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockProjectList[0]);
      accountCornService.getCreatedBy = jest.fn().mockResolvedValue(1);
      ParentCompany.findOne.mockResolvedValue({ id: 1, emailDomainName: 'test.com' });
      accountCornService.checkParent = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicProject.findOne.mockResolvedValue(null);
      db.DynamicProject.createInstance.mockResolvedValue({ id: 1 });
      Member.findAll.mockResolvedValue([]);
      Company.findAll.mockResolvedValue([]);
      Gates.findAll.mockResolvedValue([]);
      Equipments.findAll.mockResolvedValue([]);
      DeliverDefineWork.findAll.mockResolvedValue([]);
      DeliveryRequest.findAll.mockResolvedValue([]);
      CompanyDefine.findAll.mockResolvedValue([]);

      accountCornService.createMember = jest.fn().mockResolvedValue(true);
      accountCornService.EnterpriseUpdate = jest.fn().mockResolvedValue(true);

      // Execute
      const result = await accountCornService.createProject(
        mockProjectList,
        0,
        mockNewUser,
        mockEnterprise,
        'password',
      );

      // Verify
      expect(accountCornService.createMember).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });
  });

  describe('createMember - Advanced Cases', () => {
    it('should handle member creation with company', async () => {
      // Mock data
      const mockMemberList = [
        {
          id: 1,
          UserId: 1,
          ProjectId: 1,
          CompanyId: 1,
          RoleId: 2,
          firstName: 'Test Member',
        },
      ];
      const mockNewParent = { id: 1 };
      const mockNewUser = { id: 1, firstName: 'Admin' };
      const mockNewProject = { id: 1 };
      const createdBy = 1;
      const mockEnterprise = { id: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockMemberList[0]);
      accountCornService.createNewCompany = jest.fn().mockResolvedValue({ id: 1 });
      accountCornService.createUser = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });
      accountCornService.createEnterpriseMember = jest.fn().mockResolvedValue(true);
      db.DynamicMember.findOne.mockResolvedValue(null);
      accountCornService.createNewMember = jest.fn().mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.createMember(
        mockMemberList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
        createdBy,
        mockEnterprise,
      );

      // Verify
      expect(accountCornService.createNewCompany).toHaveBeenCalledWith(
        1,
        mockNewParent,
        createdBy,
        mockNewProject,
      );
      expect(accountCornService.createUser).toHaveBeenCalled();
      expect(accountCornService.createEnterpriseMember).toHaveBeenCalled();
      expect(accountCornService.createNewMember).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });

    it('should handle member creation without company', async () => {
      // Mock data
      const mockMemberList = [
        {
          id: 1,
          UserId: 1,
          ProjectId: 1,
          CompanyId: null,
          RoleId: 2,
          firstName: 'Test Member',
        },
      ];
      const mockNewParent = { id: 1 };
      const mockNewUser = { id: 1, firstName: 'Admin' };
      const mockNewProject = { id: 1 };
      const createdBy = 1;
      const mockEnterprise = { id: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockMemberList[0]);
      accountCornService.createUser = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });
      accountCornService.createEnterpriseMember = jest.fn().mockResolvedValue(true);
      db.DynamicMember.findOne.mockResolvedValue(null);
      accountCornService.createNewMember = jest.fn().mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createMember(
        mockMemberList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
        createdBy,
        mockEnterprise,
      );

      // Verify
      expect(accountCornService.createUser).toHaveBeenCalled();
      expect(accountCornService.createNewMember).toHaveBeenCalled();
    });

    it('should handle existing member', async () => {
      // Mock data
      const mockMemberList = [
        {
          id: 1,
          UserId: 1,
          ProjectId: 1,
          RoleId: 2,
          firstName: 'Test Member',
        },
      ];
      const mockNewParent = { id: 1 };
      const mockNewUser = { id: 1, firstName: 'Admin' };
      const mockNewProject = { id: 1 };
      const createdBy = 1;
      const mockEnterprise = { id: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockMemberList[0]);
      accountCornService.createUser = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });
      accountCornService.createEnterpriseMember = jest.fn().mockResolvedValue(true);
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 }); // Existing member
      accountCornService.createNewMember = jest.fn().mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createMember(
        mockMemberList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
        createdBy,
        mockEnterprise,
      );

      // Verify existing member path
      expect(db.DynamicMember.findOne).toHaveBeenCalled();
    });

    it('should handle empty member list', async () => {
      // Execute
      const result = await accountCornService.createMember(
        [],
        0,
        { id: 1 },
        { id: 1 },
        { id: 1 },
        1,
        { id: 1 },
      );

      // Verify
      expect(result).toBeUndefined();
    });

    it('should handle recursive call for multiple members', async () => {
      // Mock data
      const mockMemberList = [
        { id: 1, UserId: 1, ProjectId: 1, RoleId: 2, firstName: 'Member 1' },
        { id: 2, UserId: 2, ProjectId: 1, RoleId: 3, firstName: 'Member 2' },
      ];
      const mockNewParent = { id: 1 };
      const mockNewUser = { id: 1, firstName: 'Admin' };
      const mockNewProject = { id: 1 };
      const createdBy = 1;
      const mockEnterprise = { id: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockMemberList[0]);
      accountCornService.createUser = jest.fn().mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });
      accountCornService.createEnterpriseMember = jest.fn().mockResolvedValue(true);
      db.DynamicMember.findOne.mockResolvedValue(null);
      accountCornService.createNewMember = jest.fn().mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.createMember(
        mockMemberList,
        0,
        mockNewParent,
        mockNewUser,
        mockNewProject,
        createdBy,
        mockEnterprise,
      );

      // Verify
      expect(accountCornService.createUser).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });
  });

  describe('createNewMember', () => {
    it('should create new member when not exists', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        UserId: 1,
        ProjectId: 1,
        RoleId: 2,
      };
      const mockProject = { id: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockElement);
      db.DynamicMember.findOne
        .mockResolvedValueOnce(null) // First call for UserId/ProjectId check
        .mockResolvedValueOnce(null); // Second call for publicSchemaId check
      db.DynamicMember.createInstance.mockResolvedValue({ id: 1 });

      // Execute
      const result = await accountCornService.createNewMember(1, mockProject, mockElement);

      // Verify
      expect(db.DynamicMember.createInstance).toHaveBeenCalled();
      expect(result).toEqual({ id: 1 });
    });

    it('should return existing member when found by UserId and ProjectId', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        UserId: 1,
        ProjectId: 1,
        RoleId: 2,
      };
      const mockProject = { id: 1 };
      const existingMember = { id: 1, UserId: 1, ProjectId: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockElement);
      db.DynamicMember.findOne.mockResolvedValue(existingMember);

      // Execute
      const result = await accountCornService.createNewMember(1, mockProject, mockElement);

      // Verify
      expect(db.DynamicMember.createInstance).not.toHaveBeenCalled();
      expect(result).toEqual(existingMember);
    });

    it('should return existing member when found by publicSchemaId', async () => {
      // Mock data
      const mockElement = {
        id: 1,
        UserId: 1,
        ProjectId: 1,
        RoleId: 2,
      };
      const mockProject = { id: 1 };
      const existingMember = { id: 1, publicSchemaId: 1 };

      // Setup mocks
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockElement);
      db.DynamicMember.findOne
        .mockResolvedValueOnce(null) // First call for UserId/ProjectId check
        .mockResolvedValueOnce(existingMember); // Second call for publicSchemaId check

      // Execute
      const result = await accountCornService.createNewMember(1, mockProject, mockElement);

      // Verify
      expect(db.DynamicMember.createInstance).not.toHaveBeenCalled();
      expect(result).toEqual(existingMember);
    });
  });

  describe('createMemberHistory', () => {
    it('should return existing member id when member exists', async () => {
      // Mock data
      const memberId = 1;
      const mockProject = { id: 1 };
      const mockMemberData = { id: 1, UserId: 1 };
      const existingMember = { id: 2 };

      // Setup mocks
      accountCornService.getMemberData = jest.fn().mockResolvedValue(mockMemberData);
      accountCornService.findExistingDynamicMember = jest.fn().mockResolvedValue(existingMember);

      // Execute
      const result = await accountCornService.createMemberHistory(memberId, mockProject);

      // Verify
      expect(accountCornService.getMemberData).toHaveBeenCalledWith(1);
      expect(accountCornService.findExistingDynamicMember).toHaveBeenCalledWith(mockMemberData);
      expect(result).toBe(2);
    });

    it('should create new member when member does not exist', async () => {
      // Mock data
      const memberId = 1;
      const mockProject = { id: 1 };
      const mockMemberData = { id: 1, UserId: 1 };
      const newMember = { id: 3 };

      // Setup mocks
      accountCornService.getMemberData = jest.fn().mockResolvedValue(mockMemberData);
      accountCornService.findExistingDynamicMember = jest.fn().mockResolvedValue(null);
      accountCornService.createNewMember = jest.fn().mockResolvedValue(newMember);

      // Execute
      const result = await accountCornService.createMemberHistory(memberId, mockProject);

      // Verify
      expect(accountCornService.createNewMember).toHaveBeenCalledWith(
        '',
        mockProject,
        mockMemberData,
      );
      expect(result).toBe(3);
    });
  });

  describe('getMemberData', () => {
    it('should get and convert member data', async () => {
      // Mock data
      const memberId = 1;
      const mockMember = { id: 1, name: 'Test Member' };

      // Setup mocks
      Member.findOne.mockResolvedValue(mockMember);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockMember);

      // Execute
      const result = await accountCornService.getMemberData(memberId);

      // Verify
      expect(Member.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(accountCornService.convertObject).toHaveBeenCalledWith(mockMember);
      expect(result).toEqual(mockMember);
    });
  });

  describe('findExistingDynamicMember', () => {
    it('should find existing dynamic member', async () => {
      // Mock data
      const memberData = { id: 1 };
      const existingMember = { id: 2, publicSchemaId: 1 };

      // Setup mocks
      db.DynamicMember.findOne.mockResolvedValue(existingMember);

      // Execute
      const result = await accountCornService.findExistingDynamicMember(memberData);

      // Verify
      expect(db.DynamicMember.findOne).toHaveBeenCalledWith({
        where: { publicSchemaId: 1 },
      });
      expect(result).toEqual(existingMember);
    });
  });

  describe('createEnterpriseMember', () => {
    it('should create enterprise member when not exists', async () => {
      // Mock data
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockNewProject = { id: 1 };
      const oldProjectId = 2;
      const mockEnterprise = { id: 1 };
      const mockMemberList = [
        { CompanyId: 1, ParentCompanyId: 1, createdBy: 1, firstName: 'Test' },
      ];
      const index = 0;

      // Setup mocks
      db.DynamicMember.findOne.mockResolvedValue(null); // No existing enterprise member
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      accountCornService.convertObject = jest
        .fn()
        .mockResolvedValueOnce({ id: 1, email: '<EMAIL>' }) // For user data
        .mockResolvedValueOnce(null); // For member data (not found)
      Member.findOne.mockResolvedValue(null);
      Member.create.mockResolvedValue({ id: 1 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });
      accountCornService.createNewMember = jest.fn().mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createEnterpriseMember(
        mockNewUser,
        mockNewProject,
        oldProjectId,
        mockEnterprise,
        mockMemberList,
        index,
      );

      // Verify
      expect(Member.create).toHaveBeenCalled();
      expect(accountCornService.createNewMember).toHaveBeenCalled();
    });

    it('should handle existing enterprise member', async () => {
      // Mock data
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockNewProject = { id: 1 };
      const oldProjectId = 2;
      const mockEnterprise = { id: 1 };
      const mockMemberList = [{ CompanyId: 1 }];
      const index = 0;

      // Setup mocks
      db.DynamicMember.findOne.mockResolvedValue({ id: 1 }); // Existing enterprise member

      // Execute
      await accountCornService.createEnterpriseMember(
        mockNewUser,
        mockNewProject,
        oldProjectId,
        mockEnterprise,
        mockMemberList,
        index,
      );

      // Verify
      expect(Member.create).not.toHaveBeenCalled();
    });

    it('should handle existing member data', async () => {
      // Mock data
      const mockNewUser = { id: 1, email: '<EMAIL>' };
      const mockNewProject = { id: 1 };
      const oldProjectId = 2;
      const mockEnterprise = { id: 1 };
      const mockMemberList = [
        { CompanyId: 1, ParentCompanyId: 1, createdBy: 1, firstName: 'Test' },
      ];
      const index = 0;

      // Setup mocks
      db.DynamicMember.findOne.mockResolvedValue(null);
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      accountCornService.convertObject = jest
        .fn()
        .mockResolvedValueOnce({ id: 1, email: '<EMAIL>' })
        .mockResolvedValueOnce({ id: 1, UserId: 1, ProjectId: 2 }); // Existing member data
      Member.findOne.mockResolvedValue({ id: 1, UserId: 1, ProjectId: 2 });
      db.DynamicRole.findOne.mockResolvedValue({ id: 1, roleName: 'Account Admin' });
      accountCornService.createNewMember = jest.fn().mockResolvedValue({ id: 1 });

      // Execute
      await accountCornService.createEnterpriseMember(
        mockNewUser,
        mockNewProject,
        oldProjectId,
        mockEnterprise,
        mockMemberList,
        index,
      );

      // Verify
      expect(Member.create).not.toHaveBeenCalled();
      expect(accountCornService.createNewMember).toHaveBeenCalled();
    });
  });

  describe('createNewCompany', () => {
    it('should create new company when not exists', async () => {
      // Mock data
      const companyId = 1;
      const mockNewParent = { id: 1 };
      const createdBy = 1;
      const mockNewProject = { id: 1 };
      const mockCompanyData = { id: 1, name: 'Test Company' };

      // Setup mocks
      Company.findOne.mockResolvedValue(mockCompanyData);
      db.DynamicCompany.findOne.mockResolvedValue(null); // No existing company
      accountCornService.convertObject = jest
        .fn()
        .mockResolvedValueOnce(mockCompanyData)
        .mockResolvedValueOnce({ ...mockCompanyData, publicSchemaId: 1 });
      db.DynamicCompany.createInstance.mockResolvedValue({ id: 2 });

      // Execute
      const result = await accountCornService.createNewCompany(
        companyId,
        mockNewParent,
        createdBy,
        mockNewProject,
      );

      // Verify
      expect(Company.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(db.DynamicCompany.createInstance).toHaveBeenCalled();
      expect(result).toEqual({ id: 2 });
    });

    it('should return existing company when found', async () => {
      // Mock data
      const companyId = 1;
      const mockNewParent = { id: 1 };
      const createdBy = 1;
      const mockNewProject = { id: 1 };
      const existingCompany = { id: 2, publicSchemaId: 1 };

      // Setup mocks
      Company.findOne.mockResolvedValue({ id: 1, name: 'Test Company' });
      db.DynamicCompany.findOne.mockResolvedValue(existingCompany);

      // Execute
      const result = await accountCornService.createNewCompany(
        companyId,
        mockNewParent,
        createdBy,
        mockNewProject,
      );

      // Verify
      expect(db.DynamicCompany.createInstance).not.toHaveBeenCalled();
      expect(result).toEqual(existingCompany);
    });
  });

  describe('createNewGate', () => {
    it('should create new gate when not exists', async () => {
      // Mock data
      const mockElement = { id: 1, name: 'Test Gate' };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicGates.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest
        .fn()
        .mockResolvedValue({ ...mockElement, ProjectId: 1 });
      db.DynamicGates.create.mockResolvedValue({ id: 2 });

      // Execute
      const result = await accountCornService.createNewGate(mockElement, mockProject);

      // Verify
      expect(db.DynamicGates.findOne).toHaveBeenCalledWith({
        where: { publicSchemaId: 1 },
      });
      expect(db.DynamicGates.create).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should not create gate when already exists', async () => {
      // Mock data
      const mockElement = { id: 1, name: 'Test Gate' };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicGates.findOne.mockResolvedValue({ id: 2 });

      // Execute
      const result = await accountCornService.createNewGate(mockElement, mockProject);

      // Verify
      expect(db.DynamicGates.create).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('createNewEquipment', () => {
    it('should create new equipment when not exists', async () => {
      // Mock data
      const mockElement = { id: 1, name: 'Test Equipment' };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicEquipments.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest
        .fn()
        .mockResolvedValue({ name: 'Test Equipment', ProjectId: 1 });
      db.DynamicEquipments.create.mockResolvedValue({ id: 2 });

      // Execute
      await accountCornService.createNewEquipment(mockElement, mockProject);

      // Verify
      expect(db.DynamicEquipments.findOne).toHaveBeenCalledWith({
        where: { publicSchemaId: 1 },
      });
      expect(db.DynamicEquipments.create).toHaveBeenCalled();
    });

    it('should not create equipment when already exists', async () => {
      // Mock data
      const mockElement = { id: 1, name: 'Test Equipment' };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicEquipments.findOne.mockResolvedValue({ id: 2 });

      // Execute
      await accountCornService.createNewEquipment(mockElement, mockProject);

      // Verify
      expect(db.DynamicEquipments.create).not.toHaveBeenCalled();
    });
  });

  describe('createCompanyDefine', () => {
    it('should create company define when not exists', async () => {
      // Mock data
      const mockCompanyDefineList = [
        {
          id: 1,
          DeliverDefineWorkId: 1,
        },
      ];
      const mockDeliveryData = { id: 1 };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicCompanyDefine.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockCompanyDefineList[0]);
      db.DynamicDeliverDefineWork.findOne.mockResolvedValue({ id: 2 });
      db.DynamicCompanyDefine.create.mockResolvedValue({ id: 3 });

      // Execute
      const result = await accountCornService.createCompanyDefine(
        mockCompanyDefineList,
        0,
        mockDeliveryData,
        mockProject,
      );

      // Verify
      expect(db.DynamicCompanyDefine.create).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });

    it('should handle existing company define', async () => {
      // Mock data
      const mockCompanyDefineList = [{ id: 1 }];
      const mockDeliveryData = { id: 1 };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicCompanyDefine.findOne.mockResolvedValue({ id: 2 });

      // Execute
      const result = await accountCornService.createCompanyDefine(
        mockCompanyDefineList,
        0,
        mockDeliveryData,
        mockProject,
      );

      // Verify
      expect(db.DynamicCompanyDefine.create).not.toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });

    it('should handle recursive call for multiple items', async () => {
      // Mock data
      const mockCompanyDefineList = [
        { id: 1, DeliverDefineWorkId: 1 },
        { id: 2, DeliverDefineWorkId: 2 },
      ];
      const mockDeliveryData = { id: 1 };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicCompanyDefine.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockCompanyDefineList[0]);
      db.DynamicDeliverDefineWork.findOne.mockResolvedValue({ id: 2 });
      db.DynamicCompanyDefine.create.mockResolvedValue({ id: 3 });

      // Execute
      const result = await accountCornService.createCompanyDefine(
        mockCompanyDefineList,
        0,
        mockDeliveryData,
        mockProject,
      );

      // Verify
      expect(db.DynamicCompanyDefine.create).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });
  });

  describe('createDynamicNotification', () => {
    it('should create dynamic notification', async () => {
      // Mock data
      const mockNewNotification = { id: 1 };
      const mockNewProject = { id: 1 };
      const ProjectId = 2;
      const mockNotificationList = [
        {
          id: 1,
          MemberId: 1,
          ProjectId: 2,
        },
      ];

      // Setup mocks
      DeliveryPersonNotification.findAll.mockResolvedValue(mockNotificationList);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockNotificationList);
      db.DynamicMember.findOne.mockResolvedValue({ id: 2 });
      db.DynamicDeliveryPersonNotification.create.mockResolvedValue({ id: 3 });

      // Execute
      await accountCornService.createDynamicNotification(
        mockNewNotification,
        mockNewProject,
        ProjectId,
      );

      // Verify
      expect(DeliveryPersonNotification.findAll).toHaveBeenCalledWith({ where: { ProjectId: 2 } });
      expect(db.DynamicDeliveryPersonNotification.create).toHaveBeenCalled();
    });
  });

  describe('createDeliverDefine', () => {
    it('should create deliver define when not exists', async () => {
      // Mock data
      const mockDefineList = [
        {
          id: 1,
          DeliverDefineWorkId: 1,
        },
      ];
      const mockDeliveryData = { id: 1 };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicDeliverDefine.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest.fn().mockResolvedValue(mockDefineList[0]);
      db.DynamicDeliverDefineWork.findOne.mockResolvedValue({ id: 2 });
      db.DynamicDeliverDefine.create.mockResolvedValue({ id: 3 });

      // Execute
      const result = await accountCornService.createDeliverDefine(
        mockDefineList,
        0,
        mockDeliveryData,
        mockProject,
      );

      // Verify
      expect(db.DynamicDeliverDefine.create).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });

    it('should create deliver define work when not exists', async () => {
      // Mock data
      const mockDefineList = [
        {
          id: 1,
          DeliverDefineWorkId: 1,
        },
      ];
      const mockDeliveryData = { id: 1 };
      const mockProject = { id: 1 };

      // Setup mocks
      db.DynamicDeliverDefine.findOne.mockResolvedValue(null);
      accountCornService.convertObject = jest
        .fn()
        .mockResolvedValueOnce(mockDefineList[0])
        .mockResolvedValueOnce({ id: 1, publicSchemaId: 1 });
      db.DynamicDeliverDefineWork.findOne.mockResolvedValue(null);
      DeliverDefineWork.findOne.mockResolvedValue({ id: 1 });
      db.DynamicDeliverDefineWork.create.mockResolvedValue({ id: 2 });
      db.DynamicDeliverDefine.create.mockResolvedValue({ id: 3 });

      // Execute
      const result = await accountCornService.createDeliverDefine(
        mockDefineList,
        0,
        mockDeliveryData,
        mockProject,
      );

      // Verify
      expect(db.DynamicDeliverDefineWork.create).toHaveBeenCalled();
      expect(db.DynamicDeliverDefine.create).toHaveBeenCalled();
      expect(result).toEqual({ status: true });
    });

    it('should handle errors in createDeliverDefine', async () => {
      // Mock data
      const mockDefineList = [{ id: 1 }];
      const mockDeliveryData = { id: 1 };
      const mockProject = { id: 1 };

      // Setup mocks to throw error
      db.DynamicDeliverDefine.findOne.mockRejectedValue(new Error('Database error'));

      // Mock console.error to avoid noise in tests
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

      // Execute and verify
      await expect(
        accountCornService.createDeliverDefine(mockDefineList, 0, mockDeliveryData, mockProject),
      ).rejects.toThrow();

      consoleSpy.mockRestore();
    });
  });
});
