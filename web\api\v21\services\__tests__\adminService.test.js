// Mock all dependencies BEFORE importing anything else
jest.mock('bcrypt', () => ({
    compare: jest.fn(),
}));
jest.mock('crypto', () => ({
    randomBytes: jest.fn(),
}));
jest.mock('../../middlewares/jwtGenerator', () => ({
    token: jest.fn(),
}));
jest.mock('../../mailer', () => ({
    sendMail: jest.fn(),
}));
jest.mock('../password', () => ({
    bcryptPassword: jest.fn(),
}));
jest.mock('../../helpers/generatePassword', () => ({
    generatePassword: jest.fn().mockReturnValue('generatedPassword123'),
}));
jest.mock('../../helpers/apiError', () => {
    return jest.fn().mockImplementation((message, status) => {
        const error = new Error(message);
        error.status = status;
        return error;
    });
});

// Mock Sequelize and models
jest.mock('../../models', () => {
    const mockOp = {
        in: Symbol.for('in'),
    };

    const mockSequelize = {
        and: jest.fn((a, b) => ({ [Symbol.for('and')]: [a, b] })),
        or: jest.fn((a) => ({ [Symbol.for('or')]: a })),
        Op: mockOp,
    };

    return {
        User: {
            findOne: jest.fn(),
            createInstance: jest.fn(),
            update: jest.fn(),
        },
        ParentCompany: {
            getBy: jest.fn(),
            createInstance: jest.fn(),
        },
        Project: {
            update: jest.fn(),
        },
        Company: {
            createInstance: jest.fn(),
        },
        Enterprise: {
            createInstance: jest.fn(),
        },
        RestrictEmail: {
            getBy: jest.fn(),
        },
        Member: {
            findAll: jest.fn(),
            update: jest.fn(),
        },
        BillingHistory: {
            create: jest.fn(),
            createInstance: jest.fn(),
        },
        Sequelize: mockSequelize,
        Op: mockOp,
    };
});

// Now import the modules after mocking
const adminService = require('../adminService');
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const jwtGenerator = require('../../middlewares/jwtGenerator');
const ApiError = require('../../helpers/apiError');
const MAILER = require('../../mailer');
const { bcryptPassword } = require('../password');
const {
    User,
    ParentCompany,
    Project,
    Company,
    Enterprise,
    RestrictEmail,
    Member,
    BillingHistory,
    Sequelize,
    Op,
} = require('../../models');

describe('AdminService', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
        // Restore all spies
        jest.restoreAllMocks();
    });

    describe('createAccountAdmin', () => {
        const mockUser = {
            basicDetails: {
                email: '<EMAIL>',
            },
        };

        it('should create account admin successfully', async () => {
            const mockDone = jest.fn();
            const mockUserInput = {
                basicDetails: {
                    email: '<EMAIL>',
                },
                companyDetails: {
                    fullName: 'Test User',
                    name: 'Test Company',
                },
            };

            RestrictEmail.getBy.mockResolvedValue(null);

            // Mock registerUser to call callback with success
            jest.spyOn(adminService, 'registerUser').mockImplementation((userData, callback) => {
                callback({ id: 1 }, null);
            });

            await adminService.createAccountAdmin(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({ id: 1 }),
                false
            );
        });

        it('should handle restricted email domain', async () => {
            const mockDone = jest.fn();
            RestrictEmail.getBy.mockResolvedValue({ id: 1, isActive: true });

            await adminService.createAccountAdmin(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                { message: 'This Email domain is restricted.' }
            );
        });

        it('should handle errors during account creation', async () => {
            const mockDone = jest.fn();
            RestrictEmail.getBy.mockRejectedValue(new Error('Database error'));

            await adminService.createAccountAdmin(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('emailDomain', () => {
        it('should extract domain from simple email', async () => {
            const result = await adminService.emailDomain({ email: '<EMAIL>' });
            expect(result).toBe('example.com');
        });

        it('should extract domain from email with subdomain', async () => {
            const result = await adminService.emailDomain({ email: '<EMAIL>' });
            expect(result).toBe('example.com');
        });
    });

    describe('adminLogin', () => {
        const mockUserData = {
            email: '<EMAIL>',
            password: 'password123',
        };

        it('should login admin successfully', async () => {
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                userType: 'super admin',
                password: 'hashedPassword',
                updateInstance: jest.fn().mockResolvedValue(true),
            };

            User.findOne.mockResolvedValue(mockUser);
            bcrypt.compare.mockResolvedValue(true);
            jwtGenerator.token.mockReturnValue('mockToken');

            const mockDone = jest.fn();
            await adminService.adminLogin(mockUserData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({ token: 'mockToken' }),
                false
            );
        });

        it('should handle invalid email', async () => {
            User.findOne.mockResolvedValue(null);
            const mockDone = jest.fn();

            await adminService.adminLogin(mockUserData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                expect.any(ApiError)
            );
        });

        it('should handle invalid password', async () => {
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                userType: 'super admin',
                password: 'hashedPassword',
            };

            User.findOne.mockResolvedValue(mockUser);
            bcrypt.compare.mockResolvedValue(false);
            const mockDone = jest.fn();

            await adminService.adminLogin(mockUserData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                expect.any(ApiError)
            );
        });
    });

    describe('forgotPassword', () => {
        const mockUser = {
            id: 1,
            email: '<EMAIL>',
            isActive: true,
            updateInstance: jest.fn(),
        };

        it('should handle forgot password successfully', async () => {
            crypto.randomBytes.mockReturnValue({ toString: () => 'mockToken' });
            mockUser.updateInstance.mockResolvedValue({ ...mockUser, resetPasswordToken: 'mockToken' });
            MAILER.sendMail.mockImplementation((user, template, subject, type, callback) => {
                callback('success', null);
            });

            const mockDone = jest.fn();
            await adminService.forgotPassword(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({ resetPasswordToken: 'mockToken' }),
                false
            );
        });

        it('should handle inactive user', async () => {
            const inactiveUser = { ...mockUser, isActive: false };
            const mockDone = jest.fn();

            await adminService.forgotPassword(inactiveUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                expect.any(ApiError)
            );
        });

        it('should handle mail sending error', async () => {
            crypto.randomBytes.mockReturnValue({ toString: () => 'mockToken' });
            mockUser.updateInstance.mockResolvedValue({ ...mockUser, resetPasswordToken: 'mockToken' });
            MAILER.sendMail.mockImplementation((user, template, subject, type, callback) => {
                callback(null, new Error('Mail error'));
            });

            const mockDone = jest.fn();
            await adminService.forgotPassword(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                expect.any(ApiError)
            );
        });
    });

    describe('resetPassword', () => {
        const mockUser = {
            id: 1,
            updateInstance: jest.fn(),
        };

        it('should reset password successfully', async () => {
            bcryptPassword.mockResolvedValue('hashedPassword');
            mockUser.updateInstance.mockResolvedValue(true);

            const mockDone = jest.fn();
            await adminService.resetPassword(mockUser, { password: 'newPassword' }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(true, false);
        });

        it('should handle password reset error', async () => {
            const mockUser = {
                id: 1,
                updateInstance: jest.fn(),
            };
            bcryptPassword.mockRejectedValue(new Error('Hashing error'));
            const mockDone = jest.fn();

            await adminService.resetPassword(mockUser, { password: 'newPassword' }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(true, expect.any(Error));
        });
    });

    describe('createEnterprise', () => {
        const mockInput = {
            amountDetails: {
                amount: 100,
                currencyType: 'USD',
                currencySymbol: '$',
            },
            basicDetails: {
                projectCount: 5,
            },
            domainDetails: {
                name: 'Test Enterprise',
                domainURL: 'test.com',
            },
        };

        const mockNewUser = { id: 1 };
        const mockNewCompany = { id: 1 };
        const mockParentCompany = { id: 1 };

        it('should create enterprise successfully', async () => {
            Enterprise.createInstance.mockResolvedValue({ id: 1 });
            BillingHistory.create.mockResolvedValue({ id: 1 });

            const result = await adminService.createEnterprise(
                mockInput,
                mockNewUser,
                mockNewCompany,
                mockParentCompany
            );

            expect(result).toEqual(expect.objectContaining({ id: 1 }));
            expect(BillingHistory.create).toHaveBeenCalled();
        });

        it('should handle enterprise creation error', async () => {
            Enterprise.createInstance.mockRejectedValue(new Error('Creation error'));

            await expect(
                adminService.createEnterprise(
                    mockInput,
                    mockNewUser,
                    mockNewCompany,
                    mockParentCompany
                )
            ).rejects.toThrow('Creation error');
        });
    });

    describe('updateProject', () => {
        const mockEmailDomain = 'example.com';
        const mockUserDetail = { id: 1 };

        it('should update project successfully', async () => {
            ParentCompany.getBy.mockResolvedValue({ id: 1 });
            Project.update.mockResolvedValue([1]);
            Member.findAll.mockResolvedValue([{ UserId: 1 }]);
            Member.update.mockResolvedValue([1]);
            User.update.mockResolvedValue([1]);

            const result = await adminService.updateProject(mockEmailDomain, mockUserDetail);

            expect(result).toEqual(mockUserDetail);
            expect(Project.update).toHaveBeenCalled();
            expect(Member.update).toHaveBeenCalled();
            expect(User.update).toHaveBeenCalled();
        });

        it('should handle project update error', async () => {
            ParentCompany.getBy.mockRejectedValue(new Error('Update error'));

            await expect(
                adminService.updateProject(mockEmailDomain, mockUserDetail)
            ).rejects.toThrow('Update error');
        });

        it('should handle empty member details', async () => {
            ParentCompany.getBy.mockResolvedValue({ id: 1 });
            Project.update.mockResolvedValue([1]);
            Member.findAll.mockResolvedValue([]);
            Member.update.mockResolvedValue([1]);
            User.update.mockResolvedValue([1]);

            const result = await adminService.updateProject(mockEmailDomain, mockUserDetail);

            expect(result).toEqual(mockUserDetail);
            expect(User.update).toHaveBeenCalledWith(
                { userType: 'account admin' },
                { where: { id: { [Op.in]: [] } } }
            );
        });

        it('should handle multiple members', async () => {
            ParentCompany.getBy.mockResolvedValue({ id: 1 });
            Project.update.mockResolvedValue([1]);
            Member.findAll.mockResolvedValue([
                { UserId: 1 },
                { UserId: 2 },
                { UserId: 3 }
            ]);
            Member.update.mockResolvedValue([1]);
            User.update.mockResolvedValue([1]);

            const result = await adminService.updateProject(mockEmailDomain, mockUserDetail);

            expect(result).toEqual(mockUserDetail);
            expect(User.update).toHaveBeenCalledWith(
                { userType: 'account admin' },
                { where: { id: { [Op.in]: [1, 2, 3] } } }
            );
        });
    });

    describe('createNewUser', () => {
        const mockUserInput = {
            basicDetails: {
                email: '<EMAIL>',
                firstName: 'Test',
            },
            companyDetails: {
                fullName: 'Test User',
            },
        };

        it('should create new user successfully', async () => {
            const mockNewUser = { id: 1, email: '<EMAIL>' };
            User.createInstance.mockResolvedValue(mockNewUser);

            const result = await adminService.createNewUser(mockUserInput);

            expect(result).toEqual(mockNewUser);
            expect(User.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    email: '<EMAIL>',
                    firstName: 'Test User',
                    password: 'generatedPassword123',
                    userType: 'account admin',
                })
            );
        });

        it('should handle user creation error', async () => {
            User.createInstance.mockRejectedValue(new Error('Creation failed'));

            await expect(
                adminService.createNewUser(mockUserInput)
            ).rejects.toThrow('Creation failed');
        });

        it('should handle missing company details', async () => {
            const inputWithoutCompany = {
                basicDetails: {
                    email: '<EMAIL>',
                },
                companyDetails: {},
            };

            User.createInstance.mockResolvedValue({ id: 1 });

            await adminService.createNewUser(inputWithoutCompany);

            expect(User.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    firstName: undefined,
                    userType: 'account admin',
                })
            );
        });
    });

    describe('commonRegister', () => {
        const mockUserInput = {
            basicDetails: {
                email: '<EMAIL>',
                password: 'generatedPassword123',
            },
            companyDetails: {
                fullName: 'Test User',
                name: 'Test Company',
            },
            emailDomainName: 'example.com',
            amountDetails: {
                amount: 100,
                currencyType: 'USD',
                currencySymbol: '$',
            },
            domainDetails: {
                name: 'Test Enterprise',
                domainURL: 'test.com',
            },
        };

        it('should register user with new parent company', async () => {
            const mockDone = jest.fn();
            const mockNewUser = { id: 1 };
            const mockNewCompany = { id: 1 };
            const mockParentCompany = { id: 1 };
            const mockEnterprise = { id: 1 };

            User.createInstance.mockResolvedValue(mockNewUser);
            ParentCompany.getBy.mockResolvedValue(null);
            ParentCompany.createInstance.mockResolvedValue(mockParentCompany);
            Company.createInstance.mockResolvedValue(mockNewCompany);
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });

            await adminService.commonRegister(mockUserInput, null, mockDone);

            expect(ParentCompany.createInstance).toHaveBeenCalledWith({
                UserId: 1,
                emailDomainName: 'example.com',
            });
            expect(mockDone).toHaveBeenCalledWith(mockEnterprise, false);
        });

        it('should register user with existing parent company', async () => {
            const mockDone = jest.fn();
            const mockNewUser = { id: 1 };
            const mockNewCompany = { id: 1 };
            const mockExistingParentCompany = { id: 2 };
            const mockEnterprise = { id: 1 };

            User.createInstance.mockResolvedValue(mockNewUser);
            ParentCompany.getBy.mockResolvedValue(mockExistingParentCompany);
            Company.createInstance.mockResolvedValue(mockNewCompany);
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });

            await adminService.commonRegister(mockUserInput, null, mockDone);

            expect(ParentCompany.createInstance).not.toHaveBeenCalled();
            expect(Company.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    ParentCompanyId: 2,
                    createdBy: 1,
                    companyAutoId: 1,
                })
            );
            expect(mockDone).toHaveBeenCalledWith(mockEnterprise, false);
        });

        it('should handle registration error', async () => {
            const mockDone = jest.fn();
            User.createInstance.mockRejectedValue(new Error('Registration failed'));

            await adminService.commonRegister(mockUserInput, null, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should remove fullName from company details', async () => {
            const mockDone = jest.fn();
            const mockNewUser = { id: 1 };
            const mockNewCompany = { id: 1 };
            const mockParentCompany = { id: 1 };
            const mockEnterprise = { id: 1 };

            User.createInstance.mockResolvedValue(mockNewUser);
            ParentCompany.getBy.mockResolvedValue(mockParentCompany);
            Company.createInstance.mockResolvedValue(mockNewCompany);
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });

            await adminService.commonRegister(mockUserInput, null, mockDone);

            expect(Company.createInstance).toHaveBeenCalledWith(
                expect.not.objectContaining({
                    fullName: expect.anything(),
                })
            );
        });
    });

    describe('createExistAccountAdmin', () => {
        const mockUserInput = {
            basicDetails: {
                email: '<EMAIL>',
            },
            userExist: { id: 1 },
            amountDetails: {
                amount: 100,
                currencyType: 'USD',
                currencySymbol: '$',
            },
            domainDetails: {
                name: 'Test Enterprise',
                domainURL: 'test.com',
            },
        };

        it('should create existing account admin successfully', async () => {
            const mockDone = jest.fn();
            const mockParentCompany = { id: 1 };
            const mockEnterprise = { id: 1 };

            ParentCompany.getBy.mockResolvedValue(mockParentCompany);
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });
            Project.update.mockResolvedValue([1]);
            Member.findAll.mockResolvedValue([]);
            Member.update.mockResolvedValue([1]);
            User.update.mockResolvedValue([1]);

            await adminService.createExistAccountAdmin(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(mockEnterprise, false);
        });

        it('should handle creation error', async () => {
            const mockDone = jest.fn();
            ParentCompany.getBy.mockRejectedValue(new Error('Creation failed'));

            await adminService.createExistAccountAdmin(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createNewAccountAdmin', () => {
        const mockUserInput = {
            basicDetails: {
                email: '<EMAIL>',
            },
            companyDetails: {
                fullName: 'Test User',
            },
        };

        it('should create new account admin successfully', async () => {
            const mockDone = jest.fn();
            const mockNewUser = { id: 1 };
            const mockEnterprise = { id: 1 };

            User.createInstance.mockResolvedValue(mockNewUser);
            ParentCompany.getBy.mockResolvedValue({ id: 1 });
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });
            Project.update.mockResolvedValue([1]);
            Member.findAll.mockResolvedValue([]);
            Member.update.mockResolvedValue([1]);
            User.update.mockResolvedValue([1]);

            // Mock the createExistAccountAdmin method
            jest.spyOn(adminService, 'createExistAccountAdmin').mockImplementation((userData, callback) => {
                callback(mockEnterprise, false);
            });

            await adminService.createNewAccountAdmin(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(mockEnterprise, false);
        });

        it('should handle creation error', async () => {
            const mockDone = jest.fn();
            User.createInstance.mockRejectedValue(new Error('Creation failed'));

            await adminService.createNewAccountAdmin(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle createExistAccountAdmin error', async () => {
            const mockDone = jest.fn();
            const mockNewUser = { id: 1 };

            User.createInstance.mockResolvedValue(mockNewUser);

            // Mock the createExistAccountAdmin method to return error
            jest.spyOn(adminService, 'createExistAccountAdmin').mockImplementation((userData, callback) => {
                callback(null, new Error('Exist account creation failed'));
            });

            await adminService.createNewAccountAdmin(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                { status: false, error: expect.any(Error) }
            );
        });
    });

    describe('registerUser', () => {
        const mockUserInput = {
            basicDetails: {
                email: '<EMAIL>',
            },
            companyDetails: {
                fullName: 'Test User',
            },
            emailDomainName: 'example.com',
        };

        it('should register user successfully', async () => {
            const mockDone = jest.fn();
            const mockResponse = { id: 1 };

            // Mock the commonRegister method
            jest.spyOn(adminService, 'commonRegister').mockImplementation((userInput, newResult, callback) => {
                callback(mockResponse, false);
            });

            await adminService.registerUser(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(mockResponse, null);
        });

        it('should handle registration error', async () => {
            const mockDone = jest.fn();
            const mockError = new Error('Registration failed');

            // Mock the commonRegister method to return error
            jest.spyOn(adminService, 'commonRegister').mockImplementation((userInput, newResult, callback) => {
                callback(null, mockError);
            });

            await adminService.registerUser(mockUserInput, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('sendMail', () => {
        const mockUserData = {
            email: '<EMAIL>',
            name: 'Test User',
        };

        it('should send mail successfully', async () => {
            const mockDone = jest.fn();
            MAILER.sendMail.mockImplementation((userData, template, subject, type, callback) => {
                callback('success', null);
            });

            await adminService.sendMail(mockUserData, mockDone);

            expect(MAILER.sendMail).toHaveBeenCalledWith(
                mockUserData,
                'register',
                'Registration',
                'Sign Up',
                expect.any(Function)
            );
            expect(mockDone).toHaveBeenCalledWith(mockUserData, false);
        });

        it('should handle mail sending error', async () => {
            const mockDone = jest.fn();
            const mockError = new Error('Mail sending failed');
            MAILER.sendMail.mockImplementation((userData, template, subject, type, callback) => {
                callback(null, mockError);
            });

            await adminService.sendMail(mockUserData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(ApiError));
        });
    });

    // Additional edge cases and negative tests for existing methods
    describe('emailDomain - Additional Edge Cases', () => {
        it('should handle email with multiple subdomains', async () => {
            const result = await adminService.emailDomain({ email: '<EMAIL>' });
            expect(result).toBe('sub.example.com');
        });

        it('should handle email with single domain', async () => {
            const result = await adminService.emailDomain({ email: '<EMAIL>' });
            expect(result).toBe('example.co');
        });

        it('should handle email with complex subdomain structure', async () => {
            const result = await adminService.emailDomain({ email: '<EMAIL>' });
            expect(result).toBe('b.c.example.com');
        });

        it('should handle edge case with minimal domain', async () => {
            const result = await adminService.emailDomain({ email: 'test@a.b' });
            expect(result).toBe('a.b');
        });
    });

    describe('adminLogin - Additional Edge Cases', () => {
        it('should handle folloit admin user type', async () => {
            const mockUserData = {
                email: '<EMAIL>',
                password: 'password123',
            };

            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                userType: 'folloit admin',
                password: 'hashedPassword',
                updateInstance: jest.fn().mockResolvedValue(true),
            };

            User.findOne.mockResolvedValue(mockUser);
            bcrypt.compare.mockResolvedValue(true);
            jwtGenerator.token.mockReturnValue('mockToken');

            const mockDone = jest.fn();
            await adminService.adminLogin(mockUserData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    token: 'mockToken',
                    userType: 'folloit admin'
                }),
                false
            );
        });

        it('should handle database error during user lookup', async () => {
            const mockUserData = {
                email: '<EMAIL>',
                password: 'password123',
            };

            User.findOne.mockRejectedValue(new Error('Database connection failed'));
            const mockDone = jest.fn();

            await expect(
                adminService.adminLogin(mockUserData, mockDone)
            ).rejects.toThrow('Database connection failed');
        });

        it('should handle bcrypt comparison error', async () => {
            const mockUserData = {
                email: '<EMAIL>',
                password: 'password123',
            };

            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                userType: 'super admin',
                password: 'hashedPassword',
            };

            User.findOne.mockResolvedValue(mockUser);
            bcrypt.compare.mockRejectedValue(new Error('Bcrypt error'));
            const mockDone = jest.fn();

            await expect(
                adminService.adminLogin(mockUserData, mockDone)
            ).rejects.toThrow('Bcrypt error');
        });

        it('should handle user update error', async () => {
            const mockUserData = {
                email: '<EMAIL>',
                password: 'password123',
            };

            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                userType: 'super admin',
                password: 'hashedPassword',
                updateInstance: jest.fn().mockRejectedValue(new Error('Update failed')),
            };

            User.findOne.mockResolvedValue(mockUser);
            bcrypt.compare.mockResolvedValue(true);
            const mockDone = jest.fn();

            await expect(
                adminService.adminLogin(mockUserData, mockDone)
            ).rejects.toThrow('Update failed');
        });
    });

    describe('forgotPassword - Additional Edge Cases', () => {
        it('should handle user with null isActive', async () => {
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                isActive: null,
                updateInstance: jest.fn(),
            };
            const mockDone = jest.fn();

            await adminService.forgotPassword(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                expect.any(ApiError)
            );
        });

        it('should handle user with undefined isActive', async () => {
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                isActive: undefined,
                updateInstance: jest.fn(),
            };
            const mockDone = jest.fn();

            await adminService.forgotPassword(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                expect.any(ApiError)
            );
        });

        it('should handle user update error', async () => {
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                isActive: true,
                updateInstance: jest.fn().mockRejectedValue(new Error('Update failed')),
            };
            const mockDone = jest.fn();

            crypto.randomBytes.mockReturnValue({ toString: () => 'mockToken' });

            await expect(
                adminService.forgotPassword(mockUser, mockDone)
            ).rejects.toThrow('Update failed');
        });

        it('should handle crypto.randomBytes error', async () => {
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                isActive: true,
                updateInstance: jest.fn(),
            };
            const mockDone = jest.fn();

            crypto.randomBytes.mockImplementation(() => {
                throw new Error('Crypto error');
            });

            await expect(
                adminService.forgotPassword(mockUser, mockDone)
            ).rejects.toThrow('Crypto error');
        });
    });

    describe('resetPassword - Additional Edge Cases', () => {
        it('should handle bcryptPassword error', async () => {
            const mockUser = {
                id: 1,
                updateInstance: jest.fn(),
            };
            const mockDone = jest.fn();

            bcryptPassword.mockRejectedValue(new Error('Bcrypt error'));

            await adminService.resetPassword(mockUser, { password: 'newPassword' }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(true, expect.any(Error));
        });

        it('should handle user update error during reset', async () => {
            const mockUser = {
                id: 1,
                updateInstance: jest.fn().mockRejectedValue(new Error('Update failed')),
            };
            const mockDone = jest.fn();

            bcryptPassword.mockResolvedValue('hashedPassword');

            await adminService.resetPassword(mockUser, { password: 'newPassword' }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(true, expect.any(Error));
        });

        it('should reset all password-related fields', async () => {
            const mockUser = {
                id: 1,
                updateInstance: jest.fn().mockResolvedValue(true),
            };
            const mockDone = jest.fn();

            bcryptPassword.mockResolvedValue('hashedPassword');

            await adminService.resetPassword(mockUser, { password: 'newPassword' }, mockDone);

            expect(mockUser.updateInstance).toHaveBeenCalledWith(1, {
                password: 'hashedPassword',
                resetPasswordToken: null,
                otpCode: null,
                secret: null,
            });
            expect(mockDone).toHaveBeenCalledWith(true, false);
        });
    });

    describe('createEnterprise - Additional Edge Cases', () => {
        const mockInput = {
            amountDetails: {
                amount: 100,
                currencyType: 'USD',
                currencySymbol: '$',
            },
            basicDetails: {
                projectCount: 5,
            },
            domainDetails: {
                name: 'Test Enterprise',
                domainURL: 'test.com',
            },
        };

        const mockNewUser = { id: 1 };
        const mockNewCompany = { id: 1 };
        const mockParentCompany = { id: 1 };

        it('should create billing history with correct next payment date', async () => {
            const mockEnterprise = { id: 1 };
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });

            const result = await adminService.createEnterprise(
                mockInput,
                mockNewUser,
                mockNewCompany,
                mockParentCompany
            );

            expect(BillingHistory.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    UserId: 1,
                    EnterpriseId: 1,
                    amount: 100,
                    currency: 'USD',
                    status: 'completed',
                    paymentMethod: 'offline',
                    lastPayment: expect.any(Date),
                    nextPayment: expect.any(Date),
                })
            );

            // Verify next payment is one year from now
            const billingCall = BillingHistory.create.mock.calls[0][0];
            const nextPayment = billingCall.nextPayment;
            const lastPayment = billingCall.lastPayment;
            const yearDiff = nextPayment.getFullYear() - lastPayment.getFullYear();
            expect(yearDiff).toBe(1);
        });

        it('should handle BillingHistory creation error', async () => {
            const mockEnterprise = { id: 1 };
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockRejectedValue(new Error('Billing creation failed'));

            await expect(
                adminService.createEnterprise(
                    mockInput,
                    mockNewUser,
                    mockNewCompany,
                    mockParentCompany
                )
            ).rejects.toThrow('Billing creation failed');
        });

        it('should create enterprise with all required parameters', async () => {
            const mockEnterprise = { id: 1 };
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });

            await adminService.createEnterprise(
                mockInput,
                mockNewUser,
                mockNewCompany,
                mockParentCompany
            );

            expect(Enterprise.createInstance).toHaveBeenCalledWith({
                UserId: 1,
                name: 'Test Enterprise',
                domainURL: 'test.com',
                CompanyId: 1,
                isActive: true,
                amount: 100,
                currencyType: 'USD',
                currencySymbol: '$',
                projectCount: 5,
                ParentCompanyId: 1,
            });
        });

        it('should handle null company ID', async () => {
            const mockEnterprise = { id: 1 };
            Enterprise.createInstance.mockResolvedValue(mockEnterprise);
            BillingHistory.create.mockResolvedValue({ id: 1 });

            await adminService.createEnterprise(
                mockInput,
                mockNewUser,
                { id: null },
                mockParentCompany
            );

            expect(Enterprise.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    CompanyId: null,
                })
            );
        });
    });

    describe('createAccountAdmin - Additional Edge Cases', () => {
        it('should handle registerUser callback with error', async () => {
            const mockUser = {
                basicDetails: {
                    email: '<EMAIL>',
                },
            };
            const mockDone = jest.fn();
            const mockError = new Error('Registration failed');

            RestrictEmail.getBy.mockResolvedValue(null);

            // Mock registerUser to call callback with error
            jest.spyOn(adminService, 'registerUser').mockImplementation((userData, callback) => {
                callback(null, mockError);
            });

            await adminService.createAccountAdmin(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle registerUser callback with success', async () => {
            const mockUser = {
                basicDetails: {
                    email: '<EMAIL>',
                },
            };
            const mockDone = jest.fn();
            const mockResponse = { id: 1, email: '<EMAIL>' };

            RestrictEmail.getBy.mockResolvedValue(null);

            // Mock registerUser to call callback with success
            jest.spyOn(adminService, 'registerUser').mockImplementation((userData, callback) => {
                callback(mockResponse, null);
            });

            await adminService.createAccountAdmin(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(mockResponse, false);
        });

        it('should handle emailDomain extraction error', async () => {
            const mockUser = {
                basicDetails: {
                    email: 'invalid-email',
                },
            };
            const mockDone = jest.fn();

            // Mock emailDomain to throw error
            jest.spyOn(adminService, 'emailDomain').mockRejectedValue(new Error('Invalid email format'));

            await adminService.createAccountAdmin(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should set emailDomainName on userData', async () => {
            const mockUser = {
                basicDetails: {
                    email: '<EMAIL>',
                },
            };
            const mockDone = jest.fn();

            RestrictEmail.getBy.mockResolvedValue(null);

            // Mock registerUser to verify userData has emailDomainName
            jest.spyOn(adminService, 'registerUser').mockImplementation((userData, callback) => {
                expect(userData.emailDomainName).toBe('example.com');
                callback({ id: 1 }, null);
            });

            await adminService.createAccountAdmin(mockUser, mockDone);

            expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
        });

        it('should handle inactive restricted email domain', async () => {
            const mockUser = {
                basicDetails: {
                    email: '<EMAIL>',
                },
            };
            const mockDone = jest.fn();

            // When isActive is false, the query should return null (not found)
            RestrictEmail.getBy.mockResolvedValue(null);

            // Mock registerUser
            jest.spyOn(adminService, 'registerUser').mockImplementation((userData, callback) => {
                callback({ id: 1 }, null);
            });

            await adminService.createAccountAdmin(mockUser, mockDone);

            // Should proceed with registration since no active restriction was found
            expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
        });
    });
});