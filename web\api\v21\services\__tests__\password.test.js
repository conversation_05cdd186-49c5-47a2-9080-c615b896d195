const bcrypt = require('bcrypt');
const { bcryptPassword } = require('../password');

// Mock bcrypt module
jest.mock('bcrypt', () => ({
    genSalt: jest.fn(),
    hash: jest.fn(),
    compare: jest.fn()
}));

describe('Password Service', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('bcryptPassword', () => {
        it('should successfully hash a password', async () => {
            // Arrange
            const mockPassword = 'testPassword123';
            const mockSalt = 'mockSalt123';
            const mockHashedPassword = 'hashedPassword123';

            bcrypt.genSalt.mockResolvedValue(mockSalt);
            bcrypt.hash.mockResolvedValue(mockHashedPassword);

            // Act
            const result = await new Promise((resolve) => {
                bcryptPassword(mockPassword, (hashedPassword) => {
                    resolve(hashedPassword);
                });
            });

            // Assert
            expect(bcrypt.genSalt).toHaveBeenCalledWith(15);
            expect(bcrypt.hash).toHaveBeenCalledWith(mockPassword, mockSalt);
            expect(result).toBe(mockHashedPassword);
        });

        it('should handle empty password', async () => {
            // Arrange
            const mockPassword = '';
            const mockSalt = 'mockSalt123';
            const mockHashedPassword = 'hashedEmptyPassword';

            bcrypt.genSalt.mockResolvedValue(mockSalt);
            bcrypt.hash.mockResolvedValue(mockHashedPassword);

            // Act
            const result = await new Promise((resolve) => {
                bcryptPassword(mockPassword, (hashedPassword) => {
                    resolve(hashedPassword);
                });
            });

            // Assert
            expect(bcrypt.genSalt).toHaveBeenCalledWith(15);
            expect(bcrypt.hash).toHaveBeenCalledWith(mockPassword, mockSalt);
            expect(result).toBe(mockHashedPassword);
        });

        it('should handle special characters in password', async () => {
            // Arrange
            const mockPassword = '!@#$%^&*()_+';
            const mockSalt = 'mockSalt123';
            const mockHashedPassword = 'hashedSpecialChars';

            bcrypt.genSalt.mockResolvedValue(mockSalt);
            bcrypt.hash.mockResolvedValue(mockHashedPassword);

            // Act
            const result = await new Promise((resolve) => {
                bcryptPassword(mockPassword, (hashedPassword) => {
                    resolve(hashedPassword);
                });
            });

            // Assert
            expect(bcrypt.genSalt).toHaveBeenCalledWith(15);
            expect(bcrypt.hash).toHaveBeenCalledWith(mockPassword, mockSalt);
            expect(result).toBe(mockHashedPassword);
        });

        it('should handle bcrypt.genSalt error', async () => {
            // Arrange
            const mockPassword = 'testPassword123';
            const mockError = new Error('Salt generation failed');

            bcrypt.genSalt.mockRejectedValue(mockError);

            // Act & Assert
            await expect(new Promise((resolve, reject) => {
                bcryptPassword(mockPassword, (hashedPassword) => {
                    resolve(hashedPassword);
                });
            })).rejects.toThrow('Salt generation failed');
        });

        it('should handle bcrypt.hash error', async () => {
            // Arrange
            const mockPassword = 'testPassword123';
            const mockSalt = 'mockSalt123';
            const mockError = new Error('Hash generation failed');

            bcrypt.genSalt.mockResolvedValue(mockSalt);
            bcrypt.hash.mockRejectedValue(mockError);

            // Act & Assert
            await expect(new Promise((resolve, reject) => {
                bcryptPassword(mockPassword, (hashedPassword) => {
                    resolve(hashedPassword);
                });
            })).rejects.toThrow('Hash generation failed');
        });

        it('should handle very long password', async () => {
            // Arrange
            const mockPassword = 'a'.repeat(1000); // Very long password
            const mockSalt = 'mockSalt123';
            const mockHashedPassword = 'hashedLongPassword';

            bcrypt.genSalt.mockResolvedValue(mockSalt);
            bcrypt.hash.mockResolvedValue(mockHashedPassword);

            // Act
            const result = await new Promise((resolve) => {
                bcryptPassword(mockPassword, (hashedPassword) => {
                    resolve(hashedPassword);
                });
            });

            // Assert
            expect(bcrypt.genSalt).toHaveBeenCalledWith(15);
            expect(bcrypt.hash).toHaveBeenCalledWith(mockPassword, mockSalt);
            expect(result).toBe(mockHashedPassword);
        });
    });
}); 