const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  craneRequestCommentController: {
    createCraneRequestComment: jest.fn(),
    getCraneRequestComments: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  craneRequestCommentValidation: {
    createCraneRequestComment: jest.fn(),
    getCraneRequestComments: jest.fn(),
  },
}));

describe('craneRequestCommentRoute', () => {
  let router;
  let craneRequestCommentRoute;
  let craneRequestCommentController;
  let passportConfig;
  let craneRequestCommentValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    craneRequestCommentRoute = require('../craneRequestCommentRoute');
    const controllers = require('../../controllers');
    craneRequestCommentController = controllers.craneRequestCommentController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    craneRequestCommentValidation = validations.craneRequestCommentValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = craneRequestCommentRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(1);

      // Verify POST route
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/create_crane_request_comment',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        craneRequestCommentController.createCraneRequestComment,
      );

      // Verify GET route
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/get_crane_request_comments/:CraneRequestId/:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        craneRequestCommentController.getCraneRequestComments,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(2);
      expect(validate).toHaveBeenCalledWith(
        craneRequestCommentValidation.createCraneRequestComment,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestCommentValidation.getCraneRequestComments,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = craneRequestCommentRoute.router;
      const result2 = craneRequestCommentRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication and validation middleware for all routes', () => {
      craneRequestCommentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have validation and authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
        expect(call).toContain(passportConfig.isAuthenticated);
        expect(call).toHaveLength(4); // path + validation + auth + controller
      });
    });

    it('should configure routes in the correct order', () => {
      craneRequestCommentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      expect(postCalls[0][0]).toBe('/create_crane_request_comment');
      expect(getCalls[0][0]).toBe('/get_crane_request_comments/:CraneRequestId/:ParentCompanyId/:ProjectId');
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof craneRequestCommentRoute).toBe('object');
      expect(craneRequestCommentRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestCommentRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestCommentRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
