const moment = require('moment');
const { Sequelize, Enterprise, VersionUpdates } = require('../models');

let { Member, User, DeliveryPersonNotification, Notification } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const notificationService = {
  async buildSearchCondition(searchTerm) {
    if (!searchTerm || Object.keys(searchTerm).length === 0) return {};

    return {
      [Op.and]: [{
        [Op.or]: [
          { '$Project.projectName$': { [Sequelize.Op.iLike]: `%${searchTerm}%` } },
          { description: { [Sequelize.Op.iLike]: `%${searchTerm}%` } },
          { title: { [Sequelize.Op.iLike]: `%${searchTerm}%` } },
          { type: { [Sequelize.Op.iLike]: `%${searchTerm}%` } }
        ]
      }]
    };
  },

  async buildBaseCondition(incomeData) {
    const condition = {
      isDeleted: false,
      MemberId: { [Op.not]: Sequelize.col('Project.memberDetails.id') }
    };

    if (incomeData.ProjectId) {
      condition.ProjectId = +incomeData.ProjectId;
    }

    if (incomeData.descriptionFilter) {
      condition.description = { [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%` };
    }

    if (incomeData.dateFilter) {
      const endDate = moment(incomeData.dateFilter).add(1, 'days').format('YYYY-MM-DD');
      condition.createdAt = {
        [Op.between]: [new Date(`${incomeData.dateFilter.split('T')[0]} 00:00:00`), endDate]
      };
    }

    if (incomeData.statusFilter) {
      condition.type = incomeData.statusFilter;
    }

    if (incomeData.projectNameFilter) {
      condition.ProjectId = incomeData.projectNameFilter;
    }

    return condition;
  },

  async listNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const loginUser = inputData.user;

      const memberDetails = await Member.findAll({
        where: Sequelize.and({ UserId: loginUser.id, isActive: true, isDeleted: false })
      });

      if (!memberDetails.length) {
        return done(null, { message: 'Member Does not exist.' });
      }

      const memberDelivery = memberDetails.map(element => element.id);
      const subCondition = this.buildSubCondition(memberDetails, memberDelivery);

      const condition = await this.buildBaseCondition(incomeData);
      const searchCondition = await this.buildSearchCondition(incomeData.search);

      const notificationList = await Notification.getAll(
        condition,
        searchCondition,
        loginUser,
        subCondition
      );

      const result = { count: 0, rows: [] };

      this.getLimitedData(notificationList.rows, offset, +params.pageSize, 0, [], async (newResponse, newError) => {
        if (!newError) {
          result.rows = newResponse;
          result.count = notificationList.rows.length;
          condition.seen = false;
          result.unSeenCount = await Notification.getUnSeenCount(
            condition,
            loginUser,
            subCondition
          );
          done(result, false);
        } else {
          done(null, { message: 'Something went wrong' });
        }
      });
    } catch (e) {
      done(null, e);
    }
  },

  buildSubCondition(memberDetails, memberDelivery) {
    const subCondition = { isDeleted: false, isActive: true };
    if (memberDetails[0].RoleId === 4 || memberDetails[0].RoleId === 3) {
      subCondition.MemberId = { [Op.and]: [{ [Op.in]: memberDelivery }] };
    }
    return subCondition;
  },

  async setReadNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const newNot = await DeliveryPersonNotification.update(
        { seen: true },
        { where: { id: inputData.query.id } },
      );
      done(newNot, false);
    } catch (e) {
      done(null, e);
    }
  },
  
  async setReadAllNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);

      const newNot = await DeliveryPersonNotification.findAll(
        { where: { ProjectId: inputData.ProjectId } },
      );
      done(newNot, false);
    } catch (e) {
      done(null, e);
    }
  },
  
  async getNotificationCount(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const subCondition = { isDeleted: false };
      const memberDelivery = [];
      const memberDetails = await Member.findAll({
        where: Sequelize.and({ UserId: loginUser.id, isActive: true, isDeleted: false }),
      });
      this.populateMemberDelivery(memberDetails, memberDelivery);
      const condition = this.buildCondition(inputData);

      if (memberDetails) {
        const count = await this.getUnseenCountForMembers(memberDetails, subCondition, memberDelivery, condition, loginUser);
        done(count, false);
      } else {
        done(null, { message: 'Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  populateMemberDelivery(memberDetails, memberDelivery) {
    memberDetails.forEach(element => {
      memberDelivery.push(element.id);
    });
  },

  buildCondition(inputData) {
    const condition = { seen: false };
    if (inputData.query.ProjectId !== 'undefined' && inputData.query.ProjectId !== undefined) {
      condition.ProjectId = inputData.query.ProjectId;
    }
    return condition;
  },

  async getUnseenCountForMembers(memberDetails, subCondition, memberDelivery, condition, loginUser) {
    if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      subCondition.MemberId = { [Op.and]: [{ [Op.in]: memberDelivery }] };
    }
    const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
    return count.length;
  },

  async findEnterpriseByDomain(domainName) {
    if (!domainName) return null;

    const enterprise = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() }
    });

    return enterprise || null;
  },

  async findUserByEmail(email) {
    if (!email) return null;
    return await publicUser.findOne({ where: { email } });
  },

  async findMemberByUserId(userId) {
    if (!userId) return null;
    return await publicMember.findOne({
      where: { UserId: userId, RoleId: { [Op.ne]: 4 }, isDeleted: false }
    });
  },

  async findEnterpriseByParentCompanyId(ParentCompanyId) {
    if (!ParentCompanyId || ParentCompanyId === 'undefined') return null;

    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
  },

  async getDomainFromUserData(inputData) {
    const { domainName, email } = inputData.user;
    let domain = domainName;
    const ParentCompanyId = this.extractParentCompanyId(inputData);

    if (!domain && ParentCompanyId) {
      domain = await this.retrieveDomain(email, ParentCompanyId);
    }

    return domain;
  },

  extractParentCompanyId(inputData) {
    return inputData.body.ParentCompanyId ||
      inputData.params.ParentCompanyId ||
      (inputData.query ? +inputData.query.ParentCompanyId : undefined);
  },

  async retrieveDomain(email, ParentCompanyId) {
    const userData = await this.findUserByEmail(email);
    if (!userData) return null;

    const memberData = await this.findMemberByUserId(userData.id);
    if (memberData) {
      return await this.getDomainFromMemberData(memberData, ParentCompanyId);
    } else {
      return await this.getFallbackDomain(ParentCompanyId);
    }
  },

  async getDomainFromMemberData(memberData, ParentCompanyId) {
    if (memberData.isAccount) {
      const enterprise = await Enterprise.findOne({ where: { id: memberData.EnterpriseId, status: 'completed' } });
      return enterprise ? enterprise.name.toLowerCase() : null;
    } else {
      return await this.getFallbackDomain(ParentCompanyId);
    }
  },

  async getFallbackDomain(ParentCompanyId) {
    const enterprise = await this.findEnterpriseByParentCompanyId(ParentCompanyId);
    return enterprise ? enterprise.name.toLowerCase() : null;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const domainName = await this.getDomainFromUserData(inputData);
    const modelObj = await helper.getDynamicModel(domainName);

    Notification = modelObj.Notification;
    Member = modelObj.Member;
    User = modelObj.User;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;

    if (domainName) {
      inputData.user = await this.updateUserWithDomain(inputData, domainName);
    }

    return inputData.body.ProjectId;
  },

  async updateUserWithDomain(inputData, domainName) {
    const newUser = await User.findOne({ where: { email: inputData.user.email } });
    return newUser || inputData.user;
  },

  async getLimitedData(result, index, limit, count, finalResult, done) {
    const element = result[index];
    if (index < result.length && count < limit) {
      finalResult.push(element);
      let i = count;
      i += 1;
      this.getLimitedData(result, index + 1, limit, i, finalResult, (response, err) => {
        if (!err) {
          done(response, false);
        } else {
          done(null, err);
        }
      });
    } else {
      done(finalResult, false);
    }
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async deleteNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { query } = inputData;
      const { id } = query;
      const notDetails = await Notification.findByPk(id);
      if (notDetails) {
        const memberData = await this.findMemberData(inputData, notDetails);
        const updateNotification = await this.deleteNotificationEntry(notDetails, memberData);
        done(updateNotification, false);
      } else {
        done(null, { message: 'Notification Does not Exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async findMemberData(inputData, notDetails) {
    return await Member.findOne({
      where: { UserId: inputData.user.id, ProjectId: notDetails.ProjectId, isDeleted: false },
    });
  },

  async deleteNotificationEntry(notDetails, memberData) {
    const overAllData = await DeliveryPersonNotification.findOne({
      where: { NotificationId: notDetails.id, MemberId: memberData.id },
    });
    return await overAllData.destroy();
  },

  async versionUpdation(inputData) {
    try {
      const versionDetails = await VersionUpdates.update(
        {
          version: inputData.body.version,
          iosversion: inputData.body.iosversion,
          releasenote: JSON.stringify(inputData.body.releasenote),
        },
        {
          where: { id: 1 },
        },
      );
      if (versionDetails) {
        return { status: 200, message: 'version updated successfully.' };
      }
      return { status: 500, message: 'cannot update the version' };
    } catch (e) {
      console.log(e);
    }
  },

  async getVersion() {
    try {
      const versionDetails = await VersionUpdates.findOne({
        where: { id: 1 },
      });
      return {
        status: 200,
        data: {
          version: versionDetails.version,
          releasenote: JSON.parse(versionDetails.releasenote),
          iosversion: versionDetails.iosversion,
        },
      };
    } catch (e) {
      console.log(e);
    }
  },
};

module.exports = notificationService;