const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  CalendarController: {
    getEventNDR: jest.fn(),
    getDeliveryRequestWithCrane: jest.fn(),
    getConcreteRequest: jest.fn(),
    getInspectionEventNDR: jest.fn(),
    captureInspectionEventNDR: jest.fn(),
    captureEventNDR: jest.fn(),
    captureConcreteEventNDR: jest.fn(),
    captureCraneEventNDR: jest.fn(),
    getAllCalendarData: jest.fn(),
    checkOverlappingToRestore: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  calendarValidation: {
    getEventNDR: jest.fn(),
    getDeliveryRequestWithCrane: jest.fn(),
  },
}));

describe('calendarRoute', () => {
  let router;
  let calendarRoute;
  let CalendarController;
  let passportConfig;
  let calendarValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    calendarRoute = require('../calendarRoute');
    const controllers = require('../../controllers');
    CalendarController = controllers.CalendarController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    calendarValidation = validations.calendarValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = calendarRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(6);

      // Verify POST routes with validation
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/event_NDR/:ProjectId/:void',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CalendarController.getEventNDR,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        2,
        '/get_crane_associated_request/:ProjectId/:void',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        CalendarController.getDeliveryRequestWithCrane,
      );

      // Verify POST routes without validation
      expect(router.post).toHaveBeenNthCalledWith(
        3,
        '/get_concrete_request/:ProjectId/:void',
        passportConfig.isAuthenticated,
        CalendarController.getConcreteRequest,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        4,
        '/get_inspection_request/:ProjectId/:void',
        passportConfig.isAuthenticated,
        CalendarController.getInspectionEventNDR,
      );

      // Verify complex route with multiple controllers
      expect(router.post).toHaveBeenNthCalledWith(
        5,
        '/get_all_calendar/:ProjectId/:void',
        passportConfig.isAuthenticated,
        CalendarController.captureInspectionEventNDR,
        CalendarController.captureEventNDR,
        CalendarController.captureConcreteEventNDR,
        CalendarController.captureCraneEventNDR,
        CalendarController.getAllCalendarData,
      );

      expect(router.post).toHaveBeenNthCalledWith(
        6,
        '/checkOverlapping',
        passportConfig.isAuthenticated,
        CalendarController.checkOverlappingToRestore,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(2);
      expect(validate).toHaveBeenCalledWith(
        calendarValidation.getEventNDR,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        calendarValidation.getDeliveryRequestWithCrane,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = calendarRoute.router;
      const result2 = calendarRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes in the correct order', () => {
      calendarRoute.router;

      const postCalls = router.post.mock.calls;

      expect(postCalls[0][0]).toBe('/event_NDR/:ProjectId/:void');
      expect(postCalls[1][0]).toBe('/get_crane_associated_request/:ProjectId/:void');
      expect(postCalls[2][0]).toBe('/get_concrete_request/:ProjectId/:void');
      expect(postCalls[3][0]).toBe('/get_inspection_request/:ProjectId/:void');
      expect(postCalls[4][0]).toBe('/get_all_calendar/:ProjectId/:void');
      expect(postCalls[5][0]).toBe('/checkOverlapping');
    });

    it('should use authentication middleware for all routes', () => {
      calendarRoute.router;

      const postCalls = router.post.mock.calls;

      // All routes should have authentication
      postCalls.forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should configure complex middleware chain for get_all_calendar route', () => {
      calendarRoute.router;

      const getAllCalendarCall = router.post.mock.calls[4];

      // Should have authentication + 5 controller methods
      expect(getAllCalendarCall).toHaveLength(7);
      expect(getAllCalendarCall[1]).toBe(passportConfig.isAuthenticated);
      expect(getAllCalendarCall[2]).toBe(CalendarController.captureInspectionEventNDR);
      expect(getAllCalendarCall[3]).toBe(CalendarController.captureEventNDR);
      expect(getAllCalendarCall[4]).toBe(CalendarController.captureConcreteEventNDR);
      expect(getAllCalendarCall[5]).toBe(CalendarController.captureCraneEventNDR);
      expect(getAllCalendarCall[6]).toBe(CalendarController.getAllCalendarData);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof calendarRoute).toBe('object');
      expect(calendarRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(calendarRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(calendarRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
