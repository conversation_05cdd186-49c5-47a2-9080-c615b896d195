// Mock models first
jest.mock('../../models', () => ({
  Plan: {
    updatePlan: jest.fn(),
  },
  StripePlan: {
    findOne: jest.fn(),
  },
  Sequelize: {
    and: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
  },
  StripeSubscription: {
    findOne: jest.fn(),
    update: jest.fn(),
  },
  Project: {
    findOne: jest.fn(),
    update: jest.fn(),
  },
}));

// Mock stripe
jest.mock('stripe', () => {
  return jest.fn(() => ({
    products: {
      list: jest.fn(),
    },
    billingPortal: {
      sessions: {
        create: jest.fn(),
      },
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
    customers: {
      create: jest.fn(),
    },
    checkout: {
      sessions: {
        create: jest.fn(),
      },
    },
    paymentMethods: {
      list: jest.fn(),
    },
  }));
});

// Mock moment
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  const mockMoment = jest.fn((input, format) => {
    if (input && format) {
      return actualMoment(input, format);
    }
    return actualMoment(input);
  });
  mockMoment.format = jest.fn(() => '2023-01-01');
  mockMoment.add = jest.fn(() => mockMoment);
  return mockMoment;
});

// Mock services
jest.mock('../../services', () => ({
  stripeService: {
    stripeAddProduct: jest.fn(),
    stripeProductcreate: jest.fn(),
    planList: jest.fn(),
    upgradePlanList: jest.fn(),
    addCard: jest.fn(),
    subscribe: jest.fn(),
    subscription: jest.fn(),
    cancelSubscription: jest.fn(),
    holdSubscription: jest.fn(),
    listPlans: jest.fn(),
    updatePlanDetail: jest.fn(),
    getStripePlans: jest.fn(),
    getOldSubscribersList: jest.fn(),
  },
}));

const StripeController = require('../StripeController');
const { stripeService } = require('../../services');
const { Plan, StripePlan, Sequelize, User, StripeSubscription, Project } = require('../../models');
const stripe = require('stripe')();

describe('StripeController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1, stripeCustomerId: 'cus_123' },
      headers: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      send: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();

    // Reset global.io mock
    global.io = {
      emit: jest.fn(),
    };
  });

  describe('createPlan', () => {
    it('should create plan successfully when product exists', async () => {
      mockReq.body = {
        product: { name: 'Test Product' },
        plan: { nickName: 'Test Plan' },
      };

      StripePlan.findOne.mockResolvedValue(null);
      stripe.products.list.mockResolvedValue({
        data: [{ id: 'prod_123', name: 'Test Product' }],
      });
      stripeService.stripeAddProduct.mockImplementation((body, callback) => {
        callback(null, { id: 'plan_123', nickname: 'Test Plan' });
      });

      await StripeController.createPlan(mockReq, mockRes, mockNext);

      expect(StripePlan.findOne).toHaveBeenCalled();
      expect(stripe.products.list).toHaveBeenCalled();
      expect(stripeService.stripeAddProduct).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ id: 'plan_123', nickname: 'Test Plan' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should create plan successfully when product does not exist', async () => {
      mockReq.body = {
        product: { name: 'New Product' },
        plan: { nickName: 'New Plan' },
      };

      StripePlan.findOne.mockResolvedValue(null);
      stripe.products.list.mockResolvedValue({ data: [] });
      stripeService.stripeProductcreate.mockImplementation((product, callback) => {
        callback(null, { id: 'prod_456' });
      });
      stripeService.stripeAddProduct.mockImplementation((body, callback) => {
        callback(null, { id: 'plan_456', nickname: 'New Plan' });
      });

      await StripeController.createPlan(mockReq, mockRes, mockNext);

      expect(StripePlan.findOne).toHaveBeenCalled();
      expect(stripe.products.list).toHaveBeenCalled();
      expect(stripeService.stripeProductcreate).toHaveBeenCalled();
      expect(stripeService.stripeAddProduct).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ id: 'plan_456', nickname: 'New Plan' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error when plan already exists', async () => {
      mockReq.body = {
        product: { name: 'Existing Product' },
        plan: { nickName: 'Existing Plan' },
      };

      StripePlan.findOne.mockResolvedValue({ id: 1 });

      await StripeController.createPlan(mockReq, mockRes, mockNext);

      expect(StripePlan.findOne).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from stripe product creation', async () => {
      mockReq.body = {
        product: { name: 'New Product' },
        plan: { nickName: 'New Plan' },
      };

      const mockError = new Error('Stripe error');
      StripePlan.findOne.mockResolvedValue(null);
      stripe.products.list.mockResolvedValue({ data: [] });
      stripeService.stripeProductcreate.mockImplementation((product, callback) => {
        callback(mockError, null);
      });

      await StripeController.createPlan(mockReq, mockRes, mockNext);

      expect(stripeService.stripeProductcreate).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in create plan', async () => {
      const mockError = new Error('Exception error');
      StripePlan.findOne.mockRejectedValue(mockError);

      await StripeController.createPlan(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('listAllPlans', () => {
    it('should list all plans successfully', async () => {
      const mockResponse = [
        { id: 'plan_123', nickname: 'Basic Plan' },
        { id: 'plan_456', nickname: 'Premium Plan' },
      ];
      stripeService.planList.mockImplementation((params, callback) => {
        callback(mockResponse, null);
      });

      await StripeController.listAllPlans(mockReq, mockRes, mockNext);

      expect(stripeService.planList).toHaveBeenCalledWith(mockReq.params, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ response: mockResponse });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from list all plans', async () => {
      const mockError = new Error('Service error');
      stripeService.planList.mockImplementation((params, callback) => {
        callback(null, mockError);
      });

      await StripeController.listAllPlans(mockReq, mockRes, mockNext);

      expect(stripeService.planList).toHaveBeenCalledWith(mockReq.params, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in list all plans', async () => {
      const mockError = new Error('Exception error');
      stripeService.planList.mockImplementation(() => {
        throw mockError;
      });

      await StripeController.listAllPlans(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('upgradePlanList', () => {
    it('should get upgrade plan list successfully', async () => {
      const mockResponse = [
        { id: 'plan_789', nickname: 'Pro Plan' },
        { id: 'plan_101', nickname: 'Enterprise Plan' },
      ];
      stripeService.upgradePlanList.mockImplementation((params, callback) => {
        callback(mockResponse, null);
      });

      await StripeController.upgradePlanList(mockReq, mockRes, mockNext);

      expect(stripeService.upgradePlanList).toHaveBeenCalledWith(mockReq.params, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ response: mockResponse });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from upgrade plan list', async () => {
      const mockError = new Error('Service error');
      stripeService.upgradePlanList.mockImplementation((params, callback) => {
        callback(null, mockError);
      });

      await StripeController.upgradePlanList(mockReq, mockRes, mockNext);

      expect(stripeService.upgradePlanList).toHaveBeenCalledWith(mockReq.params, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in upgrade plan list', async () => {
      const mockError = new Error('Exception error');
      stripeService.upgradePlanList.mockImplementation(() => {
        throw mockError;
      });

      await StripeController.upgradePlanList(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('addCard', () => {
    it('should add card and subscribe successfully', async () => {
      const mockCardDetail = { id: 'card_123' };
      const mockSubDetail = { id: 'sub_123', status: 'active' };

      stripeService.addCard.mockImplementation((req, callback) => {
        callback(mockCardDetail, null);
      });
      stripeService.subscribe.mockImplementation((req, callback) => {
        callback(mockSubDetail, null);
      });

      await StripeController.addCard(mockReq, mockRes, mockNext);

      expect(stripeService.addCard).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(stripeService.subscribe).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockSubDetail });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from add card', async () => {
      const mockError = new Error('Card error');
      stripeService.addCard.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await StripeController.addCard(mockReq, mockRes, mockNext);

      expect(stripeService.addCard).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle error from subscription after card added', async () => {
      const mockCardDetail = { id: 'card_123' };
      const mockSubError = new Error('Subscription error');

      stripeService.addCard.mockImplementation((req, callback) => {
        callback(mockCardDetail, null);
      });
      stripeService.subscribe.mockImplementation((req, callback) => {
        callback(null, mockSubError);
      });

      await StripeController.addCard(mockReq, mockRes, mockNext);

      expect(stripeService.addCard).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(stripeService.subscribe).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockSubError);
    });

    it('should handle exception in add card', async () => {
      const mockError = new Error('Exception error');
      stripeService.addCard.mockImplementation(() => {
        throw mockError;
      });

      await StripeController.addCard(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('subscribe', () => {
    it('should subscribe successfully', async () => {
      const mockSubDetail = { id: 'sub_456', status: 'active' };
      stripeService.subscription.mockImplementation((req, callback) => {
        callback(mockSubDetail, null);
      });

      await StripeController.subscribe(mockReq, mockRes, mockNext);

      expect(stripeService.subscription).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockSubDetail });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from subscription', async () => {
      const mockError = new Error('Subscription error');
      stripeService.subscription.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await StripeController.subscribe(mockReq, mockRes, mockNext);

      expect(stripeService.subscription).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in subscribe', async () => {
      const mockError = new Error('Exception error');
      stripeService.subscription.mockImplementation(() => {
        throw mockError;
      });

      await StripeController.subscribe(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel subscription successfully', async () => {
      const mockSubDetail = { id: 'sub_123', status: 'canceled' };
      stripeService.cancelSubscription.mockImplementation((req, callback) => {
        callback(mockSubDetail, null);
      });

      await StripeController.cancelSubscription(mockReq, mockRes, mockNext);

      expect(stripeService.cancelSubscription).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockSubDetail });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from cancel subscription', async () => {
      const mockError = new Error('Cancel error');
      stripeService.cancelSubscription.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await StripeController.cancelSubscription(mockReq, mockRes, mockNext);

      expect(stripeService.cancelSubscription).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in cancel subscription', async () => {
      const mockError = new Error('Exception error');
      stripeService.cancelSubscription.mockImplementation(() => {
        throw mockError;
      });

      await StripeController.cancelSubscription(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('holdSubscription', () => {
    it('should hold subscription successfully', async () => {
      const mockSubDetail = { id: 'sub_123', status: 'paused' };
      stripeService.holdSubscription.mockImplementation((req, callback) => {
        callback(mockSubDetail, null);
      });

      await StripeController.holdSubscription(mockReq, mockRes, mockNext);

      expect(stripeService.holdSubscription).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ data: mockSubDetail });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from hold subscription', async () => {
      const mockError = new Error('Hold error');
      stripeService.holdSubscription.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await StripeController.holdSubscription(mockReq, mockRes, mockNext);

      expect(stripeService.holdSubscription).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it('should handle exception in hold subscription', async () => {
      const mockError = new Error('Exception error');
      stripeService.holdSubscription.mockImplementation(() => {
        throw mockError;
      });

      await StripeController.holdSubscription(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('listPlans', () => {
    it('should list plans successfully', async () => {
      const mockPlans = [
        { id: 'plan_123', nickname: 'Basic Plan' },
        { id: 'plan_456', nickname: 'Premium Plan' },
      ];
      stripeService.listPlans.mockResolvedValue(mockPlans);

      await StripeController.listPlans(mockReq, mockRes, mockNext);

      expect(stripeService.listPlans).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Plans listed Successfully.',
        data: mockPlans,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure in list plans', async () => {
      stripeService.listPlans.mockResolvedValue(null);

      await StripeController.listPlans(mockReq, mockRes, mockNext);

      expect(stripeService.listPlans).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 422,
        message: 'Cannot able to list',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error in list plans', async () => {
      const mockError = new Error('Service error');
      stripeService.listPlans.mockRejectedValue(mockError);

      await StripeController.listPlans(mockReq, mockRes, mockNext);

      expect(stripeService.listPlans).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updatePlanDetail', () => {
    it('should update plan detail successfully', async () => {
      stripeService.updatePlanDetail.mockResolvedValue(true);

      await StripeController.updatePlanDetail(mockReq, mockRes, mockNext);

      expect(stripeService.updatePlanDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Plan Updated Successfully.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure in update plan detail', async () => {
      stripeService.updatePlanDetail.mockResolvedValue(false);

      await StripeController.updatePlanDetail(mockReq, mockRes, mockNext);

      expect(stripeService.updatePlanDetail).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(422);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 422,
        message: 'Cannot able to update plan.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error in update plan detail', async () => {
      const mockError = new Error('Service error');
      stripeService.updatePlanDetail.mockRejectedValue(mockError);

      await StripeController.updatePlanDetail(mockReq, mockRes, mockNext);

      expect(stripeService.updatePlanDetail).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('editPlan', () => {
    it('should edit plan successfully', async () => {
      mockReq.body = { id: 1 };
      const mockPlanData = { id: 'plan_123', nickname: 'Updated Plan' };

      Plan.updatePlan.mockResolvedValue(true);
      stripeService.getStripePlans.mockResolvedValue(mockPlanData);
      stripeService.getOldSubscribersList.mockResolvedValue(true);

      await StripeController.editPlan(mockReq, mockRes, mockNext);

      expect(Plan.updatePlan).toHaveBeenCalledWith(1, { isPublished: false });
      expect(stripeService.getStripePlans).toHaveBeenCalledWith(mockReq);
      expect(stripeService.getOldSubscribersList).toHaveBeenCalledWith(mockReq, 1, mockPlanData);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Plan updated' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle failure in edit plan', async () => {
      mockReq.body = { id: 1 };

      Plan.updatePlan.mockResolvedValue(true);
      stripeService.getStripePlans.mockResolvedValue(null);

      await StripeController.editPlan(mockReq, mockRes, mockNext);

      expect(Plan.updatePlan).toHaveBeenCalledWith(1, { isPublished: false });
      expect(stripeService.getStripePlans).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should handle error in edit plan', async () => {
      const mockError = new Error('Database error');
      Plan.updatePlan.mockRejectedValue(mockError);

      await StripeController.editPlan(mockReq, mockRes, mockNext);

      expect(Plan.updatePlan).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('stripePortalSession', () => {
    it('should create stripe portal session successfully', async () => {
      const mockSession = { id: 'cs_123', url: 'https://billing.stripe.com/session/123' };
      stripe.billingPortal.sessions.create.mockResolvedValue(mockSession);

      await StripeController.stripePortalSession(mockReq, mockRes, mockNext);

      expect(stripe.billingPortal.sessions.create).toHaveBeenCalledWith({
        customer: 'cus_123',
        return_url: `${process.env.BASE_URL}/profile`,
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'stripe listed successfully.',
        data: mockSession,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error in stripe portal session', async () => {
      const mockError = new Error('Stripe error');
      stripe.billingPortal.sessions.create.mockRejectedValue(mockError);

      await StripeController.stripePortalSession(mockReq, mockRes, mockNext);

      expect(stripe.billingPortal.sessions.create).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('wehook', () => {
    beforeEach(() => {
      mockReq.headers = { 'stripe-signature': 'test_signature' };
      mockReq.body = 'test_body';
    });

    it('should process webhook successfully for customer.subscription.updated', async () => {
      const mockEvent = {
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_123',
            customer: 'cus_123',
            plan: {
              id: 'plan_123',
              nickname: 'monthly',
            },
          },
        },
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      User.findOne.mockResolvedValue({ id: 1 });
      StripePlan.findOne.mockResolvedValue({
        id: 1,
        stripeProductName: 'Project Plan',
        stripePlanName: 'monthly',
      });
      StripeSubscription.findOne.mockResolvedValue({ id: 1 });
      StripeSubscription.update.mockResolvedValue([1]);
      Project.findOne.mockResolvedValue({ id: 1 });
      Project.update.mockResolvedValue([1]);

      await StripeController.wehook(mockReq, mockRes);

      expect(stripe.webhooks.constructEvent).toHaveBeenCalledWith(
        'test_body',
        'test_signature',
        'whsec_sMoc4lWiJw8F5sBEvwjzmhiARSsi6lIC',
      );
      expect(User.findOne).toHaveBeenCalledWith({
        where: { stripeCustomerId: 'cus_123' },
      });
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.send).toHaveBeenCalledWith('webhook processed successfully');
    });

    it('should process webhook for trial plan', async () => {
      const mockEvent = {
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_123',
            customer: 'cus_123',
            plan: {
              id: 'plan_123',
              nickname: 'trial',
            },
          },
        },
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      User.findOne.mockResolvedValue({ id: 1 });
      StripePlan.findOne.mockResolvedValue({
        id: 1,
        stripeProductName: 'Trial Plan',
        stripePlanName: 'trial',
      });
      StripeSubscription.findOne.mockResolvedValue({ id: 1 });
      StripeSubscription.update.mockResolvedValue([1]);
      Project.findOne.mockResolvedValue({ id: 1 });
      Project.update.mockResolvedValue([1]);

      await StripeController.wehook(mockReq, mockRes);

      expect(Project.update).toHaveBeenCalledWith(
        expect.objectContaining({
          PlanId: 1,
          StripeSubscriptionId: 1,
          status: '',
          subscribedOn: expect.any(Date),
          startDate: expect.any(String),
          endDate: expect.any(String),
        }),
        { where: { id: 1 } },
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.send).toHaveBeenCalledWith('webhook processed successfully');
    });

    it('should handle webhook error', async () => {
      const mockError = new Error('Webhook error');
      stripe.webhooks.constructEvent.mockImplementation(() => {
        throw mockError;
      });

      // Mock console.log to avoid output during tests
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await StripeController.wehook(mockReq, mockRes);

      expect(consoleSpy).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.send).toHaveBeenCalledWith(`Webhook Error: ${mockError.message}`);

      consoleSpy.mockRestore();
    });

    it('should handle non-subscription-updated events', async () => {
      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: { object: {} },
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);

      await StripeController.wehook(mockReq, mockRes);

      expect(stripe.webhooks.constructEvent).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.send).toHaveBeenCalledWith('webhook processed successfully');
    });
  });

  describe('checkout', () => {
    it('should create checkout session successfully', async () => {
      mockReq.body = {
        planData: { stripePlanId: 'price_123' },
        url: '/success',
      };

      const mockPaymentMethods = {
        data: { card: 'card' },
      };
      const mockSession = {
        id: 'cs_123',
        url: 'https://checkout.stripe.com/session/123',
      };

      stripe.paymentMethods.list.mockResolvedValue(mockPaymentMethods);
      stripe.checkout.sessions.create.mockResolvedValue(mockSession);

      await StripeController.checkout(mockReq, mockRes);

      expect(stripe.paymentMethods.list).toHaveBeenCalledWith({
        customer: 'cus_123',
        type: 'card',
      });
      expect(stripe.checkout.sessions.create).toHaveBeenCalledWith({
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price: 'price_123',
            quantity: 1,
          },
        ],
        customer: 'cus_123',
        success_url: `${process.env.BASE_URL}/success?customerId=cus_123`,
        cancel_url: `${process.env.BASE_URL}/success`,
      });
      expect(mockRes.send).toHaveBeenCalledWith({
        status: 200,
        data: mockSession,
      });
    });

    it('should handle error in checkout', async () => {
      const mockError = new Error('Checkout error');
      stripe.paymentMethods.list.mockRejectedValue(mockError);

      // Mock console.log to avoid output during tests
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await StripeController.checkout(mockReq, mockRes);

      expect(consoleSpy).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.send).toHaveBeenCalledWith(`checkout Error: ${mockError.message}`);

      consoleSpy.mockRestore();
    });
  });

  describe('createCheckoutSession', () => {
    it('should create checkout session successfully', async () => {
      mockReq.body = {
        email: '<EMAIL>',
        name: 'Test User',
        phone: '1234567890',
        planData: { stripePlanId: 'price_123' },
        url: '/success',
      };

      const mockCustomer = { id: 'cus_456' };
      const mockSession = {
        id: 'cs_456',
        url: 'https://checkout.stripe.com/session/456',
      };

      stripe.customers.create.mockResolvedValue(mockCustomer);
      stripe.checkout.sessions.create.mockResolvedValue(mockSession);

      await StripeController.createCheckoutSession(mockReq, mockRes);

      expect(stripe.customers.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'Test User',
        phone: '1234567890',
      });
      expect(stripe.checkout.sessions.create).toHaveBeenCalledWith({
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price: 'price_123',
            quantity: 1,
          },
        ],
        customer: 'cus_456',
        success_url: `${process.env.BASE_URL}/success?customerId=cus_456`,
        cancel_url: `${process.env.BASE_URL}/success`,
      });
      expect(mockRes.send).toHaveBeenCalledWith({
        status: 200,
        data: mockSession,
      });
    });

    it('should handle error in create checkout session', async () => {
      const mockError = new Error('Create checkout error');
      stripe.customers.create.mockRejectedValue(mockError);

      // Mock console.log to avoid output during tests
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await StripeController.createCheckoutSession(mockReq, mockRes);

      expect(consoleSpy).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.send).toHaveBeenCalledWith(`checkout Error: ${mockError.message}`);

      consoleSpy.mockRestore();
    });
  });
});


