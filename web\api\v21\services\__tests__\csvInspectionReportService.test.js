const csvInspectionReportService = require('../csvInspectionReportService');

// Mock all dependencies
jest.mock('export-to-csv', () => ({
  ExportToCsv: jest.fn().mockImplementation(() => ({
    generateCsv: jest.fn(),
  })),
}));

jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return jest.fn((date) => {
    if (date) {
      return actualMoment(date);
    }
    return actualMoment('2023-01-01T10:00:00Z');
  });
});

describe('Direct helper function coverage', () => {
  const {
    getSelectedKeys,
    formatEquipmentDetails,
    formatDefineWorkDetails,
    formatCompanyDetails,
    formatMemberDetails
  } = require('../csvInspectionReportService');

  it('getSelectedKeys returns correct keys', () => {
    const headers = [
      { key: 'id', isActive: true },
      { key: 'description', isActive: false },
      { key: 'company', isActive: true }
    ];
    const result = getSelectedKeys(headers);
    expect(result.id).toBe(true);
    expect(result.description).toBe(false);
    expect(result.company).toBe(true);
  });

  it('formatEquipmentDetails returns joined names for valid array', () => {
    const arr = [{ Equipment: { equipmentName: 'A' } }, { Equipment: { equipmentName: 'B' } }];
    expect(formatEquipmentDetails(arr)).toBe('A, B');
  });
  it('formatEquipmentDetails returns - for empty, null, or non-array', () => {
    expect(formatEquipmentDetails([])).toBe('-');
    expect(formatEquipmentDetails(null)).toBe('-');
    expect(formatEquipmentDetails('not array')).toBe('-');
  });

  it('formatDefineWorkDetails returns joined names for valid array', () => {
    const arr = [{ DeliverDefineWork: { DFOW: 'X' } }, { DeliverDefineWork: { DFOW: 'Y' } }];
    expect(formatDefineWorkDetails(arr)).toBe('X, Y');
  });
  it('formatDefineWorkDetails returns - for empty, null, or non-array', () => {
    expect(formatDefineWorkDetails([])).toBe('-');
    expect(formatDefineWorkDetails(null)).toBe('-');
    expect(formatDefineWorkDetails('not array')).toBe('-');
  });

  it('formatCompanyDetails returns joined names for valid array', () => {
    const arr = [{ Company: { companyName: 'C1' } }, { Company: { companyName: 'C2' } }];
    expect(formatCompanyDetails(arr)).toBe('C1, C2');
  });
  it('formatCompanyDetails returns - for empty, null, or non-array', () => {
    expect(formatCompanyDetails([])).toBe('-');
    expect(formatCompanyDetails(null)).toBe('-');
    expect(formatCompanyDetails('not array')).toBe('-');
  });

  it('formatMemberDetails returns joined names for valid array', () => {
    const arr = [
      { Member: { User: { firstName: 'F', lastName: 'L' } } },
      { Member: { User: { firstName: 'A', lastName: 'B' } } }
    ];
    expect(formatMemberDetails(arr)).toBe('F L, A B');
  });
  it('formatMemberDetails returns - for empty, null, or non-array', () => {
    expect(formatMemberDetails([])).toBe('-');
    expect(formatMemberDetails(null)).toBe('-');
    expect(formatMemberDetails('not array')).toBe('-');
  });
});

jest.mock('../../middlewares/awsConfig', () => ({
  reportUpload: jest.fn(),
}));

describe('CsvInspectionReportService', () => {
  let mockDone;
  let mockExportToCsv;
  let mockAwsConfig;
  let mockData;
  let mockSelectedHeaders;

  beforeEach(() => {
    jest.clearAllMocks();

    mockDone = jest.fn();

    // Get mocked modules
    const { ExportToCsv } = require('export-to-csv');
    mockExportToCsv = ExportToCsv;
    mockAwsConfig = require('../../middlewares/awsConfig');

    // Setup default mock implementations
    mockExportToCsv.mockImplementation(() => ({
      generateCsv: jest.fn().mockResolvedValue('mock,csv,data\n1,test,value'),
    }));

    mockAwsConfig.reportUpload.mockImplementation((_buffer, _fileName, _exportType, callback) => {
      callback('http://example.com/file.csv', null);
    });

    // Mock data for testing
    mockData = [
      {
        InspectionId: 1,
        description: 'Test inspection',
        status: 'Delivered',
        inspectionStatus: 'Completed',
        inspectionType: 'Safety',
        deliveryStart: '2023-01-01T10:00:00Z',
        equipmentDetails: [
          {
            Equipment: {
              equipmentName: 'Crane A'
            }
          },
          {
            Equipment: {
              equipmentName: 'Crane B'
            }
          }
        ],
        definablefeatureofworkDetails: [
          {
            DeliverDefineWork: {
              DFOW: 'Foundation Work'
            }
          }
        ],
        responsiblecompanyDetails: [
          {
            Company: {
              companyName: 'ABC Construction'
            }
          }
        ],
        responsiblepersonDetails: [
          {
            Member: {
              User: {
                firstName: 'John',
                lastName: 'Doe'
              }
            }
          }
        ],
        locationPath: 'Building A > Floor 1',
        gateDetails: [
          {
            Gate: {
              gateName: 'Gate 1'
            }
          }
        ],
        approvedbyDetails: {
          User: {
            firstName: 'Jane',
            lastName: 'Smith'
          }
        }
      }
    ];

    mockSelectedHeaders = [
      { key: 'id', title: 'ID', isActive: true },
      { key: 'description', title: 'Description', isActive: true },
      { key: 'date', title: 'Date & Time', isActive: true },
      { key: 'status', title: 'Status', isActive: true },
      { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
      { key: 'inspectionType', title: 'Inspection Type', isActive: true },
      { key: 'approvedby', title: 'Approved By', isActive: true },
      { key: 'equipment', title: 'Equipment', isActive: true },
      { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
      { key: 'gate', title: 'Gate', isActive: true },
      { key: 'company', title: 'Responsible Company', isActive: true },
      { key: 'name', title: 'Responsible Person', isActive: true },
      { key: 'location', title: 'Location', isActive: true }
    ];
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(csvInspectionReportService).toBeDefined();
      expect(typeof csvInspectionReportService).toBe('object');
      expect(csvInspectionReportService.exportInspectionReportInCsvFormat).toBeDefined();
    });
  });

  describe('exportInspectionReportInCsvFormat', () => {
    it('should successfully export inspection report with all fields', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('mock,csv,data\n1,test,value')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        -300, // timezone offset
        'inspection_report',
        'csv',
        mockDone
      );

      expect(mockExportToCsv).toHaveBeenCalledWith({
        showLabels: true,
        showTitle: false,
        useTextFile: false,
        useBom: false,
        useKeysAsHeaders: true,
      });
      expect(mockCsvInstance.generateCsv).toHaveBeenCalledWith(expect.any(Array), true);
      expect(mockAwsConfig.reportUpload).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle AWS upload error', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('mock,csv,data\n1,test,value')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);
      mockAwsConfig.reportUpload.mockImplementation((_buffer, _fileName, _exportType, callback) => {
        callback(null, new Error('Upload failed'));
      });

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        -300,
        'inspection_report',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith(null, { message: 'cannot export document' });
    });

    it('should handle empty data array', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        [],
        mockSelectedHeaders,
        -300,
        'inspection_report',
        'csv',
        mockDone
      );

      expect(mockCsvInstance.generateCsv).toHaveBeenCalledWith([], true);
    });

    it('should handle inactive headers', async () => {
      const mockInactiveHeaders = [
        { key: 'id', title: 'ID', isActive: false },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'status', title: 'Status', isActive: false },
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Description\nTest inspection')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        mockInactiveHeaders,
        -300,
        'inspection_report',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null CSV generation', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue(null)
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        -300,
        'inspection_report',
        'csv',
        mockDone
      );

      // Should not call AWS upload or done callback when CSV is null
      expect(mockAwsConfig.reportUpload).not.toHaveBeenCalled();
      expect(mockDone).not.toHaveBeenCalled();
    });
  });

  describe('Data formatting tests', () => {
    it('should format equipment details correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment\nCrane A, Crane B')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const equipmentOnlyHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        equipmentOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty equipment details', async () => {
      const dataWithEmptyEquipment = [{
        ...mockData[0],
        equipmentDetails: []
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const equipmentOnlyHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyEquipment,
        equipmentOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null equipment details', async () => {
      const dataWithNullEquipment = [{
        ...mockData[0],
        equipmentDetails: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const equipmentOnlyHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullEquipment,
        equipmentOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format definable feature of work details correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('DFOW\nFoundation Work')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dfowOnlyHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        dfowOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty definable feature of work details', async () => {
      const dataWithEmptyDfow = [{
        ...mockData[0],
        definablefeatureofworkDetails: []
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('DFOW\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dfowOnlyHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyDfow,
        dfowOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format company details correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Company\nABC Construction')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const companyOnlyHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        companyOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty company details', async () => {
      const dataWithEmptyCompany = [{
        ...mockData[0],
        responsiblecompanyDetails: []
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Company\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const companyOnlyHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyCompany,
        companyOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Member and person details formatting', () => {
    it('should format member details correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Person\nJohn Doe')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const personOnlyHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        personOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty member details', async () => {
      const dataWithEmptyMember = [{
        ...mockData[0],
        responsiblepersonDetails: []
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Person\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const personOnlyHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyMember,
        personOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format approved by details correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\nJane Smith')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null approved by details', async () => {
      const dataWithNullApprovedBy = [{
        ...mockData[0],
        approvedbyDetails: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullApprovedBy,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle approved by with missing first name', async () => {
      const dataWithMissingFirstName = [{
        ...mockData[0],
        approvedbyDetails: {
          User: {
            firstName: null,
            lastName: 'Smith'
          }
        }
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMissingFirstName,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle approved by with missing last name', async () => {
      const dataWithMissingLastName = [{
        ...mockData[0],
        approvedbyDetails: {
          User: {
            firstName: 'John',
            lastName: null
          }
        }
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\nJohn null')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMissingLastName,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle approved by with empty first name', async () => {
      const dataWithEmptyFirstName = [{
        ...mockData[0],
        approvedbyDetails: {
          User: {
            firstName: '',
            lastName: 'Smith'
          }
        }
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyFirstName,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle approved by with undefined first name', async () => {
      const dataWithUndefinedFirstName = [{
        ...mockData[0],
        approvedbyDetails: {
          User: {
            firstName: undefined,
            lastName: 'Smith'
          }
        }
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithUndefinedFirstName,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle approved by with false first name', async () => {
      const dataWithFalseFirstName = [{
        ...mockData[0],
        approvedbyDetails: {
          User: {
            firstName: false,
            lastName: 'Smith'
          }
        }
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithFalseFirstName,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Location and Gate formatting', () => {
    it('should format location correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Location\nBuilding A > Floor 1')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const locationHeaders = [
        { key: 'location', title: 'Location', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        locationHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null location', async () => {
      const dataWithNullLocation = [{
        ...mockData[0],
        locationPath: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Location\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const locationHeaders = [
        { key: 'location', title: 'Location', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullLocation,
        locationHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format gate correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Gate\nGate 1')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const gateHeaders = [
        { key: 'gate', title: 'Gate', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        gateHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty gate details', async () => {
      const dataWithEmptyGate = [{
        ...mockData[0],
        gateDetails: []
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Gate\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const gateHeaders = [
        { key: 'gate', title: 'Gate', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyGate,
        gateHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null gate details', async () => {
      const dataWithNullGate = [{
        ...mockData[0],
        gateDetails: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Gate\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const gateHeaders = [
        { key: 'gate', title: 'Gate', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullGate,
        gateHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Status and basic field formatting', () => {
    it('should format status as Completed when status is Delivered', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Status\nCompleted')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const statusHeaders = [
        { key: 'status', title: 'Status', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        statusHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should keep original status when not Delivered', async () => {
      const dataWithPendingStatus = [{
        ...mockData[0],
        status: 'Pending'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Status\nPending')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const statusHeaders = [
        { key: 'status', title: 'Status', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithPendingStatus,
        statusHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle inspection status correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Inspection Status\nCompleted')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const inspectionStatusHeaders = [
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        inspectionStatusHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null inspection status', async () => {
      const dataWithNullInspectionStatus = [{
        ...mockData[0],
        inspectionStatus: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Inspection Status\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const inspectionStatusHeaders = [
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullInspectionStatus,
        inspectionStatusHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format ID correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('ID\n1')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const idHeaders = [
        { key: 'id', title: 'ID', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        idHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format description correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Description\nTest inspection')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const descriptionHeaders = [
        { key: 'description', title: 'Description', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        descriptionHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should format inspection type correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Inspection Type\nSafety')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const inspectionTypeHeaders = [
        { key: 'inspectionType', title: 'Inspection Type', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        inspectionTypeHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Date formatting and timezone handling', () => {
    it('should format date with timezone offset correctly', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Date & Time\nJan-01-2023 05:00 am')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dateHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        dateHeaders,
        -300, // -5 hours
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle positive timezone offset', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Date & Time\nJan-01-2023 03:00 pm')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dateHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        dateHeaders,
        300, // +5 hours
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle zero timezone offset', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Date & Time\nJan-01-2023 10:00 am')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dateHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        dateHeaders,
        0, // no offset
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle date formatting with different delivery start times', async () => {
      const dataWithDifferentDate = [{
        ...mockData[0],
        deliveryStart: '2023-12-25T15:30:00Z'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Date & Time\nDec-25-2023 03:30 pm')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dateHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithDifferentDate,
        dateHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Multiple items and complex data', () => {
    it('should handle multiple equipment items', async () => {
      const dataWithMultipleEquipment = [{
        ...mockData[0],
        equipmentDetails: [
          { Equipment: { equipmentName: 'Crane A' } },
          { Equipment: { equipmentName: 'Crane B' } },
          { Equipment: { equipmentName: 'Crane C' } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment\nCrane A, Crane B, Crane C')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const equipmentHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMultipleEquipment,
        equipmentHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle multiple DFOW items', async () => {
      const dataWithMultipleDfow = [{
        ...mockData[0],
        definablefeatureofworkDetails: [
          { DeliverDefineWork: { DFOW: 'Foundation Work' } },
          { DeliverDefineWork: { DFOW: 'Steel Work' } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('DFOW\nFoundation Work, Steel Work')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dfowHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMultipleDfow,
        dfowHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle multiple company items', async () => {
      const dataWithMultipleCompanies = [{
        ...mockData[0],
        responsiblecompanyDetails: [
          { Company: { companyName: 'ABC Construction' } },
          { Company: { companyName: 'XYZ Engineering' } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Company\nABC Construction, XYZ Engineering')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const companyHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMultipleCompanies,
        companyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle multiple member items', async () => {
      const dataWithMultipleMembers = [{
        ...mockData[0],
        responsiblepersonDetails: [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Person\nJohn Doe, Jane Smith')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const personHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMultipleMembers,
        personHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Edge cases and error scenarios', () => {
    it('should handle headers with unknown keys', async () => {
      const headersWithUnknownKeys = [
        { key: 'unknownKey', title: 'Unknown', isActive: true },
        { key: 'description', title: 'Description', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Description\nTest inspection')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        headersWithUnknownKeys,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle data with missing properties', async () => {
      const incompleteData = [{
        InspectionId: 2,
        description: 'Incomplete data test'
        // Missing other properties
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('ID,Description\n2,Incomplete data test')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const basicHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        incompleteData,
        basicHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle CSV generation error', async () => {
      const mockCsvInstance = {
        generateCsv: jest.fn().mockRejectedValue(new Error('CSV generation failed'))
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await expect(csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        mockSelectedHeaders,
        0,
        'test',
        'csv',
        mockDone
      )).rejects.toThrow('CSV generation failed');
    });

    it('should handle headers with inactive keys that are not in selectedKeys', async () => {
      const headersWithInactiveKeys = [
        { key: 'invalidKey', title: 'Invalid', isActive: true },
        { key: 'description', title: 'Description', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Description\nTest inspection')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        headersWithInactiveKeys,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null definable feature of work details', async () => {
      const dataWithNullDfow = [{
        ...mockData[0],
        definablefeatureofworkDetails: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('DFOW\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dfowOnlyHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullDfow,
        dfowOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null company details', async () => {
      const dataWithNullCompany = [{
        ...mockData[0],
        responsiblecompanyDetails: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Company\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const companyOnlyHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullCompany,
        companyOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle null member details', async () => {
      const dataWithNullMember = [{
        ...mockData[0],
        responsiblepersonDetails: null
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Person\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const personOnlyHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNullMember,
        personOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle non-array equipment details', async () => {
      const dataWithNonArrayEquipment = [{
        ...mockData[0],
        equipmentDetails: 'not an array'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const equipmentOnlyHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNonArrayEquipment,
        equipmentOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle non-array DFOW details', async () => {
      const dataWithNonArrayDfow = [{
        ...mockData[0],
        definablefeatureofworkDetails: 'not an array'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('DFOW\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dfowOnlyHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNonArrayDfow,
        dfowOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle non-array company details', async () => {
      const dataWithNonArrayCompany = [{
        ...mockData[0],
        responsiblecompanyDetails: 'not an array'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Company\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const companyOnlyHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNonArrayCompany,
        companyOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle non-array member details', async () => {
      const dataWithNonArrayMember = [{
        ...mockData[0],
        responsiblepersonDetails: 'not an array'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Person\n-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const personOnlyHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithNonArrayMember,
        personOnlyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should test all formatter functions with mixed data', async () => {
      const mixedData = [{
        InspectionId: 123,
        description: 'Mixed test',
        status: 'Pending',
        inspectionStatus: 'In Progress',
        inspectionType: 'Quality',
        deliveryStart: '2023-06-15T14:30:00Z',
        equipmentDetails: [
          { Equipment: { equipmentName: 'Mixer A' } },
          { Equipment: { equipmentName: 'Pump B' } }
        ],
        definablefeatureofworkDetails: [
          { DeliverDefineWork: { DFOW: 'Concrete Work' } }
        ],
        responsiblecompanyDetails: [
          { Company: { companyName: 'Test Corp' } }
        ],
        responsiblepersonDetails: [
          { Member: { User: { firstName: 'Alice', lastName: 'Johnson' } } }
        ],
        locationPath: 'Site A > Zone 1',
        gateDetails: [
          { Gate: { gateName: 'Main Gate' } }
        ],
        approvedbyDetails: {
          User: {
            firstName: 'Bob',
            lastName: 'Wilson'
          }
        }
      }];

      const allHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
        { key: 'inspectionType', title: 'Inspection Type', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('ID,Description,Date,Status\n123,Mixed test,Jun-15-2023 02:30 pm,Pending')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mixedData,
        allHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle headers with hasOwnProperty edge case', async () => {
      const headersWithPrototypeKey = [
        { key: 'constructor', title: 'Constructor', isActive: true },
        { key: 'description', title: 'Description', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Description\nTest inspection')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        mockData,
        headersWithPrototypeKey,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should handle empty array for all detail types', async () => {
      const dataWithEmptyArrays = [{
        ...mockData[0],
        equipmentDetails: [],
        definablefeatureofworkDetails: [],
        responsiblecompanyDetails: [],
        responsiblepersonDetails: []
      }];

      const allDetailHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment,DFOW,Company,Person\n-,-,-,-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithEmptyArrays,
        allDetailHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });

  describe('Individual formatter function coverage', () => {
    it('should call formatDefineWorkDetails with valid array', async () => {
      const dataWithValidDfow = [{
        ...mockData[0],
        definablefeatureofworkDetails: [
          { DeliverDefineWork: { DFOW: 'Test DFOW 1' } },
          { DeliverDefineWork: { DFOW: 'Test DFOW 2' } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('DFOW\nTest DFOW 1, Test DFOW 2')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dfowHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithValidDfow,
        dfowHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should call formatCompanyDetails with valid array', async () => {
      const dataWithValidCompany = [{
        ...mockData[0],
        responsiblecompanyDetails: [
          { Company: { companyName: 'Company A' } },
          { Company: { companyName: 'Company B' } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Company\nCompany A, Company B')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const companyHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithValidCompany,
        companyHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should call formatMemberDetails with valid array', async () => {
      const dataWithValidMember = [{
        ...mockData[0],
        responsiblepersonDetails: [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Person\nJohn Doe, Jane Smith')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const memberHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithValidMember,
        memberHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should call formatEquipmentDetails with valid array', async () => {
      const dataWithValidEquipment = [{
        ...mockData[0],
        equipmentDetails: [
          { Equipment: { equipmentName: 'Equipment A' } },
          { Equipment: { equipmentName: 'Equipment B' } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment\nEquipment A, Equipment B')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const equipmentHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithValidEquipment,
        equipmentHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should test all branches of Array.isArray checks', async () => {
      const dataWithMixedTypes = [{
        InspectionId: 1,
        equipmentDetails: undefined, // falsy but not array
        definablefeatureofworkDetails: {}, // truthy but not array
        responsiblecompanyDetails: 'string', // truthy but not array
        responsiblepersonDetails: 123 // truthy but not array
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment,DFOW,Company,Person\n-,-,-,-')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const allDetailHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithMixedTypes,
        allDetailHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should ensure all formatter functions are called with valid arrays', async () => {
      const dataWithAllValidArrays = [{
        InspectionId: 1,
        description: 'Test all formatters',
        equipmentDetails: [
          { Equipment: { equipmentName: 'Test Equipment' } }
        ],
        definablefeatureofworkDetails: [
          { DeliverDefineWork: { DFOW: 'Test DFOW' } }
        ],
        responsiblecompanyDetails: [
          { Company: { companyName: 'Test Company' } }
        ],
        responsiblepersonDetails: [
          { Member: { User: { firstName: 'Test', lastName: 'Person' } } }
        ]
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Equipment,DFOW,Company,Person\nTest Equipment,Test DFOW,Test Company,Test Person')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const allDetailHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithAllValidArrays,
        allDetailHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should test the specific line 19 branch for lastName', async () => {
      const dataWithApprovedBy = [{
        InspectionId: 1,
        approvedbyDetails: {
          User: {
            firstName: 'John',
            lastName: 'Doe'
          }
        }
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Approved By\nJohn Doe')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const approvedByHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithApprovedBy,
        approvedByHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should test the Date & Time formatter with specific timezone', async () => {
      const dataWithDate = [{
        InspectionId: 1,
        deliveryStart: '2023-01-01T10:00:00Z'
      }];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Date & Time\nJan-01-2023 10:00 am')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      const dateHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        dataWithDate,
        dateHeaders,
        0,
        'test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });

    it('should directly test function coverage by calling all formatters', async () => {
      // This test ensures all formatter functions are called by using the exact data structure
      const completeTestData = [{
        InspectionId: 999,
        description: 'Complete test',
        status: 'Active',
        inspectionStatus: 'Pending',
        inspectionType: 'Full',
        deliveryStart: '2023-01-01T12:00:00Z',
        locationPath: 'Test Location',
        equipmentDetails: [
          { Equipment: { equipmentName: 'Test Equipment 1' } },
          { Equipment: { equipmentName: 'Test Equipment 2' } }
        ],
        definablefeatureofworkDetails: [
          { DeliverDefineWork: { DFOW: 'Test DFOW 1' } },
          { DeliverDefineWork: { DFOW: 'Test DFOW 2' } }
        ],
        responsiblecompanyDetails: [
          { Company: { companyName: 'Test Company 1' } },
          { Company: { companyName: 'Test Company 2' } }
        ],
        responsiblepersonDetails: [
          { Member: { User: { firstName: 'Test', lastName: 'User1' } } },
          { Member: { User: { firstName: 'Test', lastName: 'User2' } } }
        ],
        gateDetails: [
          { Gate: { gateName: 'Test Gate' } }
        ],
        approvedbyDetails: {
          User: {
            firstName: 'Approver',
            lastName: 'Name'
          }
        }
      }];

      const completeHeaders = [
        { key: 'id', title: 'ID', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'inspectionStatus', title: 'Inspection Status', isActive: true },
        { key: 'inspectionType', title: 'Inspection Type', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      const mockCsvInstance = {
        generateCsv: jest.fn().mockResolvedValue('Complete CSV data')
      };
      mockExportToCsv.mockImplementation(() => mockCsvInstance);

      await csvInspectionReportService.exportInspectionReportInCsvFormat(
        completeTestData,
        completeHeaders,
        0,
        'complete_test',
        'csv',
        mockDone
      );

      expect(mockDone).toHaveBeenCalledWith('http://example.com/file.csv', false);
    });
  });
});
