const { Enterprise, Sequelize } = require('../models');
let { ConcreteRequestHistory, ConcreteRequest, User } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const historyService = {
  async getConcreteRequestHistories(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: inputData.params.ConcreteRequestId,
          ProjectId: +inputData.params.ProjectId,
          isDeleted: false,
        },
      });
      if (exist) {
        const historyList = await ConcreteRequestHistory.findAll({
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          where: {
            ConcreteRequestId: exist.id,
            ProjectId: +inputData.params.ProjectId,
          },
          order: [['id', 'DESC']],
        });
        done({ historyList, exist }, false);
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDomainFromEnterprise(domainName) {
    if (!domainName) return '';

    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() }
    });

    return domainEnterpriseValue ? domainName : '';
  },
  async getEnterpriseFromParentCompany(ParentCompanyId) {
    if (!ParentCompanyId || ParentCompanyId === 'undefined') return null;

    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
  },
  async getUserEnterprise(user, ParentCompanyId) {
    const { email } = user;
    if (!email) return null;

    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) return null;

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false }
    });

    if (!memberData) {
      return await this.getEnterpriseFromParentCompany(ParentCompanyId);
    }

    if (memberData.isAccount) {
      return await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' }
      });
    }

    return await this.getEnterpriseFromParentCompany(ParentCompanyId);
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
    let domainName = await this.getDomainFromEnterprise(inputData.user.domainName);

    if (!domainName && ParentCompanyId) {
      const enterpriseValue = await this.getUserEnterprise(inputData.user, ParentCompanyId);
      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
      }
    }

    const modelObj = await helper.getDynamicModel(domainName);
    ConcreteRequestHistory = modelObj.ConcreteRequestHistory;
    ConcreteRequest = modelObj.ConcreteRequest;
    User = modelObj.User;

    if (domainName) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      inputData.user = newUser;
    }

    return true;
  },
};

module.exports = historyService;
