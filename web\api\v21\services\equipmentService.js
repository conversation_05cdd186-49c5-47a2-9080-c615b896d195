const status = require('http-status');
const {
  Sequelize,
  Enterprise,
  DeliverEquipment,
  CraneRequestEquipment,
  DeliveryRequest,
  CraneRequest,
  DeliverHistory,
  CraneRequestHistory,
} = require('../models');
let { Equipments, Project, Member, User } = require('../models');
const { PresetEquipmentType } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
const ApiError = require('../helpers/apiError');

let publicUser;
let publicMember;
const equipmentService = {
  async addEquipment(equData, done) {
    try {
      await this.getDynamicModel(equData);
      const userDetails = equData.user;
      const inputData = equData.body;
      const createdByDetail = await Member.getBy({
        UserId: userDetails.id,
        ProjectId: inputData.ProjectId,
      });
      inputData.createdBy = createdByDetail.id;
      const projectDetails = await Project.getProject({ id: inputData.ProjectId });
      const controlluserDetails = await Member.getBy({
        id: inputData.controlledBy,
        ProjectId: inputData.ProjectId,
      });
      if (projectDetails) {
        if (controlluserDetails) {
          const nameExist = await Equipments.getEquipment({
            equipmentName: inputData.equipmentName,
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          });
          if (nameExist) {
            const err = new ApiError('Equipment Name Already exist.', status.BAD_REQUEST);
            done(null, err);
          } else {
            const lastIdValue = await Equipments.findOne({
              where: { ProjectId: inputData.ProjectId, isDeleted: false },
              order: [['equipmentAutoId', 'DESC']],
            });
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastIdValue));
            if (
              newValue &&
              newValue.equipmentAutoId !== null &&
              newValue.equipmentAutoId !== undefined
            ) {
              id = newValue.equipmentAutoId;
            }
            inputData.equipmentAutoId = id + 1;
            const newEquipment = await Equipments.createEquipment(inputData);
            done(newEquipment, false);
          }
        } else {
          const err = new ApiError('Controlled Member not in our Project.', status.BAD_REQUEST);
          done(null, err);
        }
      } else {
        const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
        done(null, err);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDomainEnterprise(domainName) {
    if (!domainName) return null;
    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() }
    });
    return domainEnterpriseValue ? domainName : null;
  },
  async getUserEnterprise(email, ParentCompanyId) {
    if (!email || !ParentCompanyId) return null;
    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) return null;

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false }
    });

    if (memberData?.isAccount) {
      const enterpriseValue = await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' }
      });
      return enterpriseValue?.name.toLowerCase();
    }

    const enterpriseValue = await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
    return enterpriseValue?.name.toLowerCase();
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const { domainName } = inputData.user;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;

    let domainNameResult = await this.getDomainEnterprise(domainName);
    if (!domainNameResult && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      domainNameResult = await this.getUserEnterprise(inputData.user.email, ParentCompanyId);
    }

    const modelObj = await helper.getDynamicModel(domainNameResult);
    Equipments = modelObj.Equipments;
    Project = modelObj.Project;
    Member = modelObj.Member;
    User = modelObj.User;

    if (domainNameResult) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }

    return domainNameResult;
  },
  async lastEquipment(inputData, done) {
    try {
      let data;
      const lastData = await Equipments.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['equipmentAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.equipmentAutoId + 1;
      } else {
        data = 1;
      }
      done({ id: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listEquipment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      let searchCondition = {};
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        condition.isActive = true;
      }
      if (incomeData.nameFilter) {
        condition.equipmentName = {
          [Sequelize.Op.iLike]: `%${incomeData.nameFilter}%`,
        };
      }
      if (incomeData.companyNameFilter) {
        condition['$controllUserDetails.Company.companyName$'] = {
          [Sequelize.Op.iLike]: `%${incomeData.companyNameFilter}%`,
        };
      }

      if (incomeData.idFilter) {
        condition.equipmentAutoId = incomeData.idFilter;
        condition.isDeleted = false;
        condition.ProjectId = inputData.params.ProjectId;
      }
      if (incomeData.memberFilter) {
        condition['$controllUserDetails.id$'] = incomeData.memberFilter;
      }
      if (incomeData.typeFilter) {
        condition['$PresetEquipmentType.equipmentType$'] = incomeData.typeFilter;
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            equipmentName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.User.email$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.Company.companyName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$PresetEquipmentType.equipmentType$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.User.firstName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        equipmentAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: inputData.params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const equipmentData = await Equipments.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      if (incomeData?.isFilter) {
        if (equipmentData.rows?.length > 0) {
          equipmentData.rows.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        } else if (equipmentData.length > 0) {
          equipmentData.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      }

      done(equipmentData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async craneListEquipment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const conditionData = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        conditionData.isActive = true;
      }
      const equipmentData = await Equipments.findAndCountAll({
        where: conditionData,
        include: [
          {
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      if (equipmentData.rows) {
        if (equipmentData.rows.length > 0) {
          equipmentData.rows.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      } else if (equipmentData.length > 0) {
        equipmentData.sort((a, b) =>
          a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
        );
      }

      done(equipmentData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getAllEquipmentType(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const equipmentData = await Equipments.findAll({
        where: {
          ProjectId: params.ProjectId,
          isDeleted: false,
        },
        include: [
          {
            required: false,
            where: { isDeleted: false, isActive: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      done(equipmentData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateEquipment(equData, done) {
    try {
      await this.getDynamicModel(equData);
      const inputData = equData.body;
      const equipmentDetails = await Equipments.findOne({
        where: Sequelize.and(
          {
            id: inputData.id,
          },
          { ProjectId: inputData.ProjectId },
        ),
      });
      if (equipmentDetails) {
        const projectDetails = await Project.getProject({ id: inputData.ProjectId });
        if (projectDetails) {
          const nameExist = await Equipments.findOne({
            where: Sequelize.and(
              {
                equipmentName: inputData.equipmentName,
              },
              { ProjectId: inputData.ProjectId },
            ),
          });
          if (nameExist && nameExist.id !== inputData.id) {
            const err = new ApiError('Equipment Name Already exist.', status.BAD_REQUEST);
            done(null, err);
          } else {
            delete inputData.ProjectId;
            const newEquipment = await Equipments.updateInstance(inputData.id, inputData);
            done(newEquipment, false);
          }
        } else {
          done(null, { message: 'Project does not exist.' });
        }
      } else {
        done(null, { message: 'Equipment id does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteEquipment(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      const { id } = input.body;
      let getEquipments;
      if (reqData.isSelectAll) {
        getEquipments = await Equipments.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false },
        });
      } else {
        getEquipments = await Equipments.findAll({
          where: {
            ProjectId: reqData.ProjectId,
            isDeleted: false,
            id: { [Op.in]: id },
          },
        });
      }
      if (getEquipments && getEquipments.length > 0) {
        for (const item of getEquipments) {
          const isEquipmentMappedToDeliveryRequest = await DeliverEquipment.findOne({
        where: {
          EquipmentId: +item.id,
          isDeleted: false,
          ProjectId: reqData.ProjectId,
        },
          });
          const isEquipmentMappedToCraneRequest = await CraneRequestEquipment.findOne({
        where: {
          EquipmentId: +item.id,
          isDeleted: false,
          ProjectId: reqData.ProjectId,
        },
          });
          if (isEquipmentMappedToDeliveryRequest) {
        return done(null, {
          message: `${item.equipmentName} cannot be deleted. ${item.equipmentName} is mapped to submitted requests`,
        });
          }
          if (isEquipmentMappedToCraneRequest) {
        return done(null, {
          message: `${item.equipmentName} cannot be deleted. ${item.equipmentName} is mapped to submitted requests`,
        });
          }
          await Equipments.update(
        { isDeleted: true },
        {
          where: { id: +item.id, ProjectId: reqData.ProjectId, isDeleted: false },
        },
          );
        }
        return done('success', false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async equipmentsForBulkUploadDeliveryRequest(req) {
    try {
      await this.getDynamicModel(req);
      const { params } = req;
      const condition = {
        ProjectId: +params.ProjectId,
        isDeleted: false,
        isActive: true,
      };
      const equipments = await Equipments.findAll({
        where: condition,
        attributes: ['id', 'equipmentName'],
      });
      return equipments;
    } catch (e) {
      console.log(e);
    }
  },
  async getPresetEquipmentTypeList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const equipmentTypes = await PresetEquipmentType.findAll({
        where: {
          isDeleted: false,
          isActive: true,
        },
        attributes: ['id', 'equipmentType'],
      });
      equipmentTypes.sort((a, b) =>
        a.equipmentType.toLowerCase() > b.equipmentType.toLowerCase() ? 1 : -1,
      );

      done(equipmentTypes, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getMappedRequests(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const equipmentDetails = await Equipments.findOne({
        where: {
          equipmentAutoId: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
        },
      });
      if (equipmentDetails) {
        let deliveryRequestEquipment = [];
        deliveryRequestEquipment = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'equipmentDetails',
              where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
              attributes: ['id'],
              include: [
                {
                  include: [
                    {
                      required: false,
                      where: { isDeleted: false, isActive: true, isCraneType: true },
                      association: 'PresetEquipmentType',
                      attributes: ['id', 'equipmentType', 'isCraneType'],
                    },
                  ],
                  association: 'Equipment',
                  attributes: ['equipmentName', 'id'],
                },
              ],
            },
          ],
        });
        const craneRequestEquipment = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'equipmentDetails',
              where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
              attributes: ['id'],
              include: [
                {
                  include: [
                    {
                      required: false,
                      where: { isDeleted: false, isActive: true, isCraneType: true },
                      association: 'PresetEquipmentType',
                      attributes: ['id', 'equipmentType', 'isCraneType'],
                    },
                  ],
                  association: 'Equipment',
                  attributes: ['equipmentName', 'id'],
                },
              ],
            },
          ],
        });
        if (craneRequestEquipment) {
          deliveryRequestEquipment.push(...craneRequestEquipment);
        }
        const craneEquipments = await Equipments.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            isActive: true,
          },
          include: [
            {
              required: true,
              where: { isDeleted: false, isActive: true, isCraneType: true },
              association: 'PresetEquipmentType',
              attributes: ['id', 'equipmentType', 'isCraneType'],
            },
          ],
          attributes: ['equipmentName', 'id', 'PresetEquipmentTypeId'],
        });
        const nonCraneEquipments = await Equipments.findAll({
          where: Sequelize.and({
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
          include: [
            {
              required: true,
              where: { isDeleted: false, isActive: true, isCraneType: false },
              association: 'PresetEquipmentType',
              attributes: ['id', 'equipmentType', 'isCraneType'],
            },
          ],
          attributes: ['equipmentName', 'id', 'PresetEquipmentTypeId'],
        });
        return done(
          {
            mappedRequest: deliveryRequestEquipment,
            equipments: {
              craneEquipments,
              nonCraneEquipments,
            },
          },
          false,
        );
      }
      const err = new ApiError('Equipment not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      console.log(e);
      return done(null, e);
    }
  },
  async deactivateEquipment(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const loginUser = req.user;

      const equipmentDetails = await Equipments.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
        },
      });

      const memberDetails = await Member.getBy({
        UserId: loginUser.id,
        ProjectId: inputData.ProjectId,
        isActive: true,
        isDeleted: false,
      });

      if (!equipmentDetails) {
        const err = new ApiError('Equipment not found.', status.BAD_REQUEST);
        return done(null, err);
      }

      await this.handleEquipmentDeactivationForRequests({
        equipmentDetails,
        inputData,
        loginUser,
        memberDetails,
        equipmentSwitchedRequests: inputData.equipmentSwitchedRequests,
      });

      const equipmentDeactivated = await Equipments.update(
        { isActive: false },
        {
          where: {
            id: equipmentDetails.id,
            isActive: true,
          },
        }
      );

      return done(equipmentDeactivated, false);
    } catch (e) {
      return done(null, e);
    }
  },

  async handleEquipmentDeactivationForRequests({
    equipmentDetails,
    inputData,
    loginUser,
    memberDetails,
    equipmentSwitchedRequests,
  }) {
    await this._deactivateEquipmentInRequests({
      requestModel: DeliveryRequest,
      historyModel: DeliverHistory,
      equipmentModel: DeliverEquipment,
      startDateField: 'deliveryStart',
      requestIdField: 'DeliveryRequestId',
      equipmentIdField: 'DeliveryId',
      equipmentDetails,
      inputData,
      loginUser,
      memberDetails,
    });

    await this._deactivateEquipmentInRequests({
      requestModel: CraneRequest,
      historyModel: CraneRequestHistory,
      equipmentModel: CraneRequestEquipment,
      startDateField: 'craneDeliveryStart',
      requestIdField: 'CraneRequestId',
      equipmentIdField: 'CraneRequestId',
      equipmentDetails,
      inputData,
      loginUser,
      memberDetails,
    });

    await this.handleEquipmentSwitchForRequests({
      equipmentSwitchedRequests,
      inputData,
      loginUser,
      memberDetails,
    });
  },

  async _deactivateEquipmentInRequests({
    requestModel,
    historyModel,
    equipmentModel,
    startDateField,
    requestIdField,
    equipmentIdField,
    equipmentDetails,
    inputData,
    loginUser,
    memberDetails,
  }) {
    const requests = await requestModel.findAll({
      where: {
        ProjectId: inputData.ProjectId,
        isDeleted: false,
        [startDateField]: { [Op.gte]: new Date() },
      },
      include: [
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
          attributes: ['id'],
          include: [
            {
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
              include: [
                {
                  association: 'PresetEquipmentType',
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  required: false,
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
            },
          ],
        },
      ],
    });

    for (const req of requests) {
      await historyModel.createInstance({
        [requestIdField]: req.id,
        MemberId: memberDetails.id,
        type: 'edit',
        ProjectId: inputData.ProjectId,
        description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Equipment ${equipmentDetails.equipmentName}`,
      });

      await equipmentModel.update(
        { isActive: false },
        {
          where: {
            EquipmentId: equipmentDetails.id,
            ProjectId: inputData.ProjectId,
            [equipmentIdField]: req.id,
            isDeleted: false,
            isActive: true,
          },
        }
      );
    }
  },

  async handleEquipmentSwitchForRequests({ equipmentSwitchedRequests, inputData, loginUser, memberDetails }) {
    if (!equipmentSwitchedRequests?.length) return;

    for (const object of equipmentSwitchedRequests) {
      const newEquipment = await Equipments.findOne({ where: { id: +object.changedEquipmentId } });

      const historyEntry = {
        MemberId: memberDetails.id,
        type: 'edit',
        ProjectId: inputData.ProjectId,
        description: `${loginUser.firstName} ${loginUser.lastName} added the Equipment ${newEquipment.equipmentName}`,
      };

      const commonFields = {
        ProjectId: inputData.ProjectId,
        isDeleted: false,
        EquipmentId: +object.changedEquipmentId,
        isActive: true,
      };

      if (['deliveryRequest', 'deliveryRequestWithCrane'].includes(object.requestType)) {
        const existing = await DeliverEquipment.findAll({
          where: { DeliveryId: object.id, ProjectId: inputData.ProjectId },
        });

        const index = existing.findIndex(item => item.EquipmentId === +object.changedEquipmentId);
        await DeliverHistory.createInstance({ ...historyEntry, DeliveryRequestId: object.id });

        if (index !== -1) {
          await DeliverEquipment.update(
            { ...commonFields, DeliveryId: object.id },
            { where: { id: existing[index].id } }
          );
        } else {
          await DeliverEquipment.createInstance({
            ...commonFields,
            DeliveryId: object.id,
            DeliveryCode: object.DeliveryId,
          });
        }
      }

      if (object.requestType === 'craneRequest') {
        const existing = await CraneRequestEquipment.findAll({
          where: { CraneRequestId: object.id, ProjectId: inputData.ProjectId },
        });

        const index = existing.findIndex(item => item.EquipmentId === +object.changedEquipmentId);
        await CraneRequestHistory.createInstance({ ...historyEntry, CraneRequestId: object.id });

        if (index !== -1) {
          await CraneRequestEquipment.update(
            { ...commonFields, CraneRequestId: object.id },
            { where: { id: existing[index].id } }
          );
        } else {
          await CraneRequestEquipment.createInstance({
            ...commonFields,
            CraneRequestId: object.id,
            CraneRequestCode: object.CraneRequestId,
          });
        }
      }
    }
  }


};
module.exports = equipmentService;