const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

const cloudFrontURl = process.env.CLOUD_FRONT_URL;
AWS.config.getCredentials((err) => {
  if (err) console.error('Failed to load credentials', err);
  else console.log('Using credentials from', AWS.config.credentials?.constructor?.name);
});

const s3Client = new AWS.S3({
  region: process.env.REGION,
});

const awsConfig = {
  async upload(fileList, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    const responseData = [];

    try {
      for (const element of fileList) {
        const fileExtension = element.originalname?.replace(/^.*\./, '') || element.name.replace(/^.*\./, '');
        const name = element.originalname || element.name;
        const uniquename = uuidv4();
        const filename = `${uniquename}.${fileExtension}`;

        uploadParams.Key = filename;
        uploadParams.Body = element.buffer;
        uploadParams.ContentDisposition = `attachment; filename=${name}`;

        const data = await s3Client.upload(uploadParams).promise();
        responseData.push({
          ...data,
          Location: cloudFrontURl + filename
        });
      }
      const uniquename = uuidv4();
      const filename = `${uniquename}.${fileExtension}`;
      uploadParams.Key = filename;
      uploadParams.Body = element.buffer;
      uploadParams.ContentDisposition = `attachment; filename=${name}`; //NOSONAR
      s3Client.upload(uploadParams, (err, data) => {
        if (err) {
          console.log(err, "error=====")
          done(null, err);
        }
        const newData = data;
        console.log(newData, "newData======")
        newData.Location = cloudFrontURl + filename;
        responseData.push(newData);
        if (responseData.length === fileList.length) {
          done(responseData, false);
        }
      });
    } catch (error) {
      console.log(error, "upload error=====");
      done(null, error);
    }
  },
  async singleUpload(fileList, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    let file;
    if (fileList.file !== undefined) {
      file = fileList.file;
    } else {
      file = fileList.files;
    }
    const element = file;
    let fileExtension;
    let name;
    if (element.originalname !== undefined) {
      fileExtension = element.originalname.replace(/^.*\./, '');
      name = element.originalname;
    } else {
      fileExtension = element.name.replace(/^.*\./, '');
      name = element.name;
    }
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `attachment; filename=${name}`;

    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      const newData = {
        ...data,
        Location: cloudFrontURl + filename
      };
      done([newData], false);
    });
  },

  async carbonEmissionFileUpload(fileList, done) {
    const uploadParams = {
      Bucket: 'folloit-development/invoice-extracts',
      Key: '',
      Body: null,
    };

    const file = fileList.file !== undefined ? fileList.file : fileList.files;
    const element = file;

    const fileExtension = element.originalname
      ? element.originalname.replace(/^.*\./, '')
      : element.name.replace(/^.*\./, '');
    const name = element.originalname || element.name;

    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;

    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `attachment; filename=${name}`;

    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }

      const newData = {
        ...data,
        Location: cloudFrontURl + filename,
        S3URI: `s3://${uploadParams.Bucket}/${uploadParams.Key}`
      };

      done(newData, false);
    });
  },


  async reportUpload(buffer, name, extension, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    const fileExtension = extension.toLowerCase();
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = buffer;
    uploadParams.ContentDisposition = `attachment; filename=${name}.${fileExtension}`;
    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      if (data) {
        const response = cloudFrontURl + filename;
        done(response, false);
      }
    });
  },
  async uploadToS3(buffer, name, extension) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    return new Promise((res, rej) => {
      try {
        const fileExtension = extension.toLowerCase();
        const fileName = `schedule_reports/${name}.${fileExtension}`;
        uploadParams.Key = fileName;
        uploadParams.Body = buffer;
        s3Client.upload(uploadParams, (err, data) => {
          if (err) {
            rej(err instanceof Error ? err : new Error(err));
          }
          if (data) {
            const response = cloudFrontURl + fileName;
            console.log('********response********', response);
            res(response);
          }
        });
      } catch (err) {
        rej(err instanceof Error ? err : new Error(err));
      }
    });
  },

  async logisticPlanUpload(fileList, done) {
    const { params } = fileList;
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    let file;
    if (fileList.file !== undefined) {
      file = fileList.file;
    } else {
      file = fileList.files;
    }
    const responseData = [];
    const element = file;
    let fileExtension;
    let name;
    if (element.originalname !== undefined) {
      fileExtension = element.originalname.replace(/^.*\./, '');
      name = element.originalname;
    } else {
      fileExtension = element.name.replace(/^.*\./, '');
      name = element.name;
    }
    const uniquename = uuidv4();
    const filename = `${+params.ProjectId}-siteplan-${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `inline; filename=${name}`;
    let contentType;

    switch (fileExtension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
      default:
        break;
    }

    uploadParams.ContentType = contentType;
    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      const newData = data;
      newData.fileLink = cloudFrontURl + filename;
      newData.fileName = element.originalname;
      responseData.push(newData);
      done(responseData, false);
    });
  },
  async sitePlanUpload(file, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    const responseData = [];
    const element = file;
    let fileExtension;
    let name;
    if (element.originalname !== undefined) {
      fileExtension = element.originalname.replace(/^.*\./, '');
      name = element.originalname;
    } else {
      fileExtension = element.name.replace(/^.*\./, '');
      name = element.name;
    }
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `inline; filename=${name}`;
    let contentType;

    switch (fileExtension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
      default:
        break;
    }

    uploadParams.ContentType = contentType;
    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      const newData = data;
      newData.fileLink = cloudFrontURl + filename;
      newData.fileName = element.originalname;
      responseData.push(newData);
      done(responseData, false);
    });
  },
};

module.exports = awsConfig;
