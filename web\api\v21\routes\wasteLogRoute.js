const { Router } = require('express');
const { validate } = require('express-validation');
const { equipmentValidation } = require('../middlewares/validations');
const WasteLogController = require('../controllers/wasteLogController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const wasteLogRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_wastelog',
            passportConfig.isAuthenticated,
            WasteLogController.addWasteLog,
        );
        router.get(
            '/wastelog_list/:pageSize/:pageNo',
            passportConfig.isAuthenticated,
            WasteLogController.listWasteLog,
        );
        return router;
    },
};
module.exports = wasteLogRoute;