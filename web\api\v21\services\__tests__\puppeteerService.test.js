// Set environment variables before requiring modules
process.env.BUCKET = 'test-bucket';
process.env.CLOUD_FRONT_URL = 'https://test-cloudfront.net/';
process.env.ACCESS_KEY_ID = 'test-access-key';
process.env.SECRET_ACCESS_KEY = 'test-secret-key';
process.env.REGION = 'us-east-1';

// Mock puppeteer first to avoid module loading issues
jest.mock('puppeteer', () => ({
    launch: jest.fn(),
}));

// Mock models
jest.mock('../models', () => ({
    ProjectSettings: {
        findOne: jest.fn(),
        findAll: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
    },
}));

const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Mock other external dependencies
jest.mock('fs');
jest.mock('uuid');

// Mock AWS SDK with proper S3 client
const mockS3Upload = jest.fn().mockReturnValue({
    promise: jest.fn(),
});

jest.mock('aws-sdk', () => ({
    S3: jest.fn(() => ({
        upload: mockS3Upload,
    })),
}));

const puppeteer = require('puppeteer');
const puppeteerService = require('../puppeteerService');

describe('PuppeteerService', () => {
    let mockBrowser;
    let mockPage;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup mock browser and page
        mockPage = {
            setContent: jest.fn().mockResolvedValue(undefined),
            pdf: jest.fn(),
            emulateTimezone: jest.fn(),
            setViewport: jest.fn(),
            goto: jest.fn(),
            waitForFunction: jest.fn(),
        };

        mockBrowser = {
            newPage: jest.fn().mockResolvedValue(mockPage),
            close: jest.fn().mockResolvedValue(undefined),
        };

        // Mock puppeteer launch
        puppeteer.launch.mockResolvedValue(mockBrowser);

        // Setup S3 upload mock
        mockS3Upload.mockReturnValue({
            promise: jest
                .fn()
                .mockResolvedValue({ Location: 'https://test-bucket.s3.amazonaws.com/test-uuid.png' }),
        });

        // Mock uuid
        uuidv4.mockReturnValue('test-uuid');
    });

    describe('generatePdfBuffer', () => {
        const mockPdfTemplate = '<html><body>Test PDF</body></html>';
        const mockPdfBuffer = Buffer.from('mock-pdf-content');

        it('should successfully generate PDF buffer', async () => {
            mockPage.pdf.mockResolvedValue(mockPdfBuffer);

            const result = await puppeteerService.generatePdfBuffer(mockPdfTemplate);

            expect(puppeteer.launch).toHaveBeenCalledWith({
                headless: true,
                args: [
                    '--disable-gpu',
                    '--disable-dev-shm-usage',
                    '--disable-setuid-sandbox',
                    '--no-sandbox',
                ],
            });
            expect(mockPage.setContent).toHaveBeenCalledWith(mockPdfTemplate, {
                waitUntil: 'networkidle0',
            });
            expect(mockPage.pdf).toHaveBeenCalledWith({
                margin: { top: '15px', right: '15px', bottom: '15px', left: '15px' },
                printBackground: true,
                format: 'A4',
                landscape: true,
            });
            expect(mockBrowser.close).toHaveBeenCalled();
            expect(result).toEqual(mockPdfBuffer);
        });

        it('should return false when PDF generation fails', async () => {
            mockPage.pdf.mockResolvedValue(null);

            const result = await puppeteerService.generatePdfBuffer(mockPdfTemplate);

            expect(result).toBe(false);
        });

        it('should handle browser launch error', async () => {
            puppeteer.launch.mockRejectedValue(new Error('Browser launch failed'));

            await expect(puppeteerService.generatePdfBuffer(mockPdfTemplate)).rejects.toThrow(
                'Browser launch failed',
            );
        });
    });

    describe('generatePdfByURL', () => {
        const mockUrl = 'https://test-url.com';
        const mockTimezone = 'UTC';
        const mockPdfBuffer = Buffer.from('mock-pdf-content');
        const mockNext = jest.fn();

        it('should successfully generate PDF from URL', async () => {
            mockPage.pdf.mockResolvedValue(mockPdfBuffer);
            // Mock waitForFunction to actually call the function to improve coverage
            mockPage.waitForFunction.mockImplementation((fn) => {
                // Mock the global document object for the anonymous function
                global.document = {
                    querySelector: jest.fn().mockReturnValue({ textContent: 'mock element' }),
                };
                // Call the function to test the anonymous function inside waitForFunction
                const result = fn();
                return Promise.resolve(result);
            });

            const result = await puppeteerService.generatePdfByURL(mockUrl, mockTimezone, mockNext);

            expect(puppeteer.launch).toHaveBeenCalledWith({
                headless: true,
                args: ['--no-sandbox'],
            });
            expect(mockPage.emulateTimezone).toHaveBeenCalledWith(mockTimezone);
            expect(mockPage.setViewport).toHaveBeenCalledWith({
                width: 1479,
                height: 764,
            });
            expect(mockPage.goto).toHaveBeenCalledWith(mockUrl, {
                waitUntil: 'networkidle2',
                timeout: 0,
            });
            expect(mockPage.waitForFunction).toHaveBeenCalled();
            expect(result).toEqual(mockPdfBuffer);
        });

        it('should return false when PDF generation fails', async () => {
            mockPage.pdf.mockResolvedValue(null);

            const result = await puppeteerService.generatePdfByURL(mockUrl, mockTimezone, mockNext);

            expect(result).toBe(false);
        });

        it('should handle timeout error', async () => {
            mockPage.waitForFunction.mockRejectedValue(new Error('Waiting failed: 10000ms exceeded'));

            const result = await puppeteerService.generatePdfByURL(mockUrl, mockTimezone, mockNext);

            expect(result).toEqual({ status: false, data: 'No data found!' });
        });

        it('should handle other errors', async () => {
            const mockError = new Error('Test error');
            mockPage.goto.mockRejectedValue(mockError);

            await puppeteerService.generatePdfByURL(mockUrl, mockTimezone, mockNext);

            expect(mockNext).toHaveBeenCalledWith(mockError);
        });
    });

    describe('saveImageToS3Bucket', () => {
        const mockImagePath = '/path/to/image.png';
        const mockFileName = 'test-image.png';
        const mockFileContent = Buffer.from('mock-image-content');

        beforeEach(() => {
            fs.readFileSync.mockReturnValue(mockFileContent);
        });

        it('should successfully upload image to S3', async () => {
            const result = await puppeteerService.saveImageToS3Bucket(mockImagePath, mockFileName);

            expect(fs.readFileSync).toHaveBeenCalledWith(mockImagePath);
            expect(mockS3Upload).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                Key: 'test-uuid.png',
                Body: mockFileContent,
                ContentType: 'image/png',
            });
            expect(result).toEqual({
                success: true,
                data: [{ fileLink: 'https://test-cloudfront.net/test-uuid.png' }],
            });
        });

        it('should handle S3 upload error', async () => {
            const mockError = new Error('S3 upload failed');
            mockS3Upload.mockReturnValue({
                promise: jest.fn().mockRejectedValue(mockError),
            });

            await expect(
                puppeteerService.saveImageToS3Bucket(mockImagePath, mockFileName),
            ).rejects.toThrow('S3 upload failed');
        });

        it('should handle file read error', async () => {
            const mockError = new Error('File read failed');
            fs.readFileSync.mockImplementation(() => {
                throw mockError;
            });

            await expect(
                puppeteerService.saveImageToS3Bucket(mockImagePath, mockFileName),
            ).rejects.toThrow('File read failed');
        });
    });
});
