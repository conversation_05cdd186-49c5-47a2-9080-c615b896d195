const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const DeliveryRequest = sequelize.define(
    'DeliveryRequest',
    {
      description: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      CompanyId: DataTypes.INTEGER,
      escort: DataTypes.BOOLEAN,
      notes: DataTypes.STRING,
      DeliveryId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      status: {
        type: DataTypes.ENUM,
        values: ['Pending', 'Approved', 'Declined', 'Delivered', 'Expired'],
      },
      approvedBy: DataTypes.INTEGER,
      deliveryStart: DataTypes.DATE,
      approved_at: DataTypes.DATE,
      deliveryEnd: DataTypes.DATE,
      ProjectId: DataTypes.INTEGER,
      vehicleDetails: DataTypes.STRING,
      createdBy: DataTypes.INTEGER,
      isQueued: DataTypes.BOOLEAN,
      isAllDetailsFilled: DataTypes.BOOLEAN,
      cranePickUpLocation: DataTypes.STRING,
      craneDropOffLocation: DataTypes.STRING,
      isAssociatedWithCraneRequest: DataTypes.BOOLEAN,
      CraneRequestId: DataTypes.INTEGER,
      requestType: DataTypes.STRING,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      isCreatedByGuestUser: DataTypes.BOOLEAN,
      OriginationAddress: DataTypes.TEXT,
      vehicleType: DataTypes.TEXT,
    },
    {},
  );
  DeliveryRequest.associate = (models) => {
    // associations can be defined
    DeliveryRequest.hasMany(models.DeliveryPerson, {
      as: 'memberDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliveryPerson, {
      as: 'memberEditData',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    DeliveryRequest.belongsTo(models.Project);
    DeliveryRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    DeliveryRequest.hasMany(models.DeliverCompany, {
      as: 'companyDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverCompany, {
      as: 'companyEditData',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverGate, {
      as: 'gateDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverDefine, {
      as: 'defineWorkDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverDefine, {
      as: 'defineEditData',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'DeliveryRequestId',
    });
    DeliveryRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    DeliveryRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
    DeliveryRequest.hasMany(models.DeliverComment, {
      as: 'comments',
      foreignKey: 'DeliveryRequestId',
    });
    DeliveryRequest.hasMany(models.DeliverAttachement, {
      as: 'attachements',
      foreignKey: 'DeliveryRequestId',
    });
    DeliveryRequest.hasMany(models.DeliverHistory, {
      as: 'history',
      foreignKey: 'DeliveryRequestId',
    });
  };
  DeliveryRequest.createInstance = async (paramData) => {
    const newDeliveryRequest = await DeliveryRequest.create(paramData);
    return newDeliveryRequest;
  };
  DeliveryRequest.getAll = async (options) => {
    const {
      attr,
      roleId,
      memberId,
      searchCondition,
      order,
      sort,
      sortColumn,
      limit,
      offset
    } = options;

    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }

    const orderQuery = buildSortQuery(sortByFieldName, sortByColumnType);
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await DeliveryRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
      order: orderQuery,
      limit: limit,
      offset: offset,
    });
    return newDeliveryRequest;
  };
  DeliveryRequest.getCalendarData = async (
    attr,
    roleId,
    memberId,
    searchCondition,
    order,
    sort,
    sortColumn,
  ) => {
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }

    const orderQuery = buildSortQuery(sortByFieldName, sortByColumnType);
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await DeliveryRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'DeliveryId',
        'notes',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
      order: orderQuery,
    });
    return newDeliveryRequest;
  };

  const buildDateFilterCondition = (dateFilter, timezoneoffset) => {
    const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);
    return {
      deliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };
  };

  const buildSortQuery = (sortByFieldName, sortByColumnType) => {
    // Ensure sortByColumnType is a string
    const sortType = String(sortByColumnType || 'DESC');

    let orderQuery;

    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', sortType]];
    } else if (sortByFieldName === 'gate') {
      orderQuery = [['gateDetails', 'Gate', 'gateName', sortType]];
    } else if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', sortType]];
    } else if (sortByFieldName === 'company') {
      orderQuery = [['companyDetails', 'Company', 'companyName', sortType]];
    } else if (sortByFieldName === 'dfow') {
      orderQuery = [['defineWorkDetails', 'DeliverDefineWork', 'DFOW', sortType]];
    } else if (sortByFieldName === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', sortType]];
    } else if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'deliveryStart' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status' ||
      sortByFieldName === 'datetime' ||
      sortByFieldName === 'requestType'
    ) {
      orderQuery = [[sortByFieldName, sortType]];
    } else {
      // Default fallback
      orderQuery = [['id', sortType]];
    }

    return orderQuery;
  };

  const buildSearchCondition = (search) => {
    if (!search) return {};

    return {
      [Op.or]: [
        { description: { [Sequelize.Op.iLike]: `%${search}%` } },
        { cranePickUpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
        { craneDropOffLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
        { '$equipmentDetails.Equipment.equipmentName$': { [Sequelize.Op.iLike]: `%${search}%` } },
        { '$location.locationPath$': { [Sequelize.Op.iLike]: `%${search}%` } }
      ]
    };
  };

  const buildDateRangeCondition = (startdate, enddate, timezoneoffset) => {
    const startDateTime = moment(startdate, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneoffset), true);
    const endDateTime = moment(enddate, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneoffset), true);
    return {
      deliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };
  };

  const buildSearchConditions = (options) => {
    const {
      descriptionFilter,
      pickFrom,
      pickTo,
      memberFilter,
      equipmentFilter,
      locationFilter,
      companyFilter,
      dateFilter,
      statusFilter,
      idFilter,
      startdate,
      enddate,
      gateFilter,
      search,
      req
    } = options;

    const conditions = [];

    if (descriptionFilter) {
      conditions.push({ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } });
    }

    if (pickFrom) {
      conditions.push({ cranePickUpLocation: { [Sequelize.Op.iLike]: `%${pickFrom}%` } });
    }

    if (pickTo) {
      conditions.push({ craneDropOffLocation: { [Sequelize.Op.iLike]: `%${pickTo}%` } });
    }

    if (memberFilter > 0) {
      conditions.push({ '$memberDetails.Member.id$': +memberFilter });
    }

    if (equipmentFilter) {
      conditions.push({
        '$equipmentDetails.Equipment.equipmentName$': {
          [Sequelize.Op.iLike]: `${equipmentFilter}`,
        },
      });
    }

    if (locationFilter) {
      conditions.push({
        '$location.id$': {
          [Op.in]: locationFilter,
        },
      });
    }

    if (typeof companyFilter === 'string' && companyFilter !== '') {
      conditions.push({
        '$companyDetails.Company.companyName$': {
          [Sequelize.Op.iLike]: `${companyFilter}`,
        },
      });
    }

    if (dateFilter) {
      conditions.push(buildDateFilterCondition(dateFilter, req.headers.timezoneoffset));
    }

    if (statusFilter) {
      conditions.push({ status: statusFilter });
    }

    if (idFilter) {
      conditions.push({ CraneRequestId: idFilter });
    }

    if (startdate) {
      conditions.push(buildDateRangeCondition(startdate, enddate, req.headers.timezoneoffset));
    }

    if (gateFilter > 0) {
      conditions.push({ '$gateDetails.Gate.id$': +gateFilter });
    }

    if (search) {
      conditions.push(buildSearchCondition(search));
    }

    return conditions;
  };

  DeliveryRequest.getCraneAssociatedRequest = async (options) => {
    const {
      roleId,
      attr,
      voidType,
      order,
      sort,
      sortByField
    } = options;

    // Build sorting logic
    const sortByFieldName = sortByField || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }

    const orderQuery = buildSortQuery(sortByFieldName, sortByColumnType);

    let commonSearch = {
      ...attr,
      isDeleted: false,
      isQueued: false,
    };

    if (+voidType === 0) {
      commonSearch.isAssociatedWithCraneRequest = true;
    }

    const conditions = buildSearchConditions(options);

    if (conditions.length > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: conditions,
          },
        ],
      };
    }

    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }

    return DeliveryRequest.findAll({
      subQuery: false,
      distinct: true,
      required: false,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          required: false,
          where: { isDeleted: false },
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
      order: orderQuery,
    });
  };
  DeliveryRequest.getSingleDeliveryRequestData = async (attr) => {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'DeliveryId',
        'status',
        'notes',
        'CraneRequestId',
        'ProjectId',
        'CraneRequestId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newDeliveryRequest;
  };

  DeliveryRequest.getWeeklyCalendarData = async (
    req,
    attr,
    roleId,
    start,
    end,
    startTime,
    endTime,
    startDate,
    endDate,
    typeFormat,
    timezone,
    eventStartTime,
    eventEndTime,
  ) => {
    let requiredCondition = true;
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    if (roleId === 2) {
      requiredCondition = false;
    }
    let startDate1 = start;
    let endDate1 = end;
    if (req.body.startDate && req.body.endDate) {
      startDate1 = req.body.startDate;
      endDate1 = req.body.endDate;
    }
    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);

    function timeToSeconds(timeString) {
      const [hours, minutes, seconds] = timeString.split(':');
      return +hours * 60 * 60 + +minutes * 60 + +seconds;
    }

    let singleQuery = true;

    if (finalFromTimeSeconds > finalToTimeSeconds) {
      singleQuery = false;
    } else {
      singleQuery = true;
    }
    if (typeFormat) {
      if (start) {
        const startDateTime = moment(startDate1, 'YYYY-MM-DD');
        const endDateTime = moment(endDate1, 'YYYY-MM-DD');
        const nextDay = moment(startDate1).add(1, 'days');
        const queryStartDate = nextDay.format('YYYY-MM-DD');
        if (singleQuery) {
          commonSearch = {
            [Op.and]: [
              sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
            ],
          };
        } else {
          commonSearch = {
            [Op.or]: [
              sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
              sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
            ],
          };
        }
      }
    } else {
      const nextDay = moment(startDate1).add(1, 'days');
      const queryStartDate = nextDay.format('YYYY-MM-DD');
      if (singleQuery) {
        commonSearch = {
          [Op.and]: [
            sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
          ],
        };
      } else {
        commonSearch = {
          [Op.or]: [
            sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
            sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate1}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
          ],
        };
      }
    }

    const newDeliveryRequest = await DeliveryRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: { ...attr, ...commonSearch, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'DeliveryId',
        'notes',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newDeliveryRequest;
  };

  DeliveryRequest.upcomingDeliveryRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      deliveryStart: { [Op.gt]: new Date() },
      requestType: 'deliveryRequestWithCrane',
      isAssociatedWithCraneRequest: true,
      ...condition,
    };
    const DeliveryRequestData = await DeliveryRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'DeliveryId',
        'status',
        'notes',
        'CraneRequestId',
        'ProjectId',
        'CraneRequestId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
      order: [['deliveryStart', 'ASC']],
    });
    return DeliveryRequestData;
  };

  DeliveryRequest.getNDRData = async (attr) => {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'voidList',
          required: false,
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newDeliveryRequest;
  };

  DeliveryRequest.guestGetNDRData = async (attr) => {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'voidList',
          required: false,
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
        {
          association: 'comments',
          required: false,
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
        {
          association: 'attachements',
          where: { isDeleted: false },
          order: [['id', 'DESC']],
          required: false,
        },
        {
          association: 'history',
          required: false,
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newDeliveryRequest;
  };
  return DeliveryRequest;
};
