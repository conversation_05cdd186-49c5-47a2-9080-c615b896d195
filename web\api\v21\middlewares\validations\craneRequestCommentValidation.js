const Joi = require('joi');

const craneRequestCommentValidation = {
  createCraneRequestComment: {
    body: Joi.object({
      comment: Joi.string().required(),
      CraneRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getCraneRequestComments: {
    params: Joi.object({
      CraneRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = craneRequestCommentValidation;
