const moment = require('moment');

const excelConcreteReportService = {
  async concreteReport(workbook, responseData, selectedHeaders, timezoneoffset) {
      const worksheet = workbook.addWorksheet('Concrete Report');

      const { rowValues, columns, selectedFlags } = this.prepareConcreteWorksheetMetadata(selectedHeaders);

      worksheet.getRow(1).values = rowValues;
      worksheet.columns = columns;

      const cellRange = this.getCellRange();
      this.populateConcreteWorksheetRows(worksheet, rowValues, responseData, selectedFlags, cellRange, timezoneoffset);

      return workbook;
    },

      prepareConcreteWorksheetMetadata(selectedHeaders) {
    const rowValues = [];
    const columns = [];
    const selectedFlags = {
      isIdSelected: false,
      isDescriptionSelected: false,
      isDateSelected: false,
      isStatusSelected: false,
      isApprovedBySelected: false,
      isCompanySelected: false,
      isOrderNumberSelected: false,
      isSlumpSelected: false,
      isTruckSpacingSelected: false,
      isPrimerOrderedSelected: false,
      isPersonSelected: false,
      isQuantityOrderedSelected: false,
      isMixDesignSelected: false,
      isLocationSelected: false,
    };

    selectedHeaders.forEach(object => {
      if (!object.isActive) return;
      rowValues.push(object.title);
      columns.push({ key: object.key, width: object.key === 'id' ? 5 : 32 });

      switch (object.key) {
        case 'id': selectedFlags.isIdSelected = true; break;
        case 'description': selectedFlags.isDescriptionSelected = true; break;
        case 'date': selectedFlags.isDateSelected = true; break;
        case 'status': selectedFlags.isStatusSelected = true; break;
        case 'approvedby': selectedFlags.isApprovedBySelected = true; break;
        case 'company': selectedFlags.isCompanySelected = true; break;
        case 'orderNumber': selectedFlags.isOrderNumberSelected = true; break;
        case 'slump': selectedFlags.isSlumpSelected = true; break;
        case 'truckSpacing': selectedFlags.isTruckSpacingSelected = true; break;
        case 'primer': selectedFlags.isPrimerOrderedSelected = true; break;
        case 'name': selectedFlags.isPersonSelected = true; break;
        case 'quantity': selectedFlags.isQuantityOrderedSelected = true; break;
        case 'mixDesign': selectedFlags.isMixDesignSelected = true; break;
        case 'location': selectedFlags.isLocationSelected = true; break;
      }
    });

    return { rowValues, columns, selectedFlags };
  },
  getCellRange() {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const range = {};
    for (let i = 0; i < alphabet.length; i++) {
      range[i] = alphabet[i];
    }
    return range;
  },

  // Helper methods to reduce cognitive complexity
  populateBasicFields(worksheet, data, flags, cell) {
    if (flags.isIdSelected) {
      worksheet.getCell(cell('Id')).value = data.ConcreteRequestId;
    }
    if (flags.isDescriptionSelected) {
      worksheet.getCell(cell('Description')).value = data.description;
    }
    if (flags.isStatusSelected) {
      worksheet.getCell(cell('Status')).value = data.status;
    }
  },

  populateDateField(worksheet, data, flags, cell, timezoneoffset) {
    if (!flags.isDateSelected) return;
    
    const start = moment(data.concretePlacementStart).add(Number(timezoneoffset), 'minutes');
    const end = moment(data.concretePlacementEnd).add(Number(timezoneoffset), 'minutes');
    worksheet.getCell(cell('Date & Time')).value = `${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
  },

  populateApprovedByField(worksheet, data, flags, cell) {
    if (!flags.isApprovedBySelected) return;
    
    const user = data.approverDetails?.User;
    worksheet.getCell(cell('Approved By')).value = user ? `${user.firstName} ${user.lastName}` : '-';
  },

  populateCompanyField(worksheet, data, flags, cell) {
    if (!flags.isCompanySelected) return;
    
    const companies = (data.concreteSupplierDetails || [])
      .map(c => c?.Company?.companyName)
      .filter(Boolean)
      .join(', ') || '-';
    worksheet.getCell(cell('Concrete Supplier')).value = companies;
  },

  populateSimpleFields(worksheet, data, flags, cell) {
    const simpleFieldMappings = [
      { flag: flags.isOrderNumberSelected, cellTitle: 'Order Number', dataField: data.concreteOrderNumber },
      { flag: flags.isSlumpSelected, cellTitle: 'Slump', dataField: data.slump },
      { flag: flags.isTruckSpacingSelected, cellTitle: 'Truck Spacing', dataField: data.truckSpacingHours },
      { flag: flags.isPrimerOrderedSelected, cellTitle: 'Primer Ordered', dataField: data.primerForPump },
      { flag: flags.isQuantityOrderedSelected, cellTitle: 'Quantity Ordered', dataField: data.concreteQuantityOrdered }
    ];

    simpleFieldMappings.forEach(({ flag, cellTitle, dataField }) => {
      if (flag) {
        worksheet.getCell(cell(cellTitle)).value = dataField || '-';
      }
    });
  },

  populatePersonField(worksheet, data, flags, cell) {
    if (!flags.isPersonSelected) return;
    
    const members = (data.memberDetails || [])
      .map(m => m?.Member?.User ? `${m.Member.User.firstName} ${m.Member.User.lastName}` : null)
      .filter(Boolean)
      .join(', ') || '-';
    worksheet.getCell(cell('Responsible Person')).value = members;
  },

  populateMixDesignField(worksheet, data, flags, cell) {
    if (!flags.isMixDesignSelected) return;
    
    const mixes = (data.mixDesignDetails || [])
      .map(m => m?.ConcreteMixDesign?.mixDesign)
      .filter(Boolean)
      .join(', ') || '-';
    worksheet.getCell(cell('Mix Design')).value = mixes;
  },

  populateLocationField(worksheet, data, flags, cell) {
    if (!flags.isLocationSelected) return;
    
    worksheet.getCell(cell('Location')).value = data.location?.locationPath || '-';
  },

  populateConcreteWorksheetRows(worksheet, rowValues, responseData, flags, cellRange, timezoneoffset) {
    for (let index = 1; index <= responseData.length; index++) {
      const data = responseData[index - 1];
      const cell = (title) => cellRange[rowValues.indexOf(title)] + (index + 1);
      worksheet.addRow();

      this.populateBasicFields(worksheet, data, flags, cell);
      this.populateDateField(worksheet, data, flags, cell, timezoneoffset);
      this.populateApprovedByField(worksheet, data, flags, cell);
      this.populateCompanyField(worksheet, data, flags, cell);
      this.populateSimpleFields(worksheet, data, flags, cell);
      this.populatePersonField(worksheet, data, flags, cell);
      this.populateMixDesignField(worksheet, data, flags, cell);
      this.populateLocationField(worksheet, data, flags, cell);
    }
  }

};
module.exports = excelConcreteReportService;
