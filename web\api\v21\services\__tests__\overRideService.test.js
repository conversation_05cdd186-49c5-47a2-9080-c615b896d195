const status = require('http-status');

// Mock all dependencies before requiring the service
jest.mock('sequelize', () => {
    const mockSequelize = {
        and: jest.fn(() => ({})),
    };
    return mockSequelize;
});

jest.mock('../../models', () => ({
    Member: {
        findOne: jest.fn(),
    },
    OverRide: {
        findOne: jest.fn(),
        createInstance: jest.fn(),
        getBy: jest.fn(),
        update: jest.fn(),
    },
    Project: {
        findByPk: jest.fn(),
    },
}));

jest.mock('../../mailer', () => ({
    sendMail: jest.fn(),
}));

jest.mock('../../helpers/apiError', () => {
    return jest.fn().mockImplementation((message, statusCode) => ({
        message,
        statusCode,
    }));
});

const overRideService = require('../overRideService');
const { Member, OverRide, Project } = require('../../models');
const MAILER = require('../../mailer');
const ApiError = require('../../helpers/apiError');

describe('overRideService', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('applyOverRide', () => {
        const mockInputData = {
            body: {
                ProjectId: 1,
                expiryDate: '2024-12-31',
                comment: 'Test comment',
            },
            user: {
                id: 1,
            },
        };

        const mockMember = {
            id: 1,
            RoleId: 2,
            UserId: 1,
            ProjectId: 1,
            isDeleted: false,
        };

        const mockProject = {
            id: 1,
            PlanId: 'plan_123',
        };

        const mockOverRide = {
            id: 1,
            MemberId: 1,
            StripePlanId: 'plan_123',
            UserId: 1,
            ProjectId: 1,
            expiryDate: '2024-12-31',
            comment: 'Test comment',
        };

        it('should successfully create a new override request', async () => {
            // Mock successful member lookup
            Member.findOne.mockResolvedValue(mockMember);
            Project.findByPk.mockResolvedValue(mockProject);
            OverRide.findOne.mockResolvedValue(null);
            OverRide.createInstance.mockResolvedValue(mockOverRide);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(Member.findOne).toHaveBeenCalledWith({
                where: expect.any(Object),
            });
            expect(Project.findByPk).toHaveBeenCalledWith(1);
            expect(OverRide.findOne).toHaveBeenCalledWith({
                where: expect.any(Object),
            });
            expect(OverRide.createInstance).toHaveBeenCalledWith({
                MemberId: 1,
                StripePlanId: 'plan_123',
                UserId: 1,
                ProjectId: 1,
                expiryDate: '2024-12-31',
                comment: 'Test comment',
            });
            expect(done).toHaveBeenCalledWith(mockOverRide, false);
        });

        it('should return message if override request already exists', async () => {
            Member.findOne.mockResolvedValue(mockMember);
            Project.findByPk.mockResolvedValue(mockProject);
            OverRide.findOne.mockResolvedValue(mockOverRide);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, {
                message: 'Your Booking Send Already , waiting for admin approval.',
            });
            expect(OverRide.createInstance).not.toHaveBeenCalled();
        });

        it('should return error if user is not a member', async () => {
            Member.findOne.mockResolvedValue(null);
            Project.findByPk.mockResolvedValue(mockProject);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, {
                message: "You are not a member / You don't have rights to apply that.",
            });
            expect(OverRide.createInstance).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            const error = new Error('Database error');
            Member.findOne.mockRejectedValue(error);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        // Additional positive test cases
        it('should handle different user roles', async () => {
            const mockMemberWithDifferentRole = { ...mockMember, RoleId: 3 };
            Member.findOne.mockResolvedValue(mockMemberWithDifferentRole);
            Project.findByPk.mockResolvedValue(mockProject);
            OverRide.findOne.mockResolvedValue(null);
            OverRide.createInstance.mockResolvedValue(mockOverRide);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(OverRide.createInstance).toHaveBeenCalledWith({
                MemberId: 1,
                StripePlanId: 'plan_123',
                UserId: 1,
                ProjectId: 1,
                expiryDate: '2024-12-31',
                comment: 'Test comment',
            });
            expect(done).toHaveBeenCalledWith(mockOverRide, false);
        });

        it('should handle different project configurations', async () => {
            const mockProjectWithDifferentPlan = { ...mockProject, StripePlanId: 'plan_456' };
            Member.findOne.mockResolvedValue(mockMember);
            Project.findByPk.mockResolvedValue(mockProjectWithDifferentPlan);
            OverRide.findOne.mockResolvedValue(null);
            OverRide.createInstance.mockResolvedValue({ ...mockOverRide, StripePlanId: 'plan_456' });

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(OverRide.createInstance).toHaveBeenCalledWith({
                MemberId: 1,
                StripePlanId: 'plan_456',
                UserId: 1,
                ProjectId: 1,
                expiryDate: '2024-12-31',
                comment: 'Test comment',
            });
        });

        // Additional negative test cases
        it('should handle null input data', async () => {
            const nullInputData = { body: null, user: { id: 1 } };
            const done = jest.fn();

            await overRideService.applyOverRide(nullInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing user in input', async () => {
            const invalidInputData = { body: mockInputData.body };
            const done = jest.fn();

            await overRideService.applyOverRide(invalidInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle project not found', async () => {
            Member.findOne.mockResolvedValue(mockMember);
            Project.findByPk.mockResolvedValue(null);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle member with isDeleted true', async () => {
            const deletedMember = { ...mockMember, isDeleted: true };
            Member.findOne.mockResolvedValue(deletedMember);
            Project.findByPk.mockResolvedValue(mockProject);

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(done).toHaveBeenCalledWith(
                null,
                expect.objectContaining({
                    message: expect.stringContaining('not a member'),
                }),
            );
        });

        it('should handle OverRide.createInstance failure', async () => {
            Member.findOne.mockResolvedValue(mockMember);
            Project.findByPk.mockResolvedValue(mockProject);
            OverRide.findOne.mockResolvedValue(null);
            OverRide.createInstance.mockRejectedValue(new Error('Creation failed'));

            const done = jest.fn();

            await overRideService.applyOverRide(mockInputData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle empty body data', async () => {
            const emptyBodyData = { body: {}, user: { id: 1 } };
            const done = jest.fn();

            await overRideService.applyOverRide(emptyBodyData, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('adminAction', () => {
        const mockInput = {
            body: {
                id: 1,
                status: 'Approved',
            },
        };

        const mockOverRide = {
            id: 1,
            MemberId: 1,
            StripePlanId: 'plan_123',
            UserId: 1,
            ProjectId: 1,
        };

        it('should successfully update override status and send mail', async () => {
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue([1]);
            MAILER.sendMail.mockImplementation((data, type, subject, tagName, callback) => {
                callback(data, null);
            });

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(OverRide.getBy).toHaveBeenCalledWith({ id: 1 });
            expect(OverRide.update).toHaveBeenCalledWith({ status: 'Approved' }, { where: { id: 1 } });
            expect(MAILER.sendMail).toHaveBeenCalledWith(
                mockOverRide,
                'adminoverride',
                'Admin Over Ride Status',
                'Admin Over Ride Status',
                expect.any(Function),
            );
            expect(done).toHaveBeenCalledWith(mockOverRide, false);
        });

        it('should handle override not found', async () => {
            OverRide.getBy.mockResolvedValue(null);

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
            expect(MAILER.sendMail).not.toHaveBeenCalled();
        });

        it('should handle update failure', async () => {
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue(null); // This simulates update failure

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
            expect(MAILER.sendMail).not.toHaveBeenCalled();
        });

        it('should handle mailer error', async () => {
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue([1]);
            MAILER.sendMail.mockImplementation((data, type, subject, tagName, callback) => {
                callback(null, new Error('Mail error'));
            });

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(
                null,
                expect.objectContaining({
                    message: 'Mail error',
                    statusCode: status.BAD_REQUEST,
                }),
            );
        });

        it('should handle database errors', async () => {
            const error = new Error('Database error');
            OverRide.getBy.mockRejectedValue(error);

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        // Additional positive test cases for adminAction
        it('should handle different status values', async () => {
            const mockInputWithDifferentStatus = {
                body: { id: 1, status: 'Rejected' },
            };
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue([1]);
            MAILER.sendMail.mockImplementation((data, type, subject, tagName, callback) => {
                callback(data, null);
            });

            const done = jest.fn();

            await overRideService.adminAction(mockInputWithDifferentStatus, done);

            expect(OverRide.update).toHaveBeenCalledWith({ status: 'Rejected' }, { where: { id: 1 } });
            expect(done).toHaveBeenCalledWith(mockOverRide, false);
        });

        it('should handle multiple rows updated', async () => {
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue([2]); // Multiple rows updated
            MAILER.sendMail.mockImplementation((data, type, subject, tagName, callback) => {
                callback(data, null);
            });

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(MAILER.sendMail).toHaveBeenCalled();
            expect(done).toHaveBeenCalledWith(mockOverRide, false);
        });

        // Additional negative test cases for adminAction
        it('should handle null input body', async () => {
            const nullBodyInput = { body: null };
            const done = jest.fn();

            await overRideService.adminAction(nullBodyInput, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing id in input', async () => {
            const missingIdInput = { body: { status: 'Approved' } };
            const done = jest.fn();

            await overRideService.adminAction(missingIdInput, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing status in input', async () => {
            const missingStatusInput = { body: { id: 1 } };
            const done = jest.fn();

            await overRideService.adminAction(missingStatusInput, done);

            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle update returning false', async () => {
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue(false);

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
            expect(MAILER.sendMail).not.toHaveBeenCalled();
        });

        it('should handle update returning undefined', async () => {
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockResolvedValue(undefined);

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(null, { message: 'Something went wrong' });
            expect(MAILER.sendMail).not.toHaveBeenCalled();
        });

        it('should handle OverRide.update throwing error', async () => {
            const updateError = new Error('Update failed');
            OverRide.getBy.mockResolvedValue(mockOverRide);
            OverRide.update.mockRejectedValue(updateError);

            const done = jest.fn();

            await overRideService.adminAction(mockInput, done);

            expect(done).toHaveBeenCalledWith(null, updateError);
        });
    });

    describe('sendMail', () => {
        const mockOverRideData = {
            id: 1,
            UserId: 1,
        };

        it('should successfully send mail', async () => {
            MAILER.sendMail.mockImplementation((data, type, subject, tagName, callback) => {
                callback(data, null);
            });

            const done = jest.fn();

            await overRideService.sendMail(
                mockOverRideData,
                'adminoverride',
                'Admin Over Ride Status',
                'Admin Over Ride Status',
                done,
            );

            expect(MAILER.sendMail).toHaveBeenCalledWith(
                mockOverRideData,
                'adminoverride',
                'Admin Over Ride Status',
                'Admin Over Ride Status',
                expect.any(Function),
            );
            expect(done).toHaveBeenCalledWith(mockOverRideData, false);
        });

        it('should handle mailer error', async () => {
            const mailError = new Error('Mail error');
            MAILER.sendMail.mockImplementation((data, type, subject, tagName, callback) => {
                callback(null, mailError);
            });

            const done = jest.fn();

            await overRideService.sendMail(
                mockOverRideData,
                'adminoverride',
                'Admin Over Ride Status',
                'Admin Over Ride Status',
                done,
            );

            expect(done).toHaveBeenCalledWith(
                null,
                expect.objectContaining({
                    message: 'Mail error',
                    statusCode: status.BAD_REQUEST,
                }),
            );
            expect(ApiError).toHaveBeenCalledWith(mailError.message, status.BAD_REQUEST);
        });
    });
});
