const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    get: jest.fn().mockReturnThis(),
    post: jest.fn().mockReturnThis(),
    route: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../controllers', () => ({
  AdminController: {
    users: jest.fn(),
    adminLogin: jest.fn(),
    createAccountAdmin: jest.fn(),
    show: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isAdmin: jest.fn(),
}));

jest.mock('../../middlewares/validations', () => ({
  adminValidation: {
    createAccountAdmin: jest.fn(),
    updateUser: jest.fn(),
  },
  userValidation: {
    login: jest.fn(),
  },
}));

describe('adminRoute', () => {
  let router;
  let adminRoute;
  let AdminController;
  let passportConfig;
  let checkAdmin;
  let adminValidation;
  let userValidation;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      get: jest.fn().mockReturnThis(),
      post: jest.fn().mockReturnThis(),
      route: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    adminRoute = require('../adminRoute');
    const controllers = require('../../controllers');
    AdminController = controllers.AdminController;
    passportConfig = require('../../config/passport');
    checkAdmin = require('../../middlewares/checkAdmin');
    const validations = require('../../middlewares/validations');
    adminValidation = validations.adminValidation;
    userValidation = validations.userValidation;
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = adminRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify routes are registered
      expect(router.get).toHaveBeenCalledWith('/users', AdminController.users);

      expect(router.post).toHaveBeenCalledWith(
        '/admin_login',
        'mocked-validate-middleware',
        AdminController.adminLogin,
      );

      expect(router.post).toHaveBeenCalledWith(
        '/create_account_admin',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        'mocked-validate-middleware',
        AdminController.createAccountAdmin,
      );

      // Verify route chaining for /users/:id
      expect(router.route).toHaveBeenCalledWith('/users/:id');
      expect(router.get).toHaveBeenCalledWith(
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AdminController.show,
      );
      expect(router.put).toHaveBeenCalledWith(
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AdminController.update,
      );
      expect(router.delete).toHaveBeenCalledWith(
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AdminController.delete,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledWith(
        userValidation.login,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        adminValidation.createAccountAdmin,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        adminValidation.updateUser,
        { keyByField: true },
        { abortEarly: false },
      );
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = adminRoute.router;
      const result2 = adminRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should configure routes with proper middleware chains', () => {
      adminRoute.router;

      // Check that authenticated routes have proper middleware
      expect(router.post).toHaveBeenCalledWith(
        '/create_account_admin',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        'mocked-validate-middleware',
        AdminController.createAccountAdmin,
      );
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof adminRoute).toBe('object');
      expect(adminRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(adminRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(adminRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
