const { Router } = require('express');
const { validate } = require('express-validation');
const CarbonEmissionController = require('../controllers/carbonEmissionController');
const multer = require('multer');
const upload1 = multer({ dest: 'uploads/' }); // NOSONAR
const storage = multer.memoryStorage();
const upload = multer({ storage }); // NOSONAR


const carbonEmissionRoute = {
    get router() {
        const router = Router();
        router.post(
            '/upload_utilities/:zipcode/:ProjectId',
            upload.single('utilities'),
            CarbonEmissionController.fileUpload,
        );

        router.get(
            '/get_dashboard_data/:ProjectId',
            CarbonEmissionController.dashboardData,
        );
        return router;
    }

};
module.exports = carbonEmissionRoute;
