// Mock dependencies first before requiring modules
jest.mock('fs');
jest.mock('lodash');
jest.mock('../../middlewares/awsConfig');
jest.mock('puppeteer', () => ({
    launch: jest.fn(),
}));
jest.mock('../puppeteerService');
jest.mock('../../models', () => ({
    Project: {
        findOne: jest.fn(),
    },
    Company: {
        findOne: jest.fn(),
    },
}));
jest.mock('moment');

const fs = require('fs');
const _ = require('lodash');
const moment = require('moment');
const pdfHeatMapService = require('../pdfHeatMapService');
const puppeteerService = require('../puppeteerService');
const awsConfig = require('../../middlewares/awsConfig');
const { Project, Company } = require('../../models');

describe('pdfHeatMapService', () => {
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock fs
        fs.readFileSync.mockReturnValue('<html>$projectName $companyName $ReportName $generatedDate $generatedBy $data</html>');

        // Mock awsConfig
        awsConfig.reportUpload.mockImplementation((buffer, name, type, callback) => {
            callback('success-url', null);
        });

        // Mock puppeteerService
        puppeteerService.generatePdfBuffer.mockResolvedValue(Buffer.from('pdf-content'));

        // Mock lodash
        _.max.mockReturnValue(10);
        _.flatMap.mockReturnValue([1, 2, 3, 4, 5]);
        _.map.mockReturnValue([{ timeslots: [1, 2, 3] }]);
        _.values.mockReturnValue([1, 2, 3]);

        // Mock models
        Project.findOne.mockResolvedValue({
            projectName: 'Test Project'
        });

        Company.findOne.mockResolvedValue({
            companyName: 'Test Company'
        });
    });

    describe('pdfFormatOfDeliveryRequest', () => {
        const mockParams = {
            ProjectId: 1
        };

        const mockLoginUser = {
            firstName: 'John',
            lastName: 'Doe'
        };

        const mockData = {
            '2024-01-15': {
                timeslots: { '08:00': 5, '09:00': 3, '10:00': 7 },
                totalCount: 15
            },
            '2024-01-16': {
                timeslots: { '08:00': 2, '09:00': 8, '10:00': 4 },
                totalCount: 14
            }
        };

        const mockReq = {
            body: {
                ParentCompanyId: 1,
                reportName: 'Heat Map Report',
                generatedDate: '2024-01-15',
                exportType: 'pdf'
            }
        };

        it('should generate PDF successfully', async () => {
            const mockDone = jest.fn();

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                mockDone
            );

            expect(Project.findOne).toHaveBeenCalledWith({
                where: { isDeleted: false, id: 1 },
                attributes: ['projectName']
            });

            expect(Company.findOne).toHaveBeenCalledWith({
                where: { isDeleted: false, ParentCompanyId: 1, isParent: true },
                attributes: ['companyName']
            });

            expect(fs.readFileSync).toHaveBeenCalledWith(
                '/usr/src/web/api/v20/views/mail-templates/report-heat-map.html',
                { encoding: 'utf-8' }
            );

            expect(puppeteerService.generatePdfBuffer).toHaveBeenCalled();
            expect(awsConfig.reportUpload).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith('success-url', false);
        });

        it('should handle project not found', async () => {
            Project.findOne.mockResolvedValue(null);
            const mockDone = jest.fn();

            await expect(
                pdfHeatMapService.pdfFormatOfDeliveryRequest(
                    mockParams,
                    mockLoginUser,
                    mockData,
                    mockReq,
                    mockDone
                )
            ).rejects.toThrow();
        });

        it('should handle company not found', async () => {
            Company.findOne.mockResolvedValue(null);
            const mockDone = jest.fn();

            await expect(
                pdfHeatMapService.pdfFormatOfDeliveryRequest(
                    mockParams,
                    mockLoginUser,
                    mockData,
                    mockReq,
                    mockDone
                )
            ).rejects.toThrow();
        });

        it('should handle PDF generation failure', async () => {
            puppeteerService.generatePdfBuffer.mockResolvedValue(null);
            const mockDone = jest.fn();

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                mockDone
            );

            expect(mockDone).toHaveBeenCalledWith(false, { message: 'cannot export the document' });
        });

        it('should handle AWS upload failure', async () => {
            awsConfig.reportUpload.mockImplementation((buffer, name, type, callback) => {
                callback(null, 'Upload failed');
            });
            const mockDone = jest.fn();

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                mockDone
            );

            expect(mockDone).not.toHaveBeenCalled();
        });

        it('should handle database errors', async () => {
            Project.findOne.mockRejectedValue(new Error('Database error'));
            const mockDone = jest.fn();

            await expect(
                pdfHeatMapService.pdfFormatOfDeliveryRequest(
                    mockParams,
                    mockLoginUser,
                    mockData,
                    mockReq,
                    mockDone
                )
            ).rejects.toThrow('Database error');
        });

        it('should handle empty data', async () => {
            const mockDone = jest.fn();
            const emptyData = {};

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                emptyData,
                mockReq,
                mockDone
            );

            expect(mockDone).toHaveBeenCalledWith('success-url', false);
        });

        it('should process heat map data correctly', async () => {
            const mockDone = jest.fn();

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                mockDone
            );

            expect(_.max).toHaveBeenCalled();
            expect(_.flatMap).toHaveBeenCalled();
            expect(_.map).toHaveBeenCalled();
            expect(_.values).toHaveBeenCalled();
        });

        it('should replace template placeholders correctly', async () => {
            const mockDone = jest.fn();

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                mockData,
                mockReq,
                mockDone
            );

            const expectedTemplate = expect.stringContaining('Test Project');
            expect(puppeteerService.generatePdfBuffer).toHaveBeenCalledWith(expectedTemplate);
        });

        it('should handle special characters in date', async () => {
            const mockDone = jest.fn();
            const dataWithComma = {
                'Monday, January 15': {
                    timeslots: { '08:00': 5 },
                    totalCount: 5
                }
            };

            await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                mockParams,
                mockLoginUser,
                dataWithComma,
                mockReq,
                mockDone
            );

            expect(mockDone).toHaveBeenCalledWith('success-url', false);
        });
    });
});