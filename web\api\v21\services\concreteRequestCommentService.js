const moment = require('moment');
const Cryptr = require('cryptr');
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  ConcreteRequest,
  ConcreteRequestResponsiblePerson,
  ConcreteRequestComment,
  Member,
  ConcreteRequestHistory,
  User,
  DeliveryPersonNotification,
  Project,
  Notification,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const MAILER = require('../mailer');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const concreteRequestCommentService = {
  async getConcreteRequestComments2(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const exist = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: +params.ConcreteRequestId,
          ProjectId: +params.ProjectId,
          isDeleted: false,
        },
      });
      if (exist) {
        const commentList = await ConcreteRequestComment.findAndCountAll({
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          where: {
            ConcreteRequestId: exist.id,
            isDeleted: false,
          },
        });
        done({ commentList, exist }, false);
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getDomainNameFromEnterprise(domainName) {
    if (!domainName) return '';

    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() }
    });

    return domainEnterpriseValue ? domainName : '';
  },
  async getEnterpriseValueFromParentCompany(ParentCompanyId, email) {
    if (!ParentCompanyId || ParentCompanyId === 'undefined') return null;

    const userData = email ? await publicUser.findOne({ where: { email } }) : null;
    if (!userData) return null;

    const memberData = await publicMember.findOne({
      where: {
        UserId: userData.id,
        RoleId: { [Op.ne]: 4 },
        isDeleted: false
      }
    });

    if (!memberData) {
      return await Enterprise.findOne({
        where: { ParentCompanyId, status: 'completed' }
      });
    }

    if (memberData.isAccount) {
      return await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' }
      });
    }

    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const { domainName: initialDomainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;

    let domainName = await this.getDomainNameFromEnterprise(initialDomainName);
    if (!domainName) {
      const enterpriseValue = await this.getEnterpriseValueFromParentCompany(
        ParentCompanyId,
        inputData.user.email
      );

      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
      }
    }

    const modelObj = await helper.getDynamicModel(domainName);
    Object.assign(this, modelObj);

    if (domainName) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      inputData.user = newUser;
    }

    return true;
  },
  async getConcreteRequestWithDetails(incomeData) {
    return await ConcreteRequest.findOne({
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
      ],
      where: {
        ConcreteRequestId: incomeData.ConcreteRequestId,
        ProjectId: incomeData.ProjectId,
        isDeleted: false,
      },
    });
  },
  async createCommentHistory(exist, memberData, loginUser, incomeData, locationChosen) {
    return {
      ConcreteRequestId: +exist.id,
      MemberId: memberData.id,
      type: 'comment',
      description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
      locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
    };
  },
  async createNotification(history, exist) {
    const notification = { ...history };
    notification.ProjectId = exist.ProjectId;
    notification.title = 'Concrete Booking Comment';
    notification.isDeliveryRequest = false;
    notification.requestType = 'concreteRequest';
    return await Notification.createInstance(notification);
  },
  async sendGuestNotifications(userDataMail, loginUser, exist, incomeData) {
    for (const userMail of userDataMail) {
      const responsibleGuestUser = userMail?.Member?.isGuestUser;
      if (responsibleGuestUser) {
        const guestMailPayload = {
          email: userMail?.Member?.User?.email,
          guestName: userMail?.Member?.User?.firstName,
          content: `We would like to inform you that 
          ${loginUser.firstName} ${loginUser.lastName} added comment : ${incomeData.comment} on Concrete Booking - ${exist.description}`,
        };
        await MAILER.sendMail(
          guestMailPayload,
          'notifyGuestOnEdit',
          `Comments added by ${loginUser.firstName} `,
          'Comments added against a Concrete Booking Notification',
          async (info, err) => {
            console.log(info, err);
          },
        );
      }
    }
  },
  async createConcreteRequestComment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const loginUser = inputData.user;

      const exist = await this.getConcreteRequestWithDetails(incomeData);
      if (!exist) {
        return done(null, { message: 'Concrete Booking does not exist' });
      }

      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: incomeData.ProjectId,
          isDeleted: false,
        }),
      });

      const locationChosen = await Locations.findOne({
        where: {
          ProjectId: exist.ProjectId,
          id: exist.LocationId,
        },
      });

      const membersToNotify = await this.collectMembersToNotify(exist, memberData);
      incomeData.MemberId = memberData.id;
      const history = await this.createCommentHistory(exist, memberData, loginUser, incomeData, locationChosen);
      const notification = await this.createNotification(history, exist);

      const previousComments = await ConcreteRequestComment.findAll({
        where: {
          ProjectId: incomeData.ProjectId,
          ConcreteRequestId: incomeData.ConcreteRequestId,
        },
        attributes: ['id', 'comment'],
      });

      const commentsArray = previousComments.map(comment => JSON.stringify(comment.comment));
      const resultedArray = commentsArray.join(',');

      const toAddCommentObject = { ...inputData.body };
      delete toAddCommentObject.ConcreteRequestId;
      toAddCommentObject.ConcreteRequestId = exist.id;

      await ConcreteRequestComment.createInstance(toAddCommentObject);
      await ConcreteRequestHistory.createInstance(history);

      history.firstName = loginUser.firstName;
      history.profilePic = loginUser.profilePic;
      history.createdAt = new Date();
      history.ProjectId = exist.ProjectId;

      const projectDetails = await Project.findByPk(exist.ProjectId);
      history.projectName = projectDetails.projectName;

      const personData = await this.getResponsiblePersons(history, exist, membersToNotify.locationFollowMembers);
      const adminData = await this.getAdminData(exist, history, membersToNotify.bookingMemberDetails);

      history.memberData = personData;
      history.adminData = adminData;

      if (membersToNotify.memberLocationPreference?.length > 0) {
        history.memberData.push(...membersToNotify.memberLocationPreference);
        await this.notifyLocationPreferenceMembers(exist, notification, history, membersToNotify.memberLocationPreference);
      }

      await this.sendNotifications(
        {
          adminData,
          personData,
          projectDetails,
          notification,
          memberData,
          loginUser,
          exist,
          incomeData,
          resultedArray,
          history
        },
        done
      );

    } catch (e) {
      done(null, e);
    }
  },
  async collectMembersToNotify(exist, memberData) {
    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: exist.ProjectId,
        LocationId: exist.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            [Op.and]: [
              {
                id: { [Op.ne]: memberData.id },
              },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    const locationFollowMembers = memberLocationPreference.map(element => element.Member.id);
    const bookingMemberDetails = exist.memberDetails.map(element => element.Member.id);

    return {
      memberLocationPreference,
      locationFollowMembers,
      bookingMemberDetails
    };
  },
  async getResponsiblePersons(history, exist, locationFollowMembers) {
    return await ConcreteRequestResponsiblePerson.findAll({
      where: { ConcreteRequestId: history.ConcreteRequestId, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          where: {
            id: { [Op.ne]: history.MemberId },
            [Op.and]: {
              RoleId: {
                [Op.notIn]: [1, 2],
              },
              id: { [Op.notIn]: locationFollowMembers },
            },
          },
          attributes: ['id', 'RoleId'],
        },
      ],
      attributes: ['id'],
    });
  },
  async getAdminData(exist, history, bookingMemberDetails) {
    return await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: exist.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: bookingMemberDetails } },
          { id: { [Op.ne]: history.MemberId } },
          { id: { [Op.notIn]: history.memberData } },
        ],
      },
      include: [
        {
          association: 'User',
          attributes: ['id', 'email', 'firstName', 'lastName'],
        },
      ],
      attributes: ['id'],
    });
  },
  async notifyLocationPreferenceMembers(exist, notification, history, memberLocationPreference) {
    await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
      memberLocationPreference,
      exist.ConcreteRequestId,
      history.locationFollowDescription,
      exist.requestType,
      exist.ProjectId,
      exist.id,
      4,
    );
    await notificationHelper.createMemberDeliveryLocationInAppNotification(
      DeliveryPersonNotification,
      exist.ProjectId,
      notification.id,
      memberLocationPreference,
      4,
    );
  },
  async sendNotifications(params, done) {
    const {
      adminData,
      personData,
      projectDetails,
      notification,
      memberData,
      loginUser,
      exist,
      incomeData,
      resultedArray,
      history
    } = params;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      notification,
      DeliveryPersonNotification,
      memberData,
      loginUser,
      4,
      'commented in a',
      'Concrete Request',
      `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
      incomeData.ConcreteRequestId,
    );

    const checkMemberNotification = await this.checkMemberNotificationPreferences(exist, notification);

    const userEmails = await this.getMemberDetailData(history, checkMemberNotification);

    if (userEmails.length > 0) {
      await this.processUserEmails(userEmails, exist, incomeData, loginUser, resultedArray, done, checkMemberNotification);
    } else {
      done(history, false);
    }
  },
  async checkMemberNotificationPreferences(exist, newNotification) {
    return await NotificationPreference.findAll({
      where: {
        ProjectId: exist.ProjectId,
        isDeleted: false,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 4,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });
  },
  async processUserEmails(userEmails, exist, incomeData, loginUser, resultedArray, done, checkMemberNotification) {
    for (const [i, element] of userEmails.entries()) {
      const time = moment(exist.concretePlacementStart).format('MM-DD-YYYY');
      const mailPayload = {
        concreteId: exist.ConcreteRequestId,
        concreteDescription: exist.description,
        concreteStart: time,
        concreteEnd: exist.concretePlacementEnd,
        newComment: incomeData.comment,
        previousComments: resultedArray,
        toEmailUserName: element.firstName,
        email: element.email,
        commentedPersonname: loginUser.firstName,
        commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
      };

      if (!mailPayload.toEmailUserName) {
        mailPayload.toEmailUserName = element.email;
      }

      const isMemberFollowLocation = await this.checkLocationFollow(exist, element);

      if (isMemberFollowLocation) {
        const memberNotification = await this.getMemberNotificationPreferences(exist, element);

        await this.notifyByEmail(memberNotification, mailPayload, loginUser, exist, incomeData, element);
      }

      if (i === userEmails.length - 1) {
        done(history, false);
      }
    }
  },
  async checkLocationFollow(exist, element) {
    return await LocationNotificationPreferences.findOne({
      where: {
        MemberId: +element.MemberId,
        ProjectId: +exist.ProjectId,
        LocationId: +exist.LocationId,
        isDeleted: false,
      },
    });
  },
  async getMemberNotificationPreferences(exist, element) {
    return await NotificationPreference.findOne({
      where: {
        MemberId: +element.MemberId,
        ProjectId: +exist.ProjectId,
        isDeleted: false,
      },
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 7,
            isDeleted: false,
          },
        },
      ],
    });
  },
  async notifyByEmail(memberNotification, mailPayload, loginUser, exist, incomeData, element) {
    if (memberNotification?.instant) {
      await MAILER.sendMail(
        mailPayload,
        'concretecommentadded',
        `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Concrete Booking ID ${exist.ConcreteRequestId}`,
        'Comments added against a Concrete Booking Notification',
        async (info, err) => {
          console.log(info, err);
        },
      );
    }

    if (memberNotification?.dailyDigest) {
      await this.createDailyDigestData({
        MemberId: +element.MemberId,
        ProjectId: +exist.ProjectId,
        ParentCompanyId: +incomeData.ParentCompanyId,
        loginUser,
        dailyDigestMessage: 'commented in a',
        requestType: 'Concrete Request',
        messages: `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
        requestId: exist.ConcreteRequestId
      });
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        this.addToEmailArray(element, existAdminData, emailArray);
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        this.addToEmailArray(element, existAdminData, emailArray);
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        this.addToEmailArray(element, existAdminData, emailArray);
      });
    }
    return emailArray;
  },
  addToEmailArray(element, existAdminData, emailArray) {
    const index = existAdminData.findIndex(
      (adminNew) => adminNew.email === element?.Member?.User?.email,
    );
    if (index === -1) {
      existAdminData.push({ email: element?.Member?.User?.email });
      emailArray.push({
        email: element?.Member?.User?.email,
        firstName: element?.Member?.User?.firstName,
        UserId: element?.Member?.User?.id,
        MemberId: element?.Member?.id,
      });
    }
  },
  async createDailyDigestData({
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    requestId
  }) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    const { imageUrl, link, height } = this.getImageUrlAndLinkForDigest(requestType);
    const object = {
      description: `<div>
  <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
    <li style="display:flex;">
      <img src="${imageUrl}" alt="message-icon" style="${height}">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="#" target="" style="text-decoration: none;color:#4470FF;">
            ${loginUser.firstName}  ${loginUser.lastName}
          </a>
          ${dailyDigestMessage}
          <a href="${process.env.BASE_URL
        }/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >
      ${messages} 
          </a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
        </p>
    </li>
  </ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  getImageUrlAndLinkForDigest(requestType) {
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    return { imageUrl, link, height };
  },
};

function determineModelType(comment) {
  if (comment.isInternal) {
    return 'internal';
  }
  if (comment.isPrivate) {
    return 'private';
  }
  if (comment.isSystem) {
    return 'system';
  }
  return 'public';
}

function determineFields(modelType, comment) {
  const fields = ['id', 'text', 'createdAt', 'createdBy', 'updatedAt', 'updatedBy'];

  if (modelType === 'internal') {
    fields.push('isInternal');
  } else if (modelType === 'private') {
    fields.push('isPrivate');
  } else if (modelType === 'system') {
    fields.push('isSystem');
  }

  if (comment.attachments?.length > 0) {
    fields.push('attachments');
  }

  return fields;
}

function getDynamicModel(comment) {
  if (!comment) {
    return null;
  }

  const modelType = determineModelType(comment);
  const fields = determineFields(modelType, comment);

  return {
    name: `ConcreteRequestComment${modelType.charAt(0).toUpperCase() + modelType.slice(1)}`,
    fields
  };
}

module.exports = concreteRequestCommentService;