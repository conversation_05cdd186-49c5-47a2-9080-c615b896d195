module.exports = (sequelize, DataTypes) => {
  const Enterprise = sequelize.define(
    'Enterprise',
    {
      name: DataTypes.STRING,
      UserId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      domainURL: DataTypes.STRING,
      ParentCompanyId: DataTypes.INTEGER,
      currencyType: DataTypes.STRING,
      amount: DataTypes.INTEGER,
      projectCount: DataTypes.INTEGER,
      status: {
        type: DataTypes.ENUM,
        values: ['Not started', 'started', 'in progress'],
      },
      isActive: DataTypes.BOOLEAN,
      dbCreated: DataTypes.BOOLEAN,
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  Enterprise.associate = (models) => {
    Enterprise.belongsTo(models.User);
    Enterprise.belongsTo(models.ParentCompany);
    Enterprise.hasMany(models.Project);
    Enterprise.hasMany(models.Member);
    return Enterprise;
  };
  Enterprise.getEnterprise = async (attr) => {
    const enterprise = await Enterprise.findOne({
      include: [
        { association: 'User', attributes: { exclude: ['password', 'resetPasswordToken'] } },
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return enterprise;
  };

  Enterprise.createInstance = async (enterpriseData) => {
    const newData = await Enterprise.create(enterpriseData);
    return newData;
  };
  return Enterprise;
};
