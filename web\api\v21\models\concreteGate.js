module.exports = (sequelize, DataTypes) => {
    const ConcreteGate = sequelize.define(
        'ConcreteGate',
        {
            ConcreteRequestId: DataTypes.INTEGER,
            isDeleted: DataTypes.BOOLEAN,
            ConcreteRequestCode: DataTypes.INTEGER,
            publicSchemaId: {
                type: DataTypes.INTEGER,
            },
            GateId: DataTypes.INTEGER,
            ProjectId: DataTypes.INTEGER,
            isActive: DataTypes.BOOLEAN,
        },
        {},
    );
    ConcreteGate.associate = (models) => {
        // associations can be defined here
        ConcreteGate.belongsTo(models.ConcreteRequest, {
            as: 'concreterequest',
            foreignKey: 'ConcreteRequestId',
        });
        ConcreteGate.belongsTo(models.Gates, {
            as: 'Gates',
            foreignKey: 'GateId',
        });
        ConcreteGate.belongsTo(models.Gates);
    };
    ConcreteGate.createInstance = async (paramData) => {
        const newConcreteGate = await ConcreteGate.create(paramData);
        return newConcreteGate;
    };
    return ConcreteGate;
};
