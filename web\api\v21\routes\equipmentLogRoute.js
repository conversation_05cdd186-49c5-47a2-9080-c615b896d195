const { Router } = require('express');
const { validate } = require('express-validation');
const { equipmentValidation } = require('../middlewares/validations');
const EquipmentLogController = require('../controllers/equipmentLogController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const equipmentLogRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_equipmentlog',
            passportConfig.isAuthenticated,
            EquipmentLogController.addEquipmentLog,
        );
        router.get(
            '/equipmentlog_list/:pageSize/:pageNo',
            passportConfig.isAuthenticated,
            EquipmentLogController.listEquipmentLog,
        );
        return router;
    },
};
module.exports = equipmentLogRoute;