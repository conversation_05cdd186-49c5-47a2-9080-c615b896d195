const { parentPort } = require('worker_threads');
const moment = require('moment');

const { parse } = require('flatted');
const {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  Role,
  Gates,
  Equipments,
  DeliverDefineWork,
  DeliverDefine,
  DeliverHistory,
  DeliveryPersonNotification,
  User,
  Notification,
  NotificationPreference,
  Locations,
} = require('../models');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const companyService = require('./companyService');
const deliveryService = require('./deliveryService');
const craneRequestService = require('./craneRequestService');
const { Sequelize } = require('../models');

const { Op } = Sequelize;

async function processRowData(getRow) {
  return {
    description: getRow?.[1],
    responsible_company: getRow?.[2],
    definable_feature_of_work: getRow?.[3],
    responsible_person: getRow?.[4],
    is_escort_needed: getRow?.[5],
    date: getRow?.[6] ? moment(getRow[6]).format('MM-DD-YYYY') : getRow?.[6],
    from_time: getRow?.[7],
    to_time: getRow?.[8],
    gate: getRow?.[9],
    equipment: getRow?.[10],
    picking_from: getRow?.[11],
    picking_to: getRow?.[12],
    vehicle_detail: getRow?.[13],
    additional_notes: getRow?.[14],
    location: getRow?.[15],
  };
}

async function getDeliveryId(ProjectId) {
  const lastIdValue = await DeliveryRequest.findOne({
    where: { ProjectId, isDeleted: false },
    order: [['DeliveryId', 'DESC']],
  });
  const newValue = JSON.parse(JSON.stringify(lastIdValue));
  return newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined
    ? newValue.DeliveryId
    : 0;
}

function calculateDeliveryDates(row, inputData) {
  if (!row.date) return { startDate: null, endDate: null };

  const startDate = moment(new Date(`${row.date} ${row.from_time}`).toUTCString())
    .add(-inputData.headers.timezoneoffset, 'm')
    .toDate()
    .toString();
  const endDate = moment(new Date(`${row.date} ${row.to_time}`).toUTCString())
    .add(-inputData.headers.timezoneoffset, 'm')
    .toDate()
    .toString();

  return { startDate, endDate };
}

async function createDeliveryRequest(row, memberDetails, ProjectId, id, inputData) {
  const { startDate, endDate } = calculateDeliveryDates(row, inputData);
  const DeliverParam = {
    description: row.description,
    vehicleDetails: row.vehicle_detail || '',
    notes: row.additional_notes || '',
    DeliveryId: id + 1,
    deliveryStart: startDate,
    deliveryEnd: endDate,
    ProjectId,
    createdBy: memberDetails.id,
    isQueued: true,
    status: 'Pending',
    escort: row.is_escort_needed === 'escort needed',
  };

  return await DeliveryRequest.createInstance(DeliverParam);
}

async function createDeliveryHistory(newDeliverData, memberDetails, loginUser) {
  const history = {
    DeliveryRequestId: newDeliverData.id,
    DeliveryId: newDeliverData.DeliveryId,
    MemberId: memberDetails.id,
    type: 'create',
    description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${newDeliverData.description}.`,
  };
  await DeliverHistory.createInstance(history);
  return history;
}

async function handleCompanyAssociation(row, updateParam, inputData) {
  if (!row.responsible_company) return;

  const responseData = await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(inputData);
  const companiesData = responseData.newCompanyList;
  const chosenCompany = companiesData.filter(
    (company) => company.companyName === row.responsible_company,
  );

  if (chosenCompany && chosenCompany.length > 0) {
    await DeliverCompany.createInstance({
      ...updateParam,
      CompanyId: chosenCompany[0].id,
    });
  } else {
    await markDeliveryAsDeleted(updateParam.DeliveryId);
  }
}

async function handleGateAssociation(row, updateParam, ProjectId) {
  if (!row.gate) return;

  const gateData = await Gates.findOne({
    where: { gateName: row.gate, ProjectId },
  });

  if (gateData) {
    await DeliverGate.createInstance({
      ...updateParam,
      GateId: gateData.id,
    });
  } else {
    await markDeliveryAsDeleted(updateParam.DeliveryId);
  }
}

async function handleEquipmentAssociation(row, updateParam, ProjectId) {
  if (!row.equipment) return;

  const equipmentData = await Equipments.findOne({
    where: { equipmentName: row.equipment, ProjectId },
    include: [
      {
        required: false,
        where: { isDeleted: false, isActive: true },
        association: 'PresetEquipmentType',
        attributes: ['id', 'equipmentType', 'isCraneType'],
      },
    ],
  });

  if (!equipmentData) {
    await markDeliveryAsDeleted(updateParam.DeliveryId);
    return;
  }

  if (equipmentData.PresetEquipmentType.isCraneType) {
    await handleCraneEquipment(row, updateParam, equipmentData, ProjectId);
  } else {
    await DeliverEquipment.createInstance({
      ...updateParam,
      EquipmentId: equipmentData.id,
    });
  }
}

async function handleCraneEquipment(row, updateParam, equipmentData, ProjectId) {
  const requestParam = {
    isAssociatedWithCraneRequest: true,
    requestType: 'deliveryRequestWithCrane',
  };

  if (row.picking_from) requestParam.cranePickUpLocation = row.picking_from;
  if (row.picking_to) requestParam.craneDropOffLocation = row.picking_to;

  const payload = { params: { ProjectId } };
  await craneRequestService.lastCraneRequest(payload, (lastDetail) => {
    if (lastDetail) requestParam.CraneRequestId = lastDetail.CraneRequestId;
  });

  await DeliverEquipment.createInstance({
    ...updateParam,
    EquipmentId: equipmentData.id,
  });

  await DeliveryRequest.update(requestParam, {
    where: { id: updateParam.DeliveryId },
  });
}

async function handleLocationAssociation(row, updateParam, ProjectId) {
  if (!row.location) return;

  const locationData = await Locations.findOne({
    where: { locationPath: row.location, ProjectId },
  });

  if (locationData) {
    await DeliveryRequest.update(
      { LocationId: locationData.id },
      { where: { id: updateParam.DeliveryId } },
    );
  } else {
    await markDeliveryAsDeleted(updateParam.DeliveryId);
  }
}

async function handleDefineWorkAssociation(row, updateParam, ProjectId) {
  if (!row.definable_feature_of_work) return;

  const defineData = await DeliverDefineWork.findOne({
    where: {
      DFOW: row.definable_feature_of_work,
      ProjectId,
    },
  });

  if (defineData) {
    await DeliverDefine.createInstance({
      ...updateParam,
      DeliverDefineWorkId: defineData.id,
    });
  } else {
    await markDeliveryAsDeleted(updateParam.DeliveryId);
  }
}

async function handleResponsiblePerson(row, updateParam, ProjectId) {
  if (!row.responsible_person) return [];

  const responsiblePerson = typeof row.responsible_person === 'object'
    ? row.responsible_person.text
    : row.responsible_person;

  const userData = await User.findOne({
    where: { email: responsiblePerson },
  });

  if (!userData) return [];

  const memberData = await Member.findOne({
    where: { UserId: userData.id, ProjectId, isDeleted: false },
  });

  if (!memberData) {
    await markDeliveryAsDeleted(updateParam.DeliveryId);
    return [];
  }

  await DeliveryPerson.createInstance({
    ...updateParam,
    MemberId: memberData.id,
  });

  return [memberData.id];
}

async function markDeliveryAsDeleted(DeliveryId) {
  await DeliveryRequest.update(
    { isDeleted: true },
    { where: { id: DeliveryId } },
  );
}

async function handleNotifications(history, ProjectId, persons, loginUser, projectDetails, newDeliverData) {
  const notification = { ...history, ProjectId, title: 'Delivery Booking Creation' };
  const newNotification = await Notification.createInstance(notification);

  const adminData = await Member.findAll({
    where: {
      [Op.or]: [
        {
          [Op.and]: [
            { ProjectId },
            { isDeleted: false },
            { [Op.or]: [{ RoleId: [2, 1] }] },
            { id: { [Op.ne]: newNotification.MemberId } },
          ],
        },
        { [Op.and]: [{ id: { [Op.in]: persons } }] },
      ],
    },
    include: [
      {
        association: 'User',
        attributes: ['id', 'firstName', 'lastName'],
      },
    ],
    attributes: ['id'],
  });

  const personData = await DeliveryPerson.findAll({
    where: { DeliveryId: history.DeliveryRequestId, isDeleted: false },
    include: [
      {
        association: 'Member',
        include: [
          {
            association: 'User',
            attributes: ['id', 'email', 'firstName', 'lastName'],
          },
        ],
        where: {
          id: { [Op.ne]: newNotification.MemberId },
          [Op.and]: {
            RoleId: {
              [Op.notIn]: [1, 2],
            },
          },
        },
      },
    ],
    attributes: ['id'],
  });

  const notificationData = {
    ...history,
    memberData: personData,
    adminData,
    firstName: loginUser.firstName,
    profilePic: loginUser.profilePic,
    createdAt: new Date(),
    ProjectId,
    projectName: projectDetails.projectName,
  };

  await notificationHelper.createDeliveryPersonNotification(
    adminData,
    [],
    projectDetails,
    newNotification,
    DeliveryPersonNotification,
    history.MemberId,
    loginUser,
    3,
    'created a',
    'Delivery Request',
    `delivery Booking (${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
    newDeliverData.id,
  );

  const checkMemberNotification = await NotificationPreference.findAll({
    where: {
      ProjectId,
      isDeleted: false,
    },
    attributes: [
      'id',
      'MemberId',
      'ProjectId',
      'ParentCompanyId',
      'NotificationPreferenceItemId',
      'instant',
      'dailyDigest',
    ],
    include: [
      {
        association: 'NotificationPreferenceItem',
        where: {
          id: 3,
          isDeleted: false,
        },
        attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
      },
    ],
  });

  notificationData.notificationPreference = checkMemberNotification;
  await pushNotification.sendDeviceToken(notificationData, 3, ProjectId);
}

async function processNDRRecord(row, memberDetails, ProjectId, loginUser, inputData, projectDetails) {
  if (!row.description) return null;

  const id = await getDeliveryId(ProjectId);
  const newDeliverData = await createDeliveryRequest(row, memberDetails, ProjectId, id, inputData);
  const history = await createDeliveryHistory(newDeliverData, memberDetails, loginUser);

  const updateParam = {
    DeliveryId: newDeliverData.id,
    DeliveryCode: newDeliverData.DeliveryId,
    ProjectId,
  };

  await handleCompanyAssociation(row, updateParam, inputData);
  await handleGateAssociation(row, updateParam, ProjectId);
  await handleEquipmentAssociation(row, updateParam, ProjectId);
  await handleLocationAssociation(row, updateParam, ProjectId);
  await handleDefineWorkAssociation(row, updateParam, ProjectId);
  const persons = await handleResponsiblePerson(row, updateParam, ProjectId);

  const createdNDR = await DeliveryRequest.getNDRData({ id: newDeliverData.id });
  if (isValidNDR(createdNDR)) {
    await updateNDRStatus(createdNDR, row);
    await handleNotifications(history, ProjectId, persons, loginUser, projectDetails, newDeliverData);
  }

  return createdNDR;
}

function isValidNDR(createdNDR) {
  return (
    createdNDR?.description &&
    createdNDR?.deliveryStart &&
    createdNDR?.deliveryEnd &&
    createdNDR?.LocationId &&
    createdNDR?.memberDetails?.length > 0 &&
    createdNDR?.companyDetails?.length > 0 &&
    createdNDR?.defineWorkDetails?.length > 0 &&
    createdNDR?.gateDetails?.length > 0 &&
    createdNDR?.equipmentDetails?.length > 0 &&
    createdNDR?.escort !== null
  );
}

async function updateNDRStatus(createdNDR, row) {
  const equipmentData = await Equipments.findOne({
    where: { id: createdNDR.equipmentDetails[0].EquipmentId },
    include: [
      {
        association: 'PresetEquipmentType',
        attributes: ['isCraneType'],
      },
    ],
  });

  if (equipmentData?.PresetEquipmentType?.isCraneType && row.picking_from && row.picking_to) {
    await DeliveryRequest.update(
      { isAllDetailsFilled: false, isQueued: true },
      { where: { id: createdNDR.id } },
    );
  } else {
    await DeliveryRequest.update(
      { isAllDetailsFilled: true, isQueued: false },
      { where: { id: createdNDR.id } },
    );
  }
}

parentPort.on('message', async (message) => {
  const { projectDetails, loginUser, ndrRecords, ProjectId, inputData } = parse(message);
  await deliveryService.getDynamicModel(inputData);

  for (const [i, element] of ndrRecords.entries()) {
    const row = await processRowData(element);
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId,
      isActive: true,
      isDeleted: false,
    });

    if (memberDetails) {
      await processNDRRecord(row, memberDetails, ProjectId, loginUser, inputData, projectDetails);
    }

    if (ndrRecords.length - 1 === i) {
      console.log('success');
    }
  }

  parentPort.postMessage('success');
});

// Export functions for testing
module.exports = {
  processRowData,
  getDeliveryId,
  calculateDeliveryDates,
  createDeliveryRequest,
  createDeliveryHistory,
  handleCompanyAssociation,
  handleGateAssociation,
  handleEquipmentAssociation,
  handleCraneEquipment,
  handleLocationAssociation,
  handleDefineWorkAssociation,
  handleResponsiblePerson,
  markDeliveryAsDeleted,
  handleNotifications,
  processNDRRecord,
  isValidNDR,
  updateNDRStatus,
};
