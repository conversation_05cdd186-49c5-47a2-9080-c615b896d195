// Mock all dependencies first
jest.mock('../../models', () => ({
    Sequelize: {
        and: jest.fn(() => ({})),
        Op: {
            ne: 'ne',
            in: 'in',
        },
    },
    Enterprise: {
        findOne: jest.fn(),
    },
}));

jest.mock('../../helpers/domainHelper', () => ({
    returnProjectModel: jest.fn(),
    getDynamicModel: jest.fn(),
}));

jest.mock('../craneRequestService', () => ({
    getVoidRequest: jest.fn(),
}));

jest.mock('../concreteRequestService', () => ({
    listConcreteRequest: jest.fn(),
}));

const voidService = require('../voidService');
const { Sequelize, Enterprise } = require('../../models');
const helper = require('../../helpers/domainHelper');
const craneRequestService = require('../craneRequestService');
const concreteRequestService = require('../concreteRequestService');

describe('voidService - Comprehensive Tests for 90% Coverage', () => {
    let mockDynamicModels;
    let mockInputData;
    let mockDone;
    let mockUser;
    let mockMember;
    let mockDeliveryRequest;

    beforeEach(() => {
        jest.clearAllMocks();

        // Create comprehensive dynamic model mocks
        mockDynamicModels = {
            VoidList: {
                findOne: jest.fn(),
                findAll: jest.fn(),
                createInstance: jest.fn(),
                destroy: jest.fn(),
            },
            Member: {
                findOne: jest.fn(),
            },
            User: {
                findOne: jest.fn(),
            },
            DeliveryRequest: {
                findOne: jest.fn(),
            },
            CraneRequest: {
                findOne: jest.fn(),
            },
            ConcreteRequest: {
                findOne: jest.fn(),
            },
            DeliverHistory: {
                createInstance: jest.fn(),
            },
            CraneRequestHistory: {
                createInstance: jest.fn(),
            },
            ConcreteRequestHistory: {
                createInstance: jest.fn(),
            },
            InspectionRequest: {
                findOne: jest.fn(),
            },
            InspectionHistory: {
                createInstance: jest.fn(),
            },
        };

        // Mock helper functions to return our dynamic models
        helper.getDynamicModel.mockResolvedValue(mockDynamicModels);
        helper.returnProjectModel.mockResolvedValue({
            Member: mockDynamicModels.Member,
            User: mockDynamicModels.User,
        });

        // Mock Enterprise
        Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });

        // Mock Sequelize
        Sequelize.and.mockReturnValue({});

        // Use the testing method to inject dependencies
        voidService._setModelsForTesting(mockDynamicModels);

        // Setup test data
        mockUser = {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            domainName: 'testdomain',
        };

        mockMember = {
            id: 1,
            UserId: 1,
            ProjectId: 1,
            isDeleted: false,
        };

        mockDeliveryRequest = {
            id: 1,
            description: 'Test Delivery Request',
        };

        mockInputData = {
            user: mockUser,
            body: {
                ProjectId: 1,
                DeliveryRequestId: 1,
                ParentCompanyId: 1,
            },
        };

        mockDone = jest.fn();

        // Setup default successful mocks
        mockDynamicModels.Member.findOne.mockResolvedValue(mockMember);
        mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue(mockDeliveryRequest);
        mockDynamicModels.VoidList.findOne.mockResolvedValue(null);
        mockDynamicModels.VoidList.createInstance.mockResolvedValue({ id: 1 });
        mockDynamicModels.DeliverHistory.createInstance.mockResolvedValue({ id: 1 });
    });

    describe('createVoidList', () => {
        it('should successfully create a void list entry', async () => {
            await voidService.createVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalled();
            expect(mockDynamicModels.DeliverHistory.createInstance).toHaveBeenCalled();
        });

        it('should return error when member does not exist', async () => {
            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.createVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project Id/Member Does not exist.' });
        });

        it('should return error when delivery request does not exist', async () => {
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue(null);

            await voidService.createVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking Id Does not exist.' });
        });

        it('should return error when void already exists', async () => {
            mockDynamicModels.VoidList.findOne.mockResolvedValue({ id: 1 });

            await voidService.createVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking already is in void list.' });
        });

        it('should handle case when VoidList.createInstance returns null', async () => {
            mockDynamicModels.VoidList.createInstance.mockResolvedValue(null);

            await voidService.createVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, false);
            expect(mockDynamicModels.DeliverHistory.createInstance).not.toHaveBeenCalled();
        });

        it('should handle errors', async () => {
            mockDynamicModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await voidService.createVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createCraneRequestVoid', () => {
        beforeEach(() => {
            mockInputData.body.CraneRequestId = 1;
            mockDynamicModels.CraneRequest.findOne.mockResolvedValue({ id: 1, description: 'Test Crane' });
            mockDynamicModels.CraneRequestHistory.createInstance.mockResolvedValue({ id: 1 });
        });

        it('should successfully create a crane request void', async () => {
            await voidService.createCraneRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalled();
            expect(mockDynamicModels.CraneRequestHistory.createInstance).toHaveBeenCalled();
        });

        it('should return error when member does not exist', async () => {
            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.createCraneRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project Id/Member Does not exist.' });
        });

        it('should return error when crane request does not exist', async () => {
            mockDynamicModels.CraneRequest.findOne.mockResolvedValue(null);

            await voidService.createCraneRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Crane Booking Id Does not exist.' });
        });

        it('should return error when crane request already in void list', async () => {
            mockDynamicModels.VoidList.findOne.mockResolvedValue({ id: 1 });

            await voidService.createCraneRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Crane Booking already is in void list.' });
        });

        it('should handle case when VoidList.createInstance returns null', async () => {
            mockDynamicModels.VoidList.createInstance.mockResolvedValue(null);

            await voidService.createCraneRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, false);
            expect(mockDynamicModels.CraneRequestHistory.createInstance).not.toHaveBeenCalled();
        });

        it('should handle errors', async () => {
            mockDynamicModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await voidService.createCraneRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createConcreteRequestVoid', () => {
        beforeEach(() => {
            mockInputData.body.ConcreteRequestId = 1;
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue({ id: 1, description: 'Test Concrete' });
            mockDynamicModels.ConcreteRequestHistory.createInstance.mockResolvedValue({ id: 1 });
        });

        it('should successfully create a concrete request void', async () => {
            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalled();
            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).toHaveBeenCalled();
        });

        it('should return error when member does not exist', async () => {
            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project Id/Member Does not exist.' });
        });

        it('should return error when concrete request does not exist', async () => {
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue(null);

            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Concrete Booking Id Does not exist.' });
        });

        it('should return error when concrete request already in void list', async () => {
            mockDynamicModels.VoidList.findOne.mockResolvedValue({ id: 1 });

            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Concrete Booking already is in void list.' });
        });

        it('should handle case when VoidList.createInstance returns null', async () => {
            mockDynamicModels.VoidList.createInstance.mockResolvedValue(null);

            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, false);
            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).not.toHaveBeenCalled();
        });

        it('should handle errors', async () => {
            mockDynamicModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createInspectionVoidList', () => {
        beforeEach(() => {
            mockInputData.body.InspectionRequestId = 1;
            mockDynamicModels.InspectionRequest.findOne.mockResolvedValue({ id: 1, description: 'Test Inspection' });
            mockDynamicModels.InspectionHistory.createInstance.mockResolvedValue({ id: 1 });
        });

        it('should successfully create an inspection void list entry', async () => {
            await voidService.createInspectionVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith({ id: 1 }, false);
            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalled();
            expect(mockDynamicModels.InspectionHistory.createInstance).toHaveBeenCalled();
        });

        it('should return error when member does not exist', async () => {
            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.createInspectionVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project Id/Member Does not exist.' });
        });

        it('should return error when inspection request does not exist', async () => {
            mockDynamicModels.InspectionRequest.findOne.mockResolvedValue(null);

            await voidService.createInspectionVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Inspection Booking Id Does not exist.' });
        });

        it('should return error when inspection request already in void list', async () => {
            mockDynamicModels.VoidList.findOne.mockResolvedValue({ id: 1 });

            await voidService.createInspectionVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Inspection Booking already is in void list.' });
        });

        it('should handle case when VoidList.createInstance returns null', async () => {
            mockDynamicModels.VoidList.createInstance.mockResolvedValue(null);

            await voidService.createInspectionVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, false);
            expect(mockDynamicModels.InspectionHistory.createInstance).not.toHaveBeenCalled();
        });

        it('should handle errors', async () => {
            mockDynamicModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await voidService.createInspectionVoidList(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('getDynamicModel', () => {
        it('should handle domain name resolution with valid enterprise', async () => {
            const mockInputWithDomain = {
                user: { domainName: 'TestDomain', email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };

            Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });
            mockDynamicModels.User.findOne.mockResolvedValue({ id: 2, email: '<EMAIL>', firstName: 'Updated' });

            await voidService.getDynamicModel(mockInputWithDomain);

            expect(Enterprise.findOne).toHaveBeenCalledWith({
                where: { name: 'testdomain' }
            });
            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
            expect(mockInputWithDomain.user).toEqual({ id: 2, email: '<EMAIL>', firstName: 'Updated' });
        });

        it('should handle case when domain name enterprise not found', async () => {
            const mockInputWithDomain = {
                user: { domainName: 'NonExistentDomain', email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };

            Enterprise.findOne.mockResolvedValue(null);

            await voidService.getDynamicModel(mockInputWithDomain);

            expect(helper.getDynamicModel).toHaveBeenCalled();
        });

        it('should handle case with no domain name but valid ParentCompanyId', async () => {
            const mockInputNoDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };
            const mockPublicUser = { id: 1, email: '<EMAIL>' };
            const mockPublicMember = { id: 1, isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'testcompany', status: 'completed' };

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(mockPublicMember) },
                User: { findOne: jest.fn().mockResolvedValue(mockPublicUser) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            await voidService.getDynamicModel(mockInputNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('testcompany');
        });

        it('should handle case with undefined ParentCompanyId', async () => {
            const mockInputUndefined = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 'undefined' }
            };

            await voidService.getDynamicModel(mockInputUndefined);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle case when user not found in public database', async () => {
            const mockInputNoDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };
            const mockEnterprise = { name: 'fallbackcompany', status: 'completed' };

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn() },
                User: { findOne: jest.fn().mockResolvedValue(null) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            await voidService.getDynamicModel(mockInputNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('fallbackcompany');
        });

        it('should handle case when member not found for user', async () => {
            const mockInputNoDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };
            const mockPublicUser = { id: 1, email: '<EMAIL>' };
            const mockEnterprise = { name: 'fallbackcompany', status: 'completed' };

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(null) },
                User: { findOne: jest.fn().mockResolvedValue(mockPublicUser) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            await voidService.getDynamicModel(mockInputNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('fallbackcompany');
        });

        it('should handle case when member is not account', async () => {
            const mockInputNoDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };
            const mockPublicUser = { id: 1, email: '<EMAIL>' };
            const mockPublicMember = { id: 1, isAccount: false, EnterpriseId: 1 };
            const mockEnterprise = { name: 'fallbackcompany', status: 'completed' };

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(mockPublicMember) },
                User: { findOne: jest.fn().mockResolvedValue(mockPublicUser) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            await voidService.getDynamicModel(mockInputNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('fallbackcompany');
        });

        it('should handle case when enterprise not found for member', async () => {
            const mockInputNoDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };
            const mockPublicUser = { id: 1, email: '<EMAIL>' };
            const mockPublicMember = { id: 1, isAccount: true, EnterpriseId: 1 };

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(mockPublicMember) },
                User: { findOne: jest.fn().mockResolvedValue(mockPublicUser) }
            });
            Enterprise.findOne.mockResolvedValue(null);

            await voidService.getDynamicModel(mockInputNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle case with incomplete enterprise status', async () => {
            const mockInputNoDomain = {
                user: { email: '<EMAIL>' },
                body: { ParentCompanyId: 1 }
            };
            const mockPublicUser = { id: 1, email: '<EMAIL>' };
            const mockPublicMember = { id: 1, isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'testcompany', status: 'pending' };

            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(mockPublicMember) },
                User: { findOne: jest.fn().mockResolvedValue(mockPublicUser) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            await voidService.getDynamicModel(mockInputNoDomain);

            expect(helper.getDynamicModel).toHaveBeenCalledWith('');
        });

        it('should handle null email', async () => {
            const mockInputNullEmail = {
                user: { email: null },
                body: { ParentCompanyId: 1 }
            };

            await voidService.getDynamicModel(mockInputNullEmail);

            expect(helper.getDynamicModel).toHaveBeenCalled();
        });
    });

    describe('removeVoidList', () => {
        it('should handle selective removal with valid void', async () => {
            const mockReqData = {
                id: [1],
                ProjectId: 1,
                isSelectAll: false
            };
            const mockVoid = {
                id: 1,
                DeliveryRequestId: 1,
                ProjectId: 1,
                CraneRequestId: null,
                ConcreteRequestId: null
            };

            mockDynamicModels.VoidList.findOne.mockResolvedValue(mockVoid);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(1);

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDynamicModels.VoidList.destroy).toHaveBeenCalledWith({
                where: { id: { in: [1] } }
            });
            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should handle selective removal when void not found', async () => {
            const mockReqData = {
                id: [999],
                ProjectId: 1,
                isSelectAll: false
            };

            mockDynamicModels.VoidList.findOne.mockResolvedValue(null);

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Some Void not available' });
        });

        it('should handle select all removal with empty void list', async () => {
            const mockReqData = {
                ProjectId: 1,
                isSelectAll: true
            };

            mockDynamicModels.VoidList.findAll.mockResolvedValue([]);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(0);

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDynamicModels.VoidList.destroy).toHaveBeenCalledWith({
                where: { ProjectId: 1 }
            });
            expect(mockDone).toHaveBeenCalledWith(0, false);
        });

        it('should handle crane request void in selective removal', async () => {
            const mockReqData = {
                id: [1],
                ProjectId: 1,
                isSelectAll: false
            };
            const mockVoid = {
                id: 1,
                CraneRequestId: 1,
                ProjectId: 1,
                DeliveryRequestId: null,
                ConcreteRequestId: null
            };
            const mockCraneRequest = { id: 1, description: 'Test Crane' };

            mockDynamicModels.VoidList.findOne.mockResolvedValue(mockVoid);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(1);
            mockDynamicModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);
            mockDynamicModels.CraneRequestHistory.createInstance.mockResolvedValue({ id: 1 });

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDynamicModels.CraneRequestHistory.createInstance).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should handle concrete request void in selective removal', async () => {
            const mockReqData = {
                id: [1],
                ProjectId: 1,
                isSelectAll: false
            };
            const mockVoid = {
                id: 1,
                ConcreteRequestId: 1,
                ProjectId: 1,
                DeliveryRequestId: null,
                CraneRequestId: null
            };
            const mockConcreteRequest = { id: 1, description: 'Test Concrete' };

            mockDynamicModels.VoidList.findOne.mockResolvedValue(mockVoid);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(1);
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue(mockConcreteRequest);
            mockDynamicModels.ConcreteRequestHistory.createInstance.mockResolvedValue({ id: 1 });

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should handle member not found in removeVoidList', async () => {
            const mockReqData = {
                id: [1],
                ProjectId: 1,
                isSelectAll: false
            };

            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Void not available' });
        });

        it('should handle errors in removeVoidList', async () => {
            const mockReqData = {
                id: [1],
                ProjectId: 1,
                isSelectAll: false
            };

            mockDynamicModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle mixed request types in select all removal', async () => {
            const mockReqData = {
                ProjectId: 1,
                isSelectAll: true
            };
            const mockVoidedList = [
                { DeliveryRequestId: 1, CraneRequestId: null, ConcreteRequestId: null, ProjectId: 1 },
                { DeliveryRequestId: null, CraneRequestId: 1, ConcreteRequestId: null, ProjectId: 1 },
                { DeliveryRequestId: null, CraneRequestId: null, ConcreteRequestId: 1, ProjectId: 1 }
            ];

            mockDynamicModels.VoidList.findAll.mockResolvedValue(mockVoidedList);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(3);

            // Mock all request types
            mockDynamicModels.CraneRequest.findOne.mockResolvedValue({ id: 1, description: 'Test Crane' });
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue({ id: 1, description: 'Test Concrete' });

            await voidService.removeVoidList({ reqData: mockReqData, loginUser: mockUser }, mockDone);

            expect(mockDynamicModels.DeliverHistory.createInstance).toHaveBeenCalled();
            expect(mockDynamicModels.CraneRequestHistory.createInstance).toHaveBeenCalled();
            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(3, false);
        });
    });

    describe('getVoidList', () => {
        it('should handle concrete request filter', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    statusFilter: 'Tentative',
                    sort: 'ASC',
                    sortByField: 'id',
                },
                user: mockUser
            };
            const mockResponse = {
                rows: [{ id: 1 }, { id: 2 }]
            };

            concreteRequestService.listConcreteRequest.mockImplementation((req, callback) => {
                callback(mockResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle crane request filter', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 0,
                    sort: 'DESC',
                    sortByField: 'id',
                },
                user: mockUser
            };
            const mockCraneResponse = [{ id: 1 }, { id: 2 }];
            const mockConcreteResponse = {
                rows: [{ id: 3 }, { id: 4 }]
            };

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(mockCraneResponse, false);
            });
            concreteRequestService.listConcreteRequest.mockImplementation((req, callback) => {
                callback(mockConcreteResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 4,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle concrete request with location filter', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    locationFilter: 'Test Location',
                    sort: 'ASC',
                    sortByField: 'id'
                },
                user: mockUser
            };
            const mockResponse = {
                rows: [{ id: 1 }, { id: 2 }]
            };

            concreteRequestService.listConcreteRequest.mockImplementation((req, callback) => {
                callback(mockResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(concreteRequestService.listConcreteRequest).toHaveBeenCalled();
            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle concrete request with mixDesignFilter', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    mixDesignFilter: 'Test Mix',
                    sort: 'DESC',
                    sortByField: 'createdAt'
                },
                user: mockUser
            };
            const mockResponse = {
                rows: [{ id: 1, createdAt: '2023-01-01' }, { id: 2, createdAt: '2023-01-02' }]
            };

            concreteRequestService.listConcreteRequest.mockImplementation((req, callback) => {
                callback(mockResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockReq.body.concreteSupplierFilter).toBe(mockReq.body.companyFilter);
            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle crane request with gate filter', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 1,
                    gateFilter: 'Gate A',
                    sort: 'ASC',
                    sortByField: 'id'
                },
                user: mockUser
            };
            const mockCraneResponse = [{ id: 1 }, { id: 2 }];

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(mockCraneResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle error in concrete request service', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    statusFilter: 'Tentative'
                },
                user: mockUser
            };

            concreteRequestService.listConcreteRequest.mockImplementation((req, callback) => {
                callback(null, new Error('Concrete service error'));
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle error in crane request service', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 0
                },
                user: mockUser
            };

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(null, new Error('Crane service error'));
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle general error in getVoidList', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {},
                user: mockUser
            };

            // Mock getDynamicModel to throw an error
            helper.getDynamicModel.mockRejectedValue(new Error('Dynamic model error'));

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createMultipleVoidList', () => {
        it('should successfully create multiple void list entries', async () => {
            const mockVoidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: [1, 2]
            };

            mockDynamicModels.VoidList.createInstance.mockResolvedValue({ id: 1 });

            await voidService.createMultipleVoidList(
                { voidData: mockVoidData, loginUser: mockUser },
                mockDone
            );

            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalledTimes(2);
        });

        it('should handle empty deliveryRequestIds array', async () => {
            const mockVoidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: []
            };

            await voidService.createMultipleVoidList(
                { voidData: mockVoidData, loginUser: mockUser },
                mockDone
            );

            expect(mockDynamicModels.VoidList.createInstance).not.toHaveBeenCalled();
        });

        it('should handle null/undefined deliveryRequestIds', async () => {
            const mockVoidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: [null, undefined, 1]
            };

            mockDynamicModels.VoidList.createInstance.mockResolvedValue({ id: 1 });

            await voidService.createMultipleVoidList(
                { voidData: mockVoidData, loginUser: mockUser },
                mockDone
            );

            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalledTimes(1); // Only for valid ID
        });

        it('should stop on first error in multiple creation', async () => {
            const mockVoidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: [1, 2]
            };

            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValueOnce(null); // First request not found

            await voidService.createMultipleVoidList(
                { voidData: mockVoidData, loginUser: mockUser },
                mockDone
            );

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking Id Does not exist.' });
            expect(mockDynamicModels.VoidList.createInstance).not.toHaveBeenCalled();
        });

        it('should stop when delivery request already in void list', async () => {
            const mockVoidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: [1, 2]
            };

            mockDynamicModels.VoidList.findOne.mockResolvedValueOnce({ id: 1 }); // Already exists

            await voidService.createMultipleVoidList(
                { voidData: mockVoidData, loginUser: mockUser },
                mockDone
            );

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking already is in void list.' });
        });

        it('should handle errors in createMultipleVoidList', async () => {
            const mockVoidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: [1]
            };

            mockDynamicModels.Member.findOne.mockRejectedValue(new Error('Database error'));

            await voidService.createMultipleVoidList(
                { voidData: mockVoidData, loginUser: mockUser },
                mockDone
            );

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('History creation methods', () => {
        it('should create delivery request void history', async () => {
            const mockExistVoid = {
                ProjectId: 1,
                DeliveryRequestId: 1
            };

            await voidService.deliveryRequestVoidHistory(mockExistVoid, mockMember, mockUser);

            expect(mockDynamicModels.DeliverHistory.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'restore',
                    description: expect.stringContaining('Restored the Delivery Booking')
                })
            );
        });

        it('should create crane request void history', async () => {
            const mockExistVoid = {
                ProjectId: 1,
                CraneRequestId: 1
            };
            const mockCraneRequest = { id: 1, description: 'Test Crane' };

            mockDynamicModels.CraneRequest.findOne.mockResolvedValue(mockCraneRequest);

            await voidService.craneRequestVoidHistory(mockExistVoid, mockMember, mockUser);

            expect(mockDynamicModels.CraneRequestHistory.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'restore',
                    description: expect.stringContaining('Restored the Crane Booking')
                })
            );
        });

        it('should create concrete request void history', async () => {
            const mockExistVoid = {
                ProjectId: 1,
                ConcreteRequestId: 1
            };
            const mockConcreteRequest = { id: 1, description: 'Test Concrete' };

            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue(mockConcreteRequest);

            await voidService.concreteRequestVoidHistory(mockExistVoid, mockMember, mockUser);

            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'restore',
                    description: expect.stringContaining('Restored the Concrete Booking')
                })
            );
        });
    });

    describe('Utility methods', () => {
        it('should correctly identify delivery request', () => {
            const element = { DeliveryRequestId: 1, CraneRequestId: null, ConcreteRequestId: null };
            expect(voidService.isDeliveryRequest(element)).toBe(true);
        });

        it('should correctly identify crane request', () => {
            const element = { DeliveryRequestId: null, CraneRequestId: 1, ConcreteRequestId: null };
            expect(voidService.isCraneRequest(element)).toBe(true);
        });

        it('should correctly identify concrete request', () => {
            const element = { DeliveryRequestId: null, CraneRequestId: null, ConcreteRequestId: 1 };
            expect(voidService.isConcreteRequest(element)).toBe(true);
        });

        it('should return false for mixed request types', () => {
            const element = { DeliveryRequestId: 1, CraneRequestId: 1, ConcreteRequestId: null };
            expect(voidService.isDeliveryRequest(element)).toBe(false);
            expect(voidService.isCraneRequest(element)).toBe(false);
        });

        it('should handle findMember utility', async () => {
            mockDynamicModels.Member.findOne.mockResolvedValue(mockMember);

            const result = await voidService.findMember(1, 1);

            expect(result).toEqual(mockMember);
            expect(mockDynamicModels.Member.findOne).toHaveBeenCalledWith({
                where: Sequelize.and({
                    UserId: 1,
                    ProjectId: 1,
                    isDeleted: false
                })
            });
        });

        it('should handle processDeliveryRequest with success', async () => {
            mockDynamicModels.VoidList.createInstance.mockResolvedValue({ id: 1 });

            const result = await voidService.processDeliveryRequest(1, { ProjectId: 1, ParentCompanyId: 1 }, mockMember);

            expect(result).toEqual({ success: true });
        });

        it('should handle processDeliveryRequest with delivery not found', async () => {
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue(null);

            const result = await voidService.processDeliveryRequest(1, { ProjectId: 1, ParentCompanyId: 1 }, mockMember);

            expect(result).toEqual({ error: 'Delivery Booking Id Does not exist.' });
        });

        it('should handle processDeliveryRequest with existing void', async () => {
            mockDynamicModels.VoidList.findOne.mockResolvedValue({ id: 1 });

            const result = await voidService.processDeliveryRequest(1, { ProjectId: 1, ParentCompanyId: 1 }, mockMember);

            expect(result).toEqual({ error: 'Delivery Booking already is in void list.' });
        });
    });

    describe('Additional helper methods for coverage', () => {
        it('should handle returnProjectModel', async () => {
            await voidService.returnProjectModel();
            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle getParentCompanyId from body', () => {
            const inputData = { body: { ParentCompanyId: 123 }, params: {} };
            const result = voidService.getParentCompanyId(inputData);
            expect(result).toBe(123);
        });

        it('should handle getParentCompanyId from params', () => {
            const inputData = { body: {}, params: { ParentCompanyId: 456 } };
            const result = voidService.getParentCompanyId(inputData);
            expect(result).toBe(456);
        });

        it('should handle isValidParentCompanyId with valid ID', () => {
            expect(voidService.isValidParentCompanyId(123)).toBe(true);
        });

        it('should handle isValidParentCompanyId with undefined', () => {
            expect(voidService.isValidParentCompanyId(undefined)).toBe(false);
        });

        it('should handle isValidParentCompanyId with string undefined', () => {
            expect(voidService.isValidParentCompanyId('undefined')).toBe(false);
        });

        it('should handle findUserByEmail with null email', async () => {
            const result = await voidService.findUserByEmail(null);
            expect(result).toBe(null);
        });

        it('should handle findUserByEmail with valid email', async () => {
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockPublicUser = { findOne: jest.fn().mockResolvedValue(mockUser) };

            // Set up the public user mock
            await voidService.returnProjectModel();
            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockDynamicModels.Member
            });
            await voidService.returnProjectModel();

            const result = await voidService.findUserByEmail('<EMAIL>');
            expect(result).toEqual(mockUser);
        });

        it('should handle updateModelReferences', () => {
            const mockModelObj = {
                VoidList: { test: 'voidlist' },
                Member: { test: 'member' },
                DeliveryRequest: { test: 'delivery' },
                User: { test: 'user' }
            };

            voidService.updateModelReferences(mockModelObj);
            // This method updates internal references, no return value to test
            expect(true).toBe(true); // Just to have an assertion
        });

        it('should handle updateUserIfNeeded with domain name', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            const mockNewUser = { id: 2, email: '<EMAIL>' };
            mockDynamicModels.User.findOne.mockResolvedValue(mockNewUser);

            await voidService.updateUserIfNeeded(inputData, 'testdomain');

            expect(inputData.user).toEqual(mockNewUser);
        });

        it('should handle updateUserIfNeeded without domain name', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            const originalUser = inputData.user;

            await voidService.updateUserIfNeeded(inputData, '');

            expect(inputData.user).toEqual(originalUser);
        });

        it('should handle getDomainFromParentCompany with valid user and member', async () => {
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockMember = { id: 1, isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'TestCompany', status: 'completed' };

            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn().mockResolvedValue(mockUser) },
                Member: { findOne: jest.fn().mockResolvedValue(mockMember) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await voidService.getDomainFromParentCompany(1, { user: { email: '<EMAIL>' } });
            expect(result).toBe('testcompany');
        });

        it('should handle getDomainFromMemberEnterprise with null enterprise', async () => {
            const mockMember = { EnterpriseId: 1 };
            Enterprise.findOne.mockResolvedValue(null);

            const result = await voidService.getDomainFromMemberEnterprise(mockMember);
            expect(result).toBe('');
        });

        it('should handle getDomainFromEnterprise with null enterprise', async () => {
            Enterprise.findOne.mockResolvedValue(null);

            const result = await voidService.getDomainFromEnterprise(1);
            expect(result).toBe('');
        });

        it('should handle findMemberByUserId with valid user', async () => {
            const mockMember = { id: 1, UserId: 1 };
            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(mockMember) },
                User: mockDynamicModels.User
            });
            await voidService.returnProjectModel();

            const result = await voidService.findMemberByUserId(1);
            expect(result).toEqual(mockMember);
        });

        it('should handle resolveDomainName with no initial domain and invalid ParentCompanyId', async () => {
            const result = await voidService.resolveDomainName(null, 'undefined', {});
            expect(result).toBe('');
        });

        it('should handle processVoidedElement with inspection request', async () => {
            const element = {
                DeliveryRequestId: null,
                CraneRequestId: null,
                ConcreteRequestId: null,
                InspectionRequestId: 1
            };

            // Mock the inspection history creation
            mockDynamicModels.InspectionHistory = { createInstance: jest.fn() };

            await voidService.processVoidedElement(element, mockMember, mockUser);

            // Since none of the conditions match, no history should be created
            expect(mockDynamicModels.DeliverHistory.createInstance).not.toHaveBeenCalled();
        });

        it('should handle findVoidById with valid ID', async () => {
            const mockVoid = { id: 1 };
            mockDynamicModels.VoidList.findOne.mockResolvedValue(mockVoid);

            const result = await voidService.findVoidById([1]);
            expect(result).toEqual(mockVoid);
        });

        it('should handle findVoidedList', async () => {
            const mockVoids = [{ id: 1 }, { id: 2 }];
            mockDynamicModels.VoidList.findAll.mockResolvedValue(mockVoids);

            const result = await voidService.findVoidedList(1);
            expect(result).toEqual(mockVoids);
        });
    });

    describe('Edge cases and error handling', () => {
        it('should handle createVoidList with isDeliveryRequest flag', async () => {
            mockInputData.body.isDeliveryRequest = false;

            await voidService.createVoidList(mockInputData, mockDone);

            // Should still work the same way
            expect(mockDone).toHaveBeenCalled();
        });

        it('should handle createConcreteRequestVoid with isDeleted filter', async () => {
            mockInputData.body.ConcreteRequestId = 1;
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue({
                id: 1,
                description: 'Test Concrete',
                isDeleted: false
            });
            mockDynamicModels.ConcreteRequestHistory.createInstance.mockResolvedValue({ id: 1 });

            await voidService.createConcreteRequestVoid(mockInputData, mockDone);

            expect(mockDynamicModels.ConcreteRequest.findOne).toHaveBeenCalledWith({
                where: { id: 1, isDeleted: false }
            });
        });

        it('should handle getVoidList with equipment filter', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 1,
                    equipmentFilter: 'Crane A',
                    sort: 'ASC',
                    sortByField: 'id'
                },
                user: mockUser
            };
            const mockCraneResponse = [{ id: 1 }, { id: 2 }];

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(mockCraneResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle getVoidList with status Declined', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 1,
                    statusFilter: 'Declined',
                    sort: 'ASC',
                    sortByField: 'id'
                },
                user: mockUser
            };
            const mockCraneResponse = [{ id: 1 }, { id: 2 }];

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(mockCraneResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle getVoidList with status Delivered', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 1,
                    status: 'Delivered',
                    sort: 'ASC',
                    sortByField: 'id'
                },
                user: mockUser
            };
            const mockCraneResponse = [{ id: 1 }, { id: 2 }];

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(mockCraneResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });

        it('should handle getVoidList with status Pending', async () => {
            const mockReq = {
                params: { pageNo: 1, pageSize: 10 },
                body: {
                    filterCount: 1,
                    status: 'Pending',
                    sort: 'ASC',
                    sortByField: 'id'
                },
                user: mockUser
            };
            const mockCraneResponse = [{ id: 1 }, { id: 2 }];

            craneRequestService.getVoidRequest.mockImplementation((req, callback) => {
                callback(mockCraneResponse, false);
            });

            await voidService.getVoidList(mockReq, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                expect.objectContaining({
                    count: 2,
                    rows: expect.any(Array)
                }),
                false
            );
        });
    });

    describe('removeVoidList', () => {
        it('should handle selective removal with valid void', async () => {
            const reqData = { id: [1], ProjectId: 1, isSelectAll: false };
            const inputData = { reqData, loginUser: mockUser };
            const mockVoid = { id: 1, DeliveryRequestId: 1, ProjectId: 1 };

            mockDynamicModels.VoidList.findOne.mockResolvedValue(mockVoid);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(1);
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.removeVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should handle select all removal', async () => {
            const reqData = { ProjectId: 1, isSelectAll: true };
            const inputData = { reqData, loginUser: mockUser };
            const mockVoids = [{ id: 1, DeliveryRequestId: 1 }];

            mockDynamicModels.VoidList.findAll.mockResolvedValue(mockVoids);
            mockDynamicModels.VoidList.destroy.mockResolvedValue(1);
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.removeVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(1, false);
        });

        it('should return error when member not found', async () => {
            const reqData = { id: [1], ProjectId: 1, isSelectAll: false };
            const inputData = { reqData, loginUser: mockUser };
            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.removeVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Void not available' });
        });

        it('should return error when void not found', async () => {
            const reqData = { id: [1], ProjectId: 1, isSelectAll: false };
            const inputData = { reqData, loginUser: mockUser };
            mockDynamicModels.VoidList.findOne.mockResolvedValue(null);

            await voidService.removeVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Some Void not available' });
        });

        it('should handle errors', async () => {
            const reqData = { id: [1], ProjectId: 1, isSelectAll: false };
            const inputData = { reqData, loginUser: mockUser };

            helper.getDynamicModel.mockRejectedValue(new Error('Database error'));

            await voidService.removeVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('createMultipleVoidList', () => {
        it('should successfully create multiple void list entries', async () => {
            const voidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: [1, 2]
            };
            const inputData = { voidData, loginUser: mockUser };

            await voidService.createMultipleVoidList(inputData, mockDone);

            expect(mockDynamicModels.VoidList.createInstance).toHaveBeenCalledTimes(2);
        });

        it('should handle empty deliveryRequestIds array', async () => {
            const voidData = {
                ProjectId: 1,
                ParentCompanyId: 1,
                deliveryRequestIds: []
            };
            const inputData = { voidData, loginUser: mockUser };

            await voidService.createMultipleVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith([], false);
        });

        it('should handle member not found', async () => {
            const voidData = { ProjectId: 1, ParentCompanyId: 1, deliveryRequestIds: [1] };
            const inputData = { voidData, loginUser: mockUser };
            mockDynamicModels.Member.findOne.mockResolvedValue(null);

            await voidService.createMultipleVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Project Id/Member Does not exist.' });
        });

        it('should handle delivery request error', async () => {
            const voidData = { ProjectId: 1, ParentCompanyId: 1, deliveryRequestIds: [1] };
            const inputData = { voidData, loginUser: mockUser };
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue(null);

            await voidService.createMultipleVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, { message: 'Delivery Booking Id Does not exist.' });
        });

        it('should handle errors', async () => {
            const voidData = { ProjectId: 1, ParentCompanyId: 1, deliveryRequestIds: [1] };
            const inputData = { voidData, loginUser: mockUser };

            helper.getDynamicModel.mockRejectedValue(new Error('Database error'));

            await voidService.createMultipleVoidList(inputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, expect.any(Error));
        });
    });

    describe('Utility methods', () => {
        it('should correctly identify delivery request', () => {
            const element = { DeliveryRequestId: 1, CraneRequestId: null, ConcreteRequestId: null };
            expect(voidService.isDeliveryRequest(element)).toBe(true);
        });

        it('should correctly identify crane request', () => {
            const element = { DeliveryRequestId: null, CraneRequestId: 1, ConcreteRequestId: null };
            expect(voidService.isCraneRequest(element)).toBe(true);
        });

        it('should correctly identify concrete request', () => {
            const element = { DeliveryRequestId: null, CraneRequestId: null, ConcreteRequestId: 1 };
            expect(voidService.isConcreteRequest(element)).toBeTruthy();
        });

        it('should handle findMember utility', async () => {
            const result = await voidService.findMember(1, 1);
            expect(result).toEqual(mockMember);
        });

        it('should handle processDeliveryRequest with success', async () => {
            const result = await voidService.processDeliveryRequest(1, { ProjectId: 1, ParentCompanyId: 1 }, mockMember);
            expect(result).toEqual({ success: true });
        });

        it('should handle processDeliveryRequest with delivery not found', async () => {
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue(null);
            const result = await voidService.processDeliveryRequest(1, { ProjectId: 1, ParentCompanyId: 1 }, mockMember);
            expect(result).toEqual({ error: 'Delivery Booking Id Does not exist.' });
        });

        it('should handle processDeliveryRequest with existing void', async () => {
            mockDynamicModels.VoidList.findOne.mockResolvedValue({ id: 1 });
            const result = await voidService.processDeliveryRequest(1, { ProjectId: 1, ParentCompanyId: 1 }, mockMember);
            expect(result).toEqual({ error: 'Delivery Booking already is in void list.' });
        });
    });

    describe('Helper methods', () => {
        it('should handle returnProjectModel', async () => {
            await voidService.returnProjectModel();
            expect(helper.returnProjectModel).toHaveBeenCalled();
        });

        it('should handle getParentCompanyId from body', () => {
            const inputData = { body: { ParentCompanyId: 123 }, params: {} };
            const result = voidService.getParentCompanyId(inputData);
            expect(result).toBe(123);
        });

        it('should handle getParentCompanyId from params', () => {
            const inputData = { body: {}, params: { ParentCompanyId: 456 } };
            const result = voidService.getParentCompanyId(inputData);
            expect(result).toBe(456);
        });

        it('should handle isValidParentCompanyId', () => {
            expect(voidService.isValidParentCompanyId(123)).toBe(true);
            expect(voidService.isValidParentCompanyId(undefined)).toBe(false);
            expect(voidService.isValidParentCompanyId('undefined')).toBe(false);
        });

        it('should handle findUserByEmail with null email', async () => {
            const result = await voidService.findUserByEmail(null);
            expect(result).toBe(null);
        });

        it('should handle findUserByEmail with valid email', async () => {
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockPublicUser = { findOne: jest.fn().mockResolvedValue(mockUser) };

            helper.returnProjectModel.mockResolvedValue({
                User: mockPublicUser,
                Member: mockDynamicModels.Member
            });

            const result = await voidService.findUserByEmail('<EMAIL>');
            expect(result).toEqual(mockUser);
        });

        it('should handle updateModelReferences', () => {
            const mockModelObj = {
                VoidList: { test: 'voidlist' },
                Member: { test: 'member' },
                DeliveryRequest: { test: 'delivery' },
                User: { test: 'user' }
            };

            voidService.updateModelReferences(mockModelObj);
            expect(true).toBe(true); // Just to have an assertion
        });

        it('should handle updateUserIfNeeded with domain name', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            const mockNewUser = { id: 2, email: '<EMAIL>' };
            mockDynamicModels.User.findOne.mockResolvedValue(mockNewUser);

            await voidService.updateUserIfNeeded(inputData, 'testdomain');

            expect(inputData.user).toEqual(mockNewUser);
        });

        it('should handle updateUserIfNeeded without domain name', async () => {
            const inputData = { user: { email: '<EMAIL>' } };
            const originalUser = inputData.user;

            await voidService.updateUserIfNeeded(inputData, '');

            expect(inputData.user).toEqual(originalUser);
        });

        it('should handle resolveDomainName', async () => {
            const result = await voidService.resolveDomainName('testdomain', 1, {});
            expect(result).toBe('testdomain');
        });

        it('should handle getDomainFromParentCompany', async () => {
            const mockUser = { id: 1, email: '<EMAIL>' };
            const mockMember = { id: 1, isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'TestCompany', status: 'completed' };

            helper.returnProjectModel.mockResolvedValue({
                User: { findOne: jest.fn().mockResolvedValue(mockUser) },
                Member: { findOne: jest.fn().mockResolvedValue(mockMember) }
            });
            Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await voidService.getDomainFromParentCompany(1, { user: { email: '<EMAIL>' } });
            expect(result).toBe('testcompany');
        });

        it('should handle getDomainFromMemberEnterprise with null enterprise', async () => {
            const mockMember = { EnterpriseId: 1 };
            Enterprise.findOne.mockResolvedValue(null);

            const result = await voidService.getDomainFromMemberEnterprise(mockMember);
            expect(result).toBe('');
        });

        it('should handle getDomainFromEnterprise with null enterprise', async () => {
            Enterprise.findOne.mockResolvedValue(null);

            const result = await voidService.getDomainFromEnterprise(1);
            expect(result).toBe('');
        });

        it('should handle findMemberByUserId', async () => {
            const mockMember = { id: 1, UserId: 1 };
            helper.returnProjectModel.mockResolvedValue({
                Member: { findOne: jest.fn().mockResolvedValue(mockMember) },
                User: mockDynamicModels.User
            });

            const result = await voidService.findMemberByUserId(1);
            expect(result).toEqual(mockMember);
        });

        it('should handle processVoidedElement with delivery request', async () => {
            const element = { DeliveryRequestId: 1, CraneRequestId: null, ConcreteRequestId: null };
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.processVoidedElement(element, mockMember, mockUser);

            expect(mockDynamicModels.DeliverHistory.createInstance).toHaveBeenCalled();
        });

        it('should handle processVoidedElement with crane request', async () => {
            const element = { DeliveryRequestId: null, CraneRequestId: 1, ConcreteRequestId: null };
            mockDynamicModels.CraneRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.processVoidedElement(element, mockMember, mockUser);

            expect(mockDynamicModels.CraneRequestHistory.createInstance).toHaveBeenCalled();
        });

        it('should handle processVoidedElement with concrete request', async () => {
            const element = { DeliveryRequestId: null, CraneRequestId: null, ConcreteRequestId: 1 };
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.processVoidedElement(element, mockMember, mockUser);

            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).toHaveBeenCalled();
        });

        it('should handle findVoidById', async () => {
            const mockVoid = { id: 1 };
            mockDynamicModels.VoidList.findOne.mockResolvedValue(mockVoid);

            const result = await voidService.findVoidById([1]);
            expect(result).toEqual(mockVoid);
        });

        it('should handle findVoidedList', async () => {
            const mockVoids = [{ id: 1 }, { id: 2 }];
            mockDynamicModels.VoidList.findAll.mockResolvedValue(mockVoids);

            const result = await voidService.findVoidedList(1);
            expect(result).toEqual(mockVoids);
        });

        it('should handle deliveryRequestVoidHistory', async () => {
            const existVoid = { DeliveryRequestId: 1, ProjectId: 1 };
            mockDynamicModels.DeliveryRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.deliveryRequestVoidHistory(existVoid, mockMember, mockUser);

            expect(mockDynamicModels.DeliverHistory.createInstance).toHaveBeenCalled();
        });

        it('should handle craneRequestVoidHistory', async () => {
            const existVoid = { CraneRequestId: 1, ProjectId: 1 };
            mockDynamicModels.CraneRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.craneRequestVoidHistory(existVoid, mockMember, mockUser);

            expect(mockDynamicModels.CraneRequestHistory.createInstance).toHaveBeenCalled();
        });

        it('should handle concreteRequestVoidHistory', async () => {
            const existVoid = { ConcreteRequestId: 1, ProjectId: 1 };
            mockDynamicModels.ConcreteRequest.findOne.mockResolvedValue({ id: 1, description: 'Test' });

            await voidService.concreteRequestVoidHistory(existVoid, mockMember, mockUser);

            expect(mockDynamicModels.ConcreteRequestHistory.createInstance).toHaveBeenCalled();
        });
    });
});
