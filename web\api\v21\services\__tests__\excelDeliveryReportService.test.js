const excelDeliveryReportService = require('../excelDeliveryReportService');
const moment = require('moment');

// Mock moment to ensure consistent date formatting in tests
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return jest.fn((date) => {
    if (date) {
      return actualMoment(date);
    }
    return actualMoment('2024-03-15T10:30:00Z');
  });
});

describe('ExcelDeliveryReportService', () => {
  let mockWorkbook;
  let mockWorksheet;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock worksheet methods
    mockWorksheet = {
      addRow: jest.fn(),
      getRow: jest.fn(() => ({
        values: []
      })),
      getCell: jest.fn(() => ({
        value: null
      })),
      columns: []
    };

    // Mock workbook methods
    mockWorkbook = {
      addWorksheet: jest.fn(() => mockWorksheet)
    };
  });

  describe('deliveryReport method', () => {
    it('should be defined', () => {
      expect(excelDeliveryReportService).toBeDefined();
      expect(excelDeliveryReportService.deliveryReport).toBeDefined();
      expect(typeof excelDeliveryReportService.deliveryReport).toBe('function');
    });

    it('should create delivery report with all headers selected', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      const responseData = [
        {
          DeliveryId: 'DEL001',
          description: 'Test delivery description',
          deliveryStart: '2024-03-15T10:30:00Z',
          status: 'Approved',
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: 'Doe'
            }
          },
          equipmentDetails: [
            { Equipment: { equipmentName: 'Crane A' } },
            { Equipment: { equipmentName: 'Truck B' } }
          ],
          defineWorkDetails: [
            { DeliverDefineWork: { DFOW: 'Lifting' } },
            { DeliverDefineWork: { DFOW: 'Transport' } }
          ],
          gateDetails: [
            { Gate: { gateName: 'Gate 1' } }
          ],
          companyDetails: [
            { Company: { companyName: 'ABC Corp' } },
            { Company: { companyName: 'XYZ Ltd' } }
          ],
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'Jane',
                  lastName: 'Smith'
                }
              }
            }
          ],
          location: {
            locationPath: 'Building A > Floor 2'
          }
        }
      ];

      const timezoneoffset = -300; // UTC-5

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      // Verify workbook methods were called
      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Delivery Report');
      expect(mockWorksheet.getRow).toHaveBeenCalledWith(1);
      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(mockWorksheet.getCell).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should handle empty response data', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true }
      ];
      const responseData = [];
      const timezoneoffset = 0;

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Delivery Report');
      expect(result).toBe(mockWorkbook);
    });

    it('should handle only inactive headers', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: false },
        { key: 'description', title: 'Description', isActive: false }
      ];
      const responseData = [
        { DeliveryId: 'DEL001', description: 'Test' }
      ];
      const timezoneoffset = 0;

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle partial data with missing fields', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      const responseData = [
        {
          DeliveryId: 'DEL002',
          description: 'Partial data delivery',
          // Missing approverDetails
          equipmentDetails: [], // Empty array
          gateDetails: null, // Null value
          // Missing location
        }
      ];

      const timezoneoffset = 0;

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Date formatting edge cases', () => {
    it('should handle different date formats', async () => {
      const selectedHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      const responseData = [
        { deliveryStart: '2024-03-15T10:30:00Z' },
        { deliveryStart: '2024-12-31T23:59:59Z' },
        { deliveryStart: '2024-01-01T00:00:00Z' }
      ];

      const timezoneoffset = 120; // UTC+2

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle null or undefined delivery start date', async () => {
      const selectedHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true }
      ];

      const responseData = [
        { deliveryStart: null },
        { deliveryStart: undefined },
        {} // Missing deliveryStart
      ];

      const timezoneoffset = 0;

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Equipment details formatting', () => {
    it('should handle multiple equipment items', async () => {
      const selectedHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      const responseData = [
        {
          equipmentDetails: [
            { Equipment: { equipmentName: 'Crane A' } },
            { Equipment: { equipmentName: 'Truck B' } },
            { Equipment: { equipmentName: 'Forklift C' } }
          ]
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle empty equipment details', async () => {
      const selectedHeaders = [
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      const responseData = [
        { equipmentDetails: [] },
        { equipmentDetails: null },
        { equipmentDetails: undefined },
        {} // Missing equipmentDetails
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Company details formatting', () => {
    it('should handle multiple companies', async () => {
      const selectedHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      const responseData = [
        {
          companyDetails: [
            { Company: { companyName: 'ABC Corp' } },
            { Company: { companyName: 'XYZ Ltd' } }
          ]
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing company details', async () => {
      const selectedHeaders = [
        { key: 'company', title: 'Responsible Company', isActive: true }
      ];

      const responseData = [
        { companyDetails: [] },
        { companyDetails: null },
        {}
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Member details formatting', () => {
    it('should handle multiple members', async () => {
      const selectedHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      const responseData = [
        {
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'John',
                  lastName: 'Doe'
                }
              }
            },
            {
              Member: {
                User: {
                  firstName: 'Jane',
                  lastName: 'Smith'
                }
              }
            }
          ]
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing member details', async () => {
      const selectedHeaders = [
        { key: 'name', title: 'Responsible Person', isActive: true }
      ];

      const responseData = [
        { memberDetails: [] },
        { memberDetails: null },
        {}
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('DFOW (Definable Feature of Work) formatting', () => {
    it('should handle multiple DFOW items', async () => {
      const selectedHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      const responseData = [
        {
          defineWorkDetails: [
            { DeliverDefineWork: { DFOW: 'Lifting' } },
            { DeliverDefineWork: { DFOW: 'Transport' } },
            { DeliverDefineWork: { DFOW: 'Installation' } }
          ]
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing DFOW details', async () => {
      const selectedHeaders = [
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true }
      ];

      const responseData = [
        { defineWorkDetails: [] },
        { defineWorkDetails: null },
        {}
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Gate details formatting', () => {
    it('should handle gate details with valid data', async () => {
      const selectedHeaders = [
        { key: 'gate', title: 'Gate', isActive: true }
      ];

      const responseData = [
        {
          gateDetails: [
            { Gate: { gateName: 'Main Gate' } }
          ]
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing gate details', async () => {
      const selectedHeaders = [
        { key: 'gate', title: 'Gate', isActive: true }
      ];

      const responseData = [
        { gateDetails: [] },
        { gateDetails: null },
        { gateDetails: undefined },
        {}
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Location formatting', () => {
    it('should handle location with valid path', async () => {
      const selectedHeaders = [
        { key: 'location', title: 'Location', isActive: true }
      ];

      const responseData = [
        {
          location: {
            locationPath: 'Building A > Floor 2 > Room 201'
          }
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing location', async () => {
      const selectedHeaders = [
        { key: 'location', title: 'Location', isActive: true }
      ];

      const responseData = [
        { location: null },
        { location: undefined },
        { location: {} }, // Empty location object
        {}
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Approver details formatting', () => {
    it('should handle approver with full name', async () => {
      const selectedHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      const responseData = [
        {
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: 'Doe'
            }
          }
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing approver details', async () => {
      const selectedHeaders = [
        { key: 'approvedby', title: 'Approved By', isActive: true }
      ];

      const responseData = [
        { approverDetails: null },
        { approverDetails: undefined },
        { approverDetails: {} }, // Empty approver object
        { approverDetails: { User: null } }, // Null user
        { approverDetails: { User: {} } }, // Empty user object
        {}
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  describe('Column width configuration', () => {
    it('should set correct width for id column', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true }
      ];

      const responseData = [
        { DeliveryId: 'DEL001', description: 'Test' }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
      // Verify that columns array is set on worksheet
      expect(mockWorksheet.columns).toBeDefined();
    });

    it('should handle headers with keys not in flagMap', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'unknownKey', title: 'Unknown Field', isActive: true }, // This key is not in flagMap
        { key: 'customField', title: 'Custom Field', isActive: true } // This key is also not in flagMap
      ];

      const responseData = [
        { DeliveryId: 'DEL001' }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
      // This test ensures we cover the else branch of the hasOwnProperty check
    });
  });

  describe('Complex scenarios', () => {
    it('should handle large dataset with all fields', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true },
        { key: 'dfow', title: 'Definable Feature of Work', isActive: true },
        { key: 'gate', title: 'Gate', isActive: true },
        { key: 'company', title: 'Responsible Company', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'location', title: 'Location', isActive: true }
      ];

      // Create multiple records to test loop functionality
      const responseData = Array.from({ length: 5 }, (_, index) => ({
        DeliveryId: `DEL00${index + 1}`,
        description: `Test delivery ${index + 1}`,
        deliveryStart: '2024-03-15T10:30:00Z',
        status: 'Approved',
        approverDetails: {
          User: {
            firstName: 'John',
            lastName: 'Doe'
          }
        },
        equipmentDetails: [
          { Equipment: { equipmentName: `Equipment ${index + 1}` } }
        ],
        defineWorkDetails: [
          { DeliverDefineWork: { DFOW: `Work ${index + 1}` } }
        ],
        gateDetails: [
          { Gate: { gateName: `Gate ${index + 1}` } }
        ],
        companyDetails: [
          { Company: { companyName: `Company ${index + 1}` } }
        ],
        memberDetails: [
          {
            Member: {
              User: {
                firstName: 'Jane',
                lastName: 'Smith'
              }
            }
          }
        ],
        location: {
          locationPath: `Location ${index + 1}`
        }
      }));

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(5);
    });

    it('should handle mixed data quality', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'approvedby', title: 'Approved By', isActive: true },
        { key: 'equipment', title: 'Equipment', isActive: true }
      ];

      const responseData = [
        // Complete record
        {
          DeliveryId: 'DEL001',
          description: 'Complete delivery',
          approverDetails: {
            User: { firstName: 'John', lastName: 'Doe' }
          },
          equipmentDetails: [
            { Equipment: { equipmentName: 'Crane' } }
          ]
        },
        // Partial record
        {
          DeliveryId: 'DEL002',
          description: 'Partial delivery',
          // Missing approverDetails
          equipmentDetails: []
        },
        // Minimal record
        {
          DeliveryId: 'DEL003'
          // Missing most fields
        }
      ];

      const result = await excelDeliveryReportService.deliveryReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        0
      );

      expect(result).toBe(mockWorkbook);
      expect(mockWorksheet.addRow).toHaveBeenCalledTimes(3);
    });
  });
});