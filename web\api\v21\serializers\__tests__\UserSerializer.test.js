const UserSerializer = require('../UserSerializer');

describe('UserSerializer', () => {
  it('should serialize all fields correctly', () => {
    const user = {
      id: 1,
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: '**********',
      planDetails: { plan: 'A' },
      roleDetails: { role: 'Admin' },
      planId: 10,
      roleId: 20,
      isAccount: true,
      profilePic: 'pic.jpg',
      type: 'user',
      versionFlag: 2,
    };
    const result = UserSerializer.serialize(user);
    expect(result).toEqual(user);
  });

  it('should serialize with missing fields as undefined', () => {
    const user = {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    };
    const result = UserSerializer.serialize(user);
    expect(result).toEqual({
      id: 1,
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: undefined,
      planId: undefined,
      roleId: undefined,
      planDetails: undefined,
      roleDetails: undefined,
      isAccount: undefined,
      profilePic: undefined,
      type: undefined,
      versionFlag: undefined,
    });
  });
});
