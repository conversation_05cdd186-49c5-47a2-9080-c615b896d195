const Joi = require('joi');

const memberValidation = {
  inviteMembers: {
    body: Joi.object({
      membersList: Joi.array().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      requestType: Joi.number().required(),
    }),
  },
  editMember: {
    body: Joi.object({
      id: Joi.number().required(),
      firstName: Joi.string().min(3).required(),
      lastName: Joi.string().optional().allow('', null),
      email: Joi.string().email().required(),
      phoneNumber: Joi.string().required(),
      phoneCode: Joi.string().required(),
      ProjectId: Joi.number().required(),
      RoleId: Joi.number().required(),
      CompanyId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getOverViewDetail: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getUserDetail: {
    body: Joi.object({
      memberId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      email: Joi.string().required(),
      requestType: Joi.number().required(),
      domainName: Joi.string().required(),
    }),
  },
  updateUserProfile: {
    body: Joi.object({
      CompanyId: Joi.number().required(),
      firstName: Joi.string().min(3).required(),
      lastName: Joi.string().required(),
      email: Joi.string(),
      phoneNumber: Joi.string().required(),
      phoneCode: Joi.string().required(),

      website: Joi.optional().allow(''),
      ProjectId: Joi.number().required(),
      companyName: Joi.string().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  deleteMember: {
    body: Joi.object({
      id: Joi.array(),
      ProjectId: Joi.number().required(),
      isSelectAll: Joi.boolean().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  searchMember: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      search: Joi.string().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  listMember: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      companyFilter: Joi.string().optional().allow('', null),
      nameFilter: Joi.optional().allow(''),
      roleFilter: Joi.number(),
      search: Joi.optional().allow(''),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      statusFilter: Joi.optional().allow(''),
    }),
  },
  listAllMember: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  listGuestMember: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      companyFilter: Joi.string().optional().allow('', null),
      nameFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      statusFilter: Joi.optional().allow(''),
    }),
  },
  addGuestAsMember: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      guestUserId: Joi.number().required(),
      guestId: Joi.number().required(),
      guestEmail: Joi.string().email().required(),
      selectedRoleId: Joi.number().required(),
      guestFirstName: Joi.string().required(),
      guestLastName: Joi.string().required(),
      memberId: Joi.number().required(),
      memberFirstName: Joi.string().required(),
      memberLastName: Joi.string().required(),
    }),
  },
  rejectGuestRequest: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      guestUserId: Joi.number().required(),
      guestId: Joi.number().required(),
      guestEmail: Joi.string().email().required(),
      selectedRoleId: Joi.number().allow('', null).optional(),
      guestFirstName: Joi.string().required(),
      guestLastName: Joi.string().required(),
      memberId: Joi.number().required(),
      memberFirstName: Joi.string().required(),
      memberLastName: Joi.string().required(),
    }),
  },
};
module.exports = memberValidation;
