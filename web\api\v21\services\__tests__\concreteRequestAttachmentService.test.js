// Mock Firebase configuration first to prevent initialization errors
jest.mock('../../config/fcm', () => ({
    sendMemberLocationPreferencePushNotificationForConcrete: jest.fn(),
    sendPushNotificationForConcrete: jest.fn()
}));

const concreteRequestAttachmentService = require('../concreteRequestAttachmentService');
const helper = require('../../helpers/domainHelper');
const notificationHelper = require('../../helpers/notificationHelper');
const pushNotification = require('../../config/fcm');
const awsConfig = require('../../middlewares/awsConfig');
const MAILER = require('../../mailer');

// Mock all dependencies
jest.mock('../../models', () => ({
    Sequelize: {
        Op: {
            ne: 'ne',
            notIn: 'notIn'
        },
        and: jest.fn()
    },
    Enterprise: {
        findOne: jest.fn()
    },
    NotificationPreference: {
        findAll: jest.fn()
    },
    Locations: {
        findOne: jest.fn()
    },
    LocationNotificationPreferences: {
        findAll: jest.fn()
    },
    Project: {
        findByPk: jest.fn()
    },
    Sequelize: {
        Op: {
            ne: 'ne',
            notIn: 'notIn'
        },
        and: jest.fn()
    }
}));

jest.mock('../../helpers/domainHelper');
jest.mock('../../helpers/notificationHelper', () => ({
    createMemberDeliveryLocationInAppNotification: jest.fn(),
    createDeliveryPersonNotification: jest.fn()
}));
jest.mock('../../config/fcm');
jest.mock('../../middlewares/awsConfig');
jest.mock('../../mailer');

describe('concreteRequestAttachmentService', () => {
    let mockModels;
    let mockInputData;
    let mockDone;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common mock data
        mockModels = {
            ConcreteRequest: {
                findOne: jest.fn(),
                createInstance: jest.fn()
            },
            ConcreteRequestAttachment: {
                findAll: jest.fn(),
                findOne: jest.fn(),
                update: jest.fn(),
                createInstance: jest.fn(),
                createMultipleInstance: jest.fn()
            },
            ConcreteRequestHistory: {
                createInstance: jest.fn()
            },
            Member: {
                findOne: jest.fn(),
                findAll: jest.fn()
            },
            User: {
                findOne: jest.fn()
            },
            Notification: {
                createInstance: jest.fn()
            },
            DeliveryPersonNotification: {
                createInstance: jest.fn()
            },
            ConcreteRequestResponsiblePerson: {
                findAll: jest.fn()
            },
            Project: {
                findByPk: jest.fn()
            },
            Enterprise: {
                findOne: jest.fn()
            },
            Locations: {
                findOne: jest.fn()
            },
            LocationNotificationPreferences: {
                findAll: jest.fn()
            },
            NotificationPreference: {
                findAll: jest.fn()
            },
            Sequelize: {
                Op: {
                    ne: 'ne',
                    notIn: 'notIn'
                },
                and: jest.fn()
            }
        };

        // Setup mock input data
        mockInputData = {
            user: {
                id: 1,
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                domainName: 'testdomain',
                profilePic: 'profile.jpg'
            },
            params: {
                ConcreteRequestId: 123,
                ProjectId: 456,
                id: 789
            },
            body: {
                ParentCompanyId: 101
            },
            files: [
                {
                    originalname: 'test.pdf',
                    name: 'test.pdf'
                }
            ]
        };

        mockDone = jest.fn();

        // Setup helper mock returns
        helper.getDynamicModel.mockResolvedValue(mockModels);
        helper.returnProjectModel.mockResolvedValue({
            Member: mockModels.Member,
            User: mockModels.User
        });

        // Setup default mock returns for location-related methods
        mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
        mockModels.Locations.findOne.mockResolvedValue({ locationPath: 'Default Location' });
        mockModels.Enterprise.findOne.mockResolvedValue({ name: 'testdomain' });
        mockModels.NotificationPreference.findAll.mockResolvedValue([]);
    });

    describe('getDynamicModel', () => {
        it('should successfully get dynamic model with domain name', async () => {
            const mockEnterprise = { name: 'testdomain' };
            mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            expect(result).toBe(true);
            expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
        });

        it('should handle undefined domain name', async () => {
            mockInputData.user.domainName = undefined;
            mockModels.User.findOne.mockResolvedValue({ id: 1 });
            mockModels.Member.findOne.mockResolvedValue({ id: 1, isAccount: true });
            mockModels.Enterprise.findOne.mockResolvedValue({ name: 'fallbackdomain' });

            const result = await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            expect(result).toBe(true);
        });
    });

    describe('getConcreteRequestAttachments', () => {
        it('should successfully get attachments', async () => {
            const mockConcreteRequest = { id: 1 };
            const mockAttachments = [{ id: 1, filename: 'test.pdf' }];

            mockModels.ConcreteRequest.findOne.mockResolvedValue(mockConcreteRequest);
            mockModels.ConcreteRequestAttachment.findAll.mockResolvedValue(mockAttachments);

            await concreteRequestAttachmentService.getConcreteRequestAttachments(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                { attachmentList: mockAttachments, exist: mockConcreteRequest },
                false
            );
        });

        it('should handle non-existent concrete request', async () => {
            mockModels.ConcreteRequest.findOne.mockResolvedValue(null);

            await concreteRequestAttachmentService.getConcreteRequestAttachments(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                { message: 'Concrete Booking id does not exist' }
            );
        });

        it('should handle errors', async () => {
            const mockError = new Error('Test error');
            mockModels.ConcreteRequest.findOne.mockRejectedValue(mockError);

            await concreteRequestAttachmentService.getConcreteRequestAttachments(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });

    describe('deleteConcreteRequestAttachment', () => {
        it('should successfully delete attachment and send notifications', async () => {
            const mockAttachment = {
                id: 1,
                ConcreteRequestId: 123,
                ConcreteRequest: { id: 123 }
            };
            const mockMember = { id: 1 };
            const mockConcreteRequest = { id: 123, ProjectId: 456 };
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberPreferences = [{ Member: { id: 1, User: { firstName: 'Test' } } }];
            const mockProject = { projectName: 'Test Project' };

            mockModels.ConcreteRequestAttachment.findOne.mockResolvedValue(mockAttachment);
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.ConcreteRequest.findOne.mockResolvedValue(mockConcreteRequest);
            mockModels.Locations.findOne.mockResolvedValue(mockLocation);
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberPreferences);
            mockModels.ConcreteRequestAttachment.update.mockResolvedValue([1]);
            mockModels.Project.findByPk.mockResolvedValue(mockProject);
            mockModels.ConcreteRequestHistory.createInstance.mockResolvedValue({});
            mockModels.Notification.createInstance.mockResolvedValue({});
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);
            mockModels.ConcreteRequestResponsiblePerson.findAll.mockResolvedValue([]);

            await concreteRequestAttachmentService.deleteConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockModels.ConcreteRequestAttachment.update).toHaveBeenCalledWith(
                { isDeleted: true },
                { where: { id: mockInputData.params.id } }
            );
            expect(pushNotification.sendMemberLocationPreferencePushNotificationForConcrete).toHaveBeenCalled();
        });

        it('should handle non-existent attachment', async () => {
            mockModels.ConcreteRequestAttachment.findOne.mockResolvedValue(null);

            await concreteRequestAttachmentService.deleteConcreteRequestAttachment(mockInputData, mockDone);

            // The update should still be called even if attachment is null
            expect(mockModels.ConcreteRequestAttachment.update).toHaveBeenCalled();
        });

        it('should handle error during deletion', async () => {
            const mockError = new Error('Deletion failed');
            mockModels.ConcreteRequestAttachment.findOne.mockRejectedValue(mockError);

            await concreteRequestAttachmentService.deleteConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle case when concrete request does not exist after attachment found', async () => {
            const mockAttachment = {
                id: 1,
                ConcreteRequestId: 123,
                ConcreteRequest: { id: 123 }
            };
            const mockMember = { id: 1 };

            mockModels.ConcreteRequestAttachment.findOne.mockResolvedValue(mockAttachment);
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.ConcreteRequest.findOne.mockResolvedValue(null);
            mockModels.ConcreteRequestAttachment.update.mockResolvedValue([1]);

            await concreteRequestAttachmentService.deleteConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockModels.ConcreteRequestAttachment.update).toHaveBeenCalled();
            // When exist is null, the method returns early without calling done with history
            expect(mockDone).not.toHaveBeenCalled();
        });

        it('should handle empty member location preferences', async () => {
            const mockAttachment = {
                id: 1,
                ConcreteRequestId: 123,
                ConcreteRequest: { id: 123 }
            };
            const mockMember = { id: 1 };
            const mockConcreteRequest = { id: 123, ProjectId: 456 };
            const mockLocation = { locationPath: 'Test Location' };

            mockModels.ConcreteRequestAttachment.findOne.mockResolvedValue(mockAttachment);
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.ConcreteRequest.findOne.mockResolvedValue(mockConcreteRequest);
            mockModels.Locations.findOne.mockResolvedValue(mockLocation);
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.ConcreteRequestAttachment.update.mockResolvedValue([1]);
            mockModels.Project.findByPk.mockResolvedValue({ projectName: 'Test Project' });
            mockModels.ConcreteRequestHistory.createInstance.mockResolvedValue({});
            mockModels.Notification.createInstance.mockResolvedValue({});
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);
            mockModels.ConcreteRequestResponsiblePerson.findAll.mockResolvedValue([]);

            await concreteRequestAttachmentService.deleteConcreteRequestAttachment(mockInputData, mockDone);

            expect(pushNotification.sendMemberLocationPreferencePushNotificationForConcrete).not.toHaveBeenCalled();
        });
    });

    describe('createConcreteRequestAttachment', () => {
        it('should successfully create attachment and send notifications', async () => {
            const mockConcreteRequest = { id: 123, ProjectId: 456 };
            const mockMember = { id: 1 };
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberPreferences = [{ Member: { id: 1, User: { firstName: 'Test' } } }];
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockUserDataMail = [
                { Member: { isGuestUser: true, User: { email: '<EMAIL>', firstName: 'Guest' } } }
            ];

            mockModels.ConcreteRequest.findOne
                .mockResolvedValueOnce(mockConcreteRequest) // for findExistingConcreteRequest
                .mockResolvedValueOnce({ memberDetails: mockUserDataMail }); // for getUserDataMail
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.Locations.findOne.mockResolvedValue(mockLocation);
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberPreferences);
            mockModels.ConcreteRequestAttachment.createMultipleInstance.mockResolvedValue({});
            mockModels.ConcreteRequestHistory.createInstance.mockResolvedValue({});
            mockModels.Notification.createInstance.mockResolvedValue({});
            mockModels.Project.findByPk.mockResolvedValue({ projectName: 'Test Project' });
            mockModels.ConcreteRequestResponsiblePerson.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            // Mock MAILER
            const mockMailer = { sendMail: jest.fn() };
            jest.doMock('../../mailer', () => mockMailer);

            // Mock AWS upload callback
            awsConfig.upload.mockImplementation((_files, callback) => {
                callback(mockUploadResult, null);
            });

            await concreteRequestAttachmentService.createConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockModels.ConcreteRequestAttachment.createMultipleInstance).toHaveBeenCalled();
            expect(pushNotification.sendMemberLocationPreferencePushNotificationForConcrete).toHaveBeenCalled();
        });

        it('should handle AWS upload error', async () => {
            const mockError = new Error('Upload failed');
            mockModels.ConcreteRequest.findOne.mockResolvedValue({ id: 123 });
            mockModels.Member.findOne.mockResolvedValue({ id: 1 });

            awsConfig.upload.mockImplementation((_files, callback) => {
                callback(null, mockError);
            });

            await concreteRequestAttachmentService.createConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle non-existent concrete request', async () => {
            mockModels.ConcreteRequest.findOne.mockResolvedValue(null);

            await concreteRequestAttachmentService.createConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(
                null,
                { message: 'Concrete Booking id does not exist' }
            );
        });

        it('should handle error during creation process', async () => {
            const mockError = new Error('Creation failed');
            mockModels.ConcreteRequest.findOne.mockRejectedValue(mockError);

            await concreteRequestAttachmentService.createConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });

        it('should handle empty member location preferences during creation', async () => {
            const mockConcreteRequest = { id: 123, ProjectId: 456 };
            const mockMember = { id: 1 };
            const mockLocation = { locationPath: 'Test Location' };
            const mockUploadResult = [{ Location: 'test-location' }];

            mockModels.ConcreteRequest.findOne
                .mockResolvedValueOnce(mockConcreteRequest)
                .mockResolvedValueOnce({ memberDetails: [] });
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.Locations.findOne.mockResolvedValue(mockLocation);
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue([]);
            mockModels.ConcreteRequestAttachment.createMultipleInstance.mockResolvedValue({});
            mockModels.ConcreteRequestHistory.createInstance.mockResolvedValue({});
            mockModels.Notification.createInstance.mockResolvedValue({});
            mockModels.Project.findByPk.mockResolvedValue({ projectName: 'Test Project' });
            mockModels.ConcreteRequestResponsiblePerson.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            awsConfig.upload.mockImplementation((_files, callback) => {
                callback(mockUploadResult, null);
            });

            await concreteRequestAttachmentService.createConcreteRequestAttachment(mockInputData, mockDone);

            expect(pushNotification.sendMemberLocationPreferencePushNotificationForConcrete).not.toHaveBeenCalled();
        });

        it('should handle non-guest users in email notifications', async () => {
            const mockConcreteRequest = { id: 123, ProjectId: 456 };
            const mockMember = { id: 1 };
            const mockLocation = { locationPath: 'Test Location' };
            const mockMemberPreferences = [{ Member: { id: 1, User: { firstName: 'Test' } } }];
            const mockUploadResult = [{ Location: 'test-location' }];
            const mockUserDataMail = [
                { Member: { isGuestUser: false, User: { email: '<EMAIL>', firstName: 'User' } } }
            ];

            mockModels.ConcreteRequest.findOne
                .mockResolvedValueOnce(mockConcreteRequest)
                .mockResolvedValueOnce({ memberDetails: mockUserDataMail });
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.Locations.findOne.mockResolvedValue(mockLocation);
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockMemberPreferences);
            mockModels.ConcreteRequestAttachment.createMultipleInstance.mockResolvedValue({});
            mockModels.ConcreteRequestHistory.createInstance.mockResolvedValue({});
            mockModels.Notification.createInstance.mockResolvedValue({});
            mockModels.Project.findByPk.mockResolvedValue({ projectName: 'Test Project' });
            mockModels.ConcreteRequestResponsiblePerson.findAll.mockResolvedValue([]);
            mockModels.NotificationPreference.findAll.mockResolvedValue([]);

            awsConfig.upload.mockImplementation((_files, callback) => {
                callback(mockUploadResult, null);
            });

            await concreteRequestAttachmentService.createConcreteRequestAttachment(mockInputData, mockDone);

            expect(mockModels.ConcreteRequestAttachment.createMultipleInstance).toHaveBeenCalled();
        });
    });

    describe('Helper Functions', () => {
        describe('extractDomainInfo', () => {
            it('should extract domain info from input data', () => {
                const result = concreteRequestAttachmentService.extractDomainInfo(mockInputData);

                expect(result).toEqual({
                    domainName: 'testdomain',
                    email: '<EMAIL>',
                    ParentCompanyId: 101
                });
            });

            it('should handle missing ParentCompanyId in body', () => {
                delete mockInputData.body.ParentCompanyId;
                mockInputData.params.ParentCompanyId = 202;

                const result = concreteRequestAttachmentService.extractDomainInfo(mockInputData);

                expect(result.ParentCompanyId).toBe(202);
            });

            it('should handle missing ParentCompanyId in both body and params', () => {
                delete mockInputData.body.ParentCompanyId;
                delete mockInputData.params.ParentCompanyId;

                const result = concreteRequestAttachmentService.extractDomainInfo(mockInputData);

                expect(result.ParentCompanyId).toBeUndefined();
            });
        });

        describe('getFileNameAndExtension', () => {
            it('should extract filename and extension from originalname', () => {
                const fileData = { originalname: 'test.pdf' };
                const result = concreteRequestAttachmentService.getFileNameAndExtension(fileData);

                expect(result).toEqual({
                    fileName: 'test.pdf',
                    extension: 'pdf'
                });
            });

            it('should extract filename and extension from name', () => {
                const fileData = { name: 'test.pdf' };
                const result = concreteRequestAttachmentService.getFileNameAndExtension(fileData);

                expect(result).toEqual({
                    fileName: 'test.pdf',
                    extension: 'pdf'
                });
            });

            it('should handle files without extension', () => {
                const fileData = { originalname: 'testfile' };
                const result = concreteRequestAttachmentService.getFileNameAndExtension(fileData);

                expect(result).toEqual({
                    fileName: 'testfile',
                    extension: 'testfile'
                });
            });

            it('should handle files with multiple dots', () => {
                const fileData = { originalname: 'test.backup.pdf' };
                const result = concreteRequestAttachmentService.getFileNameAndExtension(fileData);

                expect(result).toEqual({
                    fileName: 'test.backup.pdf',
                    extension: 'pdf'
                });
            });
        });
    });

    describe('getDomainEnterpriseValue', () => {
        it('should return domain and enterprise value when domain exists', async () => {
            const mockEnterprise = { name: 'testdomain' };
            mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await concreteRequestAttachmentService.getDomainEnterpriseValue('testdomain');

            expect(result).toEqual({
                domainName: 'testdomain',
                enterpriseValue: 'testdomain'
            });
        });

        it('should return empty domain when enterprise not found', async () => {
            mockModels.Enterprise.findOne.mockResolvedValue(null);

            const result = await concreteRequestAttachmentService.getDomainEnterpriseValue('nonexistent');

            expect(result).toEqual({
                domainName: '',
                enterpriseValue: undefined
            });
        });

        it('should handle undefined domain name', async () => {
            const result = await concreteRequestAttachmentService.getDomainEnterpriseValue(undefined);

            expect(result).toEqual({
                domainName: undefined,
                enterpriseValue: undefined
            });
        });
    });

    describe('handleUndefinedDomain', () => {
        it('should find enterprise value for account member', async () => {
            const mockUser = { id: 1 };
            const mockMember = { id: 1, isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'testenterprise' };

            mockModels.User.findOne.mockResolvedValue(mockUser);
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await concreteRequestAttachmentService.handleUndefinedDomain(101, '<EMAIL>');

            expect(result).toBe('testenterprise');
        });

        it('should find enterprise value for non-account member', async () => {
            const mockUser = { id: 1 };
            const mockMember = { id: 1, isAccount: false };
            const mockEnterprise = { name: 'testenterprise' };

            mockModels.User.findOne.mockResolvedValue(mockUser);
            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await concreteRequestAttachmentService.handleUndefinedDomain(101, '<EMAIL>');

            expect(result).toBe('testenterprise');
        });

        it('should return empty string when user not found', async () => {
            mockModels.User.findOne.mockResolvedValue(null);

            const result = await concreteRequestAttachmentService.handleUndefinedDomain(101, '<EMAIL>');

            expect(result).toBe('');
        });

        it('should return empty string when member not found', async () => {
            const mockUser = { id: 1 };
            mockModels.User.findOne.mockResolvedValue(mockUser);
            mockModels.Member.findOne.mockResolvedValue(null);

            const result = await concreteRequestAttachmentService.handleUndefinedDomain(101, '<EMAIL>');

            expect(result).toBe('');
        });
    });

    describe('getEnterpriseValue', () => {
        it('should return enterprise name for account member', async () => {
            const mockMember = { isAccount: true, EnterpriseId: 1 };
            const mockEnterprise = { name: 'TestEnterprise' };
            mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await concreteRequestAttachmentService.getEnterpriseValue(mockMember, 101);

            expect(result).toBe('testenterprise');
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { id: 1, status: 'completed' }
            });
        });

        it('should return enterprise name for non-account member', async () => {
            const mockMember = { isAccount: false };
            const mockEnterprise = { name: 'TestEnterprise' };
            mockModels.Enterprise.findOne.mockResolvedValue(mockEnterprise);

            const result = await concreteRequestAttachmentService.getEnterpriseValue(mockMember, 101);

            expect(result).toBe('testenterprise');
            expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
                where: { ParentCompanyId: 101, status: 'completed' }
            });
        });

        it('should return empty string when enterprise not found', async () => {
            const mockMember = { isAccount: true, EnterpriseId: 1 };
            mockModels.Enterprise.findOne.mockResolvedValue(null);

            const result = await concreteRequestAttachmentService.getEnterpriseValue(mockMember, 101);

            expect(result).toBe('');
        });
    });

    describe('updateIncomeUser', () => {
        it('should update user in input data', async () => {
            // First we need to call getDynamicModel to set up the User model
            await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            const mockNewUser = { id: 2, email: '<EMAIL>', firstName: 'Updated' };
            mockModels.User.findOne.mockResolvedValue(mockNewUser);

            await concreteRequestAttachmentService.updateIncomeUser(mockInputData);

            expect(mockInputData.user).toBe(mockNewUser);
        });
    });

    describe('assignDynamicModels', () => {
        it('should assign all dynamic models', () => {
            const mockModelObj = {
                ConcreteRequest: 'mockConcreteRequest',
                ConcreteRequestAttachment: 'mockConcreteRequestAttachment',
                ConcreteRequestHistory: 'mockConcreteRequestHistory',
                ConcreteRequestResponsiblePerson: 'mockConcreteRequestResponsiblePerson',
                Member: 'mockMember',
                User: 'mockUser',
                Notification: 'mockNotification',
                DeliveryPersonNotification: 'mockDeliveryPersonNotification'
            };

            concreteRequestAttachmentService.assignDynamicModels(mockModelObj);

            // Since we can't directly test the assignment, we test that the method runs without error
            expect(true).toBe(true);
        });
    });

    describe('constructBulkData', () => {
        it('should construct bulk data for multiple files', () => {
            const mockResult = [
                { Location: 'location1' },
                { Location: 'location2' }
            ];
            const mockInputData = {
                files: [
                    { originalname: 'file1.pdf' },
                    { originalname: 'file2.jpg' }
                ]
            };
            const mockExist = { id: 123, ProjectId: 456 };

            const result = concreteRequestAttachmentService.constructBulkData(mockResult, mockInputData, mockExist);

            expect(result).toEqual([
                {
                    attachment: 'location1',
                    filename: 'file1.pdf',
                    extension: 'pdf',
                    ConcreteRequestId: 123,
                    ProjectId: 456,
                    isDeleted: false
                },
                {
                    attachment: 'location2',
                    filename: 'file2.jpg',
                    extension: 'jpg',
                    ConcreteRequestId: 123,
                    ProjectId: 456,
                    isDeleted: false
                }
            ]);
        });
    });

    describe('constructGuestMailPayload', () => {
        it('should construct guest mail payload', () => {
            const mockUserData = {
                Member: {
                    User: {
                        email: '<EMAIL>',
                        firstName: 'Guest'
                    }
                }
            };
            const mockUser = { firstName: 'John', lastName: 'Doe' };

            const result = concreteRequestAttachmentService.constructGuestMailPayload(mockUserData, mockUser);

            expect(result).toEqual({
                email: '<EMAIL>',
                guestName: 'Guest',
                content: 'We would like to inform you that John Doe Attached the file in Booking.'
            });
        });
    });

    describe('constructHistoryObject', () => {
        it('should construct history object for deletion', () => {
            const mockExist = { id: 123, description: 'Test Booking', ProjectId: 456 };
            const mockLoginUser = { firstName: 'John', lastName: 'Doe', profilePic: 'pic.jpg' };
            const mockLocationChosen = { locationPath: 'Test Location' };
            const mockMemberDetail = { id: 1 };

            const result = concreteRequestAttachmentService.constructHistoryObject(
                mockExist, mockLoginUser, mockLocationChosen, mockMemberDetail
            );

            expect(result).toMatchObject({
                ConcreteRequestId: 123,
                MemberId: 1,
                type: 'attachment',
                description: 'John Doe removed the file in Test Booking',
                locationFollowDescription: 'John Doe removed the file in the Booking Test Booking. Location: Test Location.',
                ProjectId: 456,
                firstName: 'John',
                profilePic: 'pic.jpg',
                projectName: ''
            });
            expect(result.createdAt).toBeInstanceOf(Date);
        });
    });

    describe('constructNotificationObject', () => {
        it('should construct notification object', () => {
            const mockHistory = {
                ConcreteRequestId: 123,
                MemberId: 1,
                type: 'attachment',
                description: 'Test description'
            };
            const mockParams = { ProjectId: 456 };

            const result = concreteRequestAttachmentService.constructNotificationObject(mockHistory, mockParams);

            expect(result).toEqual({
                ...mockHistory,
                title: 'Concrete Booking Attachment',
                isDeliveryRequest: false,
                requestType: 'concreteRequest',
                ProjectId: 456
            });
        });
    });

    describe('getLocationFollowMembers', () => {
        it('should extract member IDs from location preferences', async () => {
            const mockMemberLocationPreference = [
                { Member: { id: 1 } },
                { Member: { id: 2 } },
                { Member: { id: 3 } }
            ];

            const result = await concreteRequestAttachmentService.getLocationFollowMembers(mockMemberLocationPreference);

            expect(result).toEqual([1, 2, 3]);
        });

        it('should handle empty location preferences', async () => {
            const result = await concreteRequestAttachmentService.getLocationFollowMembers([]);

            expect(result).toEqual([]);
        });

        it('should handle null member references', async () => {
            const mockMemberLocationPreference = [
                { Member: { id: 1 } },
                { Member: null },
                { Member: { id: 3 } }
            ];

            const result = await concreteRequestAttachmentService.getLocationFollowMembers(mockMemberLocationPreference);

            expect(result).toEqual([1, undefined, 3]);
        });
    });

    describe('findAttachment', () => {
        it('should find attachment with concrete request association', async () => {
            // First set up dynamic models
            await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            const mockAttachment = { id: 1, ConcreteRequest: { id: 123 } };
            const mockParams = { id: 1, ProjectId: 456 };
            mockModels.ConcreteRequestAttachment.findOne.mockResolvedValue(mockAttachment);

            const result = await concreteRequestAttachmentService.findAttachment(mockParams);

            expect(result).toBe(mockAttachment);
        });
    });

    describe('getMemberAndRequestDetails', () => {
        it('should get member and request details', async () => {
            // First set up dynamic models
            await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            const mockMember = { id: 1 };
            const mockRequest = { id: 123 };
            const mockAttachment = { ConcreteRequestId: 123 };
            const mockParams = { user: { id: 1 }, ProjectId: 456 };

            mockModels.Member.findOne.mockResolvedValue(mockMember);
            mockModels.ConcreteRequest.findOne.mockResolvedValue(mockRequest);

            const result = await concreteRequestAttachmentService.getMemberAndRequestDetails(mockParams, mockAttachment);

            expect(result).toEqual({
                memberDetail: mockMember,
                exist: mockRequest
            });
        });
    });

    describe('getLocation', () => {
        it('should get location details', async () => {
            const mockLocation = { id: 1, locationPath: 'Test Location' };
            const mockExist = { ProjectId: 456, LocationId: 1 };
            mockModels.Locations.findOne.mockResolvedValue(mockLocation);

            const result = await concreteRequestAttachmentService.getLocation(mockExist);

            expect(result).toBe(mockLocation);
            expect(mockModels.Locations.findOne).toHaveBeenCalledWith({
                where: {
                    ProjectId: 456,
                    id: 1
                }
            });
        });
    });

    describe('getMemberLocationPreferences', () => {
        it('should get member location preferences', async () => {
            const mockPreferences = [{ Member: { id: 1 } }];
            const mockExist = { ProjectId: 456, LocationId: 1 };
            mockModels.LocationNotificationPreferences.findAll.mockResolvedValue(mockPreferences);

            const result = await concreteRequestAttachmentService.getMemberLocationPreferences(mockExist);

            expect(result).toBe(mockPreferences);
            expect(mockModels.LocationNotificationPreferences.findAll).toHaveBeenCalledWith({
                where: {
                    ProjectId: 456,
                    LocationId: 1,
                    follow: true
                },
                include: [
                    {
                        association: 'Member',
                        attributes: ['id', 'RoleId'],
                        include: [
                            {
                                association: 'User',
                                attributes: ['id', 'firstName', 'lastName', 'email']
                            }
                        ]
                    }
                ]
            });
        });
    });

    describe('getAdminData', () => {
        it('should get admin data excluding location follow members', async () => {
            // First set up dynamic models
            await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            const mockAdminData = [{ id: 1, User: { firstName: 'Admin' } }];
            const mockParams = { ProjectId: 456 };
            const mockLocationFollowMembers = [2, 3];
            mockModels.Member.findAll.mockResolvedValue(mockAdminData);

            const result = await concreteRequestAttachmentService.getAdminData(mockParams, mockLocationFollowMembers);

            expect(result).toBe(mockAdminData);
        });
    });

    describe('getPersonData', () => {
        it('should get person data for concrete request', async () => {
            // First set up dynamic models
            await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            const mockPersonData = [{ Member: { id: 1, User: { firstName: 'Person' } } }];
            const mockAttachment = { ConcreteRequestId: 123 };
            const mockLocationFollowMembers = [2, 3];
            mockModels.ConcreteRequestResponsiblePerson.findAll.mockResolvedValue(mockPersonData);

            const result = await concreteRequestAttachmentService.getPersonData(mockAttachment, mockLocationFollowMembers);

            expect(result).toBe(mockPersonData);
        });
    });

    describe('getNotificationPreferences', () => {
        it('should get notification preferences', async () => {
            const mockPreferences = [{ id: 1 }];
            const mockParams = { ProjectId: 456 };
            mockModels.NotificationPreference.findAll.mockResolvedValue(mockPreferences);

            const result = await concreteRequestAttachmentService.getNotificationPreferences(mockParams);

            expect(result).toBe(mockPreferences);
            expect(mockModels.NotificationPreference.findAll).toHaveBeenCalledWith({
                where: {
                    ProjectId: 456,
                    isDeleted: false
                },
                include: [
                    {
                        association: 'NotificationPreferenceItem',
                        where: { id: 2, isDeleted: false }
                    }
                ]
            });
        });
    });

    describe('Edge Cases and Error Handling', () => {
        it('should handle getDynamicModel with undefined domain name', async () => {
            mockInputData.user.domainName = undefined;
            mockModels.User.findOne.mockResolvedValue({ id: 1 });
            mockModels.Member.findOne.mockResolvedValue({ id: 1, isAccount: true, EnterpriseId: 1 });
            mockModels.Enterprise.findOne.mockResolvedValue({ name: 'fallbackdomain' });

            const result = await concreteRequestAttachmentService.getDynamicModel(mockInputData);

            expect(result).toBe(true);
        });

        it('should handle extractDomainInfo with missing user properties', () => {
            const inputDataWithMissingProps = {
                user: {},
                body: {},
                params: {}
            };

            expect(() => {
                concreteRequestAttachmentService.extractDomainInfo(inputDataWithMissingProps);
            }).toThrow();
        });

        it('should handle getConcreteRequestAttachments with database error', async () => {
            const mockError = new Error('Database connection failed');
            helper.getDynamicModel.mockRejectedValue(mockError);

            await concreteRequestAttachmentService.getConcreteRequestAttachments(mockInputData, mockDone);

            expect(mockDone).toHaveBeenCalledWith(null, mockError);
        });
    });
});