const wasteLogService = require('../wasteLogService');
const { WasteLog } = require('../models');

jest.mock('../models', () => ({
    WasteLog: {
        createWasteLog: jest.fn(),
        getAll: jest.fn(),
    },
}));

describe('wasteLogService', () => {
    let done;
    beforeEach(() => {
        jest.clearAllMocks();
        done = jest.fn();
    });

    describe('addWasteLog', () => {
        it('should add a waste log and call done with result', async () => {
            const inputData = { body: { some: 'data' } };
            const mockResult = { id: 1, some: 'data' };
            WasteLog.createWasteLog.mockResolvedValue(mockResult);

            await wasteLogService.addWasteLog(inputData, done);

            expect(WasteLog.createWasteLog).toHaveBeenCalledWith(inputData.body);
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should call done with error if createWasteLog throws', async () => {
            const inputData = { body: { some: 'data' } };
            const error = new Error('DB error');
            WasteLog.createWasteLog.mockRejectedValue(error);

            await wasteLogService.addWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        it('should handle empty body data', async () => {
            const inputData = { body: {} };
            const mockResult = { id: 1 };
            WasteLog.createWasteLog.mockResolvedValue(mockResult);

            await wasteLogService.addWasteLog(inputData, done);

            expect(WasteLog.createWasteLog).toHaveBeenCalledWith({});
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle null body data', async () => {
            const inputData = { body: null };
            const mockResult = { id: 1 };
            WasteLog.createWasteLog.mockResolvedValue(mockResult);

            await wasteLogService.addWasteLog(inputData, done);

            expect(WasteLog.createWasteLog).toHaveBeenCalledWith(null);
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle complete waste log data', async () => {
            const inputData = {
                body: {
                    hauler: 'Test Hauler',
                    wasteType: 'Construction',
                    quantity: '100',
                    landfillDestination: 'Test Landfill',
                    haulVehicle: 'Truck 123',
                    wasteTickets: 'TKT001',
                    projectId: 456,
                },
            };
            const mockResult = { id: 1, ...inputData.body };
            WasteLog.createWasteLog.mockResolvedValue(mockResult);

            await wasteLogService.addWasteLog(inputData, done);

            expect(WasteLog.createWasteLog).toHaveBeenCalledWith(inputData.body);
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle database connection error', async () => {
            const inputData = { body: { some: 'data' } };
            const error = new Error('Connection timeout');
            WasteLog.createWasteLog.mockRejectedValue(error);

            await wasteLogService.addWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        it('should handle validation error', async () => {
            const inputData = { body: { some: 'data' } };
            const error = new Error('Validation failed');
            error.name = 'SequelizeValidationError';
            WasteLog.createWasteLog.mockRejectedValue(error);

            await wasteLogService.addWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        it('should handle missing equData parameter', async () => {
            const mockResult = { id: 1 };
            WasteLog.createWasteLog.mockResolvedValue(mockResult);

            await wasteLogService.addWasteLog({}, done);

            expect(WasteLog.createWasteLog).toHaveBeenCalledWith(undefined);
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });
    });

    describe('listWasteLog', () => {
        it('should list waste logs and call done with result', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockResult = [{ id: 1 }, { id: 2 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 123 },
                10,
                0,
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should call done with error if getAll throws', async () => {
            WasteLog.getAll.mockRejectedValue(new Error('DB error'));
            await wasteLogService.listWasteLog(
                {
                    params: { pageNo: 1, pageSize: 10 },
                    body: { sort: 'asc', sortByField: 'id' },
                    query: { projectId: 123 },
                },
                done,
            );
            expect(done).toHaveBeenCalledWith(null, expect.any(Error));
        });

        it('should handle missing pagination params gracefully', async () => {
            WasteLog.getAll.mockResolvedValue([]);
            await wasteLogService.listWasteLog(
                {
                    params: {},
                    body: {},
                    query: { projectId: 123 },
                },
                done,
            );
            expect(done).toHaveBeenCalledWith([], false);
        });

        it('should sort rows if isFilter is set and rows exist', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockRows = [{ wasteLogName: 'B' }, { wasteLogName: 'a' }];
            WasteLog.getAll.mockResolvedValue({ rows: mockRows });

            await wasteLogService.listWasteLog(inputData, done);
            expect(done).toHaveBeenCalledWith(
                { rows: [{ wasteLogName: 'a' }, { wasteLogName: 'B' }] },
                false,
            );
        });

        it('should sort array if isFilter is set and result is array', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockArr = [{ wasteLogName: 'B' }, { wasteLogName: 'a' }];
            WasteLog.getAll.mockResolvedValue(mockArr);

            await wasteLogService.listWasteLog(inputData, done);
            expect(done).toHaveBeenCalledWith([{ wasteLogName: 'a' }, { wasteLogName: 'B' }], false);
        });

        it('should handle empty rows with isFilter', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            WasteLog.getAll.mockResolvedValue({ rows: [] });

            await wasteLogService.listWasteLog(inputData, done);
            expect(done).toHaveBeenCalledWith({ rows: [] }, false);
        });

        // Additional comprehensive test cases for better coverage
        it('should handle NaN pagination values gracefully', async () => {
            const inputData = {
                params: { pageNo: 'invalid', pageSize: 'invalid' },
                body: { sort: 'desc', sortByField: 'createdAt' },
                query: { projectId: 456 },
            };
            const mockResult = { rows: [], count: 0 };
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 456 },
                NaN,
                NaN,
                {},
                'desc',
                'createdAt',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle zero page number correctly', async () => {
            const inputData = {
                params: { pageNo: 0, pageSize: 5 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 789 },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 789 },
                5,
                -5, // (0-1) * 5 = -5
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle large page numbers', async () => {
            const inputData = {
                params: { pageNo: 1000, pageSize: 50 },
                body: { sort: 'desc', sortByField: 'updatedAt' },
                query: { projectId: 999 },
            };
            const mockResult = { rows: [], count: 0 };
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 999 },
                50,
                49950, // (1000-1) * 50
                {},
                'desc',
                'updatedAt',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle missing sort parameters', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: {},
                query: { projectId: 123 },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 123 },
                10,
                0,
                {},
                undefined,
                undefined,
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle null body gracefully', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: null,
                query: { projectId: 123 },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 123 },
                10,
                0,
                {},
                undefined,
                undefined,
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle missing projectId in query', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
                query: {},
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: undefined },
                10,
                0,
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle database timeout error', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const error = new Error('Connection timeout');
            error.name = 'SequelizeConnectionError';
            WasteLog.getAll.mockRejectedValue(error);

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        it('should handle validation error from database', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const error = new Error('Invalid sort field');
            error.name = 'SequelizeValidationError';
            WasteLog.getAll.mockRejectedValue(error);

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(null, error);
        });

        it('should handle complex sorting with isFilter and mixed case names', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockRows = [
                { wasteLogName: 'Zebra' },
                { wasteLogName: 'apple' },
                { wasteLogName: 'Banana' },
                { wasteLogName: 'cherry' },
            ];
            WasteLog.getAll.mockResolvedValue({ rows: mockRows });

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(
                {
                    rows: [
                        { wasteLogName: 'apple' },
                        { wasteLogName: 'Banana' },
                        { wasteLogName: 'cherry' },
                        { wasteLogName: 'Zebra' },
                    ],
                },
                false,
            );
        });

        it('should handle isFilter with array result and mixed case names', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockArr = [
                { wasteLogName: 'Zebra' },
                { wasteLogName: 'apple' },
                { wasteLogName: 'Banana' },
            ];
            WasteLog.getAll.mockResolvedValue(mockArr);

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(
                [{ wasteLogName: 'apple' }, { wasteLogName: 'Banana' }, { wasteLogName: 'Zebra' }],
                false,
            );
        });

        it('should handle isFilter with null wasteLogName properties', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockRows = [
                { wasteLogName: null },
                { wasteLogName: 'apple' },
                { wasteLogName: undefined },
            ];
            WasteLog.getAll.mockResolvedValue({ rows: mockRows });

            await wasteLogService.listWasteLog(inputData, done);

            // The sort should handle null/undefined gracefully
            expect(done).toHaveBeenCalledWith({ rows: expect.any(Array) }, false);
        });

        it('should handle isFilter false explicitly', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: false, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockResult = [{ wasteLogName: 'B' }, { wasteLogName: 'a' }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            // Should not sort when isFilter is false
            expect(done).toHaveBeenCalledWith([{ wasteLogName: 'B' }, { wasteLogName: 'a' }], false);
        });

        it('should handle very large datasets with isFilter', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 1000 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockRows = Array.from({ length: 100 }, (_, i) => ({
                wasteLogName: `Item${String(i).padStart(3, '0')}`,
            }));
            WasteLog.getAll.mockResolvedValue({ rows: mockRows });

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith({ rows: expect.any(Array) }, false);
            const result = done.mock.calls[0][0];
            expect(result.rows).toHaveLength(100);
            expect(result.rows[0].wasteLogName).toBe('Item000');
        });

        it('should handle string page numbers', async () => {
            const inputData = {
                params: { pageNo: '2', pageSize: '15' },
                body: { sort: 'desc', sortByField: 'createdAt' },
                query: { projectId: 456 },
            };
            const mockResult = [{ id: 3 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 456 },
                15,
                15, // (2-1) * 15 = 15
                {},
                'desc',
                'createdAt',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle negative page numbers', async () => {
            const inputData = {
                params: { pageNo: -1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 123 },
                10,
                -20, // (-1-1) * 10 = -20
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle missing query object', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: undefined },
                10,
                0,
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle undefined params', async () => {
            const inputData = {
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 123 },
                NaN,
                NaN,
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle edge case with decimal page numbers', async () => {
            const inputData = {
                params: { pageNo: 1.5, pageSize: 10.7 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: 123 },
                10.7,
                5.35, // (1.5-1) * 10.7 = 5.35
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle empty string projectId', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { sort: 'asc', sortByField: 'id' },
                query: { projectId: '' },
            };
            const mockResult = [{ id: 1 }];
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(WasteLog.getAll).toHaveBeenCalledWith(
                { isDeleted: false, projectId: '' },
                10,
                0,
                {},
                'asc',
                'id',
            );
            expect(done).toHaveBeenCalledWith(mockResult, false);
        });

        it('should handle isFilter with empty array result', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            WasteLog.getAll.mockResolvedValue([]);

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith([], false);
        });

        it('should handle isFilter with object result but no rows property', async () => {
            const inputData = {
                params: { pageNo: 1, pageSize: 10 },
                body: { isFilter: true, sort: 'asc', sortByField: 'id' },
                query: { projectId: 123 },
            };
            const mockResult = { count: 0 };
            WasteLog.getAll.mockResolvedValue(mockResult);

            await wasteLogService.listWasteLog(inputData, done);

            expect(done).toHaveBeenCalledWith(mockResult, false);
        });
    });
});
