const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => 'mocked-multer-middleware'),
  }));
  multer.memoryStorage = jest.fn(() => 'mocked-memory-storage');
  return multer;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  LocationController: {
    addLocation: jest.fn(),
    editLocation: jest.fn(),
    listLocation: jest.fn(),
    deleteLocation: jest.fn(),
    getLocation: jest.fn(),
    sampleExcelDownload: jest.fn(),
    getLocations: jest.fn(),
    importLocation: jest.fn(),
    listLocations: jest.fn(),
    setLocationNotificationPreference: jest.fn(),
    updateMemberLocationPreference: jest.fn(),
    createDefaultLocationPathForExistingProject: jest.fn(),
    createDefaultLocationIDForExistingBookings: jest.fn(),
    findAvailableSlots: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  locationValidation: {
    addLocation: jest.fn(),
    listLocation: jest.fn(),
    deleteLocation: jest.fn(),
    getLocation: jest.fn(),
    sampleExcelFileDownload: jest.fn(),
    getLocations: jest.fn(),
    importLocation: jest.fn(),
    memberLocationPreference: jest.fn(),
    findAvailableTimeSlot: jest.fn(),
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
  isProjectAdminOnly: jest.fn(),
  isAdmin: jest.fn(),
  isAccountAdmin: jest.fn(),
}));

describe('locationRoute', () => {
  let router;
  let locationRoute;
  let LocationController;
  let passportConfig;
  let locationValidation;
  let checkAdmin;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
      put: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    locationRoute = require('../locationRoute');
    const controllers = require('../../controllers');
    LocationController = controllers.LocationController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    locationValidation = validations.locationValidation;
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = locationRoute.router;

      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify that routes are configured
      expect(router.post).toHaveBeenCalled();
      expect(router.get).toHaveBeenCalled();
      expect(router.put).toHaveBeenCalled();
      expect(router.delete).toHaveBeenCalled();
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = locationRoute.router;
      const result2 = locationRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof locationRoute).toBe('object');
      expect(locationRoute).toHaveProperty('router');

      const descriptor = Object.getOwnPropertyDescriptor(locationRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(locationRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
