const jwt = require('jsonwebtoken');
const { token } = require('../jwtGenerator');

jest.mock('jsonwebtoken');

describe('jwtGenerator', () => {
  beforeEach(() => {
    process.env.JWT_SECRET = 'test-secret';
    jest.clearAllMocks();
  });

  it('should generate a JWT with correct payload and options', () => {
    const user = { id: 42, domainName: 'testdomain' };
    jwt.sign.mockReturnValue('mocked.jwt.token');

    const result = token(user);

    expect(jwt.sign).toHaveBeenCalledWith(
      {
        iss: 'folloit',
        sub: {
          id: 42,
          domainName: 'testdomain',
        },
      },
      'test-secret',
      { expiresIn: '48h' },
    );
    expect(result).toBe('mocked.jwt.token');
  });
});
