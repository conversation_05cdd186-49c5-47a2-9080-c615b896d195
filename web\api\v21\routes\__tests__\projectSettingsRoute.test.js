const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    get: jest.fn(),
    put: jest.fn(),
  })),
}));

jest.mock('../../controllers', () => ({
  ProjectSettingsController: {
    getProjectSettings: jest.fn(),
    updateProjectSettings: jest.fn(),
    setDefaultDeliveryWindow: jest.fn(),
    proxyDownloadFile: jest.fn(),
  },
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

describe('ProjectSettingsRoute', () => {
  let router;
  let ProjectSettingsRoute;
  let ProjectSettingsController;
  let passportConfig;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      get: jest.fn(),
      put: jest.fn(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    ProjectSettingsRoute = require('../projectSettingsRoute');
    const controllers = require('../../controllers');
    ProjectSettingsController = controllers.ProjectSettingsController;
    passportConfig = require('../../config/passport');
  });

  describe('router', () => {
    it('should create a router instance', () => {
      const result = ProjectSettingsRoute.router;
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);
    });

    it('should register GET /get_projectDetails route with authentication', () => {
      ProjectSettingsRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_projectDetails',
        passportConfig.isAuthenticated,
        ProjectSettingsController.getProjectSettings,
      );
    });

    it('should register PUT / route with authentication', () => {
      ProjectSettingsRoute.router;

      expect(router.put).toHaveBeenCalledWith(
        '/',
        passportConfig.isAuthenticated,
        ProjectSettingsController.updateProjectSettings,
      );
    });

    it('should register GET /set_default_delivery_window route without authentication', () => {
      ProjectSettingsRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/set_default_delivery_window',
        ProjectSettingsController.setDefaultDeliveryWindow,
      );
    });

    it('should register GET /get_guest_projectDetails route without authentication', () => {
      ProjectSettingsRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/get_guest_projectDetails',
        ProjectSettingsController.getProjectSettings,
      );
    });

    it('should register GET /proxy_download route without authentication', () => {
      ProjectSettingsRoute.router;

      expect(router.get).toHaveBeenCalledWith(
        '/proxy_download',
        ProjectSettingsController.proxyDownloadFile,
      );
    });
  });
});
