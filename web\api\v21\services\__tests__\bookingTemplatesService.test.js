const status = require('http-status');
const bookingTemplatesService = require('../bookingTemplatesService');
const { BookingTemplates, Project, Sequelize } = require('../../models');
const ApiError = require('../../helpers/apiError');

// Mock dependencies
jest.mock('../../models', () => ({
    BookingTemplates: {
        findOne: jest.fn(),
        createTempalte: jest.fn(),
        updateInstance: jest.fn(),
        getTemplates: jest.fn(),
        get: jest.fn(),
        update: jest.fn()
    },
    Project: {
        getProject: jest.fn()
    },
    Sequelize: {
        and: jest.fn(),
        Op: {
            in: 'in'
        }
    }
}));

describe('BookingTemplatesService', () => {
    let mockUser;
    let mockPayload;
    let mockProject;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Setup common test data
        mockUser = {
            id: 1,
            name: 'Test User'
        };

        mockPayload = {
            template_name: 'Test Template',
            ProjectId: 1,
            ParentCompanyId: 1,
            recurrence: { frequency: 'daily' }
        };

        mockProject = {
            id: 1,
            name: 'Test Project'
        };
    });

    describe('createTemplate', () => {
        it('should successfully create a template', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...mockPayload });

            const result = await bookingTemplatesService.createTemplate(mockUser, mockPayload);

            expect(Project.getProject).toHaveBeenCalledWith({ id: mockPayload.ProjectId });
            expect(BookingTemplates.findOne).toHaveBeenCalled();
            expect(BookingTemplates.createTempalte).toHaveBeenCalled();
            expect(result).toHaveProperty('id', 1);
        });

        it('should throw error when project does not exist', async () => {
            Project.getProject.mockResolvedValue(null);

            await expect(bookingTemplatesService.createTemplate(mockUser, mockPayload))
                .rejects.toThrow('Project does not exist.');
        });

        it('should throw error when template name already exists', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue({ id: 1, ...mockPayload });

            await expect(bookingTemplatesService.createTemplate(mockUser, mockPayload))
                .rejects.toThrow('Template Name Already exist.');
        });

        it('should handle string recurrence object', async () => {
            const stringRecurrence = JSON.stringify({ frequency: 'daily' });
            const payloadWithStringRecurrence = { ...mockPayload, recurrence: stringRecurrence };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...payloadWithStringRecurrence });

            await bookingTemplatesService.createTemplate(mockUser, payloadWithStringRecurrence);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                recurrence: stringRecurrence
            }));
        });

        it('should handle null recurrence object', async () => {
            const payloadWithNullRecurrence = { ...mockPayload, recurrence: null };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...payloadWithNullRecurrence });

            await bookingTemplatesService.createTemplate(mockUser, payloadWithNullRecurrence);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                recurrence: 'null'
            }));
        });

        it('should handle undefined recurrence object', async () => {
            const payloadWithUndefinedRecurrence = { ...mockPayload, recurrence: undefined };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...payloadWithUndefinedRecurrence });

            await bookingTemplatesService.createTemplate(mockUser, payloadWithUndefinedRecurrence);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                recurrence: undefined
            }));
        });

        it('should handle complex recurrence object', async () => {
            const complexRecurrence = {
                frequency: 'weekly',
                days: ['monday', 'wednesday'],
                endDate: '2024-12-31',
                exceptions: ['2024-12-25']
            };
            const payloadWithComplexRecurrence = { ...mockPayload, recurrence: complexRecurrence };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...payloadWithComplexRecurrence });

            await bookingTemplatesService.createTemplate(mockUser, payloadWithComplexRecurrence);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                recurrence: JSON.stringify(complexRecurrence)
            }));
        });

        it('should throw error when Project.getProject throws an error', async () => {
            const dbError = new Error('Database connection failed');
            Project.getProject.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.createTemplate(mockUser, mockPayload))
                .rejects.toThrow('Database connection failed');
        });

        it('should throw error when BookingTemplates.findOne throws an error', async () => {
            const dbError = new Error('Query failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.createTemplate(mockUser, mockPayload))
                .rejects.toThrow('Query failed');
        });

        it('should throw error when BookingTemplates.createTempalte throws an error', async () => {
            const dbError = new Error('Insert failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.createTemplate(mockUser, mockPayload))
                .rejects.toThrow('Insert failed');
        });

        it('should set createdBy from user details', async () => {
            const userWithDifferentId = { id: 999, name: 'Different User' };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...mockPayload });

            await bookingTemplatesService.createTemplate(userWithDifferentId, mockPayload);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                createdBy: 999
            }));
        });
    });

    describe('updateTemplate', () => {
        it('should successfully update a template', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.updateInstance.mockResolvedValue({ id: 1, ...mockPayload });

            const result = await bookingTemplatesService.updateTemplate(mockUser, { ...mockPayload, id: 1 });

            expect(Project.getProject).toHaveBeenCalledWith({ id: mockPayload.ProjectId });
            expect(BookingTemplates.findOne).toHaveBeenCalled();
            expect(BookingTemplates.updateInstance).toHaveBeenCalled();
            expect(result).toHaveProperty('id', 1);
        });

        it('should throw error when updating non-existent project', async () => {
            Project.getProject.mockResolvedValue(null);

            await expect(bookingTemplatesService.updateTemplate(mockUser, mockPayload))
                .rejects.toThrow('Project does not exist.');
        });

        it('should throw error when updating with existing template name', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue({ id: 2, ...mockPayload });

            await expect(bookingTemplatesService.updateTemplate(mockUser, { ...mockPayload, id: 1 }))
                .rejects.toThrow('Template Name Already exist.');
        });

        it('should handle string recurrence in update', async () => {
            const stringRecurrence = JSON.stringify({ frequency: 'monthly' });
            const updatePayload = { ...mockPayload, id: 1, recurrence: stringRecurrence };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.updateInstance.mockResolvedValue({ id: 1, ...updatePayload });

            await bookingTemplatesService.updateTemplate(mockUser, updatePayload);

            expect(BookingTemplates.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                recurrence: stringRecurrence
            }));
        });

        it('should handle object recurrence in update', async () => {
            const objectRecurrence = { frequency: 'yearly', month: 'january' };
            const updatePayload = { ...mockPayload, id: 1, recurrence: objectRecurrence };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.updateInstance.mockResolvedValue({ id: 1, ...updatePayload });

            await bookingTemplatesService.updateTemplate(mockUser, updatePayload);

            expect(BookingTemplates.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                recurrence: JSON.stringify(objectRecurrence)
            }));
        });

        it('should throw error when Project.getProject fails in update', async () => {
            const dbError = new Error('Database error in update');
            Project.getProject.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.updateTemplate(mockUser, { ...mockPayload, id: 1 }))
                .rejects.toThrow('Database error in update');
        });

        it('should throw error when BookingTemplates.findOne fails in update', async () => {
            const dbError = new Error('Find query failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.updateTemplate(mockUser, { ...mockPayload, id: 1 }))
                .rejects.toThrow('Find query failed');
        });

        it('should throw error when BookingTemplates.updateInstance fails', async () => {
            const dbError = new Error('Update failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.updateInstance.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.updateTemplate(mockUser, { ...mockPayload, id: 1 }))
                .rejects.toThrow('Update failed');
        });

        it('should set createdBy from user details in update', async () => {
            const userWithDifferentId = { id: 777, name: 'Update User' };
            const updatePayload = { ...mockPayload, id: 1 };

            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.updateInstance.mockResolvedValue({ id: 1, ...updatePayload });

            await bookingTemplatesService.updateTemplate(userWithDifferentId, updatePayload);

            expect(BookingTemplates.updateInstance).toHaveBeenCalledWith(1, expect.objectContaining({
                createdBy: 777
            }));
        });
    });

    describe('getTemplates', () => {
        const mockReq = {
            params: { ProjectId: 1 },
            query: { pageNo: 1, pageSize: 10, ParentCompanyId: 1 },
            user: { id: 1 }
        };

        it('should get templates with pagination', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockResolvedValue({ rows: [], count: 0 });

            const result = await bookingTemplatesService.getTemplates(mockReq);

            expect(Project.getProject).toHaveBeenCalledWith({ id: mockReq.params.ProjectId });
            expect(BookingTemplates.get).toHaveBeenCalled();
            expect(result).toHaveProperty('rows');
        });

        it('should get templates for dropdown', async () => {
            const dropdownReq = {
                ...mockReq,
                query: { ...mockReq.query, isDropdown: true }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.getTemplates.mockResolvedValue([]);

            const result = await bookingTemplatesService.getTemplates(dropdownReq);

            expect(Project.getProject).toHaveBeenCalledWith({ id: mockReq.params.ProjectId });
            expect(BookingTemplates.getTemplates).toHaveBeenCalled();
            expect(Array.isArray(result)).toBe(true);
        });

        it('should throw error when project does not exist', async () => {
            Project.getProject.mockResolvedValue(null);

            await expect(bookingTemplatesService.getTemplates(mockReq))
                .rejects.toThrow('Project does not exist.');
        });

        it('should handle pagination with different page numbers', async () => {
            const paginationReq = {
                ...mockReq,
                query: { ...mockReq.query, pageNo: 3, pageSize: 5 }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockResolvedValue({ rows: [], count: 0 });

            await bookingTemplatesService.getTemplates(paginationReq);

            const expectedOffset = (3 - 1) * 5; // (pageNo - 1) * pageSize
            expect(BookingTemplates.get).toHaveBeenCalledWith(
                expect.any(Object),
                5, // pageSize
                expectedOffset, // offset
                undefined, // sort
                undefined // sortColumn
            );
        });

        it('should handle sorting parameters', async () => {
            const sortReq = {
                ...mockReq,
                query: { ...mockReq.query, sort: 'desc', sortColumn: 'created_at' }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockResolvedValue({ rows: [], count: 0 });

            await bookingTemplatesService.getTemplates(sortReq);

            expect(BookingTemplates.get).toHaveBeenCalledWith(
                expect.any(Object),
                10, // pageSize
                0, // offset
                'desc', // sort
                'created_at' // sortColumn
            );
        });

        it('should pass correct attributes to BookingTemplates.get', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockResolvedValue({ rows: [], count: 0 });

            await bookingTemplatesService.getTemplates(mockReq);

            expect(BookingTemplates.get).toHaveBeenCalledWith(
                expect.objectContaining({
                    ProjectId: mockReq.params.ProjectId,
                    ParentCompanyId: mockReq.query.ParentCompanyId,
                    isDeleted: false,
                    createdBy: +mockReq.user.id
                }),
                10, // pageSize
                0, // offset
                undefined, // sort
                undefined // sortColumn
            );
        });

        it('should pass correct attributes to BookingTemplates.getTemplates for dropdown', async () => {
            const dropdownReq = {
                ...mockReq,
                query: { ...mockReq.query, isDropdown: true }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.getTemplates.mockResolvedValue([]);

            await bookingTemplatesService.getTemplates(dropdownReq);

            expect(BookingTemplates.getTemplates).toHaveBeenCalledWith(
                expect.objectContaining({
                    ProjectId: dropdownReq.params.ProjectId,
                    ParentCompanyId: dropdownReq.query.ParentCompanyId,
                    isDeleted: false,
                    createdBy: +dropdownReq.user.id
                })
            );
        });

        it('should throw error when Project.getProject fails in getTemplates', async () => {
            const dbError = new Error('Project query failed');
            Project.getProject.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.getTemplates(mockReq))
                .rejects.toThrow('Project query failed');
        });

        it('should throw error when BookingTemplates.get fails', async () => {
            const dbError = new Error('Templates query failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.getTemplates(mockReq))
                .rejects.toThrow('Templates query failed');
        });

        it('should throw error when BookingTemplates.getTemplates fails for dropdown', async () => {
            const dropdownReq = {
                ...mockReq,
                query: { ...mockReq.query, isDropdown: true }
            };
            const dbError = new Error('Dropdown query failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.getTemplates.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.getTemplates(dropdownReq))
                .rejects.toThrow('Dropdown query failed');
        });
    });

    describe('deleteTemplate', () => {
        const mockReq = {
            params: { ProjectId: 1 },
            body: { id: [1, 2], ParentCompanyId: 1 }
        };

        it('should delete specific templates', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockResolvedValue([2]);

            await bookingTemplatesService.deleteTemplate(mockReq);

            expect(Project.getProject).toHaveBeenCalledWith({ id: mockReq.params.ProjectId });
            expect(BookingTemplates.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.any(Object)
            );
        });

        it('should delete all templates when selectAll is true', async () => {
            const selectAllReq = {
                ...mockReq,
                body: { ...mockReq.body, selectAll: true },
                user: { id: 1 }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockResolvedValue([5]);

            await bookingTemplatesService.deleteTemplate(selectAllReq);

            expect(BookingTemplates.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.objectContaining({
                    where: expect.objectContaining({
                        ProjectId: +selectAllReq.params.ProjectId,
                        createdBy: +selectAllReq.user.id
                    })
                })
            );
        });

        it('should throw error when project does not exist', async () => {
            Project.getProject.mockResolvedValue(null);

            await expect(bookingTemplatesService.deleteTemplate(mockReq))
                .rejects.toThrow('Project does not exist.');
        });

        it('should handle empty id array in specific delete', async () => {
            const emptyIdReq = {
                ...mockReq,
                body: { ...mockReq.body, id: [] }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockResolvedValue([0]);

            await bookingTemplatesService.deleteTemplate(emptyIdReq);

            expect(BookingTemplates.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.objectContaining({
                    where: expect.objectContaining({
                        id: { in: [] }
                    })
                })
            );
        });

        it('should handle single id in array for specific delete', async () => {
            const singleIdReq = {
                ...mockReq,
                body: { ...mockReq.body, id: [5] }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockResolvedValue([1]);

            await bookingTemplatesService.deleteTemplate(singleIdReq);

            expect(BookingTemplates.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.objectContaining({
                    where: expect.objectContaining({
                        id: { in: [5] },
                        ProjectId: +singleIdReq.params.ProjectId,
                        ParentCompanyId: singleIdReq.body.ParentCompanyId,
                        isDeleted: false
                    })
                })
            );
        });

        it('should handle multiple ids for specific delete', async () => {
            const multipleIdReq = {
                ...mockReq,
                body: { ...mockReq.body, id: [1, 2, 3, 4] }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockResolvedValue([4]);

            await bookingTemplatesService.deleteTemplate(multipleIdReq);

            expect(BookingTemplates.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.objectContaining({
                    where: expect.objectContaining({
                        id: { in: [1, 2, 3, 4] }
                    })
                })
            );
        });

        it('should throw error when Project.getProject fails in deleteTemplate', async () => {
            const dbError = new Error('Project lookup failed');
            Project.getProject.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.deleteTemplate(mockReq))
                .rejects.toThrow('Project lookup failed');
        });

        it('should throw error when BookingTemplates.update fails for specific delete', async () => {
            const dbError = new Error('Update operation failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.deleteTemplate(mockReq))
                .rejects.toThrow('Update operation failed');
        });

        it('should throw error when BookingTemplates.update fails for selectAll delete', async () => {
            const selectAllReq = {
                ...mockReq,
                body: { ...mockReq.body, selectAll: true },
                user: { id: 1 }
            };
            const dbError = new Error('Bulk update failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.deleteTemplate(selectAllReq))
                .rejects.toThrow('Bulk update failed');
        });

        it('should handle different ParentCompanyId in delete', async () => {
            const differentCompanyReq = {
                ...mockReq,
                body: { ...mockReq.body, ParentCompanyId: 999 }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.update.mockResolvedValue([2]);

            await bookingTemplatesService.deleteTemplate(differentCompanyReq);

            expect(BookingTemplates.update).toHaveBeenCalledWith(
                { isDeleted: true },
                expect.objectContaining({
                    where: expect.objectContaining({
                        ParentCompanyId: 999
                    })
                })
            );
        });
    });

    describe('getTemplate', () => {
        const mockReq = {
            query: {
                id: 1,
                ProjectId: 1,
                ParentCompanyId: 1
            }
        };

        it('should get template by id', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue({ id: 1, ...mockPayload });

            const result = await bookingTemplatesService.getTemplate(mockReq);

            expect(Project.getProject).toHaveBeenCalledWith({ id: mockReq.query.ProjectId });
            expect(BookingTemplates.findOne).toHaveBeenCalled();
            expect(result).toHaveProperty('id', 1);
        });

        it('should throw error when template does not exist', async () => {
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);

            await expect(bookingTemplatesService.getTemplate(mockReq))
                .rejects.toThrow('Template does not exist.');
        });

        it('should throw error when project does not exist', async () => {
            Project.getProject.mockResolvedValue(null);

            await expect(bookingTemplatesService.getTemplate(mockReq))
                .rejects.toThrow('Project does not exist.');
        });

        it('should call Sequelize.and with correct parameters', async () => {
            const { Sequelize } = require('../../models');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue({ id: 1, ...mockPayload });
            Sequelize.and.mockReturnValue({});

            await bookingTemplatesService.getTemplate(mockReq);

            expect(Sequelize.and).toHaveBeenCalledWith(
                { id: mockReq.query.id },
                { ProjectId: mockReq.query.ProjectId },
                { ParentCompanyId: mockReq.query.ParentCompanyId }
            );
        });

        it('should handle different query parameters', async () => {
            const differentReq = {
                query: {
                    id: 999,
                    ProjectId: 888,
                    ParentCompanyId: 777
                }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue({ id: 999, template_name: 'Different Template' });

            const result = await bookingTemplatesService.getTemplate(differentReq);

            expect(Project.getProject).toHaveBeenCalledWith({ id: 888 });
            expect(result).toHaveProperty('id', 999);
        });

        it('should throw error when Project.getProject fails in getTemplate', async () => {
            const dbError = new Error('Project fetch failed');
            Project.getProject.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.getTemplate(mockReq))
                .rejects.toThrow('Project fetch failed');
        });

        it('should throw error when BookingTemplates.findOne fails in getTemplate', async () => {
            const dbError = new Error('Template fetch failed');
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockRejectedValue(dbError);

            await expect(bookingTemplatesService.getTemplate(mockReq))
                .rejects.toThrow('Template fetch failed');
        });

        it('should return template with all properties', async () => {
            const fullTemplate = {
                id: 1,
                template_name: 'Full Template',
                ProjectId: 1,
                ParentCompanyId: 1,
                recurrence: '{"frequency":"daily"}',
                createdBy: 1,
                createdAt: '2024-01-01',
                updatedAt: '2024-01-02'
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(fullTemplate);

            const result = await bookingTemplatesService.getTemplate(mockReq);

            expect(result).toEqual(fullTemplate);
        });

        it('should handle string id in query parameters', async () => {
            const stringIdReq = {
                query: {
                    id: '123',
                    ProjectId: '456',
                    ParentCompanyId: '789'
                }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue({ id: 123, template_name: 'String ID Template' });

            await bookingTemplatesService.getTemplate(stringIdReq);

            expect(Project.getProject).toHaveBeenCalledWith({ id: '456' });
        });
    });

    // Additional edge case tests
    describe('Edge Cases and Error Handling', () => {
        const mockReq = {
            params: { ProjectId: 1 },
            query: { pageNo: 1, pageSize: 10, ParentCompanyId: 1 },
            user: { id: 1 }
        };

        it('should handle user object without id in createTemplate', async () => {
            const userWithoutId = { name: 'User Without ID' };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...mockPayload });

            await bookingTemplatesService.createTemplate(userWithoutId, mockPayload);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                createdBy: undefined
            }));
        });

        it('should handle empty payload in createTemplate', async () => {
            const emptyPayload = {};
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1 });

            await bookingTemplatesService.createTemplate(mockUser, emptyPayload);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                createdBy: mockUser.id,
                recurrence: undefined
            }));
        });

        it('should handle payload with extra properties', async () => {
            const payloadWithExtra = {
                ...mockPayload,
                extraProperty: 'should be preserved',
                anotherExtra: 123
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.findOne.mockResolvedValue(null);
            BookingTemplates.createTempalte.mockResolvedValue({ id: 1, ...payloadWithExtra });

            await bookingTemplatesService.createTemplate(mockUser, payloadWithExtra);

            expect(BookingTemplates.createTempalte).toHaveBeenCalledWith(expect.objectContaining({
                extraProperty: 'should be preserved',
                anotherExtra: 123
            }));
        });

        it('should handle zero values in pagination', async () => {
            const zeroPageReq = {
                ...mockReq,
                query: { ...mockReq.query, pageNo: 0, pageSize: 0 }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockResolvedValue({ rows: [], count: 0 });

            await bookingTemplatesService.getTemplates(zeroPageReq);

            const expectedOffset = (0 - 1) * 0; // Should be -0 = 0
            expect(BookingTemplates.get).toHaveBeenCalledWith(
                expect.any(Object),
                0, // pageSize
                expectedOffset, // offset
                undefined,
                undefined
            );
        });

        it('should handle negative values in pagination', async () => {
            const negativePageReq = {
                ...mockReq,
                query: { ...mockReq.query, pageNo: -1, pageSize: -5 }
            };
            Project.getProject.mockResolvedValue(mockProject);
            BookingTemplates.get.mockResolvedValue({ rows: [], count: 0 });

            await bookingTemplatesService.getTemplates(negativePageReq);

            const expectedOffset = (-1 - 1) * -5; // Should be 10
            expect(BookingTemplates.get).toHaveBeenCalledWith(
                expect.any(Object),
                -5, // pageSize
                expectedOffset, // offset
                undefined,
                undefined
            );
        });
    });
}); 