const UserController = require('./UserController');
const AdminController = require('./AdminController');
const AuthController = require('./AuthController');
const StripeController = require('./StripeController');
const ProjectController = require('./ProjectController');
const EquipmentController = require('./EquipmentController');
const GateController = require('./GateController');
const CompanyController = require('./CompanyController');
const MemberController = require('./MemberController');
const DeliveryController = require('./DeliveryController');
const inspectionController = require('./inspectionController');
const OverRideController = require('./OverRideController');
const RestrictMailController = require('./RestrictMailController');
const AddressController = require('./AddressController');
const DefineController = require('./DefineController');
const VoidController = require('./VoidController');
const AttachementController = require('./AttachementController');
const HistoryController = require('./HistoryController');
const CommentController = require('./CommentController');
const CornController = require('./CornController');
const CalendarController = require('./CalendarController');
const NotificationController = require('./NotificationController');
const AccountCornController = require('./AccountCornController');
const DashboardController = require('./DashboardController');
const DeviceTokenController = require('./DeviceTokenController');
const BillingController = require('./BillingController');
const CraneRequestController = require('./CraneRequestController');
const craneRequestAttachmentController = require('./craneRequestAttachmentController');
const craneRequestCommentController = require('./craneRequestCommentController');
const craneRequestHistoryController = require('./craneRequestHistoryController');
const calendarsettingsController = require('./calendarsettingsController');
const TimeZoneController = require('./TimeZoneController');
const NotificationPreferenceController = require('./NotificationPreferenceController');
const ConcreteRequestController = require('./ConcreteRequestController');
const concreteRequestAttachmentController = require('./concreteRequestAttachmentController');
const concreteRequestCommentController = require('./concreteRequestCommentController');
const concreteRequestHistoryController = require('./concreteRequestHistoryController');
const ReportController = require('./ReportController');
const ProjectSettingsController = require('./ProjectSettingsController');
const LocationController = require('./LocationController');
const BookingTemplatesController = require('./BookingTemplatesController');

module.exports = {
  UserController,
  AdminController,
  AuthController,
  StripeController,
  ProjectController,
  GateController,
  EquipmentController,
  CompanyController,
  MemberController,
  DeliveryController,
  inspectionController,
  OverRideController,
  RestrictMailController,
  AddressController,
  DefineController,
  VoidController,
  CommentController,
  HistoryController,
  AttachementController,
  CornController,
  CalendarController,
  NotificationController,
  AccountCornController,
  DashboardController,
  DeviceTokenController,
  BillingController,
  CraneRequestController,
  craneRequestAttachmentController,
  craneRequestCommentController,
  craneRequestHistoryController,
  calendarsettingsController,
  TimeZoneController,
  NotificationPreferenceController,
  ConcreteRequestController,
  concreteRequestAttachmentController,
  concreteRequestCommentController,
  concreteRequestHistoryController,
  ReportController,
  ProjectSettingsController,
  LocationController,
  BookingTemplatesController
};
