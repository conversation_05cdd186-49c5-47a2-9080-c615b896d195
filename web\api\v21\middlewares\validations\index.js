const userValidation = require('./userValidation');
const adminValidation = require('./adminValidation');
const stripeValidation = require('./stripeValidation');
const projectValidation = require('./projectValidation');
const equipmentValidation = require('./equipmentValidation');
const gateValidation = require('./gateValidation');
const companyValidation = require('./companyValidation');
const memberValidation = require('./memberValidation');
const deliveryValidation = require('./deliveryValidation');
const inspectionValidation = require('./inspectionValidation');
const restrictMailValidation = require('./restrictMailValidation');
const overRideValidation = require('./overRideValidation');
const addressValidation = require('./addressValidation');
const defineValidation = require('./defineValidation');
const voidValidation = require('./voidValidation');
const attachementValidation = require('./attachementValidation');
const historyValidation = require('./historyValidation');
const commentValidation = require('./commentValidation');
const calendarValidation = require('./calendarValidation');
const notificationValidation = require('./notificationValidation');
const dashboardValidation = require('./dashboardValidation');
const deviceTokenValidation = require('./deviceTokenValidation');
const billingValidation = require('./billingValidation');
const craneRequestValidation = require('./craneRequestValidation');
const craneRequestAttachmentValidation = require('./craneRequestAttachmentValidation');
const craneRequestCommentValidation = require('./craneRequestCommentValidation');
const craneRequestHistoryValidation = require('./craneRequestHistoryValidation');
const calendarSettingsValidation = require('./calendarSettingsValidation');
const notificationPreferenceValidation = require('./notificationPreferenceValidation');
const concreteRequestValidation = require('./concreteRequestValidation');
const concreteRequestAttachmentValidation = require('./concreteRequestAttachmentValidation');
const concreteRequestCommentValidation = require('./concreteRequestCommentValidation');
const concreteRequestHistoryValidation = require('./concreteRequestHistoryValidation');
const deliveryReportsValidation = require('./deliveryReportsValidation');
const craneReportsValidation = require('./craneReportsValidation');
const locationValidation = require('./locationValidation');
const templatesValidation = require('./templatesValidation');

module.exports = {
  userValidation,
  adminValidation,
  stripeValidation,
  projectValidation,
  equipmentValidation,
  gateValidation,
  companyValidation,
  memberValidation,
  deliveryValidation,
  inspectionValidation,
  restrictMailValidation,
  overRideValidation,
  addressValidation,
  defineValidation,
  voidValidation,
  attachementValidation,
  historyValidation,
  commentValidation,
  calendarValidation,
  notificationValidation,
  dashboardValidation,
  deviceTokenValidation,
  billingValidation,
  craneRequestValidation,
  craneRequestAttachmentValidation,
  craneRequestCommentValidation,
  craneRequestHistoryValidation,
  calendarSettingsValidation,
  notificationPreferenceValidation,
  concreteRequestValidation,
  concreteRequestAttachmentValidation,
  concreteRequestCommentValidation,
  concreteRequestHistoryValidation,
  deliveryReportsValidation,
  craneReportsValidation,
  locationValidation,
  templatesValidation
};
