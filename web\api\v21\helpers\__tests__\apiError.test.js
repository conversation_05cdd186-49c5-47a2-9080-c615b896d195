const httpStatus = require('http-status');
const ApiError = require('../apiError');
const ExtendableError = require('../extendableError');

describe('ApiError', () => {
  it('should be instance of ExtendableError and Error', () => {
    const err = new ApiError('fail', httpStatus.BAD_REQUEST);
    expect(err).toBeInstanceOf(ExtendableError);
    expect(err).toBeInstanceOf(Error);
  });
  it('should set message and status', () => {
    const err = new ApiError('fail', 418);
    expect(err.message).toBe('fail');
    expect(err.status).toBe(418);
  });
  it('should default status to INTERNAL_SERVER_ERROR', () => {
    const err = new ApiError('fail');
    expect(err.status).toBe(httpStatus.INTERNAL_SERVER_ERROR);
  });
});
