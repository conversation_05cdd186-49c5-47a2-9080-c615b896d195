/* eslint-disable no-await-in-loop */
const status = require('http-status');
const Cryptr = require('cryptr');
const {
  Sequelize,
  Enterprise,
  RestrictEmail,
  DeliveryPerson,
  NotificationPreferenceItem,
  NotificationPreference,
  CraneRequestResponsiblePerson,
  ConcreteRequestResponsiblePerson,
  Equipments,
  DeliveryRequest,
  CraneRequest,
  ConcreteRequest,
  CraneRequestHistory,
  DeliverHistory,
  ConcreteRequestHistory,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  User,
  Member,
  Company,
  Role,
  Project,
  ParentCompany,
  DeliveryPersonNotification,
  Notification,
} = require('../models');
const ApiError = require('../helpers/apiError');
const MAILER = require('../mailer');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const awsConfig = require('../middlewares/awsConfig');
const adminService = require('./adminService');
const { bcryptPassword } = require('./password');
const deepLinkService = require('./deepLinkService');
const { generatePassword } = require('../helpers/generatePassword');

const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');


let publicUser;
let publicMember;
let publicCompany;
let publicParentCompany;

const { Op } = Sequelize;

const memberService = {
  async checkDomain(memberData, inputData) {
    await this.getDynamicModel(inputData);
    const emailDomain = await adminService.emailDomain(memberData);
    const parentNewDetail = await ParentCompany.findOne({
      where: { emailDomainName: emailDomain },
    });
    return !!parentNewDetail;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();

    publicMember = modelData.Member;
    publicCompany = modelData.Company;
    publicParentCompany = modelData.ParentCompany;
    publicUser = modelData.User;
  },
  async checkExistMember(req, done) {
    await this.getDynamicModel(req);
    let isMemberExists = false;
    let isRestrictedEmail = false;
    const firstSplit = req.body.email.split('@')[1];
    const secondSplit = firstSplit.split('.');
    let emailDomainName;
    if (secondSplit.length === 2) {
      emailDomainName = firstSplit;
    } else if (secondSplit.length > 2) {
      const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
      emailDomainName = str;
    }
    const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
    if (restrict && req.body.RoleId === 2) {
      isRestrictedEmail = true;
    }
    const existUser = await User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', req.body.email),
        ),
        { isDeleted: false },
      ),
    });
    if (existUser) {
      const existMember = await Member.findOne({
        where: Sequelize.and({
          UserId: existUser.id,
          isDeleted: false,
          ProjectId: req.body.ProjectId,
        }),
      });
      if (existMember) {
        isMemberExists = true;
      }
    }
    if (req.body.RoleId) {
      done({ isRestrictedEmail }, false);
    } else {
      done({ isMemberExists, existUser }, false);
    }
  },
  async getNextMemberId(projectId) {
    const lastIdValue = await Member.findOne({
      where: { ProjectId: projectId, isDeleted: false },
      order: [['memberId', 'DESC']],
    });
    const newValue = JSON.parse(JSON.stringify(lastIdValue));
    return (newValue?.memberId ?? 0) + 1;
  },
  async createMember(memberData, inputData, projectDetails, roleDetails, loginUser, done) {
    const email = memberData.email;
    const existUser = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            email: { [Sequelize.Op.iLike]: `${email}` },
          },
        ],
      },
    });

    const memberId = await this.getNextMemberId(memberData.ProjectId);

    this.checkEmailValidation(inputData, async (validation, validationerror) => {
      if (validationerror) {
        return done(null, validationerror);
      }

      if (!existUser) {
        return this.handleNewUser(memberData, memberId, validation, inputData, done);
      }

      return this.handleExistingUser(memberData, existUser, validation, inputData, done);
    });
  },
  async handleNewUser(memberData, memberId, validation, inputData, done) {
    try {
      let newMember;
      let { domainName } = inputData.user;
      const { ParentCompanyId } = inputData.body;
      domainName = await this.getDynamicModel(inputData);
      const { email } = memberData;
      const existUser = await User.findOne({
        where: {
          [Op.and]: [
            {
              isDeleted: false,
              email: { [Sequelize.Op.iLike]: `${email}` },
            },
          ],
        },
      });
      const lastIdValue = await Member.findOne({
        where: { ProjectId: memberData.ProjectId, isDeleted: false },
        order: [['memberId', 'DESC']],
      });
      let id = 0;
      const newValue = JSON.parse(JSON.stringify(lastIdValue));
      if (newValue && newValue.memberId !== null && newValue.memberId !== undefined) {
        id = newValue.memberId;
      }
      const memberId = id + 1;
      this.checkEmailValidation(inputData, async (validation, validationerror) => {
        if (!validationerror) {
          if (existUser) {
            const existMember = await Member.findOne({
              where: Sequelize.and({
                UserId: existUser.id,
                isDeleted: false,
                CompanyId: { [Op.ne]: null },
              }),
            });
            if (!existMember) {
              this.checkRole(
                memberData,
                existUser,
                validation,
                inputData,
                async (roleResponse, roleError) => {
                  if (roleError) {
                    return done(null, roleError);
                  }
                  const existNewMember = await Member.findOne({
                    where: Sequelize.and({
                      UserId: existUser.id,
                      isDeleted: false,
                      CompanyId: { [Op.ne]: null },
                    }),
                  });
                  if (existNewMember) {
                    const memberParam = {
                      UserId: existUser.id,
                      firstName: existNewMember.firstName,
                      CompanyId: memberData.CompanyId,
                      RoleId: memberData.RoleId,
                      ParentCompanyId,
                      memberId: +memberId,
                      createdBy: loginUser.id,
                      phoneNumber: existNewMember.phoneNumber,
                      phoneCode: existNewMember.phoneCode,
                      ProjectId: memberData.ProjectId,
                      status: existNewMember.status,
                    };
                    newMember = await Member.createInstance(memberParam);
                    const getNotificationPreferenceItemsList =
                      await NotificationPreferenceItem.findAll({
                        where: { isDeleted: false },
                      });
                    const getProject = await Project.findOne({
                      where: {
                        isDeleted: false,
                        id: +memberData.ProjectId,
                      },
                      include: [
                        {
                          where: { isDeleted: false },
                          association: 'TimeZone',
                          required: false,
                          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
                        },
                      ],
                    });
                    const attr = {
                      time: '05:00',
                      timeFormat: 'AM',
                    };
                    let projectObject;
                    if (getProject) {
                      projectObject = getProject.toJSON();
                    }
                    attr.TimeZoneId = projectObject?.TimeZone?.id ?? 3;
                    await Member.update(attr, { where: { id: newMember.id } });
                   
                    await this.getNotificationListData(getNotificationPreferenceItemsList, newMember, memberData, inputData);
                    await this.handleNotificationCreation(memberData, DeliveryPersonNotification, Notification, domainName, projectDetails, memberParam);

                    memberParam.email = existUser.email;
                    memberParam.firstName = existUser.firstName;
                    memberParam.projectName = projectDetails.projectName;
                    memberParam.type = roleDetails.roleName;
                    delete newMember.password;
                    const loggedInMemberDetail = await Member.findOne({
                      where: Sequelize.and({
                        UserId: inputData.user.id,
                        isDeleted: false,
                        CompanyId: { [Op.ne]: null },
                      }),
                    });
                    if (+loggedInMemberDetail.RoleId === 1 && +memberData.RoleId === 2) {
                      const mailPayload = {
                        email: memberParam.email,
                        accountAdminName: inputData.user.firstName,
                        projectName: memberParam.projectName,
                        projectAdminName: memberParam.firstName,
                      };
                      this.sendMail(
                        mailPayload,
                        'assignPAtoNewProjectByAccountadmin',
                        `You were added to ${mailPayload.projectName} as Project Admin`,
                        'Assigned a New Project',
                        async (info, err) => {
                          if (err) {
                            const newError = new ApiError(err.message, status.BAD_REQUEST);
                            done(null, newError);
                          }
                          done(newMember, false);
                        },
                      );
                    } else {
                      this.sendMail(
                        memberParam,
                        'addproject',
                        'You were added as a member',
                        'Member Onboarded',
                        async (info, err) => {
                          if (err) {
                            const newError = new ApiError(err.message, status.BAD_REQUEST);
                            done(null, newError);
                          }
                          done(newMember, false);
                        },
                      );
                    }
                  } else {
                    const memberParam = {
                      UserId: existUser.id,
                      firstName: memberData.firstName,
                      CompanyId: memberData.CompanyId,
                      RoleId: memberData.RoleId,
                      ParentCompanyId,
                      memberId: +memberId,
                      phoneNumber: memberData.phoneNumber,
                      phoneCode: memberData.phoneCode,
                      createdBy: loginUser.id,
                      ProjectId: memberData.ProjectId,
                    };
                    newMember = await Member.createInstance(memberParam);
                    await this.createNotificationMember({memberData, newMember, DeliveryPersonNotification, Notification, domainName, projectDetails, memberParam, existUser})
                    memberParam.email = existUser.email;
                    memberParam.firstName = existUser.firstName;
                    memberParam.type = roleDetails.roleName;
                    memberParam.id = newMember.id;
                    memberParam.requestType = memberData.requestType;
                    memberParam.domainName = domainName;
                    this.sendMail(
                      memberParam,
                      'invite_member',
                      'You were added as a member',
                      'Member Onboarded',
                      async (info, err) => {
                        if (err) {
                          const newError = new ApiError(err.message, status.BAD_REQUEST);
                          done(null, newError);
                        }
                        delete newMember.password;
                        done(newMember, false);
                      },
                    );
                  }
                },
              );
            } else {
              done(null, { message: 'Member Already exist in this project.' });
            }
          } else {
            const userParam = {
              firstName: memberData.firstName,
              email: memberData.email,
              phoneNumber: memberData.phoneNumber,
              phoneCode: memberData.phoneCode,
            };
            const newUser = await User.createInstance(userParam);
            const memberParam = {
              UserId: newUser.id,
              firstName: memberData.firstName,
              CompanyId: memberData.CompanyId,
              RoleId: memberData.RoleId,
              ParentCompanyId,
              memberId: +memberId,
              password: userParam.password,
              phoneNumber: memberData.phoneNumber,
              phoneCode: memberData.phoneCode,
              createdBy: loginUser.id,
              ProjectId: memberData.ProjectId,
            };
            newMember = await Member.createInstance(memberParam);
            await this.createMemberNotification(memberData, newMember, DeliveryPersonNotification, Notification);

            if (domainName !== null && domainName !== undefined) {
              memberParam.email = userParam.email;
              await this.createPublicMember(memberParam, projectDetails, domainName);
            }
            memberParam.email = userParam.email;
            memberParam.firstName = newUser.firstName;
            memberParam.type = roleDetails.roleName;
            memberParam.id = newMember.id;
            memberParam.requestType = memberData.requestType;
            memberParam.domainName = domainName;
            this.sendMail(
              memberParam,
              'invite_member',
              'You were added as a member',
              'Member Onboarded',
              async (info, err) => {
                if (err) {
                  const newError = new ApiError(err.message, status.BAD_REQUEST);
                  done(null, newError);
                }
                delete newMember.password;
                done(newMember, false);
              },
            );
          }
        } else {
          done(null, validationerror);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },


  async createMemberNotification(memberData, newMember, DeliveryPersonNotification, Notification) {
     if (memberData.RoleId === 2 || memberData.RoleId === 1) {
            notificationHelper.memberNotificationCreation(
              newMember,
              DeliveryPersonNotification,
              Notification,
            );
      }
  },

  async createNotificationMember(params) {
    const {memberData, newMember, DeliveryPersonNotification, Notification, domainName, projectDetails, memberParam, existUser} = params;
    if (memberData.RoleId === 2 || memberData.RoleId === 1) {
        notificationHelper.memberNotificationCreation(
          newMember,
          DeliveryPersonNotification,
          Notification,
        );
      }
      if (domainName !== null && domainName !== undefined) {
        memberParam.email = existUser.email;
        await this.createPublicMember(memberParam, projectDetails, domainName);
     }
  },

  async  getNotificationListData(getNotificationPreferenceItemsList, newMember, memberData, inputData) {
    const { ProjectId, RoleId } = memberData;
    const { ParentCompanyId } = inputData.body;
    const locations = await Locations.findAll({
      where: { isDeleted: false, ProjectId }
    });

    for (const item of getNotificationPreferenceItemsList) {
      const isSpecialCase =
        item.id === 7 &&
        item.description === 'When a comment is added to a delivery/crane/concrete request' &&
        item.itemId === 4 &&
        item.emailNotification === true &&
        item.inappNotification === false &&
        item.isDeleted === false;

      const useInstant = isSpecialCase || item.inappNotification === true;

      await NotificationPreference.createInstance({
        MemberId: newMember.id,
        ProjectId,
        ParentCompanyId,
        NotificationPreferenceItemId: item.id,
        instant: useInstant,
        dailyDigest: !useInstant,
        isDeleted: false,
      });

      for (const location of locations) {
        await LocationNotificationPreferences.createInstance({
          MemberId: newMember.id,
          ProjectId,
          ParentCompanyId,
          LocationId: location.id,
          follow: RoleId === 2 && location.isDefault === true,
          isDeleted: false,
        });
      }
    }
  },


  async handleNotificationCreation(memberData, DeliveryPersonNotification, Notification, domainName, projectDetails, memberParam) {
    if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                    notificationHelper.memberNotificationCreation(
                      memberData,
                      DeliveryPersonNotification,
                      Notification,
                    );
    }
    if (domainName !== null && domainName !== undefined) {
      memberParam.email = existUser.email;
      await this.createPublicMember(memberParam, projectDetails, domainName);
    }
  },

  async handleExistingUser(memberData, existUser, validation, inputData, done) {
    const existMember = await Member.findOne({
      where: Sequelize.and({
        UserId: existUser.id,
        isDeleted: false,
        ProjectId: memberData.ProjectId,
      }),
    });

    if (!existMember) {
      return this.checkRole(memberData, existUser, validation, inputData, done);
    }

    return done(null, { message: 'Member already exists' });
  },
  async updateInviteMember(req, done) {
    try {
      req.user = {};
      await this.returnProjectModel();
      req.user.email = req.body.memberDetail.email;
      req.body.ParentCompanyId = req.body.memberDetail.ParentCompanyId;

      const { password } = req.body.memberDetail;
      const newOnepassword = await this.encryptPassword(password);
      const domainName = await this.getDomainName(req);
      const models = await helper.getDynamicModel(domainName);

      const memberDetail = await this.getMemberDetails(models, req.body.memberDetail.id);
      const userDetail = await models.User.findOne({ where: { id: memberDetail.UserId } });
      req.user = userDetail;

      const roleDetails = await models.Role.findOne({ where: { id: memberDetail.RoleId } });
      await this.updateUserPassword(domainName, userDetail, newOnepassword);

      const newData = await this.prepareUpdateData(req, userDetail, domainName, newOnepassword, roleDetails);
      const existCompanyDetail = await models.Company.findOne({
        where: { id: req.body.companyDetail.companyId },
      });

      if (existCompanyDetail) {
        newData.body.CompanyId = existCompanyDetail.id;
        await this.updateMemberWithNotifications(req, memberDetail);
        return this.editMember(newData, done);
      }

      const newCompany = await this.createNewCompany(models, req);
      newData.body.CompanyId = newCompany.id;
      await this.updateMemberWithNotifications(req, memberDetail);
      return this.editMember(newData, done);
    } catch (e) {
      return done(null, e);
    }
  },

  async encryptPassword(password) {
    let newPassword;
    await bcryptPassword(password, (encPassword) => {
      newPassword = encPassword;
    });
    return newPassword;
  },

  async getDomainName(req) {
    return req.body?.requestType === 1
      ? req.body.domainName
      : cryptr.decrypt(req.body.domainName);
  },

  async getMemberDetails(models, memberId) {
    return models.Member.findOne({
      where: { id: memberId, isDeleted: false },
    });
  },

  async updateUserPassword(domainName, userDetail, newPassword) {
    if (userDetail.RoleId === 4 && domainName) {
      await publicUser.update(
        { password: newPassword },
        { where: { email: userDetail.email } },
      );
    } else {
      await User.update({ password: newPassword }, { where: { id: userDetail.id } });
    }
  },

  async prepareUpdateData(req, userDetail, domainName, newPassword, roleDetails) {
    const newData = {
      body: req.body.memberDetail,
      user: {
        email: userDetail.email,
        id: userDetail.id,
      },
    };
    newData.body.password = newPassword;
    newData.body.domainName = domainName;
    newData.body.email = userDetail.email;
    newData.body.firstName = userDetail.firstName;
    newData.body.type = roleDetails.roleName;
    if (newData.body.action === 'onboarding') {
      newData.body.status = 'completed';
    }
    return newData;
  },

  async createNewCompany(models, req) {
    const companyParam = {
      ...req.body.companyDetail,
      createdBy: req.user.id,
      ProjectId: req.body.memberDetail.ProjectId,
      ParentCompanyId: req.body.memberDetail.ParentCompanyId,
      companyAutoId: 1,
    };
    delete companyParam.fullName;
    delete companyParam.lastName;
    return models.Company.create(companyParam);
  },

  async updateMemberWithNotifications(req, memberDetail) {
    await this.createMemberNotificationPreference(req);
    await this.setLocationNotificationPreferenceForAMember(memberDetail);
  },
  async setLocationNotificationPreferenceForAMember(memberDetail) {
    try {
      const getLocationsList = await Locations.findAll({
        where: { isDeleted: false, ProjectId: memberDetail.ProjectId },
      });
      for (const location of getLocationsList) {
        const object = {
          MemberId: memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          LocationId: location.id,
          follow: false,
          ParentCompanyId: memberDetail.ParentCompanyId,
          isDeleted: false,
        };
        if (memberDetail.RoleId === 2 && location.isDefault === true) {
          object.follow = true;
        }
        await LocationNotificationPreferences.createInstance(object);
      }
    } catch (e) {
      throw (null, e);
    }
  },
  async createMemberNotificationPreference(req) {
    await this.returnProjectModel();
    const getRequestDomain = req.body?.requestType === 1
      ? req.body.domainName
      : cryptr.decrypt(req.body.domainName);

    let memberDetail;
    if (getRequestDomain) {
      const models = await helper.getDynamicModel(getRequestDomain);
      memberDetail = await models.Member.findOne({
        where: { id: req.body.memberDetail.id, isDeleted: false },
      });
    } else {
      memberDetail = await Member.findOne({
        where: { id: req.body.memberDetail.id, isDeleted: false },
      });
    }
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberDetail.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    attr.TimeZoneId = projectObject?.TimeZone?.id ?? 3;
    await Member.update(attr, { where: { id: req.body.memberDetail.id } });

    for (const item of getNotificationPreferenceItemsList) {
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: req.body.memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: req.body.memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    }
  },
  async createPublicMember(memberData, projectDetails, domainName, password) {
    await this.returnProjectModel();
    const tempData = memberData;
    const userParam = {
      firstName: memberData.firstName,
      email: memberData.email,
      phoneNumber: memberData.phoneNumber,
      phoneCode: memberData.phoneCode,
      password,
    };

    let existUser = await publicUser.findOne({ where: { email: memberData.email } });
    if (!existUser) {
      existUser = await publicUser.createInstance(userParam);
    }
    tempData.UserId = existUser.id;
    let companyData;
    let existCompany;
    if (tempData.CompanyId) {
      companyData = await Company.findOne({ where: { id: tempData.CompanyId } });
      existCompany = await publicCompany.findOne({
        where: { companyName: companyData.companyName, website: companyData.website },
      });
    }

    const enterpriseValue = await Enterprise.findOne({
      where: Sequelize.where(
        Sequelize.fn('lower', Sequelize.col('name')),
        Sequelize.fn('lower', domainName),
      ),
    });
    tempData.isAccount = true;
    tempData.EnterpriseId = enterpriseValue.id;
    if (!existCompany && companyData) {
      const newData = JSON.parse(JSON.stringify(companyData));
      delete newData.id;
      const parentNew = await ParentCompany.findOne({ where: { id: newData.ParentCompanyId } });
      let existPublicParent = await publicParentCompany.findOne({
        where: { emailDomainName: parentNew.emailDomainName },
      });
      if (!existPublicParent) {
        const parentData = JSON.parse(JSON.stringify(parentNew));
        delete parentData.id;
        existPublicParent = await publicParentCompany.create(parentData);
      }
      const newCompanyData = JSON.parse(JSON.stringify(existPublicParent));
      newData.ParentCompanyId = newCompanyData.id;
      existCompany = await publicCompany.create(newData);
    }
    if (existCompany) {
      tempData.CompanyId = existCompany.id;
    }
    tempData.ProjectId = projectDetails.publicSchemaId;
    await publicMember.create(tempData);
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const domainName = await this.resolveDomainName(inputData);
    const modelObj = await helper.getDynamicModel(domainName);

    this.updateModelReferences(modelObj);

    if (domainName) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      inputData.user = newUser;
    }

    return domainName;
  },

  async resolveDomainName(inputData) {
    let { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;

    if (domainName) {
      const domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        return '';
      }
      return domainName;
    }

    if (!domainName && ParentCompanyId) {
      return await this.resolveDomainFromParentCompany(inputData, ParentCompanyId);
    }

    return '';
  },

  async resolveDomainFromParentCompany(inputData, ParentCompanyId) {
    const { email } = inputData.user;
    if (!email) return '';

    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) return '';

    const memberData = await publicMember.findOne({
      where: {
        UserId: userData.id,
        RoleId: { [Op.ne]: 4 },
        isDeleted: false
      },
    });

    if (!memberData) {
      return await this.getDomainFromEnterprise(ParentCompanyId);
    }

    if (memberData.isAccount) {
      const enterpriseValue = await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' },
      });
      return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
    }

    return await this.getDomainFromEnterprise(ParentCompanyId);
  },

  async getDomainFromEnterprise(ParentCompanyId) {
    const enterpriseValue = await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  updateModelReferences(modelObj) {
    User = modelObj.User;
    Member = modelObj.Member;
    Company = modelObj.Company;
    Role = modelObj.Role;
    Project = modelObj.Project;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Notification = modelObj.Notification;
    ParentCompany = modelObj.ParentCompany;
  },
  async getUserDetail(req, done) {
    try {
      this.returnProjectModel();
      req.user = {};
      let email;
      let domainName;
      if (req.body && req.body.requestType === 0) {
        email = cryptr.decrypt(req.body.email);
        domainName = cryptr.decrypt(req.body.domainName);
      }
      if (req.body && req.body.requestType === 1) {
        email = req.body.email;
        domainName = req.body.domainName;
      }
      req.user.email = email;
      const models = await helper.getDynamicModel(domainName);
      const inputData = req.body;
      const memberDetail = await models.Member.findOne({
        where: { id: +inputData.memberId, isDeleted: false },
        include: [
          {
            association: 'Company',
          },
        ],
      });
      if (memberDetail) {
        const userDetail = await models.User.findOne({ where: { id: +memberDetail.UserId } });
        req.user = userDetail;
        done({ userDetail, memberDetail }, false);
      } else {
        done(null, {
          status: status.UNPROCESSABLE_ENTITY,
          message: 'You were removed from the project.!',
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async inviteMembers(req, done) {
    try {
      const getDomainName = await this.getDynamicModel(req);
      req.user.domainName = getDomainName;
      const inputData = req.body;
      const membersListArray = inputData.membersList;
      const loginUser = req.user;
      const projectDetails = await Project.findByPk(inputData.ProjectId);
      if (projectDetails) {
        if (membersListArray) {
          await Promise.all(
            membersListArray.map(async (memberNew) => {
              const member = memberNew;
              member.ProjectId = inputData.ProjectId;
              member.requestType = inputData.requestType;
              member.ParentCompanyId = inputData.ParentCompanyId;
              const roleDetails = await Role.findByPk(member.RoleId);
              if (roleDetails) {
                await this.createMember(
                  member,
                  req,
                  projectDetails,
                  roleDetails,
                  loginUser,
                  async (info, err) => {
                    if (!err) {
                      done(info, false);
                    } else {
                      done(null, err);
                    }
                  },
                );
              } else {
                done(null, { message: 'Role does not exist.' });
              }
            }),
          );
        }
      } else {
        done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async resendInviteLink(req, done) {
    try {
      const data = {
        id: req.body.memberId,
        ParentCompanyId: req.body.ParentCompanyId,
        email: req.body.email,
        type: req.body.type,
        requestType: req.body.requestType,
        domainName: req.body?.requestType === 1 ? req.body.domainName : cryptr.decrypt(req.body.domainName)
      };
      const domainName = await this.getDynamicModel(req);
      data.domainName = domainName;
      this.sendMail(
        data,
        'invite_member',
        'You were added as a member',
        'Member Onboarded',
        async (info, err) => {
          if (err) {
            const newError = new ApiError(err.message, status.BAD_REQUEST);
            done(null, newError);
          }
          done(data, false);
        },
      );
    } catch (e) {
      done(null, e);
    }
  },
  async getOverViewDetail(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const project = await Project.findByPk(inputData.params.ProjectId);
      const loginUser = inputData.user;
      const accUser = await User.findOne({
        where: { id: loginUser.id, userType: 'account admin' },
      });
      if (!accUser) {
        if (project) {
          const memberDetail = await Member.findOne({
            include: [
              {
                association: 'User',
                attributes: [
                  'firstName',
                  'email',
                  'phoneNumber',
                  'phoneCode',
                  'lastName',
                  'profilePic',
                  'id',
                ],
              },
              { association: 'Role', attributes: ['roleName'] },
              {
                association: 'Company',
                attributes: [
                  'companyName',
                  'website',
                  'address',
                  'secondAddress',
                  'id',
                  'state',
                  'city',
                  'zipCode',
                  'country',
                ],
              },
            ],
            attributes: ['id', 'RoleId'],
            where: {
              UserId: inputData.user.id,
              ProjectId: inputData.params.ProjectId,
              isDeleted: false,
            },
          });
          if (memberDetail) {
            done(memberDetail, false);
          } else {
            done(null, { message: 'Member Does not exist.' });
          }
        } else {
          done(null, { message: 'Project Id Does not exist.' });
        }
      } else {
        const newMemberDetail = await Member.findOne({
          include: [
            {
              association: 'User',
              attributes: [
                'firstName',
                'email',
                'phoneNumber',
                'phoneCode',
                'lastName',
                'profilePic',
                'id',
              ],
            },
            { association: 'Role', attributes: ['roleName'] },
            {
              association: 'ParentCompany',
              include: [
                {
                  association: 'Company',
                  attributes: [
                    'companyName',
                    'website',
                    'address',
                    'secondAddress',
                    'id',
                    'state',
                    'city',
                    'zipCode',
                    'country',
                  ],
                  where: { isParent: true },
                },
              ],
            },
          ],
          attributes: ['id', 'RoleId'],
          where: {
            UserId: inputData.user.id,
            isDeleted: false,
          },
        });
        done(newMemberDetail, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async updateUserProfile(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const loginUser = inputData.user;
      const existDetails = await User.findOne({
        where: [
          Sequelize.and({
            phoneNumber: incomeData.phoneNumber,
            phoneCode: incomeData.phoneCode,
            id: { [Op.ne]: loginUser.id },
          }),
        ],
      });
      if (!existDetails) {
        const existCompanyDetails = await Company.findOne({
          where: {
            ProjectId: incomeData.ProjectId,
            id: { [Op.ne]: incomeData.CompanyId },
            companyName: { [Sequelize.Op.iLike]: incomeData.companyName },
          },
        });
        if (!existCompanyDetails) {
          await Member.update(
            {
              firstName: incomeData.firstName,
              phoneNumber: incomeData.phoneNumber,
              phoneCode: incomeData.phoneCode,
            },
            {
              where: {
                UserId: loginUser.id,
              },
            },
          );
          await User.update(
            {
              firstName: incomeData.firstName,
              phoneNumber: incomeData.phoneNumber,
              phoneCode: incomeData.phoneCode,
              lastName: incomeData.lastName,
            },
            {
              where: {
                id: loginUser.id,
              },
            },
          );
          const companyParam = {

            companyName: incomeData.companyName,

          };
          await Company.update(companyParam, { where: { id: incomeData.CompanyId } });
          done({ message: 'Details Updated Successfully.' }, false);
        } else {
          done(null, { message: 'Company Name already exist.' });
        }
      } else {
        done(null, { message: 'Mobile Number already exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async lastMember(inputData, done) {
    try {
      let data;
      const lastData = await Member.findOne({
        where: { ProjectId: +inputData.params.ProjectId, isDeleted: false, RoleId: { [Op.ne]: 1 } },
        order: [['memberId', 'DESC']],
      });
      if (lastData) {
        data = lastData.memberId + 1;
      } else {
        data = 1;
      }
      done({ memberId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async editMember(inputData, done) {
    let getDomainName;
    getDomainName = inputData.body.domainName;
    if (!getDomainName) {
      getDomainName = 'public';
    }
    const models = await helper.getDynamicModel(getDomainName);

    const memberData = inputData.body;
    const loginUser = inputData.user;
    const { email } = memberData;
    const existUser = await models.User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', email),
        ),
        { isDeleted: false },
      ),
    });
    const parentId = await models.Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await models.ParentCompany.getBy({ id: parentId.ParentCompanyId });
    this.checkRole(
      memberData,
      existUser,
      emailValidationData,
      inputData,
      async (roleResponse, roleError) => {
        if (roleError) {
          done(null, roleError);
        } else {
          const memberParam = {
            firstName: memberData.firstName,
            CompanyId: memberData.CompanyId,
            RoleId: memberData.RoleId,
            phoneNumber: memberData.phoneNumber,
            phoneCode: memberData.phoneCode,
            password: memberData.password,
          };
          if (memberData.status === 'completed') {
            memberParam.status = 'completed';
          }
          memberParam.lastName = memberData.lastName;
          const newMember = await models.Member.updateInstance(memberData.id, memberParam);
          delete memberParam.RoleId;
          delete memberParam.CompanyId;
          await models.User.update(memberParam, { where: { id: existUser.id } });
          await publicUser.update(
            { password: memberParam.password },
            { where: { email: memberData.email } },
          );
          done(newMember, false);
        }
      },
    );
  },
  async editMemberDetail(inputData, done) {
    await this.getDynamicModel(inputData);
    const memberData = inputData.body;
    const loginUser = inputData.user;
    const { email } = memberData;
    const existUser = await User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', email),
        ),
        { isDeleted: false },
      ),
    });
    const parentId = await Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
    this.checkRole(
      memberData,
      existUser,
      emailValidationData,
      inputData,
      async (roleResponse, roleError) => {
        if (roleError) {
          done(null, roleError);
        } else {
          const memberParam = {
            firstName: memberData.firstName,
            CompanyId: memberData.CompanyId,
            RoleId: memberData.RoleId,
            phoneNumber: memberData.phoneNumber,
            phoneCode: memberData.phoneCode,
            password: memberData.password,
          };
          if (memberData.status === 'completed') {
            memberParam.status = 'completed';
          }
          if (memberData.lastName) {
            memberParam.lastName = memberData.lastName;
          }
          const newMember = await Member.updateInstance(memberData.id, memberParam);
          delete memberParam.RoleId;
          delete memberParam.CompanyId;
          await User.update(memberParam, { where: { id: existUser.id } });
          done(newMember, false);
        }
      },
    );
  },
  async deleteMember(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      let getMembers;
      const { id } = input.body;
      if (reqData.isSelectAll) {
        getMembers = await Member.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false },
          include: ['User'],
        });
      } else {
        getMembers = await Member.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false, id: { [Op.in]: id } },
          include: ['User'],
        });
      }
      if (getMembers && getMembers.length > 0) {
        getMembers.map(async (item, index) => {
          const isMemberMappedToDeliveryRequest = await DeliveryPerson.findOne({
            where: {
              MemberId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isMemberMappedToCraneRequest = await CraneRequestResponsiblePerson.findOne({
            where: {
              MemberId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isMemberMappedToConcreteRequest = await ConcreteRequestResponsiblePerson.findOne({
            where: {
              MemberId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isMemberMappedToEquipment = await Equipments.findOne({
            where: {
              controlledBy: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          if (isMemberMappedToDeliveryRequest) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to submitted requests`,
            });
          }
          if (isMemberMappedToCraneRequest) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to submitted requests`,
            });
          }
          if (isMemberMappedToConcreteRequest) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to submitted requests`,
            });
          }
          if (isMemberMappedToEquipment) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to an equipment`,
            });
          }
          await Member.update(
            { isDeleted: true },
            {
              where: {
                id: +item.id,
                ProjectId: reqData.ProjectId,
                RoleId: { [Op.ne]: 1 },
                isDeleted: false,
                UserId: { [Op.not]: Sequelize.col('createdBy') },
              },
            },
          );
          if (index === getMembers.length - 1) {
            return done('success', false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async checkRole(memberData, existUser, validation, inputData, done) {
    try {
      await this.getDynamicModel(inputData);

      if (!memberData) {
        return done({ status: true }, false);
      }

      const existingRole = await this.findExistingRole(existUser, validation);
      const roleValidation = this.validateRoleAssignment(memberData.RoleId, existingRole);

      if (roleValidation.error) {
        return done(null, { message: roleValidation.message });
      }

      return done({ status: true }, false);
    } catch (e) {
      return done(null, e);
    }
  },

  async findExistingRole(existUser, validation) {
    return Member.findOne({
      where: Sequelize.and({
        UserId: existUser.id,
        isDeleted: false,
        ParentCompanyId: validation.id,
      }),
    });
  },

  validateRoleAssignment(roleId, existingRole) {
    const roleValidators = {
      [2]: () => this.validateProjectAdminRole(existingRole),
      [3]: () => this.validateProjectAdminRole(existingRole),
      [4]: () => this.validateSubContractorRole(existingRole)
    };

    const validator = roleValidators[roleId];
    return validator ? validator() : { error: false };
  },

  validateProjectAdminRole(existingRole) {
    if (!existingRole) {
      return { error: false };
    }

    if (existingRole.RoleId === 4) {
      return {
        error: true,
        message: 'This member is a sub contractor in our organization.'
      };
    }

    return { error: false };
  },

  validateSubContractorRole(existingRole) {
    if (!existingRole) {
      return { error: false };
    }

    if (existingRole.RoleId === 2 || existingRole.RoleId === 3) {
      return {
        error: true,
        message: 'This member is Project Admin/General Contractor in our organization , it is not possible to assign member as a Sub Contractor.'
      };
    }

    return { error: false };
  },

  async checkEmailValidation(inputData, done) {
    await this.getDynamicModel(inputData);
    const memberData = inputData.body;
    const loginUser = inputData.user;
    const parentId = await Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });

    if (!this.isProjectAdminOrGeneralContractor(memberData.RoleId)) {
      return done(emailValidationData);
    }

    const emailDomainName = this.extractEmailDomain(memberData.email);
    if (await this.isRestrictedEmail(emailDomainName)) {
      return done(null, { message: 'This email is not allowed' });
    }

    if (this.isSameDomain(emailValidationData.emailDomainName, emailDomainName)) {
      return done(emailValidationData, false);
    }

    return done(null, { message: 'This user is not our organization.' });
  },

  isProjectAdminOrGeneralContractor(roleId) {
    return roleId === 2 || roleId === 3;
  },

  async isRestrictedEmail(emailDomainName) {
    const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
    return !!restrict;
  },

  isSameDomain(validationDomain, emailDomain) {
    return validationDomain === emailDomain;
  },

  extractEmailDomain(email) {
    const firstSplit = email.split('@')[1];
    const secondSplit = firstSplit.split('.');
    if (secondSplit.length === 2) {
      return firstSplit;
    }
    return firstSplit.substring(firstSplit.indexOf('.') + 1);
  },

  async sendMail(userData, mailData, mailSubject, tagName, done) {
    await MAILER.sendMail(userData, mailData, mailSubject, tagName, (info, err) => {
      if (err) {
        const newError = new ApiError(err.message, status.BAD_REQUEST);
        done(null, newError);
      } else {
        done(userData, false);
      }
    });
  },
  async uploadProfile(inputData, done) {
    awsConfig.singleUpload(inputData, async (result, err) => {
      if (!err) {
        await User.update(
          {
            profilePic: result[0].Location,
          },
          {
            where: { id: inputData.user.id },
          },
        );
        done({ imageUrl: result[0].Location }, false);
      } else {
        done(null, err);
      }
    });
  },
  async listMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
        RoleId: { [Op.ne]: 1 },
        isGuestUser: false,
      };
      const memberList = await Member.getAll(
        condition,
        incomeData.nameFilter,
        incomeData.companyFilter,
        incomeData.roleFilter,
        incomeData.statusFilter,
        incomeData.search,
        +params.pageSize,
        offset,
        sort,
        sortByField,
      );
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async searchMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        [Op.and]: [
          {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            RoleId: { [Op.ne]: 1 },
            isActive: true,
            isGuestUser: false
          },
        ],
      };
      const memberList = await Member.searchMemberNDR(condition, params);
      const finalList = [];
      let assignEmail;
      memberList.forEach((element) => {
        if (element.User.firstName && element.User.lastName) {
          assignEmail = `${element.User.firstName} ${element.User.lastName}(${element.User.email})`;
        } else {
          assignEmail = `(${element.User.email})`;
        }
        finalList.push({
          id: element.id,
          emails: element.User.email,
          firstName: element.firstName,
          lastName: element.User.lastName,
          email: assignEmail,
          isGuestUser: element.isGuestUser
        });
      });
      done(finalList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async searchAutoApproveMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        [Op.and]: [
          {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            RoleId: { [Op.in]: [3, 4] },
            isActive: true,
          },
        ],
      };
      const memberList = await Member.searchMemberNDR(condition, params);
      const finalList = [];
      let assignEmail;
      memberList.forEach((element) => {
        if (element.User.firstName && element.User.lastName) {
          assignEmail = `${element.User.firstName} ${element.User.lastName} (${element.User.email})`;
        } else {
          assignEmail = `(${element.User.email})`;
        }
        finalList.push({
          id: element.id,
          emails: element.User.email,
          firstName: element.firstName,
          lastName: element.User.lastName,
          email: assignEmail,
        });
      });
      done(finalList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async searchAllMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const userData = await User.findAll({
        where: {
          isDeleted: false,
          [Op.or]: [
            {
              email: { [Sequelize.Op.iLike]: `% ${params.search}% ` },
            },
            {
              firstName: { [Sequelize.Op.iLike]: `% ${params.search}% ` },
            },
          ],
        },
        attributes: ['id', 'email', 'firstName'],
      });
      const memberData = await Member.findAll({
        where: { ProjectId: params.ProjectId, isDeleted: false },
      });
      const memberList = [];
      memberData.forEach((element) => {
        memberList.push(element.id);
      });
      const finalResult = userData.filter((e) => {
        return memberList.indexOf(e.id) === -1;
      });
      done(finalResult, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listAllMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const memberList = await Member.getAllEmail({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
        //isGuestUser: false,
      });
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async membersForBulkUploadDeliveryRequest(inputData) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const memberList = await Member.getAllEmail({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
      });
      return memberList;
    } catch (e) {
      console.log(e);
    }
  },
  // Get All Member Lists
  async getAllMemberLists(req) {
    try {
      const {
        search,
        pageSize,
        pageNo,
        sortColumn,
        sortType,
        nameFilter,
        companyFilter,
        roleFilter,
        statusFilter,
      } = req.query;
      let count = 0;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const commonSearch = {
        [Op.and]: {
          isDeleted: false,
          userType: 'user',
        },
      };
      const totalUsers = await User.findAll({ where: commonSearch });
      const memberLists = await User.getAllMembers(
        pageSize,
        offset,
        search,
        sortColumn,
        sortType,
        nameFilter,
        companyFilter,
        roleFilter,
        statusFilter,
      );
      count = totalUsers.length;
      return { memberLists, count };
    } catch (e) {
      console.log(e);
    }
  },
  // Get Member Detail
  async getMemberDetail(req) {
    try {
      this.getDynamicModel(req);
      const memberDetail = await User.getMemberDetail({ id: req.params.id });
      return memberDetail;
    } catch (e) {
      console.log(e);
    }
  },
  // Get Member associated projects
  async getMemberProjects(req) {
    try {
      const { pageSize, pageNo } = req.query;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      this.getDynamicModel(req);
      const user = {
        UserId: req.params.id,
      };
      const projectLists = await Member.getMembersProject(user, pageSize, offset);
      return projectLists;
    } catch (e) {
      console.log(e);
    }
  },
  // Update Member project status
  async updateMemberProjectStatus(req) {
    try {
      let memberStatusUpdated;
      this.getDynamicModel(req);
      if (req.body?.userStatus && !req.body?.action) {
        memberStatusUpdated = await User.update(
          {
            isActive: req.body.memberProjectStatus,
          },
          { where: { id: +req.params.id } },
        );
      } else if (req.body?.userStatus && req.body?.action) {
        const MemberDetail = await Member.findOne({
          where: { id: +req.params.id, isDeleted: false },
        });
        memberStatusUpdated = await User.update(
          {
            isActive: req.body.memberProjectStatus,
          },
          { where: { id: +MemberDetail.UserId } },
        );
      } else if (req.body && !req.body.userStatus && !req.body.action) {
        memberStatusUpdated = await Member.update(
          {
            memberProjectStatus: req.body.memberProjectStatus,
          },
          { where: { id: +req.params.id } },
        );
      }
      return memberStatusUpdated;
    } catch (e) {
      console.log(e);
    }
  },
  // change Member Password
  async changeMemberPassword(req) {
    try {
      let password = req.body.newPassword;
      const userId = req.params.id;
      await bcryptPassword(password, (encPassword) => {
        password = encPassword;
      });
      const passwordUpdated = await User.update({ password }, { where: { id: +userId } });
      const user = await User.findOne({ where: { id: +userId } });
      const mailPayload = {
        firstName: user.firstName,
        email: user.email,
        password: req.body.newPassword,
      };
      MAILER.sendMail(
        mailPayload,
        'userPasswordChangeBySuperAdmin',
        'Your Password Changed.!',
        'User/s Password Changed by a Super Admin',
        (info, err) => {
          console.log(info, err);
        },
      );
      return passwordUpdated;
    } catch (e) {
      console.log(e);
    }
  },
  // update Member Profile
  async updateMemberProfile(req) {
    try {
      const MemberDetail = req.body;
      const userId = +req.params.id;
      const existDetails = await User.findOne({
        where: [
          Sequelize.and({
            phoneNumber: MemberDetail.phoneNumber,
            phoneCode: MemberDetail.phoneCode,
            id: { [Op.ne]: userId },
          }),
        ],
      });
      if (!existDetails) {
        const existCompanyDetails = await Company.findOne({
          where: Sequelize.and(
            { id: { [Op.ne]: MemberDetail.CompanyId } },
            Sequelize.or({
              companyName: MemberDetail.companyName,
            }),
          ),
        });
        if (!existCompanyDetails) {
          await Member.update(
            {
              firstName: MemberDetail.firstName,
              phoneNumber: MemberDetail.phoneNumber,
              phoneCode: MemberDetail.phoneCode,
            },
            {
              where: {
                UserId: userId,
              },
            },
          );
          await User.update(
            {
              firstName: MemberDetail.firstName,
              phoneNumber: MemberDetail.phoneNumber,
              phoneCode: MemberDetail.phoneCode,
              lastName: MemberDetail.lastName,
            },
            {
              where: {
                id: userId,
              },
            },
          );
          const companyParam = {
            address: MemberDetail.address,
            secondAddress: MemberDetail.secondAddress,
            website: MemberDetail.website,
            companyName: MemberDetail.companyName,
            state: MemberDetail.state,
            city: MemberDetail.city,
            country: MemberDetail.country,
            zipCode: MemberDetail.zipCode,
          };
          const companyDetailsUpdated = await Company.update(companyParam, {
            where: { id: MemberDetail.CompanyId },
          });
          return { error: false, message: companyDetailsUpdated };
        }
        return { error: true, message: 'Company Name/Website already exist.' };
      }
      return { error: true, message: 'Mobile Number already exist.' };
    } catch (e) {
      console.log(e);
    }
  },
  // Get All Members
  async getAllMemberListsForAssignProject() {
    try {
      const members = await User.findAll({
        include: [
          {
            where: { isDeleted: false },
            association: 'Members',
            attributes: ['id', 'UserId', 'firstName'],
          },
        ],
        where: { isDeleted: false },
        attributes: ['id', 'firstName', 'lastName', 'email', 'phoneCode', 'phoneNumber'],
      });
      return members;
    } catch (e) {
      console.log(e);
    }
  },
  async activateMember(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: false,
        },
      });
      if (memberDetails) {
        const memberActivated = await Member.update(
          { isActive: true },
          {
            where: {
              id: inputData.id,
              ProjectId: inputData.ProjectId,
              isDeleted: false,
            },
          },
        );
        const getNotificationInAppPreferenceItemsList = await NotificationPreferenceItem.findAll({
          where: { isDeleted: false, inappNotification: true },
        });
        const getNotificationEmailPreferenceItemsList = await NotificationPreferenceItem.findAll({
          where: { isDeleted: false, emailNotification: true },
        });
        getNotificationInAppPreferenceItemsList.forEach(async (element) => {
          if (element.inappNotification) {
            await NotificationPreference.update(
              { instant: true, dailyDigest: false },
              {
                where: {
                  MemberId: +inputData.id,
                  ProjectId: +inputData.ProjectId,
                  ParentCompanyId: +inputData.ParentCompanyId,
                  NotificationPreferenceItemId: element.id,
                },
              },
            );
          }
        });
        getNotificationEmailPreferenceItemsList.forEach(async (element) => {
          if (element.emailNotification) {
            await NotificationPreference.update(
              { instant: false, dailyDigest: true },
              {
                where: {
                  MemberId: +inputData.id,
                  ProjectId: +inputData.ProjectId,
                  ParentCompanyId: +inputData.ParentCompanyId,
                  NotificationPreferenceItemId: element.id,
                },
              },
            );
          }
        });
        return done(memberActivated, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async getMappedRequests(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      if (memberDetails) {
        let deliveryRequestMember = [];
        deliveryRequestMember = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        const craneRequestMember = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        const concreteRequestMember = await ConcreteRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        const equipmentsMapped = await Equipments.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          },
          include: [
            {
              association: 'controllUserDetails',
              required: true,
              where: { isDeleted: false, id: memberDetails.id, isActive: true },
              attributes: { exclude: ['password', 'resetPasswordToken'] },
              include: [
                {
                  association: 'User',
                  required: true,
                  attributes: ['email', 'id', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        });
        if (craneRequestMember) {
          deliveryRequestMember.push(...craneRequestMember);
        }
        if (concreteRequestMember) {
          deliveryRequestMember.push(...concreteRequestMember);
        }
        if (equipmentsMapped) {
          deliveryRequestMember.push(...equipmentsMapped);
        }
        const allMember = await Member.findAll({
          where: Sequelize.and({
            ProjectId: inputData.ProjectId,
            isActive: true,
            isDeleted: false,
          }),
          attribute: ['id', 'firstName', 'UserId'],
          include: [
            {
              association: 'User',
              where: {
                isActive: true,
              },
              attribute: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        });
        return done({ mappedRequest: deliveryRequestMember, members: allMember }, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async deactivateMember(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const loginUser = req.user;
      const { memberSwitchedRequests } = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            association: 'User',
            attributes: ['id', 'email'],
            isDeleted: false,
          },
        ],
      });
      if (memberDetails) {
        const deactivatedMemberData = await Member.findOne({
          where: {
            id: inputData.id,
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
              isDeleted: false,
            },
          ],
        });

        let deliveryRequestMember = [];
        deliveryRequestMember = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        if (deliveryRequestMember && deliveryRequestMember.length > 0) {
          deliveryRequestMember.map(async (object) => {
            const history = {
              DeliveryRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Member ${deactivatedMemberData.User.firstName}  ${deactivatedMemberData.User.lastName}`,
            };
            await DeliverHistory.createInstance(history);

            await DeliveryPerson.update(
              { isActive: false },
              {
                where: {
                  MemberId: memberDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  DeliveryId: object.id,
                },
              },
            );
          });
        }
        const craneRequestMember = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        if (craneRequestMember && craneRequestMember.length > 0) {
          craneRequestMember.map(async (object) => {
            const history = {
              CraneRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Member ${deactivatedMemberData.User.firstName}  ${deactivatedMemberData.User.lastName}`,
            };
            await CraneRequestHistory.createInstance(history);

            await CraneRequestResponsiblePerson.update(
              { isActive: false },
              {
                where: {
                  MemberId: memberDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  CraneRequestId: object.id,
                },
              },
            );
          });
        }
        const concreteRequestMember = await ConcreteRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        if (concreteRequestMember && concreteRequestMember.length > 0) {
          concreteRequestMember.map(async (object) => {
            const history = {
              ConcreteRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Member, ${deactivatedMemberData.User.firstName} ${deactivatedMemberData.User.lastName}`,
            };
            await ConcreteRequestHistory.createInstance(history);

            await ConcreteRequestResponsiblePerson.update(
              { isActive: false },
              {
                where: {
                  MemberId: memberDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  ConcreteRequestId: object.id,
                },
              },
            );
          });
        }
        if (memberSwitchedRequests?.length > 0) {
          await Promise.all(memberSwitchedRequests.map(object =>
            this.handleMemberSwitchRequest(object, {
              memberDetails,
              inputData,
              loginUser,
              deliveryRequestMember,
              craneRequestMember,
              concreteRequestMember
            })
          ));
        }

        await this.clearEquipmentControl(memberDetails, inputData);
        const memberDeactivated = await this.deactivateMemberRecord(memberDetails, inputData);
        await this.updateNotificationPreferences(inputData);

        global.io.emit('deactivatedMember', {
          email: memberDetails.User.email,
          status: 'memberDeactivated',
        });

        return done(memberDeactivated, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },

  async handleMemberSwitchRequest(object, context) {
    const { requestType } = object;
    const handlers = {
      deliveryRequest: () => this.handleDeliveryRequestSwitch(object, context),
      deliveryRequestWithCrane: () => this.handleDeliveryRequestSwitch(object, context),
      craneRequest: () => this.handleCraneRequestSwitch(object, context),
      concreteRequest: () => this.handleConcreteRequestSwitch(object, context),
      default: () => this.handleEquipmentSwitch(object, context)
    };

    const handler = handlers[requestType] || handlers.default;
    return handler();
  },

  async handleDeliveryRequestSwitch(object, context) {
    const { memberDetails, inputData, loginUser, deliveryRequestMember } = context;
    const updateDeliveryParam = {
      DeliveryId: object.id,
      DeliveryCode: object.DeliveryId,
      ProjectId: inputData.ProjectId,
      isDeleted: false,
      MemberId: +object.changedMemberId,
      isActive: true,
    };

    const isMemberExists = await DeliveryPerson.findOne({
      where: updateDeliveryParam,
    });

    if (isMemberExists) return;

    const getMemberObject = await this.getMemberWithUser(object.changedMemberId);
    if (deliveryRequestMember?.length > 0) {
      await this.createDeliveryHistory(memberDetails, getMemberObject, loginUser, object, inputData);
    }

    await this.updateOrCreateDeliveryPerson(updateDeliveryParam, object, inputData);
  },

  async handleCraneRequestSwitch(object, context) {
    const { memberDetails, inputData, loginUser, craneRequestMember } = context;
    const updateCraneParam = {
      CraneRequestId: object.id,
      CraneRequestCode: object.CraneRequestId,
      ProjectId: inputData.ProjectId,
      isDeleted: false,
      MemberId: +object.changedMemberId,
      isActive: true,
    };

    const isMemberExists = await CraneRequestResponsiblePerson.findOne({
      where: updateCraneParam,
    });

    if (isMemberExists) return;

    const getMemberObject = await this.getMemberWithUser(object.changedMemberId);
    if (craneRequestMember?.length > 0) {
      await this.createCraneHistory(memberDetails, getMemberObject, loginUser, object, inputData);
    }

    await this.updateOrCreateCranePerson(updateCraneParam, object, inputData);
  },

  async handleConcreteRequestSwitch(object, context) {
    const { memberDetails, inputData, loginUser, concreteRequestMember } = context;
    const updateConcreteParam = {
      ConcreteRequestId: object.id,
      ConcreteRequestCode: object.ConcreteRequestId,
      ProjectId: inputData.ProjectId,
      isDeleted: false,
      MemberId: +object.changedMemberId,
      isActive: true,
    };

    const isMemberExists = await ConcreteRequestResponsiblePerson.findOne({
      where: updateConcreteParam,
    });

    if (isMemberExists) return;

    const getMemberObject = await this.getMemberWithUser(object.changedMemberId);
    if (concreteRequestMember?.length > 0) {
      await this.createConcreteHistory(memberDetails, getMemberObject, loginUser, object, inputData);
    }

    await this.updateOrCreateConcretePerson(updateConcreteParam, object, inputData);
  },

  async handleEquipmentSwitch(object, context) {
    const { memberDetails, inputData } = context;
    await Equipments.update(
      { controlledBy: object.changedMemberId },
      {
        where: {
          controlledBy: memberDetails.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      },
    );
  },

  async getMemberWithUser(memberId) {
    return Member.findOne({
      where: { id: +memberId },
      include: [{
        association: 'User',
        attributes: ['id', 'email', 'firstName', 'lastName'],
        isDeleted: false,
      }],
    });
  },

  async createDeliveryHistory(memberDetails, getMemberObject, loginUser, object, inputData) {
    const history = {
      DeliveryRequestId: object.id,
      MemberId: memberDetails.id,
      type: 'edit',
      ProjectId: inputData.ProjectId,
      description: `${loginUser.firstName} ${loginUser.lastName} added the member ${getMemberObject.User.firstName} ${getMemberObject.User.lastName}`,
    };
    await DeliverHistory.createInstance(history);
  },

  async createCraneHistory(memberDetails, getMemberObject, loginUser, object, inputData) {
    const history = {
      CraneRequestId: object.id,
      MemberId: memberDetails.id,
      type: 'edit',
      ProjectId: inputData.ProjectId,
      description: `${loginUser.firstName} ${loginUser.lastName} added the member ${getMemberObject.User.firstName} ${getMemberObject.User.lastName}`,
    };
    await CraneRequestHistory.createInstance(history);
  },

  async createConcreteHistory(memberDetails, getMemberObject, loginUser, object, inputData) {
    const history = {
      ConcreteRequestId: object.id,
      MemberId: memberDetails.id,
      type: 'edit',
      ProjectId: inputData.ProjectId,
      description: `${loginUser.firstName} ${loginUser.lastName} added the member ${getMemberObject.User.firstName} ${getMemberObject.User.lastName}`,
    };
    await ConcreteRequestHistory.createInstance(history);
  },

  async updateOrCreateDeliveryPerson(updateDeliveryParam, object, inputData) {
    const existMember = await DeliveryPerson.findAll({
      where: {
        ProjectId: inputData.ProjectId,
        DeliveryId: object.id,
      },
    });
    const index = existMember.findIndex(
      (item) => item.MemberId === +object.changedMemberId,
    );
    if (index !== -1) {
      await DeliveryPerson.update(updateDeliveryParam, {
        where: {
          id: existMember[index].id,
          DeliveryId: object.id,
          ProjectId: inputData.ProjectId,
        },
      });
    } else {
      await DeliveryPerson.createInstance(updateDeliveryParam);
    }
  },

  async updateOrCreateCranePerson(updateCraneParam, object, inputData) {
    const existMember = await CraneRequestResponsiblePerson.findAll({
      where: {
        ProjectId: inputData.ProjectId,
        CraneRequestId: object.id,
      },
    });
    const index = existMember.findIndex(
      (item) => item.MemberId === +object.changedMemberId,
    );
    if (index !== -1) {
      await CraneRequestResponsiblePerson.update(updateCraneParam, {
        where: {
          id: existMember[index].id,
          CraneRequestId: object.id,
          ProjectId: inputData.ProjectId,
        },
      });
    } else {
      await CraneRequestResponsiblePerson.createInstance(updateCraneParam);
    }
  },

  async updateOrCreateConcretePerson(updateConcreteParam, object, inputData) {
    const existMember = await ConcreteRequestResponsiblePerson.findAll({
      where: {
        ProjectId: inputData.ProjectId,
        ConcreteRequestId: object.id,
      },
    });
    const index = existMember.findIndex(
      (item) => item.MemberId === +object.changedMemberId,
    );
    if (index !== -1) {
      await ConcreteRequestResponsiblePerson.update(updateConcreteParam, {
        where: {
          id: existMember[index].id,
          ProjectId: inputData.ProjectId,
          ConcreteRequestId: object.id,
        },
      });
    } else {
      await ConcreteRequestResponsiblePerson.createInstance(updateConcreteParam);
    }
  },

  async clearEquipmentControl(memberDetails, inputData) {
    await Equipments.update(
      { controlledBy: null },
      {
        where: {
          controlledBy: memberDetails.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      },
    );
  },

  async deactivateMemberRecord(memberDetails, inputData) {
    return Member.update(
      { isActive: false },
      {
        where: {
          id: memberDetails.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      },
    );
  },

  async updateNotificationPreferences(inputData) {
    await NotificationPreference.update(
      { instant: false, dailyDigest: false },
      {
        where: {
          MemberId: +inputData.id,
          ProjectId: +inputData.ProjectId,
          ParentCompanyId: +inputData.ParentCompanyId,
        },
      },
    );
  },
  async getOnboardingInviteLink(req, done) {
    try {
      await this.getDynamicModel(req);
      const memberData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: memberData.id,
          ProjectId: memberData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      if (memberDetails) {
        let user = memberData;
        user.link = process.env.BASE_URL;
        user = await deepLinkService.getInviteMemberDeepLink(user);
        return done(user, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async getMemberData(req, done) {
    try {
      await this.getDynamicModel(req);
      const memberData = req.body;
      const getUser = await User.findOne({
        where: {
          id: req.user.id,
        },
      });
      const memberDetails = await Member.findOne({
        where: {
          UserId: getUser.id,
          ProjectId: +memberData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            association: 'Project',
            attributes: ['id', 'projectName'],
          },
          {
            association: 'Company',
            attributes: ['id', 'companyName'],
          },
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            association: 'Role',
            attributes: ['roleName'],
          },
        ],
      });
      return done(memberDetails, false);
    } catch (e) {
      return done(null, e);
    }
  },

  async listRetoolMembers(inputData) {
    try {
      const req = inputData;
      const membersList = await Member.findAll({
        where: { ProjectId: req.body.ProjectId, isDeleted: false, isGuestUser: false },
        include: [{
          association: 'User',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'phoneCode',
            'profilePic',
            'isActive',
          ],
        },],
      });
      return membersList;
    } catch (e) {
      throw new Error(e);
    }
  },

  async listRegisteredMembers(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const memberList = await Member.getAllEmail({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
        isGuestUser: false,
      });
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listGuestMembers(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
        RoleId: { [Op.ne]: 1 },
        isGuestUser: true,
      };
      const memberList = await Member.getAll(
        condition,
        incomeData.nameFilter,
        incomeData.companyFilter,
        incomeData.roleFilter,
        incomeData.statusFilter,
        incomeData.search,
        +params.pageSize,
        offset,
        sort,
        sortByField,
      );
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },

  async addGuestAsMember(req) {
    try {
      const ProjectId = req.body.ProjectId;
      const ParentCompanyId = req.body.ParentCompanyId;
      const guestId = req.body.guestUserId;
      let generatePasswordvalue = generatePassword();
      let originalPawword = generatePasswordvalue;
      const creatorId = req.body.memberId;
      await bcryptPassword(generatePasswordvalue, (encPassword) => {
        generatePasswordvalue = encPassword;
      });
      const userUpdateData = {
        password: generatePasswordvalue,
      }
      await User.update(userUpdateData, { where: { id: +guestId, email: req.body.guestEmail, } });
      const memberUpdateData = {
        isGuestUser: false,
        createdBy: +creatorId,
        RoleId: +req.body.selectedRoleId,
        status: 'completed',
        password: generatePasswordvalue,
      }
      const memberDetail = await Member.update(memberUpdateData,
        {
          where: {
            isDeleted: false,
            isActive: true,
            UserId: guestId,
            ProjectId,
          },
        },
      );
      const data = memberDetail;
      const notificationDetail = {
        ProjectId: +ProjectId,
        id: +req.body.guestId,
        ParentCompanyId: +ParentCompanyId,
        RoleId: +req.body.selectedRoleId,
      }
      await this.createGuestMemberNotificationPreference(notificationDetail);
      await this.setLocationNotificationPreferenceForAMember(notificationDetail);
      const getProject = await Project.findOne({
        where: {
          isDeleted: false,
          id: +ProjectId,
        },
      });
      //send intimate Email to guest user
      const mailData = {
        email: req.body.guestEmail,
        approverFirstName: req.body.memberFirstName,
        approverLastName: req.body.memberLastName,
        password: originalPawword,
        guestFirstName: req.body.guestFirstName,
        guestLastName: req.body.guestLastName,
        projectName: getProject.projectName,
        link: process.env.BASE_URL,
      }
      await MAILER.sendMail(mailData, 'guestApproved', 'Request Accepted', 'Request Approved', (info, err) => {
        if (err) {
          throw new Error(err);
        } else {
          return { data }
        }
      });

      return { data };

    } catch (error) {
      console.log(error);
      throw new Error(error);
    }

  },

  async createGuestMemberNotificationPreference(req) {
    await this.returnProjectModel();
    let memberDetail;
    memberDetail = await Member.findOne({
      where: { id: +req.id, isDeleted: false },
    });
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberDetail.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    attr.TimeZoneId = projectObject?.TimeZone?.id ?? 3;
    await Member.update(attr, { where: { id: +req.id } });

    for (const item of getNotificationPreferenceItemsList) {
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: +req.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: +item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: +req.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    }
  },

  async rejectGuestRequest(req) {
    try {
      const ProjectId = +req.body.ProjectId;
      const ParentCompanyId = +req.body.ParentCompanyId;
      const guestId = +req.body.guestUserId;
      await Member.update(
        { status: 'declined' },
        {
          where: {
            isDeleted: false,
            isActive: true,
            UserId: guestId,
            ProjectId: ProjectId,
            ParentCompanyId: ParentCompanyId,
          },
        },
      );
      const getProject = await Project.findOne({
        where: {
          isDeleted: false,
          id: +ProjectId,
        },
      });

      // send email to guest member that PA rejects your request to be a member
      const mailData = {
        email: req.body.guestEmail,
        rejectorFirstName: req.body.memberFirstName,
        rejectorLastName: req.body.memberLastName,
        guestFirstName: req.body.guestFirstName,
        guestLastName: req.body.guestLastName,
        projectName: getProject.projectName,
      }
      await MAILER.sendMail(mailData, 'guestRejected', 'Request Rejected', 'Request Rejected', (info, err) => {
        if (err) {
          throw new Error(err);
        } else {
          return { mailData }
        }
      });
      return { mailData }
    } catch (error) {
      throw new Error(error);
    }
  },

};

module.exports = memberService;