const HistoryController = require('../HistoryController');
const { historyService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  historyService: {
    createHistory: jest.fn(),
    getHistory: jest.fn(),
    getInspectionHistory: jest.fn(),
  },
}));

describe('HistoryController', () => {
  let mockReq, mockRes, mockNext;

  beforeEach(() => {
    mockReq = {};
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createHistory', () => {
    it('should create history successfully', async () => {
      const mockResponse = { id: 1, data: 'history created' };
      historyService.createHistory.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await HistoryController.createHistory(mockReq, mockRes, mockNext);

      expect(historyService.createHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'History created successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from history creation', async () => {
      const mockError = new Error('Service error');
      historyService.createHistory.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await HistoryController.createHistory(mockReq, mockRes, mockNext);

      expect(historyService.createHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in history creation', async () => {
      const mockError = new Error('Exception error');
      historyService.createHistory.mockImplementation(() => {
        throw mockError;
      });

      await HistoryController.createHistory(mockReq, mockRes, mockNext);

      expect(historyService.createHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getHistory', () => {
    it('should get history successfully', async () => {
      const mockResponse = [{ id: 1, data: 'history 1' }, { id: 2, data: 'history 2' }];
      historyService.getHistory.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await HistoryController.getHistory(mockReq, mockRes, mockNext);

      expect(historyService.getHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'History Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from history retrieval', async () => {
      const mockError = new Error('Service error');
      historyService.getHistory.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await HistoryController.getHistory(mockReq, mockRes, mockNext);

      expect(historyService.getHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in history retrieval', async () => {
      const mockError = new Error('Exception error');
      historyService.getHistory.mockImplementation(() => {
        throw mockError;
      });

      await HistoryController.getHistory(mockReq, mockRes, mockNext);

      expect(historyService.getHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getInspectionHistory', () => {
    it('should get inspection history successfully', async () => {
      const mockResponse = [{ id: 1, type: 'inspection', data: 'inspection 1' }];
      historyService.getInspectionHistory.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await HistoryController.getInspectionHistory(mockReq, mockRes, mockNext);

      expect(historyService.getInspectionHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'History Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from inspection history retrieval', async () => {
      const mockError = new Error('Service error');
      historyService.getInspectionHistory.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await HistoryController.getInspectionHistory(mockReq, mockRes, mockNext);

      expect(historyService.getInspectionHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in inspection history retrieval', async () => {
      const mockError = new Error('Exception error');
      historyService.getInspectionHistory.mockImplementation(() => {
        throw mockError;
      });

      await HistoryController.getInspectionHistory(mockReq, mockRes, mockNext);

      expect(historyService.getInspectionHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});