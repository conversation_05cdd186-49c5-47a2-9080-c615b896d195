const GateController = require('../GateController');
const { gateService } = require('../../services');

// Mock dependencies
jest.mock('../../services', () => ({
  gateService: {
    addGates: jest.fn(),
    listGates: jest.fn(),
    updateGates: jest.fn(),
    deleteGates: jest.fn(),
    getMappedRequests: jest.fn(),
    deactivateGate: jest.fn(),
    lastGate: jest.fn(),
  },
}));

describe('GateController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('addGates', () => {
    it('should add gates successfully', async () => {
      const mockResponse = { id: 1, name: 'Test Gate' };
      gateService.addGates.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await GateController.addGates(mockReq, mockRes, mockNext);

      expect(gateService.addGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate added successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from gate addition', async () => {
      const mockError = new Error('Service error');
      gateService.addGates.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.addGates(mockReq, mockRes, mockNext);

      expect(gateService.addGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('listGates', () => {
    it('should list gates successfully', async () => {
      const mockResponse = [{ id: 1, name: 'Gate 1' }];
      const mockLastDetail = { id: 1 };

      gateService.listGates.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      gateService.lastGate.mockImplementation((req, callback) => {
        callback(mockLastDetail, null);
      });

      await GateController.listGates(mockReq, mockRes, mockNext);

      expect(gateService.listGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(gateService.lastGate).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate Listed successfully.',
        data: mockResponse,
        lastId: mockLastDetail,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from gate listing', async () => {
      const mockError = new Error('Service error');
      gateService.listGates.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.listGates(mockReq, mockRes, mockNext);

      expect(gateService.listGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle error from last gate', async () => {
      const mockResponse = [{ id: 1, name: 'Gate 1' }];
      const mockError = new Error('Last gate error');

      gateService.listGates.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      gateService.lastGate.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.listGates(mockReq, mockRes, mockNext);

      expect(gateService.listGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(gateService.lastGate).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('updateGates', () => {
    it('should update gates successfully with default message', async () => {
      const mockResponse = { id: 1, name: 'Updated Gate' };
      mockReq.body.isActive = false;

      gateService.updateGates.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await GateController.updateGates(mockReq, mockRes, mockNext);

      expect(gateService.updateGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate Updated successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should update gates successfully with activation message', async () => {
      const mockResponse = { id: 1, name: 'Updated Gate' };
      mockReq.body.isActive = true;

      gateService.updateGates.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await GateController.updateGates(mockReq, mockRes, mockNext);

      expect(gateService.updateGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate Activated Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from gate update', async () => {
      const mockError = new Error('Service error');
      gateService.updateGates.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.updateGates(mockReq, mockRes, mockNext);

      expect(gateService.updateGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deleteGates', () => {
    it('should delete gates successfully', async () => {
      const mockResponse = { id: 1, deleted: true };
      gateService.deleteGates.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await GateController.deleteGates(mockReq, mockRes, mockNext);

      expect(gateService.deleteGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate deleted successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from gate deletion', async () => {
      const mockError = new Error('Service error');
      gateService.deleteGates.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.deleteGates(mockReq, mockRes, mockNext);

      expect(gateService.deleteGates).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getMappedRequests', () => {
    it('should get mapped requests successfully', async () => {
      const mockResponse = {
        mappedRequest: [{ id: 1, request: 'test' }],
        gates: [{ id: 1, gate: 'test' }],
      };
      gateService.getMappedRequests.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await GateController.getMappedRequests(mockReq, mockRes, mockNext);

      expect(gateService.getMappedRequests).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate Mapped Bookings Listed Successfully.',
        data: {
          mappedRequest: mockResponse.mappedRequest,
          gates: mockResponse.gates,
        },
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from mapped requests', async () => {
      const mockError = new Error('Service error');
      gateService.getMappedRequests.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.getMappedRequests(mockReq, mockRes, mockNext);

      expect(gateService.getMappedRequests).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('deactivateGate', () => {
    it('should deactivate gate successfully', async () => {
      const mockResponse = { id: 1, deactivated: true };
      gateService.deactivateGate.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await GateController.deactivateGate(mockReq, mockRes, mockNext);

      expect(gateService.deactivateGate).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Gate Deactivated Successfully.',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from gate deactivation', async () => {
      const mockError = new Error('Service error');
      gateService.deactivateGate.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await GateController.deactivateGate(mockReq, mockRes, mockNext);

      expect(gateService.deactivateGate).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
