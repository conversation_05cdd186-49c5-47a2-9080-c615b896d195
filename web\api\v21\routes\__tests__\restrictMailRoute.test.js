const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  RestrictMailController: {
    addRestrictMail: jest.fn(),
    getRestrictMails: jest.fn(),
    updateRestrictMail: jest.fn(),
    deleteRestrictMail: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  restrictMailValidation: {
    addRestrictMail: jest.fn(),
    getRestrictMails: jest.fn(),
    updateRestrictMail: jest.fn(),
    deleteRestrictMail: jest.fn(),
  },
}));

jest.mock('../../middlewares/checkAdmin', () => ({
  isProjectAdmin: jest.fn(),
}));

describe('restrictMailRoute', () => {
  let router;
  let restrictMailRoute;
  let RestrictMailController;
  let passportConfig;
  let restrictMailValidation;
  let checkAdmin;
  let validate;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    restrictMailRoute = require('../restrictMailRoute');
    const controllers = require('../../controllers');
    RestrictMailController = controllers.RestrictMailController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    restrictMailValidation = validations.restrictMailValidation;
    checkAdmin = require('../../middlewares/checkAdmin');
    validate = require('express-validation').validate;
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = restrictMailRoute.router;
      
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Add specific route verifications based on actual routes
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = restrictMailRoute.router;
      const result2 = restrictMailRoute.router;
      
      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      restrictMailRoute.router;

      const allCalls = [...router.post.mock.calls, ...router.get.mock.calls];
      
      allCalls.forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof restrictMailRoute).toBe('object');
      expect(restrictMailRoute).toHaveProperty('router');
      
      const descriptor = Object.getOwnPropertyDescriptor(restrictMailRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(restrictMailRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});