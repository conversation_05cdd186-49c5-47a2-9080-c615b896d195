const excelConcreteReportService = require('../excelConcreteReportService');
const moment = require('moment');

// Mock moment to ensure consistent date formatting in tests
jest.mock('moment', () => {
  const actualMoment = jest.requireActual('moment');
  return jest.fn((date) => {
    if (date) {
      return actualMoment(date);
    }
    return actualMoment('2023-01-01T10:00:00Z');
  });
});

describe('ExcelConcreteReportService', () => {
  let mockWorkbook;
  let mockWorksheet;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock worksheet methods
    mockWorksheet = {
      addWorksheet: jest.fn(),
      getRow: jest.fn(() => ({
        values: null,
      })),
      columns: null,
      getCell: jest.fn(() => ({
        value: null,
      })),
      addRow: jest.fn(() => ({
        commit: jest.fn(),
      })),
    };

    // Mock workbook
    mockWorkbook = {
      addWorksheet: jest.fn(() => mockWorksheet),
    };
  });

  describe('concreteReport', () => {
    it('should be defined', () => {
      expect(excelConcreteReportService).toBeDefined();
      expect(excelConcreteReportService.concreteReport).toBeDefined();
    });

    it('should create concrete report with all headers active', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'status', title: 'Status', isActive: true },
        { key: 'approvedBy', title: 'Approved By', isActive: true },
        { key: 'company', title: 'Concrete Supplier', isActive: true },
        { key: 'orderNumber', title: 'Order Number', isActive: true },
        { key: 'slump', title: 'Slump', isActive: true },
        { key: 'truckSpacing', title: 'Truck Spacing', isActive: true },
        { key: 'primer', title: 'Primer Ordered', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'quantity', title: 'Quantity Ordered', isActive: true },
        { key: 'mixDesign', title: 'Mix Design', isActive: true },
        { key: 'location', title: 'Location', isActive: true },
      ];

      const responseData = [
        {
          ConcreteRequestId: 1,
          description: 'Test concrete request',
          concretePlacementStart: '2023-01-01T10:00:00Z',
          status: 'Approved',
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: 'Doe',
            },
          },
          concreteSupplierDetails: [
            {
              Company: {
                companyName: 'ABC Concrete',
              },
            },
          ],
          concreteOrderNumber: 'ORD-001',
          slump: '4-6 inches',
          truckSpacingHours: 2,
          primerForPump: 'Yes',
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'Jane',
                  lastName: 'Smith',
                },
              },
            },
          ],
          concreteQuantityOrdered: 50,
          mixDesignDetails: [
            {
              ConcreteMixDesign: {
                mixDesign: '3000 PSI',
              },
            },
          ],
          location: {
            locationPath: 'Building A / Floor 1',
          },
        },
      ];

      const timezoneoffset = -300; // EST offset

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Concrete Report');
      expect(mockWorksheet.getRow).toHaveBeenCalledWith(1);
      expect(mockWorksheet.addRow).toHaveBeenCalled();
      expect(result).toBe(mockWorkbook);
    });

    it('should handle empty response data', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
      ];
      const responseData = [];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith('Concrete Report');
      expect(result).toBe(mockWorkbook);
    });

    it('should handle inactive headers', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: false },
        { key: 'description', title: 'Description', isActive: true },
      ];
      const responseData = [
        {
          ConcreteRequestId: 1,
          description: 'Test description',
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle missing data fields with default values', async () => {
      const selectedHeaders = [
        { key: 'id', title: 'Id', isActive: true },
        { key: 'description', title: 'Description', isActive: true },
        { key: 'date', title: 'Date & Time', isActive: true },
        { key: 'approvedBy', title: 'Approved By', isActive: true },
        { key: 'company', title: 'Concrete Supplier', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'mixDesign', title: 'Mix Design', isActive: true },
        { key: 'location', title: 'Location', isActive: true },
      ];

      const responseData = [
        {
          ConcreteRequestId: 2,
          // Missing most fields to test default values
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });
  });

  // Test helper functions by accessing them through the service
  describe('Helper Functions', () => {
    it('should handle data with null approver details', async () => {
      const selectedHeaders = [
        { key: 'approvedBy', title: 'Approved By', isActive: true },
      ];
      const responseData = [
        {
          approverDetails: null,
        },
        {
          approverDetails: {
            User: null,
          },
        },
        {
          approverDetails: {
            User: {
              firstName: null,
              lastName: 'Doe',
            },
          },
        },
        {
          approverDetails: {
            User: {
              firstName: 'John',
              lastName: null,
            },
          },
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle complex selection with empty arrays', async () => {
      const selectedHeaders = [
        { key: 'company', title: 'Concrete Supplier', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
        { key: 'mixDesign', title: 'Mix Design', isActive: true },
      ];
      const responseData = [
        {
          concreteSupplierDetails: [],
          memberDetails: [],
          mixDesignDetails: [],
        },
        {
          concreteSupplierDetails: null,
          memberDetails: undefined,
          mixDesignDetails: null,
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle complex nested property paths', async () => {
      const selectedHeaders = [
        { key: 'company', title: 'Concrete Supplier', isActive: true },
        { key: 'name', title: 'Responsible Person', isActive: true },
      ];
      const responseData = [
        {
          concreteSupplierDetails: [
            {
              Company: {
                companyName: 'Company 1',
              },
            },
            {
              Company: {
                companyName: 'Company 2',
              },
            },
            {
              Company: null, // Should be filtered out
            },
            {
              // Missing Company property
            },
          ],
          memberDetails: [
            {
              Member: {
                User: {
                  firstName: 'John',
                  lastName: 'Doe',
                },
              },
            },
            {
              Member: {
                User: null, // Should be filtered out
              },
            },
            {
              Member: null, // Should be filtered out
            },
          ],
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle date formatting with different timezone offsets', async () => {
      const selectedHeaders = [
        { key: 'date', title: 'Date & Time', isActive: true },
      ];
      const responseData = [
        {
          concretePlacementStart: '2023-01-01T10:00:00Z',
        },
        {
          concretePlacementStart: '2023-12-31T23:59:59Z',
        },
        {
          concretePlacementStart: null,
        },
        {
          concretePlacementStart: undefined,
        },
      ];

      // Test with different timezone offsets
      const timezoneOffsets = [0, -300, 300, -480];

      for (const offset of timezoneOffsets) {
        const result = await excelConcreteReportService.concreteReport(
          mockWorkbook,
          responseData,
          selectedHeaders,
          offset
        );
        expect(result).toBe(mockWorkbook);
      }
    });

    it('should handle headers with unknown keys', async () => {
      const selectedHeaders = [
        { key: 'unknownKey', title: 'Unknown', isActive: true },
        { key: 'id', title: 'Id', isActive: true },
      ];
      const responseData = [
        {
          ConcreteRequestId: 1,
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });

    it('should handle location with null locationPath', async () => {
      const selectedHeaders = [
        { key: 'location', title: 'Location', isActive: true },
      ];
      const responseData = [
        {
          location: null,
        },
        {
          location: {
            locationPath: null,
          },
        },
        {
          location: {
            locationPath: 'Valid Location',
          },
        },
        {
          // Missing location property
        },
      ];
      const timezoneoffset = 0;

      const result = await excelConcreteReportService.concreteReport(
        mockWorkbook,
        responseData,
        selectedHeaders,
        timezoneoffset
      );

      expect(result).toBe(mockWorkbook);
    });
  });
});
