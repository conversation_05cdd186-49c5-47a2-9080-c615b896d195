const { Sequelize, Enterprise } = require('../models');
let { DeviceToken } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const deviceTokenService = {
  async setDeviceToken(inputData, done) {
    try {
      const { user: loginUser, body: { deviceToken } } = inputData;
      await this.getDynamicModel(inputData);
      const devRes = await DeviceToken.findOne({ where: { UserId: loginUser.id } });
      if (devRes) {
        const updateDevice = await DeviceToken.update(
          { deviceToken },
          { where: { id: devRes.id } }
        );
        done(updateDevice, false);
      } else {
        const data = { UserId: loginUser.id, deviceToken };
        const newDevice = await DeviceToken.create(data);
        done(newDevice, false);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async clearDeviceToken(inputData, done) {
    try {
      const newToken = await DeviceToken.findOne({ where: { UserId: inputData.user.id } });
      if (newToken) {
        const clearToken = await newToken.destroy();
        done(clearToken, false);
      } else {
        done('Device Token Not Found.!', false);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getDomainEnterpriseValue(domainName) {
    if (!domainName) return null;
    const enterprise = await Enterprise.findOne({ where: { name: domainName.toLowerCase() } });
    return enterprise || '';
  },

  async getEnterpriseValue(ParentCompanyId) {
    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' }
    });
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId 
      || inputData.params.ParentCompanyId;

    let domainEnterpriseValue = await this.getDomainEnterpriseValue(domainName);
    if (!domainEnterpriseValue && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      domainName = await this.deriveDomainName(inputData, ParentCompanyId);
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeviceToken = modelObj.DeviceToken;
  },

  async deriveDomainName(inputData, ParentCompanyId) {
    const { email } = inputData.user;
    if (!email) return '';

    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) return '';

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false }
    });

    if (memberData) {
      const enterpriseValue = memberData.isAccount
        ? await Enterprise.findOne({
            where: { id: memberData.EnterpriseId, status: 'completed' }
          })
        : await this.getEnterpriseValue(ParentCompanyId);

      return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
    } else {
      const enterpriseValue = await this.getEnterpriseValue(ParentCompanyId);
      return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
    }
  }
};

module.exports = deviceTokenService;