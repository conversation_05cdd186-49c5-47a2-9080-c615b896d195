/* eslint-disable no-await-in-loop */
/* eslint-disable no-continue */
/* eslint-disable no-loop-func */
const moment = require('moment');
const status = require('http-status');
const Moment = require('moment');
const MomentRange = require('moment-range');
const momenttz = require('moment-timezone');

const momentRange = MomentRange.extendMoment(Moment);

const { Sequelize, Enterprise, Project, TimeZone } = require('../models');
let { CalendarSetting, Member, User } = require('../models');
const helper = require('../helpers/domainHelper');
const ApiError = require('../helpers/apiError');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const calendarSettingsService = {
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async findDomainEnterprise(domainName) {
    if (!domainName) return null;
    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });
    return domainEnterpriseValue ? domainName : '';
  },
  async findUserByEmail(email) {
    if (!email) return null;
    return publicUser.findOne({ where: { email } });
  },
  async findMemberByUser(userData) {
    if (!userData) return null;
    return publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });
  },
  async findEnterpriseByMember(memberData, ParentCompanyId) {
    if (!memberData) return null;

    if (memberData.isAccount) {
      const enterprise = await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' },
      });
      return enterprise ? enterprise.name.toLowerCase() : null;
    }

    const enterprise = await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
    return enterprise ? enterprise.name.toLowerCase() : null;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let domainName = inputData.user?.domainName || '';
    let enterpriseValue;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body?.ParentCompanyId || inputData.params?.ParentCompanyId;
    const ProjectId = inputData.query?.ProjectId || inputData.params?.ProjectId;

    domainName = await this.findDomainEnterprise(domainName);

    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const userData = await this.findUserByEmail(inputData.user?.email);
      if (userData) {
        const memberData = await this.findMemberByUser(userData);
        if (memberData) {
          domainName = await this.findEnterpriseByMember(memberData, ParentCompanyId);
        } else {
          const enterprise = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          domainName = enterprise?.name?.toLowerCase() || '';
        }
      }
    }

    const modelObj = await helper.getDynamicModel(domainName);
    Member = modelObj.Member;
    CalendarSetting = modelObj.CalendarSetting;
    User = modelObj.User;

    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user?.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async validateMember(loginUser, ProjectId, ParentCompanyId) {
    return Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId,
        isDeleted: false,
        ParentCompanyId,
      }),
    });
  },
  buildSearchCondition(ProjectId, search, isApplicableToDelivery, isApplicableToCrane, isApplicableToConcrete, isApplicableToInspection) {
    const condition = {
      ProjectId,
      isDeleted: false,
    };

    if (search) {
      condition.description = { [Op.iLike]: `%${search}%` };
    }

    if (isApplicableToDelivery) condition.isApplicableToDelivery = true;
    if (isApplicableToCrane) condition.isApplicableToCrane = true;
    if (isApplicableToConcrete) condition.isApplicableToConcrete = true;
    if (isApplicableToInspection) condition.isApplicableToInspection = true;

    return condition;
  },
  async fetchEvents(req, condition) {
    if (req.query.weeklyReportTest === 'weeklyReport') {
      return CalendarSetting.getAllWeeklyReport({
        ProjectId: +req.query.ProjectId,
        isDeleted: false,
      });
    }
    return CalendarSetting.getAll(condition);
  },
  async processEvent(eventObject, eventTimeZone, uniqueNumber) {
    const range = momentRange.range(moment(eventObject.fromDate), moment(eventObject.toDate));
    const totalDays = Array.from(range.by('day'));

    switch (eventObject.recurrence) {
      case 'Does Not Repeat': {
        return this.processNonRepeatingEvent(eventObject, eventTimeZone, totalDays, uniqueNumber);
      }
      case 'Daily': {
        return this.processDailyEvent(eventObject, eventTimeZone, totalDays, uniqueNumber);
      }
      case 'Weekly': {
        return this.processWeeklyEvent(eventObject, eventTimeZone, uniqueNumber);
      }
      case 'Monthly':
      case 'Yearly': {
        return this.processMonthlyYearlyEvent(eventObject, eventTimeZone, uniqueNumber);
      }
      default:
        return { events: [], uniqueNumber };
    }
  },
  async processNonRepeatingEvent(eventObject, eventTimeZone, totalDays, uniqueNumber) {
    const events = [];
    const startDate = totalDays[0];
    const endDate = totalDays[totalDays.length - 1];

    for (const data of totalDays) {
      uniqueNumber += 1;
      const recurrenceObject = await this.createRecurrenceObject(
        eventObject,
        eventTimeZone,
        data.toDate(),
        startDate,
        endDate,
        uniqueNumber,
        eventObject.recurrence,
      );
      events.push(recurrenceObject);
    }
    return { events, uniqueNumber };
  },
  async processDailyEvent(eventObject, eventTimeZone, totalDays, uniqueNumber) {
    const events = [];
    const startDate = totalDays[0];
    const endDate = totalDays[totalDays.length - 1];
    let dailyIndex = 0;

    while (dailyIndex < totalDays.length) {
      const data = totalDays[dailyIndex];
      uniqueNumber += 1;
      const recurrenceObject = await this.createRecurrenceObject(
        eventObject,
        eventTimeZone,
        data.toDate(),
        startDate,
        endDate,
        uniqueNumber,
        eventObject.recurrence,
      );
      events.push(recurrenceObject);
      dailyIndex += +eventObject.repeatEveryCount;
    }
    return { events, uniqueNumber };
  },
  async getAll(req, next) {
    try {
      const { ProjectId, ParentCompanyId, search } = req.query;
      const { isApplicableToDelivery, isApplicableToCrane, isApplicableToConcrete, isApplicableToInspection } = req.body;

      await this.getDynamicModel(req);
      const loginUser = req.user;

      const isMemberExists = await this.validateMember(loginUser, ProjectId, ParentCompanyId);
      if (!isMemberExists) return [];

      const condition = this.buildSearchCondition(
        ProjectId,
        search,
        isApplicableToDelivery,
        isApplicableToCrane,
        isApplicableToConcrete,
        isApplicableToInspection
      );

      const events = await this.fetchEvents(req, condition);
      if (!events.length) return events;

      const eventsArray = [];
      let uniqueNumber = 0;

      for (const eventObject of events) {
        const eventTimeZone = await TimeZone.findOne({
          where: {
            isDeleted: false,
            id: +eventObject.TimeZoneId,
          },
          attributes: [
            'id',
            'location',
            'isDayLightSavingEnabled',
            'timeZoneOffsetInMinutes',
            'dayLightSavingTimeInMinutes',
            'timezone',
          ],
        });

        if (!eventObject) continue;

        const { events: processedEvents, uniqueNumber: newUniqueNumber } =
          await this.processEvent(eventObject, eventTimeZone, uniqueNumber);

        eventsArray.push(...processedEvents);
        uniqueNumber = newUniqueNumber;
      }

      if (!eventsArray.length) return eventsArray;

      if (req.query.weeklyReportTest === 'weeklyReport') {
        return this.filterWeeklyReportEvents(eventsArray, req);
      }

      return this.filterRegularEvents(eventsArray, req);
    } catch (e) {
      const newError = new ApiError(e, status.BAD_REQUEST);
      next(newError);
    }
  },
  isDateInRange(date, eventObject) {
    return moment(date).isBetween(moment(eventObject.fromDate), moment(eventObject.endDate), null, '[]') ||
      moment(date).isSame(eventObject.fromDate) ||
      moment(date).isSame(eventObject.endDate);
  },
  getFinalDayForMonth(monthNew, eventObject) {
    const dayOfMonth = eventObject.monthlyRepeatType;
    const [week, day] = dayOfMonth.split(' ').map(s => s.toLowerCase());
    const chosenDay = moment(monthNew, 'YYYY-MM').startOf('month').day(day);

    if (chosenDay.date() > 7) chosenDay.add(7, 'd');

    const getAllDays = [];
    const month = chosenDay.month();
    while (month === chosenDay.month()) {
      getAllDays.push(chosenDay.format('YYYY-MM-DD'));
      chosenDay.add(7, 'd');
    }

    const weekIndex = {
      first: 0,
      second: 1,
      third: 2,
      fourth: 3,
      last: getAllDays.length - 1,
    }[week];

    return getAllDays[weekIndex];
  },
  filterWeeklyReportEvents(eventsArray, req) {
    let startingDate = moment(req.query.start).format('YYYY-MM-DD');
    let endingDate = moment(req.query.end).format('YYYY-MM-DD');

    if (req.body?.startDate && req.body?.endDate) {
      startingDate = moment(req.body.startDate).format('YYYY-MM-DD');
      endingDate = moment(req.body.endDate).format('YYYY-MM-DD');
    }

    const nextDay = moment(startingDate).add(1, 'days').format('YYYY-MM-DD');
    return eventsArray.filter(data => {
      const target = momenttz.utc(data.fromDate, 'YYYY-MM-DD HH:mm:ssZ').tz(req.body?.timezone);
      const timeRangeCondition = target.format('HH:mm');
      const compare = moment(target.format('YYYY-MM-DD'));

      if (req.body?.eventStartTime < req.body?.eventEndTime) {
        return compare.isBetween(startingDate, endingDate, null, '[]') &&
          timeRangeCondition <= req.body?.eventEndTime &&
          timeRangeCondition >= req.body?.eventStartTime;
      }

      return (compare.isBetween(startingDate, endingDate, null, '[]') &&
        timeRangeCondition >= req.body?.eventStartTime &&
        timeRangeCondition <= '23:59:59') ||
        (compare.isBetween(nextDay, endingDate, null, '[]') &&
          timeRangeCondition >= '00:00:00' &&
          timeRangeCondition <= req.body?.eventEndTime);
    });
  },
  filterRegularEvents(eventsArray, req) {
    const startingDate = moment(req.query.start).subtract(2, 'days').format('YYYY-MM-DD');
    const endingDate = moment(req.query.end).add(2, 'days').format('YYYY-MM-DD');

    return eventsArray.filter(data =>
      moment(moment(data.fromDate).format('YYYY-MM-DD')).isBetween(
        startingDate,
        endingDate,
        null,
        '[]',
      )
    );
  },
  async addEvent(req) {
    try {
      await this.getDynamicModel(req);
      const event = req.body;
      event.EquipmentId = event.EquipmentId ? JSON.stringify(event.EquipmentId) : '';
      event.GateId = event.GateId ? JSON.stringify(event.GateId) : '';
      event.LocationId = event.LocationId ? JSON.stringify(event.LocationId) : '';
      event.ProjectId = req.query.ProjectId;
      event.ParentCompanyId = req.query.ParentCompanyId;
      const newEvent = await CalendarSetting.createInstance(event);
      return newEvent;
    } catch (e) {
      console.log(e);
    }
  },
  async updateEvent(req, next) {
    try {
      await this.getDynamicModel(req);
      const { id } = req.params;
      const isEventExists = await CalendarSetting.isExits(id);
      if (isEventExists) {
        let data = {};
        const { ProjectId } = req.query;
        const { ParentCompanyId } = req.query;
        data = req.body;
        data.ProjectId = ProjectId;
        data.ParentCompanyId = ParentCompanyId;
        data.EquipmentId = data.EquipmentId ? JSON.stringify(data.EquipmentId) : '';
        data.GateId = data.GateId ? JSON.stringify(data.GateId) : '';
        data.LocationId = data.LocationId ? JSON.stringify(data.LocationId) : '';
        const updatedEvent = await CalendarSetting.updateInstance(+id, data);
        return updatedEvent;
      }
      const error = new ApiError('No event found', status.NOT_FOUND);
      next(error);
    } catch (e) {
      console.log(e);
    }
  },
  async getCalendarEvent(req, next) {
    try {
      const { ProjectId } = req.query;
      const { ParentCompanyId } = req.query;
      const { id } = req.params;
      await this.getDynamicModel(req);
      const loginUser = req.user;
      const isMemberExists = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          ParentCompanyId,
        }),
      });
      if (isMemberExists) {
        const event = await CalendarSetting.getOne(id);
        const eventTimeZone = await TimeZone.findOne({
          where: {
            isDeleted: false,
            id: +event.TimeZoneId,
          },
          attributes: [
            'id',
            'location',
            'isDayLightSavingEnabled',
            'timeZoneOffsetInMinutes',
            'dayLightSavingTimeInMinutes',
            'timezone',
          ],
        });
        if (event) {
          let startTime1 = '';
          let startTime2 = '';
          startTime1 = await this.convertTimezoneToUtc(
            moment(event.fromDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            event.startTime,
          );
          startTime2 = await this.convertTimezoneToUtc(
            moment(event.toDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            event.endTime,
          );
          let fromDate1 = '';
          let toDate1 = '';
          let endDate1 = '';
          fromDate1 = await this.convertTimezoneToUtc(
            moment(event.fromDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          );
          toDate1 = await this.convertTimezoneToUtc(
            moment(event.toDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          );
          endDate1 = await this.convertTimezoneToUtc(
            moment(event.endDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          );
          event.startTime = '';
          event.endTime = '';
          event.toDate = '';
          event.fromDate = '';
          event.startTime = startTime1;
          event.endTime = startTime2;
          event.fromDate = fromDate1;
          event.toDate = toDate1;
          event.endDate = endDate1;
          return event;
        }
        return {};
      }
    } catch (e) {
      const newError = new ApiError(e, status.BAD_REQUEST);
      next(newError);
    }
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
  async createRecurrenceObject(
    eventObject,
    eventTimeZone,
    data,
    startDate,
    endDate,
    uniqueNumber,
    recurrence,
  ) {
    const objectToAddEvents = {
      id: eventObject.id,
      description: eventObject.description,
      timeZoneLocation: eventObject.TimeZone.location,
      durationInMinutes: '',
      fromDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).startOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '00:00',
        )
        : await this.convertTimezoneToUtc(
          moment(data).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.startTime,
        ),
      toDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).endOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '23:59',
        )
        : await this.convertTimezoneToUtc(
          moment(data).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.endTime,
        ),
      startTime: await this.convertTimezoneToUtc(
        moment(startDate).format('MM/DD/YYYY'),
        eventTimeZone.timezone,
        eventObject.startTime,
      ),
      endTime: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).startOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '00:00',
        )
        : await this.convertTimezoneToUtc(
          moment(endDate).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.endTime,
        ),
      repeatEveryType: eventObject.repeatEveryType,
      repeatEveryCount: eventObject.repeatEveryCount,
      days: eventObject.days,
      isAllDay: eventObject.isAllDay,
      uniqueNumber,
      requestType: 'calendarEvent',
    };
    if (recurrence === 'Monthly' || recurrence === 'Yearly') {
      objectToAddEvents.chosenDateOfMonth = eventObject.chosenDateOfMonth;
      objectToAddEvents.dateOfMonth = eventObject.dateOfMonth;
      objectToAddEvents.monthlyRepeatType = eventObject.monthlyRepeatType;
    }
    objectToAddEvents.durationInMinutes = moment(objectToAddEvents.toDate)
      .diff(moment(objectToAddEvents.fromDate), 'minutes')
      .toString();
    return objectToAddEvents;
  },
  async processWeeklyEvent(eventObject, eventTimeZone, uniqueNumber) {
    const startDayWeek = moment(eventObject.fromDate).startOf('week');
    const endDayWeek = moment(eventObject.endDate).endOf('week');
    const range = momentRange.range(moment(startDayWeek), moment(endDayWeek));
    const totalDaysOfRecurrence = Array.from(range.by('day'));
    const startDate = moment(eventObject.fromDate);
    const endDate = moment(eventObject.endDate);
    const events = [];

    const repeatEveryCount = +eventObject.repeatEveryCount || 1;
    const weekIncrement = repeatEveryCount > 1 ? 7 * repeatEveryCount : 7;

    // Simple approach: process all days and filter by repeat count
    for (let i = 0; i < totalDaysOfRecurrence.length; i += weekIncrement) {
      const weekRange = totalDaysOfRecurrence.slice(i, i + 7);

      for (const data of weekRange) {
        if (!data || moment(data).isBefore(eventObject.fromDate) || moment(data).isAfter(eventObject.endDate)) {
          continue;
        }

        const day = moment(data).format('dddd');
        if (eventObject?.days.includes(day)) {
          uniqueNumber += 1;
          const recurrenceObject = await this.createRecurrenceObject(
            eventObject,
            eventTimeZone,
            data.toDate(),
            startDate,
            endDate,
            uniqueNumber,
            eventObject.recurrence,
          );
          events.push(recurrenceObject);
        }
      }
    }

    return { events, uniqueNumber };
  },
  async processMonthlyYearlyEvent(eventObject, eventTimeZone, uniqueNumber) {
    const startDate = moment(eventObject.fromDate, 'YYYY-MM-DD');
    const endDate = moment(eventObject.endDate, 'YYYY-MM-DD').endOf('month');
    const events = [];

    const monthRanges = this.generateMonthRanges(startDate, endDate, eventObject.recurrence, eventObject.repeatEveryCount);

    for (const month of monthRanges) {
      if (!month) continue;

      const event = await this.processMonthEvent(month, eventObject, eventTimeZone, uniqueNumber);
      if (event) {
        events.push(event);
        uniqueNumber++;
      }
    }

    return { events, uniqueNumber };
  },
  generateMonthRanges(startDate, endDate, recurrence, repeatEveryCount) {
    const allMonthsInPeriod = [];
    let currentDate = startDate.clone();

    while (currentDate.isBefore(endDate)) {
      allMonthsInPeriod.push(currentDate.format('YYYY-MM'));
      currentDate = recurrence === 'Monthly'
        ? currentDate.add(1, 'month')
        : currentDate.add(12, 'month');
    }

    return Array.from(
      { length: allMonthsInPeriod.length + 1 },
      (_, i) => allMonthsInPeriod[i * (recurrence === 'Monthly' ? +repeatEveryCount : 1)]
    );
  },
  async processMonthEvent(month, eventObject, eventTimeZone, uniqueNumber) {
    const currentMonthDates = Array.from(
      { length: moment(month, 'YYYY-MM').daysInMonth() },
      (_, j) => moment(month, 'YYYY-MM').startOf('month').add(j, 'days')
    );

    if (eventObject?.chosenDateOfMonth) {
      const getDate = currentMonthDates.find(
        value => moment(value).format('DD') === eventObject?.dateOfMonth
      );

      if (getDate && this.isDateInRange(getDate, eventObject)) {
        return this.createRecurrenceObject(
          eventObject,
          eventTimeZone,
          getDate.toDate(),
          moment(eventObject.fromDate),
          eventObject.endDate,
          uniqueNumber,
          eventObject.recurrence
        );
      }
    } else {
      const finalDay = this.getFinalDayForMonth(month, eventObject);
      if (finalDay && this.isDateInRange(finalDay, eventObject)) {
        return this.createRecurrenceObject(
          eventObject,
          eventTimeZone,
          finalDay,
          moment(eventObject.fromDate),
          eventObject.endDate,
          uniqueNumber,
          eventObject.recurrence
        );
      }
    }
    return null;
  },
};
module.exports = calendarSettingsService;
