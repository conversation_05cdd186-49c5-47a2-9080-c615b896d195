const deviceTokenService = require('../deviceTokenService');
const helper = require('../../helpers/domainHelper');

// Mock dependencies
jest.mock('../../models', () => ({
  Sequelize: {
    Op: {
      ne: Symbol('ne'),
    },
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  User: {
    findOne: jest.fn(),
  },
  Member: {
    findOne: jest.fn(),
  },
}));

jest.mock('../../helpers/domainHelper', () => ({
  getDynamicModel: jest.fn(),
  returnProjectModel: jest.fn(),
}));

describe('DeviceTokenService', () => {
  let mockInputData;
  let mockDone;
  let mockModels;

  beforeEach(() => {
    jest.clearAllMocks();

    mockModels = require('../../models');

    mockInputData = {
      user: {
        id: 1,
        email: '<EMAIL>',
        domainName: 'testdomain',
      },
      body: {
        ParentCompanyId: 1,
        deviceToken: 'test-device-token-123',
      },
      params: {
        ParentCompanyId: 1,
      },
    };

    mockDone = jest.fn();

    // Setup default mock implementations
    helper.returnProjectModel.mockResolvedValue({
      User: mockModels.User,
      Member: mockModels.Member,
    });

    mockModels.Enterprise.findOne.mockResolvedValue({
      name: 'testdomain',
      id: 1,
    });
  });

  describe('Basic functionality', () => {
    it('should be defined', () => {
      expect(deviceTokenService).toBeDefined();
      expect(typeof deviceTokenService).toBe('object');
    });
  });

  describe('getDomainEnterpriseValue', () => {
    it('should return null when domainName is null', async () => {
      const result = await deviceTokenService.getDomainEnterpriseValue(null);
      expect(result).toBeNull();
    });

    it('should return null when domainName is undefined', async () => {
      const result = await deviceTokenService.getDomainEnterpriseValue(undefined);
      expect(result).toBeNull();
    });

    it('should return enterprise when found', async () => {
      const enterprise = { id: 1, name: 'testdomain' };
      mockModels.Enterprise.findOne.mockResolvedValue(enterprise);

      const result = await deviceTokenService.getDomainEnterpriseValue('TestDomain');

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { name: 'testdomain' }
      });
      expect(result).toBe(enterprise);
    });

    it('should return empty string when enterprise not found', async () => {
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deviceTokenService.getDomainEnterpriseValue('nonexistent');

      expect(result).toBe('');
    });
  });

  describe('getEnterpriseValue', () => {
    it('should return enterprise by ParentCompanyId', async () => {
      const enterprise = { id: 1, name: 'test', ParentCompanyId: 1, status: 'completed' };
      mockModels.Enterprise.findOne.mockResolvedValue(enterprise);

      const result = await deviceTokenService.getEnterpriseValue(1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' }
      });
      expect(result).toBe(enterprise);
    });
  });

  describe('returnProjectModel', () => {
    it('should call helper.returnProjectModel', async () => {
      await deviceTokenService.returnProjectModel();
      expect(helper.returnProjectModel).toHaveBeenCalled();
    });
  });

  describe('setDeviceToken', () => {
    it('should handle update existing device token', async () => {
      const existingToken = { id: 1, UserId: 1 };
      const mockDeviceToken = {
        findOne: jest.fn().mockResolvedValue(existingToken),
        update: jest.fn().mockResolvedValue([1])
      };

      helper.getDynamicModel.mockResolvedValue({
        DeviceToken: mockDeviceToken
      });

      await deviceTokenService.setDeviceToken(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith([1], false);
    });

    it('should handle create new device token', async () => {
      const newToken = { id: 2, UserId: 1, deviceToken: 'test-device-token-123' };
      const mockDeviceToken = {
        findOne: jest.fn().mockResolvedValue(null),
        create: jest.fn().mockResolvedValue(newToken)
      };

      helper.getDynamicModel.mockResolvedValue({
        DeviceToken: mockDeviceToken
      });

      await deviceTokenService.setDeviceToken(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(newToken, false);
    });

    it('should handle errors', async () => {
      const error = new Error('Database error');
      helper.getDynamicModel.mockRejectedValue(error);

      await deviceTokenService.setDeviceToken(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('clearDeviceToken', () => {
    it('should clear existing device token successfully', async () => {
      const mockToken = {
        id: 1,
        UserId: 1,
        deviceToken: 'test-token',
        destroy: jest.fn().mockResolvedValue(true)
      };

      // First call getDynamicModel to set up DeviceToken
      helper.getDynamicModel.mockResolvedValue({
        DeviceToken: {
          findOne: jest.fn().mockResolvedValue(mockToken)
        }
      });

      await deviceTokenService.getDynamicModel(mockInputData);
      await deviceTokenService.clearDeviceToken(mockInputData, mockDone);

      expect(mockToken.destroy).toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(true, false);
    });

    it('should handle device token not found', async () => {
      helper.getDynamicModel.mockResolvedValue({
        DeviceToken: {
          findOne: jest.fn().mockResolvedValue(null)
        }
      });

      await deviceTokenService.getDynamicModel(mockInputData);
      await deviceTokenService.clearDeviceToken(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith('Device Token Not Found.!', false);
    });

    it('should handle errors in clearDeviceToken', async () => {
      const error = new Error('Database connection failed');

      helper.getDynamicModel.mockResolvedValue({
        DeviceToken: {
          findOne: jest.fn().mockRejectedValue(error)
        }
      });

      await deviceTokenService.getDynamicModel(mockInputData);
      await deviceTokenService.clearDeviceToken(mockInputData, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, error);
    });
  });

  describe('getDynamicModel', () => {
    it('should call required methods with existing domain enterprise', async () => {
      // Mock enterprise found
      mockModels.Enterprise.findOne.mockResolvedValue({ id: 1, name: 'testdomain' });

      await deviceTokenService.getDynamicModel(mockInputData);

      expect(helper.returnProjectModel).toHaveBeenCalled();
      expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
    });

    it('should handle deriveDomainName when no enterprise found and ParentCompanyId exists', async () => {
      // Mock no enterprise found
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      // Mock User and Member for deriveDomainName
      mockModels.User.findOne.mockResolvedValue({ id: 1 });
      mockModels.Member.findOne.mockResolvedValue({ isAccount: false });

      // Mock enterprise for getEnterpriseValue
      mockModels.Enterprise.findOne
        .mockResolvedValueOnce(null) // First call for getDomainEnterpriseValue
        .mockResolvedValueOnce({ name: 'DerivedDomain' }); // Second call for getEnterpriseValue

      await deviceTokenService.getDynamicModel(mockInputData);

      expect(helper.getDynamicModel).toHaveBeenCalledWith('deriveddomain');
    });

    it('should handle ParentCompanyId from params', async () => {
      const inputWithParamsParentCompanyId = {
        ...mockInputData,
        body: {},
        params: { ParentCompanyId: 2 }
      };

      // Mock no enterprise found
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      // Mock User and Member for deriveDomainName
      mockModels.User.findOne.mockResolvedValue({ id: 1 });
      mockModels.Member.findOne.mockResolvedValue({ isAccount: false });

      // Mock enterprise for getEnterpriseValue with ParentCompanyId 2
      mockModels.Enterprise.findOne
        .mockResolvedValueOnce(null) // First call for getDomainEnterpriseValue
        .mockResolvedValueOnce({ name: 'DerivedDomain' }); // Second call for getEnterpriseValue with ParentCompanyId 2

      await deviceTokenService.getDynamicModel(inputWithParamsParentCompanyId);

      expect(helper.getDynamicModel).toHaveBeenCalledWith('deriveddomain');
    });

    it('should not call deriveDomainName when ParentCompanyId is undefined', async () => {
      const inputWithoutParentCompanyId = {
        ...mockInputData,
        body: {},
        params: {}
      };

      // Mock no enterprise found
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      await deviceTokenService.getDynamicModel(inputWithoutParentCompanyId);

      // Should use original domain name since no ParentCompanyId to derive from
      expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
    });

    it('should not call deriveDomainName when ParentCompanyId is string "undefined"', async () => {
      const inputWithStringUndefined = {
        ...mockInputData,
        body: { ParentCompanyId: 'undefined' },
        params: {}
      };

      // Mock no enterprise found
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      await deviceTokenService.getDynamicModel(inputWithStringUndefined);

      // Should use original domain name since ParentCompanyId is 'undefined'
      expect(helper.getDynamicModel).toHaveBeenCalledWith('testdomain');
    });
  });

  describe('deriveDomainName', () => {
    beforeEach(async () => {
      // Set up publicUser and publicMember by calling returnProjectModel
      await deviceTokenService.returnProjectModel();
      jest.clearAllMocks(); // Clear mocks after setup
    });

    it('should return empty string when no email', async () => {
      const inputWithoutEmail = { ...mockInputData, user: { ...mockInputData.user, email: null } };

      const result = await deviceTokenService.deriveDomainName(inputWithoutEmail, 1);

      expect(result).toBe('');
    });

    it('should return empty string when user not found', async () => {
      mockModels.User.findOne.mockResolvedValue(null);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(mockModels.User.findOne).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
      expect(result).toBe('');
    });

    it('should handle member with isAccount true', async () => {
      const userData = { id: 1 };
      const memberData = { isAccount: true, EnterpriseId: 1 };
      const enterprise = { name: 'TestEnterprise', status: 'completed' };

      mockModels.User.findOne.mockResolvedValue(userData);
      mockModels.Member.findOne.mockResolvedValue(memberData);
      mockModels.Enterprise.findOne.mockResolvedValue(enterprise);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(mockModels.User.findOne).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
      expect(mockModels.Member.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, RoleId: { [mockModels.Sequelize.Op.ne]: 4 }, isDeleted: false }
      });
      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { id: 1, status: 'completed' }
      });
      expect(result).toBe('testenterprise');
    });

    it('should handle member with isAccount false', async () => {
      const userData = { id: 1 };
      const memberData = { isAccount: false };
      const enterprise = { name: 'TestEnterprise' };

      mockModels.User.findOne.mockResolvedValue(userData);
      mockModels.Member.findOne.mockResolvedValue(memberData);
      mockModels.Enterprise.findOne.mockResolvedValue(enterprise);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' }
      });
      expect(result).toBe('testenterprise');
    });

    it('should handle no member found', async () => {
      const userData = { id: 1 };
      const enterprise = { name: 'TestEnterprise' };

      mockModels.User.findOne.mockResolvedValue(userData);
      mockModels.Member.findOne.mockResolvedValue(null);
      mockModels.Enterprise.findOne.mockResolvedValue(enterprise);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(mockModels.Enterprise.findOne).toHaveBeenCalledWith({
        where: { ParentCompanyId: 1, status: 'completed' }
      });
      expect(result).toBe('testenterprise');
    });

    it('should return empty string when no enterprise found for isAccount true', async () => {
      const userData = { id: 1 };
      const memberData = { isAccount: true, EnterpriseId: 1 };

      mockModels.User.findOne.mockResolvedValue(userData);
      mockModels.Member.findOne.mockResolvedValue(memberData);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(result).toBe('');
    });

    it('should return empty string when no enterprise found for isAccount false', async () => {
      const userData = { id: 1 };
      const memberData = { isAccount: false };

      mockModels.User.findOne.mockResolvedValue(userData);
      mockModels.Member.findOne.mockResolvedValue(memberData);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(result).toBe('');
    });

    it('should return empty string when no enterprise found and no member', async () => {
      const userData = { id: 1 };

      mockModels.User.findOne.mockResolvedValue(userData);
      mockModels.Member.findOne.mockResolvedValue(null);
      mockModels.Enterprise.findOne.mockResolvedValue(null);

      const result = await deviceTokenService.deriveDomainName(mockInputData, 1);

      expect(result).toBe('');
    });
  });
});