// Mock craneRequestHistoryService
jest.mock('../../services', () => ({
  craneRequestHistoryService: {
    createCraneRequestHistory: jest.fn(),
    getCraneRequestHistories: jest.fn(),
  },
}));

const craneRequestHistoryController = require('../craneRequestHistoryController');
const { craneRequestHistoryService } = require('../../services');

describe('craneRequestHistoryController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    mockReq = {
      body: {},
      params: {},
      user: { id: 1 },
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('createCraneRequestHistory', () => {
    it('should create crane request history successfully', async () => {
      const mockResponse = { id: 1, action: 'created', timestamp: '2023-01-01T10:00:00Z' };

      craneRequestHistoryService.createCraneRequestHistory.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestHistoryController.createCraneRequestHistory(mockReq, mockRes, mockNext);

      expect(craneRequestHistoryService.createCraneRequestHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking History created successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from create crane request history', async () => {
      const mockError = new Error('Service error');
      craneRequestHistoryService.createCraneRequestHistory.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestHistoryController.createCraneRequestHistory(mockReq, mockRes, mockNext);

      expect(craneRequestHistoryService.createCraneRequestHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in create crane request history', async () => {
      const mockError = new Error('Exception error');
      craneRequestHistoryService.createCraneRequestHistory.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestHistoryController.createCraneRequestHistory(mockReq, mockRes, mockNext);

      expect(craneRequestHistoryService.createCraneRequestHistory).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getCraneRequestHistories', () => {
    it('should get crane request histories successfully', async () => {
      const mockResponse = [
        { id: 1, action: 'created', timestamp: '2023-01-01T10:00:00Z' },
        { id: 2, action: 'updated', timestamp: '2023-01-01T11:00:00Z' },
      ];

      craneRequestHistoryService.getCraneRequestHistories.mockImplementation((req, callback) => {
        callback(mockResponse, null);
      });

      await craneRequestHistoryController.getCraneRequestHistories(mockReq, mockRes, mockNext);

      expect(craneRequestHistoryService.getCraneRequestHistories).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Crane Booking History Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from get crane request histories', async () => {
      const mockError = new Error('Service error');
      craneRequestHistoryService.getCraneRequestHistories.mockImplementation((req, callback) => {
        callback(null, mockError);
      });

      await craneRequestHistoryController.getCraneRequestHistories(mockReq, mockRes, mockNext);

      expect(craneRequestHistoryService.getCraneRequestHistories).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle exception in get crane request histories', async () => {
      const mockError = new Error('Exception error');
      craneRequestHistoryService.getCraneRequestHistories.mockImplementation(() => {
        throw mockError;
      });

      await craneRequestHistoryController.getCraneRequestHistories(mockReq, mockRes, mockNext);

      expect(craneRequestHistoryService.getCraneRequestHistories).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(mockError);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});