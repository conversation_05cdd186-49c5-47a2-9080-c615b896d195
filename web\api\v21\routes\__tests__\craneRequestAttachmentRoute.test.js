const { Router } = require('express');

// Mock dependencies
jest.mock('express', () => ({
  Router: jest.fn(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  })),
}));

jest.mock('express-validation', () => ({
  validate: jest.fn(() => 'mocked-validate-middleware'),
}));

jest.mock('multer', () => {
  const mockMulter = jest.fn(() => ({
    array: jest.fn(() => 'mocked-multer-middleware'),
  }));
  mockMulter.memoryStorage = jest.fn(() => 'mocked-storage');
  return mockMulter;
});

jest.mock('../../config/passport', () => ({
  isAuthenticated: jest.fn(),
}));

jest.mock('../../controllers', () => ({
  craneRequestAttachmentController: {
    createCraneRequestAttachement: jest.fn(),
    deleteCraneRequestAttachement: jest.fn(),
    getCraneRequestAttachements: jest.fn(),
  },
}));

jest.mock('../../middlewares/validations', () => ({
  craneRequestAttachmentValidation: {
    createCraneRequestAttachement: jest.fn(),
    deleteCraneRequestAttachement: jest.fn(),
    getCraneRequestAttachement: jest.fn(),
  },
}));

describe('craneRequestAttachmentRoute', () => {
  let router;
  let craneRequestAttachmentRoute;
  let craneRequestAttachmentController;
  let passportConfig;
  let craneRequestAttachmentValidation;
  let validate;
  let multer;

  beforeEach(() => {
    jest.clearAllMocks();
    router = {
      post: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnThis(),
    };
    Router.mockReturnValue(router);

    // Import after mocking
    craneRequestAttachmentRoute = require('../craneRequestAttachmentRoute');
    const controllers = require('../../controllers');
    craneRequestAttachmentController = controllers.craneRequestAttachmentController;
    passportConfig = require('../../config/passport');
    const validations = require('../../middlewares/validations');
    craneRequestAttachmentValidation = validations.craneRequestAttachmentValidation;
    validate = require('express-validation').validate;
    multer = require('multer');
  });

  describe('router getter', () => {
    it('should create a router instance and configure all routes', () => {
      const result = craneRequestAttachmentRoute.router;

      // Verify Router was called
      expect(Router).toHaveBeenCalled();
      expect(result).toBe(router);

      // Verify multer setup
      expect(multer.memoryStorage).toHaveBeenCalled();
      expect(multer).toHaveBeenCalledWith({ storage: 'mocked-storage' });

      // Verify all routes are registered with correct parameters
      expect(router.post).toHaveBeenCalledTimes(1);
      expect(router.get).toHaveBeenCalledTimes(2);

      // Verify POST route
      expect(router.post).toHaveBeenNthCalledWith(
        1,
        '/add_crane_request_attachement/:CraneRequestId/?:ParentCompanyId/:ProjectId',
        'mocked-multer-middleware',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        craneRequestAttachmentController.createCraneRequestAttachement,
      );

      // Verify GET routes
      expect(router.get).toHaveBeenNthCalledWith(
        1,
        '/remove_crane_request_attachement/:id/?:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        craneRequestAttachmentController.deleteCraneRequestAttachement,
      );

      expect(router.get).toHaveBeenNthCalledWith(
        2,
        '/get_crane_request_attachements/:CraneRequestId/?:ParentCompanyId/:ProjectId',
        'mocked-validate-middleware',
        passportConfig.isAuthenticated,
        craneRequestAttachmentController.getCraneRequestAttachements,
      );

      // Verify validation middleware is configured correctly
      expect(validate).toHaveBeenCalledTimes(3);
      expect(validate).toHaveBeenCalledWith(
        craneRequestAttachmentValidation.createCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestAttachmentValidation.deleteCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      );
      expect(validate).toHaveBeenCalledWith(
        craneRequestAttachmentValidation.getCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      );

      // Verify multer array method is called
      const uploadInstance = multer.mock.results[0].value;
      expect(uploadInstance.array).toHaveBeenCalledWith('attachement', 12);
    });

    it('should return the same router instance when called multiple times', () => {
      const result1 = craneRequestAttachmentRoute.router;
      const result2 = craneRequestAttachmentRoute.router;

      expect(result1).toBe(result2);
      expect(Router).toHaveBeenCalledTimes(2);
    });

    it('should use authentication middleware for all routes', () => {
      craneRequestAttachmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have authentication
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain(passportConfig.isAuthenticated);
      });
    });

    it('should use validation middleware for all routes', () => {
      craneRequestAttachmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // All routes should have validation
      [...postCalls, ...getCalls].forEach(call => {
        expect(call).toContain('mocked-validate-middleware');
      });
    });

    it('should configure multer middleware for POST route only', () => {
      craneRequestAttachmentRoute.router;

      const postCalls = router.post.mock.calls;
      const getCalls = router.get.mock.calls;

      // POST route should have multer middleware
      expect(postCalls[0]).toHaveLength(5); // path + multer + validation + auth + controller
      expect(postCalls[0][1]).toBe('mocked-multer-middleware');

      // GET routes should not have multer middleware
      getCalls.forEach(call => {
        expect(call).toHaveLength(4); // path + validation + auth + controller
        expect(call).not.toContain('mocked-multer-middleware');
      });
    });
  });

  describe('module structure', () => {
    it('should export an object with a router getter', () => {
      expect(typeof craneRequestAttachmentRoute).toBe('object');
      expect(craneRequestAttachmentRoute).toHaveProperty('router');

      // Check that router is a getter
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestAttachmentRoute, 'router');
      expect(descriptor.get).toBeDefined();
      expect(typeof descriptor.get).toBe('function');
    });

    it('should not have a router setter', () => {
      const descriptor = Object.getOwnPropertyDescriptor(craneRequestAttachmentRoute, 'router');
      expect(descriptor.set).toBeUndefined();
    });
  });
});
