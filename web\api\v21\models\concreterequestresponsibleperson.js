module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestResponsiblePerson = sequelize.define(
    'ConcreteRequestResponsiblePerson',
    {
      ConcreteRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      ConcreteRequestCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  ConcreteRequestResponsiblePerson.associate = (models) => {
    ConcreteRequestResponsiblePerson.belongsTo(models.ConcreteRequest, {
      as: 'concreteRequest',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequestResponsiblePerson.belongsTo(models.Member, {
      as: 'Member',
      foreignKey: 'MemberId',
    });
    ConcreteRequestResponsiblePerson.belongsTo(models.Member);
  };
  ConcreteRequestResponsiblePerson.createInstance = async (paramData) => {
    const concreteResponsiblePerson = await ConcreteRequestResponsiblePerson.create(paramData);
    return concreteResponsiblePerson;
  };
  return ConcreteRequestResponsiblePerson;
};
