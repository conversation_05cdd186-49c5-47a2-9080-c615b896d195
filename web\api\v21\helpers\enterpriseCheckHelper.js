const { User, Member, Enterprise, Sequelize } = require('../models');

const { Op } = Sequelize;

const enterpriseCheck = {
  async findDomainEnterprise(domainName) {
    if (!domainName) return null;
    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });
    return domainEnterpriseValue ? domainName : '';
  },

  async findUserByEmail(email) {
    if (!email) return null;
    return User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            [Op.and]: Sequelize.and(
              Sequelize.where(
                Sequelize.fn('lower', Sequelize.col('email')),
                Sequelize.fn('lower', email),
              ),
            ),
          },
        ],
      },
    });
  },

  async findMemberByUserId(userId) {
    return Member.findOne({
      where: { UserId: userId, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });
  },

  async findEnterpriseByParentCompanyId(ParentCompanyId) {
    return Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
  },

  async findEnterpriseByMember(memberData, ParentCompanyId) {
    if (memberData.isAccount) {
      return Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' },
      });
    }
    return this.findEnterpriseByParentCompanyId(ParentCompanyId);
  },

  async checkEnterPrise(inputData) {
    const { domainName: initialDomainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId ||
      inputData.params.ParentCompanyId ||
      inputData.params.companyId;

    // First try to find domain from initial domain name
    let domainName = await this.findDomainEnterprise(initialDomainName);

    // If no domain found and we have a ParentCompanyId, try to find through user
    if (!domainName && ParentCompanyId && ParentCompanyId !== 'undefined') {
      const userData = await this.findUserByEmail(inputData.user.email);

      if (userData) {
        const memberData = await this.findMemberByUserId(userData.id);
        const enterpriseValue = memberData
          ? await this.findEnterpriseByMember(memberData, ParentCompanyId)
          : await this.findEnterpriseByParentCompanyId(ParentCompanyId);

        if (enterpriseValue) {
          domainName = enterpriseValue.name.toLowerCase();
        }
      }
    }

    return domainName;
  },
};

module.exports = enterpriseCheck;
